# funi-pass-cs-web-cli

前端脚手架工程.
技术栈：[Vue3](https://cn.vuejs.org/)、[pinia](https://pinia.web3doc.top/)、[element-plus](https://element-plus.gitee.io/zh-CN/)、[FuniUI](http://henan.funi.com/sitedocs/)、[FuniJS](http://henan.funi.com/sitedocs/)、[pnpm](https://pnpm.io/zh/)、[vite](https://vitejs.cn/)

## 安装

```sh
pnpm install
```

### 本地运行

```sh
pnpm dev
```

### 本地打包

```sh
pnpm build
```

### 目录结构

```sh
public          # 静态资源
src
├── apis        # 系统级api
├── assets      # 资源文件
├── components  # 系统级自定义组件
├── layouts     # 布局
├── apps        # 系统页面
  ├── app1            # 子系统
      ├── modules1         # 模块名1
          ├── apis         # 模块api
          ├── model        # 模块服务
          ├── views        # 模块页面
            ├── detail           # 模块详情/编辑页
            ├── list             # 模块列表页
            ├── modal            # 模块弹窗
          ├── router.js        # 模块路由
          ├── store.js         # 模块store
      ├── modules2        # 模块名2
          ├── apis         # 模块api
          ├── model        # 模块服务
          ├── views        # 模块页面
            ├── detail           # 模块详情/编辑页
            ├── list             # 模块列表页
            ├── modal            # 模块弹窗
          ├── router.js        # 模块路由
          ├── store.js         # 模块store

├── router      # 路由配置
├── store       # vuex
└── styles      # 全局样式
├── App.ve      # 主文件
└── main.js     # 主文件
```
