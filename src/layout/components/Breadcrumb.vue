<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-12-06 17:04:54
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-09 18:02:57
 * @FilePath: /funi-cloud-web-gsbms/src/layout/components/Breadcrumb.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="funi-layout__breadcrumb">
    <el-breadcrumb :separator-icon="ArrowRightBold" class="flex items-center wh-full pl-[20px]">
      <TransitionGroup appear enter-active-class="animate__animated animate__fadeIn">
        <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="item.id" :to="toPage(item)">
          {{ item.name || '' }}
        </el-breadcrumb-item>
      </TransitionGroup>
    </el-breadcrumb>
    <div class="funi-actions"></div>
  </div>
</template>
<script setup>
import { ref, watch, computed } from 'vue';
import { useRouter } from 'vue-router';
import { usePermissionStore } from '@/stores/usePermissionStore';
import { ArrowRightBold } from '@element-plus/icons-vue';

const props = defineProps({
  isDefaultPage: {
    type: Boolean,
    default: false
  }
});
const permissionStore = usePermissionStore();
const { currentRoute } = useRouter();
const breadcrumbList = ref([]);

const toPage = computed(() => page => {
  if (page.defaultPage && page.defaultPage.code) {
    return {
      name: page.defaultPage.code
    };
  } else {
    return void 0;
  }
});

watch(
  currentRoute,
  newRoute => {
    const { menus, breadcrumb, pageId } = newRoute.meta || {};
    if (!!breadcrumb && !!breadcrumb.length) {
      breadcrumbList.value = breadcrumb.map(item => ({
        id: $utils.guid(),
        name: item
      }));
    } else {
      const [menuId] = menus || [];
      const activeMenu = permissionStore.menuGroupById[menuId] || {};
      const activeMenuPage = permissionStore.pageGroupById[pageId] || {};
      const parentMenu = permissionStore.menuGroupById[activeMenu.pid] || {};
      breadcrumbList.value = [activeMenu].filter(menu => menu.pid !== '1' && !!menu.id);
      if (!props.isDefaultPage) {
        breadcrumbList.value.push(activeMenuPage);
      }
    }
  },
  { immediate: true }
);
</script>
<style lang="less" scoped>
.funi-layout__breadcrumb {
  border-bottom: 1px solid #ebedf2;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  height: 48px;

  .funi-actions {
    display: flex;
    align-items: stretch;
    padding-right: 20px;
  }
}
</style>
