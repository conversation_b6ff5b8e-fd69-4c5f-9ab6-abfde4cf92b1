<!--
 * @Author: tao.yang
 * @Date: 2022-11-10 10:00:29
 * @LastEditTime: 2023-11-01 14:43:31
 * @LastEditors: tao.yang <EMAIL>
 * @Description: 多页签
-->

<template>
  <div class="funi-layout-multi-tab">
    <!-- <Collapse v-if="!asideHidden" class="flex-shrink-0 mt-8px" color="var(--header-text-color)"></Collapse> -->
    <el-tabs type="card" v-model="activeTab" @tab-remove="removeTab">
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.fullPath"
        :label="tab.title"
        :name="tab.fullPath"
        :closable="tabs.length > 1"
      >
        <template #label>
          <el-tooltip placement="bottom" :disabled="tooltipDisabledMap[tab.fullPath]">
            <template #content>
              <div class="max-w-[200px]">{{ tab.title }}</div>
            </template>
            <template #default>
              <el-dropdown
                ref="dropdownRefs"
                trigger="contextmenu"
                :id="tab.fullPath"
                @command="handleDropdownCommand"
                @visible-change="visible => handleDropdownVisibleChange(visible, tab)"
              >
                <span class="funi-layout-multi-tab__label" :ref="el => handleToolContentRef(el, tab)">
                  {{ tab.title }}
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="item in dropdownSchmea"
                      :key="item.key"
                      :command="{ key: item.key, tab }"
                      :disabled="item.disabled(tabs, tab)"
                    >
                      <funi-icon :icon="item.icon" style="margin-right: 5px; font-size: 16px"></funi-icon>
                      {{ item.title }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tooltip>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default { name: 'FuniMultiTab' };
</script>

<script setup>
import { useSessionStorage } from '@vueuse/core';
import { ref, unref, watch, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useLayoutStore } from '@/layout/useLayoutStore';
import { usePermissionStore } from '@/stores/usePermissionStore';
import { useAppStore } from '@/stores/useAppStore';
import Collapse from '../Collapse.vue';

const emit = defineEmits(['removeTab']);
const props = defineProps({ asideHidden: Boolean });

const appStore = useAppStore();
const tabs = useSessionStorage(appStore.getStorageKey('multi-tabs'), []);
const activeTab = useSessionStorage(appStore.getStorageKey('active-multi-tab'), '');
const { currentRoute, push } = useRouter();
const store = useLayoutStore();
const permissionStore = usePermissionStore();
const dropdownRefs = ref([]);
const tooltipDisabledMap = reactive({});

const dropdownSchmea = [
  { key: 'close', icon: 'ant-design:close-outlined', title: '关闭标签页', disabled: (tabs, tab) => tabs.length <= 1 },
  {
    key: 'close-left',
    icon: 'ant-design:vertical-right-outlined',
    title: '关闭左侧标签页',
    disabled: (tabs, tab) => tabs.findIndex(i => i.fullPath === tab.fullPath) <= 0
  },
  {
    key: 'close-right',
    icon: 'ant-design:vertical-left-outlined',
    title: '关闭右侧标签页',
    disabled: (tabs, tab) => tabs.findIndex(i => i.fullPath === tab.fullPath) >= tabs.length - 1
  },
  {
    key: 'close-other',
    icon: 'ant-design:tag-outlined',
    title: '关闭其他标签页',
    disabled: (tabs, tab) => tabs.length <= 1
  }
];

// Watch
watch(
  currentRoute,
  newRoute => {
    if (newRoute.path === '/') return;
    if (!unref(tabs).find(tab => tab.fullPath === newRoute.fullPath)) {
      tabs.value.push({
        title: newRoute.query.tab || newRoute.meta.title,
        fullPath: newRoute.fullPath
      });
    }
    const [menuId] = newRoute.meta.menus || [];
    store.activeMenu = permissionStore.menuGroupById[menuId] || {};
    activeTab.value = unref(newRoute.fullPath);
  },
  { immediate: true }
);

watch(
  activeTab,
  newKey => {
    !!newKey && newKey !== unref(currentRoute.fullPath) && push(newKey);
  },
  { immediate: true }
);

// Functions
function removeTab(tabName) {
  handleDropdownCommand({ key: 'close', tab: { fullPath: tabName } });
}

function handleDropdownCommand({ key, tab } = {}) {
  const tabKey = tab.fullPath;
  const tabIndex = tabs.value.findIndex(tab => tab.fullPath === tabKey);
  if (tabIndex === -1) return;

  let spliceTabs = [];
  const activeIndex = tabs.value.findIndex(tab => tab.fullPath === unref(activeTab));

  let newAciveIndex = -1;

  switch (key) {
    case 'close':
      spliceTabs = tabs.value.splice(tabIndex, 1).map(i => i.fullPath);
      if (!!unref(tabs).length && activeIndex === tabIndex) {
        newAciveIndex = Math.min(unref(tabs).length - 1, tabIndex);
      }
      break;
    case 'close-left':
      spliceTabs = tabs.value.splice(0, tabIndex).map(i => i.fullPath);
      if (activeIndex < tabIndex) {
        newAciveIndex = 0;
      }
      break;
    case 'close-right':
      spliceTabs = tabs.value.splice(tabIndex + 1).map(i => i.fullPath);
      if (activeIndex > tabIndex) {
        newAciveIndex = unref(tabs).length - 1;
      }
      break;
    case 'close-other':
      spliceTabs = tabs.value.filter(i => i.fullPath !== tabKey).map(i => i.fullPath);
      tabs.value = tabs.value.slice(tabIndex, tabIndex + 1);
      newAciveIndex = 0;
      break;

    default:
      break;
  }

  if (!!unref(tabs).length && newAciveIndex !== -1) {
    push(unref(tabs)[newAciveIndex].fullPath);
  }

  spliceTabs.forEach(tabName => setTimeout(() => emit('removeTab', tabName), 100));
}

function handleDropdownVisibleChange(visible, tab) {
  visible && unref(dropdownRefs).forEach(item => item.id !== tab.fullPath && item.handleClose());
}

const handleToolContentRef = (el, tab) => {
  if (!!el) {
    tooltipDisabledMap[tab.fullPath] = el.scrollWidth <= el.offsetWidth;
  }
};

defineExpose({
  close: removeTab
});
</script>

<style lang="scss" scoped src="@/layout/styles/multitab.scss"></style>
