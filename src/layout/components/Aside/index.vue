<script lang="jsx">
import { computed, unref, ref, watch, nextTick, onMounted } from 'vue';
import { useRenderMenuItem } from './useRenderMenuItem';
import { useRouter } from 'vue-router';
import { useLayoutStore } from '@/layout/useLayoutStore';
import { usePermissionStore } from '@/stores/usePermissionStore';
import { useAppStore } from '@/stores/useAppStore';
import { initEvent, setMenuTooltip } from './useTooltip'

export default {
  setup(props) {
    const appStore = useAppStore();
    const layoutStore = useLayoutStore();
    const collapse = computed(() => layoutStore.collapse);
    const store = usePermissionStore();
    const asideMenus = store.menuTree.children || [];
    const defaultOpeneds = computed(() =>
      !appStore.accountConfig.menuFoldMethod ? asideMenus.map(item => item.id) : []
    );

    const { push } = useRouter();

    const defaultActive = ref('');

    const handleHyperLink = urlString => {
      try {
        const url = new URL(urlString);
        window.open(url);
      } catch (error) {
        console.error(error);
      }
    };

    const menuSelect = (index, indexPath, item) => {
      const page = store.menuGroupById[index].defaultPage;
      if (page.redirect) {
        handleHyperLink(page.url);
      } else if (!!page && !!page.route) {
        push(page.route);
      }
    };
    onMounted(() => {
      initEvent()
      setMenuTooltip()
    })

    watch(
      () => layoutStore.activeMenu,
      newMenu => !!newMenu && (defaultActive.value = newMenu.id),
      { immediate: true }
    );

    return () => (
      <aside class={['funi-layout-aside', { 'funi-layout-aside--collapse': collapse.value }]}>
        <div class="w-full flex-1 overflow-hidden">
          <el-scrollbar>
            <el-menu
              defaultActive={unref(defaultActive)}
              defaultOpeneds={unref(defaultOpeneds)}
              collapse={unref(collapse)}
              uniqueOpened={false}
              collapseTransition={false}
              onSelect={menuSelect}
            >
              {{
                default: () => {
                  const { renderMenuItem } = useRenderMenuItem();
                  return renderMenuItem(unref(asideMenus));
                }
              }}
            </el-menu>
          </el-scrollbar>
        </div>
      </aside>
    );
  }
};
</script>

<style lang="scss" scoped src="@/layout/styles/aside.scss"></style>
<style lang="scss" scoped>
@mixin v-menu-title-style {
  display: inline-block;
  width: calc(100% - var(--el-menu-icon-width) - 5px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.funi-layout-aside {
  .el-menu {
    border: none;

    .iconify {
      margin-right: 5px;
      width: var(--el-menu-icon-width);
      text-align: center;
      font-size: 18px;
      vertical-align: middle;
      flex-shrink: 0;
    }
  }

  .el-button {
    --el-button-bg-color: clear;
    --el-button-hover-bg-color: clear;
    border: none;
  }

  .el-menu-item.is-active {
    background-color: var(--aside-menu-active-bg-color);
  }

  .el-menu-item {
    margin: 4px 0 8px;
    box-sizing: border-box;

    span.v-menu__title {
      @include v-menu-title-style
    }
  }

  :deep(.el-sub-menu__title:hover),
  .el-menu-item:hover {
    color: var(--aside-menu-active-color);
  }

  .el-sub-menu__title,
  .el-sub-menu {
    margin: 4px 0;

    span.v-menu__title {
      @include v-menu-title-style
    }
  }
}
</style>
<style>
.funi-menu-tooltip {
  background: rgba(0, 0, 0);
  color: white;
  font-size: 1em;
  padding: 10px;
  border-radius: 4px;
  position: fixed;
  top: 0;
  display: none;
  opacity: 0;
  transition: opacity 0.5s;
}

.funi-menu-tooltip::after {
  content: '';
  width: 0;
  height: 0;
  border: 5px solid black;
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  position: absolute;
  bottom: -10px;
  left: var(--funi-tooltip-arrow-left, 0);
}
</style>
