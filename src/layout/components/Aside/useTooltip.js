export function verdictTooltip(target) {
  if (!target) return false;
  return target?.scrollWidth > target?.clientWidth;
}

export function setMenuTooltip() {
  window.menutooltip = document.querySelector('.funi-menu-tooltip');
  if (window.menutooltip) return;
  window.menutooltip = document.createElement('div');
  window.menutooltip.classList.add('funi-menu-tooltip');
  document.body.append(window.menutooltip);
}

export function initEvent() {
  const aside = document.querySelector('aside.funi-layout-aside');
  aside.addEventListener('mouseover', menuMouseover);
  aside.addEventListener('mouseout', menuMouseout);
}

function menuMouseover({ target } = {}) {
  const targetElement = target.closest('.el-menu-item') || target.closest('.el-sub-menu__title');
  if (targetElement) {
    const menuTitle = targetElement?.querySelector('span.v-menu__title');
    const toolTipsShow = verdictTooltip(menuTitle);
    if (!toolTipsShow) return;
    const text = menuTitle.innerText;
    window.menutooltip.innerText = text;
    window.menutooltip.style.display = 'block';
    const { bottom, height, left, right, top, width } = targetElement.getBoundingClientRect();
    const width1 = window.menutooltip.offsetWidth;

    window.menutooltip.style.top = top - height - 10 + 'px';
    let left_tip = 5;
    let arrow_left = width / 2 + left + 'px';
    if (width1 < 200) {
      left_tip = width / 2 + left - width1 / 2;
      arrow_left = width1 / 2 - 5 + 'px';
    }
    window.menutooltip.style.left = left_tip + 'px';
    window.menutooltip.style.setProperty('--funi-tooltip-arrow-left', arrow_left);
    setTimeout(() => {
      window.menutooltip.style.opacity = 1;
    });
  }
}

function menuMouseout({ target } = {}) {
  if (!target || !window.menutooltip) return;
  const targetElement = target.closest('.el-menu-item') || target.closest('.el-sub-menu__title');
  if (!targetElement) return;
  window.menutooltip.style.display = 'none';
  window.menutooltip.style.opacity = 0;
}
