<template>
  <main class="bg-[#F5F7FA] p-[12px] pt-[10px]">
    <el-scrollbar class="rounded-[4px]" height="100%" wrap-class="layout-content__wrap">
      <router-view>
        <template #default="{ Component, route }">
          <keep-alive v-if="useMultiTab" ref="keepAlive">
            <component :is="Component" :key="componentKeyMap.get(route.fullPath)" />
          </keep-alive>
          <component v-else :is="Component" :key="componentKeyMap.get(route.fullPath)" />
        </template>
      </router-view>
    </el-scrollbar>
  </main>
</template>

<script setup>
import { useSessionStorage } from '@vueuse/core';
import { ref, unref, computed } from 'vue';
import { onBeforeRouteUpdate } from 'vue-router';
import { useKeepAliveCache } from './useKeepAliveCache';
import { useAppStore } from '@/stores/useAppStore';
import useThemeConfigStore from '@/layout/components/theme/hooks/setTheme.js';

const themeConfigStore = useThemeConfigStore();

const keepAlive = ref();
const appStore = useAppStore();
const componentKeyMap = useSessionStorage(appStore.getStorageKey('component-key-map'), new Map());
const useMultiTab = computed(() => themeConfigStore.themeConfig.openTabs);

function removeCache(tabName) {
  const { pruneCache } = useKeepAliveCache(unref(keepAlive));
  const cacheKey = unref(componentKeyMap).get(tabName);
  pruneCache(cacheKey);
  componentKeyMap.value.delete(tabName);
}

onBeforeRouteUpdate((to, from) => {
  if (!unref(componentKeyMap).has(to.fullPath)) {
    componentKeyMap.value.set(to.fullPath, new Date().getTime());
  }
});

defineExpose({
  removeCache
});
</script>
