export const useKeepAliveCache = keepAlive => {
  if (!keepAlive || !keepAlive.$.__v_cache) return { pruneCache: () => {} };

  const instance = keepAlive.$;
  const { suspense, __v_cache, ctx } = instance;

  function pruneCache(cacheKey) {
    if (__v_cache.has(cacheKey)) {
      unmount(__v_cache.get(cacheKey));
      __v_cache.delete(cacheKey);
    }
  }

  function resetShapeFlag(vnode) {
    const COMPONENT_SHOULD_KEEP_ALIVE = 1 << 8;
    const COMPONENT_KEPT_ALIVE = 1 << 9;

    let shapeFlag = vnode.shapeFlag;
    if (shapeFlag & COMPONENT_SHOULD_KEEP_ALIVE) {
      shapeFlag -= COMPONENT_SHOULD_KEEP_ALIVE;
    }
    if (shapeFlag & COMPONENT_KEPT_ALIVE) {
      shapeFlag -= COMPONENT_KEPT_ALIVE;
    }
    vnode.shapeFlag = shapeFlag;
  }

  function unmount(vnode) {
    resetShapeFlag(vnode);
    ctx.renderer.um(vnode, instance, suspense, true, false);
  }

  return {
    pruneCache
  };
};
