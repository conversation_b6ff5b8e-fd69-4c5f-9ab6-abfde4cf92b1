/*
 * @Author: 古加文 <EMAIL>
 * @Date: 2022-12-01 14:12:17
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2025-01-15 15:46:49
 * @FilePath: /src/layout/components/Header/ToolBarRight/UserCenter/store/logStore.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia';
import Log from '../apis/log/index.js';
const { getUserInfoApi, updateUserInfoApi, updatePasswordApi, getPasswordRegularApi, logoutApi } = Log;

export const logStore = defineStore({
  id: 'log',
  state: () => {
    return {
      id: '',
      //姓名
      nickName: '',
      //用户名
      userName: ''
    };
  },
  actions: {
    //设置昵称
    async updateNickName(name) {
      this.nickName = name;
    },

    //设置用户名
    async updateUserName(name) {
      this.userName = name;
    },

    /**
     * @description:获取用户信息
     * @return {*}
     */
    async getUserInfo() {
      const data = await getUserInfoApi();
      data.userName = data.account;
      data.cellPhone = data.phoneNumber;
      data.emailAddress = data.emailAddress;
      this.nickName = data.username;
      this.id = data.id;
      this.updateNickName(this.nickName);
      this.updateUserName(this.userName);
      return data;
    },

    /**
     * @description:修改用户信息
     * @return {*}
     */
    async updateUserInfo({ nickName, cellPhone, emailAddress }) {
      //加密参数
      let params = {};
      params.nickName = nickName;
      params.cellPhone = $utils.encryptedData(cellPhone);
      params.emailAddress = $utils.encryptedData(emailAddress);
      const data = await updateUserInfoApi(params);
      return data;
    },

    /**
     * @description:修改密码
     * @return {*}
     */
    async updatePassword({ oldPassword, newPassword }) {
      //加密参数
      let params = {};
      params.oldPassword = $utils.encryptedData(oldPassword);
      params.newPassword = $utils.encryptedData(newPassword);
      const data = await updatePasswordApi(params);
      return data;
    },

    /**
     * @description:获取用户密码规则
     * @return {*}
     */
    async getPasswordRegular() {
      const data = await getPasswordRegularApi();
      return data;
    },

    /**
     * @description:登出
     * @return {*}
     */
    async logout() {
      const data = await logoutApi();
      return data;
    }
  }
});
