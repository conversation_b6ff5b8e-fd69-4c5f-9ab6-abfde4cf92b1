<!--
 * @Author: 古加文 <EMAIL>
 * @Date: 2022-12-21 15:23:18
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-12-13 17:39:01
 * @FilePath: /src/layout/components/Header/ToolBarRight/UserCenter/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="user-menu-view">
    <div
      class="other-link"
      v-for="(item, index) in getMenuList(option.otherLinks, defaultLinksObject)"
      :key="index"
      @click="handleItemAction(item)"
    >
      <img :src="getImageUrl(item.icon)" /><el-button class="btn" link>{{ item.label }}</el-button>
    </div>

    <el-dropdown trigger="click" v-if="isShowUserInfo" :disabled="!option.menuList || option.menuList.length < 1">
      <div class="user-drop">
        <img :src="avatar" alt="" />
        <el-button class="btn" link>{{ store.nickName }}</el-button>
      </div>
      <template #dropdown>
        <el-dropdown-menu class="menu">
          <template v-for="(childList, index) in option.menuList" :key="index">
            <el-divider v-if="index > 0" style="margin: 7px 0" />
            <template v-for="(childItem, childIndex) in getMenuList(childList, defaultObject)" :key="childIndex">
              <el-dropdown-item class="menu-item" v-if="childItem.key == 'REC'">
                <img class="menu-item-icon" :src="ic_record_pressed" />
                <div class="menu-item-text" @click="recChange">
                  {{ isRec ? '结束录制' : '开始录制' }}
                </div>
              </el-dropdown-item>
              <el-dropdown-item class="menu-item" v-else>
                <img class="menu-item-icon" :src="childItem.icon || ic_record_pressed" />
                <div class="menu-item-text" @click="() => handleItemAction(childItem)">
                  {{ childItem.label }}
                </div>
              </el-dropdown-item>
            </template>
          </template>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!--  -->
    <FuniREC v-if="showRECModal" ref="recRef" @change="rec => (isRec = rec)" />
    <UserInfoModalEdit v-if="showUserInfoModal" ref="uimeRef" />
    <component
      v-for="item in registerComponents"
      :key="item.key"
      :is="componentRender(item)"
      :ref="el => (registerRef[item.key] = el)"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, getCurrentInstance, computed } from 'vue';
import FuniREC from './FuniREC/index.vue';
import UserInfoModalEdit from './UserInfoModalEdit.vue';
import { logStore } from './store/logStore';
import { useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import ic_user from './assets/icon/ic_user.png';
import ic_record_pressed from './assets/icon/ic_record_pressed.png';
import { useAppStore } from '@/stores/useAppStore';
import xss from 'xss';

const props = defineProps({
  option: { type: Object, default: () => ({}) },
  isShowUserInfo: { type: Boolean, default: true } //显示用户下拉
});

const showUserInfoModal = ref(false);
const showRECModal = ref(false);
const uimeRef = ref();
const store = logStore();
//日志录制
const recRef = ref();
const isRec = ref();
const register = getCurrentInstance().proxy.$ucRegister;

const getImageUrl = name => new URL(`./assets/icon/${name}.png`, import.meta.url);

function recChange() {
  if (isRec.value) {
    recRef.value.stopREC();
  } else {
    recRef.value.startREC();
  }
}

onMounted(() => {
  if (props.isShowUserInfo) {
    store.getUserInfo();
  }
});

//默认提供的横向平铺链接
const defaultLinksObject = {
  ONLINE_DOC: {
    label: '在线文档',
    icon: 'ic_file_text'
  }
};

//默认提供的下拉菜单项
const defaultObject = {
  USER_CENTER: { label: '个人中心', icon: getImageUrl('ic_folder_user_pressed') },
  USER_INFO: { label: '个人信息', icon: getImageUrl('ic_folder_user_pressed') },
  APP_MANAGE: { label: '应用管理', icon: getImageUrl('ic_app_manage_pressed') },
  LOGOUT: { label: '退出登录', icon: getImageUrl('ic_logout_pressed') },
  REC_RECORD: { label: '录制记录', icon: getImageUrl('ic_rec_pressed') },
  REC: { label: '录制记录', icon: getImageUrl('ic_rec_pressed') },
  LOG: { label: '我的日志', icon: getImageUrl('ic_log_pressed') },
  ...Object.fromEntries(register.entries())
};

const registerRef = reactive({});
const registerComponents = register.values().filter(item => !!item.component);

/**
 * 获取菜单项配置
 * @param {*} item 传入菜单项配置
 */
const getMenuList = (list, defaultObj) => {
  const keys = Object.keys(defaultObj);
  return list
    .map(item => {
      if (!keys.includes(item.key)) return;
      if (item.key == 'USER_INFO') {
        showUserInfoModal.value = true;
      }
      if (item.key == 'REC') {
        showRECModal.value = true;
      }
      return Object.assign({}, defaultObj[item.key], item);
    })
    .filter(Boolean);
};

const handleItemAction = item => {
  switch (item.key) {
    case 'USER_INFO': //用户信息
      openUserInfo();
      break;
    case 'USER_CENTER': //用户中心
      navToUserCenter(item);
      break;
    case 'APP_MANAGE': //应用管理
      navToAppManage();
      break;
    case 'ONLINE_DOC': //在线文档
      navToOnlineDoc();
      break;
    case 'LOGOUT': //退出登录
      logout();
      break;
    case 'REC_RECORD': //录制记录
      navToUserRecord();
      break;
    case 'LOG': //我的日志
      navToUserLog();
      break;
    default:
      const registedItem = register.get(item.key);
      if (!!registedItem && $utils.isFunction(registedItem.action)) {
        registedItem.action(item, registerRef[item.key]);
      }
      break;
  }
};

function componentRender(item) {
  if ($utils.isFunction(item.component)) {
    return item.component(item);
  }
  return item.component;
}

/**
 * 跳转应用管理
 */
const navToAppManage = () => {
  window.open(`${window.location.origin}/${$utils.getTenantID()}/ccsapp/#/appManager/views/userAppList`);
};

/**
 * 跳转在线文档
 */
const navToOnlineDoc = () => {
  window.open(`${window.location.origin}/${$utils.getTenantID()}/site/doc/#/`);
};

/**
 * 跳转用户中心
 */
const navToUserCenter = item => {
  // sessionStorage.removeItem('sysCode');
  // sessionStorage.removeItem('token');
  sessionStorage.clear();
  window.location.href = `${window.location.origin}/${$utils.getTenantID()}/casapp/#/${item.path || 'home'}`;
};

/**
 * 跳转录制记录
 */
const navToUserRecord = () => {
  window.open(`${window.location.origin}/${$utils.getTenantID()}/casapp/#/recordList`);
};

/**
 * 跳转我的日志
 */
const navToUserLog = () => {
  window.open(`${window.location.origin}/${$utils.getTenantID()}/casapp/#/logList`);
};

/**
 * 退出登录
 */
const { push } = useRouter();
const appStore = useAppStore();
const avatar = computed(() => (appStore.user?.avatar ? $utils.fileUrl(appStore.user?.avatar) : ic_user));
const logout = () => {
  ElMessageBox.confirm('确定是否退出登录？', '退出登录', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(async () => {
    let res = await store.logout();
    if (res) {
      const url = xss(res);
      location.href = url;
    } else {
      const platUnitId = appStore.user?.platUnit?.id;
      localStorage.setItem('platUnitId', platUnitId);
      let user = sessionStorage.getItem('user');
      user = JSON.parse(user);
      sessionStorage.clear();
      if (user.userInfo.bpaasUserType == 'G') {
        const loginUrlG = `${window.location.origin}/${$utils.getTenantID()}/casapp/#/user/login`;
        window.location.href = loginUrlG;
      } else if (user.userInfo.bpaasUserType == 'B') {
        const loginUrlB = `${window.location.origin}/${$utils.getTenantID()}/casapp/#/org/login`;
        window.location.href = loginUrlB;
      } else if (user.userInfo.bpaasUserType == 'C') {
        const loginUrlC = `${window.location.origin}/${$utils.getTenantID()}/casapp/#/cus/login`;
        window.location.href = loginUrlC;
      }
    } // 清空用户数据缓存
    appStore.user = {};
  });
};
/**
 * 个人信息
 */
const openUserInfo = () => {
  uimeRef.value.show();
};
</script>

<style lang="less" scoped>
.other-link {
  display: flex;
  align-items: center;
  margin-right: 16px;

  .btn {
    line-height: 22px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
  }

  img {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    filter: grayscale(100%) brightness(200%);
  }
}

.user-drop {
  display: flex;
  align-items: center;
  padding: 0 10px;
  min-width: 80px;

  img {
    height: 24px;
    width: 24px;
    margin-right: 4px;
    border-radius: 50%;
  }

  .btn {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    line-height: 20px;
  }
}

.user-menu-view {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.menu {
  padding-left: 8px;
  padding-right: 8px;
}

.menu-item:hover img {
  filter: grayscale(0%);
}

.menu-item:hover div {
  color: var(--el-menu-active-color);
}

.menu-item-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  filter: grayscale(100%);
}

.menu-item-text {
  font-size: 14px;
  font-weight: 400;
  color: var(--el-text-color-primary);
  line-height: 26px;
}
</style>
