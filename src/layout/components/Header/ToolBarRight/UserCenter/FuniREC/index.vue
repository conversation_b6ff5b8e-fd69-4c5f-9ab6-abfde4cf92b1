<template>
  <div ref="recRef" v-show="isREC" @mousedown="dragx($event)" class="recView">
    <el-button class="recBtn" type="danger" plain>
      <div class="flex items-center">
        <div class="record-circle-icon">
          <div class="record-circle-small-icon"></div>
        </div>
        <span class="text">录制中</span>
        <span class="hover-text">结束录制</span>
      </div>
    </el-button>
  </div>
  <el-dialog v-model="dialogFormVisible" width="400" title="录制提示">
    <div style="margin-bottom: 12px">确定结束录制，并保存录制记录吗？</div>
    <el-form :model="form">
      <el-form-item label="备注">
        <el-input :rows="3" type="textarea" v-model="form.remark" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-50" @click="dialogFormVisible = false">取消</el-button>
        <el-button class="w-50" type="primary" @click="stopRequest" :loading="loading"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { nextTick, ref, reactive } from 'vue';
import Log from '../apis/log/index.js';
const { logRecordStart, logRecordEnd } = Log;

const emit = defineEmits(['change']);
const isREC = ref(false);
const dialogFormVisible = ref(false);
const recRef = ref();
const loading = ref();
const form = reactive({
  remark: ''
});

window.addEventListener(
  'storage',
  e => {
    console.log('触发录制：', e.newValue);
    if (e.key == 'REC' && e.newValue !== null) {
      isREC.value = e.newValue == 'true';
      emit('change', isREC.value);
    }
  },
  false
);

//读取localStorage录制状态
isREC.value = localStorage.getItem('REC') == 'true';
emit('change', isREC.value);

//鼠标移动事件
const dragx = el => {
  let oDiv = recRef.value; //当前元素
  let disX = el.clientX - oDiv.offsetLeft;
  let disY = el.clientY - oDiv.offsetTop;
  let startOffsetLeft, startOffsetTop;
  document.onmousedown = function (e) {
    startOffsetLeft = oDiv.offsetLeft;
    startOffsetTop = oDiv.offsetTop;
  };
  document.onmousemove = function (e) {
    //通过事件委托，计算移动的距离
    let l = e.clientX - disX;
    let t = e.clientY - disY;
    if (l < 10) {
      //如果左侧的距离小于0，就让距离等于0.不能超出屏幕左侧。如果需要磁性吸附，把0改为100或者想要的数字即可
      l = 0;
    } else if (l > document.documentElement.clientWidth - oDiv.offsetWidth - 10) {
      //如果左侧的距离>屏幕的宽度-元素的宽度。也就是说元素的右侧超出屏幕的右侧，就让元素的右侧在屏幕的右侧上
      l = document.documentElement.clientWidth - oDiv.offsetWidth;
    }
    if (t < 10) {
      //和左右距离同理
      t = 0;
    } else if (t > document.documentElement.clientHeight - oDiv.offsetHeight - 10) {
      t = document.documentElement.clientHeight - oDiv.offsetHeight;
    }
    //移动当前元素
    oDiv.style.left = l + 'px';
    oDiv.style.top = t + 'px';
  };
  document.onmouseup = function (e) {
    document.onmousemove = null;
    document.onmouseup = null;
    if (startOffsetLeft == oDiv.offsetLeft && startOffsetTop == oDiv.offsetTop) {
      stopREC();
    }
  };
  // 解决有些时候,在鼠标松开的时候,元素仍然可以拖动;
  document.ondragstart = function (ev) {
    ev.preventDefault();
  };
  document.ondragend = function (ev) {
    ev.preventDefault();
  };
  return false;
};

//开始录制
const startREC = async () => {
  await logRecordStart().then(data => {
    localStorage.setItem('aprId', data.aprId);
    isREC.value = true;
    localStorage.setItem('REC', 'true');
    emit('change', isREC.value);
  });
};

//停止录制
const stopREC = () => {
  dialogFormVisible.value = true;
};

const stopRequest = () => {
  loading.value = true;
  logRecordEnd(form)
    .then(() => {
      isREC.value = false;
      dialogFormVisible.value = false;
      localStorage.setItem('REC', 'false');
      localStorage.removeItem('aprId');
      emit('change', isREC.value);
    })
    .finally(() => {
      loading.value = false;
    });
};

defineExpose({
  startREC,
  stopREC,
  isREC
});
</script>

<style lang="less">
.recView {
  position: fixed;
  top: 10px;
  right: 240px;
  width: 104px;
  z-index: 3000;

  .recBtn:hover .record-circle-icon {
    border: 1px solid #ffffff;
  }

  .recBtn:hover .record-circle-small-icon {
    background: #ffffff;
  }

  .text {
    display: block;
  }

  .recBtn:hover .text {
    display: none;
  }

  .hover-text {
    display: none;
  }

  .recBtn:hover .hover-text {
    display: block;
  }

  .record-circle-icon {
    width: 13px;
    height: 13px;
    border: 1px solid rgb(255, 17, 0);
    border-radius: 50%;
    margin-right: 6px;
    display: flex;
    align-items: center;
    justify-content: center;

    .record-circle-small-icon {
      width: 5px;
      height: 5px;
      background: rgb(255, 17, 0);
      border-radius: 50%;
      animation: mydeamon 1s infinite; //1s值一秒内完成动画
      -webkit-animation: mydeamon 1s infinite;
      /* Safari and Chrome */
    }

    @keyframes mydeamon {
      0% {
        transform: scale(1);
        opacity: 0;
      }

      50% {
        transform: scale(1.5); //scale代表闪烁的体型变化，其他倾斜等样式自行搜索
        opacity: 1; //透明程度
      }

      100% {
        transform: scale(1);
        opacity: 0;
      }
    }
  }
}
</style>
