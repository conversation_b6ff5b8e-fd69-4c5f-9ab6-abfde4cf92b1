<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-10-13 16:15:57
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-10-16 16:51:14
 * @FilePath: /funi-cloud-web-gsbms/src/layout/components/Header/ToolBarRight/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="flex items-stretch">
    <router-view name="extension">
      <template #default="{ Component, route }">
        <component v-if="!!Component" :is="Component" />
      </template>
    </router-view>
    <Shortcuts />
    <UserCenter :option="userMenuOption" :isShowUserInfo="!pubUser"></UserCenter>
  </div>
</template>
<script setup>
import Shortcuts from './Shortcuts.vue';
import UserCenter from './UserCenter/index.vue';

const props = defineProps({
  pubUser: Boolean,
  userMenuOption: { type: Object, default: () => ({}) }
});
</script>
./FuniUserCenter/index.vue
