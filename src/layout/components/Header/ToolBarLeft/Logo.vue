<template>
  <div class="funi-layout-logo" @click="handleClick">
    <funi-image class="funi-layout-logo__img" :src="logoPath" />
    <!-- <el-divider direction="vertical" /> -->
    <div class="funi-layout-logo__label">
      {{ systemName }}
    </div>
  </div>
</template>

<script setup>
import LayoutConfig from '@/config/layout.config.js';
const props = defineProps({
  systemName: String,
  logoPath: { type: String, default: '/logo.png' },
  redirection: String
});

function handleClick() {
  if (LayoutConfig.default.disableLogo) return;

  sessionStorage.clear();
  window.location.href = props.redirection;
}
</script>
<style lang="scss" scoped src="@/layout/styles/logo.scss"></style>
