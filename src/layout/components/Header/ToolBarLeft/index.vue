<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-10-13 16:15:53
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-06-06 17:01:27
 * @FilePath: /funi-paas-cs-web-cli/src/layout/components/Header/ToolBarLeft/index.vue
 * @Description:
 * Copyright (c) 2023 by tao.yang <EMAIL>, All Rights Reserved.
-->
<template>
  <div class="funi-layout-header__left-aside">
    <template v-if="env.isBpaas && !LayoutConfig.default.hideClientSwitch">
      <span class="funi-layout-header__client-switch" @click="handleClientSwitch">
        <funi-svg name="client_switch_close" v-if="clientSwitchOpening" />
        <funi-svg name="client_switch" v-else />
      </span>
      <ClientSwitch v-model="clientSwitchOpening" />
    </template>
    <Logo :system-name="systemName" :logo-path="logoPath" :redirection="redirection"></Logo>
    <div
      v-if="env.isBpaas && !LayoutConfig.default.hideWorkbench"
      class="funi-layout-header__dashboard"
      @click="navToDashboard"
    >
      <funi-svg class="funi-layout-header__dashboard-icon" name="icon-home" />
      <span class="funi-layout-header__dashboard-text">工作台</span>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import env from '@/utils/env';
import Logo from './Logo.vue';
import ClientSwitch from './ClientSwitch.vue';
import LayoutConfig from '@/config/layout.config.js';

const props = defineProps({
  systemName: String,
  redirection: String,
  logoPath: { type: String, default: './logo.png' }
});

const clientSwitchOpening = ref(false);
const handleClientSwitch = () => {
  clientSwitchOpening.value = !clientSwitchOpening.value;
};
const navToDashboard = () => {
  sessionStorage.clear();
  window.location.href = props.redirection;
};
</script>
