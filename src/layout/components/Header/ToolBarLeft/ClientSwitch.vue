<template>
  <el-drawer destroy-on-close :with-header="false" v-model="drawer" direction="ltr">
    <el-scrollbar height="100%" wrap-class="service">
      <div class="service-item" v-for="service in services" :key="service.id">
        <h2>{{ service.classifyName }}</h2>
        <div class="client">
          <el-card
            v-for="client in service.childrens"
            shadow="hover"
            :class="{ 'is-disabled': client.disable }"
            :key="client.clientId"
            @click="handleCardClick(client)"
          >
            <funi-image :src="$utils.fileUrl(client.serviceIcon)" />
            <h4>{{ client.serviceName }}</h4>
          </el-card>
        </div>
      </div>
    </el-scrollbar>
  </el-drawer>
</template>

<script setup>
import { computed, watchEffect, ref } from 'vue';
import { useAppStore } from '@/stores/useAppStore';
import AppApis from '@/apis/app';

const props = defineProps({
  modelValue: Boolean
});
const emit = defineEmits(['update:modelValue']);

const appStore = useAppStore();
const services = ref([]);

const drawer = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
});

watchEffect(async () => {
  if (drawer.value) {
    const allServices = await appStore.getServices();
    services.value = allServices.map(item => {
      const sameClassify = appStore.system.serviceClassifyId;
      if (!sameClassify) return item;
      item.childrens = item.childrens.filter(i => i.id !== appStore.serviceId);
      return item;
    });
  }
});

const handleCardClick = async client => {
  if (client.disable) return;

  let config = appStore.platformConfig.appSwitchType;
  try {
    config = await AppApis.unifyAccountConfigDetailMy();
  } catch (error) {}
  const authorize = await AppApis.authorize({ clientId: client.clientId, serviceId: client.id });
  if (config.appOpenMethod === true) {
    document.getElementById('loading-mask').style.display = 'flex';
    window.location.href = authorize.trim();
    setTimeout(() => {
      sessionStorage.clear();
      window.location.reload();
    }, 100);
  } else {
    drawer.value = false;
    setTimeout(() => {
      window.open(authorize.trim(), client.id);
    }, 300);
  }
};
</script>

<style scoped lang="scss">
@use 'element-plus/theme-chalk/src/mixins/mixins' as *;

:deep() {
  .el-scrollbar__bar.is-vertical {
    display: none !important;
  }
}

.service {
  display: flex;
  flex-direction: column;
  align-items: stretch;

  .service-item {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: center;

    h2 {
      font-size: 18px;
      font-weight: 500;
      line-height: 26px;
      margin: 0;
      margin-bottom: 12px;
      color: var(--el-text-color-primary);
    }

    .client {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 12px;

      .is-disabled {
        filter: opacity(0.4);
        cursor: not-allowed !important;
      }

      :deep() {
        @include b(card) {
          @include set-css-var-value('card-padding', 12px);
          cursor: pointer;

          @include e(body) {
            display: flex;
            align-items: center;

            @include b(image) {
              width: 48px;
              height: 48px;
            }

            h4 {
              margin-left: 12px;
              font-size: 14px;
              font-weight: 500;
              line-height: 22px;
            }
          }
        }
      }
    }
  }

  .service-item + .service-item {
    margin-top: 20px;
  }
}
</style>
