import { ElMenuItem, ElSubMenu } from 'element-plus';
import { Icon } from '@iconify/vue';

export const useRenderMenuItem = () => {
  const renderMenuTitle = meta => {
    const { title = 'Please set title', icon } = meta;

    return (
      <>
        {!!icon && <Icon icon={icon}></Icon>}
        <span class="v-menu__title">{title}</span>
      </>
    );
  };

  const renderMenuItem = menus => {
    return menus.map(menu => {
      if (menu.hidden) return null;

      const id = menu.id;
      const meta = { title: menu.alias || menu.name, icon: menu.icon };

      if (!!menu.children && !!menu.children.length && !menu.hideChildren) {
        return (
          <ElSubMenu index={id} popperClass="funi-layout-nav-menu--horizontal">
            {{
              title: () => renderMenuTitle(meta),
              default: () => renderMenuItem(menu.children)
            }}
          </ElSubMenu>
        );
      } else {
        return (
          <ElMenuItem index={id}>
            {{
              default: () => renderMenuTitle(meta)
            }}
          </ElMenuItem>
        );
      }
    });
  };
  return { renderMenuItem };
};
