<script lang="jsx">
import { unref, ref, watch } from 'vue';
import { useRenderMenuItem } from './useRenderMenuItem';
import { useRouter } from 'vue-router';
import { useLayoutStore } from '@/layout/useLayoutStore';
import { usePermissionStore } from '@/stores/usePermissionStore';

export default {
  setup(props) {
    const layoutStore = useLayoutStore();
    const store = usePermissionStore();
    const menus = store.menuTree.children || [];

    const { push } = useRouter();

    const defaultActive = ref('');

    const menuSelect = (index, indexPath, item) => {
      const page = store.menuGroupById[index].defaultPage;
      if (!!page && !!page.route) {
        push(page.route);
      }
    };

    watch(
      () => layoutStore.activeMenu,
      newMenu => !!newMenu && (defaultActive.value = newMenu.id),
      { immediate: true }
    );

    return () => (
      <div class="funi-layout-nav-menu">
        <el-menu
          defaultActive={unref(defaultActive)}
          uniqueOpened={false}
          collapseTransition={false}
          onSelect={menuSelect}
          mode="horizontal"
        >
          {{
            default: () => {
              const { renderMenuItem } = useRenderMenuItem();
              return renderMenuItem(unref(menus));
            }
          }}
        </el-menu>
      </div>
    );
  }
};
</script>

<style lang="scss" scoped src="@/layout/styles/nav_menu.scss"></style>
