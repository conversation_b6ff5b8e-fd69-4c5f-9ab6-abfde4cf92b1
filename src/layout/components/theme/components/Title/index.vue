<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-12-26 15:38:03
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-26 16:00:43
 * @FilePath: /funi-cloud-web-gsbms/src/layout/components/theme/title/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="funi-theme-title"><slot></slot></div>
</template>
<style scoped>
.funi-theme-title {
  font-size: 16px;
  color: black;
  font-weight: 700;
  margin-bottom: 16px;
}
</style>
