<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-12-27 14:31:10
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-08 16:15:41
 * @FilePath: /funi-cloud-web-gsbms/src/layout/components/theme/components/MenuColor/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <div class="funi-theme__menu">
      <div v-for="item in group">
        <div
          class="funi-theme__menu__item funi-theme__cursor"
          :class="{
            selected: modelValue == item.value
          }"
          @click="check(item.value)"
        >
          <component :is="item.components" :name="item.value"></component>
        </div>
        <span class="title-name">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref } from 'vue';
import menuColor from './../svg/menuColor.svg.vue';
import useThemeConfigStore from './../../hooks/setTheme.js';
const themeConfigStore = useThemeConfigStore();

const group = ref([
  {
    components: menuColor,
    value: 'black',
    name: '深色侧边栏'
  },
  {
    components: menuColor,
    value: 'white',
    name: '浅色侧边栏'
  },
  {
    components: menuColor,
    value: 'blue',
    name: '蓝色侧边栏'
  }
]);
const props = defineProps({
  modelValue: {
    type: String,
    default: 'white'
  }
});
const emit = defineEmits(['update:modelValue']);

const check = type => {
  emit('update:modelValue', type);
  themeConfigStore.setMenuColor(type, true);
};
</script>
<style scoped>
@import url('./../style/comm.css');
</style>
