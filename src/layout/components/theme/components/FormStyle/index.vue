<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-12-27 14:31:10
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-17 20:36:55
 * @FilePath: /funi-paas-cs-web-cli/src/layout/components/theme/components/FormStyle/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <ThemeTitle>表单样式</ThemeTitle>
    <div class="funi-theme__menu">
      <div v-for="item in group">
        <div
          class="funi-theme__menu__item funi-theme__cursor"
          :class="{
            selected: modelValue == item.value
          }"
          @click="check(item.value)"
        >
          <component :is="item.components"></component>
        </div>
        <span class="title-name">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref } from 'vue';
import FormDefault from './../svg/formDefault.svg.vue';
import FormBorder from './../svg/formBorder.svg.vue';
import ThemeTitle from './../Title/index.vue';
import useThemeConfigStore from './../../hooks/setTheme.js';
const themeConfigStore = useThemeConfigStore();
const group = ref([
  {
    components: FormDefault,
    value: 'default',
    name: '默认表单样式'
  },
  {
    components: FormBorder,
    value: 'border',
    name: '带表格表单样式'
  }
]);
const props = defineProps({
  modelValue: {
    type: String,
    default: 'default'
  }
});
const emit = defineEmits(['update:modelValue']);

const check = type => {
  emit('update:modelValue', type);
  themeConfigStore.setFormBorder(type, true);
};
</script>
<style scoped>
@import url('./../style/comm.css');
</style>
