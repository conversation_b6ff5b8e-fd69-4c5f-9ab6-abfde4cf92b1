<!--
 * @Author: co<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-01-08 14:10:48
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-08 14:32:12
 * @FilePath: /funi-cloud-web-gsbms/src/layout/components/theme/components/svg/menuColor.svg.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <svg width="96" height="80" viewBox="0 0 96 80" fill="none" v-if="$attrs.name === 'black'">
    <g opacity="1" transform="translate(0)  rotate(0)">
      <g>
        <path
          id="矩形 3"
          fill-rule="evenodd"
          style="fill: #f0f2f5"
          opacity="1"
          d="M8 80L88 80C92.42 80 96 76.42 96 72L96 8C96 3.58 92.42 0 88 0L8 0C3.58 0 0 3.58 0 8L0 72C0 76.42 3.58 80 8 80Z"
        ></path>
      </g>
      <path
        id="矩形 4"
        fill-rule="evenodd"
        style="fill: #222a35"
        opacity="1"
        d="M8 80.0001L20 80.0001L20 0L8 0C3.58 0 0 3.58 0 8.00001L0 72.0001C0 76.4201 3.58 80.0001 8 80.0001Z"
      ></path>
    </g>
  </svg>
  <svg width="96" height="80" viewBox="0 0 96 80" fill="none" v-if="$attrs.name === 'white'">
    <g opacity="1" transform="translate(0)  rotate(0)">
      <g>
        <path
          id="矩形 3"
          fill-rule="evenodd"
          style="fill: #f0f2f5"
          opacity="1"
          d="M8 80L88 80C92.42 80 96 76.42 96 72L96 8C96 3.58 92.42 0 88 0L8 0C3.58 0 0 3.58 0 8L0 72C0 76.42 3.58 80 8 80Z"
        ></path>
      </g>
      <path
        id="矩形 4"
        fill-rule="evenodd"
        style="fill: #fefeff"
        opacity="1"
        d="M8 80.0001L20 80.0001L20 0L8 0C3.58 0 0 3.58 0 8.00001L0 72.0001C0 76.4201 3.58 80.0001 8 80.0001Z"
      ></path>
    </g>
  </svg>
  <svg width="96" height="80" viewBox="0 0 96 80" fill="none" v-if="$attrs.name === 'blue'">
    <g opacity="1" transform="translate(0)  rotate(0)">
      <g>
        <path
          id="矩形 3"
          fill-rule="evenodd"
          style="fill: #f0f2f5"
          opacity="1"
          d="M8 80L88 80C92.42 80 96 76.42 96 72L96 8C96 3.58 92.42 0 88 0L8 0C3.58 0 0 3.58 0 8L0 72C0 76.42 3.58 80 8 80Z"
        ></path>
      </g>
      <path
        id="矩形 4"
        fill-rule="evenodd"
        style="fill: #007fff"
        opacity="1"
        d="M8 80.0001L20 80.0001L20 0L8 0C3.58 0 0 3.58 0 8.00001L0 72.0001C0 76.4201 3.58 80.0001 8 80.0001Z"
      ></path>
    </g>
  </svg>
</template>
<script setup>
// const props = defineProps({
//   name: String
// });
// console.log(props.name);
</script>
