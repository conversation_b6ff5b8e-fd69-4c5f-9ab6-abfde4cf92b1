<!--
 * @Author: couti<PERSON><PERSON> <EMAIL>
 * @Date: 2023-12-26 15:33:21
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-26 17:33:04
 * @FilePath: /funi-cloud-web-gsbms/src/layout/components/theme/svg/menuTop.svg.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <svg width="96" height="80" viewBox="0 0 96 80" fill="none">
    <g opacity="1" transform="translate(0 0)  rotate(0)">
      <g>
        <path
          id="矩形 3"
          fill-rule="evenodd"
          style="fill: #f0f2f5"
          opacity="1"
          d="M8 80L88 80C92.42 80 96 76.42 96 72L96 8C96 3.58 92.42 0 88 0L8 0C3.58 0 0 3.58 0 8L0 72C0 76.42 3.58 80 8 80Z"
        ></path>
      </g>
      <path
        id="矩形 3"
        fill-rule="evenodd"
        style="fill: #222a35"
        opacity="1"
        d="M0 20L96 20L96 8C96 3.58 92.42 0 88 0L8 0C3.58 0 0 3.58 0 8L0 20Z"
      ></path>
    </g>
  </svg>
</template>
