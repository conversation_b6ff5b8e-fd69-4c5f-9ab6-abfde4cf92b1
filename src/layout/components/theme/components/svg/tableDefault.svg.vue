<template>
  <svg width="172" height="112" viewBox="0 0 172 112" fill="none">
    <g opacity="1" transform="translate(0)  rotate(0)">
      <g>
        <path
          id="矩形 1"
          fill-rule="evenodd"
          style="fill: #fefeff"
          opacity="1"
          d="M8 112L164 112C168.42 112 172 108.42 172 104L172 8C172 3.58 168.42 0 164 0L8 0C3.58 0 0 3.58 0 8L0 104C0 108.42 3.58 112 8 112Z"
        ></path>
      </g>
      <g opacity="1" transform="translate(10 16)  rotate(0)">
        <g opacity="1" transform="translate(0 0)  rotate(0)">
          <g opacity="1" transform="translate(0 0)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L50 20L50 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #c0c4cc"
              opacity="1"
              d="M9 12L20 12C21.1 12 22 11.1 22 10L22 10C22 8.9 21.1 8 20 8L9 8C7.9 8 7 8.9 7 10L7 10C7 11.1 7.9 12 9 12Z"
            ></path>
          </g>
          <g opacity="1" transform="translate(0 20)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #fefeff"
              opacity="1"
              d="M0 20L50 20L50 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L50 20L50 19L0 19L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #909399"
              opacity="1"
              d="M9 12L30 12C31.1 12 32 11.1 32 10L32 10C32 8.9 31.1 8 30 8L9 8C7.9 8 7 8.9 7 10L7 10C7 11.1 7.9 12 9 12Z"
            ></path>
          </g>
          <g opacity="1" transform="translate(0 40)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #fefeff"
              opacity="1"
              d="M0 20L50 20L50 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L50 20L50 19L0 19L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #909399"
              opacity="1"
              d="M9 12L41 12C42.1 12 43 11.1 43 10L43 10C43 8.9 42.1 8 41 8L9 8C7.9 8 7 8.9 7 10L7 10C7 11.1 7.9 12 9 12Z"
            ></path>
          </g>
          <g opacity="1" transform="translate(0 60)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #fefeff"
              opacity="1"
              d="M0 20L50 20L50 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L50 20L50 19L0 19L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #909399"
              opacity="1"
              d="M9 12L31 12C32.1 12 33 11.1 33 10L33 10C33 8.9 32.1 8 31 8L9 8C7.9 8 7 8.9 7 10L7 10C7 11.1 7.9 12 9 12Z"
            ></path>
          </g>
        </g>
        <g opacity="1" transform="translate(50 0)  rotate(0)">
          <g opacity="1" transform="translate(0 0)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L50 20L50 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #c0c4cc"
              opacity="1"
              d="M9 12L20 12C21.1 12 22 11.1 22 10L22 10C22 8.9 21.1 8 20 8L9 8C7.9 8 7 8.9 7 10L7 10C7 11.1 7.9 12 9 12Z"
            ></path>
          </g>
          <g opacity="1" transform="translate(0 20)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #fefeff"
              opacity="1"
              d="M0 20L50 20L50 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L50 20L50 19L0 19L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #909399"
              opacity="1"
              d="M9 12L31 12C32.1 12 33 11.1 33 10L33 10C33 8.9 32.1 8 31 8L9 8C7.9 8 7 8.9 7 10L7 10C7 11.1 7.9 12 9 12Z"
            ></path>
          </g>
          <g opacity="1" transform="translate(0 40)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #fefeff"
              opacity="1"
              d="M0 20L50 20L50 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L50 20L50 19L0 19L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #909399"
              opacity="1"
              d="M9 12L41 12C42.1 12 43 11.1 43 10L43 10C43 8.9 42.1 8 41 8L9 8C7.9 8 7 8.9 7 10L7 10C7 11.1 7.9 12 9 12Z"
            ></path>
          </g>
          <g opacity="1" transform="translate(0 60)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #fefeff"
              opacity="1"
              d="M0 20L50 20L50 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L50 20L50 19L0 19L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #909399"
              opacity="1"
              d="M9 12L31 12C32.1 12 33 11.1 33 10L33 10C33 8.9 32.1 8 31 8L9 8C7.9 8 7 8.9 7 10L7 10C7 11.1 7.9 12 9 12Z"
            ></path>
          </g>
        </g>
        <g opacity="1" transform="translate(100 0)  rotate(0)">
          <g opacity="1" transform="translate(0 0)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L52 20L52 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #c0c4cc"
              opacity="1"
              d="M9.28003 12L20.8801 12C21.9801 12 22.8801 11.1 22.8801 10L22.8801 10C22.8801 8.9 21.9801 8 20.8801 8L9.28003 8C8.18003 8 7.28003 8.9 7.28003 10L7.28003 10C7.28003 11.1 8.18003 12 9.28003 12Z"
            ></path>
          </g>
          <g opacity="1" transform="translate(0 20)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #fefeff"
              opacity="1"
              d="M0 20L52 20L52 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L52 20L52 19L0 19L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #909399"
              opacity="1"
              d="M9 12L32 12C33.1 12 34 11.1 34 10L34 10C34 8.9 33.1 8 32 8L9 8C7.9 8 7 8.9 7 10L7 10C7 11.1 7.9 12 9 12Z"
            ></path>
          </g>
          <g opacity="1" transform="translate(0 40)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #fefeff"
              opacity="1"
              d="M0 20L52 20L52 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L52 20L52 19L0 19L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #909399"
              opacity="1"
              d="M9.28003 12L42.7201 12C43.8201 12 44.7201 11.1 44.7201 10L44.7201 10C44.7201 8.9 43.8201 8 42.7201 8L9.28003 8C8.18003 8 7.28003 8.9 7.28003 10L7.28003 10C7.28003 11.1 8.18003 12 9.28003 12Z"
            ></path>
          </g>
          <g opacity="1" transform="translate(0 60)  rotate(0)">
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #fefeff"
              opacity="1"
              d="M0 20L52 20L52 0L0 0L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #f0f2f5"
              opacity="1"
              d="M0 20L52 20L52 19L0 19L0 20Z"
            ></path>
            <path
              id="矩形 6"
              fill-rule="evenodd"
              style="fill: #909399"
              opacity="1"
              d="M9.28003 12L32.7201 12C33.8201 12 34.7201 11.1 34.7201 10L34.7201 10C34.7201 8.9 33.8201 8 32.7201 8L9.28003 8C8.18003 8 7.28003 8.9 7.28003 10L7.28003 10C7.28003 11.1 8.18003 12 9.28003 12Z"
            ></path>
          </g>
        </g>
      </g>
    </g>
    <defs>
      <filter
        id="filter_1"
        x="0"
        y="0"
        width="180"
        height="121"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="feFloodId" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />

        <feOffset dx="0" dy="1" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="dropShadow_1" />
        <feBlend mode="normal" in="SourceGraphic" in2="dropShadow_2" result="shape" />
      </filter>
    </defs>
  </svg>
</template>
