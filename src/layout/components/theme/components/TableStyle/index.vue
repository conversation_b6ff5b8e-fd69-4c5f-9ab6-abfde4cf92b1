<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-12-27 14:31:10
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-17 20:37:16
 * @FilePath: /funi-paas-cs-web-cli/src/layout/components/theme/components/TableStyle/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <ThemeTitle>列表样式</ThemeTitle>
    <div class="funi-theme__menu">
      <div v-for="item in group">
        <div
          class="funi-theme__menu__item funi-theme__cursor"
          :class="{
            selected: modelValue == item.value
          }"
          @click="check(item.value)"
        >
          <component :is="item.components"></component>
        </div>
        <span class="title-name">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref } from 'vue';
import TableDefault from './../svg/tableDefault.svg.vue';
import TableBorder from './../svg/tableBorder.svg.vue';
import ThemeTitle from './../Title/index.vue';
import useThemeConfigStore from './../../hooks/setTheme.js';
const themeConfigStore = useThemeConfigStore();
const group = ref([
  {
    components: TableDefault,
    value: 'default',
    name: '默认列表样式'
  },
  {
    components: TableBorder,
    value: 'border',
    name: '带纵向分隔线'
  }
]);
const props = defineProps({
  modelValue: {
    type: String,
    default: 'default'
  }
});
const emit = defineEmits(['update:modelValue']);

const check = type => {
  emit('update:modelValue', type);
  themeConfigStore.setTableBorder(type, true);
};
</script>
<style scoped>
@import url('./../style/comm.css');
</style>
