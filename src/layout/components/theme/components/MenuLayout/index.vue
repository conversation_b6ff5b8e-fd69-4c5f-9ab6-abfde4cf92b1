<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-12-27 14:31:10
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-08 18:05:56
 * @FilePath: /funi-cloud-web-gsbms/src/layout/components/theme/components/MenuLayout/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <ThemeTitle>导航模式</ThemeTitle>
    <div class="funi-theme__menu">
      <div v-for="item in group">
        <div
          class="funi-theme__menu__item funi-theme__cursor"
          :class="{
            selected: modelValue == item.value
          }"
          @click="check(item.value)"
        >
          <component :is="item.components"></component>
        </div>
        <span class="title-name">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref } from 'vue';
import MenuDefault from './../svg/menuDefault.svg.vue';
import MenuTop from './../svg/menuTop.svg.vue';
import ThemeTitle from './../Title/index.vue';
import useThemeConfigStore from './../../hooks/setTheme.js';
const themeConfigStore = useThemeConfigStore();

const group = ref([
  {
    components: MenuDefault,
    value: 'vertical',
    name: '默认菜单布局'
  },
  {
    components: MenuTop,
    value: 'horizontal',
    name: '顶部菜单布局'
  }
]);
const props = defineProps({
  modelValue: {
    type: String,
    default: 'vertical'
  }
});
const emit = defineEmits(['update:modelValue']);

const check = type => {
  emit('update:modelValue', type);
  themeConfigStore.setMenuLayout(type, true);
};
</script>
<style scoped>
@import url('./../style/comm.css');
</style>
