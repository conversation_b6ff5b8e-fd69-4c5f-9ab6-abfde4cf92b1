* {
    box-sizing: border-box;
}

.funi-theme__cursor {
    cursor: pointer;
}

.theme-drawer__title {
    font-size: 18px;
    color: rgba(0, 0, 0, 1);
    flex: 0 0 auto;
    margin-right: 16px;
}

.funi-theme__drawer :deep(header.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px;
    border-bottom: 1px solid var(--el-color-info-light-9);
}

.funi-theme__drawer :deep(.el-drawer__body) {
    padding: 16px;
}

.funi-theme__config {
    display: flex;
    flex-direction: column;
    gap: 40px;
}