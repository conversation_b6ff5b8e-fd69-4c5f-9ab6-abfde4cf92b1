
import { defineStore } from 'pinia';
import { ref } from 'vue'
import { getColors } from 'theme-colors';
import { useLayoutStore } from '@/layout/useLayoutStore'
import AppApis from '@/apis/app';
import FuniJS from '@funi-lib/utils';
const Utils = FuniJS;
let controller = void 0
export default defineStore('theme_config', () => {
    const defaultConfig = {
        color: '#007fff',
        head_bgc: 'black',
        menu_bgc: 'white',
        size: 'default',
        table: 'default',
        form: 'default',
        menu: 'vertical',
        openTabs: true
    }
    const themeConfig = ref(
        Utils.clone(defaultConfig, true)
    )
    const configData = ref({})
    /**
     * @description 重置配置
     * **/
    const resetConfig = () => {
        setThemeConfig(Utils.clone(defaultConfig, true), true)
    }
    /**
     * @description 初始化请求
     * **/
    const getInitConfig = async () => {
        let data = await AppApis.unifyAccountConfigDetailMy()
        configData.value = data
        let extendParams = configData.value.extendParams ? JSON.parse(configData.value.extendParams) : void 0
        setThemeConfig(extendParams, false)

    }
    /**
     * @description 设置全局样式
     * @param {object} extendParams 扩展信息
     * @param {true | false} bool 是否请求后端
     ***/
    const setThemeConfig = async (extendParams, bool = false) => {
        let enums = {
            color: setGlobalColor,
            head_bgc: setHeadColor,
            menu_bgc: setMenuColor,
            size: setGlobalFontSize,
            menu: setMenuLayout,
            openTabs: setOpenTabs,
            table: setTableBorder,
            form: setFormBorder
        }
        if (extendParams) {
            Object.keys(extendParams).forEach(key => {
                themeConfig.value[key] = extendParams[key]
                if (enums[key] && typeof enums[key] === 'function')
                    enums[key](extendParams[key])
            })
            if (bool) setUserThemeConfig()
        }

    }
    /**
     * @description 设置全局color
     * @param {string} value color值
     * @param {true | false} bool 是否请求api存储数据
    ***/
    const setGlobalColor = (value, bool = false) => {
        if (!value) return
        const colors = Object.values(getColors(value));
        let list = ['9', '8', '7', '5', '3', void 0, '2', '4', '6', '10'];
        list.forEach((item, index) => {
            let str = '--el-color-primary';
            if (item && index < 6) str = `${str}-light-${item}`;
            else if (item && index > 5) str = `${str}-dark-${item}`
            document.documentElement.style.setProperty(str, colors[index]);
        });
        if (bool) setUserThemeConfig()
    }

    /**
     * @description 设置菜单颜色主题
     * @param {'white' | 'black' | 'blue'} value color值
     * @param {true | false} bool 是否请求api存储数据
     ***/
    const setMenuColor = (value, bool = false) => {
        let enums = {
            white: {
                '--aside-menu-bg-color': 'var(--el-nav-color-light)',
                '--aside-menu-hover-bg-color': 'var(--el-nav-color-light-hover)',
                '--aside-menu-active-bg-color': 'var(--el-nav-color-light-hover)',
                '--el-menu-text-color': 'var(--el-text-color-primary)'
            },
            black: {
                '--aside-menu-bg-color': 'var(--el-nav-color-dark)',
                '--aside-menu-hover-bg-color': 'var(--el-nav-color-dark-hover)',
                '--aside-menu-active-bg-color': 'var(--el-nav-color-dark-hover)',
                '--el-menu-text-color': 'var(--el-text-color-white)'
            },
            blue: {
                '--aside-menu-bg-color': 'var(--el-nav-color-blue)',
                '--aside-menu-hover-bg-color': 'var(--el-nav-color-blue-hover)',
                '--aside-menu-active-bg-color': 'var(--el-nav-color-blue-hover)',
                '--el-menu-text-color': 'var(--el-text-color-white)'
            }
        }
        if (!value || !enums[value]) return
        Object.keys(enums[value]).forEach(key => {
            document.documentElement.style.setProperty(key, enums[value][key]);
        })
        if (bool) setUserThemeConfig()
    }


    /**
     * @description 设置头部颜色主题
     * @param {'white' | 'black' | 'blue'} value color值
     * @param {true | false} bool 是否请求api存储数据
     ***/
    const setHeadColor = (value, bool = false) => {
        let enums = {
            white: {
                '--header-bg-color': 'var(--el-nav-color-light)',
                '--header-bg-gary-color': 'var(--el-nav-color-light-gray)',
                '--header-bg-hover-color': 'var(--el-nav-color-light-hover)',
                '--header-text-color': 'var(--el-text-color-primary)'
            },
            black: {
                '--header-bg-color': 'var(--el-nav-color-dark)',
                '--header-bg-gary-color': 'var(--el-nav-color-dark-gray)',
                '--header-bg-hover-color': 'var(--el-nav-color-dark-hover)',
                '--header-text-color': 'var(--el-text-color-white)'
            },
            blue: {
                '--header-bg-color': 'var(--el-nav-color-blue)',
                '--header-bg-gary-color': 'var(--el-nav-color-blue-gray)',
                '--header-bg-hover-color': 'var(--el-nav-color-blue-hover)',
                '--header-text-color': 'var(--el-text-color-white)'
            }
        }
        if (!value || !enums[value]) return
        Object.keys(enums[value]).forEach(key => {
            document.documentElement.style.setProperty(key, enums[value][key]);
        })
        if (bool) setUserThemeConfig()
    }



    /**
     * @description 设置菜单导航模式
     * @param {'vertical' | 'horizontal'} value color值
     * @param {true | false} bool 是否请求api存储数据
     * **/
    const setMenuLayout = (value, bool = false) => {
        if (!value) return
        const layoutStore = useLayoutStore()
        layoutStore.toggleLayoutMode(value)
        if (bool) setUserThemeConfig()
    }


    /**
     * @description 设置全局字体大小
     * @param {'default' | 'large' | 'small'} value color值
     * @param {true | false} bool 是否请求api存储数据
     * **/
    const setGlobalFontSize = (value, bool = false) => {
        let enums = {
            default: {
                '--el-font-size-extra-large': '20px',
                '--el-font-size-large': '18px',
                '--el-font-size-medium': '16px',
                '--el-font-size-base': '14px',
                '--el-font-size-small': '13px',
                '--el-font-size-extra-small': '12px'
            },
            large: {
                '--el-font-size-extra-large': '22px',
                '--el-font-size-large': '20px',
                '--el-font-size-medium': '18px',
                '--el-font-size-base': '16px',
                '--el-font-size-small': '14px',
                '--el-font-size-extra-small': '13px'
            },
            small: {
                '--el-font-size-extra-large': '18px',
                '--el-font-size-large': '16px',
                '--el-font-size-medium': '14px',
                '--el-font-size-base': '13px',
                '--el-font-size-small': '12px',
                '--el-font-size-extra-small': '12px'
            }
        }
        if (!value || !enums[value]) return
        Object.keys(enums[value]).forEach(key => {
            document.documentElement.style.setProperty(key, enums[value][key]);
        })
        if (bool) setUserThemeConfig()
    }

    /**
     * @description 设置是否开启都多页签
     * @param {true | false} value true开启，false关闭
     * @param {true | false} bool 是否请求api存储数据
     * **/
    const setOpenTabs = (value, bool = false) => {
        if ($utils.isNil(value)) return
        if (bool) setUserThemeConfig()
    }


    /**
     * @description 表格网格设置
     * @param {'default' | 'border'}  value 
     * @param {true | false}  bool 
     * **/
    const setTableBorder = (value, bool = false) => {
        let enums = {
            default: {
                '--funi-curd-border-right': 'none',
                '--funi-curd-border-around-width': '0'
            },
            border: {
                '--funi-curd-border-right': '1px solid var(--el-border-color-lighter)',
                '--funi-curd-border-around-width': '1px'
            }
        }
        if (!value || !enums[value]) return
        Object.keys(enums[value]).forEach(key => {
            document.documentElement.style.setProperty(key, enums[value][key]);
        })

        if (bool) setUserThemeConfig()
    }
    /**
     * @description 表单网格设置
     * @param {'default' | 'border'}  value 
     * @param {true | false}  bool 
     * **/
    const setFormBorder = (value, bool = false) => {
        let enums = {
            default: {
                '--funi-form-border-color': '#fff',
                '--funi-form-label-bgc': '#fff'
            },
            border: {
                '--funi-form-border-color': 'var(--el-border-color-lighter)',
                '--funi-form-label-bgc': '#f8f8f8'
            }
        }
        if (!value || !enums[value]) return
        Object.keys(enums[value]).forEach(key => {
            document.documentElement.style.setProperty(key, enums[value][key]);
        })

        if (bool) setUserThemeConfig()
    }





    const setUserThemeConfig = () => {
        if (controller && controller.abort) controller.abort();
        controller = new AbortController();
        AppApis.unifyAccountConfigUpdateMy({
            ...configData.value,
            extendParams: JSON.stringify(themeConfig.value)
        }, {
            signal: controller.signal
        })
    }


    return {
        themeConfig,
        setThemeConfig,
        setGlobalColor,
        setMenuColor,
        setUserThemeConfig,
        setMenuLayout,
        setHeadColor,
        setGlobalFontSize,
        setOpenTabs,
        resetConfig,
        getInitConfig,
        setTableBorder,
        setFormBorder
    }

})



