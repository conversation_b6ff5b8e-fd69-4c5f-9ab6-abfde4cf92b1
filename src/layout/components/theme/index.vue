<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-12-26 14:18:45
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-09 20:26:19
 * @FilePath: /funi-cloud-erm-ui/src/layout/components/theme/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="funi-theme__drawer">
    <el-drawer v-model="drawer" direction="rtl" :size="400">
      <template #header="{ close, titleId, titleClass }">
        <div style="display: flex; gap: 16px">
          <h2 :id="titleId" :class="[titleClass, 'theme-drawer__title']">主题风格设置</h2>
          <el-button type="primary" link @click="resetConfig">重置</el-button>
        </div>
      </template>
      <template #default>
        <div class="funi-theme__config">
          <MenuLayout v-model="themeConfig.menu"></MenuLayout>
          <div>
            <HeadColor v-model="themeConfig.head_bgc"></HeadColor>
            <MenuColor
              v-if="themeConfig.menu === 'vertical'"
              style="margin-top: 16px"
              v-model="themeConfig.menu_bgc"
            ></MenuColor>
          </div>
          <ColorGroup v-model="themeConfig.color"></ColorGroup>
           <TableStyle v-model="themeConfig.table"></TableStyle>
          <FormStyle v-model="themeConfig.form"></FormStyle>
          <div>
            <ThemeTitle>字号</ThemeTitle>
            <div class="funi-theme__size">
              <el-radio-group v-model="themeConfig.size" @change="sizeChange">
                <el-radio-button v-for="item in size" :key="item.value" :label="item.value">{{
                  item.name
                }}</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div>
            <ThemeTitle>开启多页签</ThemeTitle>
            <div>
              <el-switch @change="openTabsChange" v-model="themeConfig.openTabs" />
            </div>
          </div>
          <!-- <TableStyle v-model="themeConfig.table"></TableStyle>
          <FormStyle v-model="themeConfig.form"></FormStyle> -->
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="jsx">
import { ref, watch, unref } from 'vue';
import useThemeConfigStore from './hooks/setTheme.js';
import MenuLayout from './components/MenuLayout/index.vue';
import ColorGroup from './components/ColorGroup/index.vue';
import ThemeTitle from './components/Title/index.vue';
import HeadColor from './components/HeadColor/index.vue';
import MenuColor from './components/MenuColor/index.vue';
import TableStyle from './components/TableStyle/index.vue';
import FormStyle from './components/FormStyle/index.vue';

const drawer = ref(false);
const themeConfigStore = useThemeConfigStore();

const size = ref([
  { name: '默认', value: 'default' },
  { name: '偏大', value: 'large' },
  { name: '偏小', value: 'small' }
]);

const themeConfig = themeConfigStore.themeConfig;

const show = () => {
  drawer.value = true;
};
const sizeChange = e => {
  themeConfigStore.setGlobalFontSize(e, true);
};

const openTabsChange = e => {
  themeConfigStore.setOpenTabs(e, true);
};

const resetConfig = () => {
  themeConfigStore.resetConfig();
};
defineExpose({
  show
});
</script>
<style scoped>
@import url('./index.css');
</style>
