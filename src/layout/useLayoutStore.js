/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2024-01-08 11:07:16
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-09 16:27:54
 * @FilePath: /funi-cloud-web-gsbms/src/layout/useLayoutStore.js
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
import { defineStore } from 'pinia';
import { ref, unref } from 'vue';

export const useLayoutStore = defineStore('layout', () => {
  // collapse
  const collapse = ref(false);
  const layoutMode = ref('vertical');
  const toggleCollapse = () => (collapse.value = !unref(collapse));
  const toggleLayoutMode = (value) => {
    layoutMode.value = value;

  };
  // multi tab
  const activeTab = ref('');

  const activeMenu = ref({});
  return {
    collapse,
    toggleCollapse,
    layoutMode,
    toggleLayoutMode,
    activeTab,
    activeMenu
  };
});
