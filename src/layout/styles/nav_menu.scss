@use 'element-plus/theme-chalk/src/mixins/mixins' as el;

@use '@/styles/mixins/functions.scss' as *;
@use '@/styles/mixins/utils' as *;
@use './var.scss';

@include b(layout-nav-menu) {
  @include el.set-css-var-value('menu-bg-color', var(--nav-menu-bg-color));
  @include el.set-css-var-value('menu-hover-bg-color', var(--nav-menu-hover-bg-color));

  @include el.set-css-var-value('menu-active-color', var(--nav-menu-active-color));
  @include el.set-css-var-value('menu-sub-item-height', var(--nav-menu-sub-item-height));
  @include el.set-css-var-value('menu-item-height', var(--nav-menu-item-height));
  @include el.set-css-var-value('menu-item-font-size', 12px);
  @include el.set-css-var-value('menu-base-level-padding', 20px);
  @include el.set-css-var-value('menu-icon-width', 16px);
}

@include b(layout-nav-menu) {
  position: relative;
  box-sizing: border-box;
  flex-grow: 1;
  height: 100%;
  background-color: getCssVar('header', 'bg-color');
  overflow-x: auto;

  :deep() {
    $a: '';

    @include el.b(menu) {
      border: none;
      width: 100%;
      height: 100%;

      .iconify {
        margin-right: 10px;
        width: el.getCssVar('menu', 'icon-width');
        text-align: center;
        font-size: 16px;
        vertical-align: middle;
        flex-shrink: 0;
      }

      &>.#{el.$namespace}-menu-item {
        color: getCssVar('header', 'text-color');
        border: none;
        padding-right: el.getCssVar('menu', 'base-level-padding');
        line-height: 22px;
        background-color: unset;

        &:hover {
          background-color: var(--header-bg-hover-color);
          color: var(--header-text-color);
        }

        &.is-active {
          background-color: var(--header-bg-hover-color);
          color: var(--header-text-color);
        }
      }
    }

    @include el.b(sub-menu) {
      @include el.e(title) {
        color: getCssVar('header', 'text-color');
        border: none;
        padding-right: calc(var(--el-menu-base-level-padding) + 22px);
        line-height: 22px;

        &:hover {
          background-color: var(--header-bg-hover-color);
          color: var(--header-text-color);
        }
      }

      &.is-active,
      &.is-opened {
        @include el.e(title) {
          background-color: var(--header-bg-hover-color);
          color: var(--header-text-color);
        }
      }
    }

    @include el.b(button) {
      @include el.set-css-var-value('button-bg-color', clear);
      @include el.set-css-var-value('button-hover-bg-color', clear);
      border: none;
    }
  }
}

.#{el.$namespace}-menu {
  background-color: getCssVar('header', 'bg-color');
}

.#{$namespace}-layout-nav-menu--horizontal.#{el.$namespace}-menu--horizontal {
  &>.#{el.$namespace}-menu {
    &>.#{el.$namespace}-menu-item {
      color: var(--el-text-color-primary);

      &.is-active,
      &:hover {
        color: var(--el-color-primary);
      }

      .iconify {
        margin-right: 5px;
        width: var(--el-menu-icon-width);
        text-align: center;
        font-size: 18px;
        vertical-align: middle;
        flex-shrink: 0;
      }
    }
  }
}