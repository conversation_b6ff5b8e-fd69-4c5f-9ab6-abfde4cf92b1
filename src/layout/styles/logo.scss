@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
@use '@/styles/mixins/functions.scss' as *;
@use '@/styles/mixins/utils.scss' as *;
@use './var.scss';

@include b(layout-logo) {
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  text-decoration: none;
  padding-left: 20px;
  cursor: pointer;
  height: getCssVar('logo', 'height');

  &__img {
    width: auto;
    height: 30px;
  }

  &__label {
    font-size: var(--el-font-size-base);
    font-weight: 500;
    // color: white;
    margin-left: 8px;
  }
}
