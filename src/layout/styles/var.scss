@use 'sass:map';
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;

$header: () !default;
$header: map.merge(
  (
    'height': 48px,
    'text-color': el.getCssVar('text-color', 'white'),
    'hover-color': #f6f6f6,
    'bg-color': el.getCssVar('nav-color', 'dark'),
    'bg-gary-color': el.getCssVar('nav-color', 'dark-gray'),
    'bg-hover-color': el.getCssVar('nav-color', 'dark-hover')
  ),
  $header
);

$aside-menu: () !default;
$aside-menu: map.merge(
  (
    'max-width': 200px,
    'min-width': 48px,
    'bg-color': #fff,
    'hover-bg-color': el.getCssVar('nav-color', 'light-hover'),
    'active-bg-color': el.getCssVar('nav-color', 'light-hover'),
    'text-color': #fff,
    'active-color': var(--el-color-primary),
    'item-height': 40px,
    'sub-item-height': 40px
  ),
  $aside-menu
);

$nav-menu: () !default;
$nav-menu: map.merge(
  (
    'max-width': 120px,
    'min-width': 48px,
    'bg-color': el.getCssVar('nav-color', 'dark'),
    'hover-bg-color': #027fff,
    'active-bg-color': #027fff,
    'text-color': el.getCssVar('text-color', 'placeholder'),
    'active-color': #fff,
    'item-height': 40px,
    'sub-item-height': 40px
  ),
  $nav-menu
);

$logo: () !default;
$logo: map.merge(
  (
    'height': 48px,
    'title-text-color': #fff,
    'border-color': 'inherit'
  ),
  $logo
);

$multi-tab: () !default;
$multi-tab: map.merge(
  (
    'height': 40px,
    'item-height': 32px,
    'text-color': el.getCssVar('text-color', 'regular'),
    'text-color-active': el.getCssVar('color', 'primary'),
    'text-color-hover': el.getCssVar('color', 'primary'),
    'border': 1px solid #{el.getCssVar('border-color', 'light')}
  ),
  $multi-tab
);

$app-content: () !default;
$app-content: map.merge(
  (
    'padding': 24px,
    'bg-color': #e4f2ff
  ),
  $app-content
);

$funi-curd: () !default;
$funi-curd: map.merge(
  (
    'border-right': none,
    'border-around-width': 0
  ),
  $funi-curd
);
$funi-form: () !default;
$funi-form: map.merge(
  (
    'border-color': #fff,
    'label-bgc': #fff
  ),
  $funi-form
);
