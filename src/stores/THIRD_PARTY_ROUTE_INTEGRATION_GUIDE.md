# Funi PaaS 第三方路由集成开发指南

## 概述

本文档面向第三方开发者，详细说明如何在 Funi PaaS 平台中正确配置路由表，实现自动菜单生成和权限管理集成。

## 1. 路由表结构规范

### 1.1 基础结构

路由配置基于 Vue Router 标准格式，采用单一路由对象导出方式：

```javascript
// src/apps/{system-code}/routers/index.js
export default {
  path: '/system-code', // 必需：系统路径，用于提取系统编码
  name: 'SystemName', // 必需：路由名称
  redirect: '/system-code/home', // 可选：重定向路径
  meta: {
    // 必需：元数据配置
    title: '系统名称', // 必需：系统显示名称
    icon: 'system-icon', // 可选：系统图标
    isMenu: true // 可选：是否显示在菜单中
  },
  children: [
    // 可选：子路由数组
    // 子路由配置...
  ]
};
```

### 1.2 字段详细说明

| 字段               | 类型     | 必需 | 说明                             |
| ------------------ | -------- | ---- | -------------------------------- |
| `path`             | String   | ✅   | 路由路径，系统编码从此字段提取   |
| `name`             | String   | ✅   | 路由名称，用于路由跳转           |
| `component`        | Function | ❌   | 组件导入函数，父级路由通常不设置 |
| `redirect`         | String   | ❌   | 重定向路径                       |
| `meta`             | Object   | ✅   | 路由元数据                       |
| `meta.title`       | String   | ✅   | 显示标题，用于菜单和页面标题     |
| `meta.icon`        | String   | ❌   | 图标名称                         |
| `meta.isMenu`      | Boolean  | ❌   | 菜单显示控制                     |
| `meta.permissions` | Array    | ❌   | 权限码数组                       |
| `children`         | Array    | ❌   | 子路由配置                       |

## 2. 关键配置规则

### 2.1 系统编码提取规则

系统编码自动从根路由的 `path` 字段提取：

```javascript
// path: '/example-system' → systemCode: 'example-system'
// path: '/user-management' → systemCode: 'user-management'
```

**注意事项：**

- 路径必须以 `/` 开头
- 系统编码建议使用 kebab-case 格式
- 系统编码将用于组件路径生成

### 2.2 菜单显示控制 (isMenu)

`meta.isMenu` 字段控制路由是否显示在菜单中：

| 值      | 行为           | 说明                               |
| ------- | -------------- | ---------------------------------- |
| `true`  | 显示在菜单中   | 明确标记为菜单项                   |
| `false` | 不显示在菜单中 | 明确标记为非菜单项，但路由仍可访问 |
| 未设置  | 不显示在菜单中 | 默认行为，路由仍可访问             |

**重要：** 没有 `meta.title` 的路由无论 `isMenu` 值如何都不会显示在菜单中。

### 2.3 父级路由最佳实践

包含 `children` 的父级路由通常不配置 `component`：

```javascript
{
  path: 'users',
  name: 'UserManagement',
  meta: { title: '用户管理', icon: 'user', isMenu: true },
  // 不设置 component，仅作为菜单容器
  children: [
    {
      path: 'list',
      name: 'UserList',
      component: () => import('@/apps/example-system/views/UserList.vue'),
      meta: { title: '用户列表', icon: 'list', isMenu: true }
    }
  ]
}
```

**原因：**

- 父级路由主要用于菜单分组
- 避免不必要的页面组件加载
- 保持路由结构清晰

## 3. 菜单生成机制

### 3.1 菜单层级关系

系统自动根据路由嵌套结构生成菜单层级：

```
根菜单 (固定: "Bpaas平台")
├── 系统菜单 (从根路由生成)
│   ├── 二级菜单 (从 children[0] 生成)
│   │   └── 三级菜单 (从 children[0].children[0] 生成)
│   └── 二级菜单 (从 children[1] 生成)
└── 其他系统...
```

### 3.2 菜单生成规则

1. **系统级菜单**：自动从根路由的 `meta.title` 生成
2. **子级菜单**：根据 `meta.isMenu: true` 和 `meta.title` 生成
3. **菜单排序**：按照路由在数组中的顺序排序
4. **图标显示**：从 `meta.icon` 字段获取

## 4. 实际示例

### 4.1 完整路由配置示例

```javascript
export default {
  path: '/example-system',
  name: 'ExampleSystem',
  redirect: '/example-system/dashboard',
  meta: { title: '示例系统', icon: 'example', isMenu: true },
  children: [
    // 简单页面路由
    {
      path: 'dashboard',
      name: 'ExampleDashboard',
      component: () => import('@/apps/example-system/views/Dashboard.vue'),
      meta: { title: '系统概览', icon: 'dashboard', isMenu: true }
    },

    // 嵌套菜单结构
    {
      path: 'users',
      name: 'ExampleUsers',
      meta: { title: '用户管理', icon: 'user', isMenu: true },
      children: [
        {
          path: 'list',
          name: 'ExampleUserList',
          component: () => import('@/apps/example-system/views/UserList.vue'),
          meta: { title: '用户列表', icon: 'list', isMenu: true }
        },
        {
          path: 'create',
          name: 'ExampleUserCreate',
          component: () => import('@/apps/example-system/views/UserForm.vue'),
          meta: { title: '新增用户', icon: 'plus', isMenu: true }
        },
        {
          path: 'edit/:id',
          name: 'ExampleUserEdit',
          component: () => import('@/apps/example-system/views/UserForm.vue'),
          meta: { title: '编辑用户', icon: 'edit', isMenu: false }
        }
      ]
    },

    // 隐藏页面（不显示在菜单中）
    {
      path: 'hidden-page',
      name: 'ExampleHiddenPage',
      component: () => import('@/apps/example-system/views/Dashboard.vue'),
      meta: { title: '隐藏页面', icon: 'hidden' }
      // 没有 isMenu 字段，默认不显示在菜单中
    },

    // 明确隐藏的页面
    {
      path: 'api-docs',
      name: 'ExampleApiDocs',
      component: () => import('@/apps/example-system/views/Dashboard.vue'),
      meta: { title: 'API文档', icon: 'api', isMenu: false }
    }
  ]
};
```

### 4.2 不同场景的配置方法

#### 场景 1：显示在菜单中的页面

```javascript
{
  path: 'dashboard',
  name: 'Dashboard',
  component: () => import('@/apps/system/views/Dashboard.vue'),
  meta: { title: '仪表板', icon: 'dashboard', isMenu: true }
}
```

#### 场景 2：不显示在菜单中的页面

```javascript
{
  path: 'detail/:id',
  name: 'Detail',
  component: () => import('@/apps/system/views/Detail.vue'),
  meta: { title: '详情页', isMenu: false }
}
```

#### 场景 3：嵌套菜单结构

```javascript
{
  path: 'management',
  name: 'Management',
  meta: { title: '管理中心', icon: 'setting', isMenu: true },
  children: [
    {
      path: 'users',
      name: 'UserManagement',
      component: () => import('@/apps/system/views/Users.vue'),
      meta: { title: '用户管理', icon: 'user', isMenu: true }
    }
  ]
}
```

## 5. 技术要求

### 5.1 文件位置规范

路由配置文件必须放置在指定位置：

```
src/apps/{system-code}/routers/index.js
```

**示例：**

- `src/apps/user-system/routers/index.js`
- `src/apps/order-management/routers/index.js`

### 5.2 组件路径规范

组件导入路径必须遵循以下格式：

```javascript
// 正确格式
component: () => import('@/apps/{system-code}/views/ComponentName.vue');

// 示例
component: () => import('@/apps/example-system/views/Dashboard.vue');
component: () => import('@/apps/user-system/views/UserList.vue');
```

**注意事项：**

- 必须使用 `@/apps/` 前缀
- 组件文件必须在对应系统的 `views` 目录下
- 支持子目录结构

## 6. 常见错误和解决方案

### 6.1 菜单不显示

**问题：** 配置的路由没有出现在菜单中

**可能原因和解决方案：**

1. **缺少 `meta.title`**

   ```javascript
   // ❌ 错误
   meta: { isMenu: true }

   // ✅ 正确
   meta: { title: '页面标题', isMenu: true }
   ```

2. **未设置 `isMenu: true`**

   ```javascript
   // ❌ 错误（默认不显示）
   meta: { title: '页面标题' }

   // ✅ 正确
   meta: { title: '页面标题', isMenu: true }
   ```

### 6.2 组件加载失败

**问题：** 页面访问时组件加载失败

**解决方案：**

1. 检查组件路径是否正确
2. 确保组件文件存在
3. 验证导入路径格式

```javascript
// ✅ 正确的路径格式
component: () => import('@/apps/example-system/views/Dashboard.vue');
```

### 6.3 系统编码提取错误

**问题：** 系统编码提取不正确

**解决方案：** 确保根路由 `path` 格式正确：

```javascript
// ✅ 正确
export default {
  path: '/example-system' // 以 / 开头，使用 kebab-case
  // ...
};
```

## 7. 最佳实践总结

1. **明确设置 `isMenu` 字段**：避免依赖默认行为
2. **保持路径命名一致性**：使用 kebab-case 格式
3. **合理的菜单层级**：避免过深的嵌套结构
4. **组件路径规范**：严格遵循 `@/apps/{system-code}/views/` 格式
5. **父级路由设计**：包含子路由的父级通常不设置 `component`

## 8. 技术支持

如果在集成过程中遇到问题，请：

1. 检查浏览器控制台的错误信息
2. 验证路由配置格式是否正确
3. 确认文件路径和组件路径是否存在
4. 参考本文档的示例代码进行对比

遵循本指南的规范，您可以快速、正确地将第三方系统集成到 Funi PaaS 平台中。
