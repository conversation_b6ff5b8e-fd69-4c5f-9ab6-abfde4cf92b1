import { defineStore } from 'pinia';
import { ref, watch } from 'vue';

/**
 * 提取组件路径
 * 从 component: () => import('@/apps/ccs/pages/xxx.vue') 中提取路径
 */
function extractComponentPath(component, systemCode) {
  if (!component || typeof component !== 'function') {
    return null;
  }

  const componentStr = component.toString();

  // 尝试多种正则表达式模式以适应不同的构建环境
  const patterns = [
    // 开发环境: () => import('@/apps/xxx/views/Component.vue')
    /import\(['"`]@\/apps\/(.+?)['"`]\)/,
    // 构建后可能的形式: () => import('/src/apps/xxx/views/Component.vue')
    /import\(['"`]\/src\/apps\/(.+?)['"`]\)/,
    // 相对路径形式: () => import('./apps/xxx/views/Component.vue')
    /import\(['"`]\.\/apps\/(.+?)['"`]\)/,
    // 更宽松的匹配: 任何包含 apps/ 的路径
    /apps\/([^'"`\)]+)/
  ];

  for (const pattern of patterns) {
    const match = componentStr.match(pattern);
    if (match) {
      let fullPath = match[1];

      // 去掉查询参数（如 ?t=1751533715948）
      const queryIndex = fullPath.indexOf('?');
      if (queryIndex !== -1) {
        fullPath = fullPath.substring(0, queryIndex);
      }

      // 如果路径已经以系统编码开头，直接返回
      if (fullPath.startsWith(systemCode + '/')) {
        return fullPath;
      }
      // 否则，确保以系统编码开头
      return `${systemCode}/${fullPath}`;
    }
  }

  return null;
}

/**
 * 构建完整路径
 * 处理相对路径和绝对路径的拼接
 */
function buildFullPath(route, parentPath = '') {
  let fullPath;

  if (route.path.startsWith('/')) {
    // 绝对路径
    fullPath = route.path;
  } else {
    // 相对路径，需要拼接父级路径
    const cleanParentPath = parentPath.endsWith('/') ? parentPath.slice(0, -1) : parentPath;
    fullPath = `${cleanParentPath}/${route.path}`;
  }

  // 清理重复的斜杠
  return fullPath.replace(/\/+/g, '/');
}

/**
 * 检查路由是否应该在菜单中显示
 * 优先检查 meta.isMenu 字段，如果不存在则认为不是菜单
 * @param {Object} route - 路由对象
 * @param {number} level - 当前路由的层级深度（根节点为1级）
 */
function shouldShowInMenu(route, level) {
  // 没有标题的路由不显示
  if (!route.meta?.title) {
    return false;
  }

  // 优先检查 meta.isMenu 字段
  if (route.meta.hasOwnProperty('isMenu')) {
    return Boolean(route.meta.isMenu);
  }

  // 如果没有 isMenu 字段，则认为不是菜单
  return false;
}

/**
 * 创建页面信息
 */
function createPage(route, systemCode, systemId, fullPath, pageInfoList, menuPageRelationList, menuId) {
  const pageId = $utils.guid();

  // 构建 params 字段
  const params = {};

  // 提取组件路径
  const componentPath = extractComponentPath(route.component, systemCode);

  // 添加其他自定义 params 参数
  if (route.meta) {
    const excludeKeys = ['title', 'icon', 'permissions'];
    Object.keys(route.meta).forEach(key => {
      if (!excludeKeys.includes(key)) {
        params[key] = route.meta[key];
      }
    });
  }

  // 构建 paramJson 字段
  let paramJson = '';

  // 如果有组件路径，优先构建包含组件路径的 paramJson
  if (componentPath) {
    const paramData = { component: componentPath };

    // 添加其他非排除的 params 参数
    Object.keys(params).forEach(key => {
      paramData[key] = params[key];
    });

    paramJson = JSON.stringify(paramData);
  } else if (Object.keys(params).length > 0) {
    // 如果没有组件路径但有其他参数
    paramJson = JSON.stringify(params);
  }

  const pageInfo = {
    id: pageId,
    name: route.meta?.title || route.name || 'Unnamed Page',
    code: route.name || 'unnamed-page',
    url: fullPath
  };

  // 确保 paramJson 字段总是存在
  if (paramJson) {
    pageInfo.paramJson = paramJson;
  }
  console.debug('pageInfo', pageInfo);
  pageInfoList.push(pageInfo);

  // 创建菜单页面关联关系
  const relationId = $utils.guid();
  menuPageRelationList.push({
    id: relationId,
    menuId: menuId,
    pageId: pageId
  });
}

/**
 * 处理子路由（递归处理，根据 meta.isMenu 字段判断是否显示在菜单中）
 */
function processChildren(
  children,
  parentMenuId,
  systemCode,
  systemId,
  parentPath,
  menuInfoList,
  pageInfoList,
  menuPageRelationList,
  level = 2
) {
  children.forEach((child, index) => {
    const fullPath = buildFullPath(child, parentPath);

    // 根据 meta.isMenu 字段判断是否在菜单中显示
    let childMenuId = null;

    if (shouldShowInMenu(child, level)) {
      childMenuId = $utils.guid();
      const childMenu = {
        id: childMenuId,
        name: child.meta?.title || child.name || 'Unnamed',
        alias: child.meta?.title || child.name || 'Unnamed',
        pid: parentMenuId,
        sysId: systemId,
        sort: index + 1
      };

      // 添加图标信息
      if (child.meta?.icon) {
        childMenu.content = JSON.stringify({ icon: child.meta.icon });
      }

      menuInfoList.push(childMenu);
    }

    // 如果有组件，创建页面
    if (child.component) {
      createPage(
        child,
        systemCode,
        systemId,
        fullPath,
        pageInfoList,
        menuPageRelationList,
        childMenuId || parentMenuId
      );
    }

    // 递归处理更深层的子路由（根据 meta.isMenu 字段判断是否显示在菜单中）
    if (child.children && child.children.length > 0) {
      processChildren(
        child.children,
        childMenuId || parentMenuId,
        systemCode,
        systemId,
        fullPath,
        menuInfoList,
        pageInfoList,
        menuPageRelationList,
        level + 1
      );
    }
  });
}

/**
 * 处理单个系统的路由配置
 */
function processSystem(route, systemSort, menuInfoList, pageInfoList, menuPageRelationList) {
  // 从根节点的 path 字段中提取 systemCode（去除前缀斜杠）
  const systemCode = route.path.replace(/^\/+/, '');

  // 从根节点的 meta.title 字段中获取 systemName，如果不存在则使用 systemCode
  const systemName = route.meta?.title || systemCode;

  // 直接使用动态生成的格式，不再支持手动配置
  const systemId = $utils.guid();

  // 系统顶级路由作为一级菜单，pid为"1"
  const systemMenuId = $utils.guid();
  const systemMenu = {
    id: systemMenuId,
    name: systemName,
    alias: systemName,
    pid: '1', // 固定为根节点的子级
    sysId: systemId,
    sort: systemSort * 100 + 1 // 确保系统间排序正确
  };

  // 添加图标信息到content字段
  if (route.meta?.icon) {
    systemMenu.content = JSON.stringify({ icon: route.meta.icon });
  }

  menuInfoList.push(systemMenu);

  // 处理系统路由的子路由（从第2级开始）
  if (route.children && route.children.length > 0) {
    processChildren(
      route.children,
      systemMenuId,
      systemCode,
      systemId,
      route.path,
      menuInfoList,
      pageInfoList,
      menuPageRelationList
    );
  }

  // 如果系统顶级路由本身有组件，也创建页面
  if (route.component) {
    createPage(route, systemCode, systemId, route.path, pageInfoList, menuPageRelationList, systemMenuId);
  }
}

/**
 * 转换路由配置为权限数据结构
 */
function convertRoutes(routesMap) {
  const menuInfoList = [];
  const pageInfoList = [];
  const menuPageRelationList = [];

  // 1. 添加固定根节点
  menuInfoList.push({
    id: '1',
    name: 'Bpaas平台',
    alias: 'Bpaas平台',
    pid: '0',
    sort: 1
  });

  // 2. 处理每个系统（现在 routesMap 的值直接是路由对象，不是数组）
  Object.values(routesMap).forEach((route, systemIndex) => {
    processSystem(route, systemIndex + 1, menuInfoList, pageInfoList, menuPageRelationList);
  });

  return {
    menuInfoList,
    pageInfoList,
    menuPageRelationList
  };
}

/**
 * 从apps目录加载路由配置
 */
async function loadFromAppsGlob() {
  const startTime = Date.now();
  const loadedModules = [];
  const failedModules = [];
  const warnings = [];
  const errors = [];

  try {
    const routeModules = import.meta.glob(['/src/apps/*/routers/index.{js,ts}'], {
      eager: true,
      import: 'default'
    });

    const routesMap = {};

    // 处理每个模块
    Object.entries(routeModules).forEach(([path, route]) => {
      try {
        // 从路径中提取系统代码: /src/apps/student-system/routers/index.js -> student-system
        const match = path.match(/\/src\/apps\/([^\/]+)\/routers\/index\.[jt]s$/);
        if (!match) {
          warnings.push(`无法从路径 ${path} 中提取系统代码`);
          return;
        }

        const systemCode = match[1];

        // 现在期望的是单个路由对象，而不是数组
        if (!route || typeof route !== 'object' || !route.path) {
          warnings.push(`系统 ${systemCode} 的路由配置不是有效的路由对象`);
          return;
        }

        routesMap[systemCode] = route;
        loadedModules.push(systemCode);
      } catch (error) {
        const systemCode = path.match(/\/src\/apps\/([^\/]+)\//)?.[1] || 'unknown';
        failedModules.push(systemCode);
        errors.push(`加载系统 ${systemCode} 失败: ${error.message}`);
      }
    });

    console.debug('routesMap', routesMap);
    // 转换为权限数据
    const convertedData = convertRoutes(routesMap);
    console.debug('convertedData', convertedData);
    return {
      success: Object.keys(routesMap).length > 0,
      data: convertedData,
      loadedModules,
      failedModules,
      warnings,
      errors,
      loadTime: Date.now() - startTime
    };
  } catch (error) {
    errors.push(`扫描路由文件失败: ${error.message}`);
    return {
      success: false,
      data: null,
      loadedModules,
      failedModules,
      warnings,
      errors,
      loadTime: Date.now() - startTime
    };
  }
}

/**
 * 本地路由管理Store（自动加载apps目录路由）
 */
export const useLocalRouteStore = defineStore('local_route', () => {
  // 是否启用本地路由模式
  const enableLocalRoute = ref(false);

  // 转换后的权限数据
  const convertedPermissionData = ref(null);

  /**
   * 自动扫描并加载apps目录下的路由配置
   */
  async function autoLoadAppsRoutes() {
    if (!enableLocalRoute.value) {
      return;
    }

    console.log('[LocalRoute] 开始自动扫描apps目录下的路由配置...');

    try {
      // 使用内部的 loadFromAppsGlob 函数
      const result = await loadFromAppsGlob();

      if (result.success && result.data) {
        // 存储转换后的权限数据，让 usePermissionStore 主动获取
        convertedPermissionData.value = result.data;

        console.log(`[LocalRoute] 成功自动加载 ${result.loadedModules.length} 个系统的路由配置:`, result.loadedModules);

        if (result.warnings.length > 0) {
          console.warn('[LocalRoute] 加载过程中的警告:', result.warnings);
        }
      } else {
        console.error('[LocalRoute] 自动加载路由配置失败:', result.errors);
        // 清空权限数据
        convertedPermissionData.value = null;
      }

      if (result.failedModules.length > 0) {
        console.warn('[LocalRoute] 以下模块加载失败:', result.failedModules);
      }
    } catch (error) {
      console.error('[LocalRoute] 自动加载路由配置失败:', error);
      // 清空权限数据
      convertedPermissionData.value = null;
    }
  }

  // 监听enableLocalRoute变化，自动加载路由
  watch(
    enableLocalRoute,
    newValue => {
      if (newValue) {
        console.log('[LocalRoute] 本地路由已启用，开始自动加载...');
        autoLoadAppsRoutes();
      } else {
        console.log('[LocalRoute] 本地路由已禁用');
      }
    },
    { immediate: true }
  ); // immediate: true 表示立即执行一次

  function setEnableLocalRoute(enable) {
    enableLocalRoute.value = enable;
  }

  return {
    enableLocalRoute,
    setEnableLocalRoute,
    convertedPermissionData
  };
});
