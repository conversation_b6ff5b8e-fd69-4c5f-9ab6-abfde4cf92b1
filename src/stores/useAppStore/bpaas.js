import { defineStore } from 'pinia';
import { unref, ref, computed } from 'vue';
import AppApis from '@/apis/app';

function getQueryParams() {
  const params = window.location.href.split('?')[1] || '';
  return params
    .split('&')
    .filter(item => !!item)
    .reduce((pre, cur) => {
      const [key, value] = cur.split('=');
      if (!!key) return { ...pre, [key]: value };
      return pre;
    }, {});
}

export const useAppStore = defineStore('main_app', () => {
  const sysCode = ref('');
  const clientId = ref('');
  const serviceId = ref('');
  const user = ref({});
  const roles = ref([]);
  const orgs = ref([]);
  const system = ref({});
  const permission = ref({});
  const services = ref([]);
  const accountConfig = ref({});
  const serviceInfo = ref({});
  const childrenApp = ref({});
  const childrenSkip = ref({});
  const childrenLoad = ref({});

  let setupSDKPromise = null;

  /**
   * @description 平台配置
   * @param {String} platformName 平台名称
   * @param {String} platformNameFontSize 平台名称字体大小
   * @param {String} platformLogo 平台Logo
   * @param {String} systemLoginBg 登录页背景
   * @param {String} loginImg 登录框左侧图片
   * @param {String} loginLogo 登录框Logo
   * @param {String} userCenterName 用户中心系统名称
   */
  const platformConfig = ref({});

  const nickName = computed(() => unref(user).nickName || 'funi');
  const userName = computed(() => unref(user).userName || '-');
  const systemName = computed(
    () => unref(serviceInfo).name || unref(system).name || window.AppConfig?.sysName || 'Funi Paas'
  );
  const service = ref('');

  const queryParams = getQueryParams();

  async function setup() {
    try {
      await setupGetChildApp();
      setupMap();
      await setOtherSource();
      await setupToken();
      setupSysCode();
      setupClient();
      await setupUserInfo();
      await setupSysInfo();
      await setupPermissions();
      if (process.env.NODE_ENV === 'production') {
        setupVisibilityChange();
      }
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async function setOtherSource() {
    if (queryParams?.source === 'other') {
      await judgeDD();
      await AppApis.resetRouterParams(queryParams, childrenSkip.value);
    } else {
      return Promise.resolve();
    }
  }

  async function judgeDD() {
    if (queryParams.dingTalkUserId) {
      try {
        await AppApis.setDDLogin(queryParams.dingTalkUserId);
        return Promise.resolve();
      } catch {
        return Promise.reject();
      }
    } else {
      return Promise.resolve();
    }
  }
  async function setupGetChildApp() {
    const { MODE, VITE_FUNI_MODE } = import.meta.env;
    const key = new Date().getTime();
    const keyName = sessionStorage.getItem('appName') || '';
    const { origin, pathname } = window.location;
    let match = pathname.match(/[^/]+(?=\/?$)/);
    match = match[0] || null;
    const url = origin + pathname.replace(match, keyName);
    const flag = MODE === 'production' && VITE_FUNI_MODE === 'cli' && keyName;
    async function getChildren(name = 'index') {
      let module = {};
      if (flag) {
        try {
          module = await import(`${url}${name}.js?${key}`);
        } catch {
          return Promise.resolve(module);
        }
      } else {
        const enumerate = {
          'skip.config': import.meta.glob(['../../apps/*/skip.config.{js,json}'], { eager: true }),
          index: import.meta.glob(['@/apps/**/*.vue'], { import: 'default' })
          // bootstrap: import.meta.glob(['../../apps/*/bootstrap.{js,jsx,ts,tsx}'], { eager: true })
        };
        module = enumerate[name];
      }
      return module;
    }

    function addCssLink(href) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      link.type = 'text/css';
      document.head.appendChild(link);
    }
    if (flag) {
      addCssLink(`${url}assets/unocss_marionette.css?${key}`);
    }
    const [component, skip, load] = await Promise.all([
      getChildren(),
      getChildren('skip.config'),
      getChildren('bootstrap')
    ]);

    childrenApp.value = flag ? component?.default || {} : component || {};
    childrenSkip.value = skip?.default || {};
    childrenLoad.value = load?.appLoadFunctions || {};
    return Promise.resolve();
  }

  async function setupToken() {
    !!queryParams.token && sessionStorage.setItem('token', queryParams.token);
    try {
      if (!!queryParams.ticket) {
        sessionStorage.setItem('token', await AppApis.getToken(queryParams.ticket));
        sessionStorage.setItem('ticket', queryParams.ticket);
      } else if (process.env.NODE_ENV === 'production') {
        sessionStorage.setItem('token', await AppApis.getTokenFromCookie());
      }
    } catch (error) {
      console.error('setupToken=>', error);
    }
  }

  function setupSysCode() {
    sysCode.value = queryParams.app_n || sessionStorage.getItem('sysCode') || '';
    sessionStorage.setItem('sysCode', sysCode.value);
  }

  function setupClient() {
    clientId.value = queryParams.c_id || sessionStorage.getItem('c_id') || '';
    sessionStorage.setItem('c_id', clientId.value);

    serviceId.value = queryParams.s_id || sessionStorage.getItem('s_id') || '';
    sessionStorage.setItem('s_id', serviceId.value);
  }

  async function setupSysInfo() {
    if (!clientId.value) return Promise.reject('未找到client_id');
    try {
      const sysInfo = await AppApis.fetchClientInfo(clientId.value);
      system.value = sysInfo || {};
      if (!!serviceId.value) {
        const serviceVo = await AppApis.fetchServiceInfo(serviceId.value);
        serviceInfo.value = serviceVo;
      }
      service.value = sysInfo.appName?.split('_')[0];
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async function setupPermissions() {
    if (!clientId.value) return Promise.reject('未找到client_id');
    try {
      const permissionInfo = await AppApis.getSystemMenus(clientId.value, serviceId.value);
      permission.value = permissionInfo || {};
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async function setupUserInfo() {
    try {
      if (!clientId.value) return Promise.reject('未找到client_id');
      const userInfo = await AppApis.findCurrentUser();
      AppApis.heart() && setInterval(AppApis.heart, 30000);
      user.value = userInfo;
      Object.assign(window.$funi.auth, { user: user.value });
      accountConfig.value = await AppApis.unifyAccountConfigDetailMy();
      return Promise.resolve(user.value);
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async function findPlatformConfig() {
    try {
      const res = await AppApis.findPlatformConfig();
      platformConfig.value = {
        ...res,
        platformName: res.platformName,
        userCenterName: res.userCenterName,
        platformNameFontSize: res.platformNameFontSize,
        platformLogo: $utils.fileUrl((res.platformLogo || [])[0]),
        favicon: $utils.fileUrl((res.favicon || [])[0]),
        systemLoginBg: $utils.fileUrl((res.systemLoginBg || [])[0]),
        loginImg: $utils.fileUrl((res.loginImg || [])[0]),
        loginLogo: $utils.fileUrl((res.loginLogo || [])[0]),
        systemLoginBgB: $utils.fileUrl((res.systemLoginBgB || [])[0]),
        loginImgB: $utils.fileUrl((res.loginImgB || [])[0]),
        loginLogoB: $utils.fileUrl((res.loginLogoB || [])[0]),
        systemLoginBgC: $utils.fileUrl((res.systemLoginBgC || [])[0]),
        loginImgC: $utils.fileUrl((res.loginImgC || [])[0]),
        loginLogoC: $utils.fileUrl((res.loginLogoC || [])[0]),
        unifiedEncryptes: res.unifiedEncrypt?.split(',') ?? []
      };
      if (platformConfig.value.favicon) {
        const link = document.querySelector("link[rel*='icon']") || document.createElement('link');
        link.type = 'image/x-icon';
        link.rel = 'icon';
        link.href = platformConfig.value.favicon;
        document.getElementsByTagName('head')[0].appendChild(link);
      }
      sessionStorage.setItem('isgateway', res.isgateway || '');
    } catch (error) {
      console.error('findPlatformConfig=>', error);
    }
  }

  async function getServices() {
    if (services.value.length) return Promise.resolve(services.value);

    return Promise.all([
      AppApis.unifyAccountConfigDetailMy(),
      AppApis.querySaasServiceClassifyList(),
      AppApis.findClientService()
    ]).then(([unifyAccountConfig, service, clients]) => {
      const validClients = clients
        .filter(item => !item.hasOwnProperty('isShow') || unifyAccountConfig.unauthAppDisplay || item.isShow)
        .map(item => Object.assign({ disable: item.hasOwnProperty('isShow') && !item.isShow }, item));
      services.value = $utils
        .toArrayTree([...service.list, ...validClients], {
          strict: true,
          parentKey: 'serviceClassifyId',
          reverse: true,
          children: 'childrens'
        })
        .filter(i => !!i.childrens?.length);
      return services.value;
    });
  }

  function setupSDK() {
    if (!setupSDKPromise) {
      setupSDKPromise = Promise.all([AppApis.fetchOrgTree(), AppApis.fetchAllRoles()]).then(([org, role]) => {
        orgs.value = $utils.mapTree(org || [], item => ({ ...item, label: item.orgName }));
        roles.value = (role || []).map(item => ({ ...item, label: item.roleName }));
        Object.assign(window.$funi.auth, { orgs: orgs.value, roles: roles.value });
      });
    }
    return setupSDKPromise;
  }

  function getStorageKey(key) {
    return `${key}_${clientId.value}`;
  }

  function setupVisibilityChange() {
    const hiddenProp = ['hidden', 'webkitHidden', 'mozHidden', 'msHidden'].find(key => key in document);
    const eventProp = hiddenProp.replace(/hidden/i, 'visibilitychange');
    document.addEventListener(eventProp, async () => {
      if (!document[hiddenProp]) {
        // sessionStorage.removeItem('token');
        sessionStorage.setItem('token', await AppApis.getTokenFromCookie());
      }
    });
  }

  async function appsLoadFunc() {
    const allFuncs = [];
    Object.values(childrenLoad.value).forEach(item => {
      allFuncs.push(item);
    });
    try {
      await Promise.all(allFuncs.map(item => item()));
    } catch {}
    Promise.resolve();
  }

  function setupMap() {
    if (platformConfig.value.tmapEnable == '1') {
      const host = platformConfig.value.tmapProxy || '//api.tianditu.gov.cn';
      const script = document.createElement('script');
      script.src = `${host}/api?v=4.0&tk=397492a2b63e53a1c95dd49aef1f3cea`;
      script.type = 'text/javascript';
      document.head.appendChild(script);
    }

    if (platformConfig.value.amapEnable == '1') {
      // window._AMapSecurityConfig = { securityJsCode: '「你申请的安全密钥」' };
      const host = platformConfig.value.amapProxy || '//webapi.amap.com';
      const script = document.createElement('script');
      script.src = `${host}/maps?v=2.0&key=427f93a0440578daa799493226ea8c01&plugin=AMap.Geocoder,AMap.PlaceSearch,AMap.Geolocation,AMap.DistrictSearch`;
      script.type = 'text/javascript';
      document.head.appendChild(script);
    }
  }

  return {
    sysCode,
    user,
    system,
    serviceId,
    permission,
    platformConfig,
    setup,
    nickName,
    systemName,
    userName,
    setupUserInfo,
    findPlatformConfig,
    service,
    getServices,
    setupSDK,
    getStorageKey,
    accountConfig,
    setupToken,
    childrenApp,
    childrenSkip,
    childrenLoad,
    appsLoadFunc
  };
});
