import { defineStore } from 'pinia';
import { unref, ref, computed } from 'vue';
import AppApis from '@/apis/app';

function getQueryParams() {
  const params = window.location.href.split('?')[1] || '';
  return params
    .split('&')
    .filter(item => !!item)
    .reduce((pre, cur) => {
      const [key, value] = cur.split('=');
      if (!!key) return { ...pre, [key]: value };
      return pre;
    }, {});
}

export const useAppStore = defineStore('main_app', () => {
  const sysCode = ref('');
  const user = ref({});
  const system = ref({});
  const permission = ref({});

  /**
   * @description 平台配置
   * @param {String} platformName 平台名称
   * @param {String} platformNameFontSize 平台名称字体大小
   * @param {String} platformLogo 平台Logo
   * @param {String} systemLoginBg 登录页背景
   * @param {String} loginImg 登录框左侧图片
   * @param {String} loginLogo 登录框Logo
   */
  const platformConfig = ref({});

  const nickName = computed(() => unref(user).nickName || 'funi');
  const userName = computed(() => unref(user).userName || '-');
  const systemName = computed(() => unref(system).name || widnow.AppConfig.sysName);

  const queryParams = getQueryParams();

  async function setup() {
    try {
      setupSysCode();
      setupToken();
      await fetchBaseData(unref(sysCode));
      return Promise.resolve();
    } catch (error) {
      // console.error('Error=>', error);
      return Promise.reject(error);
    }
  }

  function setupSysCode() {
    sysCode.value = queryParams.sysCode || sessionStorage.getItem('sysCode') || '';
    sessionStorage.setItem('sysCode', sysCode.value);
  }

  function setupToken() {
    !!queryParams.token && sessionStorage.setItem('token', queryParams.token);
  }

  async function setupUserInfo() {
    try {
      const userInfo = await AppApis.getUserInfo();
      const decryptd = data => (data ? $utils.decryptdData(data) : '');
      user.value = {
        ...userInfo,
        userName: decryptd(userInfo.userName),
        cellPhone: decryptd(userInfo.cellPhone),
        emailAddress: decryptd(userInfo.emailAddress)
      };
      return Promise.resolve(user.value);
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async function fetchBaseData(code) {
    if (!code) return Promise.reject('sysCode不存在');
    try {
      const sysInfo = await AppApis.findSysByCode(code);
      const permissionInfo = await AppApis.getSystemMenus(code);
      system.value = sysInfo || {};
      permission.value = permissionInfo || {};
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async function findPlatformConfig() {
    try {
      const res = await AppApis.findPlatformConfig();
      platformConfig.value = {
        ...res,
        platformName: res.platformName,
        platformNameFontSize: res.platformNameFontSize,
        platformLogo: $utils.fileUrl((res.platformLogo || {}).id),
        favicon: $utils.fileUrl((res.favicon || {}).id),
        systemLoginBg: $utils.fileUrl((res.systemLoginBg || {}).id),
        loginImg: $utils.fileUrl((res.loginImg || {}).id),
        loginLogo: $utils.fileUrl((res.loginLogo || {}).id)
      };
      if (platformConfig.value.favicon) {
        const link = document.querySelector("link[rel*='icon']") || document.createElement('link');
        link.type = 'image/x-icon';
        link.rel = 'icon';
        link.href = platformConfig.value.favicon;
        document.getElementsByTagName('head')[0].appendChild(link);
      }
    } catch (error) {
      console.error('findPlatformConfig=>', error);
    }
  }

  function getStorageKey(key) {
    return `${key}_${system.value.id}`;
  }

  return {
    sysCode,
    user,
    system,
    permission,
    platformConfig,
    setup,
    nickName,
    systemName,
    userName,
    setupUserInfo,
    findPlatformConfig,
    getStorageKey
  };
});
