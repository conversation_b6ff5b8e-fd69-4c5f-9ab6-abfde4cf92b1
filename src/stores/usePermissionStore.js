import { defineStore } from 'pinia';
import { unref, computed } from 'vue';
import { useAppStore } from './useAppStore';
import { useLocalRouteStore } from './useLocalRouteStore';
import dynamicApps from '@/apps/dynamicApps.js';
import FuniJS from '@funi-lib/utils';
import { useRoute } from 'vue-router';
import LayoutProps from '@/config/layout.config.js';
import lowcode from '@/components/FuniFormEngine/common/utils/lowcode';
const Utils = FuniJS;

export const usePermissionStore = defineStore('main_permission', () => {
  const appStore = useAppStore();
  const localRouteStore = useLocalRouteStore();
  const route = useRoute();

  // 设置是否开启本地路由表
  localRouteStore.setEnableLocalRoute(true);

  /** 获取权限数据源 - 支持本地路由和接口数据 */
  const permissionDataSource = computed(() => {
    // 如果启用本地路由且有转换数据，优先使用本地路由
    if (localRouteStore.enableLocalRoute && localRouteStore.convertedPermissionData) {
      return localRouteStore.convertedPermissionData;
    }
    // 否则使用接口数据
    return appStore.permission;
  });

  /** 菜单数据 */
  const menuInfoList = computed(() => {
    return (permissionDataSource.value.menuInfoList || []).filter(item => item.id !== '1');
  });

  /** 页面数据 */
  const pageInfoList = computed(() => permissionDataSource.value.pageInfoList || []);

  /** 功能权限数据 */
  const permissionInfoList = computed(() => {
    return permissionDataSource.value.permissionInfoList || [];
  });

  /** 菜单与页面关系数据 */
  const menuPageRelationList = computed(() => {
    return permissionDataSource.value.menuPageRelationList || [];
  });
  // 子系统数据
  const childrenApp = computed(() => {
    return appStore.childrenApp || {};
  });

  const menusOfPage = computed(() => {
    return Utils.objectMap(Utils.groupBy(unref(menuPageRelationList), 'pageId'), item => item.map(i => i.menuId));
  });

  const permissionsOfPage = computed(() => {
    return Utils.objectMap(Utils.groupBy(unref(permissionInfoList), 'pageId'), obj => obj.map(item => item.code));
  });

  // const modules = import.meta.glob(['@/apps/**/*.vue'], {
  //   import: 'default'
  // });

  const pages = computed(() => {
    const { MODE, VITE_FUNI_MODE } = import.meta.env;
    const flag = MODE === 'production' && VITE_FUNI_MODE === 'cli';
    return unref(pageInfoList)
      .map(page => {
        const permissions = unref(permissionsOfPage)[page.id] || [];
        const menus = unref(menusOfPage)[page.id] || [];
        const pageObj = { ...page, permissions, menus };
        const params = JSON.parse(page.paramJson || '{}');
        let lowcodeConf = null;

        if (page.url.startsWith('http://') || page.url.startsWith('https://')) {
          Object.assign(pageObj, { redirect: true });
        } else {
          let component;
          if (params.component) {
            if (params.component.startsWith('@')) {
              const [prefix, appAndPage] = params.component.split('@');
              if (appAndPage) {
                const [appId, pageId] = appAndPage.split(':');
                component = lowcode.createAsyncComponent(appId, pageId);
                lowcodeConf = { appId, pageId };
              }
            } else {
              const withoutSlashPath = params.component.replace(/^\/*/, '');
              const keyName = flag ? withoutSlashPath : `/src/apps/${withoutSlashPath}`;
              component = unref(childrenApp)[keyName];
            }
          } else if (params.dynamicComponent) {
            const [app, componentName] = params.dynamicComponent.split('/');
            component = (dynamicApps[app] || {})[componentName];
          }
          const route = {
            path: page.url,
            name: page.code,
            meta: { ...params, menus, permissions, title: page.name, pageId: page.id, lowcodeConf },
            component
          };
          Object.assign(pageObj, { route });
        }
        return pageObj;
      })
      .filter(item => !!item.redirect || (!!item.route.path && !!item.route.component));
  });

  const pageGroupById = computed(() => {
    return Utils.objectMap(Utils.groupBy(unref(pages), 'id'), item => item[0]);
  });

  const pagesOfMenu = computed(() => {
    return Utils.objectMap(Utils.groupBy(unref(menuPageRelationList), 'menuId'), obj =>
      obj.map(item => unref(pageGroupById)[item.pageId] || {})
    );
  });

  const menus = computed(() => {
    return unref(menuInfoList).map(menu => {
      const pages = unref(pagesOfMenu)[menu.id] || [];
      const menuContent = JSON.parse(menu.content || '{}');
      const defaultPage = pages.find(page => page.id === menuContent.defaultPageId) || pages[0];
      return { ...menu, defaultPage, ...menuContent, pages };
    });
  });

  const menuGroupById = computed(() => {
    return Utils.objectMap(Utils.groupBy(unref(menus), 'id'), item => item[0]);
  });

  const menuTree = computed(() => {
    return (
      Utils.toArrayTree(Utils.clone(unref(menus), true), {
        parentKey: 'pid',
        sortKey: 'sort'
      })[0] || {}
    );
  });

  const mainRoute = computed(() => {
    const subRoutes = unref(pages)
      .filter(item => !item.redirect && !!item.route.path && !!item.route.component)
      .map(page => page.route);

    return {
      path: '/',
      name: 'Main',
      component: () => import('@/layout/index.vue'),
      meta: { hidden: true },
      props: LayoutProps.default,
      children: subRoutes
    };
  });

  const defaultRoutePage = computed(() => {
    const func = menu => {
      let menuObj = menu;
      if (!menu) {
        const [obj] = unref(menuTree).children || [];
        menuObj = obj;
      }

      if (!menuObj) return;

      const { children, pages, defaultPage } = menuObj;
      if (!children.length && !pages.length) return;

      if (!children.length && !!defaultPage) return defaultPage;

      return func(children[0]);
    };
    return func();
  });

  const permissionsInCurrentPage = computed(() => route.meta.permissions);
  const permissionsOfParentMenu = computed(() => {
    return route.meta.menus
      .map(menuId => unref(menuGroupById)[menuId])
      .map(menu => menu.defaultPage)
      .map(page => unref(permissionsOfPage)[page.id])
      .flat();
  });
  const lowcodeApps = computed(() => {
    return [
      ...new Set(
        unref(pages)
          .filter(item => item.meta.lowcodeConf)
          .map(item => item.meta.lowcodeConf.appId)
      )
    ];
  });
  return {
    /** 路由表 */
    mainRoute,
    /** 根据menuId查询menu */
    menuGroupById,
    /** 根据menuId查询pages */
    pagesOfMenu,
    /** 菜单List */
    menus,
    /** 菜单树 */
    menuTree,
    pages,
    /** 根据pageId查询page */
    pageGroupById,
    /** 根据pageId查询menuIds */
    menusOfPage,
    /** 根据pageId查询功能权限 */
    permissionsOfPage,
    permissionInfoList,
    permissionsInCurrentPage,
    permissionsOfParentMenu,

    defaultRoutePage,
    /** 低代码应用id */
    lowcodeApps,

    /** 本地路由相关 */
    localRouteStore,
    permissionDataSource
  };
});
