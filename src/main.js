/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-24 11:01:55
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2025-07-09 15:42:23
 * @FilePath: /src/main.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import FuniJS from '@funi-lib/utils';
import App from '@/App.vue';
import router from '@/router/index';
import '@/config/router.config/index.js';

import '@/styles/index.scss';
import 'virtual:uno.css';
import 'animate.css';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

import FuniFormEngine from '@/components/FuniFormEngine/install.js';
import piniaPersist from 'pinia-plugin-persist';
import 'github-markdown-css/github-markdown.css';
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css';
import appBootstrap from '@/utils/appBootstrap';
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
import 'vxe-table-plugin-element/dist/style.css';

import utils from '@/utils';
import directive from '@/utils/directive';
import components from '@/components';

import plugins from '@/plugins';

import 'virtual:svg-icons-register';

const app = createApp(App);

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

const pinia = createPinia();
pinia.use(piniaPersist);
window.$pinia = pinia;
FuniJS.CryptoUtil.init({ appKey: import.meta.env.VITE_OPEN_APP_KEY, wasmPath: import.meta.env.VITE_WASM_PATH }).then(async () => {
  const bootstrap = await appBootstrap();
  app
    .use(plugins)
    .use(utils)
    .use(components)
    .use(directive)
    .use(pinia)
    .use(router)
    .use(ElementPlus)
    .use(FuniFormEngine)
    .use(bootstrap)
    .use(VXETable);
  app.mount('#app');
});
