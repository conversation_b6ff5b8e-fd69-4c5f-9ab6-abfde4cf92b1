import router from '@/router/index.js';
import { useAppStore } from '@/stores/useAppStore';
export default {
  beforeEachHandler: async to => {
    const layoutName = to?.matched?.[0]?.components?.default?.name;
    if (layoutName === 'UserLayout' && !sessionStorage.getItem('token')) {
      const appStore = useAppStore();
      await appStore.setupToken();
    }
    // 路由未携带token和c_id，直接跳转
    if (!((to.query.token || to.query.ticket) && to.query.c_id)) return true;

    if (to.path !== '/') {
      const newDestination = $utils.clone(to, true);
      delete newDestination.query.token;
      delete newDestination.query.c_id;
      delete newDestination.query.s_id;
      delete newDestination.query.ticket;
      delete newDestination.query.app_n;
      return router.resolve(newDestination);
    }
    return true;
  }
};
