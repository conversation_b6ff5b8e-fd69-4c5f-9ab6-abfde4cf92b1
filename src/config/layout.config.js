/*
 * @Author: 古加文 <EMAIL>
 * @Date: 2023-03-07 21:18:44
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-01-12 10:36:50
 * @FilePath: /funi-paas-cs-web-cli/src/config/layout.config.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */

export default {
  default: {
    asideHidden: false,
    multiTabHidden: false,
    breadcrumbHidden: false,
    // 是否隐藏导航栏工作台快捷入口
    hideWorkbench: false,
    // 隐藏系统切换快捷入口
    hideClientSwitch: false,
    // 关闭Logo交互
    disableLogo: false,
    menuList: [[{ key: 'USER_CENTER' }, { key: 'APP_MANAGE' }, { key: 'REC' }]]
  }
};
