<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-09-02 11:47:06
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-09 20:45:56
 * @FilePath: /funi-paas-cs-web-cli/src/App.vue
 * @Description:
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <el-config-provider :locale="locale">
    <router-view />
  </el-config-provider>
</template>

<script setup>
import { RouterView, useRouter } from 'vue-router';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import { useAppStore } from '@/stores/useAppStore';
import { usePermissionStore } from '@/stores/usePermissionStore.js';
import { useWatermark } from '@/utils/hooks/useWatermark';
import useThemeConfigStore from '@/layout/components/theme/hooks/setTheme.js';

useWatermark();
const locale = zhCn;
const router = useRouter();
const appStore = useAppStore();
const permissionStore = usePermissionStore();
const themeConfigStore = useThemeConfigStore();
appStore.findPlatformConfig().finally(async () => {
  console.log('findPlatformConfig====>');
  await appStore.setup();
  await appStore?.appsLoadFunc?.();
  themeConfigStore.getInitConfig();
  router.addRoute(permissionStore.mainRoute);
  const currentPath = window.location.href.split('#')[1];
  const resolvedRoute = router.resolve(currentPath || '');
  console.log('resolvedRoute', resolvedRoute);
  if (!!resolvedRoute && resolvedRoute.path !== '/' && !!resolvedRoute.matched.length) {
    router.push(resolvedRoute);
  } else {
    const defaultRoute = permissionStore.defaultRoutePage || {};
    router.push(defaultRoute.route || '/');
  }
  document.getElementById('loading-mask').style.display = 'none';
});
</script>

<style lang="less">
.size {
  width: 100%;
  height: 100%;
}

html,
body {
  padding: 0 !important;
  margin: 0;
  overflow: hidden;
  .size;
  /* -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  box-sizing: border-box; */

  #app {
    .size;
  }
}

label {
  font-weight: 400;
}
</style>
