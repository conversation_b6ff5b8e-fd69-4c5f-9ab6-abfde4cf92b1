<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2022-12-01 16:32:51
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-02-16 10:15:58
 * @FilePath: \funi-paas-csccs-ui\src\components\FuniShareAction\ShareDialog.vue
 * @Description: 简单详情弹窗
 * Copyright (c) 2022 by 郑佳 <EMAIL>, All Rights Reserved. 
-->
<template>
  <el-dialog v-model="dialogVisible"
    :title="title"
    :destroy-on-close="true"
    :append-to-body="true"
    :width="width">
    <div class="formBorder">
      <FuniForm class="formBorder"
        :schema="schema"
        @get-form="setForm"
        :rules="rules" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button v-if="!readonly"
          type="default"
          @click="dialogVisible = false">取消</el-button>
        <el-button v-if="!readonly"
          type="primary"
          @click="onOk">确定</el-button>
        <el-button v-if="readonly"
          type="primary"
          @click="(dialogVisible = false)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessage, ElButton, ElDialog, ElForm, ElFormItem } from 'element-plus';
import FuniForm from '@/components/FuniForm/index.vue';
import { reactive, ref } from 'vue';

const emit = defineEmits(['ok']);

const props = defineProps({
  width: {
    type: String,
    default: '600px'
  },
  schema: {
    type: Array,
    default: () => {
      return new Array();
    }
  },
  rules: {
    type: Object,
    default: () => {
      return new Object();
    }
  },
  visible: {
    type: Boolean,
    default: false
  }
});

const readonly = ref(false);
const dialogVisible = ref(props.visible);
const title = ref(undefined);
let defaultValues = reactive({});
const formApi = reactive({});
//显示
function show (titleVal, values = {}, readonlyVal = false) {
  dialogVisible.value = true;
  title.value = titleVal;
  readonly.value = readonlyVal;
  for (let key in defaultValues) {
    delete defaultValues[key];
  }
  Object.assign(defaultValues, values);
}

const setForm = ({ validate, getValues, setValues }) => {
  Object.assign(formApi, { validate, getValues, setValues });
  setValues(defaultValues);
}

async function onOk () {
  const result = await formApi.validate();
  if (result.isValid) {
    const values = formApi.getValues();
    Object.assign(defaultValues, values);
    emit('ok', {
      values: defaultValues, next: () => {
        dialogVisible.value = false;
      }
    })
  }
}

defineExpose({ show, formApi });
/**
 * 功能方法
 */
</script>
<style lang='scss' scoped>
</style>