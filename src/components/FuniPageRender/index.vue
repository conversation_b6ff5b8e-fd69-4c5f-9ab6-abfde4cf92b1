<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-02-23 11:05:59
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-07-21 18:17:33
 * @FilePath: \funi-paas-csccs-ui\src\components\FuniPageRender\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<script>
import lowCode from '@/components/FuniFormEngine/common/utils/lowcode.js';
import { defineAsyncComponent, defineComponent, h } from 'vue'
export default defineAsyncComponent(() => {
  return new Promise((resovle, reject) => {
    let hasError = false;
    let errorMessage = '';
    let errComponent;
    if (location.hash) {
      let hashList = location.hash.split('/');
      if (hashList && hashList.length >= 1) {
        const pageId = hashList[hashList.length - 1];
        const component = lowCode.createAsyncComponent(pageId);
        resovle(component);
      } else {
        hasError = true;
        errorMessage = '没有页面id';
      }
    } else {
      hasError = true;
      errorMessage = '没有页面id';
    }
    if (hasError) {
      errComponent = defineComponent({
        render () {
          return h('span', errorMessage)
        }
      })
      resovle(errComponent);
    }
  })
})
</script>