<!--
 * @Author: 古加文 <EMAIL>
 * @Date: 2023-11-08 13:55:21
 * @LastEditors: 古加文 <EMAIL>
 * @LastEditTime: 2023-11-09 17:40:17
 * @FilePath: \funi-paas-cs-web-cli\src\components\FuniFilePreview\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <vue-office-docx v-if="type == 'docx'" :src="src" />
  <vue-office-excel v-else-if="type == 'xlsx'" :src="src" />
  <iframe style="width: 100%; height: 100%" v-else-if="type == 'pdf' || type == 'PDF'" :src="src" />
</template>

<script setup>
//引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx';
//引入相关样式
import '@vue-office/docx/lib/index.css';
//引入VueOfficeExcel组件
import VueOfficeExcel from '@vue-office/excel';
//引入相关样式
import '@vue-office/excel/lib/index.css';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const src = ref();
const type = ref();
const route = useRoute();

onMounted(() => {
  src.value = decodeURIComponent(route.query.src);
  type.value = route.query.type;
  if (!['docx', 'xlsx', 'pdf', 'PDF'].includes(type.value)) {
    window.location.href = src.value;
  }
});
</script>

<style lang="scss" scoped>
.fileView {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  background: gray;
}
</style>
