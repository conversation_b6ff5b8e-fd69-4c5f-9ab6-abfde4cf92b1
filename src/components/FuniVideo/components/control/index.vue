<template>
  <div class="content">
    <!-- <div style={{ color: '#4699E7' , display: 'inline-block' }}> -->
    <!-- <div :style="{ color: '#4699E7', display: 'inline-block' }">云台控制器（按下开始控制，松开停止控制）</div> -->
    <div class="control_content">
      <div class="control_content_leftcontent">
        <p class="active_p">变倍</p>
        <button
          class="active_button"
          @mousedown="startCtrl(13, '1')"
          @mouseup="startCtrl(13, '0')"
          :style="{ color: '#fff', fontSize: '18px' }"
        >
          +
        </button>
        <button
          class="active_button"
          @mousedown="startCtrl(14, '1')"
          @mouseup="startCtrl(14, '0')"
          :style="{ color: '#fff', fontSize: '18px' }"
        >
          -
        </button>
      </div>

      <div class="centercontentMax">
        <img
          class="i_top"
          title="向上"
          @mousedown="startCtrl(1, '1')"
          @mouseup="startCtrl(1, '0')"
          :src="right"
          alt=""
        />
        <img
          class="i_left"
          title="向左"
          @mousedown="startCtrl(3, '1')"
          @mouseup="startCtrl(3, '0')"
          :src="right"
          alt=""
        />
        <img
          class="i_right"
          title="向右"
          @mousedown="startCtrl(4, '1')"
          @mouseup="startCtrl(4, '0')"
          :src="right"
          alt=""
        />
        <img
          class="i_bottom"
          title="向下"
          @mousedown="startCtrl(2, '1')"
          @mouseup="startCtrl(2, '0')"
          :src="right"
          alt=""
        />
      </div>
      <div class="centercontentMin">
        <img
          class="i_top"
          title="右上"
          @mousedown="startCtrl(6, '1')"
          @mouseup="startCtrl(6, '0')"
          :src="right"
          alt=""
        />
        <img
          class="i_left"
          title="左上"
          @mousedown="startCtrl(5, '1')"
          @mouseup="startCtrl(5, '0')"
          :src="right"
          alt=""
        />
        <img
          class="i_right"
          title="右下"
          @mousedown="startCtrl(8, '1')"
          @mouseup="startCtrl(8, '0')"
          :src="right"
          alt=""
        />
        <img
          class="i_bottom"
          title="左下"
          @mousedown="startCtrl(7, '1')"
          @mouseup="startCtrl(7, '0')"
          :src="right"
          alt=""
        />
      </div>

      <div class="control_content_rightcontent">
        <p class="active_p">变焦</p>
        <button
          class="active_button"
          @mousedown="startCtrl(9, '1')"
          @mouseup="startCtrl(9, '0')"
          :style="{ color: '#fff', fontSize: '18px' }"
        >
          +
        </button>
        <button
          class="active_button"
          @mousedown="startCtrl(10, '1')"
          @mouseup="startCtrl(10, '0')"
          :style="{ color: '#fff', fontSize: '18px' }"
        >
          -
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import right from './static/img/btn1.png';

const props = defineProps({
  speed: {}
});

const emits = defineEmits(['output']);

function startCtrl(value, command) {
  if ([9, 10].includes(value)) {
    emits('output', {
      type: 'operateCamera',
      operateType: 2,
      direct: value == 9 ? 1 : 2,
      command,
      step: props.speed
    });
  } else if ([13, 14].includes(value)) {
    emits('output', {
      type: 'operateCamera',
      operateType: 1,
      direct: value == 13 ? 1 : 2,
      command,
      step: props.speed
    });
  } else {
    emits('output', {
      type: 'operateDirect',
      direct: value,
      command,
      stepX: props.speed,
      stepY: props.speed
    });
  }
  // message.info(stop === '0' ? '开始控制' : '停止控制');

  //   if (_selected != null) {
  //     window.clearTimeout(this.timer);
  //     this.fetching = true;
  //     const num = String(e);
  //     this.controlInfo.current.devicecode = _selected;
  //     this.controlInfo.current.type = num;
  //     // controlInfo.current.speed = Cinput;
  //     this.controlInfo.current.streamtype = streamtype;
  //     this.$post('/findVideos/operateDirect', {
  //       devicecode: _selected, //"31626057557454233686",
  //       streamtype: streamtype,
  //       type: num,
  //       speed: '3',
  //       stop: stop
  //     })
  //       .then(res => {
  //         if (!res || res.msg !== 'success') {
  //           this.$message.error('控制失败!');
  //           this.fetching = false;
  //         }
  //       })
  //       .catch(() => {
  //         this.$message.error('控制失败!');
  //         this.fetching = false;
  //       })
  //       .finally(() => {
  //         this.timer = window.setTimeout(() => (this.fetching = false), 1000 * 2);
  //       });
  //   }
}
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  margin: 0 auto;
  width: 292px;
  //   transform: scale(0.8);

  .control_content {
    position: relative;
    height: 140px;
  }

  .control_content_leftcontent {
    width: 50px;
    height: 120px;
    background-color: rgba(40, 44, 58, 1);
    border-radius: 5px;
    position: absolute;
    top: 10px;
    left: 10px;
    text-align: center;
  }

  .centercontentMax {
    width: 140px;
    height: 140px;
    background-color: rgb(27, 27, 39);
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 80px;
    box-shadow: 0 0 15px rgb(5 109 189) inset, 0 0 3px rgb(0 185 255);
    color: #fff;

    img {
      width: 36px;
      position: absolute;
      transform-origin: center center;
      cursor: pointer;

      &:hover {
        border-radius: 50%;
        background-color: rgb(0, 108, 166, 1);
      }
    }

    .i_top {
      left: 52px;
      top: 4px;
      transform: rotate(270deg);
    }

    .i_left {
      left: 4px;
      top: 50px;
      transform: rotate(180deg);
    }

    .i_right {
      right: 4px;
      top: 50px;
    }

    .i_bottom {
      bottom: 6px;
      left: 52px;
      transform: rotate(90deg);
    }
  }

  .centercontentMin {
    width: 50px;
    height: 50px;
    background-color: rgb(27, 27, 39, 0.1);
    border-radius: 50%;
    position: absolute;
    top: 43px;
    left: 125px;
    color: #fff;
    z-index: 1;
    transform: rotate(45deg);
    border: 1px solid #057dde;

    img {
      width: 20px;
      position: absolute;
      transform-origin: center center;
      opacity: 0.4;
      cursor: pointer;

      &:hover {
        border-radius: 50%;
        background-color: rgb(0, 108, 166, 1);
      }
    }

    .i_top {
      left: 13px;
      top: -28px;
      transform: rotate(270deg);
    }

    .i_left {
      left: -28px;
      top: 14px;
      transform: rotate(180deg);
    }

    .i_right {
      right: -28px;
      top: 15px;
    }

    .i_bottom {
      bottom: -28px;
      left: 15px;
      transform: rotate(90deg);
    }
  }

  .control_content_rightcontent {
    width: 50px;
    height: 120px;
    background-color: rgba(40, 44, 58, 1);
    border-radius: 5px;
    position: absolute;
    top: 10px;
    right: 10px;
    text-align: center;
  }

  .active_p {
    display: block;
    margin-top: 10px;
    font-size: 10px;
    color: #fff;
    opacity: 0.8;
  }

  .active_button {
    width: 30px;
    height: 30px;
    margin: 4px;
    background-color: #354c5b;
    border: none;
    border-radius: 3px;
    cursor: pointer;

    &:hover {
      background-color: #057dde;
    }
  }
}
</style>
