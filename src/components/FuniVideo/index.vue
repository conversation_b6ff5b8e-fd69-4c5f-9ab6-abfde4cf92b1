<template>
  <div class="funi-video" :style="funiVideoStyle">
    <div class="left" :style="leftStyle" v-if="isShowControl">
      <div class="top">
        <el-tree :data="_dataTree" v-if='_dataTree.length' :style="treeStyle" :props="defaultTreeProps" @node-click="treeNodeClick" />
      </div>
      <div class="bottom">
        <control @output="controlOutput" :speed="speed"></control>
      </div>
    </div>
    <div :style="rightStyle" style="overflow: hidden">
      <video
        ref="videoRef"
        :id="domId"
        muted
        :style="{ height: height ? height + 'px' : '100%', width: '100%', 'object-fit': 'cover' }"
      ></video>
    </div>
    <!-- <div id="pan-tilt"></div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import mpegts from 'mpegts.js';
import Hls from 'hls.js';
import './js/videoPlayer.js';
import control from './components/control/index.vue';
// import PanTilt from './js/panTilt/panTilt.js';
// import './js/panTilt/panTilt.css';
import { ElMessage } from 'element-plus';
const props = defineProps({
  height: {
    type: Number,
    default: 500
  },
  //video样式设置
  funiVideoStyle: {
    type: Object,
    default: () => ({
      '--funi_video_background_color': 'rgba(3,25,49,.89)'
    })
  },
  //摄像头列表样式设置
  treeStyle: {
    type: Object,
    default: () => ({
      '--el-tree-text-color': '#fff',
      '--el-tree-node-hover-bg-color': '#bae7ff'
    })
  },
  //左侧容器padding
  leftPadding: {
    type: String,
    default: '10px 20px'
  },
  //左侧宽度
  leftWidth: {
    type: [Number, String],
    default: '280px'
  },
  //右侧宽度
  rightWidth: {
    type: Number,
    default: 1
  },
  //视频适配方式，默认铺满，contain cover fill scale-down
  'object-fit': {
    type: String,
    default: 'fill'
  },
  //视频地址
  url: {
    type: String,
    default: ''
  },
  //视频类型
  type: {
    type: String,
    default: ''
  },
  //大华配置
  dhConfig: {},
  //是否显示云台，目前只有大华摄像头支持
  isShowControl: {
    type: Boolean,
    default: true
  },
  //自动播放
  isAuto: {
    type: Boolean,
    default: true
  },
  //视频列表
  dataTree: {
    type: Array,
    default: () => []
  },
  //dataTree是否包含视频地址，包含则不会单独请求接口
  dataTreeVideoUrl: {
    type: String,
    default: ''
  },
  //是否展示父级--原型未展示
  isShowTreeParent: {
    type: Boolean,
    default: false
  },
  //云台操作速度
  speed: {
    type: Number,
    default: 3
  },
  //内置aplcm接口参数
  treeApiParams: {
    type: Object,
    default: () => ({})
  },
  //自定义视频请求api--其他系统扩展用
  customTreeApi: {
    type: Array,
    default: () => ({
      api: '', ///aplcm/findVideos/resourcesTree
      method: 'post',
      params: {}
    })
  },
  //视频获取url接口
  getVideoApi: {
    type: Array,
    default: () => ({
      api: '/aplcm/findVideos/startVideo', ///findVideos/startVideo
      method: 'post',
      params: {
        channelId: '',
        dataType: 1,
        streamType: 1 //1=主码流，2=辅码流，3=辅码流
      }
    })
  },
  //云台控制变焦、变倍接口
  operateCamera: {
    type: Array,
    default: () => ({
      api: '/aplcm/findVideos/operateCamera', //aplcm/findVideos/operateCamera
      method: 'post',
      params: {}
    })
  },
  //云台控制方向接口
  operateDirectApi: {
    type: Array,
    default: () => ({
      api: '/aplcm/findVideos/operateDirect', //aplcm/findVideos/operateDirect
      method: 'post',
      params: {}
    })
  },
  //tree组件字段映射--扩展用，无需配置
  defaultTreeProps: {
    type: Object,
    default: () => ({
      children: 'children',
      label: 'name',
      id: 'id',
      isParent: 'isParent',
      pId: 'pId',
      channelId: 'id'
    })
  }
});
const domId = $utils.guid().replaceAll('-', '');
//播放器
let player;
//视频格式
let videoType;
//video DOM
let videoRef = ref();
//摄像头数据
let _dataTree = ref([]);
//通道id
let channelId;
//大华视窗编号
let snum;

//挂载
onMounted(() => {
  init();
});
watch(props, (newVal, oldVal) => {
  destroy()
  init();
});
const leftStyle = computed(() => {
  if (typeof props.leftWidth == 'string') {
    return {
      width: props.leftWidth.includes('px') ? props.leftWidth : props.leftWidth + 'px',
      padding: props.leftPadding
    };
  } else if (typeof props.leftWidth == 'number') {
    return { flex: props.leftWidth, padding: props.leftPadding };
  }
});
const rightStyle = computed(() => {
  if (typeof props.rightWidth == 'string') {
    return { width: props.rightWidth.includes('px') ? props.rightWidth : props.rightWidth + 'px' };
  } else if (typeof props.rightWidth == 'number') {
    return { flex: props.rightWidth };
  }
});
/**
 * 初始化
 */
function init() {
  videoType = props.type;
  if (!videoType && props.url) {
    videoType = props.url.split('.').reverse()[0];
  }
  //没传类型和url则默认大华
  if (!videoType) {
    videoType = 'dh';
  }
  loadTree();
  //flv
  if (['mpegts', 'm2ts', 'flv'].includes(videoType)) {
    if (!mpegts.getFeatureList().mseLivePlayback) {
      throw new Error('Your browser does not support MSE live playback');
    }
    player = mpegts.createPlayer({
      type: videoType,
      isLive: true,
      url: props.url
    });
    player.attachMediaElement(videoRef.value);
    player.load();
  } else if (['m3u8'].includes(videoType)) {
    if (videoRef.value.canPlayType('application/vnd.apple.mpegurl')) {
      videoRef.value.src = props.url;
    } else if (Hls.isSupported()) {
      player = new Hls();
      player.loadSource(props.url);
      player.attachMedia(videoType);
    } else {
      throw new Error('Your browser does not support MSE live playback');
    }
  } else if (['dh'].includes(videoType)) {
    snum = 0;
    player = new VideoPlayer(
      props.dhConfig
        ? props.dhConfig
        : {
            videoId: domId, // 唯一id，不能重复
            division: 1, //初始化创建窗口个数
            windowType: 0,
            createSuccess: versionInfo => {},
            clickWindow: (_snum, info) => {
              snum = _snum;
            },
            createError: err => {}
          }
    );
  }
  if (props.isAuto) {
    play();
  }
}

/**
 * 拼接rstp 和 token
 */
function dealUrl(data) {
  let path = data.url;
  //双网卡
  if (path.includes('|')) {
    path = path
      .split('|')
      .map(item => {
        return item + '?token=' + data.token;
      })
      .join('|');
  } else {
    path = path + '?token=' + data.token;
  }
  return path;
}
/**
 * 加载视频列表
 */
function loadTree(requestObj = props.customTreeApi) {
  if (requestObj.api) {
    $http[requestObj.method](requestObj.api, requestObj.params).then(res => {
      _dataTree.value = transFormDataTree(res.list);
    });
  } else if (props.dataTree.length) {
    _dataTree.value = transFormDataTree(props.dataTree);
  } else if(props.treeApiParams && Object.values(props.treeApiParams).length) {
    $http
      .post('/aplcm/findVideos/resourcesTree', {
        rType: 302,
        nodeType: 'resource',
        isSearchSubResources: false,
        ...props.treeApiParams
      })
      .then(res => {
        _dataTree.value = transFormDataTree(res.list);
      });
  }
}

// 递归循环是否触发treeNodeClick
let isEmitTreeNodeClick = false;
/**
 * 递归循环
 */
function loopForEach(arr, list) {
  arr.forEach(item => {
    !item.children && (item.children = []);
    let children = list.filter(x => x[props.defaultTreeProps.pId] == item[props.defaultTreeProps.id]);
    if (children.length) {
      item.children = children;
      loopForEach(item.children, list);
    } else {
      if (!isEmitTreeNodeClick && !item[props.defaultTreeProps.isParent]) {
        treeNodeClick(item, { isLeaf: true });
        isEmitTreeNodeClick = true;
      }
    }
  });
}

/**
 * 转化数据
 */
function transFormDataTree(list) {
  if (!props.isShowTreeParent) {
    let arr = list.filter(x => !x[props.defaultTreeProps.isParent]);
    arr[0] && treeNodeClick(arr[0], { isLeaf: true });
    return list.filter(x => !x[props.defaultTreeProps.isParent]);
  }
  if (list.some(x => x[props.defaultTreeProps.children])) {
    let data = list;
    while (data[props.defaultTreeProps.children]) {
      if (!(data[props.defaultTreeProps.children] && data[props.defaultTreeProps.children].length)) {
        treeNodeClick(data, { isLeaf: true });
      } else {
        data = data[props.defaultTreeProps.children];
      }
    }
    return list;
  }
  let arr = list.filter(x => x[props.defaultTreeProps.isParent]);
  loopForEach(arr, list);

  return arr;
}

//加载视频
function getVideoUrl(requestObj = props.getVideoApi) {
  if (requestObj.api) {
    $http[requestObj.method](requestObj.api, { ...requestObj.params, channelId: channelId }).then(res => {
      player.realByUrl({
        channelId: channelId,
        snum: snum,
        path: dealUrl(res)
      });
    });
  }
}

/**
 * 视频树点击---//目前只支持大华
 */
function treeNodeClick(item, node) {
  //子节点
  if (node.isLeaf) {
    channelId = item[props.defaultTreeProps.channelId];
    if (props.dataTreeVideoUrl) {
    } else {
      getVideoUrl();
    }
  }
  // emits
}

/**
 * 云台控制
 */
function controlOutput(params) {
  if (!channelId) {
    ElMessage('请选择摄像头!');
    return;
  }
  if (params.type == 'operateCamera') {
    $http[props.operateCamera.method](props.operateCamera.api, {
      ...props.operateCamera.params,
      ...params,
      channelId
    }).then(res => {});
  } else {
    $http[props.operateDirectApi.method](props.operateDirectApi.api, {
      ...props.operateDirectApi.params,
      ...params,
      channelId
    }).then(res => {});
  }
}
//播放
function play(url, _channelId = channelId) {
  player.play?.();
  videoRef.value.play?.();
  // if (player.realByUrl) {
  //   player.realByUrl({
  //     channelId: _channelId,
  //     snum: snum,
  //     path: url
  //   });
  // }
}
//暂停
function pause() {
  player.pause();
}
//销毁
function destroy() {
  player.unload?.();
  player.detachMediaElement?.();
  player.detachMedia?.();
  player.destroy?.();
  player = null;
}
//大华关闭当前视频窗口
function closeVideo() {
  player.closeVideo?.(snum);
}

defineExpose({
  init,
  play,
  pause,
  destroy,
  closeVideo
});
</script>

<style lang="scss" scoped>
.funi-video {
  display: flex;
  height: 100%;
  background-color: var(--funi_video_background_color);
  .left {
    display: flex;
    flex-direction: column;
    .top {
      flex: 1;
      overflow: scroll;
      &::-webkit-scrollbar {
        display: none;
      }
      :deep(.el-tree) {
        background: transparent;
      }
    }
    .bottom {
    }
  }
  .right {
  }
}
</style>
