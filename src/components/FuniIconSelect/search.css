* {
    box-sizing: border-box;
}

/* 搜索 */
.funi-icon-search {
    width: 100%;
    height: 50px;
    box-shadow: 0 20px 8px #ffffff;
    padding: 4px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 0.5px solid #e1e6f0;
    border-radius: 10px;
    position: relative;
    z-index: 99;
}


.type-select-box {
    width: 200px;
    height: 32px;
    border-right: 1px solid #e1e6f0;
    padding-right: 10px;
}

.type-input-box {
    width: calc(100% - 200px);
    height: 100%;
    padding: 4px 0 4px 10px;
}

.type-input-box :deep(.el-input__wrapper),
.type-input-box :deep(.el-input__wrapper.is-focus),
.type-input-box :deep(.el-input__wrapper:hover) {
    box-shadow: none;
}

.type-select-block {
    width: 100%;
    height: 100%;
    background-color: #f5f7fc;
    border-radius: 5px;
    padding: 0 10px 0 15px;
    line-height: 32px;
    color: var(--el-text-color-primary);
    font-weight: 600;
    font-size: 14px;
    position: relative;
    display: flex;
    flex-direction: column;

}

.type-select-block::after {
    content: "";
    display: block;
    position: absolute;
    width: 8px;
    height: 2px;
    top: 15px;
    right: 10px;
    background: var(--el-color-info);
    transition: all .2s ease-in-out;
}

.type-select-block::before {
    content: "";
    display: block;
    position: absolute;
    width: 8px;
    height: 2px;
    top: 15px;
    right: 15px;
    background: var(--el-color-info);
    transition: all .2s ease-in-out;
}

.type-select-block:hover::after {
    transform: rotate(-45deg);
    background: var(--el-color-primary);
}

.type-select-block:hover::before {
    transform: rotate(45deg);
    background: var(--el-color-primary);
}


.type-select {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 10px;
    box-shadow: 0 8px 24px #1a29470a, 0 2px 8px #1a294714;
    border: 0.5px solid #e1e6f0;
    width: 210px;
    height: auto;
    background-color: #fff;
    z-index: 999;
    display: none;
    flex-direction: column;
    padding: 9px 0;
    gap: 2px
}

.type-select-show {
    display: flex;
}

.type-select>div {
    height: 32px;
}

.type-select-option {
    padding: 0 10px;
    line-height: 32px;
    font-size: 14px;
    color: var(--el-text-color-primary);
    font-weight: 600;
    cursor: pointer;
}

.type-select-option__item {
    padding-left: 15px;
    transition: all .2s ease-in-out;
    position: relative;
}

.type-select-option__item:hover {
    color: var(--el-color-primary);
    background-color: #fff;
    background-color: #f5f7fc;
    border-radius: 5px;
    transition: all .2s ease-in-out;

}

.type-select-option__item:hover::after {
    width: 13px;
    background: var(--el-color-primary);
    transition: all .2s ease-in-out;
}

.type-selected-option {
    color: var(--el-color-primary);
    background: #f5f7fc;
    border-radius: 5px;
}

.type-select-option__item.type-selected-option::after {
    width: 13px;
    background: var(--el-color-primary);
}


.type-select-option__item::after {
    content: "";
    display: block;
    position: absolute;
    width: 6px;
    height: 2px;
    top: 15px;
    right: 10px;
    background: var(--el-color-info);
    transition: all .2s ease-in-out;
}







.up-enter-from,
.down-leave-to {
    transform: translateY(10px);
    opacity: 0;
}

.up-enter-active,
.down-enter-active,
.up-leave-active,
.down-leave-active {
    transition: all .2s ease-in-out;
}

.up-enter-to,
.down-enter-to,
.up-leave-from,
.down-leave-from {
    transform: translateY(0);
    opacity: 1;
}

.down-enter-from,
.up-leave-to {
    transform: translateY(-10px);
    opacity: 0;
}