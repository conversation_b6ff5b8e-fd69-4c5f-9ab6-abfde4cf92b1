* {
    box-sizing: border-box;
    --el-box-shadow-selected: 0px 0px 8px 1px #0080ff;
}

.funi-icon-select {
    height: calc(70vh - 50px);
    overflow: hidden;
    overflow-y: auto;
}

.icon-type-name {
    font-size: 18px;
    font-weight: 600;
    color: #1a2947;
    margin: 10px 0 0;
    padding-left: 10px;
}

.icon-total {
    display: inline-block;
    margin-left: 8px;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #5d667a;
    background-color: #f5f7fc;
    border: 1px solid transparent;
    padding: 0 8px;
    border-radius: 4px;
}

.icons-list {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 10px;
    padding: 10px;
}

.icons-item {
    width: 100%;
    padding-bottom: 100%;
    position: relative;
}

.icons-item__self {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    box-shadow: var(--el-box-shadow-lighter);
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    border-radius: 10px;
    cursor: pointer;
}

.icons-item__self:hover {
    box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, .2);
}

.icons-item__self>svg {
    transition: all .3s;
}

.icons-item__self:hover>svg {
    transform: scale(1.3);
    transform-origin: center;
    transition: all .3s;
}

.icon-item__clicked {
    box-shadow: var(--el-box-shadow-selected);
    background-color: #eaf4ff;
}

.icon-item__clicked>.icon-set.icon-set-advanced {
    display: block;
}

.icons-item__description {
    display: inline-block;
    height: 12px;
    transform: scale(0.7);
    transform-origin: center;
    width: 100%;
    text-align: center;
    word-wrap: break-word;
    line-height: 14px;
}

.icon-color-size {
    width: 100%;
    height: 30px;
    /* padding: 6px; */
    box-sizing: border-box;
    /* border: 1px solid var(--el-border-color-light); */
    /* border-radius: 5px; */
}

.icon-color-size__box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.icon-color-size__box>div {
    width: 50%;
    height: 100%;
    padding: 4px;
}

.icon-color {
    border-right: 1px solid var(--el-border-color-light);
}

.icon-color-block {
    width: 100%;
    height: 100%;
    background-color: var(--icon-color);
    text-align: center;
    border-radius: 3px;
    line-height: 22px;
    cursor: pointer;
}

.icon-color-block>span {
    color: #fff;
    mix-blend-mode: difference;
}

.icon-size {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 4px;
}

.icon-size>span {
    font-size: 12px;
    flex: 0 0 auto;
}

.icon-set {
    width: 14px;
    height: 14px;
    border-radius: 2px 2px 10px 2px;
    position: absolute;
    bottom: 2px;
    right: 2px;
    font-size: 12px;
    text-align: center;
    line-height: 14px;
    display: none;
}