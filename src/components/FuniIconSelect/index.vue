<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-12-04 13:57:39
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-20 16:32:22
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniIconSelect/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <!-- <div><el-button type="" @click="selectIcon">选择icon</el-button></div> -->
  <funi-dialog title="图标" v-model="dialogVisible" size="large" @confirm="onConfirm">
    <div>
      <!-- 搜索 -->
      <search
        :icon-types="iconTypes"
        :typeCode="typeCode"
        :scrollBlock="scrollBlock"
        @updateType="updateType"
        @updateIconName="updateIconName"
      ></search>
      <div class="funi-icon-select">
        <div class="icons-list funi-icons-list__remixicon" style="padding: 5px" data-name="所有"></div>
        <div v-for="(item, index) in iconTypes">
          <div class="icon-type-name" :data-name="item">
            {{ item }}
            <span class="icon-total">{{ Object.keys(iconTypesItems[item]).length }}</span>
          </div>
          <div class="icons-list funi-icons-list__remixicon" :data-name="item">
            <div class="icons-item" v-for="(iconItem, iconIndex) in iconsType[item]" :key="iconItem.id">
              <div
                class="icons-item__self"
                :class="{
                  'icon-item__clicked': iconItem.id === iconObject.id
                }"
                :id="'item-' + iconItem.id"
                @click="setSelectIcon(iconItem)"
              >
                <funi-icon
                  :icon="iconItem.key"
                  :style="{
                    'font-size': '20px',
                    color: iconItem.id === iconObject.id ? iconObject.color : iconItem.color
                  }"
                ></funi-icon>
                <div class="icons-item__description">
                  {{ getDescription(iconItem.description) }}
                </div>
                <div
                  class="icon-set"
                  :class="`icon-set-${mode}`"
                  :id="iconItem.id"
                  :ref="
                    e => {
                      virtualRefs[iconItem.key] = e;
                    }
                  "
                  @click="
                    e => {
                      clickSite(iconItem, e);
                    }
                  "
                >
                  <funi-icon icon="ri:settings-2-fill"></funi-icon>
                </div>
              </div>
            </div>
          </div>
        </div>

        <el-popover style="width: 120px" ref="popoverRef" :virtual-ref="virtualRef" trigger="click" virtual-triggering>
          <div class="icon-color-size">
            <div
              class="icon-color-size__box"
              :style="{
                '--icon-color': iconObject?.color || ''
              }"
            >
              <div class="icon-color">
                <div class="icon-color-block">
                  <span> / </span>
                </div>
              </div>
              <div class="icon-size">
                <el-input size="small" v-model="iconObject.size"></el-input>
                <span>px</span>
              </div>
            </div>
          </div>
        </el-popover>
      </div>
    </div>
  </funi-dialog>
</template>
<script setup>
import FuniDialog from '../FuniDialog/index.vue';
import icons from './assets/icons.json';
import FuniIcon from '../FuniIcon/index.vue';
import Search from './search.vue';
import { ref, onMounted, unref, nextTick, computed } from 'vue';
const props = defineProps({
  // mode:basic 基础；advanced 高级
  mode: { type: String, default: 'basic' }
});

const emit = defineEmits(['updateIcon']);
const dialogVisible = ref(false);
const selectIcon = () => {
  show('ri:folder-settings-fill');
  //   show();
};
const popoverRef = ref();
const virtualRef = ref(void 0);
let virtualRefs = {};
let timer = void 0;
const scrollBlock = ref({});
const iconObject = ref({
  key: '',
  size: '',
  color: '',
  description: '',
  id: ''
});
const typeCode = ref(void 0);
const iconTypes = ref([]);
const iconTypesItems = ref({});
let io = void 0; // IntersectionObserver监听io

/**
 * @description 计算属性获取icon 的所有type
 * **/
const iconsType = computed(() => {
  let obj = {};
  iconTypes.value.forEach(item => {
    obj[item] = [];
    Object.keys(iconTypesItems.value[item]).forEach(key => {
      obj[item].push({
        key: item == '编辑器' ? `ri:${key}` : `ri:${key}-fill`,
        size: '16',
        color: '#606266',
        description: iconTypesItems.value[item][key],
        id: 'icon_' + key
      });
    });
  });
  return obj;
});

/**
 * @description 简化icon的描述 （个别icon 的描述过于的长）
 * **/
const getDescription = des => {
  let list = des.split(',');
  for (let i = 0; i < list.length; i++) {
    if (/[\u4e00-\u9fa5]/.test(list[i])) {
      return list[i];
    }
  }
  return list[0];
};

/**
 * @description 点击icon
 * @param object iconItem 本身对象
 * **/
const setSelectIcon = iconItem => {
  if (iconObject.value.id !== iconItem.id) iconObject.value = $utils.clone(iconItem, true);
};

/**
 * @description 点击set 设置icon的coloe size
 * @param object iconItem 本身对象
 * **/
const clickSite = async iconItem => {
  let id = virtualRefs[iconItem.key].id;
  if (virtualRef.value?.id !== id) {
    unref(popoverRef).hide();
    await nextTick();
    virtualRef.value = virtualRefs[iconItem.key];
    await nextTick();
    virtualRef.value.click();
  }
};

/**
 * @description 弹窗的确认
 * **/
const onConfirm = () => {
  if (props.mode == 'basic') {
    emit('updateIcon', iconObject.value.key);
  } else if (props.mode == 'advanced') {
    emit('updateIcon', unref(iconObject));
  }
  dialogVisible.value = false;
};

/**
 * @description 打开弹窗的show事件
 * @param object|string  e
 * **/
const show = async e => {
  dialogVisible.value = true;
  await nextTick();
  updateIconName();
  if (typeof e == 'string') {
    let k = e.split(':')[1].split('-fill')[0];
    iconObject.value = {
      key: e,
      id: 'icon_' + k
    };
  } else if (e && Object.keys(e).length) {
    iconObject.value = e;
  } else {
    iconObject.value = { key: '', size: '', color: '', description: '', id: '' };
  }
  await nextTick();
  if (iconObject.value.id) setScrollIntoView();
};

/**
 * @description 设置当前icon滚动到可见区域
 * **/
const setScrollIntoView = () => {
  document.getElementById('item-' + iconObject.value.id).scrollIntoView({
    block: 'center',
    inline: 'nearest',
    behavior: 'smooth'
  });
};

/**
 * @description 更新icon的选中type
 * @param string type type名称
 * **/
const updateType = type => {
  typeCode.value = type;
  document.querySelector(`.icons-list.funi-icons-list__remixicon[data-name=${type}]`).scrollIntoView({
    block: type == '所有' ? 'center' : 'start',
    inline: 'nearest',
    behavior: 'smooth'
  });
};

/**
 * @description 初始化Scroll 配置
 * **/
const initScrollBlock = () => {
  let list = document.querySelectorAll(`.icons-list.funi-icons-list__remixicon[data-name]`);
  scrollBlock.value = [];
  list.forEach((item, index) => {
    scrollBlock.value[item.dataset.name] = {
      show: true,
      direction: 'up'
    };
  });
};

/**
 * @description 设置选中type回显
 * **/
const setTypeCode = () => {
  for (let key in scrollBlock.value) {
    if (scrollBlock.value[key].show) {
      typeCode.value = key;
      break;
    }
  }
};

/***
 * @description icon 的搜索
 * @param string e
 * **/
const updateIconName = async e => {
  let iconsList = $utils.clone(icons, true);
  if (e) {
    let types = Object.keys(iconsList);
    let obj = {};
    types.forEach(item => {
      obj[item] = {};
      for (let key in iconsList[item]) {
        if (iconsList[item][key].includes(e)) obj[item][key] = iconsList[item][key];
      }
      if (!Object.keys(obj[item]).length) Reflect.deleteProperty(obj, item);
    });
    iconTypesItems.value = obj;
  } else {
    iconTypesItems.value = iconsList;
  }
  iconTypes.value = Object.keys(iconTypesItems.value);
  await nextTick();
  initScrollBlock();
  setIntersectionObserver();
  updateType('所有');
};

const setIntersectionObserver = () => {
  let list = document.querySelectorAll(`.icons-list.funi-icons-list__remixicon[data-name]`);
  io?.disconnect();
  io = new IntersectionObserver(intersectionObserverCallback);
  for (let i = 0; i < list.length; i++) {
    io.observe(list[i]);
  }
};
const intersectionObserverCallback = entries => {
  entries.forEach(item => {
    scrollBlock.value[item.target.dataset.name].show = item.isIntersecting;
    let direction = '';
    if (item.isIntersecting) {
      direction = item.boundingClientRect.y >= 0 ? 'up' : 'down';
      if (item.target.dataset.name == '所有') direction = 'down';
    } else {
      direction = item.boundingClientRect.y <= 0 ? 'up' : 'down';
      if (item.target.dataset.name == '所有') direction = 'up';
    }
    for (let key in scrollBlock.value) {
      scrollBlock.value[key].direction = direction;
    }
  });
  clearTimeout(timer);
  timer = setTimeout(() => {
    clearTimeout(timer);
    setTypeCode();
  }, 300);
};
defineExpose({
  show
});
</script>
<style scoped>
@import url(./index.css);
</style>
