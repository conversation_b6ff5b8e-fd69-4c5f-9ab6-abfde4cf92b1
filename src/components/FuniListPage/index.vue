<template>
  <funi-teleport to=".layout-content__wrap" :disabled="!teleported">
    <el-tabs v-model="activeTab" :class="['funi_list_page', 'bg-white', { hide_tab: hideTab, teleported }]">
      <el-tab-pane :label="item.label" :name="item.key" v-for="(item, index) in cardTab" :key="item.key">
        <!-- <div class="search_box" v-if="item.showSearch !== false">
        <funi-search @output="searchOutput" />
      </div> -->
        <funi-curd
          v-on="item.curdOption.on || {}"
          :loading="loading[index]"
          :lodaData="
            item.api || item.curdOption.api ? (page, searchParams = {}) => getData(item, page, searchParams) : undefined
          "
          ref="curd"
          :isShowSearch="isShowSearch"
          :searchConfig="getSearchConfig()"
          :actionsProps="{ disabled: activeTab !== item.key }"
          :useTools="true"
          v-bind="item.curdOption"
          :reloadOnActive="false"
          @beforeRequest="beforeRequest(item)"
          @afterRequest="list => afterRequest(list, item)"
          @requestError="error => requestError(error, item)"
          @col-setting-change="handleColSettingChange"
          v-if="!item.slot"
        >
          <!-- 表格标题 -->
          <template #header v-if="item.curdOption.header">
            <component :is="contentRender(item.curdOption.header)" />
          </template>
          <!-- 表格按钮 -->
          <template #buttonGroup="scope">
            <template v-for="btn in item.curdOption.btns.filter(x => x.position !== 'right')">
              <el-button
                v-if="!btn.component"
                type="primary"
                v-bind="btn"
                v-auth="btn.auth"
                :key="btn.key"
                @click="headBtnClick(btn)"
              >
                {{ btn.label }}
              </el-button>
              <component v-else :is="btn.component()"></component>
            </template>
          </template>
          <template #extendButtonGroup="scope">
            <template v-for="btn in item.curdOption.btns.filter(x => x.position === 'right')">
              <el-button
                v-if="!btn.component && !btn.circle"
                type="primary"
                v-bind="btn"
                v-auth="btn.auth"
                :key="btn.key"
                @click="headBtnClick(btn)"
              >
                {{ btn.label }}
              </el-button>
              <el-button
                v-else-if="!btn.component && btn.circle"
                type="primary"
                v-bind="btn"
                v-auth="btn.auth"
                :key="`Circle_${btn.key}`"
                @click="headBtnClick(btn)"
              />
              <component v-else :is="btn.component()"></component>
            </template>
          </template>
          <!-- 表格列头插槽 -->
          <template
            #[column.slots.header]="scope"
            v-for="column in item.curdOption.columns.filter(x => x.slots?.header)"
          >
            <slot :name="column.slots.header" v-bind="scope"></slot>
          </template>
          <!-- 表格列插槽 -->
          <template
            #[column.slots.default]="scope"
            v-for="column in item.curdOption.columns.filter(x => x.slots?.default)"
          >
            <slot :name="column.slots.default" v-bind="scope"></slot>
          </template>
          <!-- 空数据插槽 -->
          <template #empty>
            <slot name="empty"></slot>
          </template>
          <!-- append插槽 -->
          <template #append>
            <slot name="append"></slot>
          </template>
          <template #pagination_extra="params">
            <component
              v-if="!!item.curdOption.paginationExtra"
              :is="contentRender(item.curdOption.paginationExtra(params))"
            ></component>
          </template>
          <template v-for="(_, slot) in $slots" #[slot]="params">
            <slot :name="slot" v-bind="params || {}" />
          </template>
        </funi-curd>
        <slot v-else :name="item.slot"></slot>
      </el-tab-pane>
    </el-tabs>
    <ExportSettingModal ref="exportSettingModalRef" />
  </funi-teleport>
</template>
<script setup lang="jsx">
import { computed, reactive, ref, isVNode, watch, nextTick, onActivated, unref, getCurrentInstance } from 'vue';
import AppApis from '@/apis/app';
import lzString from 'lz-string';
import { useRoute } from 'vue-router';
import ExportSettingModal from './ExportSettingModal.vue';

const emit = defineEmits(['headBtnClick', 'beforeRequest', 'afterRequest', 'requestError']);
const props = defineProps({
  cardTab: {
    type: Array,
    default() {
      return [];
    }
  },
  isShowSearch: { type: Boolean, default: true },
  active: { type: String },
  showTab: {
    type: Boolean
  },
  teleported: { type: Boolean, default: true },
  reloadOnActive: Boolean
});

const route = useRoute();
const routePath = route.path;
const instance = getCurrentInstance();

//data
const activeTab = ref(props.active || props.cardTab[0]?.key);
const tabCurdId = key => `${routePath}/${key}/curd`;
const activeTabCurdId = computed(() => tabCurdId(activeTab.value));
const exportSettingModalRef = ref();

//curd实例
let curd = ref();
let loading = reactive([]);
let skipReloadCount = 1;

//是否隐藏tab
const hideTab = computed(() => {
  if (props.cardTab.length > 1) {
    return props.showTab !== false;
  }
  return props.showTab !== true;
});

const activeCurdRef = computed(() => {
  return curd.value?.[props.cardTab.findIndex(x => x.key == activeTab.value)];
});

watch(activeTab, autoReloadActiveCurd);

onActivated(() => {
  skipReloadCount === 0 && autoReloadActiveCurd();
  skipReloadCount = Math.max(0, skipReloadCount - 1);
});

/**
 * 配置curd自动刷新，curdOption.reloadOnActive优先级高于props.reloadOnActive
 * 1. curdOption.reloadOnActive为true时，自动刷新
 * 2. curdOption.reloadOnActive为false时，不自动刷新
 * 3. curdOption.reloadOnActive未配置时，根据props.reloadOnActive判断是否自动刷新
 */
function autoReloadActiveCurd() {
  const activeTabIndex = props.cardTab.findIndex(x => x.key == activeTab.value);
  const activeCurd = curd.value?.[activeTabIndex];
  const curdOption = props.cardTab[activeTabIndex]?.curdOption;
  if (!activeCurd) return;

  const reloadOnActive =
    curdOption.reloadOnActive === true || (curdOption.reloadOnActive !== false && props.reloadOnActive);
  reloadOnActive && nextTick(() => activeCurd.reload({ resetPage: false }));
}

//请求函数
function getData(item, page, searchParams = {}) {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  //api
  let api = item.api || item.curdOption.api;
  //requestParams
  let requestParams = item.requestParams || item.curdOption.requestParams;
  if (api) {
    loading[index] = true;
    let _searchParams = searchParams;
    //外部整理搜索参数
    if (typeof item.fixSearchParams == 'function') {
      _searchParams = item.fixSearchParams(searchParams);
    }
    const params = {
      ...page,
      ...(requestParams || {}),
      ..._searchParams
    };
    return $http
      .post(api, params)
      .then(res => {
        if (item.curdOption && !!item.curdOption.dataCallback) {
          return item.curdOption.dataCallback({ response: res || {}, params });
        }
        return res;
      })
      .finally(() => {
        loading[index] = false;
      });
  }
}

async function exportData({ page = {}, full = false } = {}) {
  const cardItemIndex = props.cardTab.findIndex(x => x.key == activeTab.value);
  const cardItem = props.cardTab[cardItemIndex];

  exportSettingModalRef.value.show({
    columns: cardItem.curdOption.columns,
    onConfirm: async settings => {
      const loading = instance.proxy.$loading({ fullscreen: true });

      const columns = settings.fields;
      const fileName = `${settings.fileName || '导出数据'}.${settings.fileType}`;
      if (!!cardItem) {
        const appCode = window.location.hash.split('/').filter(Boolean)[2];
        const api = `/as/${appCode}/model/exportQueryList`;
        const requestParams = cardItem.requestParams || cardItem.curdOption.requestParams;

        const searchParams = unref(activeCurdRef).searchParams || {};
        const _searchParams =
          typeof cardItem.fixSearchParams == 'function' ? cardItem.fixSearchParams(searchParams) : searchParams;

        const params = { ...(page || {}), ...(requestParams || {}), ..._searchParams };
        if (full) {
          try {
            const totalSize = await $http.post(api, { ...params, pageSize: 2, pageNo: 1 }).then(res => res.total);
            const splitSize = 500;
            const pages = Math.ceil(totalSize / splitSize);
            const requests = await Promise.all(
              Array.from({ length: pages }).map((_, index) =>
                $http
                  .post(api, { ...params, pageSize: splitSize, pageNo: index + 1 }, { timeout: 60000 * 5 })
                  .then(async ({ list: fullData }) => {
                    if (cardItem.curdOption && !!cardItem.curdOption.dataCallback) {
                      const data = await cardItem.curdOption.dataCallback({
                        response: { list: fullData || {} },
                        params: { ...params, pageSize: splitSize, pageNo: index + 1 }
                      });
                      return data?.list ?? [];
                    }
                    return fullData;
                  })
              )
            );
            unref(activeCurdRef).handleExport(requests.flat(), fileName, columns);
          } catch (error) {}
          loading.close();
        } else {
          $http
            .post(api, params, { timeout: 60000 * 5 })
            .then(async ({ list: fullData }) => {
              if (cardItem.curdOption && !!cardItem.curdOption.dataCallback) {
                const data = await cardItem.curdOption.dataCallback({ response: { list: fullData || {} }, params });
                unref(activeCurdRef).handleExport(data?.list ?? [], fileName, columns);
              } else {
                unref(activeCurdRef).handleExport(fullData, fileName, columns);
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      }
    }
  });
}

function exportFullData() {
  exportData({ full: true });
}

function exportCurrentPageData() {
  exportData({
    page: {
      pageNo: unref(activeCurdRef).pageRef.currentPage,
      pageSize: unref(activeCurdRef).pageRef.pageSize,
      pageIndex: unref(activeCurdRef).pageRef.currentPage
    }
  });
}

//搜索配置
function getSearchConfig() {
  return (
    props.cardTab.find(x => x.key == activeTab.value).searchConfig || {
      schema: [
        {
          prop: 'keyword',
          label: '关键字',
          component: 'el-input'
        }
      ]
    }
  );
}

//头部按钮点击事件
function headBtnClick(item) {
  emit('headBtnClick', item.key);
}

//搜索
function searchOutput(arr) {
  reload();
}

//vNode渲染
function contentRender(header) {
  return isVNode(header) ? header : <span>{header || ''}</span>;
}

//刷新
function reload({ resetPage = true } = {}) {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  if (curd.value) {
    curd.value[index].reload({ resetPage });
  }
}

//刷新对应索引列表
function reloadIndex({ resetPage, index } = { resetPage: true, index: 0 }) {
  if (curd.value) {
    curd.value[index].reload({ resetPage });
  }
}

//请求前事件
function beforeRequest() {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  loading[index] = true;
  emit('beforeRequest');
}

//请求后事件
function afterRequest(list) {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  loading[index] = false;
  emit('afterRequest', list);
}

//请求抛错事件
function requestError(error) {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  loading[index] = false;
  emit('requestError', error);
}

function getSearchParams() {
  const cardItem = props.cardTab.find(x => x.key == activeTab.value);
  if (!!cardItem) {
    const requestParams = cardItem.requestParams || cardItem.curdOption.requestParams;
    const searchParams = unref(activeCurdRef).searchParams || {};
    const _searchParams =
      typeof cardItem.fixSearchParams == 'function' ? cardItem.fixSearchParams(searchParams) : searchParams;
    const page = {
      pageNo: unref(activeCurdRef).pageRef.currentPage,
      pageSize: unref(activeCurdRef).pageRef.pageSize,
      pageIndex: unref(activeCurdRef).pageRef.currentPage
    };
    return Object.assign({}, page || {}, requestParams || {}, _searchParams || {});
  }
  return {};
}

function handleColSettingChange(settings) {
  const compressedString = lzString.compress(JSON.stringify({ columns: settings }));
  AppApis.newCurdCustomization({
    tableId: activeTabCurdId.value,
    tableTitleJson: JSON.stringify({ compressed: compressedString })
  });
}

defineExpose({
  reload,
  reloadIndex,
  activeCurdRef,
  exportFullData,
  exportCurrentPageData
});
</script>
<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;

.hide_tab {
  :deep(.el-tabs__header) {
    display: none;
  }
}

.funi_list_page {
  :deep(.el-tabs__item) {
    padding: 0 20px !important;
  }

  .search_box {
    padding: 8px 16px;
    background: white;
    margin-bottom: 16px;
  }

  .title_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: white;

    .table_title {
      font-size: 16px;
      color: #515151;
    }

    .btn_box {
      display: flex;

      .btn {
        min-width: 80px;
      }
    }
  }

  :deep() {
    .#{el.$namespace}-tabs__header {
      margin-bottom: 0px;
    }
  }
}
</style>

<style lang="scss" scoped>
// 强制FuniListPage高度100%，curd分页置底，内容自动拉满
.funi_list_page.teleported {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(> .el-tabs__header) {
    flex-shrink: 0;
  }

  :deep(> .el-tabs__content) {
    flex-grow: 1;
    overflow: auto;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
      > .funi-curd__wrap {
        // height: 100%;
        flex-grow: 1;
      }
    }
  }
}
</style>
