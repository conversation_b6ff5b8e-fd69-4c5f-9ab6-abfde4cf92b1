<template>
  <funi-dialog v-model="visible" title="导出" @confirm="handleOk" @cancel="handleCancel">
    <el-form :model="form" label-width="120px" style="max-width: 600px" label-position="right">
      <el-form-item label="文件名" prop="fileName">
        <el-input v-model="form.fileName" placeholder="请输入文件名" />
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-select v-model="form.fileType" placeholder="请选择文件类型">
          <el-option label="Excel(*.xlsx)" value="xlsx" />
          <el-option label="CSV(*.csv)" value="csv" />
        </el-select>
      </el-form-item>
      <el-form-item label="选择字段">
        <div class="tree_box">
          <div class="tree_box_title">
            <el-checkbox v-model="form.allChecked" :indeterminate="form.indeterminate" @change="handleAllCheckedChange"
              >全部字段</el-checkbox
            >
          </div>
          <el-tree
            ref="treeRef"
            style="max-width: 600px"
            :data="data"
            show-checkbox
            default-expand-all
            node-key="prop"
            highlight-current
            :props="defaultProps"
            @check="handleCheckChange"
          />
        </div>
      </el-form-item>
    </el-form>
  </funi-dialog>
</template>

<script setup>
import { nextTick, reactive, ref, computed, getCurrentInstance } from 'vue';

const instance = getCurrentInstance();
const treeRef = ref(null);
const data = ref([]);
const visible = ref(false);
const form = reactive({
  fileName: '',
  fields: [],
  allChecked: true,
  indeterminate: false,
  fileType: 'xlsx'
});

let onConfirmCallback = async () => {};

const defaultProps = {
  children: 'children',
  label: 'label'
};

const allKeys = computed(() => $utils.toTreeArray(data.value).map(x => x.prop));

const handleOk = async () => {
  try {
    form.fields = [...treeRef.value.getCheckedNodes(), ...treeRef.value.getHalfCheckedNodes()];
    if (!form.fields.length) {
      instance.proxy.$notify.warning('请选择字段');
      return;
    }

    await onConfirmCallback?.({
      fileName: form.fileName,
      fields: $utils.toArrayTree(form.fields.map(({ children, ...rest }) => rest)),
      fileType: form.fileType
    });
    visible.value = false;
  } catch (error) {
    console.error('export setting confirm error', error);
  }
};

const handleCancel = () => {
  visible.value = false;
};

const show = ({ onConfirm, columns } = {}) => {
  data.value = $utils.mapTree(
    columns.filter(x => $utils.isPlainObject(x) && x.prop && x.hidden !== true),
    (item, index, items, path, parent, nodes) => ({
      ...item,
      label: item.label,
      prop: item.prop,
      parentId: parent?.id ?? ''
    })
  );

  onConfirmCallback = onConfirm;
  form.fileName = '';
  form.fileType = 'xlsx';
  form.fields = [];
  form.allChecked = true;
  form.indeterminate = false;
  visible.value = true;
  nextTick(() => {
    treeRef.value.setCheckedKeys(allKeys.value);
  });
};

const handleAllCheckedChange = () => {
  if (form.allChecked) {
    treeRef.value.setCheckedKeys(allKeys.value);
  } else {
    treeRef.value.setCheckedKeys([]);
  }
  form.indeterminate = false;
};

const handleCheckChange = (node, checked) => {
  const checkedKeys = treeRef.value.getCheckedKeys();
  form.allChecked = $utils.isEqual(checkedKeys, allKeys.value);
  form.indeterminate = !form.allChecked && checkedKeys.length > 0;
};

defineExpose({
  show
});
</script>

<style lang="scss" scoped>
.tree_box {
  width: 100%;
  height: 300px;
  overflow: hidden;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  // padding: 10px;
  .tree_box_title {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    height: 30px;
    background: #f5f7fa;
    margin: 0;
    padding-left: 23px;
    border: 1px solid #e6e6e6;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    box-sizing: border-box;
    color: #333;
  }
  .el-tree {
    flex-grow: 1;
    overflow: auto;
  }
}
</style>
