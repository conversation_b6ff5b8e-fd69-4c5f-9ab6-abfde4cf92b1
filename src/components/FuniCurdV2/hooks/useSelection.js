import { unref, ref } from 'vue';

export const useSelection = ({ table, datas, columns, emit, rowKey, rowSelection, checkOnRowClick }) => {
  // 当前行
  const currentRow = ref(null);
  const currentRowKey = ref('');

  /**
   * 单击行事件
   * @param {*} row
   * @param {*} column
   * @param {*} event
   * @returns
   */
  const handleRowClick = (row, column, event) => {
    if (!rowSelectable(row)) return;

    // if (rowSelection === 'click') {
    //   toggleSelection(row);
    //   setCurrentRow(row);
    // }
    emit('row-click', { column, row, selection: unref(table).getSelectionRows(), currentRow: currentRow.value });
  };

  /**
   * 双击行事件
   * @param {*} row
   * @param {*} column
   * @param {*} event
   * @returns
   */
  const handleRowDoubleClick = (row, column, event) => {
    if (!rowSelectable(row)) return;

    // if (rowSelection === 'dblclick') {
    //   toggleSelection(row);
    //   setCurrentRow(row);
    // }
    emit('row-dblclick', { column, row, selection: unref(table).getSelectionRows(), currentRow: currentRow.value });
  };

  /**
   * 单击单元格事件
   * @param {*} row
   * @param {*} column
   * @param {*} event
   * @returns
   */
  const handleCellClick = (row, column, cell, event) => {
    if (!rowSelectable(row) || !cellSelectable(cell)) return;
    if (checkOnRowClick) {
      toggleSelection(row);
      setCurrentRow(row);
    }
    emit('row-click', { column, row, selection: unref(table).getSelectionRows(), currentRow: currentRow.value });
  };

  /**
   * 双击单元格事件
   * @param {*} row
   * @param {*} column
   * @param {*} event
   * @returns
   */
  const handleCellDoubleClick = (row, column, cell, event) => {
    if (!rowSelectable(row) || !cellSelectable(cell)) return;

    emit('row-dblclick', { column, row, selection: unref(table).getSelectionRows(), currentRow: currentRow.value });
  };

  /**
   * 当前行是否可选中
   * @param {*} row
   * @returns {boolean}
   */
  const rowSelectable = row => {
    if (!row) return false;

    const selectionColumn = unref(columns).find(column => ['selection', 'radio'].includes(column.type));
    if (!!selectionColumn) {
      const selectable = selectionColumn.selectable;
      if ($utils.isBoolean(selectable)) return selectable;
      if ($utils.isFunction(selectable)) {
        const key = rowKey || 'id';
        const rowIndex = unref(datas).findIndex(item => item[key] === row[key]);
        return selectable(row, rowIndex);
      }
    }
    return true;
  };

  const cellSelectable = cell => {
    return !cell.className.includes('disabled-cell');
  };

  const toggleSelection = row => {
    // 复选
    unref(table).toggleRowSelection(row);
  };

  /**
   * @description: 设置当前行
   */
  const setCurrentRow = row => {
    if (!row || !rowKey) return;

    currentRowKey.value = row[rowKey];
    currentRow.value = row;
  };

  /**
   * @description: 通过RowKey设置当前行
   */
  const setCurrentRowByKey = key => {
    if (!key || !rowKey) return;
    setCurrentRow(unref(datas).find(item => item[rowKey] === key));
  };

  const rowSelected = row => {
    return !!unref(currentRowKey) && !!row ? unref(currentRowKey) === row[rowKey] : false;
  };

  const resetCurrentRow = () => {
    currentRowKey.value = '';
    currentRow.value = undefined;
  };

  return {
    currentRow,
    currentRowKey,
    handleCellClick,
    handleCellDoubleClick,
    rowSelectable,
    toggleSelection,
    setCurrentRow,
    setCurrentRowByKey,
    rowSelected,
    resetCurrentRow
  };
};
