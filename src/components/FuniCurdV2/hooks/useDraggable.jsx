export const useDraggable = (props, emit) => {
  const draggableOptions = [
    {
      el: 'tbody',
      option: {
        disabled: !props.draggable,
        handle: '.draggable-handle',
        onEnd: e => emit('draggableEnd', e)
      }
    }
  ];

  const draggableColumn = {
    fixed: 'left',
    prop: 'draggable_' + $utils.guid(),
    width: 40,
    align: 'center',
    render: ({ row, index }) => (
      <div class="flex-center">
        <funi-icon class="draggable-handle" icon="system-uicons:drag" width="24" height="24"></funi-icon>
      </div>
    )
  };

  return {
    draggableColumn,
    draggableOptions
  };
};
