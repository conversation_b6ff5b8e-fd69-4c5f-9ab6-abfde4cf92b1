<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-07-19 14:58:33
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2025-01-15 10:40:44
 * @FilePath: /src/components/FuniCurdV2/components/CurdSearch.vue
 * @Description:
 * Copyright (c) 2023 by tao.yang <EMAIL>, All Rights Reserved.
-->

<template>
  <div style="background-color: white">
    <funi-search-form-v3
      v-if="useSearchV2 && env.useSearchV2"
      ref="searchFormRef"
      v-bind="searchConfig"
      :queryFields="queryFields"
      :colNumber="colNumber"
      @search="handleSearch"
      @change="handleSearchFormChange"
      @reset="handleSearch({})"
    >
      <template v-for="(_, slot) in $slots" #[slot]="params">
        <slot :name="slot" v-bind="params || {}" />
      </template>
    </funi-search-form-v3>
    <funi-search-form
      v-else
      ref="searchFormRef"
      v-bind="searchConfig"
      @search="handleSearch"
      :colNumber="props.colNumber"
      @reset="() => handleSearch({})"
    >
      <template v-for="slotName in searchFormSlots" #[slotName]="params">
        <slot :name="slotName" v-bind="params"></slot>
      </template>
    </funi-search-form>
  </div>
</template>

<script setup>
import env from '@/utils/env';
import { computed, ref, unref } from 'vue';

defineOptions({
  name: 'CurdSearch'
});

const props = defineProps({
  /** 是用4.0模式高级查询 */
  useSearchV2: { type: Boolean, default: true },
  searchConfig: { type: Object, default: () => ({}) },
  colNumber: { type: Number, default: 4 },
  queryFields: { type: Array, default: () => [] },
  searchOnFormChange: Boolean
});

const searchFormRef = ref(null);
const emit = defineEmits(['search']);
const searchFormSlots = computed(() => {
  if (!props.searchConfig.schema) return [];

  const slotNames = props.searchConfig.schema.map(item => Object.values(item.slots || {})).flat();
  return Array.from(new Set(slotNames));
});
const once_search_event = $utils.once(params => emit('search', params));
const handleSearch = params => {
  emit('search', params);
};
const handleSearchFormChange = params => {
  !!props.searchOnFormChange ? emit('search', params) : once_search_event(params);
};

defineExpose({
  getValues: () => unref(searchFormRef).getValues(),
  setValues: values => unref(searchFormRef).setValues(values),
  resetFields: () => unref(searchFormRef).resetFields()
});
</script>
