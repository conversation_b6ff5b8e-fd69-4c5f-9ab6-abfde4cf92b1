<template>
  <div class="curd-header">
    <div class="curd-header-append-row">
      <slot name="header-append-row"></slot>
    </div>
    <div class="curd-header-bar">
      <div class="curd-header-title">
        <div v-if="$slots.header" class="curd-header-custom">
          <slot name="header"></slot>
        </div>
        <div class="curd-header-button-group">
          <slot name="buttonGroup"></slot>
        </div>
      </div>
      <div v-if="useTools" class="curd-header-tools">
        <el-tooltip content="刷新" placement="top">
          <el-button link @click="emit('refresh')">
            <funi-svg name="refresh" />
          </el-button>
        </el-tooltip>
        <el-tooltip content="斑马纹" placement="top">
          <el-button link @click="emit('stripe')">
            <funi-svg name="stripe" />
          </el-button>
        </el-tooltip>
        <el-tooltip content="紧凑度" placement="top">
          <el-dropdown trigger="click" size="large" class="ml-[8px] mr-[8px] align-middle" @command="handleResize">
            <el-button link><funi-svg name="resize" /></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :style="activeDropdownItemStyle('large')" command="large">宽松</el-dropdown-item>
                <el-dropdown-item :style="activeDropdownItemStyle('default')" command="default">默认</el-dropdown-item>
                <el-dropdown-item :style="activeDropdownItemStyle('small')" command="small">紧凑</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
        <!-- <el-tooltip content="全屏" placement="top">
        <el-button link @click="handleFullscreen">
          <funi-svg name="fullscreen" />
        </el-button>
      </el-tooltip> -->
        <CurdColumnSetting :colSettings="colSettings" @col-setting="e => emit('col-setting', e)" />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
import CurdColumnSetting from './CurdColumnSetting.vue';

defineOptions({ name: 'CurdHeader' });

const emit = defineEmits(['refresh', 'col-setting', 'fullscreen', 'resize', 'stripe']);
const props = defineProps({
  useTools: Boolean,
  colSettings: { type: Array, default: () => [] }
});

const isMax = ref(false);
const activeCommand = ref('default');

const handleFullscreen = () => {
  isMax.value = !isMax.value;
  emit('fullscreen');
};

const handleResize = command => {
  activeCommand.value = command;
  emit('resize', command);
};

const activeDropdownItemStyle = command => {
  return {
    backgroundColor: activeCommand.value === command ? 'var(--el-nav-color-light-hover)' : '',
    color: activeCommand.value === command ? 'var(--el-menu-active-color)' : ''
  };
};
</script>

<style lang="scss" scoped>
.curd-header {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin-bottom: 12px;
  margin-top: 12px;

  &-append-row {
    margin-bottom: 10px;
  }

  &-bar {
    display: flex;
  }

  &-title {
    flex-grow: 1;
    display: flex;
  }

  &-custom {
    flex-grow: 1;
  }

  &-button-group {
    flex-shrink: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    flex-wrap: nowrap;
  }
  &-tools {
    flex-shrink: 0;
    margin-left: 12px;
    display: flex;
    align-items: center;

    :deep(.el-button + .el-button) {
      margin-left: 8px;
    }

    :deep(.el-button) {
      padding: 2px 0;
      border: none;
    }
  }
}
</style>
