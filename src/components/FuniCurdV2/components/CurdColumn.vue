<template>
  <el-table-column v-bind="column">
    <!-- 自定义表头 -->
    <template #header="scope">
      <slot :name="column.slots.header">{{ column.label }}</slot>
      <CurdFilterPanel
        v-if="customFilters[column.prop] || customFilters[column.label]"
        :column="{ ...scope.column, ...column }"
        :store="scope.store"
      />
    </template>
    <template #default="scope">
      <CurdColumn
        v-if="column.children?.length"
        v-for="childrenColumn in column.children"
        :key="childrenColumn.prop"
        :column="childrenColumn"
        :customFilters="customFilters"
      ></CurdColumn>
      <slot v-else-if="column.slots?.default" :name="column.slots?.default" v-bind="scope" />
      <component v-else-if="column.render" :is="contentRender(column.render, scope)" />
      <template v-else-if="!['selection', 'index', 'expand'].includes(column.type)">
        {{ defaultRenderCell(scope) }}
      </template>
    </template>
  </el-table-column>
</template>

<script setup lang="jsx">
import CurdFilterPanel from './CurdFilterPanel.vue';
import { useRender } from '../hooks/useRender.jsx';
defineOptions({
  name: 'CurdColumn',
  inheritAttrs: false
});

const props = defineProps({
  column: { type: Object, default: () => ({}) },
  customFilters: { type: Object, default: () => ({}) }
});

const { contentRender, defaultRenderCell } = useRender();
</script>
