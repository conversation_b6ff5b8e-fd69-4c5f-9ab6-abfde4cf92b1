<template>
  <el-tooltip
    ref="tooltip"
    :visible="tooltipVisible"
    :offset="0"
    :show-arrow="false"
    :stop-popper-mouse-event="false"
    placement="bottom-start"
    teleported
    effect="light"
    pure
    :popper-class="ns.b()"
    persistent
  >
    <template #content>
      <div>
        <div :class="ns.e('content')">
          <el-scrollbar :wrap-class="ns.e('wrap')">
            <el-checkbox-group v-model="filteredValue" :class="ns.e('checkbox-group')">
              <el-checkbox v-for="filter in filters" :key="filter.value" :label="filter.value">
                {{ filter.key }}
              </el-checkbox>
            </el-checkbox-group>
          </el-scrollbar>
        </div>
        <div :class="ns.e('bottom')">
          <button
            :class="{ [ns.is('disabled')]: filteredValue.length === 0 }"
            :disabled="filteredValue.length === 0"
            type="button"
            @click="handleConfirm"
          >
            确认
          </button>
          <button type="button" @click="handleReset">重置</button>
        </div>
      </div>
    </template>
    <template #default>
      <span
        v-click-outside:[popperPaneRef]="hideFilterPanel"
        :class="[`${ns.namespace.value}-table__column-filter-trigger`, `${ns.namespace.value}-none-outline`, 'ml-4px']"
        @click="showFilterPanel"
      >
        <funi-icon
          icon="ep:filter"
          v-if="tooltipVisible || filtered"
          color="var(--el-color-primary)"
          width="12px"
          height="12px"
        />
        <funi-icon v-else icon="ep:filter" width="12px" height="12px" />
      </span>
    </template>
  </el-tooltip>
</template>

<script setup>
import { ref, computed, watch, watchEffect } from 'vue';
import { useNamespace, ClickOutside } from 'element-plus';

const props = defineProps({
  column: { type: Object, default: () => ({}) },
  store: Object,
  upDataColumn: Function
});

const vClickOutside = ClickOutside;

const tooltip = ref();
const tooltipVisible = ref(false);
const filters = ref([]);
const filteredValue = ref([]);
const filteredValueCache = ref([]);
const filtered = ref(false);

const ns = useNamespace('table-filter');

// Computed
const popperPaneRef = computed(() => {
  return tooltip.value?.popperRef?.contentRef;
});

// Watcher
watch(
  tooltipVisible,
  value => {
    if (props.column) {
      // props.upDataColumn('filterOpened', value);
    }
  },
  { immediate: true }
);

watchEffect(() => {
  if (!!props.column.customFilter?.attribute?.dataSourceUrl) {
    $http
      .fetch(props.column.customFilter.attribute.dataSourceUrl)
      .then(res => {
        if (res && res.list && res.list.length > 0) {
          filters.value = (res.list || []).map(i =>
            Object.assign({}, i, {
              key: i[props.column.customFilter.attribute.labelKey || 'key'],
              value: i[props.column.customFilter.attribute.valueKey || 'value']
            })
          );
        }
      })
      .catch(err => {
        filters.value = [];
      });
  }
});

// Functions
const confirmFilter = filteredValue => {
  props.store.commit('filterChange', {
    column: props.column,
    values: filteredValue
  });
  props.store.updateAllSelected();
  filtered.value = filteredValue.length > 0;
  filteredValueCache.value = filteredValue;
};

const showFilterPanel = e => {
  e.stopPropagation();
  filteredValue.value = filteredValueCache.value;
  tooltipVisible.value = !tooltipVisible.value;
};

const hideFilterPanel = () => {
  tooltipVisible.value = false;
};

const hidden = () => (tooltipVisible.value = false);

const handleConfirm = () => {
  confirmFilter(filteredValue.value);
  hidden();
};

const handleReset = () => {
  filteredValue.value = [];
  confirmFilter(filteredValue.value);
  hidden();
};
</script>

<style lang="scss" scoped></style>
