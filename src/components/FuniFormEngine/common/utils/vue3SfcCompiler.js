/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-24 14:06:14
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-05-16 11:55:54
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\common\utils\vue3SfcCompiler.js
 * @Description: sfc运行时编译工具
 */
import {
  watchEffect,
  ref,
  reactive,
  h,
  defineComponent,
  toRefs,
  openBlock,
  createBlock,
  getCurrentInstance,
  resolveComponent,
  createVNode,
  withCtx,
  withModifiers,
  createElementBlock,
  toDisplayString,
  createElementVNode,
  pushScopeId,
  popScopeId,
  createTextVNode,
  Fragment,
  renderList,
  mergeProps,
  toHandlers,
  withDirectives,
  resolveDirective,
  shallowRef,
  normalizeProps,
  guardReactiveProps,
  provide,
  shallowReactive,
  onMounted,
  computed,
  createCommentVNode,
  vShow,
  nextTick,
  inject
} from 'vue';
import { useMultiTab } from '@/utils/hooks/useMultiTab.js';
import { Base64 } from 'js-base64';
import { parse, compileStyle, compileScript, compileTemplate, rewriteDefault } from '@vue/compiler-sfc';

export function compile(comCode, outJsString = false) {
  let _resolveComponent = resolveComponent;
  let _openBlock = openBlock;
  let _createBlock = createBlock;
  let _createVNode = createVNode;
  let _withCtx = withCtx;
  let _withModifiers = withModifiers;
  let _createElementBlock = createElementBlock;
  let _toDisplayString = toDisplayString;
  let _createElementVNode = createElementVNode;
  let _pushScopeId = pushScopeId;
  let _popScopeId = popScopeId;
  let _createTextVNode = createTextVNode;
  let _Fragment = Fragment;
  let _renderList = renderList;
  let _mergeProps = mergeProps;
  let _toHandlers = toHandlers;
  let _normalizeProps = normalizeProps;
  let _guardReactiveProps = guardReactiveProps;
  let _createCommentVNode = createCommentVNode;
  let _withDirectives = withDirectives;
  let _vShow = vShow;
  try {
    h(), ref(), _resolveComponent();
    _openBlock();
    _createBlock();
    _createVNode();
    _withCtx();
    _withModifiers();
    _createElementBlock();
    _toDisplayString();
    _createElementVNode();
    _pushScopeId();
    _popScopeId();
    _createTextVNode();
    _Fragment();
    _renderList();
    _mergeProps();
    _toHandlers();
    _normalizeProps();
    _guardReactiveProps();
    _createCommentVNode();
  } catch {
    console.log('');
  }
  const { descriptor, error } = parse(comCode);
  if (error) {
    console.log('error', error);
  }
  const id = Date.now().toString();
  const scopeId = `data-v-${id}`;
  const script = compileScript(descriptor, { id: scopeId });
  const codeList = [];
  codeList.push(rewriteDefault(script.content, '__sfc_main__'));
  codeList.push(`__sfc_main__.__scopeId='${scopeId}'`);
  const template = compileTemplate({
    source: descriptor.template.content,
    id: scopeId,
    scoped: true
  });
  codeList.push(template.code);
  codeList.push(`__sfc_main__.render=render`);
  for (const styleBlock of descriptor.styles) {
    const styleCode = compileStyle({
      source: styleBlock.content,
      id: scopeId,
      scoped: styleBlock.scoped
    });
    const styleDOM = `
  var styleScript=document.querySelector("style[class='${scopeId}']");
  if(!styleScript){
    var el = document.createElement('style');
    el.classList.add('${scopeId}');
    el.setAttribute('type','text/${styleBlock.lang}');
    el.innerHTML =  \`${styleCode.code}\`;
    document.body.append(el);
  }
`;
    codeList.push(styleDOM);
  }
  if (descriptor.styles && descriptor.styles.length !== 0) {
    let scssPath = process.env.NODE_ENV === 'production' ? 'js/browser-scss.min.js' : '/js/browser-dev-scss.min.js';
    if (descriptor.styles.some(style => style.lang === 'scss')) {
      const scriptDom = `
  var scssScript=document.querySelector("script[src='${scssPath}']");
  if(!scssScript){
    var el=document.createElement('script');
    el.setAttribute('src','${scssPath}');
    document.body.append(el);
  } else{
    try{
      findAndConvertTags();
    } catch{}
  }
  `;
      codeList.push(scriptDom);
    }
  }
  let reg = /import [\s\S]*?from "vue";?/;
  let reg1 = /import [\s\S]*?from 'vue';?/;
  let code = codeList
    .join('\n')
    .replace(reg1, '')
    .replace(reg1, '')
    .replace(reg, '')
    .replace(reg, '')
    .replace(/const __sfc_main__/, '__sfc_main__')
    .replace(/export function render/, 'let render=function');
  if (outJsString) {
    return code;
  } else {
    let proxyStr = `return {generate:function(defineComponent,h,ref,reactive,getCurrentInstance,toRefs,_resolveComponent,
      _openBlock,_createBlock,_createVNode,_withCtx,_withModifiers,_createElementBlock,_toDisplayString,
      _createElementVNode,_pushScopeId,_popScopeId,_createTextVNode,_Fragment,_renderList,_mergeProps,
      _toHandlers, resolveComponent,resolveDirective,withDirectives,shallowRef,compileJs,_normalizeProps,
      _guardReactiveProps,provide,shallowReactive,onMounted,computed,_createCommentVNode,_withDirectives,_vShow,
      watchEffect,nextTick,useMultiTab,inject
      ){
      let __sfc_main__;
      ${code}
      return __sfc_main__;
    }}`;
    let proxy = new Function(`${proxyStr}`)();
    let __sfc_main__ = proxy.generate(
      defineComponent,
      h,
      ref,
      reactive,
      getCurrentInstance,
      toRefs,
      _resolveComponent,
      _openBlock,
      _createBlock,
      _createVNode,
      _withCtx,
      _withModifiers,
      _createElementBlock,
      _toDisplayString,
      _createElementVNode,
      _pushScopeId,
      _popScopeId,
      _createTextVNode,
      _Fragment,
      _renderList,
      _mergeProps,
      _toHandlers,
      resolveComponent,
      resolveDirective,
      withDirectives,
      shallowRef,
      compileJs,
      _normalizeProps,
      _guardReactiveProps,
      provide,
      shallowReactive,
      onMounted,
      computed,
      _createCommentVNode,
      _withDirectives,
      _vShow,
      watchEffect,
      nextTick,
      useMultiTab,
      inject
    );
    return __sfc_main__;
  }
}

export function generateComponent(jsCode) {
  let _resolveComponent = resolveComponent;
  let _openBlock = openBlock;
  let _createBlock = createBlock;
  let _createVNode = createVNode;
  let _withCtx = withCtx;
  let _withModifiers = withModifiers;
  let _createElementBlock = createElementBlock;
  let _toDisplayString = toDisplayString;
  let _createElementVNode = createElementVNode;
  let _pushScopeId = pushScopeId;
  let _popScopeId = popScopeId;
  let _createTextVNode = createTextVNode;
  let _Fragment = Fragment;
  let _renderList = renderList;
  let _mergeProps = mergeProps;
  let _toHandlers = toHandlers;
  let _normalizeProps = normalizeProps;
  let _guardReactiveProps = guardReactiveProps;
  let _createCommentVNode = createCommentVNode;
  let _withDirectives = withDirectives;
  let _vShow = vShow;
  try {
    h(), ref(), _resolveComponent();
    _openBlock();
    _createBlock();
    _createVNode();
    _withCtx();
    _withModifiers();
    _createElementBlock();
    _toDisplayString();
    _createElementVNode();
    _pushScopeId();
    _popScopeId();
    _createTextVNode();
    _Fragment();
    _renderList();
    _mergeProps();
    _toHandlers();
    _guardReactiveProps();
    _createCommentVNode();
  } catch {
    console.log('');
  }
  let proxyStr = `return {generate:function(defineComponent,h,ref,reactive,getCurrentInstance,toRefs,_resolveComponent,
    _openBlock,_createBlock,_createVNode,_withCtx,_withModifiers,_createElementBlock,_toDisplayString,
    _createElementVNode,_pushScopeId,_popScopeId,_createTextVNode,_Fragment,_renderList,_mergeProps,
    _toHandlers, resolveComponent,resolveDirective,withDirectives,shallowRef,compileJs,_normalizeProps,
    _guardReactiveProps,provide,shallowReactive,onMounted,computed,_createCommentVNode,_withDirectives,_vShow,
    watchEffect,nextTick,useMultiTab,inject
    ){
    let __sfc_main__;
    ${jsCode}
    return __sfc_main__;
  }}`;
  let proxy = new Function(`${proxyStr}`)();
  let __sfc_main__ = proxy.generate(
    defineComponent,
    h,
    ref,
    reactive,
    getCurrentInstance,
    toRefs,
    _resolveComponent,
    _openBlock,
    _createBlock,
    _createVNode,
    _withCtx,
    _withModifiers,
    _createElementBlock,
    _toDisplayString,
    _createElementVNode,
    _pushScopeId,
    _popScopeId,
    _createTextVNode,
    _Fragment,
    _renderList,
    _mergeProps,
    _toHandlers,
    resolveComponent,
    resolveDirective,
    withDirectives,
    shallowRef,
    compileJs,
    _normalizeProps,
    _guardReactiveProps,
    provide,
    shallowReactive,
    onMounted,
    computed,
    _createCommentVNode,
    _withDirectives,
    _vShow,
    watchEffect,
    nextTick,
    useMultiTab,
    inject
  );
  return __sfc_main__;
}

export function compileJs(code) {
  let jsCode = Base64.decode(code);
  let sfcCode = `<script@>${jsCode}</script@>`.replaceAll('@', '');
  const { descriptor, error } = parse(sfcCode);
  const script = compileScript(descriptor, {});
  let scriptStr = rewriteDefault(script.content, '__js_main__').replace(/const __js_main__/, '__js_main__');
  let proxyStr = `return {generate:function(){
    let __js_main__;
    ${scriptStr}
    return __js_main__;
  }}`;
  let proxy = new Function(`${proxyStr}`)();
  let __js_main__ = proxy.generate();
  return __js_main__;
}
