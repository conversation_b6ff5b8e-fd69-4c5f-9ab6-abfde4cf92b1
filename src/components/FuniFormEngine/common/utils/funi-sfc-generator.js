import {
  deepClone,
  isNotNull,
  isNull,
  isEmptyStr,
  isNotNullExpression,
  getExpressionCode,
  typeToComponent
} from './util';
import { genVue2JS } from './vue2js-generator';
import { beautifierOpts } from './beautifierLoader';
import { genVue3JS } from './vue3js-generator';
import { Base64 } from 'js-base64';
import { compile, generateComponent } from './vue3SfcCompiler';
import { getRegExp } from './validators.js';
import * as extGenerator from '../../extension/funi/funi-ext-sfc-generator.js';
import lowcode from './lowcode';

export function buildClassAttr(ctn, defaultClass) {
  const cop = ctn.options;
  let gridClassArray = [];
  !!defaultClass && gridClassArray.push(defaultClass);
  !!cop.customClass && cop.customClass.length > 0 && gridClassArray.push(cop.customClass.join(' '));
  return gridClassArray.length > 0 ? `class="${gridClassArray.join(' ')}"` : '';
}

const containerTemplates = {
  //容器组件属性
  grid: (ctn, formConfig, designerConfig = {}) => {
    const gridClassAttr = buildClassAttr(ctn, 'sfc-row');
    const wop = ctn.options;
    const vShowAttr = isNotNullExpression(wop.hiddenCode)
      ? `v-if="${wop.name}Hidden"`
      : wop.hidden
      ? `v-if="false"`
      : '';
    const gridTemplate = `<el-row ${vShowAttr} ${gridClassAttr}>
${ctn.cols
  .map(col => {
    const colOpt = col.options;
    const spanAttr = colOpt.responsive ? '' : `:span="${colOpt.span}"`;
    const mdAttr = !colOpt.responsive ? '' : `:md="${colOpt.md}"`;
    const smAttr = !colOpt.responsive ? '' : `:sm="${colOpt.sm}"`;
    const xsAttr = !colOpt.responsive ? '' : `:xs="${colOpt.xs}"`;
    const offsetAttr = colOpt.offset ? `:offset="${colOpt.offset}"` : '';
    const pushAttr = colOpt.push ? `:push="${colOpt.push}"` : '';
    const pullAttr = colOpt.pull ? `:pull="${colOpt.pull}"` : '';
    const colClassAttr = buildClassAttr(col, 'grid-cell');
    const wop = colOpt;
    const vShowAttr = isNotNullExpression(wop.hiddenCode)
      ? `v-if="${wop.name}Hidden"`
      : wop.hidden
      ? `v-if="false"`
      : '';
    return `<el-col ${vShowAttr} ${spanAttr} ${mdAttr} ${smAttr} ${xsAttr} ${offsetAttr} ${pushAttr} ${pullAttr} ${colClassAttr}>
    ${col.widgetList
      .map(cw => {
        if (cw.category === 'container') {
          return buildContainerWidget(cw, formConfig, designerConfig);
        } else {
          return buildFieldWidget(cw, formConfig, designerConfig);
        }
      })
      .join('')}
    </el-col>`;
  })
  .join('')}
</el-row>`;

    return gridTemplate;
  },

  table: (ctn, formConfig, designerConfig = {}) => {
    const tableClassAttr = buildClassAttr(ctn, 'table-layout');
    const wop = ctn.options;
    const vShowAttr = isNotNullExpression(wop.hiddenCode)
      ? `v-if="${wop.name}Hidden"`
      : wop.hidden
      ? `v-if="false"`
      : '';
    const tableTemplate = `<div class="table-container">
  <table ${vShowAttr} ${tableClassAttr}><tbody>
  ${ctn.rows
    .map(tr => {
      return `<tr>${tr.cols
        .filter(td => !td.merged)
        .map(td => {
          const tdOpt = td.options;
          const tdClassAttr = buildClassAttr(td, 'table-cell');
          const colspanAttr = !isNaN(tdOpt.colspan) && tdOpt.colspan !== 1 ? `colspan="${tdOpt.colspan}"` : '';
          const rowspanAttr = !isNaN(tdOpt.rowspan) && tdOpt.rowspan !== 1 ? `rowspan="${tdOpt.rowspan}"` : '';

          let tdStyleArray = [];
          !!tdOpt.cellWidth && tdStyleArray.push('width: ' + tdOpt.cellWidth + ' !important');
          !!tdOpt.cellHeight && tdStyleArray.push('height: ' + tdOpt.cellHeight + ' !important');
          let tdStyleAttr = tdStyleArray.length > 0 ? `style="${tdStyleArray.join(';')}"` : '';

          return `<td ${tdClassAttr} ${colspanAttr} ${rowspanAttr} ${tdStyleAttr}>${td.widgetList
            .map(tw => {
              if (tw.category === 'container') {
                return buildContainerWidget(tw, formConfig, designerConfig);
              } else {
                return buildFieldWidget(tw, formConfig, designerConfig);
              }
            })
            .join('')}
                    </td>`;
        })
        .join('')}</tr>`;
    })
    .join('')}
  </tbody></table>
</div>`;
    return tableTemplate;
  },

  tab: (ctn, formConfig, designerConfig = {}) => {
    const tabClassAttr = buildClassAttr(ctn);
    const wop = ctn.options;
    const vShowAttr = isNotNullExpression(wop.hiddenCode)
      ? `v-if="${wop.name}Hidden"`
      : wop.hidden
      ? `v-if="false"`
      : '';
    const vModel = ctn.tabs && ctn.tabs.length > 0 ? `v-model="${ctn.options.name}ActiveTab"` : '';
    const tabTemplate = `<div class="tab-container">
  <el-tabs ${vModel} ${tabClassAttr} ${vShowAttr}>
    ${ctn.tabs
      .map(tab => {
        const tabOpt = tab.options;
        const disabledAttr = tabOpt.disabled === true ? `disabled` : '';
        return `<el-tab-pane name="${tabOpt.name}" label="${tabOpt.label}" ${disabledAttr}>
        ${tab.widgetList
          .map(tw => {
            if (tw.category === 'container') {
              return buildContainerWidget(tw, formConfig, designerConfig);
            } else {
              return buildFieldWidget(tw, formConfig, designerConfig);
            }
          })
          .join('')}</el-tab-pane>`;
      })
      .join('')}
  </el-tabs>
</div>`;

    return tabTemplate;
  },

  'sub-form': (ctn, formConfig) => {
    //TODO:
  }
};

export function buildContainerWidget(widget, formConfig, designerConfig) {
  return containerTemplates[widget.type] ? containerTemplates[widget.type](widget, formConfig, designerConfig) : null;
}

export function getElAttrs(widget, formConfig) {
  //获取El组件属性
  let wop = widget.options;
  return {
    vModel: `v-model="${formConfig.modelName}.${wop.name}"`,
    vOn: wop.listener ? `v-on="${wop.name}Listener"` : '',
    readonly: wop.readonly ? `readonly="true"` : '',
    disabled: isNotNullExpression(wop.disabledCode)
      ? `:disabled="${wop.name}Disabled"`
      : wop.disabled
      ? `:disabled="true"`
      : widget.formItemFlag
      ? `:disabled="slotProps.disabled"`
      : '',
    size: wop.size ? `size="${wop.size}"` : '',
    type: wop.type ? `type="${wop.type === 'number' ? 'text' : wop.type}"` : '',
    showPassword: wop.showPassword ? `:show-password="${wop.showPassword}"` : '',
    placeholder: wop.placeholder ? `placeholder="${wop.placeholder}"` : '',
    rows: isNotNull(wop.rows) && !isNaN(wop.rows) ? `rows="${wop.rows}"` : '',
    clearable: wop.clearable ? 'clearable' : '',
    minlength: isNotNull(wop.minLength) && !isNaN(wop.minLength) ? `:minlength="${wop.minLength}"` : '',
    maxlength: isNotNull(wop.maxLength) && !isNaN(wop.maxLength) ? `:maxlength="${wop.maxLength}"` : '',
    showWordLimit: wop.showWordLimit ? `:show-word-limit="true"` : '',
    prefixIcon: wop.prefixIcon ? `prefix-icon="${wop.prefixIcon}"` : '',
    suffixIcon: wop.suffixIcon ? `suffix-icon="${wop.suffixIcon}"` : '',
    controlsPosition: wop.controlsPosition === 'right' ? `controls-position="right"` : '',
    min: isNotNull(wop.min) && !isNaN(wop.min) ? `:min="${wop.min}"` : '',
    max: isNotNull(wop.max) && !isNaN(wop.max) ? `:max="${wop.max}"` : '',
    precision: isNotNull(wop.precision) && !isNaN(wop.precision) ? `:precision="${wop.precision}"` : '',
    step: isNotNull(wop.step) && !isNaN(wop.step) ? `:step="${wop.step}"` : '',
    filterable: wop.filterable ? `filterable` : '',
    allowCreate: wop.allowCreate ? `allow-create` : '',
    defaultFirstOption: !!wop.filterable && !!wop.allowCreate ? `default-first-option` : '',
    multiple: wop.multiple ? `multiple` : '',
    multipleLimit: !isNaN(wop.multipleLimit) && wop.multipleLimit > 0 ? `:multiple-limit="${wop.multipleLimit}"` : '',
    automaticDropdown: wop.automaticDropdown ? `automatic-dropdown` : '',
    remote: wop.remote ? `remote` : '',
    format: wop.format ? `format="${wop.format}"` : '',
    valueFormat: wop.valueFormat ? `value-format="${wop.valueFormat}"` : '',
    editable: wop.editable ? `:editable="${wop.editable}"` : '',
    startPlaceholder: wop.startPlaceholder ? `start-placeholder="${wop.startPlaceholder}"` : '',
    endPlaceholder: wop.endPlaceholder ? `end-placeholder="${wop.endPlaceholder}"` : '',

    activeText: wop.activeText ? `active-text="${wop.activeText}"` : '',
    inactiveText: wop.inactiveText ? `inactive-text="${wop.inactiveText}"` : '',
    activeColor: wop.activeColor ? `active-color="${wop.activeColor}"` : '',
    inactiveColor: wop.inactiveColor ? `inactive-color="${wop.inactiveColor}"` : '',
    switchWidth: !isNaN(wop.switchWidth) && wop.switchWidth !== 40 ? `:width="${wop.switchWidth}"` : '',

    rateMax: !isNaN(wop.max) && wop.max !== 5 ? `:max="${wop.max}"` : '',
    lowThreshold: !isNaN(wop.lowThreshold) && wop.lowThreshold !== 2 ? `:low-threshold="${wop.lowThreshold}"` : '',
    highThreshold: !isNaN(wop.highThreshold) && wop.highThreshold !== 4 ? `:high-threshold="${wop.highThreshold}"` : '',
    allowHalf: wop.allowHalf ? `allow-half` : '',
    showText: wop.showText ? `show-text` : '',
    showScore: wop.showScore ? `show-score` : '',

    sliderMin: !isNaN(wop.min) && wop.min !== 0 ? `:min="${wop.min}"` : '',
    sliderMax: !isNaN(wop.max) && wop.max !== 100 ? `:max="${wop.max}"` : '',
    sliderStep: !isNaN(wop.step) && wop.step !== 1 ? `:step="${wop.step}"` : '',
    sliderRange: wop.range ? `range` : '',
    sliderVertical: wop.vertical ? `vertical` : '',

    uploadAction: wop.uploadURL ? `action="${wop.uploadURL}"` : '',
    withCredentials: wop.withCredentials ? `with-credentials` : '',
    multipleSelect: wop.multipleSelect ? `multiple` : '',
    showFileList: wop.showFileList ? `show-file-list` : '',
    limit: !isNaN(wop.limit) ? `:limit="${wop.limit}"` : '',
    uploadTipSlotChild: wop.uploadTip
      ? `<template #tip><div class="el-upload__tip">${wop.uploadTip}</div></template>`
      : '',
    pictureUploadIconChild: `<template #default><el-icon><Plus /></el-icon></template>`,
    fileUploadIconChild: `<template #default><el-link type="primary" icon="Plus"><span style="margin-left:4px;">请选择文件</span></el-link></template>`,

    buttonType: wop.type ? `type="${wop.type}"` : '',
    buttonPlain: wop.plain ? `plain` : '',
    buttonRound: wop.round ? `round` : '',
    buttonCircle: wop.circle ? `circle` : '',
    buttonIcon: wop.icon ? `icon="${wop.icon}"` : '',

    contentPosition:
      !!wop.contentPosition && wop.contentPosition !== 'center' ? `content-position="${wop.contentPosition}"` : '',

    appendButtonChild: wop.appendButton
      ? `<template #append><el-button class="${wop.buttonIcon}" ${
          wop.appendButtonDisabled ? 'disabled' : ''
        }></el-button></template>`
      : ''
  };
}

function buildRadioChildren(widget, formConfig) {
  let wop = widget.options;
  const childTag = wop.buttonStyle ? 'el-radio-button' : 'el-radio';
  const borderAttr = wop.border ? `border` : '';
  const styleAttr = `style="{display: ${wop.displayStyle}}"`;
  return `<${childTag} v-for="(item, index) in ${wop.name}Options" :key="index" :label="item.value"
          :disabled="item.disabled" ${borderAttr} ${styleAttr}>{{item.label}}</${childTag}>`;
}

function buildCheckboxChildren(widget, formConfig) {
  let wop = widget.options;
  const childTag = wop.buttonStyle ? 'el-checkbox-button' : 'el-checkbox';
  const borderAttr = wop.border ? `border` : '';
  const styleAttr = `style="{display: ${wop.displayStyle}}"`;
  return `<${childTag} v-for="(item, index) in ${wop.name}Options" :key="index" :label="item.value"
          :disabled="item.disabled" ${borderAttr} ${styleAttr}>{{item.label}}</${childTag}>`;
}

function buildSelectChildren(widget, formConfig) {
  let wop = widget.options;
  const childTag = 'el-option';
  return `<${childTag} v-for="(item, index) in ${wop.name}Options" :key="index" :label="item.label"
          :value="item.value" :disabled="item.disabled"></${childTag}>`;
}

const elTemplates = {
  //字段组件属性
  input: (widget, formConfig) => {
    const {
      vModel,
      vOn,
      readonly,
      disabled,
      size,
      type,
      showPassword,
      placeholder,
      clearable,
      minlength,
      maxlength,
      showWordLimit,
      prefixIcon,
      suffixIcon,
      appendButtonChild
    } = getElAttrs(widget, formConfig);
    return `<el-input ${vModel} ${vOn} ${readonly} ${disabled} ${size} ${type} ${showPassword} ${placeholder} ${clearable}
            ${minlength} ${maxlength} ${showWordLimit} ${prefixIcon} ${suffixIcon}>${appendButtonChild}</el-input>`;
  },

  textarea: (widget, formConfig) => {
    const {
      vModel,
      vOn,
      readonly,
      disabled,
      size,
      type,
      showPassword,
      placeholder,
      rows,
      clearable,
      minlength,
      maxlength,
      showWordLimit
    } = getElAttrs(widget, formConfig);
    return `<el-input type="textarea" ${vModel} ${vOn} ${readonly} ${disabled} ${size} ${type} ${showPassword} ${placeholder}
            ${rows} ${clearable} ${minlength} ${maxlength} ${showWordLimit}></el-input>`;
  },

  number: (widget, formConfig) => {
    const {
      vModel,
      vOn,
      disabled,
      size,
      type,
      showPassword,
      placeholder,
      controlsPosition,
      min,
      max,
      precision,
      step
    } = getElAttrs(widget, formConfig);
    return `<el-input-number ${vModel} ${vOn} class="full-width-input" ${disabled} ${size} ${type} ${showPassword}
            ${placeholder} ${controlsPosition} ${min} ${max} ${precision} ${step}></el-input-number>`;
  },

  radio: (widget, formConfig) => {
    const wop = widget.options;
    const { vModel, vOn, disabled, size } = getElAttrs(widget, formConfig);
    const radioOptions = buildRadioChildren(widget, formConfig);
    const optionsAttrs =
      wop.dataSource !== 'typeCode' && wop.optionItems && wop.optionItems.length > 0
        ? `:options = ${wop.name}Options`
        : '';
    const buttonStyleAttrs = wop.buttonStyle ? `childTag = "el-radio-button"` : `childTag = "el-radio"`;
    const borderAttr = wop.border ? `:childBorder="true"` : '';
    const displayStyleAttr =
      wop.displayStyle && wop.displayStyle === 'block' ? `:displayStyle="{display: '${wop.displayStyle}'}"` : '';
    const typeCodeAttrs = wop.dataSource === 'typeCode' && wop.typeCode ? `typeCode="${wop.typeCode}"` : '';
    return `<sfc-radio-group ${vModel} ${vOn} ${disabled} ${size} ${borderAttr} ${typeCodeAttrs} ${optionsAttrs} ${buttonStyleAttrs} ${displayStyleAttr}></sfc-radio-group>`;
  },

  checkbox: (widget, formConfig) => {
    const wop = widget.options;
    const { vModel, vOn, disabled, size } = getElAttrs(widget, formConfig);
    const checkboxOptions = buildCheckboxChildren(widget, formConfig);
    const optionsAttrs =
      wop.dataSource !== 'typeCode' && wop.optionItems && wop.optionItems.length > 0
        ? `:options = ${wop.name}Options`
        : '';
    const buttonStyleAttrs = wop.buttonStyle ? `childTag = "el-checkbox-button"` : `childTag = "el-checkbox"`;
    const borderAttr = wop.border ? `:childBorder="true"` : '';
    const displayStyleAttr =
      wop.displayStyle && wop.displayStyle === 'block' ? `:displayStyle="{display: '${wop.displayStyle}'}"` : '';
    const typeCodeAttrs = wop.dataSource === 'typeCode' && wop.typeCode ? `typeCode="${wop.typeCode}"` : '';
    let isLocal = true;
    let designerConfig = {};
    const designerConfigStr = localStorage.getItem('designerConfig');
    if (designerConfigStr) {
      try {
        designerConfig = JSON.parse(designerConfigStr);
        isLocal = designerConfig.hasModel;
      } catch {}
    }
    const isLocalAttrs = isLocal ? `:isLocal="true"` : '';
    const isStringValAttr = formConfig && formConfig.modelId ? `:isStringVal="true"` : '';
    return `<sfc-checkbox-group ${vModel} ${vOn} ${disabled} ${size} ${borderAttr} ${isLocalAttrs} ${isStringValAttr} ${typeCodeAttrs} ${optionsAttrs} ${buttonStyleAttrs} ${displayStyleAttr}></sfc-checkbox-group>`;
  },

  select: (widget, formConfig) => {
    const wop = widget.options;
    const {
      vModel,
      vOn,
      disabled,
      size,
      clearable,
      filterable,
      allowCreate,
      defaultFirstOption,
      automaticDropdown,
      multiple,
      multipleLimit,
      remote,
      placeholder
    } = getElAttrs(widget, formConfig);
    let isLocal = true;
    let designerConfig = {};
    const designerConfigStr = localStorage.getItem('designerConfig');
    if (designerConfigStr) {
      try {
        designerConfig = JSON.parse(designerConfigStr);
        isLocal = designerConfig.hasModel;
      } catch {}
    }
    const selectOptions = buildSelectChildren(widget, formConfig);
    const optionsAttrs =
      wop.dataSource !== 'typeCode' && wop.optionItems && wop.optionItems.length > 0
        ? `:options = ${wop.name}Options`
        : '';
    const typeCodeAttrs = wop.dataSource === 'typeCode' && wop.typeCode ? `typeCode="${wop.typeCode}"` : '';
    const isLocalAttrs = isLocal ? `:isLocal="true"` : '';
    return `<sfc-select ${vModel} ${vOn} class="full-width-input" ${disabled} ${size} ${clearable} ${filterable}
            ${allowCreate} ${defaultFirstOption} ${automaticDropdown} ${multiple} ${multipleLimit} ${placeholder}
            ${remote} ${typeCodeAttrs} ${isLocalAttrs} ${optionsAttrs}></sfc-select>`;
  },

  time: (widget, formConfig) => {
    const { vModel, vOn, readonly, disabled, size, placeholder, clearable, format, editable } = getElAttrs(
      widget,
      formConfig
    );
    return `<el-time-picker ${vModel} ${vOn} class="full-width-input" ${readonly} ${disabled} ${size} ${format}
            value-format="HH:mm:ss" ${placeholder} ${clearable} ${editable}></el-time-picker>`;
  },

  'time-range': (widget, formConfig) => {
    const { vModel, vOn, readonly, disabled, size, startPlaceholder, endPlaceholder, clearable, format, editable } =
      getElAttrs(widget, formConfig);
    return `<el-time-picker is-range ${vModel} ${vOn} class="full-width-input" ${readonly} ${disabled} ${size} ${format}
            value-format="HH:mm:ss" ${startPlaceholder} ${endPlaceholder} ${clearable} ${editable}></el-time-picker>`;
  },

  date: (widget, formConfig) => {
    const { vModel, vOn, readonly, disabled, size, type, placeholder, clearable, format, valueFormat, editable } =
      getElAttrs(widget, formConfig);
    return `<el-date-picker ${vModel} ${type} ${vOn} class="full-width-input" ${readonly} ${disabled} ${size} ${format}
              ${valueFormat} ${placeholder} ${clearable} ${editable}></el-date-picker>`;
  },

  'date-range': (widget, formConfig) => {
    const {
      vModel,
      vOn,
      readonly,
      disabled,
      size,
      type,
      startPlaceholder,
      endPlaceholder,
      clearable,
      format,
      valueFormat,
      editable
    } = getElAttrs(widget, formConfig);
    return `<el-date-picker is-range ${vModel} ${vOn} ${type} class="full-width-input" ${readonly} ${disabled} ${size} ${format}
            ${valueFormat} ${startPlaceholder} ${endPlaceholder} ${clearable} ${editable}></el-date-picker>`;
  },

  switch: (widget, formConfig) => {
    const { vModel, vOn, disabled, activeText, inactiveText, activeColor, inactiveColor, switchWidth } = getElAttrs(
      widget,
      formConfig
    );
    return `<el-switch ${vModel} ${vOn} ${disabled} ${activeText} ${inactiveText} ${activeColor} ${inactiveColor}
            ${switchWidth}></el-switch>`;
  },

  rate: (widget, formConfig) => {
    const { vModel, vOn, disabled, rateMax, lowThreshold, highThreshold, allowHalf, showText, showScore } = getElAttrs(
      widget,
      formConfig
    );
    return `<el-rate ${vModel} ${vOn} ${disabled} ${rateMax} ${lowThreshold} ${highThreshold} ${allowHalf}
            ${showText} ${showScore}></el-rate>`;
  },

  color: (widget, formConfig) => {
    const { vModel, vOn, disabled, size } = getElAttrs(widget, formConfig);
    return `<el-color-picker ${vModel} ${vOn} ${disabled} ${size}></el-color-picker>`;
  },

  slider: (widget, formConfig) => {
    const { vModel, vOn, disabled, sliderMin, sliderMax, sliderStep, sliderRange, sliderVertical } = getElAttrs(
      widget,
      formConfig
    );
    return `<el-slider ${vModel} ${vOn} ${disabled} ${sliderMin} ${sliderMax} ${sliderStep} ${sliderRange}
            ${sliderVertical}></el-slider>`;
  },

  'picture-upload': (widget, formConfig) => {
    const {
      vModel,
      disabled,
      uploadAction,
      withCredentials,
      multipleSelect,
      showFileList,
      limit,
      uploadTipSlotChild,
      pictureUploadIconChild
    } = getElAttrs(widget, formConfig);
    let wop = widget.options;
    let vOn = wop.listener ? `v-on="${wop.name}Listener"` : '';
    const isStringValAttr = formConfig && formConfig.modelId ? `:isStringVal="true"` : '';
    return `<sfc-upload ${vModel} ${isStringValAttr} ${
      wop.urlPrefix ? `urlPrefix="${wop.urlPrefix}"` : ''
    } :file-list="${wop.name}FileList" :headers="${wop.name}UploadHeaders" :data="${wop.name}UploadData" 
            ${disabled} ${uploadAction} list-type="picture-card" accept="${
      wop.fileTypes
    }" ${withCredentials} ${multipleSelect} ${showFileList} 
            ${limit} ${vOn}>${uploadTipSlotChild} ${pictureUploadIconChild}</sfc-upload>`;
  },

  'file-upload': (widget, formConfig) => {
    const {
      vModel,
      disabled,
      uploadAction,
      withCredentials,
      multipleSelect,
      showFileList,
      limit,
      uploadTipSlotChild,
      fileUploadIconChild
    } = getElAttrs(widget, formConfig);
    let wop = widget.options;
    let vOn = wop.listener ? `v-on="${wop.name}Listener"` : '';
    const isStringValAttr = formConfig && formConfig.modelId ? `:isStringVal="true"` : '';
    return `<sfc-upload ${vModel} ${isStringValAttr} ${
      wop.urlPrefix ? `urlPrefix="${wop.urlPrefix}"` : ''
    } :file-list="${wop.name}FileList" :headers="${wop.name}UploadHeaders" :data="${wop.name}UploadData" 
            ${disabled} ${uploadAction} list-type="text" accept="${
      wop.fileTypes
    }" ${withCredentials} ${multipleSelect} ${showFileList} 
            ${limit} ${vOn}>${uploadTipSlotChild} ${fileUploadIconChild}</sfc-upload>`;
  },

  'rich-editor': (widget, formConfig) => {
    const { vModel, vOn, disabled, placeholder } = getElAttrs(widget, formConfig);
    let wop = widget.options;
    let modeAttrs = `mode="${wop.mode ?? '1'}"`;
    return `<funi-editor :showMode="false" srcPrefix="/csccs/file/read?id=" ${modeAttrs} ${vModel} ${vOn} ${disabled} ${placeholder}></funi-editor>`;
  },

  cascader: (widget, formConfig) => {
    const { vModel, vOn, disabled, size, clearable, filterable, placeholder } = getElAttrs(widget, formConfig);
    let wop = widget.options;
    const optionsAttr = wop.dataSource !== 'api' ? `:options="${wop.name}Options"` : '';
    const apiUrlAttr = wop.dataSource === 'api' && wop.api_url ? `apiUrl="${wop.api_url}"` : ``;
    return `<sfc-cascader ${vModel} ${vOn} class="full-width-input" ${optionsAttr} ${apiUrlAttr} ${disabled} ${size} ${clearable}
            ${filterable} ${placeholder}></sfc-cascader>`;
  },

  'static-text': (widget, formConfig) => {
    return `<div>${widget.options.textContent}</div>`;
  },

  'html-text': (widget, formConfig) => {
    return `<div v-html="${widget.options.name}htmlContent"></div>`;
  },

  button: (widget, formConfig) => {
    const { buttonType, vOn, buttonPlain, buttonRound, buttonCircle, buttonIcon, disabled } = getElAttrs(
      widget,
      formConfig
    );
    return `<el-button ${buttonType} ${buttonPlain} ${buttonRound} ${buttonCircle} ${buttonIcon}
            ${disabled} ${vOn}>${widget.options.label}</el-button>`;
  },

  divider: (widget, formConfig) => {
    const { contentPosition } = getElAttrs(widget, formConfig);
    return `<el-divider direction="horizontal" ${contentPosition}></el-divider>`;
  },
  'sfc-draggable-curd': (widget, formConfig) => {
    return extGenerator.funiCurdTemplateGenerator(widget, formConfig);
  },
  'funi-label': (widget, formConfig) => {
    return extGenerator.funiLabelTemplateGenerator(widget, formConfig);
  },
  'funi-group-title': (widget, formConfig) => {
    return extGenerator.funiGroupTitleTemplateGenerator(widget, formConfig);
  },
  'sfc-user': (widget, formConfig) => {
    return extGenerator.sfcUserTemplateGenerator(widget, formConfig);
  },
  'sfc-org': (widget, formConfig) => {
    return extGenerator.sfcOrgTemplateGenerator(widget, formConfig);
  },
  'funi-select': (widget, formConfig) => {
    return extGenerator.funiSelectTemplateGenerator(widget, formConfig);
  },
  'funi-show-curd': (widget, formConfig) => {
    return extGenerator.funiShowCurdTemplateGenerator(widget, formConfig);
  },
  'sfc-file-table': (widget, formConfig) => {
    return extGenerator.sfcFileTableTemplateGenerator(widget, formConfig);
  },
  'sfc-gantt': (widget, formConfig) => {
    return extGenerator.sfcGanttTemplateGenerator(widget, formConfig);
  },
  'sfc-guid': (widget, formConfig) => {
    return extGenerator.sfcGuidTemplateGenerator(widget, formConfig);
  },
  'funi-region': (widget, formConfig) => {
    return extGenerator.funiRegionTemplateGenerator(widget, formConfig);
  },
  'sfc-operation-log': (widget, formConfig) => {
    return extGenerator.sfcOperationLogTemplateGenerator(widget, formConfig);
  },
  'sfc-funi-log': (widget, formConfig) => {
    return extGenerator.sfcFuniLogTemplateGenerator(widget, formConfig);
  },
  'sfc-iframe': (widget, formConfig) => {
    return extGenerator.sfcIframeTemplateGenerator(widget, formConfig);
  },
  'sfc-user-name': (widget, formConfig) => {
    return extGenerator.sfcUserNameTemplateGenerator(widget, formConfig);
  },
  'sfc-org-name': (widget, formConfig) => {
    return extGenerator.sfcOrgNameTemplateGenerator(widget, formConfig);
  },
  'funi-histogram-chart': (widget, formConfig) => {
    return extGenerator.funiHistogramChartTemplateGenerator(widget, formConfig);
  }
};

export function buildFieldWidget(widget, formConfig, designerConfig) {
  let wop = widget.options;
  const label = wop.labelHidden ? '' : wop.label;
  const labelWidthAttr = wop.labelHidden
    ? `label-width="0"`
    : wop.labelWidth
    ? `label-width="${wop.labelWidth}px"`
    : '';
  const labelTooltipAttr = wop.labelTooltip ? `title="${wop.labelTooltip}"` : '';
  const fieldTooltipAttr = wop.fieldTooltip ? `tooltip="${wop.fieldTooltip}"` : ''; //字段备注
  const propAttr = `prop="${wop.name}"`;

  let classArray = [];
  !!wop.required && classArray.push('required');
  !!wop.customClass && wop.customClass.length > 0 && classArray.push(wop.customClass.join(' '));
  if (wop.labelAlign) {
    wop.labelAlign !== 'label-left-align' && classArray.push(wop.labelAlign);
  } else if (widget.formItemFlag) {
    //classArray.push(formConfig.labelAlign || 'label-left-align')
    formConfig.labelAlign !== 'label-left-align' && classArray.push(formConfig.labelAlign);
  }
  if (!widget.formItemFlag) {
    classArray.push('static-content-item');
  }
  if (['funi-show-curd', 'funi-edit-curd', 'sfc-draggable-curd', 'sfc-workflow-log'].includes(widget.type)) {
    //两个列表不要默认的边框
    classArray.push('static-content-item');
  }
  const classAttr = classArray.length > 0 ? `class="${classArray.join(' ')}"` : '';

  let customLabelDom = `<template #label><span class="custom-label">${
    wop.labelIconPosition === 'front'
      ? wop.labelTooltip
        ? `<el-tooltip content="${wop.labelTooltip}" effect="light"><i class="${wop.labelIconClass}"></i></el-tooltip>${wop.label}`
        : `<i class="${wop.labelIconClass}"></i>${wop.label}`
      : wop.labelTooltip
      ? `${wop.label}<el-tooltip content="${wop.labelTooltip}" effect="light"><i class="${wop.labelIconClass}"></i></el-tooltip>`
      : `${wop.label}<i class="${wop.labelIconClass}"></i>`
  }
</span></template>`;
  !wop.labelIconClass && (customLabelDom = '');

  const fwDom = elTemplates[widget.type] ? elTemplates[widget.type](widget, formConfig, designerConfig) : null;
  const isFormItem = !!widget.formItemFlag;
  const vShowAttr = isNotNullExpression(wop.hiddenCode) ? `v-if="${wop.name}Hidden"` : wop.hidden ? `v-if="false"` : '';
  const widgetTypeAttr = widget.type ? `widgetType="${widget.type}"` : '';
  const vModel = `v-model="${formConfig.modelName}.${wop.name}"`;
  return isFormItem
    ? `<sfc-form-item label="${label}" :disabled="formDisabled" ${labelWidthAttr} ${labelTooltipAttr} ${fieldTooltipAttr} ${propAttr} ${widgetTypeAttr} ${vModel} ${classAttr} ${vShowAttr} v-slot="slotProps">
  ${customLabelDom}
  ${fwDom}
</sfc-form-item>`
    : `<div ${classAttr} ${vShowAttr}>${fwDom}</div>`;
}

function buildDialog(dialog, beautifier = null, cloneConfig = {}) {
  let config = JSON.parse(dialog.dialogConfig.configJson);
  let vOn = config.dialogConfig.listener ? `v-on="${dialog.name}Listener"` : '';
  let code = dialog.dialogConfig.code;
  let configDialog;
  try {
    configDialog = JSON.parse(dialog.dialogConfig.configJson);
  } catch (ex) {}
  if (configDialog && beautifier && cloneConfig && cloneConfig.appCode) {
    const sfcCode = genDialogSFC(
      configDialog.formConfig,
      configDialog.dialogConfig,
      configDialog.widgetList,
      beautifier,
      true,
      cloneConfig
    );
    const jsCode = compile(sfcCode, true);
    const SM_KEY = 'c8502bd294000ef49777ec31c54e6ebb';
    let jsStrBase64 = Base64.encode(jsCode);
    code = window.$utils.SM.sm4.encrypt(jsStrBase64, SM_KEY);
  }
  return `<sfc-dialog-render ref="${dialog.name}" code="${code}" ${vOn}></sfc-dialog-render>`;
}

function genTemplate(formConfig, widgetList, vue3Flag = false, beautifier = null, config = {}, designerConfig) {
  const submitAttr = vue3Flag ? `@submit.prevent` : `@submit.native.prevent`;
  let childrenList = [];
  let dialogList = [];
  widgetList.forEach(wgt => {
    if (wgt.category === 'container') {
      childrenList.push(buildContainerWidget(wgt, formConfig, designerConfig));
    } else {
      childrenList.push(buildFieldWidget(wgt, formConfig, designerConfig));
    }
  });

  if (formConfig && formConfig.dialogs && formConfig.dialogs.length) {
    formConfig.dialogs.forEach(dialog => {
      dialogList.push(buildDialog(dialog, beautifier, config));
    });
  }

  const formTemplate = `  <sfc-form :model="${formConfig.modelName}" ref="${formConfig.refName}" :rules="${
    formConfig.rulesName
  }"
    label-position="${formConfig.labelPosition}" label-width="${formConfig.labelWidth}px" size="${
    formConfig.size || 'default'
  }"
    ${submitAttr}>
  ${childrenList ? childrenList.join('\n') : ''}
  ${dialogList ? dialogList.join('\n') : ''}
</sfc-form>`;

  return formTemplate;
}

function genDialogTemplate(formConfig, dialogConfig, widgetList, vue3Flag = false, designerConfig) {
  const submitAttr = vue3Flag ? `@submit.prevent` : `@submit.native.prevent`;
  let childrenList = [];
  widgetList.forEach(wgt => {
    if (wgt.category === 'container') {
      childrenList.push(buildContainerWidget(wgt, formConfig, designerConfig));
    } else {
      childrenList.push(buildFieldWidget(wgt, formConfig, designerConfig));
    }
  });

  const titleAttr = dialogConfig.title ? `title="${dialogConfig.title}"` : '';
  const widthAttr = dialogConfig.width ? `width="${dialogConfig.width}"` : '';
  const buttonsAttr = `:buttons="${dialogConfig.name}Buttons"`;
  const formTemplate = `  <sfc-dialog ref="${dialogConfig.name}" ${titleAttr} ${widthAttr} :showClose="${
    dialogConfig.showClose
  }" ${buttonsAttr} :disabled="formDisabled" @close="handle${dialogConfig.name}Close">
    <sfc-form :model="${formConfig.modelName}" ref="${formConfig.refName}" :rules="${
    formConfig.rulesName
  }" :disabled="formDisabled"
        label-position="${formConfig.labelPosition}" label-width="${formConfig.labelWidth}px" size="${
    formConfig.size || 'default'
  }"
        ${submitAttr}>
      ${childrenList ? childrenList.join('\n') : ''}
    </sfc-form>
  </sfc-dialog>`;

  return formTemplate;
}

const genGlobalCSS = function (formConfig) {
  const globalCssTemplate = `  .el-input-number.full-width-input, .el-cascader.full-width-input, .el-select.full-width-input {
    width: 100% !important;
  }
  
  .el-form-item--medium {
    .el-radio {
      line-height: 36px !important;
    }
  
    .el-rate{
      margin-top: 8px;
    }
  }

  .el-form-item--small {
    .el-radio {
      line-height: 32px !important;
    }
  
    .el-rate{
      margin-top: 6px;
    }
  }

  .el-form-item--mini {
    .el-radio {
      line-height: 28px !important;
    }
  
    .el-rate{
      margin-top: 4px;
    }
  }
  
  .clear-fix:before, .clear-fix:after {
    display: table;
    content: "";
  }

  .clear-fix:after {
    clear: both;
  }

  .float-right {
    float: right;
  }

  .sfc-row {
    margin-top:-1px;
  }

${formConfig.cssCode}`;

  return globalCssTemplate;
};

const genScopedCSS = function (formConfig, vue3Flag = false) {
  //const vDeep = !!vue3Flag ? `::v-deep` : `:deep`
  const cssTemplate = `  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;
      
      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }
  
  div.tab-container {
  }
  
  .label-left-align ${vue3Flag ? `:deep(.el-form-item__label)` : `::v-deep .el-form-item__label`} {
    text-align: left;
  }

  .label-center-align ${vue3Flag ? `:deep(.el-form-item__label)` : `::v-deep .el-form-item__label`} {
    text-align: center;
  }

  .label-right-align ${vue3Flag ? `:deep(.el-form-item__label)` : `::v-deep .el-form-item__label`} {
    text-align: right;
  }

  .label-center-align ${vue3Flag ? `:deep(.el-form-item__label)` : `::v-deep .el-form-item__label`} {
    justify-content: center;
  }

  .label-right-align ${vue3Flag ? `:deep(.el-form-item__label)` : `::v-deep .el-form-item__label`} {
    justify-content: flex-end;
  }
  
  .custom-label {
  }

  .funi-group-title{

  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;
    ${vue3Flag ? `:deep(.el-divider--horizontal)` : `::v-deep .el-divider--horizontal`} {
      margin: 0;
    }
  }`;

  return cssTemplate;
};

/**
 * 注册容器组件的代码生成器
 * @param containerType 容器类型，必须唯一
 * @param ctGenerator 代码生成器函数，接收两个参数(containerWidget, formConfig)，返回生成的容器组件代码
 */
export const registerCWGenerator = function (containerType, ctGenerator) {
  containerTemplates[containerType] = ctGenerator;
};

/**
 * 注册字段组件的代码生成器
 * @param fieldType 字段类型，必须唯一
 * @param ftGenerator 代码生成器函数，接收两个参数(fieldWidget, formConfig)，返回生成的字段组件代码
 */
export const registerFWGenerator = function (fieldType, ftGenerator) {
  elTemplates[fieldType] = ftGenerator;
};

function getComProps(widget, config = {}, colProp = '') {
  let comProps;
  if (widget && widget.options) {
    if (widget.options.disabled === false && !widget.options.disabledCode) {
      //未禁用且没有表达式
      let disabledObj = {
        expression: `fieldAuthObj&&fieldAuthObj['${colProp}.${widget.options.name}']&&!fieldAuthObj['${colProp}.${widget.options.name}'].write`
      };
      widget.options.disabledCode = JSON.stringify(disabledObj);
    }
    const {
      label,
      name,
      defaultValue,
      hiddenCode,
      disabledCode,
      listeners,
      listColumnAlign,
      listColumnWidth,
      listColumnHidden,
      listColumnHiddenCode,
      listColumnWrap,
      listenerObjs,
      logicObjs,
      logic,
      ...props
    } = widget.options;
    comProps = { ...props, clearable: true };
    switch (widget.type) {
      case 'textarea':
        comProps.type = 'textarea';
        comProps.autosize = comProps.rows ? { minRows: comProps.rows } : true;
        comProps.inputStyle = { wordBreak: 'normal' };
        delete comProps.rows;
        break;
      case 'select':
        if (props.dataSource === 'custom') {
          comProps.options = props.optionItems;
        } else if (props.dataSource === 'typeCode') {
          comProps.typeCode = props.typeCode;
        }
        delete comProps.dataSource;
        break;
      case 'funi-select':
        if (comProps.modelOption) {
          const { typeCode, selectOptions, modelOption, ...other } = props;
          if (other && other.multiple) {
            other.isStringVal = true;
          }
          comProps = { ...other };
          if (modelOption.type === 'custom') {
            comProps.options = selectOptions;
          } else if (modelOption.type === 'typeCode') {
            comProps.typeCode = typeCode;
            comProps.isLocal = true;
          } else if (modelOption.type === 'model') {
            let asUrl = `/as/${config.appCode}/model/dicListV2`;
            comProps.model_id = modelOption.modelId;
            comProps.auto = false;
            comProps.params = [];
            let paramsList = [];
            if (modelOption.data && modelOption.data.filterParams && modelOption.data.filterParams.length > 0) {
              comProps.auto = true;
              modelOption.data.filterParams.forEach(p => {
                let conditions = [];
                if (p.conditions && p.conditions.length > 0) {
                  p.conditions.forEach(c => {
                    const expression = getExpressionCode(c.value);
                    let condNew = {
                      logicalOperator: c.logicalOperator,
                      key: c.key,
                      operator: c.operator,
                      value: expression ? `@${expression}@` : ''
                    };
                    if (c.tableName) {
                      condNew.tableName = c.tableName;
                      condNew.joinFieldName = c.joinFieldName;
                    }
                    conditions.push(condNew);
                  });
                }
                paramsList.push({ logicalOperator: p.logicalOperator, conditions });
              });
            }
            if (modelOption.data) {
              let sort = modelOption.data && modelOption.data.sortFields ? modelOption.data.sortFields : [];
              comProps.requestParams = {
                asUrl,
                model_id: modelOption.modelId,
                component_id: modelOption.data.component_id,
                params: paramsList,
                labelKey: modelOption.data.labelKey,
                valueKey: modelOption.data.valueKey
              };
              if (modelOption.data.sortFields && modelOption.data.sortFields.length > 0) {
                comProps.requestParams.sort = sort;
              }
            }
          } else if (modelOption.type === 'api') {
            let asUrl = `/as/${config.appCode}/api/list`;
            let url = modelOption.data.api_url;
            let method = modelOption.data.method ? modelOption.data.method : 'post';
            let paramsStr = `@{}@,`;
            let origin = modelOption.data.apiType;
            if (modelOption.data.requestOtherParams) {
              let params = modelOption.data.requestOtherParams;
              let paramsList = [];
              if (params && params.length > 0) {
                params.forEach(p => {
                  if (p.type === 'variable' || !p.type) {
                    const expression = getExpressionCode(p.expression);
                    if (expression) {
                      paramsList.push(`${p.name}:${expression}`);
                    }
                  } else if (p.type === 'string') {
                    paramsList.push(`${p.name}:'${p.expression}'`);
                  } else {
                    if (p.expression) {
                      paramsList.push(`${p.name}:${p.expression}`);
                    }
                  }
                });
              }
              paramsStr = `@{${paramsList.join(',')}}@`;
            }
            let headersStr = `@{}@`;
            if (modelOption.data.headers) {
              let headers = modelOption.data.headers;
              let headersList = [];
              if (headers && headers.length > 0) {
                headers.forEach(h => {
                  if (h.type === 'variable' || !h.type) {
                    const expression = getExpressionCode(h.expression);
                    if (expression) {
                      headersList.push(`${h.name}:${expression}`);
                    }
                  } else if (h.type === 'string') {
                    headersList.push(`${h.name}:'${h.expression}'`);
                  } else {
                    if (h.expression) {
                      headersList.push(`${h.name}:${h.expression}`);
                    }
                  }
                });
              }
              headersStr = `@{${headersList.join(',')}}@`;
            }
            comProps.requestParams = {
              asUrl,
              url,
              method,
              origin,
              headers: headersStr,
              params: paramsStr
            };
          } else if (modelOption.type === 'sql') {
            let asUrl = `/as/${config.appCode}/sql/list`;
            let sql_id = modelOption.data.id;
            let paramsStr = `@{}@,`;
            if (modelOption.data.requestOtherParams) {
              let params = modelOption.data.requestOtherParams;
              let paramsList = [];
              if (params && params.length > 0) {
                params.forEach(p => {
                  if (p.type === 'variable' || !p.type) {
                    const expression = getExpressionCode(p.expression);
                    if (expression) {
                      paramsList.push(`${p.name}:${expression}`);
                    }
                  } else if (p.type === 'string') {
                    paramsList.push(`${p.name}:'${p.expression}'`);
                  } else {
                    if (p.expression) {
                      paramsList.push(`${p.name}:${p.expression}`);
                    }
                  }
                });
              }
              paramsStr = `@{${paramsList.join(',')}}@`;
            }
            comProps.requestParams = {
              asUrl,
              sql_id,
              params: paramsStr
            };
          }
        }
        break;
      case 'radio':
      case 'checkbox':
        if (props.dataSource) {
          const { dataSource, typeCode, optionItems, modelOption, ...other } = props;
          comProps = { ...other };
          if (dataSource === 'custom') {
            comProps.options = optionItems;
          } else if (dataSource === 'typeCode') {
            comProps.typeCode = typeCode;
            comProps.isLocal = true;
          }
        }
        break;
      case 'time':
        comProps['value-format'] = 'HH:mm:ss';
        break;
      case 'time-range':
        comProps['value-format'] = 'HH:mm:ss';
        comProps['is-range'] = true;
        break;
      case 'date-range':
        comProps['is-range'] = true;
        break;
      case 'picture-upload':
        comProps['list-type'] = 'picture-card';
        break;
      default:
        break;
    }
    //处理默认值
    if (isNotNull(defaultValue)) {
      let expression = getExpressionCode(defaultValue);
      if (expression) {
        comProps.modelValue = `@${expression}@`;
      }
    }
    //处理隐藏
    if (isNotNull(hiddenCode)) {
      let expression = getExpressionCode(hiddenCode);
      if (expression) {
        comProps.hidden = `@${expression}@`;
      }
    }
    //处理不可用
    if (isNotNull(disabledCode)) {
      let expression = getExpressionCode(disabledCode);
      if (expression) {
        comProps.disabled = `@${expression}@`;
      }
    }
  }
  //删除没有值的多余属性
  let delKey = [];
  for (let key in comProps) {
    if (isNull(comProps[key]) || (typeof comProps[key] === 'string' && isEmptyStr(comProps[key]))) {
      delKey.push(key);
    }
  }
  if (delKey && delKey.length > 0) {
    delKey.forEach(item => {
      delete comProps[item];
    });
  }
  return comProps;
}

function convertWidget(widget, config = {}, formConfig = {}) {
  let newWidget;
  if (widget.type === 'sfc-draggable-curd') {
    let tempWidget = deepClone(widget);
    let columns = [];
    if (tempWidget.widgetList && tempWidget.widgetList.length > 0) {
      tempWidget.widgetList
        .filter(item => !!item.options)
        .forEach(item => {
          const column = {
            label: item.options.label,
            prop: item.options.name,
            align: item.options.listColumnAlign,
            width: item.options.listColumnWidth ? `${item.options.listColumnWidth}px` : undefined,
            validationHint: item.options.validationHint,
            wrap: item.options.listColumnWrap,
            component: typeToComponent(item.type)
          };
          if (item.options.listColumnHiddenCode) {
            let columnHiddenExp = getExpressionCode(item.options.listColumnHiddenCode);
            let hiddenFun = `()=>{
              let authHidden=!((fieldAuthObj&&fieldAuthObj['${widget.options.name}.${item.options.name}']&&fieldAuthObj['${widget.options.name}.${item.options.name}'].read)||!fieldAuthObj||!fieldAuthObj['${widget.options.name}.${item.options.name}']);
              let expHidden=${columnHiddenExp};
              return authHidden||expHidden;
            }`;
            let realHiddenFun = lowcode.injectContext(hiddenFun, true, formConfig.modelName);
            column.hidden = `@computed(${realHiddenFun})@`;
          } else if (!item.options.listColumnHidden) {
            column.hidden = `@computed(()=>{return !((fieldAuthObj&&fieldAuthObj['${widget.options.name}.${item.options.name}']&&fieldAuthObj['${widget.options.name}.${item.options.name}'].read)||!fieldAuthObj||!fieldAuthObj['${widget.options.name}.${item.options.name}']);})@`;
          } else {
            column.hidden = true;
          }
          column.validator = [];
          if (item.options && item.options.required) {
            column.required = true;
            column.validator.push('^\\S{1}[\\s\\S]*$');
          } else {
            column.required = `@computed(()=>{return fieldAuthObj&&fieldAuthObj['${widget.options.name}.${item.options.name}']&&fieldAuthObj['${widget.options.name}.${item.options.name}'].required;})@`;
          }
          if (item.options && item.options.validation) {
            column.validator.push(getRegExp(item.options.validation));
          }
          let comProps = getComProps(item, config, widget.options.name);
          if (comProps.clearable) {
            //先屏蔽清除
            // comProps.clearable = false;
          }
          if (comProps) {
            column.comProps = JSON.stringify({ expression: JSON.stringify(comProps) });
          }
          columns.push(column);
        });
    }
    delete tempWidget.widgetList;
    delete tempWidget.category;
    tempWidget.type = 'funi-edit-curd';
    tempWidget.icon = 'edit-curd';
    tempWidget.formItemFlag = true;
    tempWidget.options.columns = columns;
    newWidget = { ...tempWidget };
  }
  return newWidget;
}

/**
 * 转换组件配置
 * @param {*} widgetList
 */
function convertWidgetList(originWidgetList, config = {}, formConfig = {}) {
  let widgetList = deepClone(originWidgetList);
  let newWidgetList = [];
  if (widgetList && widgetList.length > 0) {
    for (let i = 0; i < widgetList.length; i++) {
      if (widgetList[i].widgetList && widgetList[i].widgetList.length > 0) {
        widgetList[i].widgetList = convertWidgetList(widgetList[i].widgetList, config, formConfig);
      }
      if (widgetList[i].rows && widgetList[i].rows.length > 0) {
        widgetList[i].rows = convertWidgetList(widgetList[i].rows, config, formConfig);
      }
      if (widgetList[i].cols && widgetList[i].cols.length > 0) {
        widgetList[i].cols = convertWidgetList(widgetList[i].cols, config, formConfig);
      }
      if (widgetList[i].tabs && widgetList[i].tabs.length > 0) {
        widgetList[i].tabs = convertWidgetList(widgetList[i].tabs, config, formConfig);
      }
      if (widgetList[i].type === 'sfc-draggable-curd') {
        const newWidget = convertWidget(widgetList[i], config, formConfig);
        newWidgetList.push(newWidget);
      } else {
        newWidgetList.push(widgetList[i]);
      }
    }
  }
  return newWidgetList;
}

export const genSFC = function (
  formConfig,
  widgetList,
  beautifier,
  vue3Flag = false,
  config = {},
  designerConfig = {}
) {
  let newWidgetList = convertWidgetList(widgetList, config, formConfig);
  const html = beautifier.html(
    genTemplate(formConfig, newWidgetList, vue3Flag, beautifier, config, designerConfig),
    beautifierOpts.html
  );
  const js = beautifier.js(
    vue3Flag
      ? genVue3JS(formConfig, newWidgetList, null, config, designerConfig)
      : genVue2JS(formConfig, newWidgetList),
    beautifierOpts.js
  );
  const globalCss = beautifier.css(genGlobalCSS(formConfig), beautifierOpts.css);
  const scopedCss = beautifier.css(genScopedCSS(formConfig, vue3Flag), beautifierOpts.css);

  return `<!-- 
Codes Generated By FuniFormEngine:
-->
<template>
${html}
</template>

<script>
${js}
</script>

<style lang="scss">
${globalCss}
</style>

<style lang="scss" scoped>
${scopedCss}
</style>`;
};

export const genDialogSFC = function (
  formConfig,
  dialogConfig,
  widgetList,
  beautifier,
  vue3Flag = false,
  config = {},
  designerConfig = {}
) {
  let newWidgetList = convertWidgetList(widgetList);
  const html = beautifier.html(
    genDialogTemplate(formConfig, dialogConfig, newWidgetList, vue3Flag, designerConfig),
    beautifierOpts.html
  );
  const js = beautifier.js(
    vue3Flag
      ? genVue3JS(formConfig, newWidgetList, dialogConfig, config, designerConfig)
      : genVue2JS(formConfig, newWidgetList),
    beautifierOpts.js
  );
  const globalCss = beautifier.css(genGlobalCSS(formConfig), beautifierOpts.css);
  const scopedCss = beautifier.css(genScopedCSS(formConfig, vue3Flag), beautifierOpts.css);

  return `<!-- 
Codes Generated By FuniFormEngine:
-->
<template>
${html}
</template>

<script>
${js}
</script>

<style lang="scss">
${globalCss}
</style>

<style lang="scss" scoped>
${scopedCss}
</style>`;
};
