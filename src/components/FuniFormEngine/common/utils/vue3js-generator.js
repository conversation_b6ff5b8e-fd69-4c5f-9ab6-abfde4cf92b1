/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-24 14:06:14
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-21 16:20:44
 * @FilePath: \src\components\FuniFormEngine\common\utils\vue3js-generator.js
 * @Description:
 */
import {
  buildActiveTabs,
  buildDefaultValueListFn,
  buildFuniVariableFn,
  buildFieldOptionsFn,
  buildRulesListFn,
  buildUploadDataFn,
  buildHtmlDataFn,
  buildListenerFn,
  buildColumnsFn,
  buildButtonsFn,
  buildSearchConfigGroupFn,
  buildActionsFn,
  buildAllowedScopeFn,
  buildrequestOtherParamFn,
  buildModelFieldWatchFn,
  buildDialogButtonsFn,
  buildCurdValidatorFn,
  buildFileTableValidatorFn,
  buildDialogListenerFn,
  buildFuniSelectParamsFn,
  buildFormValidatorFn,
  buildGanttConverterFn,
  buildCustomRenderFn,
  buildGanttTooltipTextFn,
  buildDisabledAttrsFn,
  buildHiddenAttrsFn,
  buildSrcAttrsFn,
  buildLabelPropListFn,
  buildLifeCycleFn,
  buildMapModalRenderFn,
  buildMapLegendShowFuncFn,
  buildMapDrawCallbackFn,
  buildMapLegendListFuncFn
} from './vue2js-generator';
import { traverseFieldWidgets, findAllInitLength, getExpressionCode } from './util';
import lowCode from './lowcode';

export const genVue3JS = function (formConfig, widgetList, dialogConfig = null, config = {}, designerConfig = {}) {
  let defaultValueList = [];
  let defaultValueWatchs = [];
  let funiVariableList = [];
  let rulesList = [];
  let fieldOptions = [];
  let uploadData = [];
  let htmlData = [];
  let listeners = [];
  let columnsData = [];
  let actionsData = [];
  let buttonsData = [];
  let searchConfigGroup = [];
  let requestOtherParamData = [];
  let curdValidator = [];
  let fileTableValidator = [];
  let dialogMethods = '';
  let dialogExpose = '';
  let dialogListener = [];
  let funiSelectParams = [];
  let allowedScopeData = [];
  let modelFieldWatch = [];
  let formValidator = [];
  let ganttConverters = [];
  let ganttTooltipTexts = [];
  let disabledAttrs = [];
  let hiddenAttrs = [];
  let srcAttrs = [];
  let labelProps = [];
  let mapFunList = [];
  let customRenders = [];
  let inputConvert = '';
  let validateFun = '';
  let outputConvert = '';
  let beforeSubmit = '';
  let afterSave = '';
  let afterSubmit = '';
  let getCustomAssigneeList = '';
  let busExpose = '';
  let workflowVarFunStr = '';
  traverseFieldWidgets(widgetList, (widget, parent, isContainer) => {
    if (!isContainer) {
      buildDefaultValueListFn(formConfig, widgetList, defaultValueList, defaultValueWatchs)(widget);
      buildFuniVariableFn('mapId', 'MapId', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('multipleLimit', 'MultipleLimit', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('checkLimit', 'CheckLimit', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('baseLayers', 'BaseLayers', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('layers', 'Layers', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('layersRequest', 'LayersRequest', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('drawStyle', 'DrawStyle', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('modeValueMap', 'ModeValueMap', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('boundAreaList', 'BoundAreaList', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('boundAreaRequest', 'BoundAreaRequest', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('boundAreaStyle', 'BoundAreaStyle', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('layerAuthHeader', 'LayerAuthHeader', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('highLightStyle', 'HighLightStyle', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('fitParams', 'FitParams', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('showDrawCode', 'ShowDraw', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('drawToolCode', 'DrawTool', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('funiVideoStyle', 'FuniVideoStyle', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('treeStyle', 'TreeStyle', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('urlCode', 'VideoUrl', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('dhConfig', 'DhConfig', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('dataTree', 'DataTree', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('dataTreeVideoUrl', 'DataTreeVideoUrl', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('treeApiParams', 'TreeApiParams', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('customTreeApi', 'CustomTreeApi', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('getVideoApi', 'GetVideoApi', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('operateCamera', 'OperateCamera', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('operateDirectApi', 'operateDirectApi', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('defaultTreeProps', 'defaultTreeProps', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('legendShowFunc', 'LegendShowFunc', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('hideAddBtnCode', 'HideAddBtn', formConfig, widgetList, funiVariableList)(widget);
      buildFuniVariableFn('attrs', 'Attrs', formConfig, widgetList, funiVariableList)(widget);
      buildRulesListFn(formConfig, widgetList, rulesList)(widget);
      buildFieldOptionsFn(formConfig, widgetList, fieldOptions)(widget);
      buildFuniSelectParamsFn(formConfig, widgetList, config, funiSelectParams, designerConfig)(widget);
      buildUploadDataFn(formConfig, widgetList, uploadData)(widget);
      buildHtmlDataFn(formConfig, widgetList, htmlData)(widget);
      buildListenerFn(formConfig, widgetList, listeners)(widget);
      buildColumnsFn(formConfig, widgetList, columnsData)(widget);
      buildActionsFn(formConfig, widgetList, actionsData)(widget);
      buildButtonsFn(formConfig, widgetList, buttonsData)(widget);
      buildSearchConfigGroupFn(formConfig, widgetList, searchConfigGroup)(widget);
      buildAllowedScopeFn(formConfig, widgetList, allowedScopeData)(widget);
      buildrequestOtherParamFn(formConfig, widgetList, requestOtherParamData)(widget);
      buildCurdValidatorFn(formConfig, widgetList, curdValidator)(widget);
      buildFileTableValidatorFn(formConfig, widgetList, fileTableValidator)(widget);
      buildGanttConverterFn(formConfig, widgetList, ganttConverters)(widget);
      buildMapModalRenderFn(formConfig, widgetList, mapFunList)(widget);
      buildMapLegendShowFuncFn(formConfig, widgetList, mapFunList)(widget);
      buildMapDrawCallbackFn(formConfig, widgetList, mapFunList)(widget);
      buildMapLegendListFuncFn(formConfig, widgetList, mapFunList)(widget);
      buildGanttTooltipTextFn(formConfig, widgetList, ganttTooltipTexts)(widget);
      buildDisabledAttrsFn(formConfig, widgetList, disabledAttrs)(widget);
      buildSrcAttrsFn(formConfig, widgetList, srcAttrs)(widget);
      buildLabelPropListFn(formConfig, widgetList, labelProps)(widget);
      buildCustomRenderFn(formConfig, widgetList, customRenders)(widget);
    }
    buildHiddenAttrsFn(formConfig, widgetList, hiddenAttrs)(widget);
  });
  buildDialogListenerFn(dialogListener)(formConfig);
  buildModelFieldWatchFn(formConfig, widgetList, config)(modelFieldWatch);
  formValidator = buildFormValidatorFn(formConfig);
  if (dialogConfig) {
    buildDialogButtonsFn(buttonsData)(dialogConfig, formConfig);
    dialogMethods = `
    const show=(data=null,attach = true)=>{
      instance.proxy.$refs['${dialogConfig.name}'].show();
      if(data){
        setFormData(data,attach);
      }
    }
    const close=()=>{
      instance.proxy.$refs['${dialogConfig.name}'].close();
    }
    const print=()=>{
      instance.proxy.$refs['${formConfig.refName}'].print();
    }
    const handle${dialogConfig.name}Close=(e)=>{
      let formData={...(instance.proxy.formData||{})};
      instance.proxy.$emit('dialogClose',formData);
    }
    `;
    dialogExpose = `,
    handle${dialogConfig.name}Close,
    show,
    close,
    print`;
  }
  const activeTabs = buildActiveTabs(formConfig, widgetList);

  let formWatch = '';
  if (formConfig.watch) {
    let watchStr = lowCode.injectForObjStr(`{${formConfig.watch}}`, false, formConfig.modelName, true);
    formWatch = watchStr.substring(1, watchStr.length - 1);
  }
  inputConvert = buildLifeCycleFn(formConfig, 'inputFormData', 'inputConvert');
  validateFun = buildLifeCycleFn(formConfig, 'formValite', 'validateFun');
  outputConvert = buildLifeCycleFn(formConfig, 'outputFormData', 'outputConvert');
  beforeSubmit = buildLifeCycleFn(formConfig, 'beforeSubmit', 'beforeSubmit');
  afterSave = buildLifeCycleFn(formConfig, 'afterSave', 'afterSave');
  afterSubmit = buildLifeCycleFn(formConfig, 'afterSubmit', 'afterSubmit');
  getCustomAssigneeList = buildLifeCycleFn(formConfig, 'getCustomAssigneeList', 'getCustomAssigneeList');

  if (formConfig.workflowOrgId || formConfig.workflowUserId || formConfig.workflowUserName || getCustomAssigneeList) {
    let workflowOrgIdCode = getExpressionCode(formConfig.workflowOrgId);
    let workflowUserIdCode = getExpressionCode(formConfig.workflowUserId);
    let workflowUserNameCode = getExpressionCode(formConfig.workflowUserName);
    let varList = [];
    let isFormWorkflowUser = false; //是否是表单工作流用户
    if (workflowOrgIdCode) {
      varList.push(`workflowOrgId:${workflowOrgIdCode}`);
    }
    if (workflowUserIdCode) {
      isFormWorkflowUser = true;
      varList.push(`workflowUserId:${workflowUserIdCode}`);
    }
    if (workflowUserNameCode) {
      isFormWorkflowUser = true;
      varList.push(`workflowUserName:${workflowUserNameCode}`);
    }
    if (isFormWorkflowUser || (varList && varList.length > 0 && !getCustomAssigneeList)) {
      workflowVarFunStr = `const getWorkflowVar=${lowCode.injectContext(
        `()=>{
      return {
        ${varList.join(',')}
      }
    }`,
        true,
        formConfig.modelName
      )}`;
      busExpose = `,getWorkflowVar`;
    } else if (varList && varList.length > 0 && getCustomAssigneeList) {
      workflowVarFunStr = `const getWorkflowVar=(isOrg=false)=>{
        if (isOrg) {
           const getOrgWorkflowVar=${lowCode.injectContext(
             `()=>{
              return {
                ${varList.join(',')}
              }
            }`,
             true,
             formConfig.modelName
           )}
          return getOrgWorkflowVar()
        } else {
          let customAssigneeList=[];
          if(getCustomAssigneeList){
            customAssigneeList=getCustomAssigneeList();
          }
          return customAssigneeList
        }
      }`;
      busExpose = `,getWorkflowVar`;
    } else {
      workflowVarFunStr = `const getWorkflowVar=()=>{
        let customAssigneeList=[];
        if(getCustomAssigneeList){
          customAssigneeList=getCustomAssigneeList();
        }
        return customAssigneeList
      }`;
      busExpose = `,getWorkflowVar`;
    }
  }
  let initLength = findAllInitLength(widgetList);
  let frontCode;
  if (formConfig.frontCode) {
    frontCode = formConfig.frontCode.replace(/\/\/.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, ''); // 移除所有注释
  }
  const v3JSTemplate = `  import { defineComponent, toRefs, ref, h, reactive, getCurrentInstance, resolveComponent,provide } from 'vue';
  ${
    frontCode
      ? `const createExtendForm = function(context){
  ${frontCode}
  return new ExtendForm();
  };`
      : ''
  }

  export default defineComponent({
    components: {},
    props: {
      headers: {
        type: Object
      },
      disabled:{
        type:Boolean,
        default:false
      }
    },
    setup(props) {
      const { headers } = props;
      const fieldAuthObj = inject('fieldAuthObj')||{};
      const tempData = inject('tempData')||{};
      const pageGetExtendParams=inject('getExtendParams');
      const getExtendParams=(id,context={})=>{
        let extData={};
        if(pageGetExtendParams){
          extData=pageGetExtendParams(id,context);
        }
        return extData;
      }
      let app_code,page_id;
      let initStatus = 0;
      let maxStatus = ${initLength};
      let hash = window.location.hash;
      if (hash && hash.length > 0) {
        hash = window.location.hash.split('?')[0];
        let hashList = hash.split('/');
        if (hashList && hashList.length >= 3) {
          page_id = hashList[hashList.length - 1];
          app_code = hashList[hashList.length - 2];
        }
      }
      let innerChange=false;
      const state = reactive({
        pageData:{},

        searchData:{},

        handler:{},
        
        formDisabled:props.disabled,

        ${formConfig.modelName}: {
          ${defaultValueList.join('\n')}
        }
      })

      const instance = getCurrentInstance();
      const localQuery=JSON.parse(JSON.stringify(instance.proxy.$route.query||{}));
      const context={
        router:instance.proxy.$router,
        route:instance.proxy.$route,
        query:localQuery,
        emit:instance.proxy.$emit,
        message:instance.proxy.$message,
        notify:instance.proxy.$notify,
        alert:instance.proxy.$alert,
        loading:instance.proxy.$loading,
        showSfcUpload:instance.proxy.$showSfcUpload,
        showLog:instance.proxy.$showLog
      };

      const attrsState=reactive({
        ${formConfig.rulesName}: {
          ${rulesList.join('\n')}
        },

        ${funiVariableList.join('\n')}

        ${activeTabs.join('\n')}
        
        ${fieldOptions.join('\n')}

        ${funiSelectParams.join('\n')}
        
        ${uploadData.join('\n')}

        ${htmlData.join('\n')}

        ${columnsData.join('\n')}

        ${actionsData.join('\n')}

        ${buttonsData.join('\n')}

        ${searchConfigGroup.join('\n')}

        ${allowedScopeData.join('\n')}

        ${requestOtherParamData.join('\n')}

        ${disabledAttrs.join('\n')}

        ${hiddenAttrs.join('\n')}

        ${srcAttrs.join('\n')}

        ${listeners.join('\n\n')}

        ${dialogListener.join('\n\n')}

        ${ganttConverters.join('\n\n')}

        ${customRenders.join('\n\n')}

         ${mapFunList.join('\n\n')}

        ${ganttTooltipTexts.join('\n\n')}
      });
      ${defaultValueWatchs.join('\n\n')}
      let inputConvert,validateFun,outputConvert,beforeSubmit,afterSave,afterSubmit,getCustomAssigneeList;
      ${inputConvert}
      ${validateFun}
      ${outputConvert}
      ${afterSave}
      ${beforeSubmit}
      ${afterSubmit}
      ${getCustomAssigneeList}
      ${workflowVarFunStr}
      onMounted(()=>{
        setTimeout(() => {
          Object.assign(context,{
            data:state.pageData,
            handler:state.handler,
            refs:instance.proxy.$refs,
            formData:state.${formConfig.modelName}
            })
        }, 100);
      });

      const submitForm = async () => {
        return new Promise(resovle=>{
          instance.proxy.$refs['${formConfig.refName}'].validate(async (valid,errors) => {
            if (!errors) {
              errors={};
            }
            try{
            ${curdValidator.join('\n\n')}
            ${formValidator.join('\n\n')}
            ${fileTableValidator.join('\n\n')}
            } catch{}
            if(valid&&validateFun){
              let data=state.${formConfig.modelName};
              valid= await validateFun(data);
            }
            if (valid) {
              let data=state.${formConfig.modelName};
              if(outputConvert){
                data=outputConvert(data);
              }
              resovle({valid,errors,data});
            } else {
              resovle({valid,errors});
            }
          })
        })
      }
      
      const resetForm = () => {
        innerChange=true;
        instance.proxy.$refs['${formConfig.refName}'].resetFields();
        nextTick(()=>{
          innerChange=false;
        })
      }
      
      const setFormData = async (data,attach = true)=> {
        innerChange=true;
        let realData = data||{};
        if(inputConvert){
          realData= await inputConvert(realData);
        }
        let newData=realData||{};
        if(attach){
          Object.assign(state.${formConfig.modelName},newData);
        } else {
          for(let key in state.${formConfig.modelName}){
            if(Object.keys(newData).indexOf(key)>=0){
              state.${formConfig.modelName}[key]=newData[key];
            }
          }
        }
        nextTick(()=>{
          changeInitStatus();
        })
      }

      const changeInitStatus = ()=>{
        initStatus++;
        if(initStatus>=maxStatus){
          innerChange=false;
        }
      }

      const setFormDisabled=(disabled)=>{
        state.formDisabled = disabled;
      }

      const setFormExtraData=(currentData,config)=>{
        let realData = currentData || {};
        if (inputConvert) {
          realData = inputConvert(realData)
        }
        instance.proxy.$refs['${formConfig.refName}'].setFormExtraData(realData,config);
      }
      const selectOption={};

      const handleChangeOptions=(prop,newOptions)=>{
        if(prop&&newOptions){
          if(!selectOption[prop]){
            selectOption[prop]={}
          }
          if(newOptions.isFirstChangeOptions){
            selectOption[prop].first=newOptions.options;
          }
          selectOption[prop].current=newOptions.options;
        }
      }

      const getOperateRemarkData=()=>{
        return {
          selectOption,
          labelProps:{
            ${labelProps.join(',')}
          }
        };
      }

      const exeChildMethod=(refName,methodName,params)=>{
        if(instance.proxy.$refs[refName]&&instance.proxy.$refs[refName][methodName]){
          return instance.proxy.$refs[refName][methodName](params);
        } else {
          return Promise.reject('未找到方法');
        }
      }

      ${dialogMethods}

      return {
        ...toRefs(state),
        ...toRefs(attrsState),
        localQuery,
        context,
        tempData,
        submitForm,
        resetForm,
        setFormData,
        afterSave,
        beforeSubmit,
        afterSubmit,
        changeInitStatus,
        setFormExtraData,
        getOperateRemarkData,
        handleChangeOptions,
        exeChildMethod,
        setFormDisabled${dialogExpose}${busExpose}
      }
    },
    watch:{
      ${formWatch}
      ${
        modelFieldWatch && modelFieldWatch.length > 0 && formConfig.watch && !formConfig.watch.endsWith(',')
          ? ',\n\n'
          : '\n\n'
      }
      ${modelFieldWatch.join(',\n\n')}
    },
    methods:${
      formConfig.functions ? lowCode.injectForObjStr(`{${formConfig.functions}}`, false, formConfig.modelName) : '{}'
    },
    created(){
      ${buildLifeCycleFn(formConfig, 'onFormCreated')}
    },
    mounted(){
      ${buildLifeCycleFn(formConfig, 'onFormMounted')}
    }
  })`;

  return v3JSTemplate;
};
