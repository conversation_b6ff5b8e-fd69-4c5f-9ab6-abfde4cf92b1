/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-06-21 16:23:29
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-21 11:23:19
 * @FilePath: \src\components\FuniFormEngine\common\utils\lowcode.js
 * @Description:根据配置生成代码
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import loadBeautifier from '@/components/FuniFormEngine/common/utils/beautifierLoader';
import { Base64 } from 'js-base64';
import { compile, generateComponent } from './vue3SfcCompiler';
import { defineAsyncComponent, defineComponent, h } from 'vue';
import { genSFC, genDialogSFC } from './funi-sfc-generator';
import {
  findAllWidget,
  findSpecialObjWidget,
  replaceAllWidget,
  getExpressionCode,
  getExtendIds, //获取扩展参数id树
  deepClone,
  isNotNullExpression
} from '@/components/FuniFormEngine/common/utils/util';
const SM_KEY = 'c8502bd294000ef49777ec31c54e6ebb';
export default {
  pageTypes: { list: 'list', edit: 'edit', detail: 'detail', blank: 'blank', listMobile: 'list-mobile' },
  componentType: { form: 'form', dialog: 'dialog' },
  /**
   * js代码生成组件
   * @param {js代码} jsCode
   * @returns vue组件实例
   */
  jsToComponent(jsCode) {
    return generateComponent(jsCode);
  },
  findAllWidget(widgetList) {
    return findAllWidget(widgetList);
  },
  getExtendIds(config) {
    return getExtendIds(config);
  },
  findSpecialObjWidget(widgetList) {
    return findSpecialObjWidget(widgetList);
  },
  getExpressionCode(codeJson) {
    return getExpressionCode(codeJson);
  },
  /**
   * 使用页面配置生成页面js代码
   * @param {页面配置} config
   * @param {代码格式化} beautifier
   * @returns 代码
   */
  generate(config, beautifier) {
    let sfcCode = this.generateSfc(config, beautifier);
    console.log(JSON.stringify(config));
    console.log(sfcCode);
    let code = compile(sfcCode, true);
    return code;
  },
  /**
   * 使用页面配置生成页面js代码
   * @param {页面配置} config
   * @param {代码格式化} beautifier
   * @returns 代码
   */
  generate2(config) {
    return new Promise((resovle, reject) => {
      loadBeautifier(beautifier => {
        try {
          let code = this.generate(config, beautifier);
          resovle(code);
        } catch (err) {
          reject(err);
        }
      });
    });
  },
  generateSfc2(config) {
    return new Promise((resovle, reject) => {
      loadBeautifier(beautifier => {
        try {
          let code = this.generateSfc(config, beautifier);
          resovle(code);
        } catch (err) {
          reject(err);
        }
      });
    });
  },
  /**
   * 加密代码
   * @param {js代码} code
   * @returns 加密后的代码
   */
  encrypt(code) {
    let jsStrBase64 = Base64.encode(code);
    let jsEncrypt = window.$utils.SM.sm4.encrypt(jsStrBase64, SM_KEY);
    return jsEncrypt;
  },
  /**
   * 解密代码
   * @param {*} jsEncrypt
   * @returns 解密后的代码
   */
  decrypt(jsEncrypt) {
    let jsCode = Base64.decode(window.$utils.SM.sm4.decrypt(jsEncrypt, SM_KEY));
    return jsCode;
  },
  /**
   * 生成sfc代码
   * @param {页面配置} config
   * @param {代码格式化} beautifier
   * @returns sfc代码
   */
  generateSfc(config, beautifier) {
    let sfcCode = '';
    let html, js, globalCss, scopedCss;
    if (config.type === this.pageTypes.list) {
      html = this.generateListHtml(config, beautifier);
      js = this.generateListJs(config, beautifier);
      globalCss = this.generateListGlobalCss(config, beautifier);
      scopedCss = this.generateListScopedScss(config, beautifier);
    } else if (config.type === this.pageTypes.edit || config.type === this.pageTypes.detail) {
      html = this.generateEditHtml(config, beautifier);
      js = this.generateEditJs(config, beautifier);
      globalCss = this.generateEditGlobalCss(config, beautifier);
      scopedCss = this.generateEditScopedScss(config, beautifier);
    } else if (config.type === this.pageTypes.blank) {
      html = this.generateBlankHtml(config, beautifier);
      js = this.generateBlankJs(config, beautifier);
      globalCss = this.generateBlankGlobalCss(config, beautifier);
      scopedCss = this.generateBlankScopedScss(config, beautifier);
    } else if (config.type === this.pageTypes.listMobile) {
      html = this.generateListMobileHtml(config, beautifier);
      js = this.generateListMobileJs(config, beautifier);
      globalCss = this.generateListMobileGlobalCss(config, beautifier);
      scopedCss = this.generateListMobileScopedScss(config, beautifier);
    }
    sfcCode = `<!--
Codes Generated By FuniLowCode:
-->
<template>
${html}
</template>

<script>
${js}
</script>

<style lang="scss">
${globalCss}
</style>

<style lang="scss" scoped>
${scopedCss}
</style>`;
    return sfcCode;
  },
  /**
   * 生成列表页html
   * @param {列表页配置} config
   * @param {代码格式化} beautifier
   * @returns html代码
   */
  generateListHtml(config, beautifier) {
    let children = [];
    let components = [];
    if (config.cardTab && config.cardTab.length > 0) {
      config.cardTab.forEach(item => {
        if (item.components.length > 0) {
          components.push(...item.components);
        }
      });
    }
    if (components && components.length > 0) {
      components.forEach(component => {
        if (component.type === this.componentType.dialog) {
          let dialogConfig = JSON.parse(component.configJson);
          if (config.cloneConfig && config.cloneConfig.app_code) {
            const sfcCode = genDialogSFC(
              dialogConfig.formConfig,
              dialogConfig.dialogConfig,
              dialogConfig.widgetList,
              beautifier,
              true,
              {
                appCode: config.cloneConfig.app_code
              }
            );
            const code = compile(sfcCode, true);
            component.code = this.encrypt(code);
          }
          let onAttr =
            component.ok ||
            component.cancel ||
            component.okConfig ||
            component.cancelConfig ||
            (dialogConfig.dialogConfig && dialogConfig.dialogConfig.listener)
              ? `v-on="${component.name}Listener"`
              : '';
          children.push(
            `<sfc-dialog-render ref="${component.name}" code="${component.code}" ${onAttr}></sfc-dialog-render>`
          );
        }
      });
    }
    let htmlCode = `<div style="height:100%"><funi-list-page ref="funiListPageRef" :cardTab="cardTab" :isShowSearch="isShowSearch" :active="active" :showTab="showTab" :teleported="teleported" @beforeRequest="handleBeforeRequest"></funi-list-page>${children.join(
      '\n\n'
    )}</div>`;
    return beautifier.html(htmlCode);
  },
  generateListJs(config, beautifier) {
    let joinFields = []; //存在子列的关联字段
    let paramsJoinFields = []; //在筛选参数中已设置的关联字段
    let stateData = [];
    let cardTabItems = [];
    let searchConfigGroup = [];
    let components = [];
    let orginHandlers = [];
    let orginPageVariable = [];
    let extendData = [];
    if (config && config.extendData && config.extendData.length > 0) {
      config.extendData.forEach(exData => {
        let valeStr = '';
        if (exData.value) {
          let expStr = getExpressionCode(exData.value);
          valeStr = expStr;
        }
        if (valeStr) {
          const extendValue = `@${valeStr}@`;
          extendData.push({ id: exData.id, key: exData.key, value: extendValue });
        }
      });
    }
    if (config && config.cardTab && config.cardTab.length !== 0) {
      config.cardTab.forEach((element, tabIndex) => {
        let hiddenTabStr = 'hiddenTab:false,';
        if (element.hiddenTab) {
          let hiddenTab = getExpressionCode(element.hiddenTab);
          if (hiddenTab) {
            hiddenTabStr = `hiddenTab:${hiddenTab},`;
          }
        }
        let searchOperators = {};
        let searchComponets = {};
        let searchTableNames = {};
        //生成columns
        let columns = [];
        let summaryFields = []; //需要合计的字段数字类型
        let dataCallback = '';
        let spanMethod = '';
        if (element && element.columns && element.columns.length > 0) {
          element.columns.sort((a, b) => a.serialNumber - b.serialNumber);
          let orginColumns = [...element.columns];
          if (element && element.actions && element.actions.length > 0) {
            element.actions.sort((a, b) => a.serialNumber - b.serialNumber);
            let renderList = [];
            let actionList = [];
            let showConditionList = [];
            element.actions.forEach((item, index) => {
              const { eventsConfig, events, componentType, iconPosition, showConditions, ...other } = item;
              if (['del', 'withdraw'].includes(other.eventsType)) {
                other.isPopconfirm = true;
                if (other.eventsType === 'del') {
                  other.popconfirmTip = `'确定删除该数据?'`;
                } else {
                  other.popconfirmTip = `'确定撤回该数据?'`;
                }
              }
              let showlogicStr = ''; //显示条件。需要显示的时候才给权限不然报错
              if (showConditions) {
                const expression = getExpressionCode(showConditions);
                if (expression) {
                  showlogicStr = `if(${expression}){`;
                }
              }
              const realEvents = {};
              if (eventsConfig) {
                for (let key in eventsConfig) {
                  if (eventsConfig[key]) {
                    let realKey = other.isPopconfirm && key === 'onClick' ? 'onConfirm' : key;
                    let eventCode = this.generateEvent(eventsConfig[key], true);
                    realEvents[realKey] = `@()=>{let param={};${eventCode}}@`;
                  }
                }
              } else if (events) {
                for (let key in events) {
                  if (events[key]) {
                    let realKey = other.isPopconfirm && key === 'onClick' ? 'onConfirm' : key;
                    realEvents[realKey] = `@${events[key]}@`;
                  }
                }
              }
              let eventsStr = JSON.stringify(realEvents)
                .replaceAll('"@', '')
                .replaceAll('@"', '')
                .replaceAll('"', "'")
                .replaceAll('\\n', '');
              let buttonCode = `
              ${
                other.icon && (iconPosition === 'right' || iconPosition === 'left')
                  ? `
              const FuniIcon=resolveComponent('funi-icon');
              let icon = h(FuniIcon,{icon:'${other.icon}'});
              const label${index}=h('span',{style:{marginRight:'4px'}},'${other.label}');
              `
                  : ''
              }
              const button${index}=h(ElButton,
              {
                style:{marginLeft:'12px'},
                link:${componentType === 'link' ? 'true' : 'false'},
                type:'${other.type ?? ''}',
                size:'${other.size ?? ''}',
                ${other.isPopconfirm ? '' : `...${eventsStr},`}
                ${
                  other.isLinkage && element.selectType
                    ? `disabled:!(state.selectionRows&&state.selectionRows.length>0)`
                    : ''
                }
              },${
                iconPosition === 'right'
                  ? `{default:()=>[label${index},icon]}`
                  : iconPosition === 'left'
                  ? `{default:()=>[icon,label${index}]}`
                  : `{default:()=>['${other.label}']}`
              });
              ${
                other.auth
                  ? `
                  let action${index};
                  ${showlogicStr}
              const directive${index}=withDirectives(
                  button${index},
                  [[authDirective, '${other.auth}']]
                );
                action${index}=directive${index};
                ${
                  showlogicStr
                    ? `} else {
                  action${index}=button${index};
                }`
                    : ''
                }
              `
                  : `let action${index}=button${index};`
              }
              ${
                other.isPopconfirm
                  ? `
              context.params.activeCardIndex=${tabIndex};
              const delAction${index}=h(ElPopconfirm,{
                title:${other.popconfirmTip},
                width:200,
                ...${eventsStr}
              },{
                reference:()=>[
                  action${index}
                ]
              });
              `
                  : ''
              }
              `;
              renderList.push(buttonCode);
              actionList.push(other.isPopconfirm ? `delAction${index}` : `action${index}`);
              if (showConditions) {
                const expression = getExpressionCode(showConditions);
                if (expression) {
                  let logicStr = `if(!(${expression})){actionList.splice(${index},1)}`;
                  showConditionList.push(logicStr);
                }
              }
            });
            if (showConditionList && showConditionList) {
              //确保从大的index开始删除
              showConditionList.reverse();
            }
            let renderStr =
              renderList.length > 0
                ? `({row,index})=>{
                  const ElButton=resolveComponent('el-button');
                  const ElPopconfirm=resolveComponent('el-popconfirm');
                  const authDirective = resolveDirective('auth');
                ${renderList.join('')}
                let actionList=[${actionList.toString()}];
                ${showConditionList.join('\n')}
                return h('div',{style:{marginLeft:'-12px'}},{default:()=>[actionList]});
                }
            `
                : '';
            let actionColumn = {
              serialNumber: element.actions.length + 1,
              label: '操作',
              prop: 'action',
              fixed: 'right',
              showOverflowTooltip: false,
              render: renderStr
            };
            orginColumns.push(actionColumn);
          }
          orginColumns.forEach(col => {
            const { render, eventsConfig, formatter, children, data_type, ...other } = col; //formatter是后端用的，先排除
            if (['int', 'double'].includes(data_type)) {
              summaryFields.push({ prop: col.prop, data_type });
            }
            let realRender;
            if (render && render.startsWith(`({row,index})=>{`)) {
              realRender = render;
            } else if (render) {
              realRender = `({row,index})=>{${render}}`;
            } else if (eventsConfig) {
              for (let key in eventsConfig) {
                if (eventsConfig[key] && key === 'onClick') {
                  let eventCode = this.generateEvent(eventsConfig[key], true);
                  realRender = `({row,index})=>{
                    const ElLink=resolveComponent('el-link');
                    const link=h(ElLink,{type:'primary',onClick:()=>{
                      let param={};${eventCode}
                    }},{default:()=>row['${col.prop}']});
                    return link
                  }`;
                  break;
                }
              }
            }
            let newCol = { ...other };
            if (children && children.length > 0) {
              children.forEach(child => {
                let realChildRender;
                if (child.render && child.render.startsWith(`({row,index})=>{`)) {
                  realChildRender = child.render;
                } else if (child.render) {
                  realChildRender = `({row,index})=>{${child.render}}`;
                }
                if (realChildRender) {
                  child.render = `@${(this.injectContext(realChildRender) ?? '').replaceAll('"', "'")}@`;
                }
                delete child.childrenOptions;
                delete child.formatter;
              });
              newCol.align = 'center';
              newCol.children = children;
            }
            if (realRender) {
              newCol.render = `@${(this.injectContext(realRender) ?? '').replaceAll('"', "'")}@`;
            }
            columns.push(newCol);
          });
          //需要合并的列
          joinFields = orginColumns.filter(col => col && col.children && col.children.length > 0).map(col => col.prop);
          if (joinFields && joinFields.length > 0) {
            dataCallback = `dataCallback:(res)=>{
              return new Promise((resolve)=>{
                let mergeIds=[];
                if(res&&res.response&&res.response.list&&res.response.list.length>0){
                  for(let i=0;i<res.response.list.length;i++){
                    let id=res.response.list[i].id;
                    res.response.list[i].guid=window.$utils.guid();
                    let fIndex=mergeIds.findIndex(item=>item===id);
                    if(fIndex>=0){
                      res.response.list[i].rowspan=0;
                    } else {
                      let mergeLength=res.response.list.filter(item=>item.id===id).length;
                      if(mergeLength>1){
                        res.response.list[i].rowspan=mergeLength;
                        mergeIds.push(id);
                      }
                    }
                  }
                }
                resolve({list:res.response.list,total:res.response.total});
              })
            },`;
            spanMethod = `spanMethod:({row,column,rowIndex,columnIndex})=>{
              let joinFields=${JSON.stringify(joinFields)};
              let parentProp=column&&column.property?column.property.split('.')[0]:'';
              if(row.rowspan>=0&&!joinFields.includes(parentProp)){
                return {
                  rowspan: row.rowspan,
                  colspan: 1,
                }
              }
            },`;
          }
        }
        //添加行号
        if (element.isLineNumber === true) {
          columns.unshift({ label: '序号', prop: '', width: '60', type: 'index' });
        }

        //如果需要选中数据
        if (element.selectType === 'radio') {
          //单选
          columns.unshift({ type: 'radio' });
        } else if (element.selectType === 'selection') {
          //多选
          columns.unshift({ type: 'selection' });
        }

        //生成btns
        let btns = [];
        if (element && element.buttons && element.buttons.length > 0) {
          element.buttons.forEach(btn => {
            const { eventsConfig, events, componentType, iconPosition, ...other } = btn;
            const realEvents = {};
            if (eventsConfig) {
              for (let key in eventsConfig) {
                if (eventsConfig[key]) {
                  let eventCode = this.generateEvent(eventsConfig[key]);
                  realEvents[key] = `@${this.injectContext(eventCode)}@`;
                }
              }
            } else if (events) {
              for (let key in events) {
                if (events[key]) {
                  realEvents[key] = `@${this.injectContext(events[key])}@`;
                }
              }
            }
            let eventsStr = JSON.stringify(realEvents)
              .replaceAll('"@', '')
              .replaceAll('@"', '')
              .replaceAll('"', "'")
              .replaceAll('\\n', '');
            let renderButton = `()=>{
              const ElButton=resolveComponent('el-button');
              ${
                other.icon && (iconPosition === 'right' || iconPosition === 'left')
                  ? `
              const FuniIcon=resolveComponent('funi-icon');
              let icon = h(FuniIcon,{icon:'${other.icon}'});
              const label=h('span',{style:{marginRight:'4px'}},'${other.label}');
              `
                  : ''
              }
              const button=h(ElButton,
              {
                link:${componentType === 'link' ? 'true' : 'false'},
                type:'${other.type ?? ''}',
                size:'${other.size ?? ''}',
                ...${eventsStr},
                ${
                  other.isLinkage && element.selectType
                    ? `disabled:!(state.selectionRows&&state.selectionRows.length>0)`
                    : ''
                }
              },${
                iconPosition === 'right'
                  ? `{default:()=>[label,icon]}`
                  : iconPosition === 'left'
                  ? `{default:()=>[icon,label]}`
                  : `{default:()=>['${other.label}']}`
              });
              ${
                other.auth
                  ? `
              const authDirective = resolveDirective('auth');
              return withDirectives(
                button,
                [[authDirective, '${other.auth}']]
              );
              `
                  : `return button;`
              }

            }`;
            let newBtn = {
              component: `@${renderButton}@`
            };
            btns.push(newBtn);
          });
        }
        btns.sort((a, b) => a.serialNumber - b.serialNumber);
        let searchConfig = { schema: [], setParams: element.searchSetParams };
        if (element && element.searchConfigGroup && element.searchConfigGroup.length > 0) {
          searchConfigGroup.push(...element.searchConfigGroup);
          let linkageList = [];
          element.searchConfigGroup.forEach(cfg => {
            if (cfg.schema && cfg.schema.length > 0) {
              cfg.schema.forEach((sch, schIndex) => {
                searchOperators[sch.prop] = sch.operator ?? 'EQUAL';
                searchComponets[sch.prop] = sch.component;
                searchTableNames[sch.prop] = sch.tableName;
                if (sch.joinFieldName) {
                  paramsJoinFields.push(sch.joinFieldName);
                }
                if ('el-select' === sch.componentType) {
                  let { uuid, component, componentType, typeName, typeCode, label, prop, props, ...otherConfig } = sch;
                  searchConfig.schema.push({
                    component: 'funi-select',
                    label,
                    prop,
                    props: {
                      typeCode: typeCode ?? typeName,
                      isLocal: true,
                      ...otherConfig,
                      style: { width: '100%' },
                      ...props
                    }
                  });
                } else if (['model', 'APIs'].includes(sch.componentType)) {
                  let {
                    api_url,
                    uuid,
                    component,
                    componentType,
                    typeName,
                    typeCode,
                    expression,
                    params,
                    label,
                    prop,
                    model_id,
                    labelKey,
                    valueKey,
                    sort,
                    ...otherConfig
                  } = sch;
                  let realFilterParams = [];
                  if (params && params.length > 0) {
                    params.forEach(param => {
                      let conditions = [];
                      if (param && param.conditions && param.conditions.length > 0) {
                        param.conditions.forEach(cond => {
                          if (cond.value) {
                            try {
                              const expressionStr = getExpressionCode(cond.value);
                              if (expressionStr) {
                                let linkageExps = expressionStr.match(/'context\.formData\..+?'/g);
                                if (linkageExps && linkageExps.length > 0) {
                                  linkageExps.forEach(lprop => {
                                    cond.value = cond.value.replaceAll(lprop, lprop.substr(1, lprop.length - 2));
                                    let linkageProp = lprop.substr(18, lprop.length - 19);
                                    let fIndex = linkageList.findIndex(link => link.linkageProp === linkageProp);
                                    if (fIndex < 0) {
                                      linkageList.push({
                                        linkageProp,
                                        linkages: [{ tabIndex, schIndex, filterProp: cond.key }]
                                      });
                                    } else {
                                      linkageList[fIndex].linkages.push({ tabIndex, schIndex, filterProp: cond.key });
                                    }
                                  });
                                }
                              }
                            } catch (ex) {
                              console.log(ex);
                            }
                          }
                          if (cond.joinFieldName) {
                            paramsJoinFields.push(cond.joinFieldName);
                          }
                          let valeStr = '';
                          if (cond.value) {
                            let expStr = getExpressionCode(cond.value);
                            valeStr = expStr;
                          }
                          if (valeStr) {
                            let condNew = {
                              key: cond.key,
                              operator: cond.operator,
                              value: `@${valeStr}@`,
                              logicalOperator: cond.logicalOperator
                            };
                            if (cond.tableName) {
                              condNew.tableName = cond.tableName;
                            }
                            conditions.push(condNew);
                          } else {
                            let condNew = {
                              key: cond.key,
                              operator: cond.operator,
                              logicalOperator: cond.logicalOperator
                            };
                            if (cond.tableName) {
                              condNew.tableName = cond.tableName;
                            }
                            conditions.push(condNew);
                          }
                        });
                      }
                      realFilterParams.push({ logicalOperator: param.logicalOperator, conditions });
                    });
                  }
                  searchConfig.schema.push({
                    component: 'sfc-select',
                    label,
                    prop,
                    props: {
                      filterable: true,
                      changeField: '',
                      auto: false,
                      requestParams: {
                        asUrl: componentType === 'APIs' ? api_url : `@'/as/'+app_code+'/model/dicListV2'@`,
                        page_id: `@page_id@`,
                        model_id: model_id,
                        labelKey,
                        valueKey,
                        sort,
                        params: `@computed(${this.injectContext(
                          `()=>{
                          return ${JSON.stringify(realFilterParams)
                            .replaceAll('"@', '')
                            .replaceAll('@"', '')
                            .replaceAll('\\n', '')}
                        }`,
                          true,
                          'formData'
                        )})@`
                      },
                      ...otherConfig,
                      style: { width: '100%' }
                    }
                  });
                } else {
                  searchConfig.schema.push(sch);
                }
              });
            }
          });
          //检索联动处理
          if (linkageList && linkageList.length > 0 && searchConfig.schema && searchConfig.schema.length > 0) {
            linkageList.forEach(linkage => {
              let fIndex = searchConfig.schema.findIndex(item => item.prop === linkage.linkageProp);
              if (fIndex >= 0) {
                if (!searchConfig.schema[fIndex].props) {
                  searchConfig.schema[fIndex].props = {};
                }
                let changeFieldCode = '';
                if (linkage.linkages && linkage.linkages.length > 0) {
                  linkage.linkages.forEach(link => {
                    changeFieldCode += `state.cardTab[${link.tabIndex}].curdOption.searchConfig.schema[${link.schIndex}].props.changeField = e;`;
                  });
                }
                searchConfig.schema[fIndex].props.onChange = `e => {
                  context.formData['${linkage.linkageProp}'] = e;
                  ${changeFieldCode}
                }`;
              }
            });
          }
        }
        let listener = {};
        if (element && element.events && element.events.length > 0) {
          let events = [...element.events];
          events.forEach(evt => {
            let body = evt.body;
            if (evt.bodyConfig) {
              body = this.generateEvent(evt.bodyConfig);
            }
            listener[evt.name] = `${body.replaceAll('"', "'")}`;
          });
        }
        let requestParams = {};
        if (element.requestOtherParams && element.requestOtherParams.tabType === 'model') {
          const { params, ...other } = element.requestOtherParams;
          requestParams = { ...other };
          let realParams = [];
          if (params && params.length > 0) {
            params.forEach(p => {
              const conditions = [];
              if (p.conditions) {
                p.conditions.forEach(c => {
                  let valeStr = '';
                  if (c.value) {
                    let expStr = getExpressionCode(c.value);
                    valeStr = expStr;
                  }
                  if (c.joinFieldName) {
                    paramsJoinFields.push(c.joinFieldName);
                  }
                  if (valeStr) {
                    let condNew = {
                      key: c.key,
                      operator: c.operator,
                      value: `@${valeStr}@`,
                      logicalOperator: c.logicalOperator
                    };
                    if (c.tableName) {
                      condNew.tableName = c.tableName;
                    }
                    conditions.push(condNew);
                  } else {
                    let condNew = {
                      key: c.key,
                      operator: c.operator,
                      logicalOperator: c.logicalOperator
                    };
                    if (c.tableName) {
                      condNew.tableName = c.tableName;
                    }
                    conditions.push(condNew);
                  }
                });
              }
              realParams.push({ logicalOperator: p.logicalOperator, conditions });
            });
          }
          requestParams.params = realParams;
          requestParams.joinFields = Array.from(new Set([...joinFields, ...paramsJoinFields]));
        } else if (element.requestOtherParams) {
          const { params, ...other } = element.requestOtherParams;
          let realParams = {};
          if (params && params.length > 0) {
            params.forEach(p => {
              if (isNotNullExpression(p.expression)) {
                let expStr = getExpressionCode(p.value);
                realParams[p.key] = `@${expStr}@`;
              } else {
                realParams[p.key] = p.value;
              }
            });
          }
          if (element.requestOtherParams.tabType === 'apis' && element.requestOtherParams.origin === 'other') {
            Object.assign(requestParams, realParams);
          } else {
            Object.assign(requestParams, { params: realParams });
          }
        }
        if (element && element.components && element.components.length > 0) {
          components.push(...element.components);
        }
        if (element && element.handlers && element.handlers.length > 0) {
          orginHandlers.push(...element.handlers);
        }
        if (element && element.pageVariable && element.pageVariable.length > 0) {
          orginPageVariable.push(...element.pageVariable);
        }
        cardTabItems.push(
          `{
      label: '${element.label ?? ''}',
      ${hiddenTabStr}
      key: '${element.key ?? ''}',
      fixSearchParams:(searchParams)=>{
        let filter=[];
        let searchOperators=${JSON.stringify(searchOperators)};
        let searchComponets=${JSON.stringify(searchComponets)};
        let searchTableNames=${JSON.stringify(searchTableNames)};
        if(searchParams){
          for(let key in searchParams){
            filter.push({key,operator:searchOperators[key],value:searchParams[key],component:searchComponets[key],tableName:searchTableNames[key]});
          }
        }
        return {
          filter
        }
      },
      curdOption: {
        ${element.api ? `api:'${element.api}',` : ''}${element.pagination === false ? `pagination:false,` : ''}${
            element.reloadOnActive === true ? `reloadOnActive:true,` : ''
          }${element.header ? `header:'${element.header}',` : ''}${
            element.pageSize ? `defaultPage:{ pageSize: ${element.pageSize}, pageNo: 1 },` : ''
          }${element.emptyText ? `emptyText:'${element.emptyText}',` : ''}${
            element.rowBackGroundColor ? `rowStyle:{'background':'${element.rowBackGroundColor}'},` : ''
          }${element.stripe === false ? `stripe:false,` : ''}${
            element.showSummary === true ? 'showSummary:true,' : ''
          }${
            element.showSummary === true && summaryFields && summaryFields.length > 0
              ? `summaryMethod:(summaryParam)=>{
                let summaryFields=${JSON.stringify(summaryFields)};
                const { columns, data } = summaryParam
                const sums = []
                columns.forEach((column, index) => {
                  if (index === 0) {
                    sums[index] = '合计'
                    return
                  }
                  const values = data.map((item) => Number(item[column.property]));
                  const fIndex = summaryFields.findIndex(f=>f.prop===column.property);
                  if (!values.every((value) => Number.isNaN(value))&&fIndex>=0) {
                    let total = values.reduce((prev, curr) => {
                      const value = Number(curr);
                      if (!Number.isNaN(value)) {
                        return prev + curr;
                      } else {
                        return prev;
                      }
                    }, 0);
                    sums[index]=summaryFields[fIndex].data_type==='int'?total.toFixed(0):total.toFixed(2);
                  } else {
                    sums[index] = '';
                  }
                })
                return sums
            },`
              : ''
          }
        searchConfig:${JSON.stringify(this.stringifyObj(searchConfig, true, 'formData', false, false, false))
          .replaceAll('"@', '')
          .replaceAll('@"', '')
          .replaceAll('\\n', '')},
        rowKey:${joinFields && joinFields.length > 0 ? `'guid'` : `'id'`},
        fixedButtons:${element.fixedButtons === false ? 'false' : 'true'},
        btns:${JSON.stringify(btns).replaceAll('"@', '').replaceAll('@"', '').replaceAll('\\n', '')},
        ${dataCallback}
        ${spanMethod}
        columns: ${JSON.stringify(columns).replaceAll('"@', '').replaceAll('@"', '').replaceAll('\\n', '')},
        requestParams:computed(()=>{
          let requestVersion=state.requestVersion;
          context.params={activeCardIndex:${tabIndex}};
          return ${
            requestParams
              ? `{page_id,extendData:getExtendParams('${element.id ?? element.key}',context),...${JSON.stringify(
                  requestParams
                )
                  .replaceAll('"@', '')
                  .replaceAll('@"', '')
                  .replaceAll('\\n', '')}}`
              : '{page_id}'
          }}),
        on:{${
          element.selectType === 'radio'
            ? `'current-change':(currentRow)=>{
            state.selectionRows=[currentRow];
          },`
            : element.selectType === 'selection'
            ? `'selection-change':(selection)=>{
            state.selectionRows=[...selection];
          },`
            : ''
        }${listener.rowClick ? `'rowClick':${this.injectContext(listener.rowClick)},` : ''}${
            listener.afterRequest ? `'afterRequest':${this.injectContext(listener.afterRequest)},` : ''
          }${listener.requestError ? `'requestError':${this.injectContext(listener.requestError)},` : ''}
        }
      }}
        `
        );
      });
    }
    let cardTabData = `cardTab`;
    stateData.push(cardTabData);
    let isShowSearchData = `isShowSearch:false`;
    if (searchConfigGroup.length > 0) {
      searchConfigGroup.forEach(g => {
        if (g.schema && g.schema.length > 0) {
          isShowSearchData = `isShowSearch:true`;
        }
      });
    }
    stateData.push(isShowSearchData);
    let activeData = `active:undefined`;
    if (config && config.active) {
      activeData = `active:cardTab.length>0?cardTab[0].key:'${config.active}'`;
    }
    stateData.push(activeData);
    let showTabData = `showTab:${config && config.cardTab && config.cardTab.length > 1 ? 'false' : 'false'}`;
    if (config && config.showTab !== undefined && config.showTab !== null) {
      showTabData = `showTab:${config.showTab === true ? 'false' : 'false'}`;
    }
    stateData.push(showTabData);
    let teleportedData = `teleported:${config.teleported === true ? 'true' : 'false'}`;
    stateData.push(teleportedData);
    let selectionRowsData = `selectionRows:[]`;
    stateData.push(selectionRowsData);

    //生成组件的监听事件
    if (components.length > 0) {
      components.forEach(com => {
        if (com.type === this.componentType.dialog) {
          let dialogConfig = JSON.parse(com.configJson);
          if (
            com.ok ||
            com.cancel ||
            com.okConfig ||
            com.cancelConfig ||
            (dialogConfig.dialogConfig && dialogConfig.dialogConfig.listener)
          ) {
            let comListener = {};
            if (dialogConfig.dialogConfig && dialogConfig.dialogConfig.listener) {
              let proxyStr = `return {${dialogConfig.dialogConfig.listener}}`;
              try {
                comListener = new Function(`${proxyStr}`)();
                for (let key in comListener) {
                  comListener[key] = `@${this.injectContext(comListener[key] + '')}@`;
                }
              } catch (err) {
                console.log(err);
              }
            }
            if (com.ok || com.okConfig) {
              let okBody = com.ok;
              if (com.okConfig) {
                okBody = this.generateEvent(com.okConfig);
              }
              comListener.ok = `@${this.injectContext(okBody)}@`;
            }
            if (com.cancel || com.cancelConfig) {
              let cancelBody = com.cancel;
              if (com.cancelConfig) {
                cancelBody = this.generateEvent(com.cancelConfig);
              }
              comListener.cancel = `@${this.injectContext(cancelBody)}@`;
            }
            stateData.push(
              `${com.name}Listener:${JSON.stringify(comListener)
                .replaceAll('"@', '')
                .replaceAll('@"', '')
                .replaceAll('\\n', '')}`
            );
          }
        }
      });
    }

    let handlers = [];
    if (orginHandlers.length > 0) {
      orginHandlers.forEach(item => {
        handlers.push(`${item.name}:compileJs('${Base64.encode(item.body)}')`);
      });
    }
    let pageVariable = [];
    let pageVariableStr = `()=>{
      return {}
    }`;
    if (orginPageVariable.length > 0) {
      orginPageVariable.forEach(item => {
        if (h.type === 'string' || !h.type) {
          pageVariable.push(`${item.name}:'${item.defaultVal}'`);
        } else {
          pageVariable.push(`${item.name}:${item.defaultVal}`);
        }
      });
      pageVariableStr = this.injectContext(
        `()=>{
          let pageVal={};
          try{
            pageVal={
              ${pageVariable.join(',')}
            }
          } catch(ex){}
        return pageVal;
      }`,
        true
      );
    }

    let jsCode = `import { defineComponent, toRefs, ref, h, reactive, getCurrentInstance, resolveComponent,resolveDirective, withDirectives } from 'vue';

//获取扩展参数
function getExtendParams(extendId,context={}){
  const extendList=${
    extendData && extendData.length > 0
      ? JSON.stringify(extendData).replaceAll('"@', '').replaceAll('@"', '').replaceAll('\\n', '')
      : '[]'
  }
  let extendParams={};
  if(extendId){
    extendList.forEach(item=>{
      if(item.id===extendId){
        extendParams[item.key]=item.value;
      }
    })
  }
  return extendParams;
}

export default defineComponent({
  components: {},
  props: {

  },
  setup(props) {
    let app_code,page_id;
      let hash = window.location.hash;
      if (hash && hash.length > 0) {
        hash = window.location.hash.split('?')[0];
        let hashList = hash.split('/');
        if (hashList && hashList.length >= 3) {
          page_id = hashList[hashList.length - 1];
          app_code = hashList[hashList.length - 2];
        }
      }
    const tempData={};
    const instance = getCurrentInstance();
    let cardTab=[${cardTabItems.join('\n,\n')}].filter(card=>!card.hiddenTab);
    const state = reactive({
      formData:{},
      requestVersion:0,
      pageData:${pageVariableStr},
      handler:{
        ${handlers.join('\n,\n')}
      },
      ${stateData.join('\n,\n')}
    });
    const localQuery=JSON.parse(JSON.stringify(instance.proxy.$route.query||{}));
    const context={
      router:instance.proxy.$router,
      route:instance.proxy.$route,
      query:localQuery,
      emit:instance.proxy.$emit,
      message:instance.proxy.$message,
      notify:instance.proxy.$notify,
      alert:instance.proxy.$alert,
      loading:instance.proxy.$loading,
      showSfcUpload:instance.proxy.$showSfcUpload,
      showLog:instance.proxy.$showLog,
    };
    const handleBeforeRequest=()=>{
      state.requestVersion++;
    };

    onMounted(()=>{
      setTimeout(() => {
        Object.assign(context,{
          data:state.pageData,
          handler:state.handler,
          refs:instance.proxy.$refs,
          })
      }, 100);
    });
    return {
      ...toRefs(state),
      localQuery,
      tempData,
      handleBeforeRequest,
      context
    }
  },
  mounted() {

  },
  activated() {
    this.$refs.funiListPageRef.reload({resetPage:false});
  },
  methods: {
  },
})`;
    return beautifier.js(jsCode);
  },
  generateListGlobalCss(config, beautifier) {
    let scssCode = ``;
    return beautifier.css(scssCode);
  },
  generateListScopedScss(config, beautifier) {
    let scssCode = ``;
    return beautifier.css(scssCode);
  },
  generateListMobileHtml(config, beautifier) {
    let htmlCode = `<div style="height:100%">
    <funi-list-page-mobile class="list-page" ref="funiListPageMobileRef" v-bind="listPageProps"/>
    </div>`;
    return beautifier.html(htmlCode);
  },
  generateListMobileJs(config, beautifier) {
    let realConfig = {};
    if (config && config.cardTab && config.cardTab.length > 0) {
      let realCardTab = [];
      config.cardTab.forEach(tab => {
        let requestParams = {};
        if (tab.requestOtherParams) {
          const { params, ...other } = tab.requestOtherParams;
          requestParams = { ...other };
          let realParams = [];
          if (params && params.length > 0) {
            params.forEach(p => {
              realParams.push({ key: p.name, value: p.expression });
            });
          }
          requestParams.params = realParams;
        }
        let imageConfig;
        let realColumn = [];
        if (tab.columns) {
          for (let key in tab.columns) {
            let column = { label: '', prop: tab.columns[key] };
            if (key === 'titleProp') {
              column.props = { style: { fontWeight: 'bold' } };
            }
            if (key === 'imgProp') {
              imageConfig = { prop: tab.columns[key] };
            } else {
              realColumn.push(column);
            }
          }
        }
        let opreateConfig = { hide: true };
        if (tab.buttons && tab.buttons.length > 0) {
          let addButton = tab.buttons[0];
          let onClick = '';
          if (addButton.eventsConfig && addButton.eventsConfig.onClick) {
            onClick = this.generateEvent(addButton.eventsConfig.onClick);
          } else if (addButton.events && addButton.events.onClick) {
            onClick = addButton.events.onClick;
          }
          opreateConfig = { hide: false, auth: addButton.auth, props: { onClick } };
        }
        let listener = {};
        if (config && config.events && config.events.length > 0) {
          let events = [...config.events];
          events.forEach(evt => {
            let body = evt.body;
            if (evt.bodyConfig) {
              body = this.generateEvent(evt.bodyConfig);
            }
            listener[evt.name] = body;
          });
        }
        let cardStyle = { borderRadius: '4px', margin: '4px 4px', padding: '8px 0px' };
        if (tab.rowBackGroundColor) {
          cardStyle.background = tab.rowBackGroundColor;
        }
        if (tab.borderRadius) {
          cardStyle.borderRadius = `${tab.borderRadius}px`;
        }

        if (tab.margin) {
          let topStr = `${tab.margin.top ?? '0'}px`;
          let rightStr = `${tab.margin.right ?? '0'}px`;
          let bottomStr = `${tab.margin.bottom ?? '0'}px`;
          let leftStr = `${tab.margin.left ?? '0'}px`;
          cardStyle.margin = `${topStr} ${rightStr} ${bottomStr} ${leftStr}`;
        }

        if (tab.padding) {
          let topStr = `${tab.padding.top ?? '0'}px`;
          let rightStr = `${tab.padding.right ?? '0'}px`;
          let bottomStr = `${tab.padding.bottom ?? '0'}px`;
          let leftStr = `${tab.padding.left ?? '0'}px`;
          cardStyle.padding = `${topStr} ${rightStr} ${bottomStr} ${leftStr}`;
        }

        let searchConfig = { filterHide: true };
        searchConfig.placeholder = config.searchTip ?? '';
        searchConfig.prop = config.searchProp ?? '';
        if (config.searchConfigGroup && config.searchConfigGroup.length > 0) {
          searchConfig.schema = config.searchConfigGroup[0].schema;
          searchConfig.filterHide = false;
        }
        let realTab = {
          label: '',
          curdOption: {
            isDefaultQuery: tab.isDefaultQuery,
            api: tab.api,
            isShowSearch: true,
            statusConfig: {},
            fixSearchParams: `(filterParams) => {
              let filter=[];
              if(filterParams){
                for(let key in filterParams){
                  filter.push({key,value:filterParams[key]});
                }
              }
              return {
                filter
              }
            }`,
            imageConfig,
            searchConfig,
            requestParams,
            columns: realColumn,
            actionConfig: {},
            opreateConfig,
            cardStyle,
            ...listener
          }
        };
        realCardTab.push(realTab);
      });
      realConfig.cardTab = realCardTab;
    }
    let cardTabStr = '';
    if (realConfig && realConfig.cardTab && realConfig.cardTab.length > 0) {
      let cardTab = [];
      realConfig.cardTab.forEach(tab => {
        let tabStr = `${JSON.stringify(this.stringifyObj(tab))
          .replaceAll('"@', '')
          .replaceAll('@"', '')
          .replaceAll('\\n', '\n')}`;
        cardTab.push(tabStr);
      });
      cardTabStr = `cardTab:[${cardTab.toString()}]`;
    }
    let listPageProps = `{${cardTabStr}}`;
    let jsCode = `
    export default {
      setup(props){
        const instance=getCurrentInstance();
        const state=new reactive({
        });
        const allProps= new reactive({
          listPageProps:${listPageProps},
        });
        return {
          ...toRefs(state),
          ...toRefs(allProps),
        }
      }
    }
    `;
    return beautifier.js(jsCode);
  },
  generateListMobileGlobalCss(config, beautifier) {
    let scssCode = ``;
    return beautifier.css(scssCode);
  },
  generateListMobileScopedScss(config, beautifier) {
    let scssCode = `.list-page{
height:100%;
}`;
    return beautifier.css(scssCode);
  },
  generateEditHtml(config, beautifier) {
    let htmlCode = `<div>
  <funi-detail ref="funiDetailRef" :auditContainerStyle="{padding:'0px 7px'}" :steps="steps" :bizName="bizName" :detailHeadOption="detailHeadOption" :auditButtons="[]" :isFormOpinion="true" :beforeAuditFn="beforeAuditFn" @auditEvent="onAudit" @tabChange="handleTabChange" ${
    config.type === this.pageTypes.detail ? `:businessId="businessId" :showHead="true"` : `:showHead="false"`
  }></funi-detail>
</div>`;
    return htmlCode;
  },
  /**
   * pc 编辑页
   * @param {*} config
   * @param {*} beautifier
   * @returns
   */
  generateEditJs(config, beautifier) {
    const buttons = [];
    let buttonsStr = '[]';
    let hideStatusBar = false;
    let extendData = [];
    if (config && config.extendData && config.extendData.length > 0) {
      config.extendData.forEach(exData => {
        let valeStr = '';
        if (exData.value) {
          let expStr = getExpressionCode(exData.value);
          valeStr = expStr;
        }
        if (valeStr) {
          const extendValue = `@${valeStr}@`;
          extendData.push({ id: exData.id, key: exData.key, value: extendValue });
        }
      });
    }
    if (config && config.detailHeadOption && config.detailHeadOption.hideStatusBar) {
      hideStatusBar = true;
    }
    if (config && config.detailHeadOption && config.detailHeadOption.btns && config.detailHeadOption.btns.length > 0) {
      config.detailHeadOption.btns.forEach(item => {
        let eventCode = item.events?.onClick;
        if (item.eventsConfig && item.eventsConfig.onClick) {
          let funCode = this.generateEvent(item.eventsConfig.onClick, true);
          eventCode = `()=>{let param={};${funCode}}`;
        }
        const { label, eventsConfig, events, ...props } = item;
        let btn = {
          name: label,
          type: 'sfc-button',
          props: {
            label,
            ...props
          },
          on: {
            click: `@${this.injectContext(eventCode)}@`
          }
        };
        buttons.push(btn);
      });
      buttonsStr = JSON.stringify(buttons)
        .replaceAll('"@', '')
        .replaceAll('@"', '')
        .replaceAll('"', "'")
        .replaceAll('\\n', '');
    }
    const stateData = [];
    //业务状态
    const bizNameData = `bizName:bizName`;
    stateData.push(bizNameData);
    //选中页签
    const activeStepIndex = `activeStepIndex:0`;
    stateData.push(activeStepIndex);
    //头部数据
    const detailHeadOption = `detailHeadOption:reactive({title: '',
    no: businessId,
    status: status,
    hideStatusBar: ${!hideStatusBar ? '!businessId' : 'true'},
    btns:${buttonsStr}
  })`;
    stateData.push(detailHeadOption);
    //步骤组件
    const steps = [];
    if (config && config.steps && config.steps.length > 0) {
      let mainModelId = config.steps[0].modelId;
      config.steps.forEach((item, stepIndex) => {
        item.preservable = !!item.modelId; //有模型则可以暂存
        let code = item.code;
        if (config.cloneConfig && config.cloneConfig.app_code) {
          let cfg;
          try {
            cfg = JSON.parse(item.configJson);
          } catch (ex) {
            console.log(ex);
          }
          if (cfg) {
            const sfcCode = genSFC(cfg.formConfig, cfg.widgetList, beautifier, true, {
              appCode: config.cloneConfig.app_code
            });
            code = this.encrypt(compile(sfcCode, true));
          }
        }
        let changeHeadEvent = `()=>{}`;
        if (config.detailHeadOption && config.detailHeadOption.title) {
          changeHeadEvent = `(formData)=>{
            Object.assign(state.formData,formData);
            context.formData=state.formData;
            state.detailHeadOption.title=${getExpressionCode(config.detailHeadOption.title)};
          }`;
        }
        let stepConfig = `
        {
          title:'${item.title ?? ''}',
          hiddenTab:${
            item.hiddenTab ? (isNotNullExpression(item.hiddenTab) ? getExpressionCode(item.hiddenTab) : false) : false
          },
          preservable:${item.preservable === false ? 'false' : 'true'},
          type: childStep,
          props: {
            editable,
            modelValue:undefined,
            detailExtendId:'${item.id}',
            saveExtendId:'${item.saveExtendId}',
            key:'${item.serialNumber}',
            code: '${code ?? ''}',
            serialNumber:'${item.serialNumber}',
            modelId:'${item.modelId}',
            mainModelId:'${mainModelId}',
            urls:${config.urls ? JSON.stringify(config.urls) : `{}`}
          },
          on:{
            ${item.serialNumber === 1 ? `changeHead:${this.injectContext(changeHeadEvent)}` : ''}
          }
        }
        `;
        steps.push(stepConfig);
      });
    }
    let stepsData = `steps:computed(()=>{
      let stepList=[${steps.join(',')}];
      let list=stepList.filter(item=>!item.hiddenTab);
      return list;
    })`;
    stateData.push(stepsData);
    let handlers = [];
    if (config && config.handlers && config.handlers.length > 0) {
      config.handlers.forEach(item => {
        handlers.push(`${item.name}:compileJs('${Base64.encode(item.body)}')`);
      });
    }
    let jsCode = `import { defineComponent, toRefs, ref, h, reactive,shallowRef, getCurrentInstance, resolveComponent,resolveDirective, withDirectives,provide } from 'vue';

    //获取扩展参数
function getExtendParams(extendId,context={}){
  const extendList=${
    extendData && extendData.length > 0
      ? JSON.stringify(extendData).replaceAll('"@', '').replaceAll('@"', '').replaceAll('\\n', '')
      : '[]'
  }
  let extendParams={};
  if(extendId){
    extendList.forEach(item=>{
      if(item.id===extendId){
        extendParams[item.key]=item.value;
      }
    })
  }
  return extendParams;
}
    export default defineComponent({
      setup(props){
        let app_code,page_id;
        let hash = window.location.hash;
        if (hash && hash.length > 0) {
          hash = window.location.hash.split('?')[0];
          let hashList = hash.split('/');
          if (hashList && hashList.length >= 3) {
            page_id = hashList[hashList.length - 1];
            app_code = hashList[hashList.length - 2];
          }
        }
        const tempData={};
        const instance = getCurrentInstance();
        const multiTab = useMultiTab();
        const businessId=instance.proxy.$route.query.businessId;
        let bizName=instance.proxy.$route.query.bizName||'详情';
        if (bizName==='修改') {
          bizName='编辑';
        }
        const status=instance.proxy.$route.query.statusName||'';
        const onlyShow=!['审核','audit'].includes(bizName);
        const editable = ${config.type === this.pageTypes.detail ? 'false' : 'true'};
        const childStep=resolveComponent('sfc-step');
        const fieldAuth=reactive([]);
        const fieldAuthObj=reactive({});
        const busObj=reactive({});
        provide('fieldAuth',fieldAuth);
        provide('fieldAuthObj',fieldAuthObj);
        provide('getExtendParams',getExtendParams);
        provide('busObj',busObj);
        const state = reactive({
          formData:{},
          businessId,
          onlyShow,
          pageData:{},
          handler:{
            ${handlers.join('\n,\n')}
          },
          ${stateData.join('\n,\n')}
        });
        const localQuery=JSON.parse(JSON.stringify(instance.proxy.$route.query||{}));
        const context={
          formData:state.formData,
          router:instance.proxy.$router,
          route:instance.proxy.$route,
          query:localQuery,
          emit:instance.proxy.$emit,
          message:instance.proxy.$message,
          notify:instance.proxy.$notify,
          alert:instance.proxy.$alert,
          loading:instance.proxy.$loading,
          showSfcUpload:instance.proxy.$showSfcUpload,
          showLog:instance.proxy.$showLog
        };
        onMounted(async ()=>{
          setTimeout(() => {
            Object.assign(context,{
              data:state.pageData,
              handler:state.handler,
              refs:instance.proxy.$refs,
              })
          }, 100);
          let businessConfigCode='';
          let permissBusinessId=businessId;
          if (busObj.businessCode) {
            businessConfigCode=busObj.businessCode;
          } else {
          const busType = instance.proxy.$route.query.busType ?? 'ADD';
          if (['CHANGE','CANCEL'].includes(busType) && bizName === '新建') {
            permissBusinessId=''
          }
          let ruleRes=[];
          try{
              ruleRes = await instance.proxy.$lowCodeRequest.fetchPageRulesAsync(page_id, busType); 
            } catch(ex){}
            if(ruleRes && ruleRes.list && ruleRes.list.length > 0){
              businessConfigCode = ruleRes.list[0].bus_code;
              busObj.businessCode = businessConfigCode;
            }
          }
          window.$http.post('/as/'+app_code+'/getDataPermission',{page_id,businessId:permissBusinessId,businessConfigCode})
            .then(res=>{
              if(res&&res.list){
                fieldAuth.push(...res.list);
                res.list.forEach(auth =>{
                  if(auth&&auth.fieldName){
                    fieldAuthObj[auth.fieldName] = {...(auth||{})};
                  }
                })
              }
            })
        });
        const beforeAuditFn=async (event)=>{
          let businessJson={
            extendData:getExtendParams('${config.id ?? ''}')
          };
          if(event&&event.businessExecutionType==='AGREE'){
            if(fieldAuth&&fieldAuth.length>0){
              let writeFields=fieldAuth.filter(f=>f.write);
              if(writeFields&&writeFields.length>0&&instance.proxy.$refs.funiDetailRef){
                let writeFieldNames=writeFields.map(item=>item.fieldName);
                if(instance.proxy.$refs.funiDetailRef.contentRef&&instance.proxy.$refs.funiDetailRef.contentRef.stepRef){
                  let stepForms;
                  if(Array.isArray(instance.proxy.$refs.funiDetailRef.contentRef.stepRef)&&instance.proxy.$refs.funiDetailRef.contentRef.stepRef.length>0){
                    stepForms = instance.proxy.$refs.funiDetailRef.contentRef.stepRef;
                  } else if(!Array.isArray(Array.isArray(instance.proxy.$refs.funiDetailRef.contentRef.stepRef))) {
                    stepForms = [instance.proxy.$refs.funiDetailRef.contentRef.stepRef];
                  }
                  if (stepForms&&stepForms.length>0) {
                    let formJson;
                    let operateRemarkData={};
                    for(let index=0;index<stepForms.length;index++){
                    try{
                        let stepForm=stepForms[index];
                        let stepFormJson = await stepForm.submitForm();
                        debugger;
                        let stepOperateRemarkData = stepForm.getOperateRemarkData();
                        if (stepFormJson&&!formJson) {
                          formJson={};
                        }
                        if(stepOperateRemarkData&&!operateRemarkData){
                          operateRemarkData={};
                        }
                        [...(writeFieldNames||[]),'id','cid'].forEach(n=>{
                          if (formJson&&(formJson[n]===null||formJson[n]===undefined)) {
                            formJson[n]=stepFormJson[n];
                          }
                        })
                        if (!operateRemarkData.selectOption&&!operateRemarkData.labelProps) {
                          Object.assign(operateRemarkData,stepOperateRemarkData);
                        }
                        if(operateRemarkData&&operateRemarkData.selectOption){
                          Object.assign(operateRemarkData.selectOption,(stepOperateRemarkData||{}).selectOption);
                        }
                        if(operateRemarkData&&operateRemarkData.labelProps){
                          Object.assign(operateRemarkData.labelProps,(stepOperateRemarkData||{}).labelProps);
                        }
                        
                      } catch(ex){
                       instance.proxy.$refs.funiDetailRef.setCurrent(index);
                       throw ex;
                      }
                    }
                    let selectOption = operateRemarkData.selectOption;
                    let labelProps = operateRemarkData.labelProps;
                    if(formJson){
                      businessJson.id=formJson.id;
                      businessJson.cid=formJson.cid;
                      let remarks=[];
                      let remarkKeys=[];
                      Object.keys(fieldAuthObj).forEach(k => {
                        const v=fieldAuthObj[k];
                        if(v&&v.print){
                          remarkKeys.push(k);
                        }
                      });
                      if (writeFieldNames&&writeFieldNames.length>0) {
                        writeFieldNames.forEach(n=>{
                          if (n) {
                            businessJson[n]=formJson[n];
                            if(labelProps&&labelProps[n]){
                              let currentVal=formJson[n];
                              if (selectOption[n]) {
                                const currentValObj = selectOption[n].current.find(item => item.value === currentVal);
                                if (currentValObj) {
                                  currentVal = currentValObj.label;
                                }
                              }
                              if(['string', 'number', 'undefined'].includes(typeof currentVal)){
                                if (remarkKeys&&remarkKeys.length>0) {
                                  if (remarkKeys.includes(n)&&labelProps[n]) {
                                    remarks.push(labelProps[n]+':'+(currentVal?currentVal : '空'));
                                  }
                                } else if (labelProps[n]) {
                                  remarks.push(labelProps[n]+':'+(currentVal?currentVal : '空'));
                                }
                              }
                            }
                          }
                        })
                      }
                      if(remarks&&remarks.length>0){
                        businessJson.operateRemark='提交数据:'+remarks.join(',');
                      }
                    }
                  }
                }
              }
            }
            if(instance.proxy.$refs.funiDetailRef.contentRef&&instance.proxy.$refs.funiDetailRef.contentRef.stepRef){
              let mainStepForm;
              if(Array.isArray(instance.proxy.$refs.funiDetailRef.contentRef.stepRef)&&instance.proxy.$refs.funiDetailRef.contentRef.stepRef.length>0){
                mainStepForm = instance.proxy.$refs.funiDetailRef.contentRef.stepRef[0]
              } else if(!Array.isArray(Array.isArray(instance.proxy.$refs.funiDetailRef.contentRef.stepRef))) {
                mainStepForm = instance.proxy.$refs.funiDetailRef.contentRef.stepRef;
              }
              if (mainStepForm) {
                let workflowObj = mainStepForm.getWorkflowVar();
                let customAssigneeList = [];
                if (Array.isArray(workflowObj)&&workflowObj.length>0) {
                  customAssigneeList = workflowObj;
                  businessJson.customAssigneeList=customAssigneeList;
                } else if (workflowObj && (workflowObj.workflowUserId||workflowObj.workflowUserName)) {
                  customAssigneeList = [{ assigneeType: 'U', assigneeId: workflowObj.workflowUserId,assigneeName:workflowObj.workflowUserName }];
                  businessJson.customAssigneeList=customAssigneeList;
                }
              }
            }
          }
          return businessJson;
        }
        const onAudit=async (workflow={})=>{
          let mainStepForm;
          if(Array.isArray(instance.proxy.$refs.funiDetailRef.contentRef.stepRef)&&instance.proxy.$refs.funiDetailRef.contentRef.stepRef.length>0){
            mainStepForm = instance.proxy.$refs.funiDetailRef.contentRef.stepRef[0]
          } else if(!Array.isArray(Array.isArray(instance.proxy.$refs.funiDetailRef.contentRef.stepRef))) {
            mainStepForm = instance.proxy.$refs.funiDetailRef.contentRef.stepRef;
          }
          if(mainStepForm){
            await mainStepForm.afterSubmit(workflow);
          }
          multiTab.closeCurrentPage();${
            config.urls.list
              ? `instance.proxy.$router.push({
                path:'${config.urls.list}'
              });`
              : ''
          }
        }

        const handleTabChange=(tabIndex)=>{
          state.activeStepIndex=tabIndex;
        }
        return {
          ...toRefs(state),
          localQuery,
          tempData,
          beforeAuditFn,
          onAudit,
          handleTabChange
        }
      },
      mounted(){
      }
    })`;
    return beautifier.js(jsCode);
  },
  generateEditGlobalCss(config, beautifier) {
    let scssCode = ``;
    return beautifier.css(scssCode);
  },
  generateEditScopedScss(config, beautifier) {
    let scssCode = ``;
    return beautifier.css(scssCode);
  },
  generateBlankHtml(config, beautifier) {
    let htmlCode = `<div style="height100%;width:100%;background:#ffffff;padding:8px;">
    <sfc-blank :code="code" :urls="urls" :modelId="modelId"/>
</div>`;
    return htmlCode;
  },
  /**
   * pc 编辑页
   * @param {*} config
   * @param {*} beautifier
   * @returns
   */
  generateBlankJs(config, beautifier) {
    let stateData = [];
    if (config && config.steps && config.steps.length > 0) {
      let code = config.steps[0].code;
      let modelId = config.steps[0].modelId;
      let mainModelId = config.steps[0].modelId;
      stateData.push(`code:'${code}'`);
      stateData.push(`modelId:'${modelId}'`);
      stateData.push(`mainModelId:'${mainModelId}'`);
    }
    const urls = `urls:${config.urls ? JSON.stringify(config.urls) : `{}`}`;
    stateData.push(urls);
    let jsCode = `import { defineComponent, toRefs, ref, h, reactive,shallowRef, getCurrentInstance, resolveComponent,resolveDirective, withDirectives,provide } from 'vue';
    export default defineComponent({
      setup(props){
        let app_code,page_id;
        let hash = window.location.hash;
        if (hash && hash.length > 0) {
          hash = window.location.hash.split('?')[0];
          let hashList = hash.split('/');
          if (hashList && hashList.length >= 3) {
            page_id = hashList[hashList.length - 1];
            app_code = hashList[hashList.length - 2];
          }
        }
        const instance = getCurrentInstance();
        const state = reactive({
          ${stateData.join('\n,\n')}
        });
        const localQuery=JSON.parse(JSON.stringify(instance.proxy.$route.query||{}));
        const context={
          router:instance.proxy.$router,
          route:instance.proxy.$route,
          query:localQuery,
          emit:instance.proxy.$emit,
          message:instance.proxy.$message,
          notify:instance.proxy.$notify,
          alert:instance.proxy.$alert,
          loading:instance.proxy.$loading,
          showSfcUpload:instance.proxy.$showSfcUpload,
          showLog:instance.proxy.$showLog
        };
        onMounted(()=>{
          setTimeout(() => {
            Object.assign(context,{
              data:state.pageData,
              handler:state.handler,
              refs:instance.proxy.$refs,
              })
          }, 100);
        });
        return {
          ...toRefs(state),
          localQuery
        }
      },
      mounted(){
      }
    })`;
    return beautifier.js(jsCode);
  },
  generateBlankGlobalCss(config, beautifier) {
    let scssCode = ``;
    return beautifier.css(scssCode);
  },
  generateBlankScopedScss(config, beautifier) {
    let scssCode = ``;
    return beautifier.css(scssCode);
  },
  /**
   *
   * @param {*} funCode 代码块
   * @param {*} isSetup 是否再setup函数中
   * @param {*} formModelName 设置的模型名称
   * @param {*} isWatch 是否监听 监听用函数 其它用箭头函数
   * @param {*} isNest 是否嵌套
   * @returns 注入上下文后的代码块
   */
  injectContext(funCode, isSetup = true, formModelName = null, isWatch = false, isNest = false, isWorkFlow = true) {
    let stateName = isSetup ? 'state' : 'this';
    let tempDataName = isSetup ? 'tempData' : 'this.tempData';
    let proxyName = isSetup ? 'instance.proxy' : 'this';
    let paramStr = '...params';
    if (funCode) {
      let realCode = funCode.trim();
      if (funCode.indexOf('=>') > 0) {
        if (realCode.startsWith('(')) {
          realCode = `function ${realCode.replace('=>', '')}`;
        } else {
          realCode = `function (${realCode.replace('=>', ')')}`;
        }
        paramStr = realCode.substr(10, realCode.indexOf(')') - 10);
      } else {
        let startIndex = realCode.indexOf('(');
        if (startIndex >= 0) {
          realCode = `function ${realCode.substr(startIndex, realCode.length - startIndex)}`;
          paramStr = realCode.substr(10, realCode.indexOf(')') - 10);
        }
      }
      let code = `${isWatch ? `function (${paramStr}) {` : `(${paramStr}) => {`}${
        paramStr === '...params'
          ? ''
          : paramStr.includes('{')
          ? `const params = ${paramStr};`
          : `const params = {${paramStr}};`
      }
      const context={
        params:params,
        data:${stateName}.pageData,
        handler:${stateName}.handler,
        refs:${proxyName}.$refs,
        router:${proxyName}.$router,
        route:${proxyName}.$route,
        query:${isSetup ? 'localQuery' : 'this.localQuery'},
        emit:${proxyName}.$emit,
        message:${proxyName}.$message,
        notify:${proxyName}.$notify,
        alert:${proxyName}.$alert,
        loading:${proxyName}.$loading,
        showSfcUpload:${proxyName}.$showSfcUpload,
        showLog:${proxyName}.$showLog,
        ${formModelName ? `formData:${stateName}.${formModelName},` : ''}
        ${formModelName ? `stepData:${tempDataName}?.stepData,` : ''}
        ${formModelName && isWorkFlow ? `workflow:${tempDataName}?.workflow,` : ''}
      };
      ${isNest ? `Object.assign(context.params,params);` : `context.params=params;`}
      let realFun=${realCode.replaceAll('"', "'")};
      return realFun.bind({})(${paramStr === '...params' ? '' : paramStr});
    }`;
      return code;
    } else {
      return '()=>{}';
    }
  },
  /**
   * 给事件对象注入上下文
   * @param {*} objStr
   *
   */
  injectForObjStr(
    objStr,
    isSetup = true,
    formModelName = null,
    isWatch = false,
    realKeyList = [], //映射到真实key
    isNest = false,
    needContext = true, //是否需要上下文
    isWorkFlow = true
  ) {
    let resultStr = `{}`;
    let actions = [];
    if (objStr) {
      try {
        let listener = new Function(`return ${objStr}`)();
        if (listener) {
          for (let key in listener) {
            if (typeof listener[key] === 'function') {
              let realKey = key;
              if (realKeyList && realKeyList.length > 0) {
                let fIndex = realKeyList.findIndex(item => item.key === key);
                if (fIndex >= 0) {
                  realKey = realKeyList[fIndex].value;
                }
              }
              actions.push(
                `'${realKey}':${
                  needContext
                    ? this.injectContext(listener[key].toString(), isSetup, formModelName, isWatch, isNest, isWorkFlow)
                    : listener[key].toString()
                }`
              );
            } else if (typeof listener[key] === 'string') {
              actions.push(`'${key}':'${listener[key] ?? ''}'`);
            } else if (typeof listener[key] === 'object') {
              let valeStr = `${JSON.stringify(listener[key])}`.replaceAll(`"`, `'`);
              let objStr = `${key}:${valeStr}`;
              actions.push(objStr);
            } else {
              actions.push(`'${key}':${listener[key] ?? ''}`);
            }
          }
        }
      } catch (ex) {
        console.log(ex);
      }
    }
    if (actions && actions.length > 0) {
      resultStr = `{${actions.join(',')}}`;
    }
    return resultStr;
  },
  stringifyObj(obj, isSetup = true, formModelName = null, isWatch = false, isNest = false, isWorkFlow = true) {
    let cloneObj = {};
    if (obj) {
      for (let key in obj) {
        if (typeof obj[key] === 'string') {
          let value;
          try {
            value = new Function(`return ${obj[key]}`)();
            // eslint-disable-next-line no-empty
          } catch (ex) {}
          if (typeof value === 'function') {
            let funCode = `@${this.injectContext(obj[key], isSetup, formModelName, isWatch, isNest, isWorkFlow)}@`;
            cloneObj[key] = funCode;
          } else if (typeof value === 'object') {
            console.log('obj', obj[key]);
          } else {
            cloneObj[key] = obj[key];
          }
        } else if (Array.isArray(obj[key])) {
          let cloneArray = [];
          if (obj[key] && obj[key].length > 0) {
            obj[key].forEach(item => {
              if (typeof item === 'object') {
                cloneArray.push(this.stringifyObj(item, isSetup, formModelName, isWatch, isNest, isWorkFlow));
              } else {
                cloneArray.push(item);
              }
            });
          }
          cloneObj[key] = cloneArray;
        } else if (typeof obj[key] === 'object') {
          cloneObj[key] = this.stringifyObj(obj[key], isSetup, formModelName, isWatch, isNest, isWorkFlow);
        } else {
          cloneObj[key] = obj[key];
        }
      }
    }
    return cloneObj;
  },
  /**
   *
   * @param {*} funConfig 函数配置
   * @param {*} isNest 是否嵌套的逻辑
   * @returns 函数代码
   */
  generateEvent(funConfig, isNest = false, argName = '') {
    let funCode = '';
    if (funConfig && funConfig.snippets) {
      const snippets = funConfig.snippets;
      if (isNest) {
        funCode = `${snippets}`;
      } else {
        funCode = `(${argName})=>{${snippets}}`;
      }
      return funCode;
    }
    let assignExpList = []; //赋值表达式
    let paramExpList = []; //组装参数表达式
    let exeExpList = [];
    let successCode = '';
    let failCode = '';
    if (funConfig.success) {
      successCode = this.generateEvent(funConfig.success, true);
    }
    if (funConfig.fail) {
      failCode = this.generateEvent(funConfig.fail, true);
    }
    if (funConfig.assignExpList && funConfig.assignExpList.length > 0) {
      funConfig.assignExpList.forEach(item => {
        let expStr = `${item.declaration ?? ''} ${item.leftVal} ${item.operator} ${item.rightVal ?? ''};`;
        assignExpList.push(expStr);
      });
    }

    if (funConfig.paramExpList && funConfig.paramExpList.length >= 0) {
      funConfig.paramExpList.forEach(item => {
        let exp = getExpressionCode(item.rightVal);
        let rightVal = exp ? exp : item.rightVal;
        let expStr = `param.${item.prop} = ${rightVal};`;
        paramExpList.push(expStr);
      });
    }
    if (funConfig.exeExpList && funConfig.exeExpList.length > 0) {
      let appendSuccess = true; //是否把成功时代码贴到后面
      funConfig.exeExpList.forEach(item => {
        let rightExp = '';
        if (item.type === 'dialog') {
          if (item.dialogName && item.dialogName.includes(':')) {
            const [tabIndex, dialogName] = item.dialogName.split(':');
            rightExp = `instance.proxy.$refs.funiDetailRef.contentRef.stepRef[${tabIndex}].exeChildMethod('${dialogName}','${item.methodName}',param);`;
          } else {
            rightExp = `context.refs.${item.dialogName}.${item.methodName}(param);`;
          }
        } else if (item.type === 'router') {
          rightExp = `context.router.${item.methodName}({path:'${item.pageid}',query:param})`;
        } else if (item.type === 'link') {
          let routerStr;
          if (item.routerName) {
            routerStr = `name:'${item.routerName}'`;
          } else if (item.routerPath) {
            routerStr = `path:'${item.routerPath}'`;
          }
          rightExp = `context.router.${item.methodName}({${routerStr},query:param})`;
        } else if (['alert', 'message', 'notify'].indexOf(item.type) >= 0) {
          if (item.type === 'alert') {
            rightExp = `
            const {title,message} = param;
            context['${item.type}'](message,title);
            `;
          } else if (item.type === 'notify') {
            rightExp = `
            context['${item.type}'](param);
            `;
          } else {
            rightExp = `
            const {message} = param;
            context['${item.type}'][param.type](message);
            `;
          }
        } else if (item.type === 'log') {
          rightExp = `console.log(param)`;
        } else if (item.type === 'comp' && item.component) {
          if (item.component.method && item.component.method.value === 'submitForm') {
            appendSuccess = false;
            rightExp = `${item.component.method.value}(param)
            .then(res => {
              if (res.valid) {
                context.emit('ok', res.data);
                ${successCode}
              }
              else if (res.errors) {
                let messages = [];
                for (let err in res.errors) {
                  if (res.errors[err] && res.errors[err] && res.errors[err].length >
                    0) {
                    messages.push(...res.errors[err].map(item => item.message));
                  }
                }
                if (messages.length > 0) {
                  context.notify(messages[0]);
                }
                else {
                  context.notify('请更正数据');
                }
              }
              else {
                context.notify('请更正数据');
              }
            })
            .catch(err=>{
              ${failCode}
            });`;
          } else {
            rightExp = `context.refs.${item.component.value}.${item.component.method.value}(param);`;
          }
        }
        let expStr = `${item.needReturn ? 'return' : ''}${rightExp}`;
        exeExpList.push(expStr);
      });
      //如果需要success且有代码
      if (appendSuccess && successCode) {
        exeExpList.push(successCode);
      }
      funCode = isNest
        ? `
      ${assignExpList.join('')}
        ${paramExpList.join('')}
        ${exeExpList.join('')}
      `
        : `(${argName})=>{
        ${assignExpList.join('')}
        let param={};
        ${paramExpList.join('')}
        ${exeExpList.join('')}
      }`;
    } else if (funConfig.logicStringVal && funConfig.logicStringVal.length > 0) {
      funCode = isNest
        ? `${assignExpList.join('')}
        ${paramExpList.join('')}
        if(${funConfig.logicStringVal}){
          ${successCode}
        } else {
          ${failCode}
        }`
        : `(${argName})=>{
        ${assignExpList.join('')}
        let param={};
        ${paramExpList.join('')}
        if(${funConfig.logicStringVal}){
          ${successCode}
        } else {
          ${failCode}
        }
      }`;
    } else if (funConfig.logicExpList && funConfig.logicExpList.length > 0) {
      let logicExpList = [];
      funConfig.logicExpList.forEach(item => {
        let logicExpStr = `${item.leftVal}${item.operator}${item.rightVal}${item.logicOperator}`;
        logicExpList.push(logicExpStr);
      });
      funCode = isNest
        ? `${assignExpList.join('')}
        ${paramExpList.join('')}
        if(${logicExpList.join('')}){
          ${successCode}
        } else {
          ${failCode}
        }`
        : `(${argName})=>{
        ${assignExpList.join('')}
        let param={};
        ${paramExpList.join('')}
        if(${logicExpList.join('')}){
          ${successCode}
        } else {
          ${failCode}
        }
      }`;
    } else if (funConfig.request) {
      let requestStr = `window.$http.${funConfig.request.method ?? 'post'}(${funConfig.request.url},param)
      .then(${funConfig.request.responseName ?? '()'}=>{
        ${successCode}
      })
      .catch(${funConfig.request.errorName ?? '()'}=>{
        ${failCode}
      })
      `;
      funCode = requestStr;
    } else if (assignExpList && assignExpList.length > 0) {
      funCode = isNest
        ? `${assignExpList.join('')}
        ${paramExpList.join('')}`
        : `(${argName})=>{
        ${assignExpList.join('')}
        let param={};
        ${paramExpList.join('')}
      }`;
    }
    return funCode;
  },
  /**
   * 编辑配置转换详情配置
   * @param {*} editConfig
   * @returns
   */
  convertDetailConfig(editConfig) {
    let detailConfig = editConfig;
    if (detailConfig && detailConfig.steps && detailConfig.steps.length) {
      const needReplace = [
        { editCom: 'input', detailCom: 'funi-label' },
        { editCom: 'textarea', detailCom: 'funi-label' },
        { editCom: 'time', detailCom: 'funi-label' },
        { editCom: 'number', detailCom: 'funi-label' },
        { editCom: 'date', detailCom: 'funi-label' }
      ]; //需要替换的组件
      detailConfig.steps.forEach(step => {
        if (step.configJson) {
          let config;
          try {
            config = JSON.parse(step.configJson);
          } catch (ex) {
            console.log(ex);
          }
          if (config && config.widgetList && config.widgetList.length > 0) {
            let realConfig = {
              widgetList: replaceAllWidget(config.widgetList, needReplace),
              formConfig: config.formConfig
            };
            if (realConfig) {
              const configStr = JSON.stringify(realConfig);
              step.configJson = JSON.stringify(configStr);
              step.config = this.encrypt(configStr);
            }
          }
        }
      });
    }
    return detailConfig;
  },
  /**
   * 使用pageId,url
   * @param {*} id
   * @param {*} url
   * @returns
   */
  createAsyncComponent(appId, pageId, url = '/as/pageDesign/findByPageId/') {
    let id = pageId;
    return defineAsyncComponent(() => {
      return new Promise((resovle, reject) => {
        if (!id) {
          let app_code, page_id;
          var hash = window.location.hash;
          if (hash && hash.length > 0) {
            hash = window.location.hash.split('?')[0];
            let hashList = hash.split('/');
            if (hashList && hashList.length >= 3) {
              id = hashList[hashList.length - 1];
            }
          }
        }
        window.$http
          .fetch(`${url}${id}`, { id, appId })
          .then(pageData => {
            if (pageData) {
              const jsCode = this.decrypt(pageData.design_content);
              let component = this.jsToComponent(jsCode);
              resovle(component);
            } else {
              reject(new Error('网络错误'));
            }
          })
          .catch(err => {
            reject(err);
          });
      });
    });
  }
};
