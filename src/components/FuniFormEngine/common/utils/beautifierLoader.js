/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-24 14:06:14
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-24 14:28:43
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\common\utils\beautifierLoader.js
 * @Description: 
 */
import {loadRemoteScript} from "./util";
import {BEAUTIFIER_PATH} from "./config";

let beautifierObj

export const beautifierOpts = {
  html: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'separate',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  },
  js: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'normal',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  },
  css: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'normal',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  }
}

export default function loadBeautifier(callback) {
  if (beautifierObj) {
    callback(beautifierObj)
    return
  }

  loadRemoteScript(BEAUTIFIER_PATH, () => {
    // eslint-disable-next-line no-undef
    beautifierObj = beautifier  //beautifier为全局对象
    callback(beautifierObj)
  })
}
