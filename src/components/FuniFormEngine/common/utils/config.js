/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-28 11:25:37
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-04-19 19:01:10
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\common\utils\config.js
 * @Description:
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
export const VARIANT_FORM_VERSION = '3.0.9';

//export const MOCK_CASE_URL = 'https://www.fastmock.site/mock/2de212e0dc4b8e0885fea44ab9f2e1d0/vform/'
export const MOCK_CASE_URL = 'https://ks3-cn-beijing.ksyuncs.com/vform-static/vcase/';

//export const ACE_BASE_PATH = 'public/lib/ace/src-min-noconflict'
export const ACE_BASE_PATH = 'https://ks3-cn-beijing.ksyun.com/vform2021/ace-mini';

export const BEAUTIFIER_PATH = process.env.NODE_ENV === 'production' ? 'js/beautifier.min.js' : '/js/beautifier.min.js';
