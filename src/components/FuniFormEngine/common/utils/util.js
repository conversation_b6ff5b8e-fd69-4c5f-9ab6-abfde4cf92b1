import Clipboard from 'clipboard';
export const FormulaRegex = /(@var|@func){@id{(.*?)}:@expression{(.*?)}:@label{(.*?)}}/g;

const curdTypes = ['sfc-draggable-curd', 'funi-show-curd'];

export function isNull(value) {
  return value === null || value === undefined;
}

export function isNotNull(value) {
  return value !== null && value !== undefined;
}

export function isEmptyStr(str) {
  //return (str === undefined) || (!str) || (!/[^\s]/.test(str));
  return str === undefined || (!str && str !== 0 && str !== '0') || !/[^\s]/.test(str);
}

export function validateDs(requestOtherParam, fw = null, designerConfig = {}) {
  let valid = false;
  if (
    ['funi-edit-curd', 'sfc-draggable-curd', 'funi-show-curd'].includes(fw.type) &&
    !designerConfig.hasModel &&
    !fw.options.selfDataSoure
  ) {
    valid = true;
  }
  let message;
  if (requestOtherParam) {
    if (requestOtherParam.type === 'model') {
      valid = requestOtherParam.data && requestOtherParam.data.model_id;
      message = '模型';
      if (valid && fw && ['funi-select', 'sfc-select'].includes(fw.type)) {
        valid = requestOtherParam.data && requestOtherParam.data.labelKey;
        message = '显示字段';
      }
    } else if (requestOtherParam.type === 'typeCode') {
      valid = false;
    } else if (requestOtherParam.type === 'custom') {
      valid = false;
    } else if (requestOtherParam.type === 'api') {
      valid = requestOtherParam.data && requestOtherParam.data.api_url;
      message = 'APIs';
    } else if (requestOtherParam.type === 'sql') {
      valid = requestOtherParam.data && requestOtherParam.data.sql_id;
      message = 'SQL';
    }
  }
  return { valid, message };
}

export const generateId = function () {
  return Math.floor(Math.random() * 100000 + Math.random() * 20000 + Math.random() * 5000);
};

export const deepClone = function (origin) {
  if (origin === undefined) {
    return undefined;
  }

  return JSON.parse(JSON.stringify(origin));
};

export const overwriteObj = function (obj1, obj2) {
  /* 浅拷贝对象属性，obj2覆盖obj1 */
  // for (let prop in obj2) {
  //   if (obj2.hasOwnProperty(prop)) {
  //     obj1[prop] = obj2[prop]
  //   }
  // }

  Object.keys(obj2).forEach(prop => {
    obj1[prop] = obj2[prop];
  });
};

export const addWindowResizeHandler = function (handler) {
  let oldHandler = window.onresize;
  if (typeof window.onresize != 'function') {
    window.onresize = handler;
  } else {
    window.onresize = function () {
      oldHandler();
      handler();
    };
  }
};

const createStyleSheet = function () {
  let head = document.head || document.getElementsByTagName('head')[0];
  let style = document.createElement('style');
  style.type = 'text/css';
  head.appendChild(style);
  return style.sheet;
};

export const insertCustomCssToHead = function (cssCode, formId = '') {
  let head = document.getElementsByTagName('head')[0];
  let oldStyle = document.getElementById('vform-custom-css');
  if (oldStyle) {
    head.removeChild(oldStyle); //先清除后插入！！
  }
  if (formId) {
    oldStyle = document.getElementById('vform-custom-css' + '-' + formId);
    !!oldStyle && head.removeChild(oldStyle); //先清除后插入！！
  }

  let newStyle = document.createElement('style');
  newStyle.type = 'text/css';
  newStyle.rel = 'stylesheet';
  newStyle.id = formId ? 'vform-custom-css' + '-' + formId : 'vform-custom-css';
  try {
    newStyle.appendChild(document.createTextNode(cssCode));
  } catch (ex) {
    newStyle.styleSheet.cssText = cssCode;
  }

  head.appendChild(newStyle);
};

export const insertGlobalFunctionsToHtml = function (functionsCode, formId = '') {
  let bodyEle = document.getElementsByTagName('body')[0];
  let oldScriptEle = document.getElementById('v_form_global_functions');
  !!oldScriptEle && bodyEle.removeChild(oldScriptEle); //先清除后插入！！
  if (formId) {
    oldScriptEle = document.getElementById('v_form_global_functions' + '-' + formId);
    !!oldScriptEle && bodyEle.removeChild(oldScriptEle); //先清除后插入！！
  }

  let newScriptEle = document.createElement('script');
  newScriptEle.id = formId ? 'v_form_global_functions' + '-' + formId : 'v_form_global_functions';
  newScriptEle.type = 'text/javascript';
  newScriptEle.innerHTML = functionsCode;
  bodyEle.appendChild(newScriptEle);
};

export const optionExists = function (optionsObj, optionName) {
  if (!optionsObj) {
    return false;
  }

  return Object.keys(optionsObj).indexOf(optionName) > -1;
};

export const loadRemoteScript = function (srcPath, callback) {
  /*加载远程js，加载成功后执行回调函数*/
  let sid = encodeURIComponent(srcPath);
  let oldScriptEle = document.getElementById(sid);

  if (!oldScriptEle) {
    let s = document.createElement('script');
    s.src = srcPath;
    s.id = sid;
    document.body.appendChild(s);

    s.onload = s.onreadystatechange = function (_, isAbort) {
      /* 借鉴自ace.js */
      if (isAbort || !s.readyState || s.readyState === 'loaded' || s.readyState === 'complete') {
        s = s.onload = s.onreadystatechange = null;
        if (!isAbort) {
          callback();
        }
      }
    };
  }
};

export function traverseFieldWidgets(widgetList, handler, parent = null) {
  if (!widgetList) {
    return;
  }

  widgetList.map(w => {
    if (w.formItemFlag) {
      handler(w, parent);
    } else if (w.type === 'grid') {
      handler(w, parent, true);
      w.cols.map(col => {
        traverseFieldWidgets(col.widgetList, handler, w);
      });
    } else if (w.type === 'table') {
      handler(w, parent, true);
      w.rows.map(row => {
        row.cols.map(cell => {
          traverseFieldWidgets(cell.widgetList, handler, w);
        });
      });
    } else if (w.type === 'tab') {
      handler(w, parent, true);
      w.tabs.map(tab => {
        traverseFieldWidgets(tab.widgetList, handler, w);
      });
    } else if (w.type === 'sub-form') {
      handler(w, parent, true);
      traverseFieldWidgets(w.widgetList, handler, w);
    } else if (w.category === 'container') {
      //自定义容器
      handler(w, parent, true);
      traverseFieldWidgets(w.widgetList, handler, w);
    } else if (w.type === 'html-text') {
      handler(w, parent);
    } else if (w.options && (w.options.listener || w.options.hiddenCode)) {
      handler(w, parent);
    }
  });
}

export function traverseContainerWidgets(widgetList, handler) {
  if (!widgetList) {
    return;
  }

  widgetList.map(w => {
    if (w.category === 'container') {
      handler(w);
    }

    if (w.type === 'grid') {
      w.cols.map(col => {
        traverseContainerWidgets(col.widgetList, handler);
      });
    } else if (w.type === 'table') {
      w.rows.map(row => {
        row.cols.map(cell => {
          traverseContainerWidgets(cell.widgetList, handler);
        });
      });
    } else if (w.type === 'tab') {
      w.tabs.map(tab => {
        traverseContainerWidgets(tab.widgetList, handler);
      });
    } else if (w.type === 'sub-form') {
      traverseContainerWidgets(w.widgetList, handler);
    } else if (w.category === 'container') {
      //自定义容器
      traverseContainerWidgets(w.widgetList, handler);
    }
  });
}

export function traverseAllWidgets(widgetList, handler) {
  if (!widgetList) {
    return;
  }

  widgetList.map(w => {
    handler(w);

    if (w.type === 'grid') {
      w.cols.map(col => {
        handler(col);
        traverseAllWidgets(col.widgetList, handler);
      });
    } else if (w.type === 'table') {
      w.rows.map(row => {
        row.cols.map(cell => {
          handler(cell);
          traverseAllWidgets(cell.widgetList, handler);
        });
      });
    } else if (w.type === 'tab') {
      w.tabs.map(tab => {
        traverseAllWidgets(tab.widgetList, handler);
      });
    } else if (w.type === 'sub-form') {
      traverseAllWidgets(w.widgetList, handler);
    } else if (w.category === 'container') {
      //自定义容器
      traverseAllWidgets(w.widgetList, handler);
    }
  });
}

function handleWidgetForTraverse(widget, handler) {
  if (widget.category) {
    traverseFieldWidgetsOfContainer(widget, handler);
  } else if (widget.formItemFlag) {
    handler(widget);
  }
}

/**
 * 遍历容器内的字段组件
 * @param con
 * @param handler
 */
export function traverseFieldWidgetsOfContainer(con, handler) {
  if (con.type === 'grid') {
    con.cols.forEach(col => {
      col.widgetList.forEach(cw => {
        handleWidgetForTraverse(cw, handler);
      });
    });
  } else if (con.type === 'table') {
    con.rows.forEach(row => {
      row.cols.forEach(cell => {
        cell.widgetList.forEach(cw => {
          handleWidgetForTraverse(cw, handler);
        });
      });
    });
  } else if (con.type === 'tab') {
    con.tabs.forEach(tab => {
      tab.widgetList.forEach(cw => {
        handleWidgetForTraverse(cw, handler);
      });
    });
  } else if (con.type === 'sub-form') {
    con.widgetList.forEach(cw => {
      handleWidgetForTraverse(cw, handler);
    });
  } else if (con.category === 'container') {
    //自定义容器
    con.widgetList.forEach(cw => {
      handleWidgetForTraverse(cw, handler);
    });
  }
}

/**
 * 获取所有字段组件
 * @param widgetList
 * @returns {[]}
 */
export function getAllFieldWidgets(widgetList) {
  if (!widgetList) {
    return [];
  }

  let result = [];
  let handlerFn = w => {
    if (w.formItemFlag) {
      let propField = {
        type: w.type,
        name: w.options.name,
        label: w.options.label,
        field: w
      };
      result.push(propField);
      if (curdTypes.includes(w.type)) {
        let curdWidgetList = [];
        if (w.type === 'sfc-draggable-curd') {
          curdWidgetList = w.widgetList;
        } else if (w.type === 'funi-show-curd') {
          if (w.options && w.options.columns && w.options.columns.length > 0) {
            curdWidgetList = w.options.columns.map(col => {
              return {
                type: 'funi-show-curd-col',
                formItemFlag: true,
                options: {
                  name: col.prop,
                  label: col.label
                }
              };
            });
          }
        }
        if (curdWidgetList && curdWidgetList.length > 0) {
          let children = [];
          let childrenFn = cw => {
            if (cw.formItemFlag) {
              children.push({
                type: cw.type,
                name: `${w.options.name}.${cw.options.name}`,
                label: `${w.options.label}.${cw.options.label}`,
                field: cw
              });
            }
          };
          traverseFieldWidgets(curdWidgetList, childrenFn);
          result.push(...children);
        }
      }
    }
  };
  traverseFieldWidgets(widgetList, handlerFn);
  return result;
}

/**
 * 获取所有容器组件
 * @param widgetList
 * @returns {[]}
 */
export function getAllContainerWidgets(widgetList) {
  if (!widgetList) {
    return [];
  }

  let result = [];
  let handlerFn = w => {
    result.push({
      type: w.type,
      name: w.options.name,
      container: w
    });
  };
  traverseContainerWidgets(widgetList, handlerFn);

  return result;
}

export function copyToClipboard(content, clickEvent, $message, successMsg, errorMsg) {
  const clipboard = new Clipboard(clickEvent.target, {
    text: () => content
  });

  clipboard.on('success', () => {
    $message.success(successMsg);
    clipboard.destroy();
  });

  clipboard.on('error', () => {
    $message.error(errorMsg);
    clipboard.destroy();
  });

  clipboard.onClick(clickEvent);
}

export function getQueryParam(variable) {
  let query = window.location.search.substring(1);
  let vars = query.split('&');
  for (let i = 0; i < vars.length; i++) {
    let pair = vars[i].split('=');
    if (pair[0] == variable) {
      return pair[1];
    }
  }

  return undefined;
}

export function getDefaultFormConfig() {
  return {
    modelId: '',
    modelName: 'formData',
    refName: 'vForm',
    rulesName: 'rules',
    labelWidth: 90,
    labelPosition: 'right',
    size: '',
    labelAlign: 'label-right-align',
    cssCode: '',
    customClass: '',
    functions: '', //全局函数
    layoutType: 'PC',
    jsonVersion: 3,

    onFormCreated: '',
    onFormMounted: '',
    watch: '',
    dialogs: [],
    validators: []
  };
}

export function buildDefaultFormJson() {
  return {
    widgetList: [],
    formConfig: deepClone(getDefaultFormConfig())
  };
}

export function findAllWidget(widgetList) {
  let realWidgetList = widgetList.filter(wgt => !['container'].includes(wgt.category));
  if (!realWidgetList) {
    realWidgetList = [];
  }
  widgetList.forEach(wgt => {
    if (wgt.cols && wgt.cols.length > 0) {
      wgt.cols.forEach(col => {
        if (col.widgetList && col.widgetList.length > 0) {
          let childrenWidget = findAllWidget(col.widgetList);
          realWidgetList.push(...childrenWidget);
        }
      });
    } else if (wgt.type === 'table' && wgt.rows && wgt.rows.length > 0) {
      wgt.rows.forEach(row => {
        if (row.cols && row.cols.length > 0) {
          row.cols.forEach(col => {
            if (col.widgetList && col.widgetList.length > 0) {
              let childrenWidget = findAllWidget(col.widgetList);
              realWidgetList.push(...childrenWidget);
            }
          });
        }
      });
    } else if (wgt.type === 'tab' && wgt.tabs && wgt.tabs.length > 0) {
      wgt.tabs.forEach(tab => {
        let childrenWidget = findAllWidget(tab.widgetList);
        realWidgetList.push(...childrenWidget);
      });
    } else if (wgt.type === 'card' && wgt.widgetList && wgt.widgetList.length > 0) {
      let childrenWidget = findAllWidget(wgt.widgetList);
      realWidgetList.push(...childrenWidget);
    }
  });
  return realWidgetList;
}

//包含表单自身需要完成初始化的组件数量,用于判断表单是否初始化完成
export function findAllInitLength(widgetList) {
  let length = 1;
  let list = findAllWidget(widgetList);
  if (list && list.length > 0) {
    let needInits = list.filter(item => ['sfc-draggable-curd', 'funi-edit-curd'].includes(item.type));
    if (needInits) {
      length = needInits.length + 1;
    }
  }
  return length;
}

export function findAllField(widgetList) {
  const allWidgetList = findAllWidget(widgetList);
  const fieldList = allWidgetList
    .filter(wgt => wgt.formItemFlag && wgt.options && wgt.options.name)
    .map(item => {
      let field = {
        id: item.id,
        name: item.options?.name,
        label: item.options?.label,
        type: item.type
      };
      if (item.widgetList && item.widgetList.length > 0) {
        field.children = item.widgetList
          .filter(childWgt => childWgt.formItemFlag && childWgt.options && childWgt.options.name)
          .map(childItem => {
            return {
              id: childItem.id,
              name: childItem.options?.name,
              label: childItem.options?.label,
              type: childItem.type
            };
          });
      } else if (
        item.type === 'funi-show-curd' &&
        item.options &&
        item.options.columns &&
        item.options.columns.length > 0
      ) {
        field.children = item.options.columns.map(col => {
          return {
            id: `${item.id}-${col.prop}`,
            name: col.prop,
            label: col.label,
            type: 'funi-show-curd-column'
          };
        });
      }
      return field;
    });
  return fieldList;
}

export function replaceAllWidget(widgetList, needReplace = []) {
  widgetList.forEach(wgt => {
    if (!['container'].includes(wgt.category)) {
      let fIndex = needReplace.findIndex(item => item.editCom === wgt.type);
      if (fIndex >= 0) {
        wgt.type = needReplace[fIndex].detailCom ?? 'funi-label';
        wgt.icon = needReplace[fIndex].icon ?? 'funi-label';
        wgt.optons = {
          name: wgt.options?.name,
          label: wgt.options?.label,
          labelAlign: wgt.options?.labelAlign,
          defaultValue: wgt.options?.defaultValue,
          modelField: wgt.options?.modelField,
          placeholder: wgt.options?.placeholder,
          size: wgt.options?.size,
          labelWidth: wgt.options?.labelWidth,
          labelHidden: wgt.options?.labelHidden,
          hidden: wgt.options?.hidden,
          customClass: wgt.options?.customClass,
          labelIconClass: wgt.options?.labelIconClass,
          labelIconPosition: wgt.options?.labelIconPosition,
          labelTooltip: wgt.options?.labelTooltip,
          validation: wgt.options?.validation,
          validationHint: wgt.options?.validationHint
        };
      }
    }
  });

  widgetList.forEach(wgt => {
    if (wgt.cols && wgt.cols.length > 0) {
      wgt.cols.forEach(col => {
        if (col.widgetList && col.widgetList.length > 0) {
          replaceAllWidget(col.widgetList, needReplace);
        }
      });
    } else if (wgt.type === 'table' && wgt.rows && wgt.rows.length > 0) {
      wgt.rows.forEach(row => {
        if (row.cols && row.cols.length > 0) {
          row.cols.forEach(col => {
            if (col.widgetList && col.widgetList.length > 0) {
              replaceAllWidget(col.widgetList, needReplace);
            }
          });
        }
      });
    } else if (wgt.type === 'tab' && wgt.tabs && wgt.tabs.length > 0) {
      wgt.tabs.forEach(tab => {
        replaceAllWidget(tab.widgetList, needReplace);
      });
    } else if (wgt.type === 'card' && wgt.widgetList && wgt.widgetList.length > 0) {
      replaceAllWidget(wgt.widgetList, needReplace);
    }
  });
  return widgetList;
}

export function findSpecialObjWidget(widgetList) {
  let modelIdList = [];
  let sqlList = [];
  let permissionList = [];
  let formatList = [];
  let realWidgetList = findAllWidget(widgetList);
  if (realWidgetList && realWidgetList.length > 0) {
    realWidgetList.forEach(wgt => {
      let sqlData;
      if (
        wgt.type === 'funi-select' &&
        wgt.options &&
        wgt.options.modelOption &&
        wgt.options.modelOption.type === 'sql' &&
        wgt.options.modelOption.data &&
        wgt.options.modelOption.data.sql_url &&
        wgt.options.modelOption.data.id
      ) {
        sqlData = wgt.options.modelOption.data;
      } else if (
        (wgt.type === 'funi-edit-curd' || wgt.type === 'funi-show-curd') &&
        wgt.options &&
        wgt.options.requestOtherParam &&
        wgt.options.requestOtherParam.type === 'sql' &&
        wgt.options.requestOtherParam.data &&
        wgt.options.requestOtherParam.data.sql_url &&
        wgt.options.requestOtherParam.data.id
      ) {
        sqlData = wgt.options.requestOtherParam.data;
      }
      if (sqlData) {
        sqlList.push({
          id: sqlData.id,
          content: sqlData.sql_url,
          component_id: sqlData.id,
          params: JSON.stringify(sqlData.requestOtherParams || [])
        });
      }
      if (wgt.type === 'funi-edit-curd') {
        if (wgt.options && wgt.options.hasAddAuth && wgt.options.addPermission) {
          permissionList.push({ ...wgt.options.addPermission });
        }
        if (wgt.options && wgt.options.hasMinusAuth && wgt.options.minusPermission) {
          permissionList.push({ ...wgt.options.minusPermission });
        }
      }
      if (wgt.type === 'funi-show-curd') {
        if (wgt.options && wgt.options.buttons && wgt.options.buttons.length > 0) {
          wgt.options.buttons.forEach(btn => {
            if (btn.permission) {
              permissionList.push({ ...btn.permission });
            }
          });
        }
        if (wgt.options && wgt.options.actions && wgt.options.actions.length > 0) {
          wgt.options.actions.forEach(btn => {
            if (btn.permission) {
              permissionList.push({ ...btn.permission });
            }
          });
        }
      } else if (
        (wgt.type === 'funi-edit-curd' || wgt.type === 'funi-show-curd') &&
        wgt.options &&
        wgt.options.requestOtherParam &&
        wgt.options.requestOtherParam.type === 'model' &&
        wgt.options.requestOtherParam.data &&
        wgt.options.requestOtherParam.data.model_id
      ) {
        modelIdList.push(wgt.options.requestOtherParam.data.model_id);
      }
      if (wgt.type === 'sfc-org') {
        formatList.push({ name: wgt.options.name });
      }

      if (wgt.type === 'sfc-user') {
        formatList.push({ name: wgt.options.name });
      }
    });
  }
  return { modelIdList, sqlList, permissionList, formatList };
}

export function randomString(randomLen, min, max) {
  var str = '',
    range = min,
    arr = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      'a',
      'b',
      'c',
      'd',
      'e',
      'f',
      'g',
      'h',
      'i',
      'j',
      'k',
      'l',
      'm',
      'n',
      'o',
      'p',
      'q',
      'r',
      's',
      't',
      'u',
      'v',
      'w',
      'x',
      'y',
      'z',
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'I',
      'J',
      'K',
      'L',
      'M',
      'N',
      'O',
      'P',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
      'Z'
    ];
  // 随机产生
  if (randomLen) {
    range = Math.round(Math.random() * (max - min)) + min;
  }
  for (var i = 0; i < range; i++) {
    let pos = Math.round(Math.random() * (arr.length - 1));
    str += arr[pos];
  }
  return str;
}

export function getExpressionCode(codeJson) {
  let codeObj = {};
  try {
    codeObj = JSON.parse(codeJson);
  } catch (ex) {
    console.log('ex', ex);
  }
  return codeObj.expression;
}

export function decodeFormula(formula) {
  const matches = [];
  while (1) {
    const match = FormulaRegex.exec(formula);
    if (!match) break;
    matches.push(match);
  }
  return matches.reduce(
    (prev, curr) => {
      const [detail, __, id, expression, label] = curr;
      return {
        label: prev.label.replace(detail, ` ${label}`).replace('( ', '(').trim(),
        expression: prev.expression
          .replace(detail, ` ${expression || id}`)
          .replace('( ', '(')
          .trim()
      };
    },
    { label: formula, expression: formula }
  );
}

export function isExpression(codeJson) {
  let isExpression = false;
  let codeObj = {};
  try {
    codeObj = JSON.parse(codeJson);
    isExpression = true;
  } catch (ex) {
    console.log('ex', ex);
    isExpression = false;
  }
  return isExpression;
}

export function isNotNullExpression(codeJson) {
  let isExpression = false;
  let codeObj = {};
  try {
    codeObj = JSON.parse(codeJson);
    isExpression = !!codeObj.expression;
  } catch (ex) {
    console.log('ex', ex);
    isExpression = false;
  }
  return isExpression;
}

//驼峰转短横线
export function toShortLineCase(camelCaseWord) {
  let shortLineWord = '';
  if (camelCaseWord && camelCaseWord.length > 0) {
    for (let i = 0; i < camelCaseWord.length; i++) {
      let strCode = camelCaseWord[i].charCodeAt();
      if (i != 0 && strCode >= 65 && strCode <= 90) {
        shortLineWord += `-${camelCaseWord[i]}`.toLowerCase();
      } else {
        shortLineWord += camelCaseWord[i].toLowerCase();
      }
    }
  }
  return shortLineWord;
}

export function typeToComponent(type) {
  const typeObj = {
    input: 'ElInput',
    textarea: 'ElInput',
    number: 'ElInputNumber',
    radio: 'SfcRadioGroup',
    checkbox: 'SfcCheckboxGroup',
    select: 'FuniSelect',
    time: 'ElTimePicker',
    'time-range': 'ElTimePicker',
    date: 'ElDatePicker',
    'date-range': 'ElDatePicker',
    switch: 'ElSwitch',
    rate: 'ElRate',
    color: 'ElColorPicker',
    slider: 'ElSlider',
    'picture-upload': 'SfcUpload',
    'file-upload': 'SfcUpload',
    'rich-editor': 'VueEditor',
    cascader: 'ElCascader',
    'funi-label': 'FuniLabel',
    'funi-group-title': 'FuniGroupTitle',
    'sfc-user': 'SfcUser',
    'sfc-org': 'SfcOrg',
    'funi-select': 'SfcSelect',
    'sfc-guid': 'SfcGuid',
    'funi-region': 'FuniRegion'
  };

  return typeObj[type];
}

export function findCurdColumns(widgetList, childWidget) {
  let columns = [];
  const allWidgetList = findAllWidget(widgetList);
  if (allWidgetList) {
    allWidgetList.forEach(item => {
      if (item.type === 'sfc-draggable-curd' && item.widgetList && item.widgetList.length > 0) {
        let fIndex = item.widgetList.findIndex(child => child.id === childWidget.id);
        if (fIndex >= 0) {
          columns = item.widgetList
            .filter(child => child.id !== childWidget.id)
            .map(child => {
              return {
                label: child.options?.label,
                name: child.options?.name
              };
            });
        }
      }
    });
  }
  return columns;
}
//获取扩展参数下拉数据源
export function getExtendIds(config) {
  let extendIds = [];
  const includeBtns = [
    'del',
    'batchDel',
    'allExport',
    'export',
    'line_export',
    'leadInto',
    'withdraw',
    'launchProcess'
  ];
  if (['edit', 'detail'].includes(config.type)) {
    if (config.steps && config.steps.length > 0) {
      config.steps.forEach(step => {
        let children = [];
        let stepPrefix = config.steps.length > 1 ? `${step.title}-` : '';
        const saveExtendId = {
          label: `${stepPrefix}保存`,
          value: step.saveExtendId
        };
        extendIds.push(saveExtendId);
        let stepExtendId = {
          label: `${stepPrefix}详情`,
          value: step.id
        };
        if (step.configJson) {
          const stepConfig = JSON.parse(step.configJson);
          if (stepConfig && stepConfig.widgetList && stepConfig.widgetList.length > 0) {
            const widgetList = findAllWidget(stepConfig.widgetList);
            if (widgetList && widgetList.length > 0) {
              widgetList.forEach(item => {
                if (item.type == 'funi-show-curd' && item.options) {
                  if (item.options.actions) {
                    item.options.actions.forEach(action => {
                      let actionExtendId = {
                        label: `${item.options.label}-${action.label ?? ''}`,
                        value: action.id
                      };
                      children.push(actionExtendId);
                    });
                  }
                  if (item.options.buttons) {
                    item.options.buttons.forEach(button => {
                      let buttonExtendId = {
                        label: `${item.options.label}-${button.label ?? ''}`,
                        value: button.id
                      };
                      children.push(buttonExtendId);
                    });
                  }
                }
              });
            }
          }
        }
        if (config.steps.length > 1) {
          stepExtendId.children = children;
          extendIds.push(stepExtendId);
        } else {
          extendIds.push(stepExtendId);
          extendIds = extendIds.concat(children);
        }
      });
    }

    if (config && config.id && config.type === 'detail') {
      const busExtendId = {
        label: `执行工作流`,
        value: config.id
      };
      extendIds.push(busExtendId);
    }
  } else {
    extendIds = (config.cardTab || []).map(item => {
      return {
        label: item.label,
        value: item.key,
        children: [
          ...(item.buttons
            ? item.buttons
                .filter(f => includeBtns.includes(f.eventsType))
                .map(i => ({ label: `${item.label}-${i.label}`, value: i.uuid }))
            : []),
          ...(item.actions
            ? item.actions
                .filter(f => includeBtns.includes(f.eventsType))
                .map(i => ({ label: `${item.label}-${i.label}`, value: i.uuid }))
            : [])
        ]
      };
    });

    if (config.cardTab?.length === 1) {
      let f = JSON.parse(JSON.stringify(extendIds));
      f[0].children.forEach(item => {
        item.label = item.label.split('-')[1];
      });
      let j = f.map(item => ({
        label: '列表查询',
        value: item.value
      }));
      extendIds = [...j, ...f[0].children];
    }
  }
  return extendIds;
}

export function createEXpFun(expression) {
  return new Function(`return async function(context) {return ${expression}}`);
}
