import {
  isNotNull,
  traverseContainerWidgets,
  traverseFieldWidgets,
  deepClone,
  findAllWidget,
  getExpressionCode,
  isExpression,
  isNotNullExpression
} from './util';
import { translate } from './i18n';
import FormValidators, { getRegExp } from './validators';
import lowcode from './lowcode';

function getContext(formConfig) {
  return `const context= {
    data:state.pageData,
    query:localQuery,
    formData:state.${formConfig.modelName},
    stepData:tempData?.stepData
  };`;
}

export function buildDefaultValueListFn(formConfig, widgetList, resultList, defaultValueWatchs) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const fd = fop.defaultValue;
    const fAction = `${fop.defaultAction}`;
    if (isNotNull(fd)) {
      let expression = getExpressionCode(fd);
      if (expression) {
        let expCode = `()=>{
          let defaultVal=${expression};
          let defaultAction='${fAction ? fAction : ''}';
          if(defaultAction==='2') {
            state.${formConfig.modelName}.${fop.name}=defaultVal;
          } else if(!innerChange){
            state.${formConfig.modelName}.${fop.name}=defaultVal;
          }
    }`;
        let watchCode = `watchEffect(${lowcode.injectContext(expCode, true, formConfig.modelName)})`;
        defaultValueWatchs.push(watchCode);
      } else if (!isExpression(fd)) {
        resultList.push(`${fop.name}: ${JSON.stringify(fd)},`);
      } else if (fieldWidget.type === 'funi-select' && fop.multiple) {
        resultList.push(`${fop.name}: [${fd}],`);
      } else {
        resultList.push(`${fop.name}: ${fd},`);
      }
    } else if (fieldWidget.type === 'picture-upload') {
      resultList.push(`${fop.name}: [],`);
    } else {
      resultList.push(`${fop.name}: null,`);
    }
  };
}

export function buildFuniVariableFn(propName, upperPropName, formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const fml = fop[propName];
    if (isNotNull(fml)) {
      let expression = getExpressionCode(fml);
      if (expression) {
        let funCode = `()=>{
          let ${propName}=${expression};
          return ${propName};
          }`;
        resultList.push(
          `${fop.name}${upperPropName}:computed(${lowcode.injectContext(funCode, true, formConfig.modelName)}),`
        );
      }
    }
  };
}

export function buildRulesListFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    let fieldRules = [];
    if (fop.requiredCode && isNotNullExpression(fop.requiredCode)) {
      const requiredExpression = getExpressionCode(fop.requiredCode);
      const requiredCode = `()=>{
        let required = ${requiredExpression};
        return required;
      }`;
      fieldRules.push(`{
        required: computed(${lowcode.injectContext(requiredCode, true, formConfig.modelName)}),
        message: '${fop.requiredHint ? fop.requiredHint : translate('render.hint.fieldRequired')}',
      }`);
    } else if (fop.required) {
      fieldRules.push(`{
        required: true,
        message: '${fop.requiredHint ? fop.requiredHint : translate('render.hint.fieldRequired')}',
      }`);
    }

    if (fop.validation) {
      let vldName = fop.validation;
      if (FormValidators[vldName]) {
        fieldRules.push(`{
          pattern: ${eval(getRegExp(vldName))},
          trigger: ['blur', 'change'],
          message: '${fop.validationHint}'
        }`);
      } else {
        fieldRules.push(`{
          pattern: '${eval(vldName)}',
          trigger: ['blur', 'change'],
          message: '${fop.validationHint}'
        }`);
      }
    }

    //TODO: 自定义校验函数

    fieldRules.length > 0 && resultList.push(`${fop.name}: [${fieldRules.join(',')}],`);
  };
}

export function buildFieldOptionsFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const ft = fieldWidget.type;
    if (ft === 'radio' || ft === 'checkbox' || ft === 'select' || ft === 'cascader' || ft === 'funi-select') {
      if (ft === 'funi-select') {
        resultList.push(`${fop.name}Options: ${JSON.stringify(fop.selectOptions)},`);
      } else {
        resultList.push(`${fop.name}Options: ${JSON.stringify(fop.optionItems)},`);
      }
    }
  };
}

export function buildFuniSelectParamsFn(formConfig, widgetList, config, resultList, designerConfig) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const ft = fieldWidget.type;
    if (fop && fop.modelOption && fop.modelOption.data && fop.modelOption.data.model_id) {
      fop.modelOption.modelId = fop.modelOption.data.model_id;
      fop.modelOption.modelName = fop.modelOption.data.modelName;
    }
    if (ft === 'funi-select' && fop.modelOption && fop.modelOption.type === 'model' && fop.modelOption.modelId) {
      let url = `/as/${config.appCode}/model/dicListV2`;
      let sort =
        fop.modelOption.data && fop.modelOption.data.sortFields
          ? `sort:${JSON.stringify(fop.modelOption.data.sortFields)},`
          : '';
      let labelKey =
        fop.modelOption.data && fop.modelOption.data.labelKey ? `labelKey:'${fop.modelOption.data.labelKey}',` : '';
      let valueKey =
        fop.modelOption.data && fop.modelOption.data.valueKey ? `valueKey:'${fop.modelOption.data.valueKey}',` : '';
      let params = [];
      if (
        fop.modelOption &&
        fop.modelOption.data &&
        fop.modelOption.data.filterParams &&
        fop.modelOption.data.filterParams.length > 0
      ) {
        fop.modelOption.data.filterParams.forEach(p => {
          params.push({ logicalOperator: p.logicalOperator, conditions: p.conditions });
        });
      }
      let paramsList = [];
      if (params && params.length > 0) {
        params.forEach(p => {
          let conditions = [];
          if (p.conditions && p.conditions.length > 0) {
            p.conditions.forEach(c => {
              const expression = getExpressionCode(c.value);
              let condNew = {
                logicalOperator: c.logicalOperator,
                key: c.key,
                operator: c.operator,
                value: `@${expression}@`
              };
              if (c.tableName) {
                condNew.tableName = c.tableName;
                condNew.joinFieldName = c.joinFieldName;
              }
              conditions.push(condNew);
            });
          }
          paramsList.push({ logicalOperator: p.logicalOperator, conditions });
        });
      }
      let paramsStr =
        paramsList.length > 0
          ? `params:${JSON.stringify(paramsList)},`.replaceAll('"@', '').replaceAll('@"', '').replaceAll('\\n', '')
          : '';
      resultList.push(`${fop.name}RequestParams: computed(()=>{
            ${getContext(formConfig)}
            let requestParams;
            try {
            requestParams= {
              asUrl:'${url}',
              page_id,
              model_id:'${fop.modelOption.modelId}',
              ${labelKey}${valueKey}${sort}${paramsStr}
            } }catch(ex){console.log(ex);}
            return requestParams;
          }),`);
    } else if (
      ft === 'funi-select' &&
      fop.modelOption &&
      fop.modelOption.type === 'api' &&
      fop.modelOption.data &&
      fop.modelOption.data.api_url
    ) {
      let url = fop.modelOption.data.api_url;
      let asUrl = fop.modelOption.data.apiType === 'other' ? url : `/as/${config.appCode}/api/list`;
      let method = fop.modelOption.data.method ?? 'post';
      let headersStr = `headers:'',`;
      let originStr = `origin:'${fop.modelOption.data.apiType}',`;
      if (fop.modelOption.data.headers) {
        let headers = fop.modelOption.data.headers;
        let headersList = [];
        if (headers && headers.length > 0) {
          headers.forEach(h => {
            if (h.type === 'variable' || !h.type) {
              const expression = getExpressionCode(h.expression);
              headersList.push(`${h.name}:${expression}`);
            } else if (h.type === 'string') {
              headersList.push(`${h.name}:'${h.expression}'`);
            } else {
              headersList.push(`${h.name}:${h.expression}`);
            }
          });
        }
        headersStr = `headers:{${headersList.join(',')}},`;
      }
      let paramsStr = `params:{},`;
      if (fop.modelOption.data.requestOtherParams) {
        let params = fop.modelOption.data.requestOtherParams;
        let paramsList = [];
        if (params && params.length > 0) {
          params.forEach(p => {
            if (p.type === 'variable' || !p.type) {
              const expression = getExpressionCode(p.expression);
              paramsList.push(`${p.name}:${expression}`);
            } else if (p.type === 'string') {
              paramsList.push(`${p.name}:'${p.expression}'`);
            } else {
              paramsList.push(`${p.name}:${p.expression}`);
            }
          });
        }
        paramsStr = `params:{
          ${paramsList.join(',')}
        }`;
      }
      resultList.push(`${fop.name}RequestParams: computed(()=>{
        const context= {
          data:state.pageData,
          query:localQuery,
          formData:state.${formConfig.modelName}
        };
        let requestParams;
        try {
        requestParams= {
          asUrl:'${asUrl}',
          method:'${method}',
          ${originStr}
          url:'${url}',
          ${headersStr}
          ${paramsStr}
        } }catch(ex){console.log(ex);}
        return requestParams;
      }),`);
    } else if (
      ft === 'funi-select' &&
      fop.modelOption &&
      fop.modelOption.type === 'sql' &&
      fop.modelOption.data &&
      fop.modelOption.data.sql_url &&
      fop.modelOption.data.id
    ) {
      let asUrl = `/as/${config.appCode}/sql/list`;
      let sql_id = fop.modelOption.data.id ? `sql_id:'${fop.modelOption.data.id}',` : `sql_id:'',`;
      let paramsStr = `params:{},`;
      if (fop.modelOption.data.requestOtherParams) {
        let params = fop.modelOption.data.requestOtherParams;
        let paramsList = [];
        if (params && params.length > 0) {
          params.forEach(p => {
            if (p.type === 'variable' || !p.type) {
              const expression = getExpressionCode(p.expression);
              paramsList.push(`${p.name}:${expression}`);
            } else if (p.type === 'string') {
              paramsList.push(`${p.name}:'${p.expression}'`);
            } else {
              paramsList.push(`${p.name}:${p.expression}`);
            }
          });
        }
        paramsStr = `params:{
          ${paramsList.join(',')}
        }`;
      }
      resultList.push(`${fop.name}RequestParams: computed(()=>{
        ${getContext(formConfig)}
        let requestParams;
        try {
        requestParams= {
          asUrl:'${asUrl}',
          page_id,
          component_id:'${fop.modelOption.data.component_id}',
          ${sql_id}
          ${paramsStr}
        } }catch(ex){console.log(ex);}
        return requestParams;
      }),`);
    }
  };
}

export function buildUploadDataFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const ft = fieldWidget.type;
    if (ft === 'picture-upload' || ft === 'file-upload') {
      resultList.push(`${fop.name}FileList: [],`);
      resultList.push(`${fop.name}UploadHeaders: {...(headers||{})},`);
      resultList.push(`${fop.name}UploadData: {},`);
    }
  };
}

export function buildHtmlDataFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const ft = fieldWidget.type;
    if (ft === 'html-text') {
      resultList.push(`${fop.name}htmlContent: '${fop.htmlContent}',`);
    }
  };
}

export function buildListenerFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const fl = fop.listener;
    if (fl) {
      let listener = lowcode.injectForObjStr(`{${fop.listener}}`, true, formConfig.modelName);
      resultList.push(`${fop.name}Listener: ${listener},`);
    }
  };
}

export function buildColumnsFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const fl = fop.columns;
    if (fl) {
      let columns = deepClone(fl);
      if (fieldWidget.type === 'funi-show-curd') {
        //展示列表出来选中和序号
        if (fop.isLineNumber) {
          columns.splice(0, 0, { label: '序号', prop: '_show_curd_xh', align: 'center', type: 'index' });
        }
        if (fop.selectType) {
          columns.splice(0, 0, { prop: '_show_curd_xz', align: 'center', type: fop.selectType });
        }
      }
      const convertColumn = col => {
        if (col && col.children && col.children.length > 0) {
          col.children.forEach(colChild => {
            convertColumn(colChild);
          });
        }
        if (col.comProps) {
          try {
            const expression = getExpressionCode(col.comProps);
            let comPropsStr = expression;
            let props = JSON.parse(comPropsStr);
            if (props.listener) {
              let listener = lowcode.injectForObjStr(
                `{${props.listener}}`,
                true,
                formConfig.modelName,
                false,
                [{ key: 'change', value: 'onChange' }],
                true,
                fieldWidget.type !== 'funi-edit-curd' //列表里面就不要上下文了
              );
              props.listener = `@${listener.replaceAll(`\n`, '')}@`;
            }
            col.comProps = `@${lowcode.injectContext(
              `(row,index) => {return ${JSON.stringify(props)};}`,
              true,
              formConfig.modelName
            )}@`;
            // if (col.component === 'SfcSelect') {
            //   col.comProps = `@${lowcode.injectContext(`(row,index) => {return ${comPropsStr};}`)}@`;
            // } else {
            //   col.comProps = `@${lowcode.injectForObjStr(`${comPropsStr}`, true, formConfig.modelName)}@`;
            // }
          } catch (ex) {
            console.log(ex);
          }
        }
        if (col.render && fieldWidget.type === 'funi-show-curd') {
          let colRender = col.render;
          let funHead = '{row,index})=>{';
          if (colRender.includes(funHead)) {
            colRender = colRender.replaceAll(
              funHead,
              `{row,index})=>{ const params = {
          row,
          index
        };
        const context = {
          params: params,
          data: state.pageData,
          handler: state.handler,
          refs: instance.proxy.$refs,
          router: instance.proxy.$router,
          route: instance.proxy.$route,
          query: localQuery,
          emit: instance.proxy.$emit,
          message: instance.proxy.$message,
          notify: instance.proxy.$notify,
          alert: instance.proxy.$alert,
          loading: instance.proxy.$loading,
          showSfcUpload: instance.proxy.$showSfcUpload,
          showLog: instance.proxy.$showLog,
          formData: state.formData,
          stepData: tempData?.stepData,
          workflow: tempData?.workflow,
        };
        context.params = params;`
            );
          } else {
            colRender = `@({row,index})=>{ 
        const params = {
          row,
          index
        };
        const context = {
          params: params,
          data: state.pageData,
          handler: state.handler,
          refs: instance.proxy.$refs,
          router: instance.proxy.$router,
          route: instance.proxy.$route,
          query: localQuery,
          emit: instance.proxy.$emit,
          message: instance.proxy.$message,
          notify: instance.proxy.$notify,
          alert: instance.proxy.$alert,
          loading: instance.proxy.$loading,
          showSfcUpload: instance.proxy.$showSfcUpload,
          showLog: instance.proxy.$showLog,
          formData: state.formData,
          stepData: tempData?.stepData,
          workflow: tempData?.workflow,
        };
        context.params = params;
        ${colRender}
        }@`;
          }
          col.render = colRender;
        }
        if (!col.render && col.eventsConfig) {
          let eventCode = lowcode.generateEvent(col.eventsConfig, true);
          const realRender = `({row,index})=>{
                  const ElLink=resolveComponent('el-link');
                  const link=h(ElLink,{type:'primary',onClick:()=>{
                    let param={};${eventCode}
                  }},{default:()=>row['${col.prop}']});
                  return link
                }`;
          col.render = `@${lowcode.injectContext(realRender)}@`;
        }
        if (fieldWidget.type === 'funi-show-curd') {
          if (col.hidden !== true) {
            col.hidden = `@computed(()=>{return !((fieldAuthObj&&fieldAuthObj['${fop.name}.${col.prop}']&&fieldAuthObj['${fop.name}.${col.prop}'].read)||!fieldAuthObj||!fieldAuthObj['${fop.name}.${col.prop}'])})@`;
          }
        }
      };
      columns.forEach(column => {
        convertColumn(column);
      });
      resultList.push(
        `${fop.name}Columns: ${JSON.stringify(columns || [])},`
          .replaceAll('"@', '')
          .replaceAll('@"', '')
          .replaceAll(`'@`, '')
          .replaceAll(`@'`, '')
          .replaceAll('\\n', '')
      );
    }
  };
}

export function buildActionsFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const fl = fop.actions;
    if (fl) {
      let actions = deepClone(fl);
      actions.forEach(btn => {
        if (btn.attrs) {
          try {
            let attrsStr = btn.attrs.replaceAll('@', '');
            btn.attrs = `@${lowcode.injectForObjStr(`${attrsStr}`, true, formConfig.modelName)}@`;
          } catch (ex) {
            console.log(ex);
          }
        } else if (btn.logic && btn.logic.snippets) {
          try {
            let attrsStr = `@{onClick:()=>{${btn.logic.snippets}}}`.replaceAll('@', '');
            btn.attrs = `@${lowcode.injectForObjStr(`${attrsStr}`, true, formConfig.modelName)}@`;
            delete btn.logic;
          } catch (ex) {
            console.log(ex);
          }
        }

        if (btn.showConditions && isNotNullExpression(btn.showConditions)) {
          btn.show = `@${getExpressionCode(btn.showConditions)}@`;
          delete btn.showConditions;
        }
      });
      resultList.push(
        `${fop.name}Actions: ({row,index})=>{context.params={row,index}; return ${JSON.stringify(actions || [])}},`
          .replaceAll('"@', '')
          .replaceAll('@"', '')
          .replaceAll('\\n', '')
      );
    }
  };
}

export function buildButtonsFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const fl = fop.buttons;
    if (fl) {
      let buttons = deepClone(fl);
      buttons.forEach(btn => {
        if (btn.attrs) {
          try {
            let attrsStr = btn.attrs.replaceAll('@', '');
            btn.attrs = `@${lowcode.injectForObjStr(`${attrsStr}`, true, formConfig.modelName)}@`;
          } catch (ex) {
            console.log(ex);
          }
        } else if (btn.logic && btn.logic.snippets) {
          try {
            let attrsStr = `@{onClick:()=>{${btn.logic.snippets}}}`.replaceAll('@', '');
            btn.attrs = `@${lowcode.injectForObjStr(`${attrsStr}`, true, formConfig.modelName)}@`;
            delete btn.logic;
          } catch (ex) {
            console.log(ex);
          }
        }
      });
      resultList.push(
        `${fop.name}Buttons: ${JSON.stringify(buttons || [])},`
          .replaceAll('"@', '')
          .replaceAll('@"', '')
          .replaceAll('\\n', '')
      );
    }
  };
}

export function buildSearchConfigGroupFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const groupsOld = fop.searchConfigGroup;
    if (groupsOld) {
      let groups = deepClone(groupsOld);
      if (groups && groups.length > 0) {
        let linkageList = [];
        groups.forEach(item => {
          if (item && item.schema && item.schema.length > 0) {
            item.schema.forEach((schema, schIndex) => {
              let realFilterParams = [];
              const { api_url, model_id, componentType, labelKey } = schema;
              let valueFormat = schema.valueFormat;
              if (schema.component === 'el-date-picker') {
                let type = 'date';
                switch (schema.valueFormat) {
                  case 'YYYY-MM-DD HH:mm:ss':
                    type = 'datetime';
                    break;
                  case 'YYYY-MM-DD':
                    type = 'date';
                    break;
                  case 'YYYY-MM':
                    type = 'month';
                    break;
                  case 'YYYY':
                    type = 'year';
                    break;
                  case 'ww':
                    type = 'week';
                    valueFormat = '[Week] ww';
                    break;
                  default:
                    break;
                }
                if (schema.componentType === 'dateInterval' && !['ww'].includes(schema.valueFormat)) {
                  type = `${type}range`;
                }
                schema.props = {
                  type,
                  'value-format': type === 'week' ? '' : valueFormat,
                  format: valueFormat
                };
                if (schema.componentType === 'dateInterval') {
                  schema.props.style = {
                    width: '95%'
                  };
                }
              } else if (schema.componentType === 'FuniSearchRegion') {
                schema.props = {
                  attribute: {
                    region: JSON.stringify({
                      maxLevel: 5,
                      cascadeColumn: [
                        {
                          column: 'bus_district_code',
                          level: 1
                        },
                        {
                          column: 'bus_district_code',
                          level: 2
                        },
                        {
                          column: 'bus_district_code',
                          level: 3
                        },
                        {
                          column: 'bus_district_code',
                          level: 4
                        },
                        {
                          column: 'bus_district_code',
                          level: 5
                        }
                      ]
                    })
                  }
                };
              } else if (['model', 'APIs'].includes(componentType)) {
                let params = schema.params;
                if (params && params.length > 0) {
                  params.forEach(param => {
                    let conditions = [];
                    if (param && param.conditions && param.conditions.length > 0) {
                      param.conditions.forEach(cond => {
                        if (cond.value) {
                          try {
                            let expressionStr = getExpressionCode(cond.value);
                            if (expressionStr) {
                              if (!expressionStr.startsWith("'")) {
                                expressionStr = `'${expressionStr}`;
                              }
                              if (!expressionStr.endsWith("'")) {
                                expressionStr = `${expressionStr}'`;
                              }
                              let linkageExps = expressionStr.match(/'context\.searchData\..+?'/g);
                              if (linkageExps && linkageExps.length > 0) {
                                linkageExps.forEach(lprop => {
                                  cond.value = cond.value.replaceAll(lprop, lprop.substr(1, lprop.length - 2));
                                  let linkageProp = lprop.substr(20, lprop.length - 21);
                                  let fIndex = linkageList.findIndex(link => link.linkageProp === linkageProp);
                                  if (fIndex < 0) {
                                    linkageList.push({
                                      linkageProp,
                                      linkages: [{ tabIndex: 0, schIndex, filterProp: cond.key }]
                                    });
                                  } else {
                                    linkageList[fIndex].linkages.push({ tabIndex: 0, schIndex, filterProp: cond.key });
                                  }
                                });
                              }
                            }
                          } catch (ex) {
                            console.log(ex);
                          }
                        }
                        let valeStr = '';
                        if (cond.value) {
                          let expStr = getExpressionCode(cond.value);
                          valeStr = expStr;
                        }
                        if (valeStr) {
                          let condNew = {
                            key: cond.key,
                            operator: cond.operator,
                            value: `@${valeStr}@`,
                            logicalOperator: cond.logicalOperator
                          };
                          if (cond.tableName) {
                            condNew.tableName = cond.tableName;
                            condNew.joinFieldName = cond.joinFieldName;
                          }
                          conditions.push(condNew);
                        } else {
                          let condNew = {
                            key: cond.key,
                            operator: cond.operator,
                            logicalOperator: cond.logicalOperator
                          };
                          if (cond.tableName) {
                            condNew.tableName = cond.tableName;
                            condNew.joinFieldName = cond.joinFieldName;
                          }
                          conditions.push(condNew);
                        }
                      });
                    }
                    realFilterParams.push({ logicalOperator: param.logicalOperator, conditions });
                  });
                }
                if (!schema.props) {
                  item.props = {};
                }
                Object.assign(schema.props, {
                  filterable: true,
                  changeField: '',
                  auto: false,
                  clearable: true,
                  requestParams: {
                    asUrl: componentType === 'APIs' ? api_url : `@'/as/'+app_code+'/model/dicListV2'@`,
                    page_id: `@page_id@`,
                    model_id: model_id,
                    labelKey,
                    params: `@computed(${lowcode.injectContext(
                      `()=>{
                        context.searchData = state.searchData['${fop.name}']||{};
                        return ${JSON.stringify(realFilterParams)
                          .replaceAll('"@', '')
                          .replaceAll('@"', '')
                          .replaceAll('\\n', '')}
                      }`,
                      true,
                      'formData'
                    )})@`
                  },
                  style: { width: '100%' }
                });
              }
            });
          }
        });
        //检索联动处理
        if (groups && groups.length > 0) {
          groups.forEach((item, gIndex) => {
            if (linkageList && linkageList.length > 0 && item.schema && item.schema.length > 0) {
              linkageList.forEach(linkage => {
                let fIndex = item.schema.findIndex(item => item.prop === linkage.linkageProp);
                if (fIndex >= 0) {
                  if (!item.schema[fIndex].props) {
                    item.schema[fIndex].props = {};
                  }
                  let changeFieldCode = '';
                  if (linkage.linkages && linkage.linkages.length > 0) {
                    linkage.linkages.forEach(link => {
                      changeFieldCode += `attrsState.${fop.name}SearchConfigGroup[${gIndex}].schema[${link.schIndex}].props.changeField = e;`;
                    });
                  }
                  item.schema[fIndex].props.onChange = `@e => {
                  if(!state.searchData['${fop.name}']){
                    state.searchData['${fop.name}']={};
                  }
                  state.searchData['${fop.name}']['${linkage.linkageProp}'] = e;
                  ${changeFieldCode}
                }@`;
                }
              });
            }
          });
        }
      }
      resultList.push(
        `${fop.name}SearchConfigGroup: ${JSON.stringify(groups || [])},`
          .replaceAll('"@', '')
          .replaceAll('@"', '')
          .replaceAll('\\n', '')
      );
    }
  };
}

export function buildAllowedScopeFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const fl = fop.allowedScope;
    if (fl && fl.length > 0) {
      let allowedScope = deepClone(fl);
      resultList.push(`${fop.name}AllowedScope: ${JSON.stringify(allowedScope || [])},`);
    }
  };
}

export function buildrequestOtherParamFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const fl = fop.requestOtherParam;
    if (fl) {
      if (fl.type === 'model' && fl.data && fl.data.model_id) {
        let joinFields = [];
        if (fop && fop.columns) {
          joinFields = fop.columns.filter(col => col && col.children && col.children.length > 0).map(col => col.prop);
        }
        let sort = fl.data && fl.data.sortFields ? `sort:${JSON.stringify(fl.data.sortFields)},` : '';
        let params = [];
        if (fl && fl.data && fl.data.filterParams && fl.data.filterParams.length > 0) {
          fl.data.filterParams.forEach(p => {
            params.push({ logicalOperator: p.logicalOperator, conditions: p.conditions });
          });
        }
        let paramsList = [];
        if (params && params.length > 0) {
          params.forEach(p => {
            let conditions = [];
            if (p.conditions && p.conditions.length > 0) {
              p.conditions.forEach(c => {
                const expression = getExpressionCode(c.value);
                let condNew = {
                  logicalOperator: c.logicalOperator,
                  key: c.key,
                  operator: c.operator,
                  value: `@${expression}@`
                };
                if (c.tableName) {
                  condNew.tableName = c.tableName;
                  condNew.joinFieldName = c.joinFieldName;
                }
                conditions.push(condNew);
              });
            }
            paramsList.push({ logicalOperator: p.logicalOperator, conditions });
          });
        }
        let paramsStr =
          paramsList.length > 0
            ? `params:${JSON.stringify(paramsList)},`.replaceAll('"@', '').replaceAll('@"', '').replaceAll('\\n', '')
            : '';
        resultList.push(`${fop.name}RequestOtherParam: computed(()=>{
          ${getContext(formConfig)}
            let requestParams;
            try {
            requestParams= {
              joinFields:${JSON.stringify(joinFields)},
              page_id,
              model_id:'${fl.data.model_id}',
              component_id:'${fl.data.component_id}',
              ${sort}${paramsStr}
            } }catch(ex){console.log(ex);}
            return requestParams;
          }),`);
      } else if (fl.type === 'api' && fl.data && fl.data.api_url) {
        let url = fl.data.api_url;
        let method = fl.data.method ?? 'post';
        let headersStr = `headers:'',`;
        let originStr = `origin:'${fl.data.apiType}',`;
        if (fl.data.headers) {
          let headers = fl.data.headers;
          let headersList = [];
          if (headers && headers.length > 0) {
            headers.forEach(h => {
              if (h.type === 'string' || !h.type) {
                headersList.push(`${h.name}:'${h.expression}'`);
              } else {
                headersList.push(`${h.name}:${h.expression}`);
              }
            });
          }
          headersStr = `headers:{${headersList.join(',')}},`;
        }
        let paramsStr = `params:{},`;
        if (fl.data.requestOtherParams) {
          let params = fl.data.requestOtherParams;
          let paramsList = [];
          if (params && params.length > 0) {
            params.forEach(p => {
              if (p.type === 'variable' || !p.type) {
                const expression = getExpressionCode(p.expression);
                paramsList.push(`${p.name}:${expression}`);
              } else if (p.type === 'string') {
                paramsList.push(`${p.name}:'${p.expression}'`);
              } else {
                paramsList.push(`${p.name}:${p.expression}`);
              }
            });
          }
          paramsStr = `params:{
          ${paramsList.join(',')}
        }`;
        }
        resultList.push(`${fop.name}RequestOtherParam: computed(()=>{
          ${getContext(formConfig)}
        let requestParams;
        try {
        requestParams= {
          method:'${method}',
          url:'${url}',
          ${originStr}
          ${headersStr}
          ${paramsStr}
        } }catch(ex){console.log(ex);}
        return requestParams;
      }),`);
      } else if (fl.type === 'sql' && fl.data && fl.data.id) {
        let sql_id = fl.data.id ? `sql_id:'${fl.data.id}',` : `sql_id:'',`;
        let paramsStr = `params:{},`;
        if (fl.data.requestOtherParams) {
          let params = fl.data.requestOtherParams;
          let paramsList = [];
          if (params && params.length > 0) {
            params.forEach(p => {
              if (p.type === 'variable' || !p.type) {
                const expression = getExpressionCode(p.expression);
                paramsList.push(`${p.name}:${expression}`);
              } else if (p.type === 'string') {
                paramsList.push(`${p.name}:'${p.expression}'`);
              } else {
                paramsList.push(`${p.name}:${p.expression}`);
              }
            });
          }
          paramsStr = `params:{
          ${paramsList.join(',')}
        }`;
        }
        resultList.push(`${fop.name}RequestOtherParam: computed(()=>{
        const context= {
          data:state.pageData,
          query:localQuery,
          formData:state.${formConfig.modelName}
        };
        let requestParams;
        try {
        requestParams= {
          page_id,
          component_id:'${fl.data.component_id}',
          ${sql_id}
          ${paramsStr}
        } }catch(ex){console.log(ex);}
        return requestParams;
      }),`);
      }
    }
  };
}

export function buildModelFieldWatchFn(formConfig, originWidgetList, config) {
  let widgetList = findAllWidget(originWidgetList);
  return function (modelFieldWatch) {
    let modelList = widgetList
      .filter(
        wgt => wgt.type === 'funi-select' && wgt.options && wgt.options.modelOption && wgt.options.modelOption.modelId
      )
      .map(wgt => {
        return {
          value: wgt.options.modelOption.modelId,
          label: wgt.options.modelOption.modelName,
          name: wgt.options.name
        };
      });

    if (modelList && modelList.length > 0) {
      modelList.forEach(model => {
        let fieldList = widgetList.filter(
          wgt => wgt.options && wgt.options.modelField && wgt.options.modelField.indexOf(`${model.name}@`) === 0
        );
        let childFieldList = [];
        const findChildWidget = wgt => {
          let list = [];
          const findWidgetList = wgtList => {
            let colList = wgtList.filter(
              wgt => wgt.options && wgt.options.modelField && wgt.options.modelField.indexOf(`${model.name}@`) === 0
            );
            list.push(...colList);
            wgtList.forEach(wgt => {
              if (['container'].includes(wgt.category)) {
                let colContainerlist = findChildWidget(wgt);
                list.push(...colContainerlist);
              }
            });
          };
          if (wgt.cols && wgt.cols.length > 0) {
            wgt.cols.forEach(col => {
              if (col.widgetList && col.widgetList.length > 0) {
                findWidgetList(col.widgetList);
              }
            });
          } else if (wgt.type === 'table' && wgt.rows && wgt.rows.length > 0) {
            wgt.rows.forEach(row => {
              let rowlist = findChildWidget(row);
              list.push(...rowlist);
            });
          } else if (wgt.type === 'tab' && wgt.tabs && wgt.tabs.length > 0) {
            wgt.tabs.forEach(tab => {
              findWidgetList(tab.widgetList);
            });
          } else if (wgt.type === 'card' && wgt.widgetList && wgt.widgetList.length > 0) {
            findWidgetList(wgt.widgetList);
          }
          return list;
        };
        widgetList.forEach(wgt => {
          if (['container'].includes(wgt.category)) {
            let list = findChildWidget(wgt);
            childFieldList.push(...list);
          }
        });
        fieldList.push(...childFieldList);
        if (fieldList && fieldList.length > 0) {
          let setFields = [];
          fieldList.forEach(field => {
            let reFieldName = field.options.modelField.split('@')[1];
            setFields.push(`this.${formConfig.modelName}.${field.options.name}=newObj.${reFieldName};`);
          });
          let url = `/as/${config.appCode}/detailById`;
          let watchCode = `"${formConfig.modelName}.${model.name}":{
            handler(newVal){
              if(newVal){
                window.$http.post('${url}',{model_id:'${model.value}',id:newVal})
                .then(res=>{
                  let newObj=res||{};
                  ${setFields.join('\n\n')}
                })
              } else{
                let newObj={};
                ${setFields.join('\n\n')}
              }
            },
            immediate:true
          }`;
          modelFieldWatch.push(watchCode);
        }
      });
    }
  };
}

export function buildDialogButtonsFn(resultList) {
  return function (dialogConfig, formConfig) {
    const fl = dialogConfig.buttons;
    if (fl) {
      let buttons = deepClone(fl);
      buttons.forEach(btn => {
        if (btn.attrs) {
          try {
            let attrsStr = btn.attrs.replaceAll('@', '');
            btn.attrs = `@${lowcode.injectForObjStr(`${attrsStr}`, true, formConfig.modelName)}@`;
          } catch (ex) {
            console.log(ex);
          }
        }
      });
      resultList.push(
        `${dialogConfig.name}Buttons: ${JSON.stringify(buttons || [])},`
          .replaceAll('"@', '')
          .replaceAll('@"', '')
          .replaceAll('\\n', '')
      );
    }
  };
}

export function buildDialogListenerFn(resultList) {
  return function (formConfig) {
    const dialogs = formConfig.dialogs;
    if (dialogs && dialogs.length > 0) {
      dialogs.forEach(dialog => {
        let config = JSON.parse(dialog.dialogConfig.configJson);
        let listener = lowcode.injectForObjStr(`{${config.dialogConfig.listener ?? ''}}`, true, formConfig.modelName);
        resultList.push(`${dialog.name}Listener: ${listener},`);
      });
    }
  };
}

export function buildCurdValidatorFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    const fl = fop.columns;
    if (fl && fieldWidget.type === 'funi-edit-curd') {
      resultList.push(`valid = valid && instance.proxy.$refs['${fop.name}'].validate();`);
    }
  };
}

export function buildFileTableValidatorFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fieldWidget.type === 'sfc-file-table') {
      resultList.push(`if(valid){valid = instance.proxy.$refs['${fop.name}'].verification();`);
      resultList.push(`if(!valid){errors['${fop.name}']=[{message:'请上传必传件'}]};}`);
    }
  };
}

export function buildFormValidatorFn(formConfig) {
  let resultList = [];
  if (formConfig && formConfig.validators && formConfig.validators.length) {
    formConfig.validators.forEach(v => {
      const expression = getExpressionCode(v.expression);
      let message = '';
      if (isNotNullExpression(v.message)) {
        message = getExpressionCode(v.message);
      } else {
        message = v.message;
      }
      resultList.push(
        `if(valid){valid = valid && (${expression}); if(!valid){errors['${v.name}']=[{message:${message}}];}}`
      );
    });
  }
  return resultList;
}

export function buildActiveTabs(formConfig, widgetList) {
  let resultList = [];
  const handlerFn = function (cw) {
    const cop = cw.options;
    const ct = cw.type;
    if (ct === 'tab') {
      cw.tabs.length > 0 && resultList.push(`'${cop.name}ActiveTab': '${cw.tabs[0].options.name}',`);
    }
  };
  traverseContainerWidgets(widgetList, handlerFn);

  return resultList;
}

export function buildGanttConverterFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.converterCode) {
      resultList.push(
        `${fop.name}Converter:${lowcode.injectContext(`${fop.converterCode}`, true, formConfig.modelName)},`
      );
    }
  };
}

export function buildCustomRenderFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.renderer) {
      resultList.push(
        `${fop.name}Renderer:${lowcode.injectContext(
          `(createElement,props)=>{${fop.renderer}}`,
          true,
          formConfig.modelName
        )},`
      );
    }
  };
}

export function buildMapModalRenderFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.modalContentRender) {
      resultList.push(
        `${fop.name}ModalContentRender:${lowcode.injectContext(
          `(currentOverlayData,currentClickLayer)=>{${fop.modalContentRender}}`,
          true,
          formConfig.modelName
        )},`
      );
    }
  };
}

export function buildMapLegendShowFuncFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.legendShowFunc) {
      resultList.push(
        `${fop.name}LegendShowFunc:${lowcode.injectContext(
          `(legend)=>{${fop.legendShowFunc}}`,
          true,
          formConfig.modelName
        )},`
      );
    }
  };
}

export function buildMapDrawCallbackFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.drawCallback) {
      resultList.push(
        `${fop.name}DrawCallback:${lowcode.injectContext(
          `(oldFeatures)=>{${fop.drawCallback}}`,
          true,
          formConfig.modelName
        )},`
      );
    }
  };
}

export function buildMapLegendListFuncFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.legendListFunc) {
      resultList.push(
        `${fop.name}LegendListFunc:${lowcode.injectContext(
          `(legendList)=>{${fop.legendListFunc}}`,
          true,
          formConfig.modelName
        )},`
      );
    }
  };
}

export function buildGanttTooltipTextFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.tooltipTextCode) {
      resultList.push(
        `${fop.name}TooltipText:${lowcode.injectContext(`${fop.tooltipTextCode}`, true, formConfig.modelName)},`
      );
    }
  };
}

export function buildDisabledAttrsFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.disabledCode && isNotNullExpression(fop.disabledCode)) {
      let expression = getExpressionCode(fop.disabledCode);
      let funCode = `()=>{
        let disabled = ${expression};
        return disabled;
      }`;
      resultList.push(`${fop.name}Disabled:computed(${lowcode.injectContext(funCode, true, formConfig.modelName)}),`);
    }
  };
}

export function buildHiddenAttrsFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.hiddenCode && isNotNullExpression(fop.hiddenCode)) {
      let expression = getExpressionCode(fop.hiddenCode);
      let funCode = `()=>{
        let hidden = ${expression};
        return !hidden;
      }`;
      resultList.push(`${fop.name}Hidden:computed(${lowcode.injectContext(funCode, true, formConfig.modelName)}),`);
    }
  };
}

export function buildSrcAttrsFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (fop.src && isNotNullExpression(fop.src)) {
      let expression = getExpressionCode(fop.src);
      let funCode = `()=>{
        let src = ${expression};
        return src;
      }`;
      resultList.push(`${fop.name}Src:computed(${lowcode.injectContext(funCode, true, formConfig.modelName)}),`);
    }
  };
}

export const genVue2JS = function (formConfig, widgetList) {
  let defaultValueList = [];
  let rulesList = [];
  let fieldOptions = [];
  let uploadData = [];
  traverseFieldWidgets(widgetList, widget => {
    buildDefaultValueListFn(formConfig, widgetList, defaultValueList)(widget);
    buildRulesListFn(formConfig, widgetList, rulesList)(widget);
    buildFieldOptionsFn(formConfig, widgetList, fieldOptions)(widget);
    buildUploadDataFn(formConfig, widgetList, uploadData)(widget);
  });

  const activeTabs = buildActiveTabs(formConfig, widgetList);

  const v2JSTemplate = `  export default {
    components: {},
    props: {
      headers: {
        type: Object
      }
    },
    data() {
      const { headers } = this;
      return {
        ${formConfig.modelName}: {
          ${defaultValueList.join('\n')}
        },
        
        ${formConfig.rulesName}: {
          ${rulesList.join('\n')}
        },
        
        ${activeTabs.join('\n')}
        
        ${fieldOptions.join('\n')}
        
        ${uploadData.join('\n')}
      }
    },
    computed: {},
    watch: {},
    created() {
    },
    mounted() {
    },
    methods: {
      submitForm() {
        this.$refs['vForm'].validate(valid => {
          if (!valid) return
          
          //TODO: 提交表单
        })
      },
      
      resetForm() {
        this.$refs['vForm'].resetFields()
      }
    }
  }`;

  return v2JSTemplate;
};

//生成字段标签和字段名的映射
export function buildLabelPropListFn(formConfig, widgetList, resultList) {
  return function (fieldWidget) {
    const fop = fieldWidget.options;
    if (!['sfc-file-table'].includes(fieldWidget.type)) {
      resultList.push(`${fop.name}:'${fop.label}'`);
    }
  };
}

//生成表单生命周期函数
export function buildLifeCycleFn(formConfig, lifeCyleName, formFunName = '') {
  let lifeCycleCode = '';
  switch (lifeCyleName) {
    case 'onFormCreated':
    case 'onFormMounted':
      if (formConfig.frontCode) {
        lifeCycleCode = `const lifeCycleFun=${lowcode.injectContext(
          `()=>{
            const ef=createExtendForm(context);
            ef.${lifeCyleName}();
            }`,
          false,
          formConfig.modelName
        )};
      lifeCycleFun();`;
      } else if (formConfig[lifeCyleName]) {
        lifeCycleCode = `const lifeCycleFun=${lowcode.injectContext(
          `()=>{${formConfig[lifeCyleName]}}`,
          false,
          formConfig.modelName
        )};
      lifeCycleFun();`;
      }
      break;
    case 'inputFormData':
    case 'formValite':
    case 'outputFormData':
      if (formConfig.frontCode) {
        lifeCycleCode =
          `${formFunName}=` +
          lowcode.injectContext(
            `(formData)=>{
              const ef=createExtendForm(context);
              return ef.${lifeCyleName}(formData);
            };`,
            true,
            formConfig.modelName
          );
      } else if (formConfig[formFunName]) {
        lifeCycleCode =
          `${formFunName}=` +
          lowcode.injectContext(`(formData)=>{${formConfig[formFunName]}}`, true, formConfig.modelName);
      }
      break;
    case 'beforeSubmit':
    case 'afterSave':
      if (formConfig.frontCode && formConfig.frontCode.includes(lifeCyleName)) {
        lifeCycleCode =
          `${formFunName}=` +
          lowcode.injectContext(
            `(${['afterSave'].includes(lifeCyleName) ? 'id' : ''})=>{
              const ef=createExtendForm(context);
              ${['afterSave'].includes(lifeCyleName) ? 'context.formData.id=id;' : ''}
              ${
                ['afterSave', 'beforeSubmit'].includes(lifeCyleName) ? 'return ' : ''
              }ef.${lifeCyleName}(context.formData);
            };`,
            true,
            formConfig.modelName
          );
      }
      break;
    case 'afterSubmit':
      if (formConfig.frontCode && formConfig.frontCode.includes(lifeCyleName)) {
        lifeCycleCode =
          `${formFunName}=` +
          lowcode.injectContext(
            `(workflow)=>{
              const ef=createExtendForm(context);
              return ef.${lifeCyleName}(workflow);
            };`,
            true,
            formConfig.modelName
          );
      }
      break;
    case 'getCustomAssigneeList':
      if (formConfig.frontCode) {
        lifeCycleCode =
          `${formFunName}=` +
          lowcode.injectContext(
            `()=>{
              const ef=createExtendForm(context);
              return ef.${lifeCyleName}();
            };`,
            true,
            formConfig.modelName
          );
      } else if (formConfig[lifeCyleName]) {
        lifeCycleCode = `${formFunName}=${lowcode.injectContext(
          `()=>{${formConfig[lifeCyleName]}}`,
          false,
          formConfig.modelName
        )};`;
      }
      break;
    default:
      break;
  }

  return lifeCycleCode;
}
