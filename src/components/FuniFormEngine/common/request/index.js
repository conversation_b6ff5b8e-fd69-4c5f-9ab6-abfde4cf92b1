const fetchPageRulesAsync = async function (page_id, busType) {
  let url = `/as/pageBusRel/findByPageId/${page_id}/${busType}`;
  const result = window.$http.fetch(url);
  return result;
};

const postQueryExpressionVariableForEggAsync = async function (params) {
  let url = `/bpmn/businessManage/queryExpressionVariableForEgg`;
  const result = window.$http.post(url, params);
  return result;
};

const postPageFindAllAsync = async function (params) {
  let url = `/as/page/findAll`;
  const result = window.$http.post(url, params);
  return result;
};

const postOpsDicTypeListAsync = async function (params) {
  let url = `/csops/dic/type/list`;
  const result = window.$http.post(url, params);
  return result;
};

const postCcsDicTypeListAsync = async function (params) {
  let url = `/csccs/dicTypeList/typeList`;
  const result = window.$http.post(url, params);
  return result;
};

const postFieldFindAllAsync = async function (params) {
  let url = `/as/modelField/findAll`;
  const result = window.$http.post(url, params);
  return result;
};

const postFieldFindAllTreeAsync = async function (params) {
  let url = `/as/modelField/findAllTree`;
  const result = await window.$http.post(url, params);
  if (result && result.list && result.list.length > 0) {
    for (let i = 0; i < result.list.length; i++) {
      let item = result.list[i];
      if (item.data_type === 'many_to_many' || item.data_type === 'master_detail') {
        let model_id;
        try {
          const config = JSON.parse(item.config);
          model_id =
            item.data_type === 'many_to_many' ? config.association_table_model_id : config.correlation_model_id;
        } catch (e) {
          console.log(e);
        }
        let { list } = await postFieldFindAllAsync({
          model_id
        });
        if (list && list.length > 0) {
          list.forEach(element => {
            element.joinFieldName = item.code;
          });
        }
        result.list[i].children = list;
      }
    }
  }
  return result;
};

const postPageFormatUpdateAsync = async function (params) {
  let url = `/as/pageFormat/update`;
  const result = window.$http.post(url, params);
  return result;
};

const postModelFindAllAsync = async function (params) {
  let url = `/as/model/findAll`;
  const result = window.$http.post(url, params);
  return result;
};

const postApisListAsync = async function (params) {
  let url = `/as/apis/list`;
  const result = window.$http.post(url, params);
  return result;
};

const postAppCodeApiListAsync = async function (app_code, params) {
  let url = `/as/${app_code}/api/list`;
  const result = window.$http.post(url, params);
  return result;
};

const postSqlFindAllAsync = async function (params) {
  let url = `/as/sql/findAll`;
  const result = window.$http.post(url, params);
  return result;
};

const postPageSqlUpdateAsync = async function (params) {
  let url = `/as/pageSql/update`;
  const result = window.$http.post(url, params);
  return result;
};

const postSqlListAsync = async function (app_code, params) {
  let url = `/as/${app_code}/sql/list`;
  const result = window.$http.post(url, params);
  return result;
};

const postDicTypeListAsync = async function (app_code, params) {
  let url = `/as/${app_code}/getBusDicList`;
  const result = window.$http.post(url, params);
  return result;
};

const downloadExportListAsync = async function (app_code, params) {
  let url = `/as/${app_code}/exportList`;
  const result = window.$http.downloadFile(url, params);
  return result;
};

const proxyRequestAsync = async function (url, params, method = 'post', query = undefined, headers = undefined) {
  if (query) {
    const objectToQueryString = obj => {
      return Object.keys(obj)
        .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
        .join('&');
    };
    // 使用示例
    var queryString = objectToQueryString(query);
    if (queryString) {
      url = url + '?' + queryString;
    }
  }
  let result;
  if (headers) {
    result = window.$http[method](url, params, { headers });
  } else {
    result = window.$http[method](url, params);
  }
  return result;
};

// 代理请求 需要as 代理的时候用as代理，不需要as代理的时候直接请求
const proxyOtherRequestAsync = async function (url, params, isShowCurd = false) {
  const apiUrl = /^\/as.*\/api\/list$/;
  const sqlUrl = /^\/as.*\/sql\/list$/;
  const emuUrl = /^\/as.*\/typeCode\/list$/;
  const modelUrl = /^\/as.*\/model\/dicListV2$/;
  const modelUrl2 = /^\/as.*\/model\/dicList$/;
  const modelUrl3 = /^\/as.*\/model\/list$/;
  const match1 = url.match(apiUrl);
  const match2 = url.match(emuUrl);
  const match3 = url.match(modelUrl);
  const match4 = url.match(modelUrl2);
  const match5 = url.match(modelUrl3);
  const match6 = url.match(sqlUrl);
  if (match1 || match2 || match3 || match4 || match5 || match6) {
    return proxyRequestAsync(url, params);
  } else {
    const method = params.method || 'post';
    const query = params.query;
    const headers = params.headers;
    let realParams;
    if (isShowCurd) {
      delete params.query;
      delete params.headers;
      delete params.method;
      realParams = params;
    } else {
      realParams = params.params;
    }
    return proxyRequestAsync(url, realParams, method, query, headers);
  }
};

const isAsDataSoureUrl = function (url) {
  let isAsUrl = false;
  const apiUrl = /^\/as.*\/api\/list$/;
  const sqlUrl = /^\/as.*\/sql\/list$/;
  const modelUrl = /^\/as.*\/model\/list$/;
  const apiMatch = url.match(apiUrl);
  const sqlMatch = url.match(modelUrl);
  const modeLMatch = url.match(sqlUrl);
  return apiMatch || sqlMatch || modeLMatch;
};

const postApisInfoAsync = async function (params) {
  let url = `/as/apis/info`;
  const result = window.$http.post(url, params);
  return result;
};

const postSqlfindByIdAsync = async function (id) {
  let url = `/as/sql/findById/${id}`;
  const result = window.$http.fetch(url);
  return result;
};

export default {
  fetchPageRulesAsync,
  postQueryExpressionVariableForEggAsync,
  postPageFindAllAsync,
  postOpsDicTypeListAsync,
  postCcsDicTypeListAsync,
  postFieldFindAllAsync,
  postFieldFindAllTreeAsync,
  postPageFormatUpdateAsync,
  postModelFindAllAsync,
  postApisListAsync,
  postAppCodeApiListAsync,
  postSqlFindAllAsync,
  postPageSqlUpdateAsync,
  postSqlListAsync,
  downloadExportListAsync,
  proxyRequestAsync,
  proxyOtherRequestAsync,
  postDicTypeListAsync,
  isAsDataSoureUrl,
  postApisInfoAsync,
  postSqlfindByIdAsync
};
