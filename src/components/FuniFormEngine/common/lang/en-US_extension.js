/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-24 13:45:03
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-21 10:47:40
 * @FilePath: \src\components\FuniFormEngine\common\lang\en-US_extension.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export default {
  extension: {
    widgetLabel: {
      card: 'Card',
      alert: 'Alert',
      'funi-label': 'funi-label',
      'funi-group-title': 'funi-group-title',
      'funi-select': 'funi-select',
      'funi-edit-curd': 'funi-edit-curd',
      'funi-show-curd': 'funi-show-curd',
      'funi-sfc-dialog': 'dialog',
      'sfc-user': 'user',
      'sfc-org': 'org',
      'sfc-gantt': 'gantt',
      'sfc-guid': 'guid',
      'funi-region': 'region',
      'sfc-file-table': 'file-table',
      'sfc-draggable-curd': 'list',
      'sfc-operation-log': 'operation-log',
      'sfc-funi-operation-log': 'funi-operation-log',
      'sfc-user-name': 'create-user',
      'sfc-org-name': 'org-name',
      'funi-histogram-chart': 'histogram-chart',
      'sfc-hyper-link': 'link',
      'funi-money-input': 'mone-input',
      'sfc-iframe': 'iframe',
      'sfc-ol-map': 'ol-map',
      'funi-line-chart': 'line-chart',
      'funi-pie-chart': 'pie-chart',
      'funi-scatterplot-chart': 'scatterplot-chart',
      'funi-radar-chart': 'radar-chart',
      'sfc-video': 'video',
      'sfc-workflow-log': 'workflow-log',
      'sfc-custom': 'custom'
    },

    setting: {
      cardFolded: 'Folded',
      cardShowFold: 'Show Fold',
      cardWidth: 'Width Of Card',
      cardShadow: 'Shadow',

      alertTitle: 'Title',
      alertType: 'Type',
      description: 'Description',
      closable: 'Closable',
      closeText: 'Text On Close Btn',
      center: 'Center',
      showIcon: 'Show Icon',
      effect: 'Effect',
      url: 'url',
      title: 'title',
      requestOtherParam: '',
      modelId: 'model',
      typeCode: 'typeCode',
      isShowSearch: 'isShowSearch',
      selectType: 'selectType',
      isLineNumber: 'isLineNumber',
      searchConfig: 'searchConfig',
      rowKey: 'rowKey',
      columns: 'columns',
      pagination: 'pagination',
      actions: 'actions',
      buttons: 'buttons',
      actionbuttons: 'actionbuttons',
      tableMaxHeight: 'maxHeight',
      allowedScope: 'allowedScope',
      allowedSelfOrg: 'allowedSelfOrg',
      allowedSelfUser: 'allowedSelf',
      mode: 'mode',
      hasAddAuth: 'AddAuth',
      hasMinusAuth: 'hasMinusAuth',
      labellength: 'length',
      converter: 'converter',
      lvl: 'level',
      showAddressFull: 'showAddressFull',
      addressFullWidth: 'addressFullWidth',
      tooltip: 'tooltip',
      tooltipText: 'tooltipText',
      height: 'height(px)',
      minGridColumnWidth: 'minGridColumnWidth(px)',
      isFull: 'full',
      src: 'src',
      tipName: 'defaultName',
      color: 'color',
      customcfg: 'config',
      isInitUpdate: 'update',
      checkedOnlyOne: 'checkedOnlyOne',
      horizontalScrolling: 'horizontalScrolling',
      externalLink: 'link',
      moneyCapitalShow: 'moneyCapitalShow',
      precision: 'precision',
      unit: 'unit',
      showPrefix: 'showPrefix',
      api: 'APIs',
      fileListUrl: 'fileListUrl',
      delFileUrl: 'delFileUrl',
      uploadFileUrl: 'uploadFileUrl',
      checkFileUrl: 'checkFileUrl',
      checkLimit: 'checkLimit',
      mapId: 'mapId',
      viewSyncMapId: 'viewSyncMapId',
      projection: 'projection',
      baseMapLayer: 'baseMapLayer',
      showBaseMapLayerText: 'showBaseMapLayerText',
      mapCenter: 'mapCenter',
      zoom: 'zoom',
      baseLayers: 'baseLayers',
      layers: 'layers',
      layersApi: 'layersApi',
      layersApiMethod: 'layersApiMethod',
      layersRequest: 'layersRequest',
      legendBoxWidth: 'legendBoxWidth',
      showLayerTree: 'showLayerTree',
      showOverlay: 'showOverlay',
      showDraw: 'showDraw',
      drawTool: 'drawTool',
      drawStyle: 'drawStyle',
      legendFontSize: 'legendFontSize',
      isCircle: 'isCircle',
      splitNumber: 'splitNumber',
      isNumber: 'isNumber',
      fill: 'fill',
      indicatorData: 'indicatorData',
      enableSelect: 'enableSelect',
      maxSplit: 'maxSplit',
      drawTypeEnum: 'drawTypeEnum',
      oldSns: 'oldSns',
      geomDataType: 'geomDataType',
      modeValueMap: 'modeValueMap',
      boundAreaList: 'boundAreaList',
      boundAreaRequest: `boundAreaRequest`,
      boundAreaStyle: 'boundAreaStyle',
      layerAuthHeader: 'layerAuthHeader',
      highLightStyle: 'highLightStyle',
      wfsServer: 'wfsServer',
      fitParams: 'fitParams',
      modalContentRender: 'modalContentRender',
      legendShowFunc: 'legendShowFunc',
      drawToolCode: '',
      funiVideoStyle: 'funiVideoStyle',
      treeStyle: 'treeStyle',
      leftPadding: 'leftPadding',
      leftWidth: 'leftWidth',
      rightWidth: 'rightWidth',
      objectFit: 'objectFit',
      urlCode: '地址',
      viedoType: 'viedoType',
      dhConfig: 'dhConfig',
      isShowControl: 'isShowControl',
      isAuto: 'isAuto',
      dataTree: 'dataTree',
      dataTreeVideoUrl: 'dataTreeVideoUrl',
      isShowTreeParent: 'isShowTreeParent',
      videoSpeed: 'videoSpeed',
      treeApiParams: 'treeApiParams',
      customTreeApi: 'customTreeApi',
      getVideoApi: 'getVideoApi',
      operateCamera: 'operateCamera',
      operateDirectApi: 'operateDirectApi',
      defaultTreeProps: 'defaultTreeProps',
      reloadOnActive: 'reloadOnActive',
      baseMapChangeTool: 'baseMapChangeTool',
      isAutoShowModal: 'isAutoShowModal',
      drawCallback: 'drawCallback',
      legendListFunc: 'legendListFunc',
      hideAddBtn: 'hideAddBtn',
      useTools: 'useTools',
      businessId: 'businessId',
      renderer: 'renderer',
      attrs: 'attrs'
    }
  }
};
