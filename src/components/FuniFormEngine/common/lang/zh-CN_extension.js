/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2022-11-08 17:40:47
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-21 10:47:22
 * @FilePath: \src\components\FuniFormEngine\common\lang\zh-CN_extension.js
 * @Description:
 */
export default {
  extension: {
    widgetLabel: {
      card: '卡片',
      alert: '提示',
      curd: '列表',
      'funi-label': '标签',
      'funi-group-title': '组标题',
      'funi-select': '下拉选项',
      'funi-edit-curd': '编辑列表',
      'funi-show-curd': '展示列表',
      'funi-sfc-dialog': '弹窗',
      'sfc-user': '人员选择',
      'sfc-org': '部门选择',
      'sfc-gantt': '甘特图',
      'sfc-guid': '唯一编号',
      'funi-region': '地址',
      'sfc-file-table': '附件信息',
      'sfc-draggable-curd': '编辑列表',
      'sfc-operation-log': '操作记录',
      'sfc-funi-log': '操作日志',
      'sfc-funi-operation-log': '业务记录',
      'sfc-iframe': '嵌入网页',
      'sfc-ol-map': '地图',
      'sfc-user-name': '创建人',
      'sfc-org-name': '所属部门',
      'funi-histogram-chart': '柱状图',
      'sfc-hyper-link': '超链接',
      'funi-money-input': '金额输入',
      'funi-line-chart': '折线图',
      'funi-pie-chart': '饼装图',
      'funi-scatterplot-chart': '散点图',
      'funi-radar-chart': '雷达图',
      'sfc-video': '视频',
      'sfc-workflow-log': '工作流日志',
      'sfc-custom': '自定义'
    },

    setting: {
      cardFolded: '是否收起',
      cardShowFold: '显示折叠按钮',
      cardWidth: '卡片宽度',
      cardShadow: '显示阴影',
      alertTitle: '标题',
      alertType: '类型',
      description: '辅助性文字',
      closable: '是否可关闭',
      closeText: '关闭按钮文字',
      center: '文字居中',
      showIcon: '显示图标',
      effect: '显示效果',
      curdType: '类型',
      curdTitle: '标题',
      funiCurdTitle: '标题',
      isShowSearch: '显示搜索',
      isLineNumber: '行号',
      selectType: '数据选择列',
      searchConfig: '搜索设置',
      columns: '列',
      pagination: '是否分页',
      showIndex: '显示序号',
      url: 'url',
      title: '标题',
      requestOtherParam: '',
      modelId: '模型',
      typeCode: '编码',
      rowKey: 'rowKey',
      buttons: '按钮',
      actionbuttons: '功能按钮',
      actions: '操作列',
      tableMaxHeight: '最大高度',
      allowedScope: '选择范围',
      allowedSelfUser: '允许当前用户',
      allowedSelfOrg: '允许当前部门',
      mode: '选择模式',
      hasAddAuth: '新增权限',
      hasMinusAuth: '删除权限',
      labellength: '最大长度',
      converter: '转换函数',
      lvl: '可选级数',
      showAddressFull: '显示地址详情',
      addressFullWidth: '详情宽度',
      tooltip: '开启提示工具',
      tooltipText: '提示工具渲染函数',
      height: '高度(px)',
      minGridColumnWidth: '表格宽度(px)',
      isFull: '铺满',
      src: '资源地址',
      tipName: '默认序列名',
      color: '颜色',
      customcfg: '配置',
      isInitUpdate: '实时更新',
      checkedOnlyOne: '选中唯一选项',
      horizontalScrolling: '允许横向滚动',
      externalLink: '链接地址',
      moneyCapitalShow: '显示大写',
      precision: '数值精度',
      unit: '单位',
      showPrefix: '显示符号',
      api: 'APIs',
      fileListUrl: '列表接口',
      delFileUrl: '删除接口',
      uploadFileUrl: '删除接口',
      checkFileUrl: '校验接口',
      checkLimit: '多选限制数',
      mapId: 'mapId',
      viewSyncMapId: '视图同步Id',
      projection: '坐标系',
      baseMapLayer: '基础地图',
      showBaseMapLayerText: '显示地图标注',
      mapCenter: '地图中心点',
      zoom: '缩放级别',
      baseLayers: '底图图层服务',
      layers: '图层服务',
      layersApi: '图层服务接口',
      layersApiMethod: '接口请求方法',
      layersRequest: '接口请求配置',
      legendBoxWidth: '图例宽度',
      showLayerTree: '显示图层树',
      showOverlay: '显示弹窗',
      showDraw: '显示绘制功能',
      drawTool: '绘制工具',
      drawStyle: '绘制样式',
      legendFontSize: '图例字体大小',
      isCircle: '是否圆形图表',
      splitNumber: '雷达圈数',
      isNumber: '是否显示数字',
      fill: '是否填充',
      indicatorData: '指标',
      enableSelect: '允许选中效果',
      maxSplit: '图斑最大拆分数',
      drawTypeEnum: '绘制输出类型',
      oldSns: 'oldSns',
      geomDataType: '经纬度数据类型',
      modeValueMap: '值字段映射',
      boundAreaList: '边界区域接口',
      boundAreaRequest: '边界请求配置',
      boundAreaStyle: '边界样式',
      layerAuthHeader: '权限请求头',
      highLightStyle: '高亮样式',
      wfsServer: 'wfs服务地址',
      fitParams: '自适应参数',
      modalContentRender: '弹窗渲染函数',
      legendShowFunc: '图例回调函数',
      drawToolCode: '',
      funiVideoStyle: '视频样式',
      treeStyle: '树形样式',
      leftPadding: '左边距',
      leftWidth: '左侧宽度',
      rightWidth: '右侧宽度',
      objectFit: '适配方式',
      urlCode: '地址',
      viedoType: '视频类型',
      dhConfig: '大华配置',
      isShowControl: '显示云台',
      isAuto: '自动播放',
      dataTree: '视频列表',
      dataTreeVideoUrl: '视频地址',
      isShowTreeParent: '是否展示父级',
      videoSpeed: '云台操作速度',
      treeApiParams: '接口参数',
      customTreeApi: '自定义Api',
      getVideoApi: '视频获取接口',
      operateCamera: '云台控制变焦',
      operateDirectApi: '云台控制方向',
      defaultTreeProps: '组件字段',
      reloadOnActive: '激活刷新',
      baseMapChangeTool: '切换底图',
      isAutoShowModal: '是否自动弹框',
      drawCallback: '绘制回调函数',
      legendListFunc: '图例数组过滤',
      hideAddBtn: '隐藏添加按钮',
      useTools: '显示小工具',
      businessId: '业务件ID',
      renderer: '渲染函数',
      attrs: '组件属性'
    }
  }
};
