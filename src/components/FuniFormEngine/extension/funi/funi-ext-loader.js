/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-16 17:12:09
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-21 16:08:26
 * @FilePath: \src\components\FuniFormEngine\extension\funi\funi-ext-loader.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import {
  spliceBasicFieldSchema,
  addContainerWidgetSchema,
  addAdvancedFieldSchema,
  addCustomWidgetSchema,
  addSystemFieldSchema
} from '../../widget-panel/widgetsConfig';
import * as PERegister from '../../setting-panel/propertyRegister';
import * as PEFactory from '../../setting-panel/property-editor-factory.jsx';
import { registerFWGenerator } from '../../common/utils/funi-sfc-generator';
import {
  funiLabelSchema,
  funiGroupTitleSchema,
  funiSelectSchema,
  funiCurdSchema,
  funiShowCurdSchema,
  funiSfcDialogSchema,
  sfcUserSchema,
  sfcOrgSchema,
  sfcGanttSchema,
  sfcFileTableSchema,
  sfcGuidSchema,
  funiRegionSchema,
  sfcDraggableCurdSchema,
  sfcOperationLogSchema,
  sfcFuniOperationLogSchema,
  sfcFuniLogSchema,
  sfcIframeSchema,
  sfcUserNameSchema,
  sfcOrgNameSchema,
  funiHistogramChartSchema,
  sfchyperLinkSchema,
  funiMoneyInputSchema,
  sfcOlMapSchema,
  funiLineChartSchema,
  funiPieChartSchema,
  funiScatterplotChartSchema,
  funiRadarChartSchema,
  sfcVideoSchema,
  sfcWorkflowLogSchema,
  sfcCustomSchema
} from './funi-ext-schema';
import FuniSelectWidget from './funi-select-widget/index.vue';
import FuniCurdWidget from './funi-edit-curd-widget/index.vue';
import FuniShowCurdWidget from './funi-show-curd-widget/index.vue';
import FuniLabelWidget from './funi-label-widget/index.vue';
import SfcUserWidget from './sfc-user-widget/index.vue';
import SfcOrgWidget from './sfc-org-widget/index.vue';
import FuniGroupTitleWidget from './funi-group-title-widget/index.vue';
import SfcDialogWidget from './funi-sfc-dialog-widget/sfc-dialog-widget.vue';
import FuniSfcDialogWidget from './funi-sfc-dialog-widget/index.vue';
import SfcGanttWidget from './sfc-gantt-widget/index.vue';
import SfcFileTableWidget from './sfc-file-table-widget/index.vue';
import SfcGuidWidget from './sfc-guid-widget/index.vue';
import SfcOperationLogWidget from './sfc-operation-log-widget/index.vue';
import SfcFuniOperationLogWidget from './sfc-funi-operation-log-widget/index.vue';
import SfcFuniLogWidget from './sfc-funi-log-widget/index.vue';
import FuniRegionWidget from './funi-region-widget/index.vue';
import SfcDraggableCurdWidget from './sfc-draggable-curd-widget/index.vue';
import SfcIframeWidget from './sfc-iframe-widget/index.vue';
import SfcUserNameWidget from './sfc-user-name-widget/index.vue';
import SfcOrgNameWidget from './sfc-org-name-widget/index.vue';
import FuniHistogramChartWidget from './funi-histogram-chart-widget/index.vue';
import FuniLineChartWidget from './funi-line-chart-widget/index.vue';
import FuniPieChartWidget from './funi-pie-chart-widget/index.vue';
import FuniScatterplotChartWidget from './funi-scatterplot-chart-widget/index.vue';
import FuniRadarChartWidget from './funi-radar-chart-widget/index.vue';
import SfcHyperLinkWidget from './sfc-hyper-link-widget/index.vue';
import FuniMoneyInputWidget from './funi-money-input-widget/index.vue';
import SfcOlMapWidget from './sfc-ol-map-widget/index.vue';
import SfcVideoWidget from './sfc-video-widget/index.vue';
import SfcWorkflowLogWidget from './sfc-workflow-log-widget/index.vue';
import SfcCustomWidget from './sfc-custom-widget/index.vue';
import {
  funiLabelTemplateGenerator,
  funiGroupTitleTemplateGenerator,
  funiSelectTemplateGenerator,
  funiCurdTemplateGenerator,
  funiShowCurdTemplateGenerator,
  sfcUserTemplateGenerator,
  sfcOrgTemplateGenerator,
  sfcGanttTemplateGenerator,
  sfcFileTableTemplateGenerator,
  sfcGuidTemplateGenerator,
  funiRegionTemplateGenerator,
  sfcOperationLogTemplateGenerator,
  sfcFuniOperationLogTemplateGenerator,
  sfcFuniLogTemplateGenerator,
  sfcIframeTemplateGenerator,
  sfcUserNameTemplateGenerator,
  sfcOrgNameTemplateGenerator,
  funiHistogramChartTemplateGenerator,
  funiLineChartTemplateGenerator,
  funiPieChartTemplateGenerator,
  funiScatterplotChartTemplateGenerator,
  funiRadarChartTemplateGenerator,
  sfcHyperLinkTemplateGenerator,
  funiMoneyInputTemplateGenerator,
  sfcOlMapTemplateGenerator,
  sfcVideoTemplateGenerator,
  sfcWorkflowLogTemplateGenerator,
  sfcCustomTemplateGenerator
} from './funi-ext-sfc-generator';
function loadFuniExt(app) {
  addAdvancedFieldSchema(sfcGuidSchema);
  app.component(SfcGuidWidget.name, SfcGuidWidget);
  PERegister.registerCPEditor(
    app,
    'sfc-guid-url',
    'sfc-guid-url-editor',
    PEFactory.createInputTextEditor('url', 'extension.setting.url')
  );
  registerFWGenerator('sfc-guid', sfcGuidTemplateGenerator);

  addAdvancedFieldSchema(funiLabelSchema);
  app.component(FuniLabelWidget.name, FuniLabelWidget);
  PERegister.registerCPEditor(
    app,
    'funi-label-length',
    'funi-label-length-editor',
    PEFactory.createInputNumberEditor('length', 'extension.setting.labellength')
  );
  registerFWGenerator('funi-label', funiLabelTemplateGenerator);

  addAdvancedFieldSchema(funiGroupTitleSchema);
  app.component(FuniGroupTitleWidget.name, FuniGroupTitleWidget);
  PERegister.registerCPEditor(
    app,
    'funi-group-title-title',
    'funi-group-title-title-editor',
    PEFactory.createInputTextEditor('title', 'extension.setting.title')
  );

  //超链接
  addAdvancedFieldSchema(sfchyperLinkSchema);
  app.component(SfcHyperLinkWidget.name, SfcHyperLinkWidget);
  PERegister.registerCPEditor(
    app,
    'sfc-hyper-link-src',
    'sfc-hyper-link-src-editor',
    PEFactory.createFuniVariableSetterEditor('src', 'extension.setting.src')
  );
  registerFWGenerator('sfc-hyper-link', sfcHyperLinkTemplateGenerator);

  //金额输入
  addAdvancedFieldSchema(funiMoneyInputSchema);
  app.component(FuniMoneyInputWidget.name, FuniMoneyInputWidget);
  PERegister.registerCPEditor(
    app,
    'funi-money-input-moneyCapitalShow',
    'funi-money-input-moneyCapitalShow',
    PEFactory.createBooleanEditor('moneyCapitalShow', 'extension.setting.moneyCapitalShow')
  );
  PERegister.registerCPEditor(
    app,
    'funi-money-input-showPrefix',
    'funi-money-input-showPrefix',
    PEFactory.createBooleanEditor('showPrefix', 'extension.setting.showPrefix')
  );
  PERegister.registerCPEditor(
    app,
    'funi-money-input-precision',
    'funi-money-input-src-precision',
    PEFactory.createInputNumberEditor('precision', 'extension.setting.precision')
  );
  PERegister.registerCPEditor(
    app,
    'funi-money-input-unit',
    'funi-money-input-src-unit',
    PEFactory.createInputNumberEditor('unit', 'extension.setting.unit')
  );
  registerFWGenerator('funi-money-input', funiMoneyInputTemplateGenerator);

  addAdvancedFieldSchema(funiRegionSchema);
  app.component(FuniRegionWidget.name, FuniRegionWidget);
  PERegister.registerCPEditor(
    app,
    'funi-region-lvl',
    'funi-region-lvl-editor',
    PEFactory.createInputNumberEditor('lvl', 'extension.setting.lvl', { min: 1, max: 5 })
  );
  PERegister.registerCPEditor(
    app,
    'funi-region-showAddressFull',
    'funi-region-showAddressFull-editor',
    PEFactory.createBooleanEditor('showAddressFull', 'extension.setting.showAddressFull')
  );
  PERegister.registerCPEditor(
    app,
    'funi-region-addressFullWidth',
    'funi-region-addressFullWidth-editor',
    PEFactory.createInputTextEditor('addressFullWidth', 'extension.setting.addressFullWidth')
  );
  registerFWGenerator('funi-region', funiRegionTemplateGenerator);

  addAdvancedFieldSchema(sfcUserSchema);
  app.component(SfcUserWidget.name, SfcUserWidget);

  registerFWGenerator('sfc-user', sfcUserTemplateGenerator);
  PERegister.registerCPEditor(
    app,
    'sfc-user-allowedScope',
    'sfc-user-allowedScope-editor',
    PEFactory.createUserOrgScopeEditor('allowedScope', 'extension.setting.allowedScope')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-user-allowedSelf',
    'sfc-user-allowedSelf-editor',
    PEFactory.createBooleanEditor('allowedSelf', 'extension.setting.allowedSelfUser')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-user-mode',
    'sfc-user-mode-editor',
    PEFactory.createSelectEditor('mode', 'extension.setting.mode', {
      optionItems: [
        { label: '多选', value: 'multiple' },
        { label: '单选', value: 'single' }
      ]
    })
  );

  addAdvancedFieldSchema(sfcOrgSchema);

  PERegister.registerCPEditor(
    app,
    'sfc-org-allowedScope',
    'sfc-org-allowedScope-editor',
    PEFactory.createUserOrgScopeEditor('allowedScope', 'extension.setting.allowedScope', true)
  );
  PERegister.registerCPEditor(
    app,
    'sfc-org-allowedSelf',
    'sfc-org-allowedSelf-editor',
    PEFactory.createBooleanEditor('allowedSelf', 'extension.setting.allowedSelfOrg')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-org-mode',
    'sfc-org-mode-editor',
    PEFactory.createSelectEditor('mode', 'extension.setting.mode', {
      optionItems: [
        { label: '多选', value: 'multiple' },
        { label: '单选', value: 'single' }
      ]
    })
  );

  app.component(SfcOrgWidget.name, SfcOrgWidget);

  registerFWGenerator('sfc-org', sfcOrgTemplateGenerator);

  registerFWGenerator('funi-group-title', funiGroupTitleTemplateGenerator);

  spliceBasicFieldSchema(funiSelectSchema, 3);

  app.component(FuniSelectWidget.name, FuniSelectWidget);
  PERegister.registerCPEditor(
    app,
    'funi-select-url',
    'funi-select-url-editor',
    PEFactory.createInputTextEditorHasModel('url', 'extension.setting.url')
  );
  PERegister.registerCPEditor(
    app,
    'funi-select-typeCode',
    'funi-select-typeCode-editor',
    PEFactory.createInputTextEditorHasModel('typeCode', 'extension.setting.typeCode')
  );
  PERegister.registerDSEditor(
    app,
    'funi-select-modelOption',
    'funi-select-modelOption-editor',
    PEFactory.createSimpleDatasourceEditor('modelOption')
  );
  PERegister.registerCPEditor(
    app,
    'funi-select-options',
    'funi-select-options-editor',
    PEFactory.createOptionsEditor('options')
  );
  PERegister.registerCPEditor(
    app,
    'funi-select-isInitUpdate',
    'funi-select-isInitUpdate-editor',
    PEFactory.createBooleanEditor('isInitUpdate', 'extension.setting.isInitUpdate')
  );
  PERegister.registerCPEditor(
    app,
    'funi-select-checkedOnlyOne',
    'funi-select-checkedOnlyOne-editor',
    PEFactory.createBooleanEditor('checkedOnlyOne', 'extension.setting.checkedOnlyOne')
  );
  registerFWGenerator('funi-select', funiSelectTemplateGenerator);

  // addAdvancedFieldSchema(funiCurdSchema);
  // app.component(FuniCurdWidget.name, FuniCurdWidget);
  // PERegister.registerCPEditor(
  //   app,
  //   'funi-edit-curd-rowKey',
  //   'funi-edit-curd-rowKey-editor',
  //   PEFactory.createInputTextEditor('rowKey', 'extension.setting.rowKey')
  // );
  // PERegister.registerCPEditor(
  //   app,
  //   'funi-edit-curd-rowKey',
  //   'funi-edit-curd-rowKey-editor',
  //   PEFactory.createInputTextEditor('rowKey', 'extension.setting.rowKey')
  // );
  // PERegister.registerDSEditor(
  //   app,
  //   'funi-edit-curd-requestOtherParam',
  //   'funi-edit-curd-requestOtherParam-editor',
  //   PEFactory.createDatasourceEditor('requestOtherParam', 'extension.setting.requestOtherParam')
  // );
  // PERegister.registerCPEditor(
  //   app,
  //   'funi-edit-curd-pagination',
  //   'funi-edit-curd-pagination-editor',
  //   PEFactory.createBooleanEditor('pagination', 'extension.setting.pagination')
  // );

  // PERegister.registerDSEditor(
  //   app,
  //   'funi-edit-curd-columns',
  //   'funi-edit-curd-columns-editor',
  //   PEFactory.createColumnEditor('edit')
  // );

  // //配置操作列
  // PERegister.registerDSEditor(
  //   app,
  //   'funi-edit-curd-actions',
  //   'funi-eid-curd-actions-editor',
  //   PEFactory.createButtonsEditor(
  //     [
  //       {
  //         label: '新建',
  //         value: 'add',
  //         defaultProps: {
  //           componentType: 'link',
  //           type: 'primary',
  //           size: 'default',
  //           showIcon: 'onlyIcon',
  //           icon: 'ep:circle-plus',
  //           iconPosition: 'left'
  //         },
  //         getDefaultAttrs(options) {
  //           return `context.refs.${options.name}.push();`;
  //         }
  //       },
  //       {
  //         label: '删除',
  //         value: 'delete',
  //         defaultProps: {
  //           componentType: 'link',
  //           type: 'primary',
  //           size: 'default',
  //           showIcon: 'onlyIcon',
  //           icon: 'ep:remove',
  //           iconPosition: 'left'
  //         },
  //         getDefaultAttrs(options) {
  //           return `context.refs.${options.name}.delete(index)`;
  //         }
  //       }
  //     ],
  //     'actions'
  //   )
  // );

  // //配置按钮属性
  // PERegister.registerDSEditor(
  //   app,
  //   'funi-edit-curd-buttons',
  //   'funi-eid-curd-buttons-editor',
  //   PEFactory.createButtonsEditor([
  //     {
  //       label: '新建',
  //       value: 'add',
  //       defaultProps: {
  //         componentType: 'button',
  //         type: 'primary',
  //         size: 'default',
  //         showIcon: 'txtIcon',
  //         icon: 'ep:circle-plus',
  //         iconPosition: 'left'
  //       },
  //       getDefaultAttrs(options) {
  //         return `context.refs.${options.name}.push();`;
  //       }
  //     }
  //   ])
  // );

  registerFWGenerator('funi-edit-curd', funiCurdTemplateGenerator);

  //可拖动的curd
  addAdvancedFieldSchema(sfcDraggableCurdSchema);
  app.component(SfcDraggableCurdWidget.name, SfcDraggableCurdWidget);
  PERegister.registerCPEditor(
    app,
    'sfc-draggable-curd-rowKey',
    'sfc-draggable-curd-rowKey-editor',
    PEFactory.createInputTextEditor('rowKey', 'extension.setting.rowKey')
  );
  PERegister.registerDSEditor(
    app,
    'sfc-draggable-curd-requestOtherParam',
    'sfc-draggable-curd-requestOtherParam-editor',
    PEFactory.createDatasourceEditor('requestOtherParam', 'extension.setting.requestOtherParam')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-draggable-curd-useTools',
    'sfc-draggable-curd-useTools-editor',
    PEFactory.createBooleanEditor('useTools', 'extension.setting.useTools')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-draggable-curd-pagination',
    'sfc-draggable-curd-pagination-editor',
    PEFactory.createBooleanEditor('pagination', 'extension.setting.pagination')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-draggable-curd-showIndex',
    'sfc-draggable-curd-showIndex-editor',
    PEFactory.createBooleanEditor('showIndex', 'extension.setting.showIndex')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-draggable-curd-horizontalScrolling',
    'sfc-draggable-curd-horizontalScrolling-editor',
    PEFactory.createBooleanEditor('horizontalScrolling', 'extension.setting.horizontalScrolling')
  );

  //配置操作列
  PERegister.registerCPEditor(
    app,
    'sfc-draggable-curd-actions',
    'sfc-draggable-curd-actions-editor',
    PEFactory.createButtonsEditor(
      [
        {
          label: '新建',
          value: 'add',
          defaultProps: {
            componentType: 'link',
            type: 'primary',
            size: 'default',
            showIcon: 'onlyIcon',
            icon: 'ep:circle-plus',
            iconPosition: 'left'
          },
          getDefaultAttrs(options) {
            return `context.refs.${options.name}.push();`;
          }
        },
        {
          label: '删除',
          value: 'delete',
          defaultProps: {
            componentType: 'link',
            type: 'primary',
            size: 'default',
            showIcon: 'onlyIcon',
            icon: 'ep:remove',
            iconPosition: 'left'
          },
          getDefaultAttrs(options) {
            return `context.refs.${options.name}.delete({index})`;
          }
        }
      ],
      'actions'
    )
  );

  //配置按钮属性
  PERegister.registerCPEditor(
    app,
    'sfc-draggable-curd-buttons',
    'sfc-draggable-curd-buttons-editor',
    PEFactory.createButtonsEditor([
      {
        label: '新建',
        value: 'add',
        defaultProps: {
          componentType: 'button',
          type: 'primary',
          size: 'default',
          showIcon: 'txtIcon',
          icon: 'ep:circle-plus',
          iconPosition: 'left'
        },
        getDefaultAttrs(options) {
          return `context.refs.${options.name}.push();`;
        }
      }
    ])
  );

  addAdvancedFieldSchema(funiShowCurdSchema);
  app.component(FuniShowCurdWidget.name, FuniShowCurdWidget);
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-rowKey',
    'funi-show-curd-rowKey-editor',
    PEFactory.createInputTextEditor('rowKey', 'extension.setting.rowKey')
  );
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-tableMaxHeight',
    'funi-show-curd-tableMaxHeight-editor',
    PEFactory.createInputTextEditor('tableMaxHeight', 'extension.setting.tableMaxHeight')
  );
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-pagination',
    'funi-show-curd-pagination-editor',
    PEFactory.createBooleanEditor('pagination', 'extension.setting.pagination')
  );
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-isShowSearch',
    'funi-show-curd-isShowSearch-editor',
    PEFactory.createSearchConfigEditor('isShowSearch', 'searchConfigGroup', 'extension.setting.isShowSearch')
  );
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-useTools',
    'funi-show-curd-useTools-editor',
    PEFactory.createBooleanEditor('useTools', 'extension.setting.useTools')
  );
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-isLineNumber',
    'funi-show-curd-isLineNumber-editor',
    PEFactory.createBooleanEditor('isLineNumber', 'extension.setting.isLineNumber')
  );
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-reloadOnActive',
    'funi-show-curd-reloadOnActive-editor',
    PEFactory.createBooleanEditor('reloadOnActive', 'extension.setting.reloadOnActive')
  );
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-selectType',
    'funi-show-curd-selectType-editor',
    PEFactory.createCheckLimitSelectEditor('selectType', 'extension.setting.selectType', {
      optionItems: [
        { label: '多选样式', value: 'selection' },
        { label: '单选样式', value: 'radio' },
        { label: '关闭', value: '' }
      ]
    })
  );
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-url',
    'funi-show-curd-url-editor',
    PEFactory.createCurdUrlEditor('url', 'extension.setting.url')
  );
  PERegister.registerDSEditor(
    app,
    'funi-show-curd-requestOtherParam',
    'funi-show-curd-requestOtherParam-editor',
    PEFactory.createDatasourceEditor('requestOtherParam', 'extension.setting.requestOtherParam')
  );
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-columns',
    'funi-show-curd-columns-editor',
    PEFactory.createColumnEditor('show')
  );
  //配置操作列
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-actions',
    'funi-show-curd-actions-editor',
    PEFactory.createButtonsEditor(
      [
        {
          label: '删除',
          value: 'delete',
          defaultProps: {
            componentType: 'link',
            type: 'primary',
            size: 'default',
            showIcon: 'onlyIcon',
            icon: 'ep:remove',
            iconPosition: 'left'
          },
          getDefaultAttrs(options) {
            return `context.refs.${options.name}.delete({index})`;
          }
        },
        {
          label: '自定义',
          value: 'custom',
          defaultProps: {
            componentType: 'button',
            type: 'primary',
            size: 'default',
            showIcon: 'txtIcon',
            icon: 'ep:circle-plus',
            iconPosition: 'left'
          },
          getDefaultAttrs(options) {
            return ``;
          }
        },
        {
          label: '列表页-删除',
          value: 'list-delete',
          defaultProps: {
            componentType: 'link',
            type: 'primary',
            size: 'default',
            showIcon: 'noIcon',
            label: '删除',
            iconPosition: 'left'
          },
          getDefaultAttrs(options) {
            return `
            context.params.row=row;
              const id=context.params.row.id;
                    const loading=context.loading({ fullscreen: true });
                    window.$http.post('${options.deleteApi}',{
                      id,
                      model_id:'${options.model_id}',
                      extendData: getExtendParams('${options.buttonId}')
                    }).then(()=>{
                      loading.close();
                      context.notify({title:'成功',message:'删除成功',type:'success'});
                      context.refs.${options.name}.reload()
                    }).catch(()=>{
                      loading.close();
                    })`;
          }
        },
        {
          label: '导出',
          value: 'export',
          defaultProps: {
            componentType: 'link',
            type: 'primary',
            size: 'default',
            showIcon: 'txtIcon',
            icon: '',
            iconPosition: 'left'
          },
          getDefaultAttrs(options) {
            return `
              context.params.row=row;
              const ids=[context.params.row.id];
                      if(ids&&ids.length>0){
                        const loading=context.loading({ fullscreen: true });
                        window.$http.downloadFile('${options.lineExportApi}',{
                          page_id:'${options.page_id}',
                          model_id:'${options.model_id}',
                          component_id:'${options.component_id}',
                          params:[{key:'id',value:ids,operator:'IN'}],
                          conditions:[],
                          extendData: getExtendParams('${options.buttonId}')
                        }).then(()=>{
                          loading.close();
                          context.notify({title:'成功',message:'导出成功',type:'success'});
                        })
                        .catch((ex)=>{
                          loading.close();
                          context.notify({title:'失败',message:'导出失败',type:'error'});
                        })
                      } else {
                        context.notify({title:'提示',message:'未找到有效数据!',type:'info'});`;
          }
        },
        {
          label: '撤回',
          value: 'withdraw',
          defaultProps: {
            componentType: 'link',
            type: 'primary',
            size: 'default',
            showIcon: 'txtIcon',
            icon: '',
            iconPosition: 'left'
          },
          getDefaultAttrs(options) {
            return `
              context.params.row=row;
              const id=context.params.row.id;
                    const loading=context.loading({ fullscreen: true });
                    window.$http.post('${options.withdrawApi}',{
                      businessId: context.params.row.business_id,
                      businessExecutionType:'REVOCATION',
                      extendData: getExtendParams('${options.buttonId}')
                    }).then(()=>{
                      loading.close();
                      context.notify({title:'成功',message:'撤回成功',type:'success'});
                      context.refs.funiListPageRef.reload({resetPage:false});
                    }).catch(()=>{
                      loading.close();
                    })`;
          }
        }
      ],
      'actions'
    )
  );
  //配置按钮属性
  PERegister.registerCPEditor(
    app,
    'funi-show-curd-buttons',
    'funi-show-curd-buttons-editor',
    PEFactory.createButtonsEditor([
      {
        label: '自定义',
        value: 'custom',
        defaultProps: {
          componentType: 'button',
          type: 'primary',
          size: 'default',
          showIcon: 'txtIcon',
          icon: 'ep:circle-plus',
          iconPosition: 'left'
        },
        getDefaultAttrs(options) {
          return ``;
        }
      },
      {
        label: '全部导出',
        value: 'allExport',
        defaultProps: {
          componentType: 'button',
          type: 'primary',
          size: 'default',
          showIcon: 'txtIcon',
          icon: '',
          iconPosition: 'left'
        },
        getDefaultAttrs(options) {
          return `
            const loading=context.loading({ fullscreen: true });
                  window.$http.downloadFile('${options.allExportApi}',{
                    page_id:'${options.page_id}',
                    model_id:'${options.model_id}',
                    component_id:'${options.component_id}',
                    extendData: getExtendParams('${options.buttonId}')
                  }).then(()=>{
                    loading.close();
                    context.notify({title:'成功',message:'导出成功',type:'success'});
                  })
                  .catch((ex)=>{
                    console.log('ex',ex);
                    context.notify({title:'失败',message:'导出失败',type:'error'});
                    loading.close();
                  })`;
        }
      }
    ])
  );
  registerFWGenerator('funi-show-curd', funiShowCurdTemplateGenerator);

  //附件上传
  addAdvancedFieldSchema(sfcFileTableSchema);
  app.component(SfcFileTableWidget.name, SfcFileTableWidget);
  registerFWGenerator('sfc-file-table', sfcFileTableTemplateGenerator);
  PERegister.registerCPEditor(
    app,
    'sfc-file-table-hideAddBtn',
    'sfc-file-table-hideAddBtn-editor',
    PEFactory.createBooleanVarEditor('hideAddBtn', 'hideAddBtnCode', 'extension.setting.hideAddBtn')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-file-table-fileListUrl',
    'sfc-file-table-fileListUrl-editor',
    PEFactory.createSelectApisEditor('fileListUrl', 'extension.setting.fileListUrl')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-file-table-delFileUrl',
    'sfc-file-table-delFileUrl-editor',
    PEFactory.createSelectApisEditor('delFileUrl', 'extension.setting.delFileUrl')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-file-table-uploadFileUrl',
    'sfc-file-table-uploadFileUrl-editor',
    PEFactory.createSelectApisEditor('uploadFileUrl', 'extension.setting.uploadFileUrl')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-file-table-checkFileUrl',
    'sfc-file-table-checkFileUrl-editor',
    PEFactory.createSelectApisEditor('checkFileUrl', 'extension.setting.checkFileUrl')
  );

  //操作记录
  addAdvancedFieldSchema(sfcOperationLogSchema);
  app.component(SfcOperationLogWidget.name, SfcOperationLogWidget);
  registerFWGenerator('sfc-operation-log', sfcOperationLogTemplateGenerator);

  PERegister.registerCPEditor(
    app,
    'sfc-operation-log-api',
    'sfc-operation-log-api-editor',
    PEFactory.createSelectApisEditor('api', 'extension.setting.api')
  );

  //业务操作记录
  addAdvancedFieldSchema(sfcFuniOperationLogSchema);
  app.component(SfcFuniOperationLogWidget.name, SfcFuniOperationLogWidget);
  registerFWGenerator('sfc-funi-operation-log', sfcFuniOperationLogTemplateGenerator);

  PERegister.registerCPEditor(
    app,
    'sfc-funi-operation-log-api',
    'sfc-funi-operation-log-api-editor',
    PEFactory.createSelectApisEditor('api', 'extension.setting.api')
  );

  //操作日志
  addAdvancedFieldSchema(sfcFuniLogSchema);
  app.component(SfcFuniLogWidget.name, SfcFuniLogWidget);
  registerFWGenerator('sfc-funi-log', sfcFuniLogTemplateGenerator);

  PERegister.registerCPEditor(
    app,
    'sfc-funi-log-api',
    'sfc-funi-log-api-editor',
    PEFactory.createSelectApisEditor('api', 'extension.setting.api')
  );

  //iframe
  addAdvancedFieldSchema(sfcIframeSchema);
  app.component(SfcIframeWidget.name, SfcIframeWidget);
  registerFWGenerator('sfc-iframe', sfcIframeTemplateGenerator);
  PERegister.registerCPEditor(
    app,
    'sfc-iframe-src',
    'sfc-iframe-src-editor',
    PEFactory.createFuniVariableSetterEditor('src', 'extension.setting.src')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-iframe-height',
    'sfc-iframe-height-editor',
    PEFactory.createInputTextEditor('height', 'extension.setting.height')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-iframe-isFull',
    'sfc-iframe-isFull-editor',
    PEFactory.createBooleanEditor('isFull', 'extension.setting.isFull')
  );

  //甘特图
  addCustomWidgetSchema(sfcGanttSchema);
  app.component(SfcGanttWidget.name, SfcGanttWidget);
  PERegister.registerCPEditor(
    app,
    'sfc-gantt-height',
    'sfc-gantt-height-editor',
    PEFactory.createInputNumberEditor('height', 'extension.setting.height')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-gantt-minGridColumnWidth',
    'sfc-gantt-minGridColumnWidth-editor',
    PEFactory.createInputNumberEditor('minGridColumnWidth', 'extension.setting.minGridColumnWidth')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-gantt-tooltip',
    'sfc-gantt-tooltip-editor',
    PEFactory.createBooleanEditor('tooltip', 'extension.setting.tooltip')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-gantt-columns',
    'sfc-gantt-columns-editor',
    PEFactory.createGantColumnsEditor('columns', 'extension.setting.columns')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-gantt-converter',
    'sfc-gantt-converter-editor',
    PEFactory.createCodeEditor('converter', 'extension.setting.converter', 'converterCode', {
      title: '转换函数编辑',
      prefix: '(list)=>{',
      suffix: '}'
    })
  );
  PERegister.registerCPEditor(
    app,
    'sfc-gantt-tooltipText',
    'sfc-gantt-tooltipText-editor',
    PEFactory.createCodeEditor('tooltipText', 'extension.setting.tooltipText', 'tooltipTextCode', {
      title: '提示工具渲染函数',
      prefix: '(start, end, task)=>{',
      suffix: '}'
    })
  );
  PERegister.registerDSEditor(
    app,
    'sfc-gantt-requestOtherParam',
    'sfc-gantt-requestOtherParam-editor',
    PEFactory.createSimpleDatasourceEditor('requestOtherParam', 'extension.setting.requestOtherParam')
  );
  registerFWGenerator('sfc-gantt', sfcGanttTemplateGenerator);

  addSystemFieldSchema(sfcUserNameSchema);
  app.component(SfcUserNameWidget.name, SfcUserNameWidget);
  PERegister.registerCPEditor(
    app,
    'sfc-user-name-length',
    'sfc-user-name-length-editor',
    PEFactory.createInputNumberEditor('length', 'extension.setting.labellength')
  );
  registerFWGenerator('sfc-user-name', sfcUserNameTemplateGenerator);
  addSystemFieldSchema(sfcOrgNameSchema);
  app.component(SfcOrgNameWidget.name, SfcOrgNameWidget);
  PERegister.registerCPEditor(
    app,
    'sfc-org-name-length',
    'sfc-org-name-length-editor',
    PEFactory.createInputNumberEditor('length', 'extension.setting.labellength')
  );
  registerFWGenerator('sfc-org-name', sfcOrgNameTemplateGenerator);

  //柱状图
  addCustomWidgetSchema(funiHistogramChartSchema);
  app.component(FuniHistogramChartWidget.name, FuniHistogramChartWidget);
  PERegister.registerCPEditor(
    app,
    'funi-histogram-chart-tipName',
    'funi-histogram-chart-tipName-editor',
    PEFactory.createInputTextEditor('tipName', 'extension.setting.tipName')
  );
  PERegister.registerCPEditor(
    app,
    'funi-histogram-chart-color',
    'funi-histogram-chart-color-editor',
    PEFactory.createColorPickerEditor('color', 'extension.setting.color')
  );
  PERegister.registerCPEditor(
    app,
    'funi-histogram-chart-height',
    'funi-histogram-chart-height-editor',
    PEFactory.createInputNumberEditor('height', 'extension.setting.height')
  );
  PERegister.registerCPEditor(
    app,
    'funi-histogram-chart-custom',
    'funi-histogram-chart-custom-editor',
    PEFactory.createCodeEditor('custom', 'extension.setting.customcfg', 'customCode', {
      title: '图标配置(Json格式)',
      prefix: '{',
      suffix: '}',
      mode: 'json'
    })
  );
  PERegister.registerCPEditor(
    app,
    'funi-histogram-chart-converter',
    'funi-histogram-chart-converter-editor',
    PEFactory.createCodeEditor('converter', 'extension.setting.converter', 'converterCode', {
      title: '转换函数编辑',
      prefix: '(list)=>{',
      suffix: '}'
    })
  );
  PERegister.registerDSEditor(
    app,
    'funi-histogram-chart-requestOtherParam',
    'funi-histogram-chart-requestOtherParam-editor',
    PEFactory.createSimpleDatasourceEditor('requestOtherParam', 'extension.setting.requestOtherParam')
  );
  registerFWGenerator('funi-histogram-chart', funiHistogramChartTemplateGenerator);

  //折线图
  addCustomWidgetSchema(funiLineChartSchema);
  app.component(FuniLineChartWidget.name, FuniLineChartWidget);
  PERegister.registerCPEditor(
    app,
    'funi-line-chart-title',
    'funi-line-chart-tipName-title',
    PEFactory.createInputTextEditor('title', 'extension.setting.title')
  );
  PERegister.registerCPEditor(
    app,
    'funi-line-chart-height',
    'funi-line-chart-height-editor',
    PEFactory.createInputNumberEditor('height', 'extension.setting.height')
  );
  PERegister.registerCPEditor(
    app,
    'funi-line-chart-custom',
    'funi-line-chart-custom-editor',
    PEFactory.createCodeEditor('custom', 'extension.setting.customcfg', 'customCode', {
      title: '图标配置(Json格式)',
      prefix: '{',
      suffix: '}',
      mode: 'json'
    })
  );
  PERegister.registerCPEditor(
    app,
    'funi-line-chart-converter',
    'funi-line-chart-converter-editor',
    PEFactory.createCodeEditor('converter', 'extension.setting.converter', 'converterCode', {
      title: '转换函数编辑',
      prefix: '(list)=>{',
      suffix: '}'
    })
  );
  PERegister.registerDSEditor(
    app,
    'funi-line-chart-requestOtherParam',
    'funi-line-chart-requestOtherParam-editor',
    PEFactory.createSimpleDatasourceEditor('requestOtherParam', 'extension.setting.requestOtherParam')
  );
  registerFWGenerator('funi-line-chart', funiLineChartTemplateGenerator);

  //饼状图
  addCustomWidgetSchema(funiPieChartSchema);
  app.component(FuniPieChartWidget.name, FuniPieChartWidget);
  PERegister.registerCPEditor(
    app,
    'funi-pie-chart-title',
    'funi-pie-chart-tipName-title',
    PEFactory.createInputTextEditor('title', 'extension.setting.title')
  );
  PERegister.registerCPEditor(
    app,
    'funi-pie-chart-height',
    'funi-pie-chart-height-editor',
    PEFactory.createInputNumberEditor('height', 'extension.setting.height')
  );
  PERegister.registerCPEditor(
    app,
    'funi-pie-chart-custom',
    'funi-pie-chart-custom-editor',
    PEFactory.createCodeEditor('custom', 'extension.setting.customcfg', 'customCode', {
      title: '图标配置(Json格式)',
      prefix: '{',
      suffix: '}',
      mode: 'json'
    })
  );
  PERegister.registerCPEditor(
    app,
    'funi-pie-chart-converter',
    'funi-pie-chart-converter-editor',
    PEFactory.createCodeEditor('converter', 'extension.setting.converter', 'converterCode', {
      title: '转换函数编辑',
      prefix: '(list)=>{',
      suffix: '}'
    })
  );
  PERegister.registerDSEditor(
    app,
    'funi-pie-chart-requestOtherParam',
    'funi-pie-chart-requestOtherParam-editor',
    PEFactory.createSimpleDatasourceEditor('requestOtherParam', 'extension.setting.requestOtherParam')
  );
  registerFWGenerator('funi-pie-chart', funiPieChartTemplateGenerator);

  //散点图
  addCustomWidgetSchema(funiScatterplotChartSchema);
  app.component(FuniScatterplotChartWidget.name, FuniScatterplotChartWidget);
  PERegister.registerCPEditor(
    app,
    'funi-scatterplot-chart-title',
    'funi-scatterplot-chart-tipName-title',
    PEFactory.createInputTextEditor('title', 'extension.setting.title')
  );
  PERegister.registerCPEditor(
    app,
    'funi-scatterplot-chart-height',
    'funi-scatterplot-chart-height-editor',
    PEFactory.createInputNumberEditor('height', 'extension.setting.height')
  );
  PERegister.registerCPEditor(
    app,
    'funi-scatterplot-chart-custom',
    'funi-scatterplot-chart-custom-editor',
    PEFactory.createCodeEditor('custom', 'extension.setting.customcfg', 'customCode', {
      title: '图标配置(Json格式)',
      prefix: '{',
      suffix: '}',
      mode: 'json'
    })
  );
  PERegister.registerCPEditor(
    app,
    'funi-scatterplot-chart-converter',
    'funi-scatterplot-chart-converter-editor',
    PEFactory.createCodeEditor('converter', 'extension.setting.converter', 'converterCode', {
      title: '转换函数编辑',
      prefix: '(list)=>{',
      suffix: '}'
    })
  );
  PERegister.registerDSEditor(
    app,
    'funi-scatterplot-chart-requestOtherParam',
    'funi-scatterplot-chart-requestOtherParam-editor',
    PEFactory.createSimpleDatasourceEditor('requestOtherParam', 'extension.setting.requestOtherParam')
  );
  registerFWGenerator('funi-scatterplot-chart', funiScatterplotChartTemplateGenerator);

  //雷达图
  addCustomWidgetSchema(funiRadarChartSchema);
  app.component(FuniRadarChartWidget.name, FuniRadarChartWidget);
  PERegister.registerCPEditor(
    app,
    'funi-radar-chart-title',
    'funi-radar-chart-tipName-title',
    PEFactory.createInputTextEditor('title', 'extension.setting.title')
  );
  PERegister.registerCPEditor(
    app,
    'funi-radar-chart-height',
    'funi-radar-chart-height-editor',
    PEFactory.createInputNumberEditor('height', 'extension.setting.height')
  );
  PERegister.registerCPEditor(
    app,
    'funi-radar-chart-legendFontSize',
    'funi-radar-chart-legendFontSize-editor',
    PEFactory.createInputNumberEditor('legendFontSize', 'extension.setting.legendFontSize')
  );
  PERegister.registerCPEditor(
    app,
    'funi-radar-chart-isCircle',
    'funi-radar-chart-isCircle-editor',
    PEFactory.createBooleanEditor('isCircle', 'extension.setting.isCircle')
  );
  PERegister.registerCPEditor(
    app,
    'funi-radar-chart-splitNumber',
    'funi-radar-chart-splitNumber-editor',
    PEFactory.createInputNumberEditor('splitNumber', 'extension.setting.splitNumber')
  );
  PERegister.registerCPEditor(
    app,
    'funi-radar-chart-isNumber',
    'funi-radar-chart-isNumber-editor',
    PEFactory.createBooleanEditor('isNumber', 'extension.setting.isNumber')
  );
  PERegister.registerCPEditor(
    app,
    'funi-radar-chart-indicatorData',
    'funi-radar-chart-indicatorData-editor',
    PEFactory.createIndicatorDataEditor('indicatorData', 'extension.setting.indicatorData')
  );
  PERegister.registerCPEditor(
    app,
    'funi-radar-chart-custom',
    'funi-radar-chart-custom-editor',
    PEFactory.createCodeEditor('custom', 'extension.setting.customcfg', 'customCode', {
      title: '图标配置(Json格式)',
      prefix: '{',
      suffix: '}',
      mode: 'json'
    })
  );
  PERegister.registerCPEditor(
    app,
    'funi-radar-chart-converter',
    'funi-radar-chart-converter-editor',
    PEFactory.createCodeEditor('converter', 'extension.setting.converter', 'converterCode', {
      title: '转换函数编辑',
      prefix: '(list)=>{',
      suffix: '}'
    })
  );
  PERegister.registerDSEditor(
    app,
    'funi-radar-chart-requestOtherParam',
    'funi-radar-chart-requestOtherParam-editor',
    PEFactory.createSimpleDatasourceEditor('requestOtherParam', 'extension.setting.requestOtherParam')
  );
  registerFWGenerator('funi-radar-chart', funiRadarChartTemplateGenerator);

  //地图
  addAdvancedFieldSchema(sfcOlMapSchema);
  app.component(SfcOlMapWidget.name, SfcOlMapWidget);
  registerFWGenerator('sfc-ol-map', sfcOlMapTemplateGenerator);
  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-mapHeight',
    'sfc-ol-map-mapHeight-editor',
    PEFactory.createInputNumberEditor('mapHeight', 'extension.setting.height')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-mapId',
    'sfc-ol-map-mapId-editor',
    PEFactory.createFuniVariableSetterEditor('mapId', 'extension.setting.mapId')
  );
  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-viewSyncMapId',
    'sfc-ol-map-viewSyncMapId-editor',
    PEFactory.createInputTextEditor('viewSyncMapId', 'extension.setting.viewSyncMapId')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-projection',
    'sfc-ol-map-projection-editor',
    PEFactory.createSelectEditor('projection', 'extension.setting.projection', {
      optionItems: [
        { label: 'EPSG:4326', value: 'EPSG:4326' },
        { label: 'EPSG:4490', value: 'EPSG:4490' },
        { label: 'EPSG:3857', value: 'EPSG:3857' }
      ]
    })
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-baseMapLayer',
    'sfc-ol-map-baseMapLayer-editor',
    PEFactory.createSelectEditor('baseMapLayer', 'extension.setting.baseMapLayer', {
      optionItems: [
        { label: '天地图影像', value: 'tdtImgLayer' },
        { label: '天地图矢量', value: 'tdtVecLayer' },
        { label: '无底图', value: '' }
      ]
    })
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-showBaseMapLayerText',
    'sfc-ol-map-showBaseMapLayerText-editor',
    PEFactory.createBooleanEditor('showBaseMapLayerText', 'extension.setting.showBaseMapLayerText')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-mapCenter',
    'sfc-ol-map-mapCenter-editor',
    PEFactory.createInputArrayEditor('mapCenter', 'extension.setting.mapCenter')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-zoom',
    'sfc-ol-map-zoom-editor',
    PEFactory.createInputNumberEditor('zoom', 'extension.setting.zoom')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-baseLayers',
    'sfc-ol-map-baseLayers-editor',
    PEFactory.createFuniVariableSetterEditor('baseLayers', 'extension.setting.baseLayers')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-layers',
    'sfc-ol-map-layers-editor',
    PEFactory.createFuniVariableSetterEditor('layers', 'extension.setting.layers')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-layersRequest',
    'sfc-ol-map-layersRequest-editor',
    PEFactory.createFuniVariableSetterEditor('layersRequest', 'extension.setting.layersRequest')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-legendBoxWidth',
    'sfc-ol-map-legendBoxWidth-editor',
    PEFactory.createInputNumberEditor('legendBoxWidth', 'extension.setting.legendBoxWidth')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-showLayerTree',
    'sfc-ol-map-showLayerTree-editor',
    PEFactory.createBooleanEditor('showLayerTree', 'extension.setting.showLayerTree')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-showOverlay',
    'sfc-ol-map-showOverlay-editor',
    PEFactory.createBooleanEditor('showOverlay', 'extension.setting.showOverlay')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-showDraw',
    'sfc-ol-map-showDraw-editor',
    PEFactory.createBooleanVarEditor('showDraw', 'showDrawCode', 'extension.setting.showDraw')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-drawTool',
    'sfc-ol-map-drawTool-editor',
    PEFactory.createSelectEditor('drawTool', 'extension.setting.drawTool', {
      multiple: true,
      optionItems: [
        { label: '添加', value: 'add' },
        { label: '合并', value: 'merge' },
        { label: '拆分', value: 'split' },
        { label: '删除', value: 'del' }
      ]
    })
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-drawToolCode',
    'sfc-ol-map-drawToolCode-editor',
    PEFactory.createFuniVariableSetterEditor('drawToolCode', 'extension.setting.drawToolCode')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-drawTypeEnum',
    'sfc-ol-map-drawTypeEnum-editor',
    PEFactory.createSelectEditor('drawTypeEnum', 'extension.setting.drawTypeEnum', {
      optionItems: [
        { label: '新增', value: 'VECTOR_ADD' },
        { label: '分割', value: 'VECTOR_SPLIT' },
        { label: '混合', value: 'VECTOR_MERGE' },
        { label: '删除', value: 'VECTOR_DELETE' }
      ]
    })
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-drawStyle',
    'sfc-ol-map-drawStyle-editor',
    PEFactory.createFuniVariableSetterEditor('drawStyle', 'extension.setting.drawStyle')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-enableSelect',
    'sfc-ol-map-enableSelect-editor',
    PEFactory.createBooleanEditor('enableSelect', 'extension.setting.enableSelect')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-baseMapChangeTool',
    'sfc-ol-map-baseMapChangeTool-editor',
    PEFactory.createBooleanEditor('baseMapChangeTool', 'extension.setting.baseMapChangeTool')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-isAutoShowModal',
    'sfc-ol-map-isAutoShowModal-editor',
    PEFactory.createBooleanEditor('isAutoShowModal', 'extension.setting.isAutoShowModal')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-maxSplit',
    'sfc-ol-map-maxSplit-editor',
    PEFactory.createInputNumberEditor('maxSplit', 'extension.setting.maxSplit')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-oldSns',
    'sfc-ol-map-oldSns-editor',
    PEFactory.createInputTextEditor('oldSns', 'extension.setting.oldSns')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-geomDataType',
    'sfc-ol-map-geomDataType-editor',
    PEFactory.createSelectEditor('geomDataType', 'extension.setting.geomDataType', {
      optionItems: [
        { label: 'jsonString2', value: 'jsonString2' },
        { label: 'jsonString', value: 'jsonString' },
        { label: 'json', value: 'json' },
        { label: 'wkt', value: 'wkt' }
      ]
    })
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-modeValueMap',
    'sfc-ol-map-modeValueMap-editor',
    PEFactory.createFuniVariableSetterEditor('modeValueMap', 'extension.setting.modeValueMap')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-boundAreaList',
    'sfc-ol-map-boundAreaList-editor',
    PEFactory.createFuniVariableSetterEditor('boundAreaList', 'extension.setting.boundAreaList')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-boundAreaRequest',
    'sfc-ol-map-boundAreaRequest-editor',
    PEFactory.createFuniVariableSetterEditor('boundAreaRequest', 'extension.setting.boundAreaRequest')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-boundAreaStyle',
    'sfc-ol-map-boundAreaStyle-editor',
    PEFactory.createFuniVariableSetterEditor('boundAreaStyle', 'extension.setting.boundAreaStyle')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-layerAuthHeader',
    'sfc-ol-map-layerAuthHeader-editor',
    PEFactory.createFuniVariableSetterEditor('layerAuthHeader', 'extension.setting.layerAuthHeader')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-highLightStyle',
    'sfc-ol-map-highLightStyle-editor',
    PEFactory.createFuniVariableSetterEditor('highLightStyle', 'extension.setting.highLightStyle')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-wfsServer',
    'sfc-ol-map-wfsServer-editor',
    PEFactory.createInputTextEditor('wfsServer', 'extension.setting.wfsServer')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-fitParams',
    'sfc-ol-map-fitParams-editor',
    PEFactory.createFuniVariableSetterEditor('fitParams', 'extension.setting.fitParams')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-modalContentRender',
    'sfc-ol-map-modalContentRender-editor',
    PEFactory.createCodeEditor('modalContentRender', 'extension.setting.modalContentRender', 'modalContentRender', {
      title: '弹窗渲染函数',
      prefix: '(currentOverlayData,currentClickLayer)=>{',
      suffix: '}'
    })
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-legendShowFunc',
    'sfc-ol-map-legendShowFunc-editor',
    PEFactory.createCodeEditor('legendShowFunc', 'extension.setting.legendShowFunc', 'legendShowFunc', {
      title: '图例回调函数',
      prefix: '(legend)=>{',
      suffix: '}'
    })
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-drawCallback',
    'sfc-ol-map-drawCallback-editor',
    PEFactory.createCodeEditor('drawCallback', 'extension.setting.drawCallback', 'drawCallback', {
      title: '绘制回调函数',
      prefix: '(oldFeatures)=>{',
      suffix: '}'
    })
  );

  PERegister.registerCPEditor(
    app,
    'sfc-ol-map-legendListFunc',
    'sfc-ol-map-legendListFunc-editor',
    PEFactory.createCodeEditor('legendListFunc', 'extension.setting.legendListFunc', 'legendListFunc', {
      title: '图例数组自定义过滤',
      prefix: '(legendList)=>{',
      suffix: '}'
    })
  );

  //视频组件
  addAdvancedFieldSchema(sfcVideoSchema);
  app.component(SfcVideoWidget.name, SfcVideoWidget);
  registerFWGenerator('sfc-video', sfcVideoTemplateGenerator);
  PERegister.registerCPEditor(
    app,
    'sfc-video-height',
    'sfc-video-height-editor',
    PEFactory.createInputNumberEditor('height', 'extension.setting.height')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-funiVideoStyle',
    'sfc-video-funiVideoStyle-editor',
    PEFactory.createFuniVariableSetterEditor('funiVideoStyle', 'extension.setting.funiVideoStyle')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-treeStyle',
    'sfc-video-treeStyle-editor',
    PEFactory.createFuniVariableSetterEditor('treeStyle', 'extension.setting.treeStyle')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-leftPadding',
    'sfc-video-leftPadding-editor',
    PEFactory.createInputTextEditor('leftPadding', 'extension.setting.leftPadding')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-leftWidth',
    'sfc-video-leftWidth-editor',
    PEFactory.createInputTextEditor('leftWidth', 'extension.setting.leftWidth')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-rightWidth',
    'sfc-video-rightWidth-editor',
    PEFactory.createInputTextEditor('rightWidth', 'extension.setting.rightWidth')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-objectFit',
    'sfc-video-objectFit-editor',
    PEFactory.createSelectEditor('objectFit', 'extension.setting.objectFit', {
      optionItems: [
        { label: 'fill', value: 'fill' },
        { label: 'contain', value: 'contain' },
        { label: 'cover', value: 'cover' },
        { label: 'scale-down', value: 'scale-down' }
      ]
    })
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-urlCode',
    'sfc-video-urlCode-editor',
    PEFactory.createFuniVariableSetterEditor('urlCode', 'extension.setting.urlCode')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-type',
    'sfc-video-type-editor',
    PEFactory.createInputTextEditor('type', 'extension.setting.viedoType')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-dhConfig',
    'sfc-video-dhConfig-editor',
    PEFactory.createFuniVariableSetterEditor('dhConfig', 'extension.setting.dhConfig')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-isShowControl',
    'sfc-video-isShowControl',
    PEFactory.createBooleanEditor('isShowControl', 'extension.setting.isShowControl')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-isAuto',
    'sfc-video-isAuto',
    PEFactory.createBooleanEditor('isAuto', 'extension.setting.isAuto')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-dataTree',
    'sfc-video-dataTree-editor',
    PEFactory.createFuniVariableSetterEditor('dataTree', 'extension.setting.dataTree')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-dataTreeVideoUrl',
    'sfc-video-dataTreeVideoUrl-editor',
    PEFactory.createFuniVariableSetterEditor('dataTreeVideoUrl', 'extension.setting.dataTreeVideoUrl')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-isShowTreeParent',
    'sfc-video-isShowTreeParent',
    PEFactory.createBooleanEditor('isShowTreeParent', 'extension.setting.isShowTreeParent')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-speed',
    'sfc-video-speed',
    PEFactory.createInputNumberEditor('speed', 'extension.setting.videoSpeed')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-treeApiParams',
    'sfc-video-treeApiParams-editor',
    PEFactory.createFuniVariableSetterEditor('treeApiParams', 'extension.setting.treeApiParams')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-customTreeApi',
    'sfc-video-customTreeApi-editor',
    PEFactory.createFuniVariableSetterEditor('customTreeApi', 'extension.setting.customTreeApi')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-getVideoApi',
    'sfc-video-getVideoApi-editor',
    PEFactory.createFuniVariableSetterEditor('getVideoApi', 'extension.setting.getVideoApi')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-operateCamera',
    'sfc-video-operateCamera-editor',
    PEFactory.createFuniVariableSetterEditor('operateCamera', 'extension.setting.operateCamera')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-operateDirectApi',
    'sfc-video-operateDirectApi-editor',
    PEFactory.createFuniVariableSetterEditor('operateDirectApi', 'extension.setting.operateDirectApi')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-video-defaultTreeProps',
    'sfc-video-defaultTreeProps-editor',
    PEFactory.createFuniVariableSetterEditor('defaultTreeProps', 'extension.setting.defaultTreeProps')
  );

  //业务操作记录
  addAdvancedFieldSchema(sfcWorkflowLogSchema);
  app.component(SfcWorkflowLogWidget.name, SfcWorkflowLogWidget);
  registerFWGenerator('sfc-workflow-log', sfcWorkflowLogTemplateGenerator);

  PERegister.registerCPEditor(
    app,
    'sfc-workflow-log-businessId',
    'sfc-workflow-log-businessId-editor',
    PEFactory.createInputTextEditor('businessId', 'extension.setting.businessId')
  );

  //业务操作记录
  addAdvancedFieldSchema(sfcCustomSchema);
  app.component(SfcCustomWidget.name, SfcCustomWidget);
  registerFWGenerator('sfc-custom', sfcCustomTemplateGenerator);

  PERegister.registerCPEditor(
    app,
    'sfc-custom-attrs',
    'sfc-custom-attrs-editor',
    PEFactory.createFuniVariableSetterEditor('attrs', 'extension.setting.attrs')
  );

  PERegister.registerCPEditor(
    app,
    'sfc-custom-renderer',
    'sfc-custom-renderer-editor',
    PEFactory.createCodeEditor('renderer', 'extension.setting.renderer', 'renderer', {
      title: '渲染函数编辑',
      prefix: '(createElement,props)=>{',
      suffix: '}'
    })
  );
}
export default {
  loadFuniExt
};
