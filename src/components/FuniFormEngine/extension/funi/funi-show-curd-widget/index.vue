<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-24 13:45:03
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-25 18:13:39
 * @FilePath: \src\components\FuniFormEngine\extension\funi\funi-show-curd-widget\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <form-item-wrapper :designer="designer"
      :field="field"
      :rules="rules"
      :design-state="designState"
      :parent-widget="parentWidget"
      :parent-list="parentList"
      :index-of-parent-list="indexOfParentList"
      :sub-form-row-index="subFormRowIndex"
      :sub-form-col-index="subFormColIndex"
      :sub-form-row-id="subFormRowId">
      <sfc-show-curd v-model="fieldModel"
        :label="field.options.label"
        :size="field.options.size"
        :buttons="field.options.buttons"
        :pagination="field.options.pagination"
        :disabled="field.options.disabled"
        :isShowSearch="field.options.isShowSearch"
        :searchConfigGroup="field.options.searchConfigGroup"
        :url="field.options.url"
        :actions="()=>{return field.options.actions}"
        :columns="columns"
        :requestOtherParam="field.options.requestOtherParam"
        :isDesign="true"
        :useTools="field.options.useTools"
        style="margin-left:-4px;" />
    </form-item-wrapper>
  </div>
</template>
<script>
import FormItemWrapper from '../../../form-widget/field-widget/form-item-wrapper.vue'
import emitter from '../../../common/utils/emitter'
import i18n from '../../../common/utils/i18n'
import fieldMixin from "../../../form-widget/field-widget/fieldMixin"
export default {
  name: "funi-show-curd-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  components: {
    FormItemWrapper,
  },
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  created () {
    this.registerToRefList()
    this.initEventHandler()
  },
  beforeUnmount () {
    this.unregisterFromRefList()
  },
  computed: {
    columns () {
      let list = [];
      if (this.field && this.field.options) {
        list = [...(this.field.options.columns || [])];
        let typeIndex = list.findIndex(item => ['selection', 'radio'].includes(item.type));
        if (typeIndex >= 0) {
          list.splice(typeIndex, 1);
        }
        let lnIndex = list.findIndex(item => ['index'].includes(item.type));
        if (lnIndex >= 0) {
          list.splice(lnIndex, 1);
        }
        if (this.field.options.isLineNumber) {
          list.splice(0, 0, { label: '序号', prop: '_show_curd_xh', type: 'index' });
        }
        if (this.field.options.selectType) {
          list.splice(0, 0, { prop: '_show_curd_xz', type: this.field.options.selectType });
        }
      }
      return list;
    }
  },
  methods: {
    handleCloseCustomEvent () {
      if (this.field.options.onClose) {
        let changeFn = new Function(this.field.options.onClose)
        changeFn.call(this)
      }
    }

  }
}
</script>
<style lang="scss" scoped>
.full-width-input {
  width: 100% !important;
}
</style>