/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-16 17:03:56
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-21 16:30:20
 * @FilePath: \src\components\FuniFormEngine\extension\funi\funi-ext-sfc-generator.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { isNotNull, validateDs, isNotNullExpression } from '../../common/utils/util';
import { getElAttrs } from '../../common/utils/funi-sfc-generator';

export const funiLabelTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vOn } = getElAttrs(fw, formConfig);
  const lengthAttrs = wop.length ? `:length="${wop.length}"` : '';
  const modelValue = `:modelValue="${formConfig.modelName}.${wop.name}"`;
  const funiLabelTemplate = `<funi-label ${modelValue} ${lengthAttrs} class="full-width-input" ${vOn}/>`;
  return funiLabelTemplate;
};

export const sfcUserTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const allowedSelfAttrs = wop.allowedSelf ? `:allowedSelf="true"` : `:allowedSelf="false"`;
  const modeAttrs = wop.mode ? `mode="${wop.mode}"` : '';
  const allowedScopeAttrs =
    wop.allowedScope && wop.allowedScope.length > 0 ? `:allowedScope="${wop.name}AllowedScope"` : '';
  const sfcUserTemplate = `<sfc-user ${vModel} ${disabled} ${allowedScopeAttrs} ${allowedSelfAttrs} ${modeAttrs} ${vOn}/>`;
  return sfcUserTemplate;
};

export const sfcOrgTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const allowedSelfAttrs = wop.allowedSelf ? `:allowedSelf="true"` : `:allowedSelf="false"`;
  const modeAttrs = wop.mode ? `mode="${wop.mode}"` : '';
  const allowedScopeAttrs =
    wop.allowedScope && wop.allowedScope.length > 0 ? `:allowedScope="${wop.name}AllowedScope"` : '';
  const sfcOrgTemplate = `<sfc-org ${vModel} ${disabled} ${allowedScopeAttrs} ${allowedSelfAttrs} ${modeAttrs} ${vOn}/>`;
  return sfcOrgTemplate;
};

export const funiGroupTitleTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vOn } = getElAttrs(fw, formConfig);
  const titleAttr = wop.title ? `title="${wop.title}"` : '';
  const funiLabelTemplate = `<funi-group-title ${titleAttr} ${vOn}/>`;
  return funiLabelTemplate;
};

export const funiSelectTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  let typeCodeAttr = wop.typeCode ? `typeCode="${wop.typeCode}"` : '';
  let urlAttr = wop.url ? `url="${wop.url}"` : '';
  let optionsAttr = wop.selectOptions && wop.selectOptions.length > 0 ? `:options="${wop.name}Options"` : '';
  let isInitUpdateAttr = wop.isInitUpdate ? `:isInitUpdate="true"` : '';
  let checkedOnlyOneAttr = wop.checkedOnlyOne ? `:checkedOnlyOne="true"` : '';
  let requestParamsAttr = '';
  let htmlTag = 'sfc-select';
  const {
    vModel,
    vOn,
    disabled,
    size,
    clearable,
    filterable,
    allowCreate,
    defaultFirstOption,
    automaticDropdown,
    multiple,
    remote,
    placeholder
  } = getElAttrs(fw, formConfig);
  let isLocal = true;
  let designerConfig = {};
  const designerConfigStr = localStorage.getItem('designerConfig');
  if (designerConfigStr) {
    try {
      designerConfig = JSON.parse(designerConfigStr);
      isLocal = designerConfig.hasModel;
    } catch {}
  }
  let isLocalAttrs = isLocal ? `:isLocal="true"` : '';
  if (wop.modelOption && wop.modelOption.type && ['model', 'api', 'sql'].includes(wop.modelOption.type)) {
    htmlTag = 'sfc-select';
    urlAttr = '';
    typeCodeAttr = '';
    optionsAttr = '';
    isLocalAttrs = '';
    requestParamsAttr = `:requestParams="${wop.name}RequestParams"`;
    let dsObj = validateDs(wop.modelOption, fw);
    if (!dsObj.valid) {
      throw new Error(`请填写下拉框${dsObj.message}!`);
    }
  } else if (wop.modelOption && wop.modelOption.type && ['typeCode'].includes(wop.modelOption.type)) {
    optionsAttr = '';
  } else if (wop.modelOption && wop.modelOption.type && ['custom'].includes(wop.modelOption.type)) {
    typeCodeAttr = '';
  }
  const isStringValAttr = formConfig && formConfig.modelId ? `:isStringVal="true"` : '';
  const multipleLimit = wop.multipleLimit ? `:multiple-limit="${wop.name}MultipleLimit"` : '';
  const funiSelectTemplate = `<${htmlTag} ${vModel} class="full-width-input" ${isInitUpdateAttr} ${checkedOnlyOneAttr} ${typeCodeAttr} ${urlAttr} ${isLocalAttrs} ${isStringValAttr} ${optionsAttr} ${requestParamsAttr} ${disabled} ${size} ${clearable} ${filterable}
${allowCreate} ${defaultFirstOption} ${automaticDropdown} ${multiple} ${multipleLimit} ${placeholder}
${remote} ${vOn} @change-options="val=>handleChangeOptions('${wop.name}',val)"/>`;
  return funiSelectTemplate;
};

export const funiCurdTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  let dsObj = validateDs(wop.requestOtherParam, fw);
  if (!dsObj.valid) {
    throw new Error(`请填写编辑列表${dsObj.message}!`);
  }
  const labelAttr = wop.label ? `label="${wop.label}"` : '';
  const modelIdAttr =
    wop.requestOtherParam && wop.requestOtherParam.modelId ? `modelId="${wop.requestOtherParam.modelId}"` : '';
  const urlAttr = wop.url ? `url="${wop.url}"` : '';
  let addAuthAttr = '';
  if (wop && wop.hasAddAuth && wop.addPermission && wop.addPermission.code) {
    addAuthAttr = `addAuth="${wop.addPermission.code}"`;
  }
  let minusAuthAttr = '';
  if (wop && wop.hasMinusAuth && wop.minusPermission && wop.minusPermission.code) {
    minusAuthAttr = `minusAuth="${wop.minusPermission.code}"`;
  }
  const actionsAttr = wop.actions && wop.actions.length > 0 ? `:actions="${wop.name}Actions"` : '';
  const horizontalScrollingAttr = !wop.horizontalScrolling ? `:horizontalScrolling="false"` : '';
  let isCorrelation;
  if (wop.requestOtherParam && wop.requestOtherParam.data) {
    isCorrelation = !!wop.requestOtherParam.data.isCorrelation;
  }
  const isCorrelationAttr = isCorrelation === false ? `:isCorrelation="false"` : `:isCorrelation="true"`;
  const useToolsAttr = wop.useTools ? `:useTools="${wop.useTools}"` : '';
  const { vModel, vOn, size, disabled } = getElAttrs(fw, formConfig);
  const funiSelectTemplate = `<sfc-curd ref="${wop.name}" ${modelIdAttr} ${urlAttr} ${addAuthAttr} ${minusAuthAttr} ${vModel} ${disabled} ${isCorrelationAttr} ${useToolsAttr} ${labelAttr} ${size} :pagination="${wop.pagination}" :showIndex="${wop.showIndex}" ${horizontalScrollingAttr} :columns="${wop.name}Columns" ${actionsAttr} :buttons="${wop.name}Buttons" :requestOtherParam="${wop.name}RequestOtherParam" ${vOn} @init="changeInitStatus"></sfc-curd>`;
  return funiSelectTemplate;
};

export const funiShowCurdTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  let isError = false;
  let dsObj = validateDs(wop.requestOtherParam, fw);
  if (!dsObj.valid) {
    isError = true;
  }
  const modelIdAttr =
    wop.requestOtherParam && wop.requestOtherParam.modelId ? `modelId="${wop.requestOtherParam.modelId}"` : '';
  const isManyToManyAttr =
    wop.requestOtherParam && wop.requestOtherParam.dataType === 'many_to_many' ? `:isManyToMany="true"` : '';
  const mFieldNameAtrr =
    wop.requestOtherParam &&
    wop.requestOtherParam.dataType === 'many_to_many' &&
    wop.requestOtherParam.data &&
    wop.requestOtherParam.data.mFieldName
      ? `mFieldName="${wop.requestOtherParam.data.mFieldName}"`
      : '';
  const labelAttr = wop.label ? `label="${wop.label}"` : '';
  let requestUrl = wop.url;
  if (
    wop.requestOtherParam &&
    wop.requestOtherParam.type === 'api' &&
    wop.requestOtherParam.data &&
    wop.requestOtherParam.data.apiType === 'other'
  ) {
    requestUrl = wop.requestOtherParam.data.api_url;
  }
  const urlAttr = requestUrl && !isError ? `url="${requestUrl}"` : '';
  const isArrayValAttr = isError ? `:isArrayVal="true"` : '';
  const isShowSearchAttr = wop.isShowSearch ? `:isShowSearch="${wop.isShowSearch}"` : '';
  const useToolsAttr = wop.useTools ? `:useTools="${wop.useTools}"` : '';
  const checkLimit = wop.checkLimit ? `:checkLimit="${wop.name}CheckLimit"` : '';
  const maxHeightAttr = wop.tableMaxHeight ? `maxHeight="${wop.tableMaxHeight}"` : '';
  const rowKeyAttr = wop.rowKey ? `rowKey="${wop.rowKey}"` : '';
  const actionsAttr = wop.actions && wop.actions.length > 0 ? `:actions="${wop.name}Actions"` : '';
  let isCorrelation;
  if (wop.requestOtherParam && wop.requestOtherParam.data) {
    isCorrelation = !!wop.requestOtherParam.data.isCorrelation;
  }
  const isCorrelationAttr = isCorrelation === false ? `:isCorrelation="false"` : `:isCorrelation="true"`;
  const reloadOnActiveAttr = wop.reloadOnActive === true ? `:reloadOnActive="true"` : '';
  const { vModel, vOn, size, disabled } = getElAttrs(fw, formConfig);
  const funiSelectTemplate = `<sfc-show-curd ref="${wop.name}" ${isArrayValAttr} ${vModel} ${rowKeyAttr} ${disabled} ${isCorrelationAttr} ${checkLimit} ${labelAttr} ${size} ${urlAttr} ${isShowSearchAttr} ${useToolsAttr} ${isManyToManyAttr} ${reloadOnActiveAttr} ${mFieldNameAtrr} ${maxHeightAttr} ${modelIdAttr} :pagination="${wop.pagination}" :columns="${wop.name}Columns" :buttons="${wop.name}Buttons" :searchConfigGroup="${wop.name}SearchConfigGroup" ${actionsAttr} :requestOtherParam="${wop.name}RequestOtherParam" ${vOn}></sfc-show-curd>`;
  return funiSelectTemplate;
};

export const sfcGanttTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  let dsObj = validateDs(wop.requestOtherParam, fw);
  if (!dsObj.valid) {
    throw new Error(`请填写甘特图${dsObj.message}!`);
  }
  const urlAttr = wop.url ? `url="${wop.url}"` : '';
  const converterAttr = wop.converter ? `:converter="${wop.name}Converter"` : '';
  const tooltipTextAttr = wop.tooltipText ? `:tooltipText="${wop.name}TooltipText"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const configAttr = `:config="{grid_width: 60}"`;
  const tooltipAttr = wop.tooltip ? `:tooltip="true"` : ':tooltip="false"';
  const valueAttr = `:value="${formConfig.modelName}.${wop.name}"`;
  const heightAttrs = wop.height ? `:height="${wop.height}"` : '';
  const minGridColumnWidthAttrs = wop.minGridColumnWidth ? `:minGridColumnWidth="${wop.minGridColumnWidth}"` : '';
  const sfcGanttTemplate = `<sfc-gantt ref="${wop.name}" ${disabled} ${heightAttrs} ${minGridColumnWidthAttrs} ${configAttr} ${valueAttr} ${urlAttr} ${tooltipAttr} ${converterAttr} ${tooltipTextAttr} :columns="${wop.name}Columns" :requestOtherParam="${wop.name}RequestOtherParam" ${vOn}></sfc-gantt>`;
  return sfcGanttTemplate;
};

export const sfcFileTableTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  let hideAddBtnAttr = wop.hideAddBtn ? `:hideAddBtn="${wop.hideAddBtn}"` : '';
  if (isNotNullExpression(wop.hideAddBtnCode)) {
    hideAddBtnAttr = `:hideAddBtn="${wop.name}HideAddBtn"`;
  }
  const sfcFileTableTemplate = `<sfc-file-table ref="${wop.name}" ${hideAddBtnAttr} ${disabled} ${vModel} ${vOn}></sfc-file-table>`;
  return sfcFileTableTemplate;
};

export const sfcGuidTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const urlAttr = wop.url ? `url="${wop.url}"` : '';
  const { vModel, vOn, disabled, vShowAttr } = getElAttrs(fw, formConfig);
  const sfcGanttTemplate = `<sfc-guid ref="${wop.name}" ${vShowAttr} ${vModel} ${urlAttr} ${disabled} ${vOn}/>`;
  return sfcGanttTemplate;
};

export const funiRegionTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const isEditAttr = wop.disabledCode
    ? `:isEdit="!${wop.name}Disabled"`
    : wop.disabled
    ? `:isEdit="false"`
    : fw.formItemFlag
    ? `:isEdit="!slotProps.disabled"`
    : '';
  const lvlAttr = `:lvl="${wop.lvl}"`;
  const showAddressFullAttr = wop.showAddressFull ? `:showAddressFull="true"` : ':showAddressFull="false"';
  const addressFullWidth = `addressFullWidth="${wop.addressFullWidth}"`;
  const { vModel, vOn } = getElAttrs(fw, formConfig);
  const sfcGanttTemplate = `<sfc-region ref="${wop.name}" ${vModel} ${isEditAttr} ${lvlAttr} ${showAddressFullAttr} ${addressFullWidth} ${vOn}/>`;
  return sfcGanttTemplate;
};

export const sfcOperationLogTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const apiAttr = wop.api ? `api="${wop.api}"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const sfcGanttTemplate = `<sfc-operation-log ref="${wop.name}" ${apiAttr} ${vModel} ${disabled} ${vOn}/>`;
  return sfcGanttTemplate;
};

export const sfcFuniOperationLogTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const apiAttr = wop.api ? `api="${wop.api}"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const sfcFuniOperationemplate = `<sfc-funi-operation-log ref="${wop.name}" ${apiAttr} ${vModel} ${disabled} ${vOn}/>`;
  return sfcFuniOperationemplate;
};

export const sfcFuniLogTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const apiAttr = wop.api ? `api="${wop.api}"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const sfcGanttTemplate = `<sfc-funi-log ref="${wop.name}" title="${wop.label}" ${apiAttr} ${vModel} ${disabled} ${vOn} style="width:100%;"/>`;
  return sfcGanttTemplate;
};

export const sfcIframeTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vModel } = getElAttrs(fw, formConfig);
  const sfcGanttTemplate = `<sfc-iframe ref="${wop.name}" :src="${wop.name}Src" ${vModel} height="${wop.height}" :isFull="${wop.isFull}"/>`;
  return sfcGanttTemplate;
};

export const sfcUserNameTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vOn } = getElAttrs(fw, formConfig);
  const lengthAttrs = wop.length ? `:length="${wop.length}"` : '';
  const modelValue = `:modelValue="${formConfig.modelName}.${wop.name}"`;
  const funiLabelTemplate = `<funi-label ${modelValue} ${lengthAttrs} class="full-width-input" ${vOn}/>`;
  return funiLabelTemplate;
};

export const sfcOrgNameTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vOn } = getElAttrs(fw, formConfig);
  const lengthAttrs = wop.length ? `:length="${wop.length}"` : '';
  const modelValue = `:modelValue="${formConfig.modelName}.${wop.name}"`;
  const funiLabelTemplate = `<sfc-org-name ${modelValue} ${lengthAttrs} class="full-width-input" ${vOn}/>`;
  return funiLabelTemplate;
};

export const funiHistogramChartTemplateGenerator = function (fw, formConfig, designerConfig) {
  const wop = fw.options;
  let dsObj = validateDs(wop.requestOtherParam, fw, designerConfig);
  if (!dsObj.valid) {
    throw new Error(`请填写柱状图${dsObj.message}!`);
  }
  const urlAttr = wop.url ? `url="${wop.url}"` : '';
  const converterAttr = wop.converter ? `:converter="${wop.name}Converter"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const titleAttr = wop.label ? `title="${wop.label}"` : '';
  const tipNameAttr = wop.tipName ? `tipName="${wop.tipName}"` : '';
  const colorAttr = wop.color ? `color="${wop.color}"` : '';
  const valueAttr = `:value="${formConfig.modelName}.${wop.name}"`;
  const heightAttrs = wop.height ? `:height="${wop.height}"` : '';
  const funiChartTemplate = `<sfc-chart ref="${wop.name}" chartName="funi-histogram-chart" ${disabled} ${titleAttr} ${tipNameAttr} ${colorAttr} ${heightAttrs} ${valueAttr} ${urlAttr} ${converterAttr} :requestOtherParam="${wop.name}RequestOtherParam" ${vOn}></sfc-chart>`;
  return funiChartTemplate;
};

export const funiLineChartTemplateGenerator = function (fw, formConfig, designerConfig) {
  const wop = fw.options;
  let dsObj = validateDs(wop.requestOtherParam, fw, designerConfig);
  if (!dsObj.valid) {
    throw new Error(`请填写折线图${dsObj.message}!`);
  }
  const urlAttr = wop.url ? `url="${wop.url}"` : '';
  const converterAttr = wop.converter ? `:converter="${wop.name}Converter"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const titleAttr = wop.label ? `title="${wop.label}"` : '';
  const valueAttr = `:value="${formConfig.modelName}.${wop.name}"`;
  const heightAttrs = wop.height ? `:height="${wop.height}"` : '';
  const funiChartTemplate = `<sfc-chart ref="${wop.name}" chartName="funi-line-chart" dataName="data" ${disabled} ${titleAttr} ${heightAttrs} ${valueAttr} ${urlAttr} ${converterAttr} :requestOtherParam="${wop.name}RequestOtherParam" ${vOn}></sfc-chart>`;
  return funiChartTemplate;
};

export const funiPieChartTemplateGenerator = function (fw, formConfig, designerConfig) {
  const wop = fw.options;
  let dsObj = validateDs(wop.requestOtherParam, fw, designerConfig);
  if (!dsObj.valid) {
    throw new Error(`请填写饼状图${dsObj.message}!`);
  }
  const urlAttr = wop.url ? `url="${wop.url}"` : '';
  const converterAttr = wop.converter ? `:converter="${wop.name}Converter"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const titleAttr = wop.label ? `title="${wop.label}"` : '';
  const valueAttr = `:value="${formConfig.modelName}.${wop.name}"`;
  const heightAttrs = wop.height ? `:height="${wop.height}"` : '';
  const funiChartTemplate = `<sfc-chart ref="${wop.name}" chartName="funi-pie-chart" dataName="data" ${disabled} ${titleAttr} ${heightAttrs} ${valueAttr} ${urlAttr} ${converterAttr} :requestOtherParam="${wop.name}RequestOtherParam" ${vOn}></sfc-chart>`;
  return funiChartTemplate;
};

export const funiScatterplotChartTemplateGenerator = function (fw, formConfig, designerConfig) {
  const wop = fw.options;
  let dsObj = validateDs(wop.requestOtherParam, fw, designerConfig);
  if (!dsObj.valid) {
    throw new Error(`请填写散点图${dsObj.message}!`);
  }
  const urlAttr = wop.url ? `url="${wop.url}"` : '';
  const converterAttr = wop.converter ? `:converter="${wop.name}Converter"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const titleAttr = wop.label ? `title="${wop.label}"` : '';
  const valueAttr = `:value="${formConfig.modelName}.${wop.name}"`;
  const heightAttrs = wop.height ? `:height="${wop.height}"` : '';
  const funiChartTemplate = `<sfc-chart ref="${wop.name}" chartName="funi-scatterplot-chart" dataName="data" ${disabled} ${titleAttr} ${heightAttrs} ${valueAttr} ${urlAttr} ${converterAttr} :requestOtherParam="${wop.name}RequestOtherParam" ${vOn}></sfc-chart>`;
  return funiChartTemplate;
};

export const funiRadarChartTemplateGenerator = function (fw, formConfig, designerConfig) {
  const wop = fw.options;
  let dsObj = validateDs(wop.requestOtherParam, fw, designerConfig);
  if (!dsObj.valid) {
    throw new Error(`请填写散点图${dsObj.message}!`);
  }
  const urlAttr = wop.url ? `url="${wop.url}"` : '';
  const converterAttr = wop.converter ? `:converter="${wop.name}Converter"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const titleAttr = wop.label ? `title="${wop.label}"` : '';
  const valueAttr = `:value="${formConfig.modelName}.${wop.name}"`;
  const heightAttrs = wop.height ? `:height="${wop.height}"` : '';
  const legendFontSizeAttrs = wop.legendFontSize ? `:legendFontSize="${wop.legendFontSize}"` : '';
  const splitNumberAttrs = wop.splitNumber ? `:splitNumber="${wop.splitNumber}"` : '';
  const isCircleAttr = wop.isCircle ? `:isCircle="true"` : '';
  const isNumberAttr = wop.isNumber ? `:isNumber="true"` : '';
  const indicatorDataAttr = wop.indicatorData
    ? `:indicatorData="${JSON.stringify(wop.indicatorData).replaceAll('"', "'")}"`
    : '';
  const funiChartTemplate = `<sfc-chart ref="${wop.name}" chartName="funi-radar-chart" dataName="data" ${disabled} ${titleAttr}
  ${indicatorDataAttr} ${heightAttrs} ${legendFontSizeAttrs} ${isCircleAttr} ${isNumberAttr} ${splitNumberAttrs} ${valueAttr} ${urlAttr} ${converterAttr} :requestOtherParam="${wop.name}RequestOtherParam" ${vOn}></sfc-chart>`;
  return funiChartTemplate;
};

export const sfcHyperLinkTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vModel, vOn } = getElAttrs(fw, formConfig);
  const funiLabelTemplate = `<sfc-hyper-link ref="${wop.name}" ${vModel} :src="${wop.name}Src" class="full-width-input" ${vOn}/>`;
  return funiLabelTemplate;
};

export const funiMoneyInputTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const isEditAttr = wop.disabledCode
    ? `:isEdit="!${wop.name}Disabled"`
    : wop.disabled
    ? `:isEdit="false"`
    : fw.formItemFlag
    ? `:isEdit="!slotProps.disabled"`
    : '';
  const moneyCapitalShowAttr = wop.moneyCapitalShow ? `:moneyCapitalShow="true"` : `:moneyCapitalShow="false"`;
  const showPrefixAttr = wop.showPrefix ? `:showPrefix="true"` : `:showPrefix="false"`;
  const precisionAttr = `:precision="${wop.precision}"`;
  const unitAttr = `:unit="${wop.unit}"`;
  const funiLabelTemplate = `<funi-money-input ref="${wop.name}" ${vModel} ${isEditAttr} ${moneyCapitalShowAttr} ${precisionAttr} ${unitAttr} ${showPrefixAttr} class="full-width-input" ${vOn}/>`;
  return funiLabelTemplate;
};

export const sfcOlMapTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vModel } = getElAttrs(fw, formConfig);
  const mapHeightAttr = wop.mapHeight ? `:mapHeight="${wop.mapHeight}"` : '';
  const viewSyncMapIdAttr = wop.viewSyncMapId ? `viewSyncMapId="${wop.viewSyncMapId}"` : '';
  const projectionAttr = wop.projection ? `projection="${wop.projection}"` : '';
  const baseMapLayersAttr = wop.baseMapLayers ? `baseMapLayers="${wop.baseMapLayers}"` : '';
  const showBaseMapLayerTextAttr = wop.showBaseMapLayerText
    ? `:showBaseMapLayerText="${wop.showBaseMapLayerText}"`
    : '';
  const zoomAttr = wop.zoom ? `:zoom="${wop.zoom}"` : '';
  const layersApiAttr = wop.layersApi ? `layersApi="${wop.layersApi}"` : '';
  const mapCenterAttr = wop.mapCenter && wop.mapCenter.length >= 1 ? `:mapCenter="[${wop.mapCenter}]"` : '';
  const layersApiMethodAttr = wop.layersApiMethod ? `layersApiMethod="${wop.layersApiMethod}"` : '';
  const legendBoxWidthAttr = wop.legendBoxWidth ? `:legendBoxWidth="${wop.legendBoxWidth}"` : '';
  const showLayerTreeAttr = !wop.showLayerTree ? `:showLayerTree="${wop.showLayerTree}"` : '';
  const showOverlayAttr = wop.showOverlay ? `:showOverlay="${wop.showOverlay}"` : '';
  let showDrawAttr = wop.showDraw ? `:showDraw="${wop.showDraw}"` : '';
  if (isNotNullExpression(wop.showDrawCode)) {
    showDrawAttr = `:showDraw="${wop.name}ShowDraw"`;
  }
  const drawToolAttr = isNotNullExpression(wop.drawToolCode)
    ? `:drawTool="${wop.name}DrawTool"`
    : wop.drawTool && wop.drawTool.length > 0
    ? `:drawTool="[${wop.drawTool.map(item => `'${item}'`)}]"`
    : '';
  const baseLayersAttr = isNotNullExpression(wop.baseLayers) ? `:baseLayers="${wop.name}BaseLayers"` : '';
  const layersAttr = isNotNullExpression(wop.layers) ? `:layers="${wop.name}Layers"` : '';
  const layersRequestAttr = isNotNullExpression(wop.layersRequest) ? `:layersRequest="${wop.name}LayersRequest"` : '';
  const mapIdAttr = isNotNullExpression(wop.mapId) ? `:mapId="${wop.name}MapId"` : '';
  const drawStyleAttr = isNotNullExpression(wop.drawStyle) ? `:drawStyle="${wop.name}DrawStyle"` : '';
  const enableSelectAttr = !wop.enableSelect ? `:enableSelect="false"` : `:enableSelect="true"`;
  const baseMapChangeToolAttr = !wop.baseMapChangeTool ? `:baseMapChangeTool="false"` : `:baseMapChangeTool="true"`;
  const isAutoShowModalAttr = wop.isAutoShowModal ? `:isAutoShowModal="true"` : ``;
  const maxSplitAttr = wop.maxSplit >= 0 ? `:maxSplit="${wop.maxSplit}"` : '';
  const listenerAttr = wop.listener ? `v-on="${wop.name}Listener"` : '';
  const drawTypeEnumAttr = wop.drawTypeEnum ? `drawTypeEnum="${wop.drawTypeEnum}"` : '';
  const geomDataTypeAttr = wop.geomDataType ? `geomDataType="${wop.geomDataType}"` : '';
  const modeValueMapAttr = isNotNullExpression(wop.modeValueMap) ? `:modeValueMap="${wop.name}ModeValueMap"` : '';
  const boundAreaListAttr = isNotNullExpression(wop.boundAreaList) ? `:boundAreaList="${wop.name}BoundAreaList"` : '';
  const boundAreaRequestAttr = isNotNullExpression(wop.boundAreaRequest)
    ? `:boundAreaRequest="${wop.name}BoundAreaRequest"`
    : '';
  const boundAreaStyleAttr = isNotNullExpression(wop.boundAreaStyle)
    ? `:boundAreaStyle="${wop.name}BoundAreaStyle"`
    : '';
  const layerAuthHeaderAttr = isNotNullExpression(wop.layerAuthHeader)
    ? `:layerAuthHeader="${wop.name}LayerAuthHeader"`
    : '';
  const highLightStyleAttr = isNotNullExpression(wop.highLightStyle)
    ? `:highLightStyle="${wop.name}HighLightStyle"`
    : '';
  const fitParamsAttr = isNotNullExpression(wop.fitParams) ? `:fitParams="${wop.name}fitParams"` : '';
  const wfsServerAttr = wop.wfsServer ? `wfsServer="${wop.wfsServer}"` : '';
  const modalContentRenderAttr = wop.modalContentRender ? `:modalContentRender="${wop.name}ModalContentRender"` : '';
  const legendShowFuncAttr = wop.legendShowFunc ? `:legendShowFunc="${wop.name}LegendShowFunc"` : '';
  const drawCallbackAttr = wop.drawCallback ? `:drawCallback="${wop.name}DrawCallback"` : '';
  const legendListFuncAttr = wop.legendListFunc ? `:legendListFunc="${wop.name}LegendListFunc"` : '';
  const sfcOlMapTemplate = `<sfc-ol-map ref="${wop.name}" ${vModel} ${mapHeightAttr} ${mapIdAttr} ${viewSyncMapIdAttr} ${projectionAttr} ${baseMapLayersAttr} ${showBaseMapLayerTextAttr} ${mapCenterAttr} ${zoomAttr} ${layersApiAttr} ${layersApiMethodAttr} ${legendBoxWidthAttr} ${showLayerTreeAttr} ${showOverlayAttr} ${showDrawAttr} ${drawToolAttr} ${baseLayersAttr} ${layersAttr} ${layersRequestAttr} ${drawStyleAttr} ${modeValueMapAttr} ${boundAreaListAttr} ${boundAreaRequestAttr} ${layerAuthHeaderAttr} ${highLightStyleAttr} ${fitParamsAttr} ${modalContentRenderAttr} ${wfsServerAttr} ${boundAreaStyleAttr} ${enableSelectAttr} ${baseMapChangeToolAttr} ${isAutoShowModalAttr} ${maxSplitAttr} ${drawTypeEnumAttr} ${geomDataTypeAttr} ${listenerAttr} ${legendShowFuncAttr} ${drawCallbackAttr} ${legendListFuncAttr}/>`;
  return sfcOlMapTemplate;
};

export const sfcVideoTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vModel } = getElAttrs(fw, formConfig);
  const heightAttr = wop.height ? `:height="${wop.height}"` : '';
  const funiVideoStyleAttr = isNotNullExpression(wop.funiVideoStyle)
    ? `:funiVideoStyle="${wop.name}FuniVideoStyle"`
    : '';
  const treeStyleAttr = isNotNullExpression(wop.treeStyle) ? `:treeStyle="${wop.name}TreeStyle"` : '';
  const leftPaddingAttr = wop.leftPadding ? `leftPadding="${wop.leftPadding}"` : '';
  const leftWidthAttr = wop.leftWidth ? `leftWidth="${wop.leftWidth}"` : '';
  const rightWidthAttr = wop.rightWidth ? `rightWidth="${wop.rightWidth}"` : '';
  const objectFitAttr = wop.objectFit ? `objectFit="${wop.objectFit}"` : '';
  const urlAttr = isNotNullExpression(wop.urlCode) ? `:url="${wop.name}VideoUrl"` : '';
  const typeAttr = wop.type ? `type="${wop.type}"` : '';
  const dhConfigAttr = isNotNullExpression(wop.dhConfig) ? `:dhConfig="${wop.name}DhConfig"` : '';
  const isShowControlAttr = isNotNull(wop.isShowControl) ? `:isShowControl="${wop.isShowControl}"` : '';
  const isAutoAttr = isNotNull(wop.isAuto) ? `:isAuto="${wop.isAuto}"` : '';
  const dataTreeAttr = isNotNullExpression(wop.dataTree) ? `:dataTree="${wop.name}DataTree"` : '';
  const dataTreeVideoUrlAttr = isNotNullExpression(wop.dataTreeVideoUrl)
    ? `:dataTreeVideoUrl="${wop.name}DataTreeVideoUrl"`
    : '';
  const isShowTreeParentAttr = isNotNull(wop.isShowTreeParent) ? `:isShowTreeParent="${wop.isShowTreeParent}"` : '';
  const speedAttr = isNotNull(wop.speed) ? `:speed="${wop.speed}"` : '';
  const treeApiParamsAttr = isNotNullExpression(wop.treeApiParams) ? `:treeApiParams="${wop.name}TreeApiParams"` : '';
  const customTreeApisAttr = isNotNullExpression(wop.customTreeApi) ? `:customTreeApi="${wop.name}CustomTreeApi"` : '';
  const getVideoApiAttr = isNotNullExpression(wop.getVideoApi) ? `:getVideoApi="${wop.name}GetVideoApi"` : '';
  const operateCameraAttr = isNotNullExpression(wop.operateCamera) ? `:operateCamera="${wop.name}OperateCamera"` : '';
  const operateDirectApiAttr = isNotNullExpression(wop.operateDirectApi)
    ? `:operateDirectApi="${wop.name}OperateDirectApi"`
    : '';
  const defaultTreePropsAttr = isNotNullExpression(wop.defaultTreeProps)
    ? `:defaultTreeProps="${wop.name}DefaultTreeProps"`
    : '';
  const sfcVideoTemplate = `<sfc-video ref="${wop.name}" ${vModel} ${heightAttr} ${funiVideoStyleAttr} ${treeStyleAttr} ${leftPaddingAttr} ${leftWidthAttr} ${rightWidthAttr} ${objectFitAttr} ${urlAttr} ${typeAttr} ${dhConfigAttr} ${isShowControlAttr} ${isAutoAttr} ${dataTreeAttr} ${dataTreeVideoUrlAttr} ${isShowTreeParentAttr} ${speedAttr} ${treeApiParamsAttr} ${customTreeApisAttr} ${getVideoApiAttr} ${operateCameraAttr} ${operateDirectApiAttr} ${defaultTreePropsAttr}/>`;
  return sfcVideoTemplate;
};

export const sfcWorkflowLogTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const apiAttr = wop.api ? `api="${wop.api}"` : '';
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const sfcFuniOperationemplate = `<sfc-workflow-log ref="${wop.name}" ${apiAttr} ${vModel} ${disabled} ${vOn}/>`;
  return sfcFuniOperationemplate;
};

export const sfcCustomTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const { vModel, vOn, disabled } = getElAttrs(fw, formConfig);
  const attrsAttr = isNotNullExpression(wop.attrs) ? `v-bind="${wop.name}Attrs"` : '';
  const rendererAttr = wop.renderer ? `:renderer="${wop.name}Renderer"` : '';
  const sfcVideoTemplate = `<sfc-custom ref="${wop.name}" ${vModel} ${disabled} ${vOn} ${rendererAttr} ${attrsAttr}/>`;
  return sfcVideoTemplate;
};
