/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-16 17:03:18
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-21 16:17:46
 * @FilePath: \src\components\FuniFormEngine\extension\funi\funi-ext-schema.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import SnowflakeId from 'snowflake-id';
const snowflake = new SnowflakeId();

export const funiLabelSchema = {
  type: 'funi-label',
  icon: 'funi-label',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    length: 0,
    label: '',
    labelAlign: '',
    fieldTooltip: '',
    defaultValue: '',
    defaultAction: '1',
    modelField: '',
    placeholder: '',
    size: '',
    labelWidth: null,
    labelHidden: false,
    required: false,
    requiredCode: '',
    requiredHint: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    validation: '',
    validationHint: '',
    listener: ''
  }
};

export const funiGroupTitleSchema = {
  type: 'funi-group-title',
  icon: 'group-title',
  formItemFlag: false,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    columnWidth: '200px',
    title: '组标题',
    hidden: false,
    hiddenCode: '',
    //-------------------
    customClass: '', //自定义css类名
    //-------------------
    listener: ''
  }
};

export const sfcUserSchema = {
  type: 'sfc-user',
  icon: 'user',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    mode: 'multiple',
    label: '人员选择',
    columnWidth: '200px',
    defaultValue: '',
    defaultAction: '1',
    allowedScope: [],
    disabled: false,
    disabledCode: '',
    required: false,
    requiredCode: '',
    hidden: false,
    hiddenCode: '',
    requiredHint: '',
    validation: '',
    validationHint: '',
    //-------------------
    customClass: '', //自定义css类名
    //-------------------
    listener: ''
  }
};

export const sfcOrgSchema = {
  type: 'sfc-org',
  icon: 'org',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    mode: 'multiple',
    label: '部门选择',
    columnWidth: '200px',
    defaultValue: '',
    defaultAction: '1',
    allowedScope: [],
    disabled: false,
    disabledCode: '',
    hidden: false,
    hiddenCode: '',
    required: false,
    requiredCode: '',
    requiredHint: '',
    validation: '',
    validationHint: '',
    //-------------------
    customClass: '', //自定义css类名
    //-------------------
    listener: ''
  }
};

export const funiSelectSchema = {
  type: 'funi-select',
  icon: 'select-field',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '',
    labelAlign: '',
    fieldTooltip: '',
    defaultValue: '',
    defaultAction: '1',
    placeholder: '',
    columnWidth: '200px',
    size: '',
    labelWidth: null,
    labelHidden: false,
    disabled: false,
    disabledCode: '',
    clearable: true,
    filterable: false,
    allowCreate: false,
    remote: false,
    automaticDropdown: false, //自动下拉
    multiple: false,
    multipleLimit: 0,
    url: '',
    // typeCode: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    required: false,
    requiredCode: '',
    requiredHint: '',
    validation: '',
    validationHint: '',
    listener: '',
    isInitUpdate: false,
    checkedOnlyOne: false,
    modelOption: {},
    selectOptions: [
      { label: 'radio 1', value: 1 },
      { label: 'radio 2', value: 2 },
      { label: 'radio 3', value: 3 }
    ]
  }
};

export const funiCurdSchema = {
  type: 'funi-edit-curd',
  icon: 'edit-curd',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '',
    labelAlign: '',
    fieldTooltip: '',
    defaultValue: { list: [] },
    defaultAction: '1',
    columnWidth: '200px',
    size: '',
    labelWidth: null,
    labelHidden: true,
    disabled: false,
    disabledCode: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    required: false,
    requiredCode: '',
    requiredHint: '',
    listener: '',
    rowKey: 'id',
    url: '',
    columns: [],
    actions: [],
    buttons: [],
    requestOtherParam: {},
    pagination: false
  }
};

export const funiShowCurdSchema = {
  type: 'funi-show-curd',
  icon: 'show-curd',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '',
    labelAlign: '',
    fieldTooltip: '',
    defaultValue: null,
    defaultAction: '1',
    placeholder: '',
    columnWidth: '200px',
    size: '',
    labelWidth: null,
    labelHidden: true,
    disabled: false,
    disabledCode: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    required: false,
    requiredCode: '',
    useTools: false, //是否显示工具
    requiredHint: '',
    listener: '',
    rowKey: 'id',
    tableMaxHeight: '',
    url: '',
    requestOtherParam: {},
    columns: [],
    actions: [],
    buttons: [],
    isShowSearch: false,
    searchConfigGroup: [{ name: 'default', schema: [] }],
    pagination: true,
    selectType: '', //选中类型
    isLineNumber: true, //行号
    selfDataSoure: false, //是否打开自定义数据源
    checkLimit: undefined, //多选数量
    reloadOnActive: false //是否在激活时重新加载数据
  }
};

export const funiSfcDialogSchema = {
  type: 'funi-sfc-dialog',
  icon: 'sfc-dialog',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '',
    defaultValue: null,
    defaultAction: '1',
    disabled: false,
    disabledCode: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    listener: ''
  }
};

export const sfcFileTableSchema = {
  type: 'sfc-file-table',
  icon: 'file-table',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '',
    disabled: false,
    disabledCode: '',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '', //自定义css类名
    hideAddBtn: false, //是否隐藏添加按钮
    hideAddBtnCode: '',
    fileListUrl: '', //列表接口
    delFileUrl: '', //删除接口
    uploadFileUrl: '', //上传接口
    checkFileUrl: '' //校验接口
  }
};

export const sfcGanttSchema = {
  type: 'sfc-gantt',
  icon: 'gantt',
  formItemFlag: true,
  isPopover: true,
  popoverTitle: '甘特图',
  popoverContent: `API输出结构:
  [
        { id: 1, name: '张三', text: '', render: 'split' },
        { id: 101, parent: 1, text: 'Task 1', start_date: '2023-07-02', end_date: '2023-07-25', color: '#D0E4FD' },
        { id: 102, parent: 1, text: 'Task 4', start_date: '2023-08-02', end_date: '2023-08-25', color: '#D0E4FD' },
        { id: 2, name: '李四', text: 'Task 3', start_date: '2023-08-10', end_date: '2023-09-01', color: '#D0E4FD' },
        { id: 3, name: '王二', text: 'Task 2', start_date: '2023-07-06', end_date: '2023-09-01' }
      ]`,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '', //自定义css类名
    listener: '',
    converterCode: '',
    columns: [{ name: 'name', label: '姓名', align: 'center', width: '*' }],
    config: { grid_width: 60 },
    tooltip: true,
    converter: '',
    tooltipText: '',
    tooltipTextCode: '',
    height: 200,
    minGridColumnWidth: 100,
    url: '',
    requestOtherParam: {}
  }
};

export const sfcGuidSchema = {
  type: 'sfc-guid',
  icon: 'guid',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '编码',
    hidden: false,
    hiddenCode: '',
    disabled: false,
    disabledCode: '',
    required: false,
    requiredCode: '',
    labelHidden: false,
    customClass: '', //自定义css类名
    listener: ''
    // url: ''
  }
};

export const funiRegionSchema = {
  type: 'funi-region',
  icon: 'region',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '地址',
    hidden: false,
    hiddenCode: '',
    disabled: false,
    disabledCode: '',
    required: false,
    requiredCode: '',
    labelHidden: false,
    showAddressFull: false,
    customClass: '', //自定义css类名
    listener: '',
    lvl: 5,
    // showAddressFull: true,
    addressFullWidth: '16%'
  }
};

export const sfcDraggableCurdSchema = {
  type: 'sfc-draggable-curd',
  icon: 'draggable-curd',
  formItemFlag: true,
  widgetList: [],
  options: {
    name: '',
    label: '列表标题',
    labelAlign: '',
    fieldTooltip: '',
    defaultValue: { list: [] },
    defaultAction: '1',
    columnWidth: '200px',
    size: '',
    labelWidth: null,
    labelHidden: true,
    disabled: false,
    disabledCode: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    required: false,
    requiredCode: '',
    useTools: false, //是否显示工具
    requiredHint: '',
    listener: '',
    rowKey: 'id',
    url: '',
    actions: [],
    buttons: [],
    requestOtherParam: {},
    columns: [],
    pagination: false,
    showIndex: true,
    horizontalScrolling: true,
    isCorrelation: false, //是否主子明细
    selfDataSoure: false //是否打开自定义数据源
  }
};

export const sfcOperationLogSchema = {
  type: 'sfc-operation-log',
  icon: 'guid',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '操作记录',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    api: '',
    customClass: '' //自定义css类名
  }
};

export const sfcFuniOperationLogSchema = {
  type: 'sfc-funi-operation-log',
  icon: 'guid',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '操作记录',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    api: '',
    customClass: '' //自定义css类名
  }
};

export const sfcFuniLogSchema = {
  type: 'sfc-funi-log',
  icon: 'sfc-funi-log',
  formItemFlag: true,
  options: {
    name: '',
    label: '操作日志',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    api: '',
    customClass: '' //自定义css类名
  }
};

export const sfcIframeSchema = {
  type: 'sfc-iframe',
  icon: 'sfc-iframe',
  formItemFlag: true,
  options: {
    name: '',
    label: '嵌入网页',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '', //自定义css类名
    height: 500,
    src: '',
    isFull: false
  }
};

export const sfcUserNameSchema = {
  type: 'sfc-user-name',
  icon: 'user',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: 'creator_name',
    length: 0,
    label: '创建人',
    labelAlign: '',
    fieldTooltip: '',
    defaultValue:
      '{"codes":"@var{@id{28cdfd96-4591-23f0-dedc-9e0db187d228}:@expression{$funi.auth.user.username}:@label{用户信息.名称}}","label":"用户信息.名称","isExpress":true,"expression":"$funi.auth.user.username"}',
    defaultAction: '1',
    modelField: '',
    placeholder: '',
    size: '',
    labelWidth: null,
    labelHidden: false,
    required: false,
    requiredCode: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    validation: '',
    validationHint: '',
    listener: ''
  }
};
export const sfcOrgNameSchema = {
  type: 'sfc-org-name',
  icon: 'org',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: 'creator_unit',
    length: 0,
    label: '所属部门',
    labelAlign: '',
    fieldTooltip: '',
    defaultValue:
      '{"codes":"@var{@id{6f1ee512-1f79-3ae2-ca37-5910905e8237}:@expression{$funi.auth.user.unit.name}:@label{用户信息.管理主体.名称}}","label":"用户信息.管理主体.名称","isExpress":true,"expression":"$funi.auth.user.unit.name"}',
    defaultAction: '1',
    modelField: '',
    placeholder: '',
    size: '',
    labelWidth: null,
    labelHidden: false,
    required: false,
    requiredCode: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    validation: '',
    validationHint: '',
    listener: ''
  }
};

export const funiHistogramChartSchema = {
  type: 'funi-histogram-chart',
  icon: 'funi-histogram-chart',
  formItemFlag: true,
  isPopover: true,
  popoverTitle: '柱状图',
  popoverContent: `API输出结构:[{ dataValue: 5, dataLabel: '衬衫', color: 'red' }]`,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '',
    tipName: '',
    color: '#5470c6',
    height: 300,
    converter: '',
    url: '',
    requestOtherParam: {}
  }
};

export const sfchyperLinkSchema = {
  type: 'sfc-hyper-link',
  icon: 'sfc-hyper-link',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '',
    labelAlign: '',
    fieldTooltip: '',
    defaultValue: '',
    defaultAction: '1',
    src: '',
    modelField: '',
    placeholder: '',
    size: '',
    labelWidth: null,
    labelHidden: false,
    required: false,
    requiredCode: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    validation: '',
    validationHint: '',
    listener: ''
  }
};

export const funiMoneyInputSchema = {
  type: 'funi-money-input',
  icon: 'funi-money-input',
  formItemFlag: true,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '',
    labelAlign: '',
    fieldTooltip: '',
    defaultValue: '',
    defaultAction: '1',
    disabled: false,
    disabledCode: '',
    moneyCapitalShow: true,
    precision: 2,
    showPrefix: true,
    unit: 1,
    modelField: '',
    placeholder: '',
    size: '',
    labelWidth: null,
    labelHidden: false,
    required: false,
    requiredCode: '',
    hidden: false,
    hiddenCode: '',
    customClass: '', //自定义css类名
    validation: '',
    validationHint: '',
    listener: ''
  }
};

export const sfcOlMapSchema = {
  type: 'sfc-ol-map',
  icon: 'sfc-ol-map',
  formItemFlag: true,
  options: {
    name: '',
    label: '地图',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '', //自定义css类名
    mapHeight: '500',
    // mapId: snowflake.generate(),
    viewSyncMapId: '',
    mapId: '',
    projection: 'EPSG:4326',
    baseMapLayer: 'tdtImgLayer',
    showBaseMapLayerText: false,
    mapCenter: [104.07, 30.66],
    zoom: 12,
    baseLayers: '',
    layers: '',
    layersRequest: '',
    legendBoxWidth: undefined,
    showLayerTree: true,
    showOverlay: false,
    showDraw: false,
    showDrawCode: '',
    drawTool: ['add', 'merge', 'split', 'del'],
    drawToolCode: '',
    drawStyle: '',
    enableSelect: true,
    baseMapChangeTool: true,
    isAutoShowModal: false,
    maxSplit: 2,
    drawTypeEnum: '',
    oldSns: 'contract_sn',
    geomDataType: 'jsonString2',
    modeValueMap: '',
    boundAreaList: '',
    boundAreaRequest: '',
    boundAreaStyle: '',
    layerAuthHeader: '',
    highLightStyle: '',
    wfsServer: '',
    fitParams: '',
    modalContentRender: '',
    legendShowFunc: '',
    drawCallback: '',
    legendListFunc: '',
    listener: ''
  }
};

export const sfcVideoSchema = {
  type: 'sfc-video',
  icon: 'sfc-video',
  formItemFlag: true,
  options: {
    name: '',
    label: '视频',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '', //自定义css类名
    height: 500,
    funiVideoStyle: '',
    treeStyle: '',
    leftPadding: '',
    leftWidth: '',
    rightWidth: '',
    objectFit: 'fill',
    urlCode: '',
    type: '',
    dhConfig: '',
    isShowControl: true,
    isAuto: true,
    dataTree: '',
    dataTreeVideoUrl: '',
    isShowTreeParent: false,
    speed: 3,
    treeApiParams: '',
    customTreeApi: '',
    getVideoApi: '',
    operateCamera: '',
    operateDirectApi: '',
    defaultTreeProps: '',
    listener: ''
  }
};

export const funiLineChartSchema = {
  type: 'funi-line-chart',
  icon: 'funi-line-chart',
  formItemFlag: true,
  isPopover: true,
  popoverTitle: '折线图',
  popoverContent: `API输出结构:[{ dataValue: 5, dataLabel: '衬衫', color: 'red' }]`,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '折线图',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '',
    height: 300,
    converter: '',
    url: '',
    requestOtherParam: {}
  }
};

export const funiPieChartSchema = {
  type: 'funi-pie-chart',
  icon: 'funi-pie-chart',
  formItemFlag: true,
  isPopover: true,
  popoverTitle: '饼状图',
  popoverContent: `API输出结构:[{ dataValue: 5, dataLabel: '衬衫', color: 'red' }]`,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '饼状图',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '',
    height: 300,
    converter: '',
    url: '',
    requestOtherParam: {}
  }
};

export const funiScatterplotChartSchema = {
  type: 'funi-scatterplot-chart',
  icon: 'funi-scatterplot-chart',
  formItemFlag: true,
  isPopover: true,
  popoverTitle: '散点图',
  popoverContent: `API输出结构:[{
       dataLabel: '数据名称',
       dataValue: [
         [160, 150],
         [157, 160],
         [170, 120]
       ]
     }]`,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '散点图',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '',
    height: 300,
    converter: '',
    url: '',
    requestOtherParam: {}
  }
};

export const funiRadarChartSchema = {
  type: 'funi-radar-chart',
  icon: 'funi-radar-chart',
  formItemFlag: true,
  isPopover: true,
  popoverTitle: '雷达图',
  popoverContent: `API输出结构:[{
          dataValue: [4200, 3000, 20000, 35000, 50000, 18000],
          dataLabel: 'Allocated Budget'
        },
        {
          dataValue: [5000, 14000, 28000, 26000, 42000, 21000],
          dataLabel: 'Actual Spending'
        }]`,
  options: {
    listColumnWidth: null,
    listColumnAlign: 'center',
    listColumnHidden: false,
    listColumnHiddenCode: '',
    listColumnWrap: false,
    name: '',
    label: '雷达图',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    customClass: '',
    height: 300,
    legendFontSize: 10,
    isCircle: false,
    splitNumber: 3,
    isNumber: false,
    indicatorData: [
      { dataLabel: 'one', dataValue: 6500 },
      { dataLabel: 'tow', dataValue: 16000 },
      { dataLabel: 'three', dataValue: 30000 },
      { dataLabel: 'four', dataValue: 38000 },
      { dataLabel: 'five', dataValue: 52000 },
      { dataLabel: 'six', dataValue: 25000 }
    ],
    converter: '',
    url: '',
    requestOtherParam: {}
  }
};

export const sfcWorkflowLogSchema = {
  type: 'sfc-workflow-log',
  icon: 'sfc-workflow-log',
  formItemFlag: true,
  options: {
    name: '',
    label: '工作流日志',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    businessId: '',
    customClass: '' //自定义css类名
  }
};

export const sfcCustomSchema = {
  type: 'sfc-custom',
  icon: 'sfc-custom',
  formItemFlag: true,
  options: {
    name: '',
    label: '自定义组件',
    hidden: false,
    hiddenCode: '',
    labelHidden: true,
    renderer: '', //渲染函数
    attrs: '', //自定义属性
    customClass: '', //自定义css类名
    listener: ''
  }
};
