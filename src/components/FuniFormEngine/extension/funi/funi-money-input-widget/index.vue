<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-16 17:01:58
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-08-14 10:46:22
 * @FilePath: \src\components\FuniFormEngine\extension\funi\funi-money-input-widget\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <form-item-wrapper :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId">
    <funi-money-input ref="fieldEditor"
      v-model="fieldModel"
      :isEdit="!field.options.disabled"
      :moneyCapitalShow="field.options.moneyCapitalShow"
      :precision="field.options.precision"
      :showPrefix="field.options.showPrefix"
      :unit="field.options.unit"
      class="full-width-input" />
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from '../../../form-widget/field-widget/form-item-wrapper.vue'
import emitter from '../../../common/utils/emitter'
import i18n from '../../../common/utils/i18n'
import fieldMixin from "../../../form-widget/field-widget/fieldMixin"
export default {
  name: "funi-money-input-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  components: {
    FormItemWrapper
  },
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  created () {
    this.registerToRefList()
    this.initEventHandler()
  },
  beforeUnmount () {
    this.unregisterFromRefList()
  },
  methods: {
    handleCloseCustomEvent () {
      if (this.field.options.onClose) {
        let changeFn = new Function(this.field.options.onClose)
        changeFn.call(this)
      }
    }

  }
}
</script>
<style lang="scss" scoped>
.full-width-input {
  width: 100% !important;
}
</style>