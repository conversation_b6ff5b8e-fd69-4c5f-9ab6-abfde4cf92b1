<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:49
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-08-15 15:17:25
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\extension\funi\funi-group-title-widget\index.vue
 * @Description: 
-->
<template>
  <static-content-wrapper :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId">
    <funi-group-title ref="fieldEditor"
      :title="field.options.title" /></static-content-wrapper>
</template>

<script>
import StaticContentWrapper from '../../../form-widget/field-widget/static-content-wrapper.vue'
import emitter from '../../../common/utils/emitter'
import i18n, { translate } from "../../../common/utils/i18n";
import fieldMixin from "../../../form-widget/field-widget/fieldMixin"

export default {
  name: "funi-group-title-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  components: {
    StaticContentWrapper,
  },
  computed: {

  },
  beforeCreate () {
    /* 这里不能访问方法和属性！！ */
  },

  created () {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList()
    this.initEventHandler()

    this.handleOnCreated()
  },

  mounted () {
    this.handleOnMounted()
  },

  beforeUnmount () {
    this.unregisterFromRefList()
  },

  methods: {

  }

}
</script>
