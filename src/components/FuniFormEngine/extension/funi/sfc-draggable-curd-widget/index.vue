<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-10-17 17:36:13
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-10-30 18:10:53
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\extension\funi\sfc-draggable-curd-widget\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->

<template>
  <form-item-wrapper ref="formItemWrapperRef"
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId">
    <sfc-draggable-curd :class="[selected ? 'curd-selected' : '', !!field.options.folded ? 'folded' : '', customClass]"
      style="width:100%;"
      :widget="field"
      :parentWidget="parentWidget"
      :parentList="parentList"
      :indexOfParentList="indexOfParentList"
      :designer="designer"
      :label="field.options.label"
      :pagination="field.options.pagination"
      :columns="field.options.columns"
      :actions="field.options.actions"
      :buttons="field.options.buttons"
      @click.stop="selectWidget(field)" />
  </form-item-wrapper>
</template>

<script>
import i18n from "../../../common/utils/i18n";
import SfcDraggableCurd from "./sfc-draggable-curd.vue";
import FormItemWrapper from '../../../form-widget/field-widget/form-item-wrapper.vue'
import emitter from '../../../common/utils/emitter'
import fieldMixin from "../../../form-widget/field-widget/fieldMixin"

export default {
  name: "sfc-draggable-curd-widget",
  componentName: 'ContainerWidget',
  mixins: [emitter, fieldMixin, i18n],
  inject: ['refList'],
  components: {
    FormItemWrapper,
    SfcDraggableCurd
  },
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },
  },
  created () {
    this.registerToRefList()
    this.initEventHandler()
  },
  beforeUnmount () {
    this.unregisterFromRefList()
  },
  methods: {
    handleCloseCustomEvent () {
      if (this.field.options.onClose) {
        let changeFn = new Function(this.field.options.onClose)
        changeFn.call(this)
      }
    },
    selectWidget (field) {
      this.$refs.formItemWrapperRef.selectField(field);
    }
  }
}
</script>

<style lang="scss" scoped>
.sfc-draggable-curd-widget {
  padding: 0px 8px;
  .sfc-draggable-curd.curd-selected {
    outline: 2px solid #409eff !important;
  }
}
:deep(.selected) {
  font-weight: normal !important;
  color: inherit !important;
}
</style>
