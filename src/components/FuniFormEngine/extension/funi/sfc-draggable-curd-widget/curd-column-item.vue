<template>
  <div class="curd-column-item"
    :style="itemStyle">
    <div class="column-label"
      :style="columnStyle">{{label}}</div>
    <div class="column-component"
      :style="columnStyle">组件</div>
  </div>
</template>

<script>
import { reactive, toRefs } from 'vue'
export default {
  name: 'curd-column-item',
  components: {
  },
  props: {
    label: {
      type: String
    },
    align: {
      type: String,
      default: 'center'
    },
    width: {
      type: [String, Number]
    }
  },
  setup (props) {
    const state = reactive({
      itemStyle: {
        width: props.width + ''
      },
      columnStyle: {
        'justify-content': props.align ?? 'center',
      }
    })
    if (!props.width) {
      delete state.itemStyle.width;
      state.itemStyle['flex-grow'] = 1;
    }
    return { ...toRefs(state) }
  }
}
</script>
<style lang="scss" scoped>
.curd-column-item {
  display: flex;
  flex-direction: column;
  .column-label {
    padding: 8px 0px;
    border-top: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    display: flex;
  }
  .column-component {
    padding: 8px 0px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
  }
}
</style>
