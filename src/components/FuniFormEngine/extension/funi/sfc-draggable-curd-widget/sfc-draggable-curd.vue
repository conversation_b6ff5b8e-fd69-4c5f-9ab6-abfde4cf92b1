<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-10-18 11:06:06
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-25 18:00:17
 * @FilePath: \src\components\FuniFormEngine\extension\funi\sfc-draggable-curd-widget\sfc-draggable-curd.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="sfc-draggable-curd"
    @click="e=>$emit('click',e)">
    <el-row justify="space-between"
      align="middle">
      <div class="header-title">
        <funi-group-title :title="label" />
        <div class="header-buttons">
          <component v-for="(item, index) in leftButtonComputed"
            :key="index"
            :is="item.component"
            v-bind="item.props || {}"
            :style="item.style || {}" />
        </div>
      </div>
      <div class="header-buttons">
        <component v-for="(item, index) in rightButtonComputed"
          :key="index"
          :is="item.component"
          v-bind="item.props || {}"
          :style="item.style || {}" />
      </div>
    </el-row>
    <div class="body-row">
      <curd-column-item v-if="widget.widgetList&&widget.widgetList.length>0&&widget.options.showIndex"
        label="序号"
        :widget="{options:{}}"
        style="width:60px;min-width: 60px;">
        <span>1</span>
      </curd-column-item>
      <div class="dragg-container">
        <draggable :list="widget.widgetList"
          item-key="id"
          v-bind="{group:'dragGroup', ghostClass: 'custom-ghost',animation: 200}"
          handle=".drag-handler"
          tag="transition-group"
          :component-data="{name: 'fade'}"
          @add="(evt) => onDragAdd(evt)"
          @update="onContainerDragUpdate"
          :move="checkContainerMove">
          <template #item="{ element: subWidget, index: swIdx }">
            <div :style="getStyle(subWidget)"
              style="flex-shrink:0">
              <component :is="subWidget.type + '-widget'"
                :field="subWidget"
                :widget="subWidget"
                :designer="designer"
                :key="subWidget.id"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
                :design-state="true"
                style="width:100%;" />
            </div>
          </template>
        </draggable>
      </div>
      <curd-column-item v-if="widget.widgetList&&widget.widgetList.length>0&&actions&&actions.length>0"
        label="操作"
        :widget="{options:{}}"
        style="min-width:75px;">
        <span>
          <sfc-button v-for="(item,index) in actions"
            :key="index"
            :type="item.type"
            :componentType="item.componentType"
            :icon="item.showIcon==='noIcon'?'':item.icon"
            :iconPosition="item.iconPosition"
            :label="item.showIcon==='onlyIcon'?'':item.label"
            :size="item.size" />
        </span>
      </curd-column-item>
    </div>
    <el-row v-if="pagination"
      class="footer-pagination"
      justify="end">
      <el-pagination v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :small="true"
        :disabled="false"
        layout="total, sizes, prev, pager, next, jumper"
        :total="20" />
    </el-row>
    <el-row>
      <div style="height:20px;"></div>
    </el-row>
  </div>
</template>

<script>
import { containers, basicFields, advancedFields, customFields } from "../../../widget-panel/widgetsConfig";
import { computed, h, reactive, withDirectives, resolveDirective, resolveComponent, toRefs, watch } from 'vue';
import { generateId, deepClone } from "../../../common/utils/util.js";
import i18n from "../../../common/utils/i18n";
import containerMixin from "../../../form-widget/container-widget/containerMixin";
import refMixinDesign from "../../../common/js/refMixinDesign";
import FieldComponents from '../../../form-widget/field-widget/index.js';
export default {
  name: 'sfc-draggable-curd',
  components: {
    ...FieldComponents
  },
  mixins: [i18n, containerMixin, refMixinDesign],
  inject: ['refList'],
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    label: {
      type: String,
      default: '可拖拽列表'
    },
    pagination: {
      type: Boolean,
      default: false
    },
    columns: { type: Array, default: () => [] },
    actions: { type: Array, default: () => [] },
    buttons: { type: Array, default: () => [] },
  },
  setup (props) {
    const state = reactive({
      currentPage: 1,
      pageSize: 10
    })

    watch(() => props.columns, (newVal, oldVal) => {
      if (!(oldVal && oldVal.length > 0)) {
        const allFields = [...containers, ...basicFields, ...advancedFields, ...customFields];
        if (newVal && newVal.length > 0) {
          let widgetList = [];
          newVal.forEach(item => {
            const tempId = generateId();
            let type = 'input'
            if (['enum'].includes(item.dataType)) {
              type = 'funi-select';
            } else if (['association'].includes(item.dataType)) {
              type = 'funi-select';
            } else if (['date'].includes(item.dataType)) {
              type = 'date';
            }
            let fIndex = allFields.findIndex(f => f.type === type);
            if (fIndex >= 0) {
              const widget = deepClone(allFields[fIndex]);
              widget.key = tempId;
              widget.id = `input${tempId}`;
              widget.options.name = item.prop;
              widget.options.label = item.label;
              widgetList.push(widget);
            }
          });
          Object.assign(props.widget, {
            widgetList
          });
        } else {
          Object.assign(props.widget, { widgetList: [] });
        }
      }
    })

    const getButtons = (buttons, position) => {
      let realButtons = [];
      if (buttons && buttons.length > 0) {
        buttons.filter(btn => {
          if (position === 'right') {
            return btn.position === 'right'
          } else if (position === 'all') {
            return true
          } else {
            return btn.position !== 'right'
          }
        }).forEach(btn => {
          let attrs = {};
          if (typeof (btn.attrs) === 'string') {
            try {
              attrs = new Function(`return ${btn.attrs.replaceAll('@', '')}`)();
            } catch (ex) { console.log(ex); }
          } else {
            attrs = btn.attrs;
          }
          if (btn.showIcon === 'noIcon') {
            delete btn.icon;
            delete btn.iconPosition;
          }
          if (btn.showIcon === 'onlyIcon') {
            delete btn.label;
          }
          const button = h(resolveComponent('sfc-button'), {
            type: btn.type,
            componentType: btn.componentType,
            icon: btn.icon,
            iconPosition: btn.iconPosition,
            label: btn.label,
            size: btn.size,
            ...attrs
          });
          let action;
          if (btn.hasAuth && btn.permission && btn.permission.code) {
            const authDirective = resolveDirective('auth');
            action = withDirectives(
              button,
              [[authDirective, btn.permission.code]]
            );
          } else {
            action = button;
          }
          realButtons.push({
            component: action
          })
        })
      }
      return realButtons;
    }

    const leftButtonComputed = computed(() => {
      const { buttons } = props;
      // let realButtons = getButtons(buttons, 'left');
      let realButtons = [];
      return realButtons;
    })

    const rightButtonComputed = computed(() => {
      const { buttons } = props;
      let realButtons = getButtons(buttons, 'all');
      return realButtons;
    })

    return { ...toRefs(state), leftButtonComputed, rightButtonComputed };
  },
  created () {
    this.initRefList()
  },
  methods: {
    /**
     * 检查接收哪些组件拖放，如不接受某些组件拖放，则根据组件类型判断后返回false
     * @param evt
     * @returns {boolean}
     */
    checkContainerMove (evt) {
      return true
    },
    getStyle (widget) {
      let styleObj = { 'flex-grow': '1' };
      if (widget && widget.options && widget.options.listColumnWidth > 0) {
        styleObj = { width: widget.options.listColumnWidth + 'px' };
      }
      return styleObj;
    },
    onDragAdd (evt) {
      const newIndex = evt.newIndex;
      const widget = this.widget.widgetList[newIndex];
      if (widget) {
        const typeList = ['input', 'textarea', 'number', 'radio', 'checkbox', 'time', 'time-range', 'date', 'date-range',
          'switch', 'rate', 'color', 'slider', 'picture-upload', 'file-upload', 'cascader', 'funi-label', 'funi-group-title',
          'sfc-user', 'sfc-org', 'funi-select', 'sfc-guid', 'funi-region']
        if (!typeList.includes(widget.type)) {
          this.$notify({ message: '列表不支持该组件!', type: 'error' });
          setTimeout(() => {
            if (this.widget.widgetList && this.widget.widgetList.length > 0) {
              let delIndex = this.widget.widgetList.findIndex(item => item.id === widget.id);
              this.widget.widgetList.splice(delIndex, 1);
            }
          }, 100)
          return;
        }
      }
      this.onContainerDragAdd(evt, this.widget.widgetList);
    }
  }
}
</script>
<style lang="scss" scoped>
.sfc-draggable-curd {
  .header-title {
    margin-left: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    .header-buttons {
      margin-left: 8px;
      margin-right: 8px;
    }
  }
  .body-row {
    display: flex;
    flex-direction: row;
    padding-left: 8px;
    min-height: 60px;
    .dragg-container {
      flex-grow: 1;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      overflow-x: auto;
      .custom-ghost {
        min-height: 60px;
      }
    }
  }
  .footer-pagination {
    margin: 8px 0px;
  }
  :deep(.el-row) {
    border: none !important;
  }
  .body-row {
    border-bottom: 1px solid #ebeef5 !important;
  }
}
</style>
