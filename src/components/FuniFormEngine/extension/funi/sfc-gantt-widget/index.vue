<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-24 13:45:03
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-11-17 14:33:42
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\extension\funi\sfc-gantt-widget\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <form-item-wrapper :designer="designer"
      :field="field"
      :rules="rules"
      :design-state="designState"
      :parent-widget="parentWidget"
      :parent-list="parentList"
      :index-of-parent-list="indexOfParentList"
      :sub-form-row-index="subFormRowIndex"
      :sub-form-col-index="subFormColIndex"
      :sub-form-row-id="subFormRowId">
      <sfc-gantt ref="ganttRef"
        :columns="columnsComputed"
        :height="field.options.height"
        :minGridColumnWidth="field.options.minGridColumnWidth"
        :config="field.options.config"
        :tooltip="field.options.tooltip" />
    </form-item-wrapper>
  </div>
</template>
<script>
import { deepClone } from '../../../common/utils/util';
import FormItemWrapper from '../../../form-widget/field-widget/form-item-wrapper.vue'
import emitter from '../../../common/utils/emitter'
import i18n from '../../../common/utils/i18n'
import fieldMixin from "../../../form-widget/field-widget/fieldMixin"
export default {
  name: "sfc-gantt-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  components: {
    FormItemWrapper,
  },
  inject: ['designStatus'],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  watch: {
    'designStatus.previewShow': {
      handler (newVal) {
        if (!newVal) {
          if (this.$refs.ganttRef) {
            this.$refs.ganttRef.initGantt();
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    columnsComputed () {
      return deepClone(this.field.options.columns);
    }
  },
  created () {
    this.registerToRefList()
    this.initEventHandler()
  },
  beforeUnmount () {
    this.unregisterFromRefList()
  },
  methods: {
    handleCloseCustomEvent () {
      if (this.field.options.onClose) {
        let changeFn = new Function(this.field.options.onClose)
        changeFn.call(this)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.full-width-input {
  width: 100% !important;
}
</style>