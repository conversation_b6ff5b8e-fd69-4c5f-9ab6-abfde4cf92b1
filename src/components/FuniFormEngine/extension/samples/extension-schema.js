/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2022-11-08 17:40:46
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-03-27 16:45:20
 * @FilePath: \variant-form3-vite-master\src\extension\samples\extension-schema.js
 * @Description: 
 */
export const cardSchema = {
  type: 'card',
  category: 'container',
  icon: 'card',
  widgetList: [],
  options: {
    name: '',
    label: 'card',
    hidden: false,
    folded: false,
    showFold: true,
    cardWidth: '100%',
    shadow: 'never',
    customClass: '',
  }
}

export const alertSchema = {
  type: 'alert',
  icon: 'alert',
  formItemFlag: false,
  options: {
    name: '',
    title: 'Good things are coming...',
    type: 'info',
    description: '',
    closable: true,
    closeText: '',
    center: true,
    showIcon: false,
    effect: 'light',
    hidden: false,
    onClose: '',
    customClass: '',
  }
}

export const curdSchema = {
  type: 'curd',
  icon: 'alert',
  formItemFlag: false,
  options: {
    name: '',
    title: '暂存',
    type:'primary'
  }
}
