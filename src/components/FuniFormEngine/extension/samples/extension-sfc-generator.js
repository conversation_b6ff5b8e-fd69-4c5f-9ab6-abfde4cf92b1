/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2022-11-08 17:40:46
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-27 16:26:48
 * @FilePath: \src\components\FuniFormEngine\extension\samples\extension-sfc-generator.js
 * @Description:
 */
import { isNotNullExpression } from '@/components/FuniFormEngine/common/utils/util';
import {
  buildClassAttr,
  buildContainerWidget,
  buildFieldWidget
} from '@/components/FuniFormEngine/common/utils/funi-sfc-generator';

export const cardTemplateGenerator = function (cw, formConfig, designerConfig) {
  const wop = cw.options;
  //const headerAttr = `header="${wop.label}"`
  const classAttr = buildClassAttr(cw);
  const styleAttr = wop.cardWidth ? `style="{width: ${wop.cardWidth} !important}"` : '';
  const shadowAttr = `shadow="${wop.shadow}"`;
  const vShowAttr = isNotNullExpression(wop.hiddenCode) ? `v-if="${wop.name}Hidden"` : wop.hidden ? `v-if="false"` : '';

  const cardTemplate = `<div class="card-container">
  <el-card ${classAttr} ${styleAttr} ${shadowAttr} ${vShowAttr}>
    <template #header>
      <div class="clear-fix">
      <funi-group-title title="${wop.label}" groupMargin="0px 0px"/>
        ${wop.showFold ? `<i class="float-right el-icon-arrow-down"></i>` : ''}
      </div>
    </template>
    ${cw.widgetList
      .map(wItem => {
        if (wItem.category === 'container') {
          return buildContainerWidget(wItem, formConfig, designerConfig);
        } else {
          return buildFieldWidget(wItem, formConfig, designerConfig);
        }
      })
      .join('')}
  </el-card>
</div>`;

  return cardTemplate;
};

export const alertTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const titleAttr = `title="${wop.title}"`;
  const typeAttr = `type=${wop.type}`;
  const descriptionAttr = wop.description ? `description="${wop.description}"` : '';
  const closableAttr = `:closable="${wop.closable}"`;
  const closeTextAttr = wop.closeText ? `close-text="${wop.closeText}"` : '';
  const centerAttr = `:center="${wop.center}"`;
  const showIconAttr = `:show-icon="${wop.showIcon}"`;
  const effectAttr = `effect="${wop.effect}"`;

  const alertTemplate = `<el-alert ${titleAttr} ${typeAttr} ${descriptionAttr} ${closableAttr} ${closeTextAttr} ${centerAttr} 
  ${showIconAttr} ${effectAttr}>
</el-alert>`;

  return alertTemplate;
};

export const curdTemplateGenerator = function (fw, formConfig) {
  const wop = fw.options;
  const typeAttr = `type="${wop.type}"`;

  const alertTemplate = `<el-button ${typeAttr}>${wop.title}
</el-button>`;
  return alertTemplate;
};
