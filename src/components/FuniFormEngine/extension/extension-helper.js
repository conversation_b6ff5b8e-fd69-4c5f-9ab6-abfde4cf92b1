/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-24 16:09:47
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-24 16:32:22
 * @FilePath: \funi-form-engine-demo\src\extension\extension-helper.js
 * @Description: 
 */
import {
  addContainerWidgetSchema,
  addBasicFieldSchema,
  addAdvancedFieldSchema,
  addCustomWidgetSchema
} from '../widget-panel/widgetsConfig'
import {
  registerCommonProperty,
  registerAdvancedProperty,
  registerEventProperty
} from '../setting-panel/propertyRegister'


export default {
  addContainerWidgetSchema,
  addBasicFieldSchema,
  addAdvancedFieldSchema,
  addCustomWidgetSchema,

  registerCommonProperty,
  registerAdvancedProperty,
  registerEventProperty,
}
