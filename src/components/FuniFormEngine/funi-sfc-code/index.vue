<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-26 15:52:16
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-12 18:43:41
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\funi-sfc-code\index.vue
 * @Description: 
-->
<template>
  <div class="index">
    <sfc-design-dialog :title="title"
      v-model="dialogVisible"
      width="65%"
      destroy-on-close
      :show-close="true"
      @fullChange="onFullChange">
      <code-editor :key="key"
        :mode="'html'"
        :readonly="true"
        v-model="code"
        :user-worker="false"
        :maxLines="maxLines" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary"
            @click="onSave">
            确定
          </el-button>
        </span>
      </template>
    </sfc-design-dialog>
  </div>
</template>

<script>
import { Base64 } from 'js-base64'
import CodeEditor from '../code-editor/index.vue';
import { compile } from "../common/utils/vue3SfcCompiler";
import lowcode from '../common/utils/lowcode';
export default {
  name: 'index',
  components: {
    CodeEditor
  },
  props: {
  },
  data () {
    return {
      key: 0,
      title: '',
      dialogVisible: false,
      code: '',
      configJson: '',
      isForm: false,
      maxLines: 20
    }
  },
  mounted () {

  },
  methods: {
    show (code, configJson, isForm) {
      this.title = isForm ? '表单代码预览' : '弹窗代码预览';
      this.isForm = isForm;
      this.dialogVisible = true;
      this.code = code;
      this.configJson = configJson;
    },
    onFullChange (isFull) {
      this.maxLines = isFull ? 50 : 20;
      this.key++;
    },
    onSave () {
      const { code, configJson, isForm } = this;
      let jsStr = compile(code, true);
      this.$emit('save', { code: lowcode.encrypt(jsStr), config: lowcode.encrypt(configJson) });
      this.$emit('saveConfig', { code: lowcode.encrypt(jsStr), config: lowcode.encrypt(configJson), configJson, isClose: !isForm });
      this.dialogVisible = false;
    },
    saveCode (code, configJson) {
      let jsStr = compile(code, true);
      this.$emit('save', { code: lowcode.encrypt(jsStr), config: lowcode.encrypt(configJson) });
      this.$emit('saveConfig', { code: lowcode.encrypt(jsStr), config: lowcode.encrypt(configJson), configJson });
    },
    getCode (code, configJson) {
      let jsStr = compile(code, true);
      return { code: lowcode.encrypt(jsStr), config: lowcode.encrypt(configJson), configJson }
    }
  },
}
</script>