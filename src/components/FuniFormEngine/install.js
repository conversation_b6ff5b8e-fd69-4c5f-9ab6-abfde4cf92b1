/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-25 10:54:04
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-21 10:20:55
 * @FilePath: \src\components\FuniFormEngine\install.js
 * @Description:
 */
// import './common/styles/index.scss';
import './common/iconfont/iconfont.css';
import './common/styles/global.scss';
import vuePrint from 'vue3-print-nb';
import Draggable from '@/../lib/vuedraggable/lib/vuedraggable.umd.js';
import { addDirective } from './common/utils/directive';
import 'virtual:svg-icons-register';
import { installI18n } from './common/utils/i18n';
import lowCode from './common/utils/lowcode.js';
import lowCodeRequest from './common/request/index.js';
import { registerIcon } from './common/utils/el-icons';
import ContainerWidgets from './form-widget/container-widget/index';
import ContainerItems from './form-render/container-item/index';
import { loadExtension } from './extension/extension-loader';
import SfcUpload from './components/sfc-upload/index.vue';
import { showSfcUpload } from './components/sfc-upload-dialog/service.js';
import { createShowLog } from './components/sfc-funi-log-dialog/service.js';
import { createCustomDialog } from './components/sfc-dialog/service.js';
import SfcCurd from './components/sfc-curd/index.vue';
import SfcShowCurd from './components/sfc-show-curd/index.vue';
import SfcDialog from './components/sfc-dialog/index.vue';
import SfcDialogRender from './components/sfc-dialog-render/index.vue';
import SfcStep from './components/sfc-step/index.vue';
import SfcUser from './components/sfc-user/index.vue';
import SfcOrg from './components/sfc-org/index.vue';
import SfcIframe from './components/sfc-iframe/index.vue';
import SfcForm from './components/sfc-form/index.vue';
import SfcFormItem from './components/sfc-form-item/index.vue';
import SfcSelect from './components/sfc-select/index.vue';
import SfcGantt from './components/sfc-gantt/index.vue';
import SfcButton from './components/sfc-button/index.vue';
import FuniFormEngine from './index.vue';
import DialogDesigner from './DialogDesigner.vue';
import SfcBlank from './components/sfc-blank/index.vue';
import SfcFileTable from './components/sfc-file-table/index.vue';
import SfcRadioGroup from './components/sfc-radio-group/index.vue';
import SfcCheckboxGroup from './components/sfc-checkbox-group/index.vue';
import SfcGuid from './components/sfc-guid/index.vue';
import SfcOperationLog from './components/sfc-operation-log/index.vue';
import SfcFuniOperationLog from './components/sfc-funi-operation-log/index.vue';
import SfcFuniLog from './components/sfc-funi-log/index.vue';
import SfcRegion from './components/sfc-region/index.vue';
import SfcDesignDialog from './sfc-design-dialog/index.vue';
import SfcOrgName from './components/sfc-org-name/index.vue';
import SfcChart from './components/sfc-chart/index.vue';
import SfcHyperLink from './components/sfc-hyper-link/index.vue';
import SfcCascader from './components/sfc-cascader/index.vue';
import SfcOlMap from './components/sfc-ol-map/index.vue';
import SfcVideo from './components/sfc-video/index.vue';
import SfcWorkflowLog from './components/sfc-workflow-log/index.vue';
import SfcCustom from './components/sfc-custom/index.vue';
export default {
  install(app) {
    app.use(vuePrint);
    app.component('draggable', Draggable);
    app.component('SfcUpload', SfcUpload);
    app.component('SfcCurd', SfcCurd);
    app.component('SfcShowCurd', SfcShowCurd);
    app.component('SfcDialog', SfcDialog);
    app.component('SfcDialogRender', SfcDialogRender);
    app.component('SfcStep', SfcStep);
    app.component('SfcForm', SfcForm);
    app.component('SfcFormItem', SfcFormItem);
    app.component('SfcUser', SfcUser);
    app.component('SfcOrg', SfcOrg);
    app.component('SfcSelect', SfcSelect);
    app.component('SfcGantt', SfcGantt);
    app.component('SfcBlank', SfcBlank);
    app.component('SfcFileTable', SfcFileTable);
    app.component('SfcButton', SfcButton);
    app.component('DialogDesigner', DialogDesigner);
    app.component('SfcRadioGroup', SfcRadioGroup);
    app.component('SfcCheckboxGroup', SfcCheckboxGroup);
    app.component('SfcGuid', SfcGuid);
    app.component('SfcOperationLog', SfcOperationLog);
    app.component('SfcFuniOperationLog', SfcFuniOperationLog);
    app.component('SfcFuniLog', SfcFuniLog);
    app.component('FuniFormEngine', FuniFormEngine);
    app.component('SfcRegion', SfcRegion);
    app.component('SfcDesignDialog', SfcDesignDialog);
    app.component('SfcIframe', SfcIframe);
    app.component('SfcOrgName', SfcOrgName);
    app.component('SfcChart', SfcChart);
    app.component('SfcHyperLink', SfcHyperLink);
    app.component('SfcCascader', SfcCascader);
    app.component('SfcOlMap', SfcOlMap);
    app.component('SfcVideo', SfcVideo);
    app.component('SfcWorkflowLog', SfcWorkflowLog);
    app.component('SfcCustom', SfcCustom);
    app.config.globalProperties.$lowCode = lowCode;
    app.config.globalProperties.$lowCodeRequest = lowCodeRequest;
    app.config.globalProperties.$showSfcUpload = showSfcUpload;
    app.config.globalProperties.$showLog = createShowLog(app);
    app.config.globalProperties.$showCustomDialog = createCustomDialog(app);
    addDirective(app);
    installI18n(app);
    registerIcon(app);
    app.use(ContainerWidgets);
    app.use(ContainerItems);
    loadExtension(app);
  }
};
