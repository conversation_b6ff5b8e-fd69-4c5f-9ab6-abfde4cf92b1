/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-09-20 11:01:01
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-01-09 10:29:06
 * @FilePath: \src\components\FuniFormEngine\toolbar-panel\templateFrontCode.js
 * @Description:
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved.
 */
const getTemplateFrontCode = (onFormCreated, onFormMounted, inputConvert, validateFun, outputConvert) => {
  const templateFrontCode = `/* 公共API：
 * context: {
            params: {}, // 页面参数
            data: {}, // 页面数据
            handler: {}, // 页面方法
            refs: {}, // vue $refs对象
            router: {}, // vue $router对象
            route: {}, // vue $route对象
            query: {}, // 页面query参数
            emit: {}, // vue $emit方法
            message: {}, // vue $message方法
            notify: {}, // vue $notify方法
            alert: {}, // vue $alert方法
            loading: {}, // vue $loading方法
            showSfcUpload: {}, // vue $showSfcUpload方法
            showLog: {}, // vue $showLog方法
            formData: {}, // 当前表单数据
            stepData: {}, // 步骤表单数据
            workflow: {}, // 工作流数据
}
除onFormCreated外其他函数均可直接使用context对象
*/
// 表单插件代码
class ExtendForm {

  /**
   * 表单创建时执行(context对象不可用)
   */
  onFormCreated(){
  ${onFormCreated ?? ''}
  }

  /**
   * 表单挂载时执行
   */
  onFormMounted(){
  ${onFormMounted ?? ''}
  }

  /**
   * 输入数据前置钩子，进入表单页获取业务数据对象后执行
   * @param {Object} formData - 后端返回的数据，可对业务数据预处理后输出(此时 context.formData 不可用)
   * @returns {Object} 预处理后的formData
   */
  inputFormData(formData){
    // 对输入的formData进行预处理
    ${inputConvert ? inputConvert : 'return formData;'}
  }

  /**
   * 表单提交校验 提交按钮点击后执行
   * @param {Object} formData - 当前页表单数据
   * @returns {boolean} 校验结果 true表示校验通过,false校验不通过
   */
  formValite(formData){
    ${validateFun ? validateFun : 'return true;'}
  }

  /**
   * 输出数据前置钩子，表单提交校验后执行
   * @param {Object} formData - 输入校验后的表单数据
   * @returns {Object} 经转换后需要传给后端的数据
   */
  outputFormData(formData){
    // 对输出的formData进行转换处理
    ${outputConvert ? outputConvert : 'return formData;'}
  }

  /**
   * 保存后执行
   * @param {Object} formData - 当前页表单数据
   * @returns {Object} 返回值可以是boolean 或 promise对象
   */
  afterSave(formData){
    return true;
  }

  /**
   * 提交前事件 输出数据函数之后执行
   * @param {Object} formData - 当前页表单数据
   * @returns {Object} 返回值可以是boolean 或 promise对象
   */
  beforeSubmit(formData){
    return true;
  }

  /**
   * 提交成功后执行
   * @param {Object} workflow - 工作流数据
   */
  afterSubmit(workflow={}){
    return true;
  }

  /**
   * 获取自定义审批人列表
   * @param {Object} formData - outputFormData返回的数据
   * @returns {Object} 返回审批人对象数组 若已设置工作流信息则该函数不起作用
   */
  getCustomAssigneeList(){
    return [];
  }
}`;
  return templateFrontCode;
};

export { getTemplateFrontCode };
