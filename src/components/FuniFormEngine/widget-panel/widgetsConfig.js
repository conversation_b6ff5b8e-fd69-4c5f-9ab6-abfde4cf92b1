export const containers = [
  {
    type: 'grid',
    category: 'container',
    icon: 'grid',
    cols: [],
    options: {
      name: '',
      hidden: false,
      hiddenCode: '',
      gutter: 12,
      colHeight: null, //栅格列统一高度属性，用于解决栅格列设置响应式布局浮动后被挂住的问题！！
      customClass: '' //自定义css类名
    }
  },

  {
    type: 'table',
    category: 'container',
    icon: 'table',
    rows: [],
    options: {
      name: '',
      hidden: false,
      hiddenCode: '',
      customClass: '' //自定义css类名
    }
  },

  {
    type: 'tab',
    category: 'container',
    icon: 'tab',
    displayType: 'border-card',
    tabs: [],
    options: {
      name: '',
      hidden: false,
      hiddenCode: '',
      customClass: '' //自定义css类名
    }
  },

  {
    type: 'grid-col',
    category: 'container',
    icon: 'grid-col',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      hidden: false,
      hiddenCode: '',
      span: 12,
      offset: 0,
      push: 0,
      pull: 0,
      responsive: false, //是否开启响应式布局
      md: 12,
      sm: 12,
      xs: 12,
      customClass: '' //自定义css类名
    }
  },

  {
    type: 'table-cell',
    category: 'container',
    icon: 'table-cell',
    internal: true,
    widgetList: [],
    merged: false,
    options: {
      name: '',
      cellWidth: '',
      cellHeight: '',
      colspan: 1,
      rowspan: 1,
      customClass: '' //自定义css类名
    }
  },

  {
    type: 'tab-pane',
    category: 'container',
    icon: 'tab-pane',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      hidden: false,
      hiddenCode: '',
      active: false,
      disabled: false,
      disabledCode: '',
      customClass: '' //自定义css类名
    }
  }
];

export const basicFields = [
  {
    type: 'input',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      type: 'text',
      defaultValue: '',
      defaultAction: '1',
      modelField: '',
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      clearable: true,
      showPassword: false,
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      appendButton: false,
      appendButtonDisabled: false,
      buttonIcon: 'custom-search',
      //-------------------
      listener: ''
    }
  },

  {
    type: 'textarea',
    icon: 'textarea-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      rows: 3,
      defaultValue: '',
      defaultAction: '1',
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'number',
    icon: 'number-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: 0,
      defaultAction: '1',
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      min: -100000000000,
      max: 100000000000,
      precision: 0,
      step: 1,
      controlsPosition: 'right',
      //-------------------
      listener: ''
    }
  },

  {
    type: 'radio',
    icon: 'radio-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: null,
      defaultAction: '1',
      columnWidth: '200px',
      size: '',
      displayStyle: 'inline',
      buttonStyle: false,
      border: false,
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      optionItems: [
        { label: 'radio 1', value: 1 },
        { label: 'radio 2', value: 2 },
        { label: 'radio 3', value: 3 }
      ],
      typeCode: '',
      dataSource: 'custom',
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'checkbox',
    icon: 'checkbox-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: [],
      defaultAction: '1',
      columnWidth: '200px',
      size: '',
      displayStyle: 'inline',
      buttonStyle: false,
      border: false,
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      optionItems: [
        { label: 'check 1', value: 1 },
        { label: 'check 2', value: 2 },
        { label: 'check 3', value: 3 }
      ],
      typeCode: '',
      dataSource: 'custom',
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      listener: ''
    }
  },
  /*
  {
    type: 'select',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: '',
      defaultAction: '1',
      placeholder: '',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      clearable: true,
      filterable: false,
      allowCreate: false,
      remote: false,
      automaticDropdown: false, //自动下拉
      multiple: false,
      multipleLimit: 0,
      optionItems: [
        { label: 'select 1', value: 1 },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 }
      ],
      typeCode: '',
      dataSource: 'custom',
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      listener: ''
    }
  },*/

  {
    type: 'time',
    icon: 'time-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: null,
      defaultAction: '1',
      placeholder: '',
      columnWidth: '200px',
      size: '',
      autoFullWidth: true,
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      clearable: true,
      editable: false,
      format: 'HH:mm:ss', //时间格式
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: ''
    }
  },

  {
    type: 'time-range',
    icon: 'time-range-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: null,
      defaultAction: '1',
      startPlaceholder: '',
      endPlaceholder: '',
      columnWidth: '200px',
      size: '',
      autoFullWidth: true,
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      clearable: true,
      editable: false,
      format: 'HH:mm:ss', //时间格式
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'date',
    icon: 'date-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      type: 'date',
      defaultValue: null,
      defaultAction: '1',
      placeholder: '',
      columnWidth: '200px',
      size: '',
      autoFullWidth: true,
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      clearable: true,
      editable: false,
      format: 'YYYY-MM-DD', //日期显示格式
      valueFormat: 'YYYY-MM-DD', //日期对象格式
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'date-range',
    icon: 'date-range-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      type: 'daterange',
      defaultValue: null,
      defaultAction: '1',
      startPlaceholder: '',
      endPlaceholder: '',
      columnWidth: '200px',
      size: '',
      autoFullWidth: true,
      labelWidth: null,
      labelHidden: false,
      readonly: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      clearable: true,
      editable: false,
      format: 'YYYY-MM-DD', //日期显示格式
      valueFormat: 'YYYY-MM-DD', //日期对象格式
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'switch',
    icon: 'switch-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: null,
      defaultAction: '1',
      columnWidth: '200px',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      switchWidth: 40,
      activeText: '',
      inactiveText: '',
      activeColor: null,
      inactiveColor: null,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'rate',
    icon: 'rate-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: null,
      defaultAction: '1',
      columnWidth: '200px',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      max: 5,
      lowThreshold: 2,
      highThreshold: 4,
      allowHalf: false,
      showText: false,
      showScore: false,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'color',
    icon: 'color-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: null,
      defaultAction: '1',
      columnWidth: '200px',
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: ''
    }
  },

  {
    type: 'slider',
    icon: 'slider-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      columnWidth: '200px',
      showStops: true,
      size: '',
      labelWidth: null,
      labelHidden: false,
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      required: false,
      requiredCode: '',
      requiredHint: '',
      validation: '',
      validationHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      min: 0,
      max: 100,
      step: 10,
      range: false,
      //vertical: false,
      height: null,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'static-text',
    icon: 'static-text',
    formItemFlag: false,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      columnWidth: '200px',
      hidden: false,
      hiddenCode: '',
      textContent: 'static text',
      //-------------------
      customClass: '', //自定义css类名
      //-------------------
      listener: ''
    }
  },

  {
    type: 'html-text',
    icon: 'html-text',
    formItemFlag: false,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      columnWidth: '200px',
      hidden: false,
      hiddenCode: '',
      htmlContent: '<b>html text</b>',
      //-------------------
      customClass: '', //自定义css类名
      //-------------------
      listener: ''
    }
  },

  {
    type: 'button',
    icon: 'button',
    formItemFlag: false,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      columnWidth: '200px',
      size: '',
      displayStyle: 'block',
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      type: '',
      plain: false,
      round: false,
      circle: false,
      icon: null,
      isSubmit: false,
      //-------------------
      customClass: '', //自定义css类名
      //-------------------
      listener: ''
    }
  },

  {
    type: 'divider',
    icon: 'divider',
    formItemFlag: false,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      columnWidth: '200px',
      direction: 'horizontal',
      contentPosition: 'center',
      hidden: false,
      hiddenCode: '',
      //-------------------
      customClass: '', //自定义css类名
      //-------------------
      listener: ''
    }
  }

  //
];

export const advancedFields = [
  {
    type: 'picture-upload',
    icon: 'picture-upload-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      required: false,
      requiredCode: '',
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      //-------------------
      // uploadURL: '',
      uploadTip: '',
      // urlPrefix: '',
      // withCredentials: true,
      multipleSelect: false,
      showFileList: true,
      limit: 3,
      fileMaxSize: 5, //MB
      fileTypes: ['.jpg', '.jpeg', '.png'],
      //headers: [],
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'file-upload',
    icon: 'file-upload-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      required: false,
      requiredCode: '',
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      //-------------------
      uploadURL: '',
      uploadTip: '',
      urlPrefix: '',
      // withCredentials: true,
      multipleSelect: false,
      showFileList: true,
      limit: 3,
      fileMaxSize: 5, //MB
      fileTypes: ['.doc', '.docx', '.xls', '.xlsx'],
      //headers: [],
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'rich-editor',
    icon: 'rich-editor-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      placeholder: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      contentHeight: '500px',
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      required: false,
      requiredCode: '',
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      mode: '1',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      //-------------------
      listener: ''
    }
  },

  {
    type: 'cascader',
    icon: 'cascader-field',
    formItemFlag: true,
    options: {
      listColumnWidth: null,
      listColumnAlign: 'center',
      listColumnHidden: false,
      listColumnHiddenCode: '',
      listColumnWrap: false,
      name: '',
      label: '',
      labelAlign: '',
      fieldTooltip: '',
      defaultValue: '',
      defaultAction: '1',
      placeholder: '',
      size: '',
      labelWidth: null,
      labelHidden: false,
      columnWidth: '200px',
      disabled: false,
      disabledCode: '',
      hidden: false,
      hiddenCode: '',
      clearable: true,
      filterable: false,
      multiple: false,
      checkStrictly: false, //可选择任意一级选项，默认不开启
      showAllLevels: true, //显示完整路径
      optionItems: [
        { label: 'select 1', value: 1, children: [{ label: 'child 1', value: 11 }] },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 }
      ],
      required: false,
      requiredCode: '',
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      //-------------------
      customClass: '', //自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      //-------------------
      listener: ''
    }
  }
];

export const customFields = [];

export const systemFields = [];

export function addContainerWidgetSchema(containerSchema) {
  containers.push(containerSchema);
}

export function addBasicFieldSchema(fieldSchema) {
  basicFields.push(fieldSchema);
}

export function spliceBasicFieldSchema(fieldSchema, index) {
  basicFields.splice(index, 0, fieldSchema);
}

export function addAdvancedFieldSchema(fieldSchema) {
  advancedFields.push(fieldSchema);
}

export function addCustomWidgetSchema(widgetSchema) {
  customFields.push(widgetSchema);
}

export function addSystemFieldSchema(fieldSchema) {
  systemFields.push(fieldSchema);
}
