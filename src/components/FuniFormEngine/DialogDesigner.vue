<!--
/**
 * author: vformAdmin
 * email: <EMAIL>
 * website: https://www.vform666.com
 * date: 2021.08.18
 * remark: 如果要分发VForm源码，需在本文件顶部保留此文件头信息！！
 */
-->

<template>
  <el-container class="main-container"
    :class="fullscreen?'full-height':'normal-height'">
    <el-container>
      <el-aside class="side-panel">
        <widget-panel :designer="designer"
          :scrollerHeight="scrollerHeight" />
      </el-aside>
      <el-container class="center-layout-container">
        <el-header class="toolbar-header">
          <toolbar-panel :designer="designer"
            :form-config="designer.formConfig"
            ref="toolbarRef"
            @save="onFuniSave"
            @saveConfig="onSaveConfig">
            <template v-for="(idx, slotName) in $slots"
              #[slotName]>
              <slot :name="slotName"></slot>
            </template>
          </toolbar-panel>
        </el-header>
        <el-main class="form-widget-main">
          <el-scrollbar class="container-scroll-bar"
            :style="{ height: scrollerHeight - 56 - 36 + 'px' }">
            <v-form-widget :designer="designer"
              :form-config="designer.formConfig" />
          </el-scrollbar>
        </el-main>
      </el-container>

      <el-aside>
        <setting-panel :designer="designer"
          :selected-widget="designer.selectedWidget"
          :form-config="designer.formConfig"
          :dialog-config="designer.dialogConfig"
          :parentConfig="parentConfig"
          @edit-event-handler="testEEH" />
      </el-aside>
    </el-container>
  </el-container>
</template>

<script>
import WidgetPanel from './widget-panel/index.vue';
import ToolbarPanel from './toolbar-panel/index.vue';
import SettingPanel from './setting-panel/index1.vue';
import VFormWidget from './form-widget/index.vue';
import { createDesigner } from './common/js/designer';
import {
  addWindowResizeHandler,
  deepClone,
  getQueryParam,
  getAllContainerWidgets,
  getAllFieldWidgets,
  traverseAllWidgets
} from './common/utils/util';
import { MOCK_CASE_URL, VARIANT_FORM_VERSION } from './common/utils/config';
import i18n, { changeLocale } from './common/utils/i18n';
// import axios from 'axios'
import SvgIcon from './svg-icon/index.vue';
import { Base64 } from 'js-base64';
import lowcode from './common/utils/lowcode';

export default {
  name: 'DialogDesigner',
  componentName: 'DialogDesigner',
  mixins: [i18n],
  components: {
    SvgIcon,
    WidgetPanel,
    ToolbarPanel,
    SettingPanel,
    VFormWidget
  },
  props: {
    /* 后端字段列表API */
    fieldListApi: {
      type: Object,
      default: null
    },

    /* 禁止显示的组件名称数组 */
    bannedWidgets: {
      type: Array,
      default: () => []
    },

    designerConfig: {
      type: Object,
      default: () => {
        return {};
      }
    },
    config: {
      type: String,
      default: ''
    },
    parentConfig: [Object],
    fullscreen: {
      type: Boolean,
      default: false
    }
  },
  inject: ['idCollectObject'],
  data () {
    return {
      vFormVersion: VARIANT_FORM_VERSION,
      curLangName: '',
      curLocale: '',

      vsCodeFlag: false,
      caseName: '',

      docUrl: 'https://www.vform666.com/document3.html',
      gitUrl: 'https://github.com/vform666/variant-form3-vite',
      chatUrl: 'https://www.vform666.com/pages/chat-group/',
      subScribeUrl: 'https://www.vform666.com/pages/pro/',

      scrollerHeight: '550px',

      designer: createDesigner(this, false),

      fieldList: []
    };
  },
  provide () {
    return {
      serverFieldList: this.fieldList,
      getDesignerConfig: () => {
        let config = Object.assign(
          {
            frontCodeButton: false,
            languageMenu: true, //是否显示语言切换菜单
            externalLink: true, //是否显示GitHub、文档等外部链接
            formTemplates: true, //是否显示表单模板
            eventCollapse: true, //是否显示组件事件属性折叠面板
            widgetNameReadonly: false, //禁止修改组件名称
            layoutTypeShow: false, //切换设计布局按钮组显示
            componentTreeShow: false, //组件树按钮显示
            clearDesignerButton: true, //是否显示清空设计器按钮
            previewFormButton: false, //是否显示预览表单按钮
            importJsonButton: true, //是否显示导入JSON按钮
            exportJsonButton: true, //是否显示导出JSON器按钮
            exportCodeButton: false, //是否显示导出代码按钮
            generateSFCButton: false, //是否显示生成SFC按钮
            previewFuniFormButton: true, //房联预览
            generateFuniSFCButton: true, //房联vue3单文件组件
            generateFuniSFCCodeButton: true,

            toolbarMaxWidth: 250, //设计器工具按钮栏最大宽度（单位像素）
            toolbarMinWidth: 250, //设计器工具按钮栏最小宽度（单位像素）

            presetCssCode: '', //设计器预设CSS样式代码

            resetFormJson: false, //是否在设计器初始化时将表单内容重置为空
            relevanceUrl: '/as/model/dicModeList', //用于关联表单的url
            modelFieldUrl: '/as/modelField/findAll', //模型字段列表url
            findAllTreeUrl: '/as/modelField/findAllTree',//带关联表的字段列表
            dicFieldUrl: '/as/modelField/dicFieldList', //字典类型字段列表
            tableSelectUrl: '/api/table/select', //业务表下拉选择接口
            tableDetailUrl: '/api/table/detail', //业务表详情接口
            templateAllUrl: '/as/template/findAll',//获取模板的url
            hasModel: true, //表单是否必须关联一个模型
            modelChange: true, //模型是否可选
            appCode: this.idCollectObject ? this.idCollectObject.app_code : '',
            previewTitle: '组件预览'
          },
          this.designerConfig
        );
        localStorage.setItem('designerConfig', JSON.stringify(config));
        return config;
      },
      getBannedWidgets: () => this.bannedWidgets
    };
  },
  created () {
    this.vsCodeFlag = getQueryParam('vscode') == 1;
    this.caseName = getQueryParam('case');
  },
  watch: {
    config: {
      handler (newVal) {
        if (newVal) {
          let config = JSON.parse(lowcode.decrypt(newVal));
          if (config) {
            this.designer.loadFormJson(config);
            this.designer.refreshData();
          }
        }
      },
      immediate: true
    }
  },
  mounted () {
    this.initLocale();
    this.loadCase();
    this.loadFieldListFromServer();
  },
  methods: {
    testEEH (eventName, eventParams) {
      console.log('test', eventName);
      console.log('test222222', eventParams);
    },

    showLink (configName) {
      if (this.designerConfig[configName] === undefined) {
        return true;
      }

      return !!this.designerConfig[configName];
    },

    openHome () {
      if (this.vsCodeFlag) {
        const msgObj = {
          cmd: 'openUrl',
          data: {
            url: 'https://www.vform666.com/'
          }
        };
        window.parent.postMessage(msgObj, '*');
      }
    },

    openUrl (event, url) {
      if (this.vsCodeFlag) {
        const msgObj = {
          cmd: 'openUrl',
          data: {
            url
          }
        };
        window.parent.postMessage(msgObj, '*');
      } else {
        let aDom = event.currentTarget;
        aDom.href = url;
        //window.open(url, '_blank') //直接打开新窗口，会被浏览器拦截
      }
    },

    loadCase () {
      if (!this.caseName) {
        return;
      }
    },

    initLocale () {
      this.curLocale = localStorage.getItem('v_form_locale');
      if (this.vsCodeFlag) {
        this.curLocale = this.curLocale || 'en-US';
      } else {
        this.curLocale = this.curLocale || 'zh-CN';
      }
      this.curLangName = this.i18nt('application.' + this.curLocale);
      this.changeLanguage(this.curLocale);
    },

    loadFieldListFromServer () {
      if (!this.fieldListApi) {
        return;
      }
    },

    handleLanguageChanged (command) {
      this.changeLanguage(command);
      this.curLangName = this.i18nt('application.' + command);
    },

    changeLanguage (langName) {
      changeLocale(langName);
    },

    setFormJson (formJson) {
      let modifiedFlag = false;
      if (formJson) {
        if (typeof formJson === 'string') {
          modifiedFlag = this.designer.loadFormJson(JSON.parse(formJson));
        } else if (formJson.constructor === Object) {
          modifiedFlag = this.designer.loadFormJson(formJson);
        }
      }

      if (modifiedFlag) {
        this.designer.emitHistoryChange();
      }
    },

    getFormJson () {
      return {
        widgetList: deepClone(this.designer.widgetList),
        formConfig: deepClone(this.designer.formConfig),
        dialogConfig: deepClone(this.designer.dialogConfig)
      };
    },

    clearDesigner () {
      this.$refs.toolbarRef.clearFormWidget();
    },

    /**
     * 刷新表单设计器
     */
    refreshDesigner () {
      //this.designer.loadFormJson( this.getFormJson() )  //只有第一次调用生效？？
      let fJson = this.getFormJson();
      this.designer.clearDesigner(true); //不触发历史记录变更
      this.designer.loadFormJson(fJson);
    },

    /**
     * 预览表单
     */
    previewForm () {
      this.$refs.toolbarRef.previewForm();
    },

    /**
     * 导入表单JSON
     */
    importJson () {
      this.$refs.toolbarRef.importJson();
    },

    /**
     * 导出表单JSON
     */
    exportJson () {
      this.$refs.toolbarRef.exportJson();
    },

    /**
     * 导出Vue/HTML代码
     */
    exportCode () {
      this.$refs.toolbarRef.exportCode();
    },

    /**
     * 生成SFC代码
     */
    generateSFC () {
      this.$refs.toolbarRef.generateSFC();
    },

    generateFuniCode () {
      return this.$refs.toolbarRef.generateFuniCode();
    },
    /**
     * 获取所有字段组件
     * @returns {*[]}
     */
    getFieldWidgets (widgetList = null) {
      return widgetList ? getAllFieldWidgets(widgetList) : getAllFieldWidgets(this.designer.widgetList);
    },

    /**
     * 获取所有容器组件
     * @returns {*[]}
     */
    getContainerWidgets (widgetList = null) {
      return widgetList ? getAllContainerWidgets(widgetList) : getAllContainerWidgets(this.designer.widgetList);
    },

    /**
     * 升级表单json，以补充最新的组件属性
     * @param formJson
     */
    upgradeFormJson (formJson) {
      if (!formJson.widgetList || !formJson.formConfig) {
        this.$message.error('Invalid form json!');
        return;
      }

      traverseAllWidgets(formJson.widgetList, w => {
        this.designer.upgradeWidgetConfig(w);
      });
      this.designer.upgradeFormConfig(formJson.formConfig);

      return formJson;
    },

    getWidgetRef (widgetName, showError = false) {
      return this.$refs['formRef'].getWidgetRef(widgetName, showError);
    },

    getSelectedWidgetRef () {
      return this.$refs['formRef'].getSelectedWidgetRef();
    },
    onFuniSave (e) {
      this.$emit('save', e);
    },
    onSaveConfig (e) {
      this.$emit('saveConfig', e);
    }
    //TODO: 增加更多方法！！
  }
};
</script>

<style lang="scss" scoped>
.el-container.main-container {
  background: #fff;

  :deep(aside) {
    /* 防止aside样式被外部样式覆盖！！ */
    margin: 0;
    padding: 0;
    background: inherit;
  }
}

.el-container.full-height {
  height: 100%;
  overflow-y: hidden;
}

.el-container.normal-height {
  height: 550px;
  overflow-y: hidden;
}

.el-container.center-layout-container {
  min-width: 480px;
  border-left: 2px dotted #ebeef5;
  border-right: 2px dotted #ebeef5;
}

.el-header.main-header {
  border-bottom: 2px dotted #ebeef5;
  height: 48px !important;
  line-height: 48px !important;
  min-width: 800px;
}

div.main-title {
  font-size: 18px;
  color: #242424;
  display: flex;
  align-items: center;
  justify-items: center;

  img {
    cursor: pointer;
    width: 36px;
    height: 36px;
  }

  span.bold {
    font-size: 20px;
    font-weight: bold;
    margin: 0 6px 0 6px;
  }

  span.version-span {
    font-size: 14px;
    color: #101f1c;
    margin-left: 6px;
  }
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.el-dropdown-link {
  margin-right: 12px;
  cursor: pointer;
}

div.external-link {
  display: flex;
  align-items: center;

  a {
    font-size: 13px;
    text-decoration: none;
    margin-right: 10px;
    color: #606266;
  }
}

.el-header.toolbar-header {
  font-size: 14px;
  border-bottom: 1px dotted #cccccc;
  height: 42px !important;
  //line-height: 42px !important;
}

.el-aside.side-panel {
  width: 260px !important;
  overflow-y: hidden;
}

.el-main.form-widget-main {
  padding: 0;

  position: relative;
  overflow-x: hidden;
}

.container-scroll-bar {
  :deep(.el-scrollbar__wrap),
  :deep(.el-scrollbar__view) {
    overflow-x: hidden;
  }
}
</style>
