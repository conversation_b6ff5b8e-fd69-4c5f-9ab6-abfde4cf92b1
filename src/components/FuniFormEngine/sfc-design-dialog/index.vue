<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-01 17:02:04
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-20 10:17:42
 * @FilePath: \src\components\FuniFormEngine\sfc-design-dialog\index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div :class="fullscreen?'sfc-design-dialog-full':'sfc-design-dialog-normal'">
    <el-dialog v-bind="$attrs"
      v-model="localVal"
      :fullscreen="fullscreen"
      :show-close="false"
      :class="localClass">
      <template #header>
        <div class="sfc-design-dialog-title">
          <el-text size="large">{{$attrs.title}}</el-text>
          <div style="display: flex; align-items: center">
            <el-icon style="margin-right: 20px; cursor: pointer"
              @click="fullScreenClick">
              <FullScreen />
            </el-icon>
            <el-icon style="cursor: pointer"
              @click="close">
              <Close />
            </el-icon>
          </div>
        </div>
      </template>
      <template #default="params">
        <slot name="default"
          v-bind="params || {}"></slot>
      </template>
      <template #footer="params">
        <slot name="footer"
          v-bind="params || {}"></slot>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { computed, ref, useAttrs, watch } from "vue";
defineOptions({
  name: 'SfcDesignDialog'
});

const emit = defineEmits('update:modelValue', 'fullChange');
const attrs = useAttrs();

const props = defineProps({
  modelValue: [Boolean],
  customClass: [String]
})

const localVal = ref(false);

const localClass = computed(() => {
  return props.customClass ? `sfc-design-dialog ${props.customClass}` : `sfc-design-dialog`;
})

const fullscreen = ref(false);

watch(() => props.modelValue, (newVal) => {
  localVal.value = newVal;
}, { immediate: true })

watch(() => localVal, (newVal) => {
  emit('update:modelValue', newVal);
})

const fullScreenClick = () => {
  fullscreen.value = !fullscreen.value;
  emit('fullChange', fullscreen.value);
}

const close = () => {
  localVal.value = false;
}
</script>
<style lang="scss" scoped>
.sfc-design-dialog {
  .sfc-design-dialog-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    max-height: 550px;
  }
}
.sfc-design-dialog-full {
  :deep(.el-dialog__body) {
    height: calc(100% - 120px) !important;
  }
}
</style>
