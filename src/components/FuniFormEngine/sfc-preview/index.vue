<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 17:58:53
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-24 14:29:43
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\sfc-preview\index.vue
 * @Description: 
-->
<script setup>
import { h, reactive, defineComponent, useAttrs } from "vue";
import { compile } from "../common/utils/vue3SfcCompiler";
const props = defineProps({
  sfcCode: {
    type: String,
  },
});
const ChildComponent = compile(props.sfcCode);
const attrs = useAttrs();
const render = h(ChildComponent, { ...attrs });
const getFormData = () => {
  return new Promise(resovle => {
    setTimeout(() => {
      resovle({
        field1: 'value1',
        field2: 'value2'
      })
    }, 100)
  })
}
defineExpose({ getFormData });
</script>
<template>
  <div class="preview-render">
    <render />
  </div>
</template>
<style lang="less" scoped>
.preview-render {
  overflow: auto;
}
</style>