<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-24 13:45:03
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-22 09:51:30
 * @FilePath: \src\components\FuniFormEngine\setting-panel\form-setting1.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-form :model="formConfig"
      size="small"
      label-position="left"
      label-width="120px"
      class="setting-form"
      @submit.prevent>
      <el-collapse v-model="formActiveCollapseNames"
        class="setting-collapse">
        <el-collapse-item name="1"
          :title="i18nt('designer.setting.basicSetting')">
          <el-form-item v-if="designerConfig.hasModel && idCollectObject && idCollectObject.app_id"
            :label="i18nt('designer.setting.model')">
            <funi-select v-model="formConfig.modelId"
              :action="modelAction"
              :disabled="!designerConfig.modelChange"
              :clearable="true"
              style="width: 100%" />
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formSize')">
            <el-select v-model="formConfig.size">
              <el-option v-for="item in formSizes"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelPosition')">
            <el-radio-group v-model="formConfig.labelPosition"
              class="radio-group-custom">
              <el-radio-button label="left">{{ i18nt('designer.setting.leftPosition') }}</el-radio-button>
              <el-radio-button label="top">{{ i18nt('designer.setting.topPosition') }}</el-radio-button>
              <el-radio-button label="right">{{ i18nt('designer.setting.rightPosition') }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelAlign')">
            <el-radio-group v-model="formConfig.labelAlign"
              class="radio-group-custom">
              <el-radio-button label="label-left-align">{{ i18nt('designer.setting.leftAlign') }}</el-radio-button>
              <el-radio-button label="label-center-align">{{ i18nt('designer.setting.centerAlign') }}</el-radio-button>
              <el-radio-button label="label-right-align">{{ i18nt('designer.setting.rightAlign') }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelWidth')">
            <el-input-number v-model="formConfig.labelWidth"
              :min="0"
              style="width: 100%" />
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formCss')">
            <el-button type="info"
              icon="el-icon-edit"
              plain
              round
              @click="editFormCss">{{
              i18nt('designer.setting.addCss')
            }}</el-button>
          </el-form-item>
          <!-- -->
          <!-- <el-form-item :label="i18nt('designer.setting.customClass')">
            <el-select v-model="formConfig.customClass"
              multiple
              filterable
              allow-create
              default-first-option>
              <el-option v-for="(item, idx) in cssClassList"
                :key="idx"
                :label="item"
                :value="item" />
            </el-select>
          </el-form-item> -->
          <!-- -->
          <el-form-item :label="i18nt('designer.setting.globalFunctions')">
            <el-button type="info"
              icon="el-icon-edit"
              plain
              round
              @click="editGlobalFunctions">{{
              i18nt('designer.setting.addEventHandler')
            }}</el-button>
          </el-form-item>
          <!-- <el-form-item label-width="0">
            <el-divider class="custom-divider">{{i18nt('designer.setting.formSFCSetting')}}</el-divider>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formModelName')">
            <el-input type="text"
              v-model="formConfig.modelName" />
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formRefName')">
            <el-input type="text"
              v-model="formConfig.refName" />
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formRulesName')">
            <el-input type="text"
              v-model="formConfig.rulesName" />
          </el-form-item> -->
        </el-collapse-item>
        <el-collapse-item name="4"
          title="校验规则">
          <el-form-item label-width="0px">
            <validator-items-setting v-model="formConfig.validators"
              :designer="designer" />
          </el-form-item>
        </el-collapse-item>
        <el-collapse-item v-if="showEventCollapse() && !showToolButton('frontCodeButton')"
          name="2"
          :title="i18nt('designer.setting.eventSetting')">
          <el-form-item label="表单创建"
            label-width="150px">
            <FuniCodeEditor title="表单创建"
              btnLabel="编写代码"
              prefix="form.onFormCreated:{"
              suffix="}"
              mode="javascript"
              :isReturn="false"
              v-model="formConfig.onFormCreated"
              @update:modelValue="
                val => {
                  if (val) {
                    formConfig.frontCode = undefined;
                  }
                }
              " />
          </el-form-item>
          <el-form-item label="表单挂载"
            label-width="150px">
            <FuniCodeEditor title="表单挂载"
              btnLabel="编写代码"
              prefix="form.onFormMounted:{"
              suffix="}"
              mode="javascript"
              :isReturn="false"
              v-model="formConfig.onFormMounted"
              @update:modelValue="
                val => {
                  if (val) {
                    formConfig.frontCode = undefined;
                  }
                }
              " />
          </el-form-item>
          <!-- -->
          <el-form-item label="输入数据转换"
            label-width="150px">
            <FuniCodeEditor title="编辑函数"
              tip="/*输入后端返回的数据，输出经转换后的表单数据(此时 context.formData 不可用)*/"
              btnLabel="编写代码"
              prefix="(formData)=>{"
              suffix="}"
              mode="javascript"
              :isReturn="true"
              v-model="formConfig.inputConvert"
              @update:modelValue="
                val => {
                  if (val) {
                    formConfig.frontCode = undefined;
                  }
                }
              " />
          </el-form-item>
          <el-form-item label="校验函数"
            label-width="150px">
            <FuniCodeEditor title="编辑函数"
              tip="/*返回true表示校验通过,false校验不通过,支持返回Promise resolve(true)通过,resolve(false)不通过*/"
              btnLabel="编写代码"
              prefix="()=>{"
              suffix="}"
              mode="javascript"
              :isReturn="true"
              v-model="formConfig.validateFun"
              @update:modelValue="
                val => {
                  if (val) {
                    formConfig.frontCode = undefined;
                  }
                }
              " />
          </el-form-item>
          <el-form-item label="输出数据转换"
            label-width="150px">
            <FuniCodeEditor title="编辑函数"
              tip="/*输入校验后的表单数据，输出经转换后需要传给后端的数据*/"
              btnLabel="编写代码"
              prefix="(formData)=>{"
              suffix="}"
              mode="javascript"
              :isReturn="true"
              v-model="formConfig.outputConvert"
              @update:modelValue="
                val => {
                  if (val) {
                    formConfig.frontCode = undefined;
                  }
                }
              " />
          </el-form-item>
          <el-form-item label="监听"
            label-width="150px">
            <FuniCodeEditor title="表单数据监听"
              tip="/*监听表单数据*/"
              btnLabel="编写代码"
              prefix="form.watch:{"
              suffix="}"
              mode="javascript"
              :isReturn="false"
              v-model="formConfig.watch"
              @update:modelValue="
                val => {
                  if (val) {
                    formConfig.frontCode = undefined;
                  }
                }
              " />
          </el-form-item>
          <!-- -->
          <!--
          <el-form-item label="onFormValidate">
            <el-button type="info" icon="el-icon-edit" plain round @click="editFormEventHandler('onFormValidate')">
              {{i18nt('designer.setting.addEventHandler')}}</el-button>
          </el-form-item>
          -->
        </el-collapse-item>
      </el-collapse>
    </el-form>

    <div v-if="showFormEventDialogFlag"
      class=""
      v-drag="['.drag-dialog.el-dialog', '.drag-dialog .el-dialog__header']">
      <sfc-design-dialog :title="i18nt('designer.setting.editFormEventHandler')"
        v-model="showFormEventDialogFlag"
        :show-close="true"
        custom-class="drag-dialog small-padding-dialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true">
        <el-alert type="info"
          :closable="false"
          :title="'form.' + eventParamsMap[curEventName]" />
        <code-editor :mode="'javascript'"
          :readonly="false"
          :userWorker="false"
          v-model="formEventHandlerCode"
          ref="ecEditor" />
        <el-alert type="info"
          :closable="false"
          title="}" />
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="showFormEventDialogFlag = false"> {{ i18nt('designer.hint.cancel') }}</el-button>
            <el-button type="primary"
              @click="saveFormEventHandler"> {{ i18nt('designer.hint.confirm') }}</el-button>
          </div>
        </template>
      </sfc-design-dialog>
    </div>

    <div v-if="showEditFormCssDialogFlag"
      class=""
      v-drag="['.drag-dialog.el-dialog', '.drag-dialog .el-dialog__header']">
      <sfc-design-dialog :title="i18nt('designer.setting.formCss')"
        v-model="showEditFormCssDialogFlag"
        :show-close="true"
        custom-class="drag-dialog small-padding-dialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true">
        <code-editor :mode="'css'"
          :readonly="false"
          v-model="formCssCode" />
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="showEditFormCssDialogFlag = false"> {{ i18nt('designer.hint.cancel') }}</el-button>
            <el-button type="primary"
              @click="saveFormCss"> {{ i18nt('designer.hint.confirm') }}</el-button>
          </div>
        </template>
      </sfc-design-dialog>
    </div>

    <div v-if="showEditFunctionsDialogFlag"
      class=""
      v-drag="['.drag-dialog.el-dialog', '.drag-dialog .el-dialog__header']">
      <sfc-design-dialog :title="i18nt('designer.setting.globalFunctions')"
        v-model="showEditFunctionsDialogFlag"
        :show-close="true"
        custom-class="drag-dialog small-padding-dialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true">
        <el-alert type="info"
          :closable="false"
          :title="'methods: {'" />
        <code-editor :mode="'javascript'"
          :readonly="false"
          v-model="functionsCode"
          :userWorker="false"
          ref="gfEditor" />
        <el-alert type="info"
          :closable="false"
          :title="'}'" />
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="showEditFunctionsDialogFlag = false"> {{ i18nt('designer.hint.cancel') }}</el-button>
            <el-button type="primary"
              @click="saveGlobalFunctions"> {{ i18nt('designer.hint.confirm') }}</el-button>
          </div>
        </template>
      </sfc-design-dialog>
    </div>
  </div>
</template>

<script>
import i18n from '../common/utils/i18n';
import CodeEditor from '../code-editor/index.vue';
import FuniCodeEditor from './property-editor/code-editor/code-editor.vue';
import { deepClone, insertCustomCssToHead, insertGlobalFunctionsToHtml } from '../common/utils/util';
import ValidatorItemsSetting from './validator-items-setting/validator-items-setting.vue';

export default {
  name: 'form-setting',
  mixins: [i18n],
  components: {
    CodeEditor,
    ValidatorItemsSetting,
    FuniCodeEditor
  },
  props: {
    designer: Object,
    formConfig: Object
  },
  inject: ['getDesignerConfig', 'idCollectObject', 'serverFieldList'],
  data () {
    return {
      designerConfig: this.getDesignerConfig(),

      formActiveCollapseNames: ['1', '2', '4'],

      formSizes: [
        { label: 'default', value: '' },
        { label: 'large', value: 'large' },
        //{label: 'medium', value: 'medium'},
        { label: 'small', value: 'small' }
        //{label: 'mini', value: 'mini'},
      ],

      showEditFormCssDialogFlag: false,
      formCssCode: '',
      cssClassList: [],

      showEditFunctionsDialogFlag: false,
      functionsCode: '',

      showFormEventDialogFlag: false,
      formEventHandlerCode: '',
      curEventName: '',

      eventParamsMap: {
        onFormCreated: 'onFormCreated() {',
        onFormMounted: 'onFormMounted() {',
        watch: 'watch:{'
        //'onFormValidate':     'onFormValidate() {',
      }
    };
  },
  created () {
    //导入表单JSON后需要重新加载自定义CSS样式！！！
    this.designer.handleEvent('form-json-imported', () => {
      this.formCssCode = this.formConfig.cssCode;
      insertCustomCssToHead(this.formCssCode);
      this.extractCssClass();
      this.designer.emitEvent('form-css-updated', deepClone(this.cssClassList));
    });
    if (
      this.designerConfig.hasModel &&
      this.idCollectObject &&
      this.idCollectObject.pageType !== 'blank' &&
      this.idCollectObject.model_id &&
      this.formConfig &&
      !this.formConfig.modelId
    ) {
      Object.assign(this.formConfig, { modelId: this.idCollectObject.model_id });
    }
  },
  mounted () {
    /* SettingPanel和FormWidget为兄弟组件, 在FormWidget加载formConfig时，
       此处SettingPanel可能无法获取到formConfig.cssCode, 故加个延时函数！ */
    setTimeout(() => {
      this.formCssCode = this.formConfig.cssCode;
      insertCustomCssToHead(this.formCssCode);
      this.extractCssClass();
      this.designer.emitEvent('form-css-updated', deepClone(this.cssClassList));
    }, 1200);
  },
  watch: {
    'formConfig.modelId': {
      handler (newVal) {
        if (this.designerConfig.hasModel && newVal) {
          if (this.idCollectObject && (this.idCollectObject.model_id || this.idCollectObject.pageType === 'blank')) {
            let url = this.designerConfig.findAllTreeUrl;
            let param = { model_id: newVal };
            let requestList = [
              window.$http.post(url, param),
              window.$http.post(`${this.designerConfig.relevanceUrl}`, { app_id: this.idCollectObject.app_id })
            ];
            Promise.all(requestList).then(([res, res1]) => {
              if (res && res.list && res.list.length > 0) {
                let fieldList = res.list.map(item => {
                  let option = {
                    ...item,
                    label: item.name,
                    name: item.code,
                    required: item.is_required === 1
                  };
                  if (item.config) {
                    let config = {};
                    try {
                      config = JSON.parse(item.config);
                      if (config.correlation_model && res1 && res1.list && res1.list.length > 0) {
                        let fIndex = res1.list.findIndex(item1 => item1.code === config.correlation_model);
                        if (fIndex >= 0) {
                          option.correlation_model_id = res1.list[fIndex].value;
                          option.correlation_model_name = res1.list[fIndex].label;
                        }
                      }
                    } catch { }
                  }
                  return option;
                });
                if (this.serverFieldList && this.serverFieldList.length > 0) {
                  this.serverFieldList.splice(0, this.serverFieldList.length);
                }
                this.serverFieldList.push(...fieldList);
              }
            });
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    showToolButton (configName) {
      if (this.designerConfig[configName] === undefined) {
        return true;
      }

      return !!this.designerConfig[configName];
    },
    modelAction () {
      return new Promise(resovle => {
        window.$http.post(`${this.designerConfig.relevanceUrl}`, { app_id: this.idCollectObject.app_id }).then(res => {
          resovle(res.list);
        });
      });
    },
    showEventCollapse () {
      if (this.designerConfig['eventCollapse'] === undefined) {
        return true;
      }

      return !!this.designerConfig['eventCollapse'];
    },

    editFormCss () {
      this.formCssCode = this.designer.formConfig.cssCode;
      this.showEditFormCssDialogFlag = true;
    },

    extractCssClass () {
      let regExp = /\..*{/g;
      let result = this.formCssCode.match(regExp);
      let cssNameArray = [];

      if (!!result && result.length > 0) {
        result.forEach(rItem => {
          let classArray = rItem.split(','); //切分逗号分割的多个class
          if (classArray.length > 0) {
            classArray.forEach(cItem => {
              let caItem = cItem.trim();
              if (caItem.indexOf('.', 1) !== -1) {
                //查找第二个.位置
                let newClass = caItem.substring(caItem.indexOf('.') + 1, caItem.indexOf('.', 1)); //仅截取第一、二个.号之间的class
                if (newClass) {
                  cssNameArray.push(newClass.trim());
                }
              } else if (caItem.indexOf(' ') !== -1) {
                //查找第一个空格位置
                let newClass = caItem.substring(caItem.indexOf('.') + 1, caItem.indexOf(' ')); //仅截取第一、二个.号之间的class
                if (newClass) {
                  cssNameArray.push(newClass.trim());
                }
              } else {
                if (caItem.indexOf('{') !== -1) {
                  //查找第一个{位置
                  let newClass = caItem.substring(caItem.indexOf('.') + 1, caItem.indexOf('{'));
                  cssNameArray.push(newClass.trim());
                } else {
                  let newClass = caItem.substring(caItem.indexOf('.') + 1);
                  cssNameArray.push(newClass.trim());
                }
              }
            });
          }
        });
      }

      //this.cssClassList.length = 0
      this.cssClassList.splice(0, this.cssClassList.length); //清除数组必须用splice，length=0不会响应式更新！！
      this.cssClassList = Array.from(new Set(cssNameArray)); //数组去重
    },

    saveFormCss () {
      this.extractCssClass();
      this.designer.formConfig.cssCode = this.formCssCode;
      insertCustomCssToHead(this.formCssCode);
      this.showEditFormCssDialogFlag = false;

      this.designer.emitEvent('form-css-updated', deepClone(this.cssClassList));
    },

    editGlobalFunctions () {
      this.functionsCode = this.designer.formConfig.functions;
      this.showEditFunctionsDialogFlag = true;
    },

    saveGlobalFunctions () {
      const codeHints = this.$refs.gfEditor.getEditorAnnotations();
      let syntaxErrorFlag = false;
      if (!!codeHints && codeHints.length > 0) {
        codeHints.forEach(chItem => {
          if (chItem.type === 'error') {
            syntaxErrorFlag = true;
          }
        });

        if (syntaxErrorFlag) {
          this.$message.error(this.i18nt('designer.setting.syntaxCheckWarning'));
          return;
        }
      }

      this.designer.formConfig.functions = this.functionsCode;
      insertGlobalFunctionsToHtml(this.functionsCode);
      this.showEditFunctionsDialogFlag = false;
    },

    editFormEventHandler (eventName) {
      this.curEventName = eventName;
      this.formEventHandlerCode = this.formConfig[eventName];
      this.showFormEventDialogFlag = true;
    },

    saveFormEventHandler () {
      const codeHints = this.$refs.ecEditor.getEditorAnnotations();
      let syntaxErrorFlag = false;
      if (!!codeHints && codeHints.length > 0) {
        codeHints.forEach(chItem => {
          if (chItem.type === 'error') {
            syntaxErrorFlag = true;
          }
        });

        if (syntaxErrorFlag) {
          this.$message.error(this.i18nt('designer.setting.syntaxCheckWarning'));
          return;
        }
      }

      this.formConfig[this.curEventName] = this.formEventHandlerCode;
      this.showFormEventDialogFlag = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.setting-form {
  :deep(.el-form-item__label) {
    font-size: 13px;
    //text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  :deep(.el-form-item--small.el-form-item) {
    margin-bottom: 16px;
  }

  .radio-group-custom {
    :deep(.el-radio-button__inner) {
      padding-left: 12px;
      padding-right: 12px;
    }
  }

  .custom-divider.el-divider--horizontal {
    margin: 10px 0;
  }
}

.setting-collapse {
  :deep(.el-collapse-item__content) {
    padding-bottom: 6px;
  }

  :deep(.el-collapse-item__header) {
    font-style: italic;
    font-weight: bold;
  }
}

.small-padding-dialog {
  :deep(.el-dialog__body) {
    padding: 6px 15px 12px 15px;
  }
}
</style>
