<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-06-01 18:01:02
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-19 16:26:48
 * @FilePath: \src\components\FuniFormEngine\setting-panel\dialog-designer-dialog.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <sfc-design-dialog v-model="dialogVisible"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
      title="弹窗设计"
      width="80%"
      @fullChange="onFullChange">
      <dialog-designer :config="config"
        :designerConfig="designerConfig"
        :parentConfig="designer"
        :fullscreen="fullscreen"
        @saveConfig="handleSave" />
    </sfc-design-dialog>
  </div>
</template>
<script setup>
import { ElNotification } from 'element-plus';
import { nextTick, reactive, ref, watchEffect, inject } from 'vue';
import DialogDesigner from '../DialogDesigner.vue';
const emit = defineEmits(['changeStatus', 'save'])
const props = defineProps({
  designer: Object,
})
const getDesignerConfig = inject('getDesignerConfig');
const parentConfig = getDesignerConfig();
const { relevanceUrl, tableSelectUrl, tableDetailUrl, hasModel, defaultModelId } = parentConfig;
const designerConfig = {
  frontCodeButton: parentConfig.frontCodeButton,
  toolbarMaxWidth: 175,
  toolbarMinWidth: 175, relevanceUrl, tableSelectUrl, tableDetailUrl, hasModel, defaultModelId
};
const dialogVisible = ref(false);
const fullscreen = ref(false);
let config = reactive(null)

watchEffect(() => {
  emit('changeStatus', dialogVisible.value);
})

const show = (config1 = null) => {
  config = config1;
  dialogVisible.value = true;
}

const handleSave = (e) => {
  if (!e.isClose) {
    emit('save', e);
    dialogVisible.value = false;
  }
}

const onFullChange = (e) => {
  fullscreen.value = e;
}

defineExpose({ show });
</script>
<style lang='scss' scoped>
</style>