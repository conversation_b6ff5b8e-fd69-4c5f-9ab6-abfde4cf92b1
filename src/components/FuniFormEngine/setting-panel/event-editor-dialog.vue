<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-12-01 09:45:57
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-12 17:09:23
 * @FilePath: \src\components\FuniFormEngine\setting-panel\event-editor-dialog.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div>
    <el-link :type="modelValue ? 'primary' : 'default'"
      icon="Edit"
      @click="handleEdit"><span v-if="isBtnLabel">编辑</span></el-link>
    <FuniEventEditor ref="eventEditorRef"
      :modalList="modalList"
      :formList="formList"
      :componentList="componentList"
      :variables="variables"
      :pageList="pageList"
      @save="handleSave" />
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { computed, onMounted, reactive, ref, watchEffect, inject, getCurrentInstance } from 'vue';
import { findAllWidget } from '../common/utils/util.js';

const emit = defineEmits('change', 'update:modelValue');

const props = defineProps({
  modelValue: [Object],
  parentConfig: [Object],
  designer: [Object],
  //事件对象
  logic: {
    type: Object
  },
  variables: {
    type: Array,
    default: () => {
      return []
    }
  },
  isBtnLabel: {
    type: Boolean,
    default: true
  }
});
const instance = getCurrentInstance();
const eventEditorRef = ref();

// ID集合
const idCollectObject = inject('idCollectObject', {});

const pages = reactive([]);

onMounted(() => {
  if (idCollectObject && idCollectObject.app_id) {
    let params = { app_id: idCollectObject.app_id };
    instance.proxy.$lowCodeRequest.postPageFindAllAsync(params)
      .then(res => {
        let list = res.list;
        if (list && list.length > 0) {
          list.forEach(item => {
            pages.push({ name: item.name, path: `/app/${idCollectObject.app_code}/${item.id}` });
          })
        }
      })
  }
})

const modalList = computed(() => {
  let modals = [];
  if (props.parentConfig && props.parentConfig.formConfig && props.parentConfig.formConfig.dialogs && props.parentConfig.formConfig.dialogs.length > 0) {
    modals = props.parentConfig.formConfig.dialogs.map(m => {
      return {
        label: m.name,
        value: m.name
      }
    })
  } else if (!props.parentConfig) {
    if (props.designer && props.designer.formConfig && props.designer.formConfig.dialogs && props.designer.formConfig.dialogs.length > 0) {
      modals = props.designer.formConfig.dialogs.map(m => {
        return {
          label: m.name,
          value: m.name
        }
      })
    }
  }
  return modals;
})

const formList = computed(() => {
  let forms = [];
  if (props.designer && props.designer.formConfig) {
    forms = [{ label: '表单', value: props.designer.formConfig.refName }];
  }
  return forms;
})

const componentList = computed(() => {
  let components = [];
  if (props.designer && props.designer.widgetList && props.designer.widgetList.length > 0) {
    components = findAllWidget(props.designer.widgetList).filter(item => ['funi-show-curd', 'sfc-draggable-curd', 'sfc-ol-map'].includes(item.type))
      .map(item => {
        let methods = [];
        if (item.type === 'funi-show-curd') {
          methods.push({ key: '刷新', value: 'refresh' }, { key: '添加列表数据', value: 'pushList' }, { key: '删除', value: 'delete' }, { key: '重置列表', value: 'reloadList' });
        } else if (item.type === 'sfc-draggable-curd') {
          methods.push({ key: '添加', value: 'push' }, { key: '校验', value: 'validate' }, { key: '添加列表数据', value: 'pushList' }, { key: '删除', value: 'delete' });
        } else if (item.type === 'sfc-ol-map') {
          methods.push({ key: '获取地图实例', value: 'getMap' }, { key: '获取地图view', value: 'getView' }, { key: '设置地图中心点', value: 'setCenter' }, { key: '设置地图zoom', value: 'setZoom' }, { key: '图斑绘制', value: 'draw' }, { key: '清除之前绘制', value: 'drawClear' },
            { key: '拆分图斑', value: 'drawSplit' }, { key: '撤销', value: 'drawRevoke' }, { key: '自适应范围', value: 'mapFit' }, { key: '获取当前视窗经纬度', value: 'getViewBox' },
            { key: '添加边界图层', value: 'addBoundAreaLayer' }, { key: '高亮要素', value: 'highLightFeatures' }, { key: '切换图层', value: 'changeLayer' }, { key: '通过sql查询高亮', value: 'highLightByFilter' },
            { key: '高亮图层清空', value: 'highLightLayerClear' }, { key: '关闭弹框', value: 'closeModal' }, { key: '添加图层', value: 'addLayer' }, { key: '移除指定图层', value: 'removeLayer' },
            { key: 'wms图层过滤', value: 'filterWms' }, { key: '同步地图', value: 'setView' }
          );
        }
        return {
          key: item.options.label,
          value: item.options.name,
          methods
        }
      });
  }
  return components;
})

const pageList = computed(() => {
  return pages;
})

const handleEdit = () => {
  eventEditorRef.value.show({ code: 'onClick', name: '点击事件', logic: props.logic });
};

const handleSave = async getEvent => {
  const eventsConfig = await getEvent;
  emit('change', eventsConfig);
  emit('update:modelValue', eventsConfig);
  emit('change-logic', getEvent);
  eventEditorRef.value.hide();
};
</script>
<style lang="scss" scoped></style>
