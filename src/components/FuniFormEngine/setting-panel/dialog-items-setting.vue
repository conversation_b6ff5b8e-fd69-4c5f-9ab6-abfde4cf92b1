<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-06-01 17:17:31
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-19 14:59:54
 * @FilePath: \src\components\FuniFormEngine\setting-panel\dialog-items-setting.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div style="width:100%;">
    <el-row>
      <el-table size="small"
        row-key="id"
        :data="dialogList">
        <el-table-column prop="name"
          :show-overflow-tooltip="true"
          label="弹窗名称" />
        <el-table-column label="操作">
          <template #default="scope">

          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #header>
            <el-link type="primary"
              icon="Plus"
              @click="handleAdd">添加</el-link>
          </template>
          <template #default="scope">
            <el-button link
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
              icon="EditPen" />
            <el-button link
              type="primary"
              size="small"
              @click="handleDelete(scope.$index )"
              icon="CircleClose" />
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <dialog-designer-dialog ref="dialogDesignerRef"
      :designer="designer"
      @changeStatus="e=>$emit('changeStatus',e)"
      @save="handleSave" />
  </div>
</template>
<script setup>
import { getCurrentInstance, reactive, ref, watch, watchEffect } from 'vue';
import DialogDesignerDialog from './dialog-designer-dialog.vue';
const emit = defineEmits(['update:modelValue', 'changeStatus']);

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => { return [] }
  },
  designer: Object,
})

const instance = getCurrentInstance();

const dialogDesignerRef = ref();

let dialogList = reactive([])

// watch(props.modelValue, (newVal) => {
//   dialogList = newVal || [];
// })

watchEffect(() => {
  dialogList = props.modelValue || [];
})

const handleAdd = () => {
  dialogDesignerRef.value.show();
}

const handleEdit = (row) => {
  dialogDesignerRef.value.show(row.dialogConfig.config);
}

const handleDelete = (delIndex) => {
  instance.proxy.$confirm(`确定删除弹窗:${dialogList[delIndex].name}吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    dialogList.splice(delIndex, 1);
    emit('update:modelValue', dialogList);
  }).catch(() => {
  });
}

const handleSave = e => {
  let config = JSON.parse(e.configJson);
  let id = config.dialogConfig.id;
  let name = config.dialogConfig.name;
  let fIndex = dialogList.findIndex(item => item.id === id);
  if (fIndex >= 0) {
    dialogList.splice(fIndex, 1);
  }
  dialogList.push({ id, name, dialogConfig: e });
  emit('update:modelValue', dialogList);
}
</script>
<style lang='scss' scoped>
</style>