<template>
  <div>
    <el-form :model="dialogConfig"
      size="small"
      label-position="left"
      label-width="100px"
      class="setting-form"
      @submit.prevent>
      <el-collapse v-model="dialogActiveCollapseNames"
        class="setting-collapse">
        <el-collapse-item name="1"
          title="基本属性">
          <el-form-item prop="name"
            :rules="nameRequiredRule">
            <template #label>
              <span>唯一名称
                <el-tooltip effect="light"
                  content="唯一名称">
                  <svg-icon icon-class="el-info" /></el-tooltip>
              </span>
            </template>
            <el-input v-model="dialogConfig.name" />
          </el-form-item>
          <el-form-item label="弹窗标题"
            prop="title">
            <el-input v-model="dialogConfig.title" />
          </el-form-item>
          <el-form-item label="弹窗宽度(px)"
            prop="width">
            <el-input-number :min="0"
              v-model="dialogConfig.width" />
          </el-form-item>
          <el-form-item label="显示关闭按钮"
            prop="showClose">
            <el-switch v-model="dialogConfig.showClose" />
          </el-form-item>
          <buttons-editor v-model="dialogConfig.buttons"
            :optionModel="dialogConfig"
            :designer="designer"
            :parentConfig="parentConfig"
            :actionTypeOptions="actionTypeOptions" />
        </el-collapse-item>
        <el-collapse-item name="2"
          title="事件属性">
          <el-form-item label="确定事件"
            label-width="150px"
            prop="listener">
            <!-- <el-button type="info"
              icon="el-icon-edit"
              plain
              round
              @click="editEventHandler()">
              编辑代码</el-button> -->
            <buttons-event-dialog eventName="点确定触发(入参:formData)"
              eventCode="ok"
              argName="formData"
              :needWrap="false"
              :designer="parentConfig"
              :logic="dialogConfig.listenerLogic"
              @change="handleChange"
              @change-logic="handleChangeLogic" />
          </el-form-item>
          <el-form-item label="关闭弹窗事件"
            label-width="150px"
            prop="close">
            <!-- <el-button type="info"
              icon="el-icon-edit"
              plain
              round
              @click="editEventHandler()">
              编辑代码</el-button> -->
            <buttons-event-dialog eventName="关闭弹窗触发(入参:formData)"
              eventCode="dialogClose"
              argName="formData"
              :needWrap="false"
              :designer="parentConfig"
              :logic="dialogConfig.closeLogic"
              @change="handleCloseChange"
              @change-logic="handleChangeCloseLogic" />
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
    </el-form>
    <div v-if="showDialogEventDialogFlag"
      class=""
      v-drag="['.drag-dialog.el-dialog', '.drag-dialog .el-dialog__header']">
      <el-dialog title="编辑事件代码"
        v-model="showDialogEventDialogFlag"
        :show-close="true"
        custom-class="drag-dialog small-padding-dialog"
        append-to-body
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :destroy-on-close="true">
        <el-alert type="info"
          :closable="false"
          title="{" />
        <code-editor :mode="'javascript'"
          :readonly="false"
          :userWorker="false"
          v-model="listenerCode"
          ref="ecEditor" />
        <el-alert type="info"
          :closable="false"
          title="}" />
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="showDialogEventDialogFlag = false">
              取消</el-button>
            <el-button type="primary"
              @click="saveEventHandler">
              确定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import ButtonsEditor from './property-editor/buttons-editor/buttons-editor.vue';
import CodeEditor from '../code-editor/index.vue'
import { generateId } from "../common/utils/util"
import ButtonsEventDialog from './property-editor/buttons-editor/buttons-event-dialog.vue'
export default {
  name: "dialog-setting",
  components: {
    ButtonsEditor,
    ButtonsEventDialog,
    CodeEditor
  },
  props: {
    designer: Object,
    dialogConfig: Object,
    parentConfig: Object
  },
  inject: ['getDesignerConfig'],
  data () {
    return {
      nameRequiredRule: [{ required: true, message: '名称是必填' }],
      designerConfig: this.getDesignerConfig(),
      dialogActiveCollapseNames: ['1', '2'],
      showDialogEventDialogFlag: false,
      listenerCode: this.dialogConfig.listener,
      actionTypeOptions: [
        {
          label: '取消',
          value: 'cancel',
          defaultProps: {
            componentType: 'button',
            type: 'default',
            size: 'default',
            showIcon: 'txtIcon',
            icon: 'ep:close',
            iconPosition: 'left'
          },
          getDefaultAttrs (options) {
            return `context.emit('cancel');
            context.refs.${options.name}.close();`;
          }
        },
        {
          label: '确定',
          value: 'ok',
          defaultProps: {
            componentType: 'button',
            type: 'primary',
            size: 'default',
            showIcon: 'txtIcon',
            icon: 'ep:check',
            iconPosition: 'left'
          },
          getDefaultAttrs (options) {
            return `submitForm().then(res=>{
                if(res.valid){
                  context.emit('ok',res.data);
                } else if(res.errors){
                  let messages=[];
                  for(let err in res.errors){
                    if(res.errors[err]&&res.errors[err]&&res.errors[err].length>0){
                      messages.push(...res.errors[err].map(item=>item.message));
                    }
                  }
                  if(messages.length>0){
                    context.notify(messages[0]);
                  } else {
                    context.notify('请更正数据');
                  }
                } else {
                  context.notify('请更正数据');
                }
              })`;
          }
        },
        {
          label: '打印',
          value: 'print',
          defaultProps: {
            componentType: 'button',
            type: 'primary',
            size: 'default',
            showIcon: 'txtIcon',
            icon: 'ep:printer',
            iconPosition: 'left'
          },
          getDefaultAttrs (options) {
            return `print()`;
          }
        },
        {
          label: '自定义',
          value: 'custom',
          defaultProps: {
            componentType: 'button',
            type: 'primary',
            size: 'default',
            showIcon: 'noIcon',
          },
          getDefaultAttrs (options) {
            return ``;
          }
        }
      ]
    }
  },
  created () {
    let tempId = generateId();
    if (!this.dialogConfig.id) {
      this.dialogConfig.id = `${tempId}`;
    }
    if (!this.dialogConfig.name) {
      this.dialogConfig.name = `dialog${tempId}`;
    }
    if (this.dialogConfig.showClose === null || this.dialogConfig.showClose === undefined) {
      this.dialogConfig.showClose = true;
    }
    if (this.dialogConfig.buttons === null || this.dialogConfig.buttons === undefined) {
      this.dialogConfig.buttons = [];
    }
  },
  methods: {
    editEventHandler () {
      this.showDialogEventDialogFlag = true;
    },
    saveEventHandler () {
      const listener = this.listenerCode;
      Object.assign(this.dialogConfig, { listener })
      this.showDialogEventDialogFlag = false;
    },
    handleChange (code) {
      const okCode = `${code}`;
      let listenerCode = `${code}${this.dialogConfig.closeCode ? `,${this.dialogConfig.closeCode}` : ''}`
      const listener = listenerCode;
      Object.assign(this.dialogConfig, { okCode, listener })
    },
    handleChangeLogic (logic) {
      const listenerLogic = logic;
      Object.assign(this.dialogConfig, { listenerLogic })
    },
    handleCloseChange (code) {
      const closeCode = `${code}`;
      let listenerCode = `${this.dialogConfig.okCode ? `${this.dialogConfig.okCode},` : ''}${code}`
      const listener = listenerCode;
      Object.assign(this.dialogConfig, { closeCode, listener })
    },
    handleChangeCloseLogic (logic) {
      const closeLogic = logic;
      Object.assign(this.dialogConfig, { closeLogic })
    }
  }
}
</script>
<style lang='scss' scoped>
.setting-collapse {
  :deep(.el-collapse-item__content) {
    padding-bottom: 6px;
  }

  :deep(.el-collapse-item__header) {
    font-style: italic;
    font-weight: bold;
  }
}
</style>
