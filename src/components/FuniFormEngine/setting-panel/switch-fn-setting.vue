<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-15 16:04:26
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-05-14 11:06:00
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\switch-fn-setting.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="switch-fn-setting">
    <el-switch v-model="localDisabled" />
    <FuniVariableSetter class="setting-variable"
      v-model="localCodes"
      @change="handleChange"
      :variables="variables" />
  </div>
</template>
<script setup>
import { computed, reactive, ref } from 'vue';
import {
  isNotNullExpression,
} from '../common/utils/util';

const emit = defineEmits(['update:modelValue', 'update:codesValue']);

const props = defineProps({
  modelValue: [Boolean],
  codesValue: [String],
  variables: [Array]
})

const localDisabled = computed({
  get: () => {
    return props.modelValue
  },
  set: (newVal) => {
    emit('update:modelValue', newVal);
  }
})

const localCodes = computed(() => {
  return props.codesValue;
})

const handleChange = (e) => {
  let newVal = e;
  if (e && !isNotNullExpression(e)) {
    newVal = '';
  }
  emit('update:codesValue', newVal);
}

</script>
<style lang='scss' scoped>
.switch-fn-setting {
  display: flex;
  flex-direction: row;
  .setting-variable {
    margin: 0px 4px;
    flex-grow: 1;
  }
}
</style>