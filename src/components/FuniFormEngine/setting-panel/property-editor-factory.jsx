/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-07-17 16:37:31
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-18 19:19:34
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor-factory.jsx
 * @Description:
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved.
 */
import { translate } from '../common/utils/i18n';
import { randomString } from '../common/utils/util';
import emitter from '../common/utils/emitter';
import OptionItemsEditor from '../setting-panel/property-editor/optionItems-editor.vue';
import ColumnEditor from '../setting-panel/property-editor/column-editor/column-editor.vue';
import GanttColumnEditor from '../setting-panel/property-editor/gantt-column-editor/gantt-column-editor.vue';
import ButtonsEditor from '../setting-panel/property-editor/buttons-editor/buttons-editor.vue';
import DatasourceEditor from '../setting-panel/property-editor/datasource-editor/datasource-editor.vue';
import UserOrgScopeEditor from '../setting-panel/property-editor/user-org-scope-editor/user-org-scope-editor.vue';
import SearchConfigEditor from '../setting-panel/property-editor/search-config-editor/search-config-editor.vue';
import CodeEditor from '../setting-panel/property-editor/code-editor/code-editor.vue';
import fieldVariablesMixins from '../setting-panel/property-editor/fieldVariablesMixins';
import CheckLimitEditor from './property-editor/checkLimit-editor/checkLimit-editor.vue';
import ItemsDataEditor from './property-editor/items-data-editor/items-data-editor.vue';
import BoolVarEditor from './property-editor/bool-var-editor.vue';
import { ElMessageBox } from 'element-plus';

export const createInputTextEditor = function (propName, propLabelKey) {
  return {
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-input type="text" v-model={this.optionModel[propName]} />
        </el-form-item>
      );
    }
  };
};

export const createInputTextEditorHasModel = function (propName, propLabelKey) {
  return {
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    data() {
      return {
        designerConfig: this.getDesignerConfig()
      };
    },
    render(h) {
      const { designerConfig, idCollectObject } = this;
      if (!(designerConfig.hasModel && idCollectObject && idCollectObject.app_id)) {
        //需要选模型就不填url
        return (
          <el-form-item label={translate(propLabelKey)}>
            <el-input type="text" v-model={this.optionModel[propName]} />
          </el-form-item>
        );
      } else {
        this.optionModel[propName] = '';
      }
    }
  };
};

export const createFuniVariableSetterEditor = function (propName, propLabelKey) {
  return {
    mixins: [fieldVariablesMixins],
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    data() {
      return {
        designerConfig: this.getDesignerConfig(),
        variables: []
      };
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <FuniVariableSetter v-model={this.optionModel[propName]} variables={this.variables} />
        </el-form-item>
      );
    }
  };
};

export const createInputArrayEditor = function (propName, propLabelKey, length = 2) {
  return {
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    computed: {
      localVal: {
        get() {
          const arrayVal = this.optionModel[propName];
          return arrayVal.join(',');
        },
        set(val) {
          if (val) {
            val = val.replaceAll('，', ',');
            let arrayVal = val
              .split(',')
              .map(item => Number(item))
              .filter(item => !isNaN(item));
            if (arrayVal.length == length) {
              this.optionModel[propName] = arrayVal;
            }
          } else {
            this.optionModel[propName] = [];
          }
        }
      }
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-input type="text" v-model={this.localVal} />
        </el-form-item>
      );
    }
  };
};

export const createCurdUrlEditor = function (propName, propLabelKey) {
  return {
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    data() {
      return {
        designerConfig: this.getDesignerConfig()
      };
    },
    render(h) {
      const { designerConfig, idCollectObject } = this;
      if (!(designerConfig.hasModel && idCollectObject && idCollectObject.app_id)) {
        //需要选模型就不填url
        return (
          <el-form-item label={translate(propLabelKey)}>
            <el-input type="text" v-model={this.optionModel[propName]} />
          </el-form-item>
        );
      }
    }
  };
};

export const createInputTextChangeEditor = function (propName, propLabelKey, change) {
  return {
    props: {
      optionModel: Object
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-input type="text" v-model={this.optionModel[propName]} onChange={change(this.optionModel)} />
        </el-form-item>
      );
    }
  };
};

export const createInputNumberEditor = function (propName, propLabelKey, option = {}) {
  return {
    props: {
      optionModel: Object
    },
    methods: {
      updateValue(newValue) {
        if (newValue === undefined || newValue === null || isNaN(newValue)) {
          this.optionModel[propName] = null;
        } else {
          this.optionModel[propName] = Number(newValue);
        }
      }
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-input-number
            type="text"
            min={option.min}
            max={option.max}
            v-model={this.optionModel[propName]}
            onChange={this.updateValue}
            style="width: 100%"
          />
        </el-form-item>
      );
    }
  };
};

export const createBooleanEditor = function (propName, propLabelKey) {
  return {
    props: {
      optionModel: Object
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-switch v-model={this.optionModel[propName]} />
        </el-form-item>
      );
    }
  };
};

export const createBooleanVarEditor = function (propName, propCodeName, propLabelKey) {
  return {
    components: {
      BoolVarEditor
    },
    props: {
      optionModel: Object,
      designer: Object,
      selectedWidget: Object
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <BoolVarEditor
            optionModel={this.optionModel}
            selectedWidget={this.selectedWidget}
            v-model={this.optionModel[propName]}
            v-model:codesValue={this.optionModel[propCodeName]}
          />
        </el-form-item>
      );
    }
  };
};

export const createCheckboxGroupEditor = function (propName, propLabelKey, configs) {
  return {
    props: {
      optionModel: Object
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-checkbox-group v-model={this.optionModel[propName]}>
            {configs.optionItems.map(item => {
              return <el-checkbox label={item.value}>{item.label}</el-checkbox>;
            })}
          </el-checkbox-group>
        </el-form-item>
      );
    }
  };
};

export const createRadioGroupEditor = function (propName, propLabelKey, configs) {
  return {
    props: {
      optionModel: Object
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-radio-group v-model={this.optionModel[propName]}>
            {configs.optionItems.map(item => {
              return <el-radio label={item.value}>{item.label}</el-radio>;
            })}
          </el-radio-group>
        </el-form-item>
      );
    }
  };
};

export const createRadioButtonGroupEditor = function (propName, propLabelKey, configs) {
  return {
    props: {
      optionModel: Object
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-radio-group v-model={this.optionModel[propName]}>
            {configs.optionItems.map(item => {
              return <el-radio-button label={item.value}>{item.label}</el-radio-button>;
            })}
          </el-radio-group>
        </el-form-item>
      );
    }
  };
};

export const createSelectEditor = function (propName, propLabelKey, configs) {
  return {
    props: {
      optionModel: Object
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-select v-model={this.optionModel[propName]} multiple={configs.multiple}>
            {configs.optionItems.map(item => {
              return <el-option label={item.label} value={item.value} />;
            })}
          </el-select>
        </el-form-item>
      );
    }
  };
};

export const createCheckLimitSelectEditor = function (propName, propLabelKey, configs) {
  return {
    components: {
      CheckLimitEditor
    },
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    render(h) {
      return (
        <el-row>
          <el-form-item label={translate(propLabelKey)} style="width:100%;">
            <el-select v-model={this.optionModel[propName]}>
              {configs.optionItems.map(item => {
                return <el-option label={item.label} value={item.value} />;
              })}
            </el-select>
          </el-form-item>
          {this.optionModel[propName] === 'selection' ? (
            <CheckLimitEditor
              designer={this.designer}
              selectedWidget={this.selectedWidget}
              optionModel={this.optionModel}
            />
          ) : null}
        </el-row>
      );
    }
  };
};

export const createEventHandlerEditor = function (eventPropName, eventParams) {
  return {
    props: {
      optionModel: Object
    },
    mixins: [emitter],
    methods: {
      editEventHandler() {
        this.dispatch('SettingPanel', 'editEventHandler', [eventPropName, [...eventParams]]);
      }
    },
    render(h) {
      return (
        <el-form-item label={eventPropName} label-width="150px">
          <el-button type="info" icon="el-icon-edit" plain round onClick={this.editEventHandler}>
            {translate('designer.setting.addEventHandler')}
          </el-button>
        </el-form-item>
      );
    }
  };
};

export const createCodeEditor = function (
  propName,
  propLabelKey,
  codePropName,
  option = { title: '代码编辑器', prefix: '{', suffix: '}' }
) {
  return {
    props: {
      optionModel: Object
    },
    components: {
      CodeEditor
    },
    methods: {
      handleChange(e) {
        this.optionModel[codePropName] = `${option.prefix}${e}${option.suffix}`;
      }
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <code-editor
            title={option.title}
            prefix={option.prefix}
            suffix={option.suffix}
            mode={option.mode ?? 'javascript'}
            v-model={this.optionModel[propName]}
            onChange={this.handleChange}
          ></code-editor>
        </el-form-item>
      );
    }
  };
};

export const createOptionsEditor = function (propName = 'optionItems') {
  return {
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    components: {
      OptionItemsEditor
    },
    render(h) {
      return (
        <optionItems-editor
          designer={this.designer}
          selectedWidget={this.selectedWidget}
          optionModel={this.optionModel}
          propName={propName}
        ></optionItems-editor>
      );
    }
  };
};

export const createColumnEditor = function (mode) {
  return {
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    components: {
      ColumnEditor
    },
    render() {
      return (
        <column-editor
          mode={mode}
          designer={this.designer}
          selectedWidget={this.selectedWidget}
          optionModel={this.optionModel}
          isCurd={true}
          v-model={this.optionModel['columns']}
        ></column-editor>
      );
    }
  };
};

export const createButtonsEditor = function (actionTypeOptions = [], propName = 'buttons') {
  return {
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    components: {
      ButtonsEditor
    },
    render() {
      return (
        <buttons-editor
          designer={this.designer}
          selectedWidget={this.selectedWidget}
          optionModel={this.optionModel}
          actionTypeOptions={actionTypeOptions}
          propName={propName}
          isCurdActions={propName === 'actions'}
          isCurdButtons={propName === 'buttons'}
          v-model={this.optionModel[propName]}
        ></buttons-editor>
      );
    }
  };
};

export const createModelOptionEditor = function () {
  return {
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    components: {
      OptionItemsEditor
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    data() {
      return {
        options: []
      };
    },
    render(h) {
      if (!this.optionModel.modelOption) {
        this.optionModel.modelOption = {};
      }
      return (
        <el-form-item label="关联表单">
          <funi-select
            clearable="true"
            v-model={this.optionModel.modelOption['modelId']}
            onChange={this.handleChange.bind(this)}
            options={this.options}
          />
        </el-form-item>
        // <optionItems-editor designer={this.designer} selectedWidget={this.selectedWidget} optionModel={this.optionModel}></optionItems-editor>
      );
    },
    mounted() {
      let config = this.getDesignerConfig();
      let appId = '';
      if (this.idCollectObject) {
        appId = this.idCollectObject.app_id;
      }
      window.$http.post(`${config.relevanceUrl}`, { app_id: appId }).then(res => {
        this.options = res.list;
      });
    },
    methods: {
      handleChange(val) {
        this.optionModel.url = '';
        this.optionModel.typeCode = '';
        this.optionModel.optionItems = [];
        let fIndex = this.options.findIndex(o => o.value === val);
        if (fIndex >= 0) {
          this.optionModel.modelOption.modelName = this.options[fIndex].label;
        }
      }
    }
  };
};

export const createDatasourceEditor = function (propName = 'requestOtherParam') {
  return {
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
      isCurd: {
        type: Boolean,
        default: false
      }
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    data() {
      return {
        designerConfig: this.getDesignerConfig(),
        oldVal: { ...(this.optionModel[propName] || {}) },
        oldWidgetId: undefined
      };
    },
    methods: {
      onExtract(columns) {
        if (columns && columns.length) {
          let list = columns.map((col, index) => {
            return {
              serialNumber: index + 1,
              prop: col
            };
          });
          Object.assign(this.optionModel, { columns: list });
        }
      },
      onExtractColumn(columns) {
        if (columns && columns.length) {
          let list = columns.map((col, index) => {
            return {
              serialNumber: index + 1,
              prop: col.prop,
              label: col.label
            };
          });
          Object.assign(this.optionModel, { columns: list });
        }
      },
      onChange(newVal) {
        if (newVal && newVal.data) {
          let app_code = '';
          if (this.idCollectObject) {
            app_code = this.idCollectObject.app_code;
          }
          let api_url = '';
          if (newVal && newVal.type === 'api') {
            // api
            api_url = newVal.data.api_url;
          }
          let url = newVal ? (this.designerConfig.hasModel ? `/as/${app_code}/${newVal.type}/list` : api_url) : '';
          Object.assign(this.optionModel, { url });
          let isChange = true;
          let oldVal = this.oldVal;
          if (
            oldVal &&
            newVal.type === oldVal.type &&
            newVal.data &&
            this.optionModel.columns &&
            this.optionModel.columns.length > 0
          ) {
            isChange = false;
          }
          if (newVal.type === 'model' && newVal && newVal.data && newVal.data.model_id) {
            let updateColumn = () => {
              let model_id =
                newVal &&
                newVal.dataType === 'many_to_many' &&
                newVal.modelId === newVal.middleModelId &&
                newVal.correlationModelId
                  ? newVal.correlationModelId
                  : newVal.data.model_id;
              window.$http.post(this.designerConfig.modelFieldUrl, { model_id }).then(res => {
                let columns = [];
                if (res && res.list && res.list.length > 0) {
                  columns = res.list.map((item, index) => {
                    let col = {
                      serialNumber: index + 1,
                      label: item.name,
                      prop: item.code
                    };
                    if (this.selectedWidget.type === 'sfc-draggable-curd') {
                      col.config = item.config;
                      col.dataType = item.data_type;
                    }
                    return col;
                  });
                }
                Object.assign(this.optionModel, { columns });
              });
            };
            if (isChange) {
              updateColumn();
            } else if (
              newVal.data.model_id !== oldVal.data.model_id &&
              (!this.oldWidgetId || this.oldWidgetId === this.selectedWidget?.id)
            ) {
              ElMessageBox.confirm('已存在列信息是否覆盖?', '提示', {
                confirmButtonText: '覆盖',
                cancelButtonText: '取消',
                type: 'warning'
              })
                .then(() => {
                  updateColumn();
                })
                .catch(() => {});
            }
          }
          this.oldWidgetId = this.selectedWidget?.id;
          this.oldVal = { ...(newVal || {}) };
        }
      }
    },
    render() {
      return (
        <DatasourceEditor
          designer={this.designer}
          selectedWidget={this.selectedWidget}
          optionModel={this.optionModel}
          isCurd={this.isCurd}
          onChange={this.onChange}
          onExtract={this.onExtract}
          onExtractColumn={this.onExtractColumn}
          v-model={this.optionModel[propName]}
        ></DatasourceEditor>
      );
    }
  };
};

export const createSimpleDatasourceEditor = function (propName = 'requestOtherParam') {
  return {
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
      isCurd: {
        type: Boolean,
        default: false
      }
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    data() {
      return {
        designerConfig: this.getDesignerConfig()
      };
    },
    methods: {
      onChange(newVal) {
        if (newVal && newVal.data) {
          let app_code = '';
          if (this.idCollectObject) {
            app_code = this.idCollectObject.app_code;
          }
          let url = newVal ? `/as/${app_code}/${newVal.type}/list` : '';
          Object.assign(this.optionModel, { url });
        }
      }
    },
    render() {
      return (
        <DatasourceEditor
          designer={this.designer}
          selectedWidget={this.selectedWidget}
          optionModel={this.optionModel}
          onChange={this.onChange}
          isSimple={true}
          isCurd={this.isCurd}
          v-model={this.optionModel[propName]}
        ></DatasourceEditor>
      );
    }
  };
};

export const createSelectModelEditor = function (propName, propLabelKey) {
  return {
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    data() {
      return {
        designerConfig: this.getDesignerConfig()
      };
    },
    methods: {
      modelAction() {
        return new Promise(resovle => {
          window.$http
            .post(`${this.designerConfig.relevanceUrl}`, { app_id: this.idCollectObject.app_id })
            .then(res => {
              resovle(res.list);
            });
        });
      },
      handleChange() {
        const { designerConfig, idCollectObject } = this;
        if (designerConfig.hasModel && idCollectObject && idCollectObject.app_code) {
          this.optionModel.url = `/as/${idCollectObject.app_code}/model/list`;
        }
      }
    },
    mounted() {
      const { designerConfig, idCollectObject } = this;
      if (
        designerConfig.hasModel &&
        idCollectObject &&
        idCollectObject.app_code &&
        this.optionModel &&
        !this.optionModel.modelId
      ) {
        this.optionModel.modelId = idCollectObject.model_id;
        this.optionModel.requestUrl = `/as/${idCollectObject.app_code}/model/list`;
      }
    },
    render(h) {
      const { designerConfig, idCollectObject } = this;
      if (designerConfig.hasModel && idCollectObject && idCollectObject.app_code) {
        return (
          <el-form-item label={translate(propLabelKey)}>
            <funi-select v-model={this.optionModel[propName]} action={this.modelAction} onChange={this.handleChange} />
          </el-form-item>
        );
      }
    }
  };
};

export const createSelectApisEditor = function (propName, propLabelKey) {
  return {
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    data() {
      return {
        designerConfig: this.getDesignerConfig()
      };
    },
    methods: {
      modelAction(name) {
        return new Promise(resovle => {
          this.$lowCodeRequest
            .postApisListAsync({ app_id: idCollectObject.app_id, name, pageNo: 1, pageSize: 20 })
            .then(res => {
              resovle(
                (res.list || []).map(item => {
                  return {
                    label: item.name,
                    value: item.url
                  };
                })
              );
            });
        });
      }
    },
    mounted() {},
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <funi-select
            remote={true}
            filterable={true}
            clearable={true}
            v-model={this.optionModel[propName]}
            action={this.modelAction}
          />
        </el-form-item>
      );
    }
  };
};

export const createUserOrgScopeEditor = function (propName, propLabelKey, isOrg = false) {
  return {
    components: {
      UserOrgScopeEditor
    },
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    data() {
      return {
        designerConfig: this.getDesignerConfig(),
        hide: {
          role: isOrg,
          user: true,
          org: false
        }
      };
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <UserOrgScopeEditor v-model={this.optionModel[propName]} hide={this.hide} />
        </el-form-item>
      );
    }
  };
};

export const createAuthEditor = function (propName, propLabelKey) {
  return {
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    methods: {
      handleChange(e) {
        if (!this.optionModel) return;
        if (propName === 'hasAddAuth') {
          if (e && !this.optionModel.addPermission) {
            this.optionModel.addPermission = {
              id: window.$utils.guid(),
              name: '新增',
              code: `AS_AUTH_BTN_${randomString(true, 8, 10)}`,
              type: '2',
              type_name: '按钮'
            };
          }
        } else if (propName === 'hasMinusAuth') {
          if (e && !this.optionModel.minusPermission) {
            this.optionModel.minusPermission = {
              id: window.$utils.guid(),
              name: '删除',
              code: `AS_AUTH_BTN_${randomString(true, 8, 10)}`,
              type: '2',
              type_name: '按钮'
            };
          }
        }
      }
    },
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-switch v-model={this.optionModel[propName]} onChange={this.handleChange} />
        </el-form-item>
      );
    }
  };
};

export const createGantColumnsEditor = function (propName, propLabelKey) {
  return {
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    methods: {},
    render(h) {
      return <GanttColumnEditor v-model={this.optionModel[propName]} />;
    }
  };
};

export const createColorPickerEditor = function (propName, propLabelKey) {
  return {
    props: {
      optionModel: Object
    },
    inject: ['getDesignerConfig', 'idCollectObject'],
    render(h) {
      return (
        <el-form-item label={translate(propLabelKey)}>
          <el-color-picker v-model={this.optionModel[propName]} show-alpha />
        </el-form-item>
      );
    }
  };
};

export const createSearchConfigEditor = function (propName, searchConfigName, propLabelKey) {
  return {
    components: {
      SearchConfigEditor
    },
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    methods: {
      handleShowChange(e) {
        this.optionModel[propName] = e;
      },
      handleConfigChange(e) {
        if (!(this.optionModel[searchConfigName] && this.optionModel[searchConfigName].length > 0)) {
          this.optionModel[searchConfigName] = [{ name: 'default', schema: [] }];
        }
        this.optionModel[searchConfigName][0].schema = e;
      }
    },
    render(h) {
      if (!(this.optionModel[searchConfigName] && this.optionModel[searchConfigName].length > 0)) {
        this.optionModel[searchConfigName] = [{ name: 'default', schema: [] }];
      }
      return (
        <el-form-item label={translate(propLabelKey)} size="default">
          <SearchConfigEditor
            designer={this.designer}
            selectedWidget={this.selectedWidget}
            optionModel={this.optionModel}
            isShowSearch={this.optionModel[propName]}
            searchConfig={this.optionModel[searchConfigName][0].schema}
            onShowChange={this.handleShowChange}
            onConfigChange={this.handleConfigChange}
          />
        </el-form-item>
      );
    }
  };
};

export const createIndicatorDataEditor = function (propName, propLabelKey) {
  return {
    components: {
      ItemsDataEditor
    },
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    data() {
      return {
        columns: [
          { label: '名称', width: 65, type: 'el-input', prop: 'dataLabel' },
          {
            label: '最大值',
            width: 105,
            type: 'el-input-number',
            prop: 'dataValue',
            props: { controls: false, style: { width: '80px' } }
          }
        ]
      };
    },
    render() {
      return (
        <el-form-item labelWidth={50} label={translate(propLabelKey)} size="small">
          <ItemsDataEditor
            designer={this.designer}
            selectedWidget={this.selectedWidget}
            columns={this.columns}
            v-model={this.optionModel[propName]}
          />
        </el-form-item>
      );
    }
  };
};

export const createEmptyEditor = function () {
  return {
    render() {
      return <div style="display: none" />;
    }
  };
};
