<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-02 13:42:38
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-26 09:52:03
 * @FilePath: \src\components\FuniFormEngine\setting-panel\validator-items-setting\validator-items-dialog.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="validator-items-dialog">
    <sfc-design-dialog v-model="dialogVisible"
      title="添加规则"
      width="600px">
      <div style="padding-left: 20px;padding-right: 20px;">
        <funi-form ref="funiFormRef"
          :schema="schema"
          :rules="rules" />
      </div>
      <template #footer>
        <div>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary"
            @click="onSave">
            保存
          </el-button>
        </div>
      </template>
    </sfc-design-dialog>
  </div>
</template>

<script>
import {
  deepClone,
  findAllField
} from "../../common/utils/util"
export default {
  name: 'validator-items-dialog',
  components: {
  },
  props: {
    designer: Object,
  },
  data () {
    let variables = [];
    if (this.designer && this.designer.widgetList) {
      const fieldList = findAllField(this.designer.widgetList);
      if (fieldList && fieldList.length > 0) {
        const children = fieldList.map(item => {
          let child = {
            id: window.$utils.guid(), label: item.label, expression: `context.formData.${item.name}`
          }
          if (item.children && item.children.length > 0) {
            child.children = item.children
              .map(childItem => {
                return {
                  id: window.$utils.guid(),
                  label: childItem.label,
                  expression: `context.formData&&context.formData.${item.name}?context.formData.${item.name}.list?.filter(item=>item.${childItem.name}).map(item=>item.${childItem.name}):[]`
                };
              });
          }
          return child;
        })
        variables.push({
          id: 'formField',
          label: '表单字段',
          children
        })
        variables.push({
          id: 'route',
          label: '路由参数',
          children: [
            { id: 'routeId', label: 'id', expression: 'context.query.id' },
            { id: 'routeBusinessId', label: '业务id', expression: 'context.query.businessId' },
            { id: 'routeCid', label: '关联id', expression: 'context.query.cid' },
            { id: 'routeBizName', label: '操作类型', expression: 'context.query.bizName' },
            { id: 'routeBusType', label: '业务类型', expression: 'context.query.busType' }
          ]
        })
      }
    }
    return {
      id: '',
      dialogVisible: false,
      schema: [
        {
          prop: 'name',
          component: 'el-input',
          label: '规则名称'
        },
        {
          prop: 'expression',
          component: 'FuniVariableSetter',
          props: {
            variables
          },
          label: '通过表达式'
        },
        {
          prop: 'message',
          component: 'FuniVariableSetter',
          props: {
            variables
          },
          label: '不通过提示'
        }
      ],
      rules: {
        name: [{ required: true, message: '必填', trigger: 'change' }],
        expression: [{ required: true, message: '必填', trigger: 'change' }],
        message: [{ required: true, message: '必填', trigger: 'change' }]
      }
    }
  },
  mounted () {

  },
  computed: {
  },
  watch: {
    modelValue: {
      handler (newVal) {
        if (newVal) {
          this.id = newVal.id;
        }
        if (this.$refs.funiFormRef) {
          this.$refs.funiFormRef.setValues(newVal || {});
        }
      },
      immediate: true
    }
  },
  methods: {
    show (newVal) {
      this.dialogVisible = true;
      if (newVal) {
        this.id = newVal.id;
        this.$nextTick(() => {
          if (this.$refs.funiFormRef) {
            this.$refs.funiFormRef.setValues(newVal || {});
          }
        })
      } else {
        this.id = window.$utils.guid();
        this.$nextTick(() => {
          if (this.$refs.funiFormRef) {
            this.$refs.funiFormRef.resetFields();
          }
        })
      }
    },
    onSave () {
      const { id } = this;
      this.$refs.funiFormRef.validateField()
        .then(({ isValid, error, values }) => {
          if (isValid) {
            this.$emit('update:modelValue', { ...values, id });
            this.$emit('save', { ...values, id });
            this.dialogVisible = false;
          }
        })
    },
  },
}
</script>
