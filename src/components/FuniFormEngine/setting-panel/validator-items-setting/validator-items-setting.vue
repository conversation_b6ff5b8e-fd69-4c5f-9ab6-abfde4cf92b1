<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-01 15:44:16
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-04-01 18:06:34
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\validator-items-setting\validator-items-setting.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="validator-items-setting">
    <el-row>
      <el-table size="small"
        row-key="id"
        :data="localVal">
        <el-table-column prop="name"
          :show-overflow-tooltip="true"
          label="规则名称" />
        <el-table-column label="操作">
          <template #header>
            <el-link type="primary"
              icon="Plus"
              @click="handleAdd">添加</el-link>
          </template>
          <template #default="scope">
            <el-button link
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
              icon="EditPen" />
            <el-button link
              type="primary"
              size="small"
              @click="handleDelete(scope.$index )"
              icon="CircleClose" />
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <ValidatorItemsDialog :designer="designer"
      @save="handleInput"
      ref="validatorItemsSettingRef" />
  </div>
</template>

<script>
import ValidatorItemsDialog from './validator-items-dialog.vue'
export default {
  name: 'validator-items-setting',
  components: {
    ValidatorItemsDialog
  },
  props: {
    designer: Object,
    modelValue: {
      type: Array,
      default: () => { return [] }
    }
  },
  data () {
    return {
      dialogVisible: false,
      localVal: []
    }
  },
  mounted () {

  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.localVal = newVal || [];
      },
      immediate: true
    }
  },
  methods: {
    handleAdd () {
      this.$refs.validatorItemsSettingRef.show();
    },
    handleEdit (row) {
      this.$refs.validatorItemsSettingRef.show(row);
    },
    handleDelete (delIndex) {
      const { localVal } = this;
      localVal.splice(delIndex, 1);
      this.$emit('update:modelValue', localVal);
    },
    handleInput (e) {
      if (e) {
        let fIndex = this.localVal.findIndex(item => item.id === e.id);
        if (fIndex < 0) {
          this.localVal.push(e);
        } else {
          Object.assign(this.localVal[fIndex], e);
        }
      }
    }
  },
}
</script>
<style lang="scss" scoped>
.validator-items-setting {
  width: 100%;
}
</style>
