<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-16 10:30:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-04-08 09:55:55
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\validator-items-setting.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="validator-items-setting">
    <el-row><el-divider><span style="font-weight:bold;">校验规则</span></el-divider></el-row>
    <el-row>
      <el-table size="small"
        row-key="id"
        :data="validatorList">
        <el-table-column prop="name"
          :show-overflow-tooltip="true"
          label="规则名称" />
        <el-table-column label="操作">
          <template #header>
            <el-link type="primary"
              icon="Plus"
              @click="handleAdd">添加</el-link>
          </template>
          <template #default="scope">
            <el-button link
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
              icon="EditPen" />
            <el-button link
              type="primary"
              size="small"
              @click="handleDelete(scope.$index )"
              icon="CircleClose" />
          </template>
        </el-table-column>
      </el-table>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'validator-items-setting',
  components: {
  },
  props: {
    modelValue: {
      type: Array,
      default: () => { return [] }
    }
  },
  data () {
    return {
      validatorList: []
    }
  },
  mounted () {

  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.validatorList = newVal || [];
      },
      immediate: true
    }
  },
  methods: {
    handleAdd () {
      const { validatorList } = this;
      this.$emit('update:modelValue', validatorList);
    },
    handleEdit (row) {
      console.log('row', row);
    },
    handleDelete (delIndex) {
      const { validatorList } = this;
      validatorList.splice(delIndex, 1);
      this.$emit('update:modelValue', validatorList);
    }
  },
}
</script>
<style lang="scss" scoped>
.validator-items-setting {
  width: 100%;
}
</style>
