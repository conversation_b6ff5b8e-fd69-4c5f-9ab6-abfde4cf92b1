<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-23 16:17:22
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\event-handler\onCreated-editor.vue
 * @Description: 
-->
<template>
  <el-form-item label="onCreated"
    label-width="150px">
    <el-button type="info"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onCreated', eventParams)">
      {{i18nt('designer.setting.addEventHandler')}}</el-button>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";
import eventMixin from "./eventMixin"

export default {
  name: "onCreated-editor",
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      eventParams: [],
    }
  }

}
</script>

<style scoped>
</style>
