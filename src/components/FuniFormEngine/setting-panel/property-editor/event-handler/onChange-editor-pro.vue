<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-20 16:22:06
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-27 11:25:36
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\event-handler\onChange-editor-pro.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-button type="primary" @click="dialogVisible = true">编辑</el-button>
    <sfc-design-dialog
      v-if="dialogVisible"
      title="change事件设置"
      destroy-on-close
      append-to-body
      v-model="dialogVisible"
    >
      <div>
        <el-row v-for="(item, index) in localVal" :key="index" style="margin: 8px 0px">
          <el-col :span="8">
            <span>
              <el-tree-select
                :data="gloptions"
                placeholder="关联表单字段"
                filterable
                allow-create
                clearable
                v-model="item.gl"
                @change="e => handleChange(e, item)"
              />
              <VariableSetter
                style="margin-left: 24px"
                :variables="variables"
                v-model="item.gl"
                @change="e => handleChange(e, item)"
              />
            </span>
          </el-col>
          <el-col :span="4"
            ><span>填充到当前{{ isCurd ? '行' : '表单' }}</span></el-col
          >
          <el-col :span="8">
            <funi-select :options="dqoptions" placeholder="字段" clearable v-model="item.dq" />
          </el-col>
          <el-col :span="4">
            <el-row>
              <el-link type="primary" style="margin: 0px 16px" @click="onAdd">添加</el-link>
              <el-link v-if="index > 0" type="primary" @click="onRemove(index)">删除</el-link>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave"> 保存 </el-button>
        </span>
      </template>
    </sfc-design-dialog>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref, shallowReactive, watch, inject, nextTick, getCurrentInstance } from 'vue';
import { findAllWidget, findAllField } from '../../../common/utils/util.js';
import VariableSetter from '../../variable-setter.vue';

const emit = defineEmits('update:modelValue');

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => {
      return [];
    }
  },
  designer: Object,
  selectedWidget: Object,
  optionModel: Object,
  parentWidget: Object,
  isCurd: {
    type: Boolean,
    default: false
  }
});
const instance = getCurrentInstance();
const designerConfig = inject('getDesignerConfig')();
const idCollectObject = inject('idCollectObject');
const dialogVisible = ref(false);
const gloptions = shallowReactive([]);
const dqoptions = shallowReactive([]);
const localVal = shallowReactive([]);
const variables = shallowReactive([]);
const onAdd = () => {
  localVal.push({ gl: '', dq: '' });
};
const onRemove = index => {
  localVal.splice(index, 1);
};
const handleSave = () => {
  dialogVisible.value = false;
  emit('update:modelValue', localVal);
  emit('change', localVal);
};
watch(
  () => props.modelValue,
  newVal => {
    localVal.splice(0, localVal.length);
    localVal.push(...(newVal || []));
    if (localVal.length === 0) {
      localVal.push({ gl: '', dq: '' });
    }
  },
  { immediate: true }
);

watch(
  () => dialogVisible.value,
  newVal => {
    if (!newVal) return;
    gloptions.splice(0, gloptions.length);
    dqoptions.splice(0, dqoptions.length);
    variables.splice(0, variables.length);
    const generateVariables = () => {
      //生成变量
      if (gloptions.length > 0) {
        let vList = gloptions.map(g => {
          let item = {
            id: g.expression,
            label: g.label,
            value: g.expression,
            expression: g.expression
          };
          let children = (g.children || []).map(gc => {
            return {
              id: gc.expression,
              label: gc.label,
              value: g.expression,
              expression: gc.expression
            };
          });
          if (children && children.length > 0) {
            item.children = children;
          }
          return item;
        });
        variables.push({ id: window.$utils.guid(), label: '关联字段', children: vList });
      }
      if (props.designer && props.designer.widgetList) {
        const fieldList = findAllField(props.designer.widgetList);
        if (fieldList && fieldList.length > 0) {
          const children = fieldList.map(item => {
            let child = {
              id: window.$utils.guid(),
              label: item.label,
              expression: `context.formData.${item.name}`
            };
            if (item.children && item.children.length > 0) {
              child.children = item.children.map(childItem => {
                return {
                  id: window.$utils.guid(),
                  label: childItem.label,
                  expression: `context.formData&&context.formData.${item.name}?context.formData.${item.name}.list?.filter(item=>item.${childItem.name}).map(item=>item.${childItem.name}):[]`
                };
              });
            }
            return child;
          });
          let formChildren = children;
          if (
            idCollectObject &&
            idCollectObject.previousTabFields &&
            idCollectObject.previousTabFields.previous &&
            idCollectObject.previousTabFields.previous.length > 0
          ) {
            let stepFormChildren = [];
            let preSteps = idCollectObject.previousTabFields.previous;
            //添加前面步骤的表单字段
            preSteps.forEach(element => {
              if (element.tabTitle && element.fields && element.fields.length > 0) {
                let stepChildren = element.fields.map(item => {
                  return {
                    id: window.$utils.guid(),
                    label: item.name,
                    expression: `context.stepData?.step${element.tabIndex}?.${item.key}`
                  };
                });
                let stepFormItem = {
                  id: 'stepFormData' + element.tabIndex,
                  label: element.tabTitle,
                  children: stepChildren
                };
                stepFormChildren.push(stepFormItem);
              }
            });
            formChildren = [
              ...stepFormChildren,
              {
                id: 'currentFomrData',
                label: idCollectObject.previousTabFields.activeTitle,
                children: formChildren
              }
            ];
          }
          variables.push({
            id: 'formField',
            label: '表单字段',
            children: formChildren
          });
          variables.push(
            {
              id: 'route',
              label: '路由参数',
              children: [
                { id: 'routeId', label: 'id', expression: 'context.query.id' },
                { id: 'routeBusinessId', label: '业务id', expression: 'context.query.businessId' },
                { id: 'routeCid', label: '关联id', expression: 'context.query.cid' },
                { id: 'routeBizName', label: '操作类型', expression: 'context.query.bizName' },
                { id: 'routeBusType', label: '业务类型', expression: 'context.query.busType' }
              ]
            },
            {
              id: 'workflow',
              label: '工作流信息',
              children: [
                {
                  id: 'currentActivityId',
                  label: '当前节点ID',
                  expression: 'context.workflow?.businessFrontendInfo?.currentActivityId'
                },
                {
                  id: 'currentActivityName',
                  label: '当前节点名',
                  expression: 'context.workflow?.businessFrontendInfo?.currentActivityName'
                }
              ]
            }
          );
        }
      }
      return variables;
    };
    if (
      props.selectedWidget &&
      props.selectedWidget.options &&
      props.selectedWidget.options.modelOption &&
      (props.selectedWidget.options.modelOption.type === 'model' ||
        props.selectedWidget.options.modelOption.type === 'api')
    ) {
      let createFieldList = (fieldList, parentFieldName = '') => {
        let outList = fieldList.map(item => {
          let fieldId = `e.${parentFieldName}${item.code}`;
          let option = {
            label: `${item.name}`,
            value: `{\"codes\":\"@var{@id{${fieldId}}:@expression{e.${parentFieldName}${item.code}}:@label{关联字段.${item.name}}}\",\"label\":\"关联字段.${item.name}\",\"isExpress\":true,\"expression\":\"e.${parentFieldName}${item.code}\"}`,
            config: item.config,
            expression: `e.${parentFieldName}${item.code}`
          };
          if (item.children) {
            let parentName = `${parentFieldName}${item.code}?.`;
            let children = createFieldList(item.children, parentName, '');
            option.children = children;
          }
          return option;
        });
        return outList;
      };
      let model_id = props.selectedWidget.options.modelOption.modelId;
      let outputConfig = props.selectedWidget.options.modelOption.data
        ? props.selectedWidget.options.modelOption.data.outputConfig
        : null;
      if (model_id) {
        instance.proxy.$lowCodeRequest.postFieldFindAllTreeAsync({ model_id }).then(res => {
          let fieldList = createFieldList(res.list);
          gloptions.push(...fieldList);
          generateVariables();
        });
      } else if (outputConfig && outputConfig.length > 0) {
        const getFieldList = outputFieldList => {
          return outputFieldList.map(item => {
            let name = item.name;
            let code = item.field;
            let fieldObj = { name, code };
            if (item.children && item.children.length > 0) {
              const children = getFieldList(item.children);
              fieldObj.children = children;
            }
            return fieldObj;
          });
        };
        let list = getFieldList(outputConfig[0].children);
        let fieldList = createFieldList(list);
        let labelId = 'e.label';
        let valueId = 'e.value';
        fieldList.unshift({
          label: '标签',
          expression: 'e.label',
          value: `{\"codes\":\"@var{@id{${labelId}}:@expression{e.label}:@label{关联字段.标签}}\",\"label\":\"关联字段.标签\",\"isExpress\":true,\"expression\":\"e.label\"}`
        });
        fieldList.unshift({
          label: '值',
          expression: 'e.value',
          value: `{\"codes\":\"@var{@id{${valueId}}:@expression{e.value}:@label{关联字段.值}}\",\"label\":\"关联字段.值\",\"isExpress\":true,\"expression\":\"e.value\"}`
        });
        gloptions.push(...fieldList);
        generateVariables();
      }
    } else {
      let labelId = 'e.label';
      let valueId = 'e.value';
      gloptions.push({
        label: '标签',
        expression: 'e.label',
        value: `{\"codes\":\"@var{@id{${labelId}}:@expression{e.label}:@label{关联字段.标签}}\",\"label\":\"关联字段.标签\",\"isExpress\":true,\"expression\":\"e.label\"}`
      });
      gloptions.push({
        label: '值',
        expression: 'e.value',
        value: `{\"codes\":\"@var{@id{${valueId}}:@expression{e.value}:@label{关联字段.值}}\",\"label\":\"关联字段.值\",\"isExpress\":true,\"expression\":\"e.value\"}`
      });
    }
    generateVariables();
    let wgtList = [];
    if (props.isCurd) {
      if (props.parentWidget) {
        wgtList = findAllWidget(props.parentWidget.widgetList).filter(
          wgt => wgt.options.name !== props.selectedWidget.options.name
        );
      }
    } else {
      wgtList = findAllWidget(props.designer.widgetList).filter(
        wgt => wgt.options.name !== props.selectedWidget.options.name
      );
    }

    if (wgtList && wgtList.length > 0) {
      let options = wgtList.map(wgt => {
        return {
          label: wgt.options.label,
          value: wgt.options.name
        };
      });
      dqoptions.push(...options);
    }
  }
);

const handleChange = (e, item) => {
  let localValNew = [...(localVal || [])];
  localVal.splice(0, localVal.length);
  nextTick(() => {
    localVal.push(...localValNew);
  });
};
</script>
<style lang="scss" scoped></style>
