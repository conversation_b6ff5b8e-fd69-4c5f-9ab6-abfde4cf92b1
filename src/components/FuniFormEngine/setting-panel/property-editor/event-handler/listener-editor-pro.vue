<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-01-24 09:46:43
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\event-handler\listener-editor-pro.vue
 * @Description: 
-->
<template>
  <div>
    <el-form-item v-for="(item,index) in events"
      :key="index"
      :label="item.title||item.label"
      label-width="150px">
      <BottonsEventDialog :variables="variables"
        :logic="logicObjs[item.label]"
        :needWrap="false"
        :isNest="true"
        :event-name="item.title?item.title:item.label"
        :eventLabel="item.label"
        :designer="designer"
        :selectedWidget="selectedWidget"
        :optionModel="optionModel"
        :parentWidget="parentWidget"
        :isCurd="isCurd"
        argName="e"
        @change-body="code=>onChange(item.label,code)"
        @change-logic="logic=>onChnageLogic(item.label,logic)" />
    </el-form-item>
  </div>
</template>

<script>
import i18n from "../../../common/utils/i18n";
import eventMixin from "./eventMixin";
import CodeEditor from '../code-editor/code-editor.vue'
import { deepClone, getExpressionCode } from '../../../common/utils/util';
import BottonsEventDialog from "../buttons-editor/buttons-event-dialog.vue";
import fieldVariablesMixins from "../../property-editor/fieldVariablesMixins";

export default {
  name: "listener-editor-pro",
  mixins: [i18n, eventMixin, fieldVariablesMixins],
  components: {
    CodeEditor,
    BottonsEventDialog
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    parentWidget: Object,
    isCurd: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    events () {
      const type = this.selectedWidget.type;
      const list = [
        { label: 'change', prefix: 'e', title: 'change事件' },
        { label: 'changeObj', prefix: 'e', title: 'changeObj事件' },
        {
          label: 'input', prefix: 'e', title: '输入事件'
        },
        { label: 'blur', prefix: 'e', title: '失去焦点事件' },
        { label: 'focus', prefix: 'e', title: '获得焦点事件' },
        { label: 'click', prefix: 'e', title: '点击事件' },
        { label: 'rowClick', prefix: 'e', title: '点击表格行', },
        { label: 'afterRequest', prefix: 'e', title: '查询成功', },
        { label: 'requestError', prefix: 'e', title: '查询失败', },
        { label: 'mapLoadend', prefix: 'e', title: '地图加载完成', },
        { label: 'drawend', prefix: 'e', title: '绘制完成', },
        { label: 'layerClick', prefix: 'e,curProps', title: '图斑点击', },
      ];
      let realList = []
      switch (type) {
        case 'input':
          realList = list.filter(item => ['change', 'input'].includes(item.label));
          break;
        case 'button': case 'sfc-hyper-link':
          realList = list.filter(item => ['click'].includes(item.label));
          break;
        case 'funi-select': case 'sfc-select':
          realList = list.filter(item => ['changeObj'].includes(item.label));
          break;
        case 'funi-show-curd':
          realList = list.filter(item => ['change', 'rowClick', 'afterRequest', 'requestError'].includes(item.label));
          break;
        case 'sfc-ol-map':
          realList = list.filter(item => ['mapLoadend', 'drawend', 'layerClick'].includes(item.label));
          break;
        default:
          realList = [list[0]];
          break;
      }
      return realList;
    }
  },
  data () {
    return {
      eventParams: ['value'],
      listeners: {},
      listenerObjs: {},
      variables: [],
      logicObjs: {},
    }
  },
  watch: {
    'selectedWidget.options.listeners': {
      handler (newVal) {
        this.listeners = newVal || {};
      },
      immediate: true
    },
    'selectedWidget.options.listenerObjs': {
      handler (newVal) {
        this.listenerObjs = newVal || {};
      },
      immediate: true
    },
    'selectedWidget.options.logicObjs': {
      handler (newVal) {
        let logicObjs = newVal || {};
        if (this.selectedWidget.options && this.selectedWidget.options.listeners) {
          const listeners = this.selectedWidget.options.listeners;
          for (let evt in listeners) {
            if (!logicObjs[evt]) {
              logicObjs[evt] = {
                "actionItem": {
                  "pid": "0",
                  "root": true,
                  "isSelected": true,
                  "name": "自定义JS",
                  "code": "snippet",
                  "type": "SNIPPET",
                  "group": {
                    "id": "app",
                    "label": "应用动作"
                  },
                  "id": window.$utils.guid(),
                  "parentId": null,
                  "children": [],
                  "snippets": `${listeners[evt]}`
                },
                "snippets": `${listeners[evt]}`
              }
            }
          }
        }
        this.logicObjs = logicObjs;
      },
      immediate: true
    }
  },
  methods: {
    generateCode () {
      const { listeners, listenerObjs, events } = this;
      let list = [];
      for (let evt in listeners) {
        let prefix = 'e';
        if (events && events.length > 0) {
          let event = events.find(item => item.label === evt);
          if (event) {
            prefix = event.prefix;
            if (prefix && prefix.includes(',')) {
              prefix = `(${prefix})`;
            }
          }
        }
        list.push(`${evt}:${prefix}=>{${listeners[evt]}}`);
      }
      const listenerCode = `${list.join(',')}`;
      Object.assign(this.selectedWidget.options, { listener: listenerCode });
      Object.assign(this.selectedWidget.options, { listeners: listeners });
      Object.assign(this.selectedWidget.options, { listenerObjs: listenerObjs });
    },
    onChange (evtName, code) {
      if (evtName === 'click') {
        code = `let param={e};${code}`;
      }
      this.listeners[evtName] = code;
      this.generateCode();
    },
    onObjChange (evtName, list) {
      const cloneList = deepClone(list);
      let code = '';
      let prefix = this.isCurd ? 'row.' : 'context.formData.';
      if (cloneList && cloneList.length > 0) {
        cloneList.forEach(element => {
          let glCode = getExpressionCode(element.gl);
          if (glCode) {
            code += `${prefix}${element.dq}=${glCode};`;
          }
        });
      }
      this.listenerObjs[evtName] = cloneList;
      this.listeners[evtName] = code;
      this.generateCode();
    },
    onChnageLogic (evtName, logic) {
      let logicObjs = this.selectedWidget.options.logicObjs || {};
      logicObjs[evtName] = logic;
      Object.assign(this.selectedWidget.options, { logicObjs });
    }
  }
}
</script>

<style scoped>
</style>
