<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-11-15 19:42:08
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\event-handler\listener-editor.vue
 * @Description: 
-->
<template>
  <el-form-item label="listener"
    label-width="150px">
    <el-button type="info"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('listener', eventParams)">
      {{i18nt('designer.setting.addEventHandler')}}</el-button>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";
import eventMixin from "./eventMixin"

export default {
  name: "listener-editor",
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      eventParams: ['value'],
    }
  }
}
</script>

<style scoped>
</style>
