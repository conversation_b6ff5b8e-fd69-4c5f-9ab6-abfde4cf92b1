<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-08-24 20:00:45
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-14 16:25:28
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\user-org-scope-editor\user-org-scope-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="user-org-scope-dialog">
    <el-button :type="typeComputed"
      icon="el-icon-edit"
      :plain="plain"
      :round="round"
      :link="link"
      @click="show" />
    <el-dialog v-if="dialogVisible"
      v-model="dialogVisible"
      destroy-on-close
      append-to-body
      title="选择范围"
      width="1200px">
      <FuniRUOC v-model="scopeList"
        :hide="hide"
        :activeName="activeNameComputed"
        :roleProps="roleProps" />
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary"
          @click="onSave"> 保存 </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { computed, getCurrentInstance, reactive, toRefs, watch } from 'vue';
export default {
  name: 'user-org-scope-dialog',
  components: {},
  props: {
    plain: {
      type: Boolean,
      default: true
    },
    round: {
      type: Boolean,
      default: true
    },
    link: {
      type: Boolean,
    },
    modelValue: {
      type: Array,
      default: () => {
        return [];
      }
    },
    hide: {
      type: Object,
      default: () => {
        return {
          role: false,
          user: true,
          org: false
        };
      }
    }
  },
  setup (props) {
    const instance = getCurrentInstance();
    const state = reactive({
      scopeList: [],
      dialogVisible: false,
      allowedSelf: false
    });
    const typeComputed = computed(() => {
      return state.scopeList && state.scopeList.length > 0 ? 'primary' : 'default';
    });

    const activeNameComputed = computed(() => {
      return props.hide?.role ? 'org' : 'role'
    });

    const roleProps = reactive({
      request: {
        api: '/csccs/roleList/roleAllList',
        method: 'post',
        param: { flag: false }
      }
    });
    return {
      ...toRefs(state),
      roleProps,
      typeComputed,
      activeNameComputed
    };
  },
  data () {
    return {};
  },
  mounted () { },
  methods: {
    show () {
      this.scopeList = [...(this.modelValue || [])];
      this.dialogVisible = true;
    },
    onSave () {
      const { scopeList } = this;
      this.$emit('change', scopeList);
      this.$emit('update:modelValue', scopeList);
      this.dialogVisible = false;
    }
  }
};
</script>
