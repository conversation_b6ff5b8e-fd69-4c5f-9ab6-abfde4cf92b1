<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-24 14:53:49
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\field-button\button-type-editor.vue
 * @Description: 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.displayType')">
    <el-select v-model="optionModel.type">
      <el-option label="default"
        value=""></el-option>
      <el-option label="primary"
        value="primary"></el-option>
      <el-option label="success"
        value="success"></el-option>
      <el-option label="warning"
        value="warning"></el-option>
      <el-option label="danger"
        value="danger"></el-option>
      <el-option label="info"
        value="info"></el-option>
      <el-option label="text"
        value="text"></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";
import propertyMixin from "../propertyMixin"

export default {
  name: "button-type-editor",
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style lang="scss" scoped>
</style>
