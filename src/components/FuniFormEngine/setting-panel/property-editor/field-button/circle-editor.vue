<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-24 14:53:27
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\field-button\circle-editor.vue
 * @Description: 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.circle')">
    <el-switch v-model="optionModel.circle"></el-switch>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";
import propertyMixin from "../propertyMixin"

export default {
  name: "circle-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>
</style>
