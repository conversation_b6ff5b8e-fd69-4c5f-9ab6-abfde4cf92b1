<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-26 15:01:20
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\urlPrefix-editor.vue
 * @Description: 
-->
<template>
  <div>
    <el-form-item :label="i18nt('designer.setting.urlPrefix')">
      <el-input type="textarea"
        :rows="3"
        v-model="optionModel.urlPrefix" />
    </el-form-item>
  </div>
</template>

<script>
import i18n from "../../common/utils/i18n"

export default {
  name: "urlPrefix-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>
</style>
