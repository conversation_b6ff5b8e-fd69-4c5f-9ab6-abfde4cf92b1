<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-06 18:38:08
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-09-07 09:36:40
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\seting-curd-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="seting-curd-editor">

  </div>
</template>

<script>
export default {
  name: 'seting-curd-editor',
  components: {
  },
  props: {

  },
  data () {
    return {

    }
  },
  mounted () {

  },
  methods: {

  },
}
</script>
