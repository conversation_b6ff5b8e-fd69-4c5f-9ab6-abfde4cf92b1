<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-06 14:51:54
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-09-06 18:35:20
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\column-editor\components\sfc-select-setting-dialog.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="sfc-select-setting-dialog">
    <el-dialog v-if="dialogVisible"
      v-model="dialogVisible"
      title="组件属性设置"
      width="500"
      append-to-body>
      <span>
        <funi-form ref="funiFormRef"
          :schema="schema"
          :rules="rules" />
      </span>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary"
          @click="handleOk">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { inject, reactive, ref, toRefs, unref, defineEmits, nextTick, watch, getCurrentInstance } from 'vue';
export default {
  name: 'sfc-select-setting-dialog',
  components: {
  },
  props: {

  },
  setup (props) {
    const instance = getCurrentInstance();
    const idCollectObject = inject('idCollectObject');
    const state = reactive({
      dialogVisible: false,
      schema: [
        {
          label: '关联模型',
          prop: 'model_id',
          component: 'funi-select',
          props: {
            action: () => {
              return new Promise((resolve, reject) => {
                instance.proxy.$lowCodeRequest.postModelFindAllAsync({ app_id: idCollectObject.app_id })
                  .then(res => {
                    if (res && res.list && res.list.length > 0) {
                      let list = res.list.map(item => {
                        return {
                          label: item.name,
                          value: item.id
                        }
                      })
                      resolve(list);
                    }
                  })
                  .catch(err => {
                    reject(err);
                  })
              })
            }
          },
        },
        {
          label: '',
          labelWidth: '0px',
          prop: 'params',
          component: 'sfc-curd',
          props: {
            label: '筛选参数',
            pagination: false,
            columns: [
              {
                label: '参数名',
                prop: 'key',
                component: 'ElInput',
              },
              {
                label: '参数值',
                prop: 'value',
                component: 'ElInput'
              }
            ]
          }
        }
      ],
      rules: {
        model_id: [{ required: true, message: '请选择模型', trigger: 'blur' }],
      },
      localVal: undefined
    })
    const show = (values = {}) => {
      state.dialogVisible = true;
      nextTick(() => {
        const funiFormRef = instance.proxy.$refs.funiFormRef;
        funiFormRef?.setValues(values || {});
      })
    }
    const handleOk = async () => {
      const funiFormRef = instance.proxy.$refs.funiFormRef;
      const { error, isValid } = await funiFormRef.validate();
      if (isValid) {
        let values = funiFormRef.getValues();
        instance.proxy.$emit('save', { ...values });
        state.dialogVisible = false;
      }
    }
    return {
      ...toRefs(state),
      show,
      handleOk
    }
  },
  data () {
    return {
    }
  },
  mounted () {

  },
  methods: {

  },
}
</script>
