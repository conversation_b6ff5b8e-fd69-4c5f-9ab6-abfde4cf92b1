<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-30 17:42:57
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-12-01 11:10:19
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\column-editor\column-code-dialog.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <!-- <el-link type="primary"
      icon="View"
      @click="handleEdit">属性编码</el-link> -->
    <el-link type="primary"
      icon="Edit"
      @click="handleSetting"
      style="margin-left:8px;">编辑</el-link>
    <el-dialog v-if="dialogVisible"
      v-model="dialogVisible"
      title="属性编辑"
      width="1000px"
      destroy-on-close
      :append-to-body="true">
      <div>
        <el-alert type="info"
          :closable="false"
          title="{" />
        <CodeEdit :userWorker="true"
          v-model="attrsCode" />
        <el-alert type="info"
          :closable="false"
          title="}" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary"
            @click="handleSave">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
    <form-dialog ref="formDialogRef" />
    <sfc-select-setting-dialog ref="sfcSelectSettingDialogRef"
      @save="handleSfcSelectSave" />
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { reactive, ref, watchEffect, unref } from 'vue';
import CodeEdit from '../../../code-editor/index.vue'
import FormDialog from '../form-dialog/form-dialog.vue'
import SfcSelectSettingDialog from './components/sfc-select-setting-dialog.vue';
const emit = defineEmits('update:modelValue');

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  componentName: [String]
})

const sfcSelectSettingDialogRef = ref();

const dialogVisible = ref(false);

const attrsCode = ref('');

watchEffect(() => {
  let newVal = props.modelValue ?? '';
  newVal = newVal.replaceAll('@{', '');
  if (newVal && newVal.length > 0 && newVal[newVal.length - 1] === '@') {
    newVal = newVal.substring(0, newVal.length - 2);
  }
  attrsCode.value = newVal;
})

const handleEdit = (e) => {
  dialogVisible.value = true;
  let newVal = props.modelValue ?? '';
  newVal = newVal.replaceAll('@{', '');
  if (newVal && newVal.length > 0 && newVal[newVal.length - 1] === '@') {
    newVal = newVal.substring(0, newVal.length - 2);
  }
  attrsCode.value = newVal;
}

const handleSave = () => {
  dialogVisible.value = false;
  let code = `@{${attrsCode.value}}@`;
  emit('change', code);
  emit('update:modelValue', code);
}

const handleSetting = () => {
  if (props.componentName === 'SfcSelect') {
    let valueStr = `{${attrsCode.value}}`;
    let values = {};
    try {
      values = new Function(`return ${valueStr}`)();
    } catch { }
    unref(sfcSelectSettingDialogRef).show(values);
  } else {
    handleEdit();
  }
}

const handleSfcSelectSave = (values) => {
  let code = `@${JSON.stringify(values || {})}@`;
  emit('change', code);
  emit('update:modelValue', code);
}

</script>
<style lang='scss' scoped>
</style>