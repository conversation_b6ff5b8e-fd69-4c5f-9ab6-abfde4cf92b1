<template>
  <div class="column-editor-dialog">
    <el-button :type="typeComputed"
      icon="el-icon-edit"
      plain
      round
      @click="addColumn">{{
      i18nt('designer.setting.addCurdColumn')
    }}</el-button>
    <el-dialog v-if="dialogVisible"
      v-model="dialogVisible"
      destroy-on-close
      :title="i18nt('designer.setting.addCurdColumn')"
      width="78%">
      <funi-curd size="default"
        :columns="columns"
        :pagination="false"
        :draggable="true"
        :data="localVal"
        @draggableEnd="handleDraggableEnd">
        <template #header>
          <div style="height: 100%; display: flex; align-items: center"></div>
        </template>
        <template #buttonGroup>
          <component v-for="(item, index) in actions"
            :key="index"
            :is="item.component"
            v-bind="item.props || {}"
            v-on="item.on || {}"
            :style="item.style || {}" />
        </template>
      </funi-curd>
      <template #footer>
        <span>
          <el-button @click="dialogVisible = false"
            size="default">取消</el-button>
          <el-button type="primary"
            @click="onSave"
            size="default"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { deepClone } from '../../../common/utils/util';
import i18n from '../../../common/utils/i18n';
import { ElButton, ElInput, ElInputNumber, ElLink, ElAutocomplete } from 'element-plus';
import { h, resolveComponent } from 'vue';
import CodeDialog from './code-dialog.vue';
import EventEditorDialog from '../../event-editor-dialog.vue';
import { createRenderCode, RENDERTYPES } from '../../../common/utils/renderUtils';
export default {
  name: 'column-editor-dialog',
  mixins: [i18n],
  inject: ['getDesignerConfig', 'idCollectObject'],
  components: {},
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    modelValue: {
      type: Array,
      default: () => {
        return [];
      }
    },
    /**
     * 模式：编辑模式自动添加序号和选中列
     */
    mode: {
      type: String,
      default: 'edit'
    },
    variables: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data () {
    return {
      designerConfig: this.getDesignerConfig(),
      dialogVisible: false,
      actions: [
        {
          component: h(
            ElButton,
            {
              type: 'primary',
              icon: 'CirclePlus',
              size: 'default'
            },
            { default: () => '新建' }
          ),
          on: {
            click: () => {
              const { mode } = this;
              if (mode === 'edit') {
                this.localVal.push({ id: window.$utils.guid(), component: 'ElInput' });
              } else {
                this.localVal.push({ id: window.$utils.guid(), align: 'left' });
              }
            }
          }
        }
      ],
      localVal: [],
      propOptions: [],
      fieldList: [],
      fieldRequestId: ''
    };
  },
  mounted () { },
  computed: {
    columns () {
      const { mode, dialogVisible, propOptions, optionModel, designer, parentConfig, variables } = this;
      if (!dialogVisible) {
        return [];
      }
      let editColumns = [
        {
          label: '序号',
          type: 'index',
          prop: '',
          width: '60px'
        },
        {
          label: '列名',
          prop: 'label',
          width: '165px',
          render: ({ row }) => {
            return h(ElInput, {
              modelValue: row.label,
              placeholder: '请输入列名',
              onInput: e => {
                row.label = e;
              }
            });
          }
        },
        {
          label: '字段名',
          prop: 'prop',
          width: '165px',
          render: ({ row }) => {
            let propCom;
            let onInput = e => {
              row.prop = e;
            };
            let onChange = e => {
              row.model_id = '';
              if (
                e &&
                row.component === 'SfcSelect' &&
                this.optionModel &&
                this.optionModel.requestOtherParam &&
                this.optionModel.requestOtherParam.type === 'model' &&
                this.optionModel.requestOtherParam.data &&
                this.optionModel.requestOtherParam.data.model_id
              ) {
                let model_id = this.optionModel.requestOtherParam.data.model_id;
                this.getFieldModelId(model_id, e, row);
              }
              if (
                optionModel &&
                optionModel.requestOtherParam &&
                optionModel.requestOtherParam.type === 'model' &&
                optionModel.requestOtherParam.data &&
                optionModel.requestOtherParam.data.model_id
              ) {
                row.prop = e;
              }
            };
            if (
              optionModel &&
              optionModel.requestOtherParam &&
              optionModel.requestOtherParam.type === 'model' &&
              optionModel.requestOtherParam.data &&
              optionModel.requestOtherParam.data.model_id
            ) {
              const FuniSelect = resolveComponent('funi-select');
              propCom = h(FuniSelect, {
                modelValue: row.prop,
                placeholder: '请选择字段名',
                clearable: true,
                options: propOptions,
                onInput,
                onChange,
                onCurrentChange: propObj => {
                  row.label = propObj.label;
                }
              });
            } else {
              propCom = h(ElInput, {
                modelValue: row.prop,
                placeholder: '请输入字段名',
                onInput,
                onChange
              });
            }
            return propCom;
          }
        },
        {
          label: '校验',
          prop: 'validator',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.validator,
              placeholder: '请输入正则表达式(不含/)',
              clearable: true,
              options: [
                { label: '必填', value: '^\\S{1}[\\s\\S]*$' },
                { label: '手机号', value: '^[1][3-9][0-9]{9}$' },
                { label: '非负整数', value: '^[1-9]\\d*|0$' },
                { label: '非负两位小数', value: '^[0-9]+(.[0-9]{1,2})?$' },
                {
                  label: '身份证号码',
                  value: `^[1-9]\\d{5}(18|19|20|(3\\d))\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$`
                },
                { label: '电子邮件', value: '^([-_A-Za-z0-9.]+)@([_A-Za-z0-9]+\\.)+[A-Za-z0-9]{2,3}$' },
                {
                  label: 'url',
                  value:
                    '^([hH][tT]{2}[pP]:\\/\\/|[hH][tT]{2}[pP][sS]:\\/\\/)(([A-Za-z0-9-~]+)\\.)+([A-Za-z0-9-~\\/])+$'
                }
              ],
              multiple: true,
              filterable: true,
              allowCreate: true,
              onChange: e => {
                row.validator = e;
              },
              style: {
                width: '100%'
              }
            });
          }
        },
        {
          label: '宽度',
          prop: 'width',
          width: '100px',
          render: ({ row }) => {
            return h(ElInput, {
              modelValue: row.width,
              placeholder: '请输入宽度',
              onInput: e => {
                row.width = e;
              }
            });
          }
        },
        {
          label: '组件类型',
          prop: 'component',
          width: '130px',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.component,
              placeholder: '请选择组件类型',
              clearable: true,
              options: [
                { label: '显示文本', value: 'FuniLabel' },
                { label: '输入框', value: 'ElInput' },
                { label: '数字输入框', value: 'ElInputNumber' },
                { label: '下拉框', value: 'FuniSelect' },
                { label: '日期选择器', value: 'ElDatePicker' },
                { label: '模型', value: 'SfcSelect' }
              ],
              onChange: e => {
                row.component = e;
                row.model_id = '';
                if (
                  row.prop &&
                  e === 'SfcSelect' &&
                  this.optionModel &&
                  this.optionModel.requestOtherParam &&
                  this.optionModel.requestOtherParam.type === 'model' &&
                  this.optionModel.requestOtherParam.data &&
                  this.optionModel.requestOtherParam.data.model_id
                ) {
                  let model_id = this.optionModel.requestOtherParam.data.model_id;
                  this.getFieldModelId(model_id, row.prop, row);
                }
              }
            });
          }
        },
        {
          label: '组件属性',
          prop: 'comProps',
          render: ({ row }) => {
            const FuniVariableSetter = resolveComponent('funi-variable-setter');
            return h(FuniVariableSetter, {
              modelValue: row.comProps,
              variables: this.getVariables(row.component, row),
              onChange: e => {
                row.comProps = e;
              }
            });
          }
        },
        {
          label: '操作',
          prop: 'action',
          align: 'center',
          render: ({ row, index }) => {
            return h('div', {}, [
              // h(ElLink, {
              //   type: 'primary',
              //   icon: 'CirclePlus',
              //   onClick: () => {
              //     this.localVal.splice(index + 1, 0, { id: window.$utils.guid(), component: 'ElInput' });
              //   }
              // }),
              h(ElLink, {
                type: 'primary',
                icon: 'Remove',
                onClick: () => {
                  this.localVal.splice(index, 1);
                }
              })
            ]);
          }
        }
      ];
      let showColumns = [
        {
          label: '序号',
          type: 'index',
          prop: '',
          width: '60px'
        },
        {
          label: '列字段',
          prop: 'prop',
          align: 'center',
          width: '165px',
          render: ({ row }) => {
            let propCom;
            let options = row.childOptions || this.propOptions;
            let onInput = e => {
              row.prop = e;
            };
            let onChange = e => {
              row.model_id = '';
              if (
                e &&
                row.component === 'SfcSelect' &&
                this.optionModel &&
                this.optionModel.requestOtherParam &&
                this.optionModel.requestOtherParam.type === 'model' &&
                this.optionModel.requestOtherParam.data &&
                this.optionModel.requestOtherParam.data.model_id
              ) {
                let model_id = this.optionModel.requestOtherParam.data.model_id;
                this.getFieldModelId(model_id, e, row);
              }
              if (
                optionModel &&
                optionModel.requestOtherParam &&
                optionModel.requestOtherParam.type === 'model' &&
                optionModel.requestOtherParam.data &&
                optionModel.requestOtherParam.data.model_id
              ) {
                row.prop = e;
                let config, dataType;
                for (let i = 0; i < options.length; i++) {
                  let item = options[i];
                  if (item.value === e) {
                    config = item.config;
                    dataType = item.dataType;
                  } else if (item.children && item.children.length > 0) {
                    for (let j = 0; j < item.children.length; j++) {
                      if (item.children[j].value === e) {
                        config = item.children[j].config;
                        dataType = item.children[j].dataType;
                      }
                      if (config) {
                        break;
                      }
                    }
                  }
                  if (config) {
                    break;
                  }
                }
                if (config && dataType) {
                  let configObj = {};
                  try {
                    configObj = JSON.parse(config);
                    if (dataType === 'enum') {
                      row.formatter = 'ENUM';
                      row.dicCode = configObj.association_option_set_code;
                      row.dicName = configObj.association_option_set;
                    } else {
                      row.formatter = '';
                      row.dicCode = '';
                      row.dicName = '';
                    }
                    if (dataType === 'master_detail') {
                      //是否主子明细
                      row.isMasterDetail = true;
                      row.correlationModelId = configObj.correlation_model_id;
                    } else {
                      row.isMasterDetail = false;
                      delete row.correlationModelId;
                    }
                  } catch { }
                } else {
                  row.formatter = '';
                  row.dicCode = '';
                  row.dicName = '';
                }
                const renderType = config && config.is_primary_key + '' === '1' ? RENDERTYPES.MASTERLink : '';
                this.setRender(row, renderType);
              }
            };
            if (
              optionModel &&
              optionModel.requestOtherParam &&
              optionModel.requestOtherParam.type === 'model' &&
              optionModel.requestOtherParam.data &&
              optionModel.requestOtherParam.data.model_id
            ) {
              //当结合as后端使用
              const ElTreeSelect = resolveComponent('el-tree-select');
              propCom = h(ElTreeSelect, {
                modelValue: row.prop,
                placeholder: '请选择列字段',
                size: 'default',
                data: options,
                onInput,
                onChange,
                onCurrentChange: propObj => {
                  row.label = propObj.label;
                }
              });
            } else if (this.fieldList && this.fieldList.length > 0) {
              const FuniSelect = resolveComponent('FuniSelect');
              propCom = h(FuniSelect, {
                modelValue: row.prop,
                placeholder: '请输入列字段',
                size: 'default',
                options: this.fieldList,
                filterable: true,
                allowCreate: true,
                onInput,
                onChange
              });
            } else {
              //当低代码作为组件使用时
              propCom = h(ElInput, {
                modelValue: row.prop,
                placeholder: '请输入列字段',
                size: 'default',
                onInput,
                onChange
              });
            }
            return propCom;
          }
        },
        {
          label: '列标题',
          prop: 'label',
          align: 'center',
          width: '165px',
          render: ({ row }) => {
            return h(ElInput, {
              modelValue: row.label,
              placeholder: '请输入列名',
              size: 'default',
              onInput: e => {
                row.label = e;
              }
            });
          }
        },
        {
          label: '列宽',
          prop: 'width',
          align: 'center',
          render: ({ row }) => {
            return row.isMasterDetail
              ? ''
              : h(ElInputNumber, {
                modelValue: row.width,
                placeholder: '请输入宽度',
                size: 'default',
                onInput: e => {
                  row.width = e;
                }
              });
          }
        },
        {
          label: '是否排序',
          prop: 'sortable',
          width: '100px',
          align: 'center',
          render: ({ row }) => {
            const ElSwitch = resolveComponent('ElSwitch');
            return row.isMasterDetail
              ? ''
              : h(ElSwitch, {
                modelValue: row.sortable,
                size: 'default',
                onChange: e => {
                  row.sortable = e;
                }
              });
          }
        },
        {
          label: '类型',
          prop: 'type',
          width: '165px',
          align: 'center',
          hidden: true, //应需求要求隐藏,先满足需求
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return row.isMasterDetail
              ? ''
              : h(FuniSelect, {
                modelValue: row.type,
                placeholder: '请选择列类型',
                size: 'default',
                options: [
                  { label: '常规', value: null },
                  { label: '多选', value: 'selection' },
                  { label: '单选', value: 'radio' },
                  { label: '序号', value: 'index ' },
                  { label: '展开', value: 'expand ' }
                ],
                onChange: e => {
                  row.type = e;
                  row.model_id = '';
                  if (
                    row.prop &&
                    e === 'SfcSelect' &&
                    this.optionModel &&
                    this.optionModel.requestOtherParam &&
                    this.optionModel.requestOtherParam.type === 'model' &&
                    this.optionModel.requestOtherParam.data &&
                    this.optionModel.requestOtherParam.data.model_id
                  ) {
                    let model_id = this.optionModel.requestOtherParam.data.model_id;
                    this.getFieldModelId(model_id, row.prop, row);
                  }
                }
              });
          }
        },
        {
          label: '是否固定',
          prop: 'fixed',
          width: '165px',
          align: 'center',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return row.isMasterDetail
              ? ''
              : h(FuniSelect, {
                modelValue: row.fixed,
                options: [
                  { label: '无', value: '' },
                  { label: '左侧', value: 'left' },
                  { label: '右侧', value: 'right' }
                ],
                placeholder: '请选择是否固定',
                size: 'default',
                onChange: e => {
                  row.fixed = e;
                }
              });
          }
        },
        {
          label: '对齐方式',
          prop: 'align',
          align: 'center',
          width: '165px',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return row.isMasterDetail
              ? ''
              : h(FuniSelect, {
                modelValue: row.align,
                options: [
                  { label: '靠左', value: 'left' },
                  { label: '居中', value: 'center' },
                  { label: '靠右', value: 'right' }
                ],
                placeholder: '请选择对齐方式',
                size: 'default',
                onChange: e => {
                  row.align = e;
                }
              });
          }
        },
        {
          label: '格式化',
          prop: 'formatter',
          align: 'center',
          showOverflowTooltip: false,
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            let formatterOptions = [];
            const enumSourceStr = localStorage.getItem('enumSource');
            if (enumSourceStr) {
              let enumSource = [];
              try {
                enumSource = JSON.parse(enumSourceStr);
              } catch { }
              if (enumSource && enumSource.length >= 0) {
                let fIndex = enumSource.findIndex(item => item.code === 'ASAPP_FORMAT');
                if (fIndex >= 0 && enumSource[fIndex].dicResponses && enumSource[fIndex].dicResponses.length > 0) {
                  formatterOptions = enumSource[fIndex].dicResponses.map(item => {
                    return {
                      ...item,
                      label: item.label ?? item.name,
                      value: item.value ?? item.code
                    };
                  });
                }
              }
            }
            let width = '165px';
            let children = [
              h(FuniSelect, {
                modelValue: row.formatter,
                options: formatterOptions,
                placeholder: '请选择',
                size: 'default',
                clearable: true,
                style: {
                  width: '150px'
                },
                onChange: e => {
                  row.formatter = e;
                  row.dicName = '';
                  row.dicCode = '';
                  const renderType = '';
                  this.setRender(row, renderType);
                }
              })
            ];
            if (row.formatter === 'ENUM') {
              children.push(
                h(ElAutocomplete, {
                  modelValue: row.dicName,
                  clearable: true,
                  'fetch-suggestions': (queryString, cb) => {
                    const appCode = this.idCollectObject.app_code;
                    this.$lowCodeRequest
                      .postDicTypeListAsync(appCode, { keyword: queryString })
                      .then(res => {
                        let data = res.map(item => ({ value: item.name, link: item.code }));
                        cb(data);
                      });
                  },
                  style: {
                    width: '150px',
                    'margin-left': '10px'
                  },
                  placeholder: '请输入字典名称/编码',
                  size: 'default',
                  onInput: val => {
                    row.dicName = val;
                  },
                  onSelect: val => {
                    row.dicCode = val.link;
                    row.dicName = val.value;
                    this.setRender(row);
                  }
                })
              );
              width = '330px';
            }
            return row.isMasterDetail ? '' : h('div', { style: { width } }, children);
          }
        },
        {
          label: '自定义渲染',
          prop: 'render',
          width: '100px',
          align: 'center',
          render: ({ row }) => {
            return row.isMasterDetail
              ? ''
              : h(CodeDialog, {
                modelValue: row.render,
                size: 'default',
                onChange: e => {
                  row.render = e;
                }
              });
          }
        },
        {
          label: '是否隐藏',
          prop: 'hidden',
          width: '100px',
          align: 'center',
          hidden: false, //应需求要求隐藏,先满足需求
          render: ({ row }) => {
            const ElSwitch = resolveComponent('ElSwitch');
            return row.isMasterDetail
              ? ''
              : h(ElSwitch, {
                modelValue: row.hidden,
                size: 'default',
                onChange: e => {
                  row.hidden = e;
                }
              });
          }
        },
        {
          label: '事件',
          prop: 'attrs',
          align: 'center',
          render: ({ row }) => {
            return row.isMasterDetail
              ? ''
              : h(EventEditorDialog, {
                size: 'default',
                modelValue: row.eventsConfig,
                logic: row.logic,
                designer,
                parentConfig,
                variables,
                isBtnLabel: false,
                onChange: e => {
                  row.eventsConfig = e;
                },
                onChangeLogic: e => {
                  row.logic = e;
                }
              });
          }
        },
        {
          label: '操作',
          prop: 'action',
          width: '100px',
          align: 'center',
          fixed: 'right',
          render: ({ row, index }) => {
            let actionChildren = [
              h(ElLink, {
                type: 'primary',
                icon: 'Remove',
                size: 'default',
                onClick: () => {
                  if (row.isMasterDetailChild) {
                    let fIndex = this.localVal.findIndex(col => col.id === row.parentId);
                    if (fIndex >= 0) {
                      let delIndex = this.localVal[fIndex].children.findIndex(child => child.id === row.id);
                      this.localVal[fIndex].children.splice(delIndex, 1);
                    }
                  } else {
                    this.localVal.splice(index, 1);
                  }
                }
              })
            ];
            if (row.isMasterDetail) {
              actionChildren.splice(
                0,
                0,
                h(ElLink, {
                  type: 'primary',
                  icon: 'CirclePlus',
                  size: 'default',
                  style: {
                    marginRight: '16px'
                  },
                  onClick: () => {
                    if (!row.children) {
                      row.children = [];
                    }
                    this.$lowCodeRequest.postFieldFindAllTreeAsync({ model_id: row.correlationModelId }).then(res => {
                      if (res && res.list && res.list.length > 0) {
                        let childOptions = this.getTreeData(res.list, `${row.prop}.`);
                        row.children.push({
                          id: window.$utils.guid(),
                          parentId: row.id,
                          align: 'left',
                          isMasterDetailChild: true,
                          correlationModelId: row.correlationModelId,
                          childOptions
                        });
                      }
                    });
                  }
                })
              );
            }
            return h('div', {}, actionChildren);
          }
        }
      ];
      return mode === 'edit' ? editColumns : showColumns;
    },
    typeComputed () {
      return this.localVal && this.localVal.length > 0 ? 'primary' : 'info';
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.localVal = [...(deepClone(newVal) || [])];
      },
      immediate: true
    },
    'optionModel.requestOtherParam': {
      handler (newVal) {
        let type, id;
        if (newVal) {
          type = newVal.type;
          if (type === 'api') {
            id = newVal.data?.api_id;
          } else if (type === 'sql') {
            id = newVal.data?.sql_id;
          }
        }
        if (type && id && this.fieldRequestId !== id) {
          this.fieldRequestId = id;
          if (type === 'api') {
            this.$lowCodeRequest.postApisInfoAsync({ id }).then(result => {
              let fields = [];
              if (result && result.output_config) {
                const config = JSON.parse(result.output_config);
                if (config && config.length > 0) {
                  if (config[0] && config[0].children.length > 0) {
                    fields = config[0].children.map(field => {
                      return {
                        label: field.field,
                        value: field.field
                      }
                    })
                  }
                }
              }
              this.fieldList = fields;
            });
          } else if (type === 'sql') {
            this.$lowCodeRequest.postSqlfindByIdAsync(id).then(result => {
              let fields = [];
              if (result && result.output_config) {
                const config = JSON.parse(result.output_config);
                if (config && config.length > 0) {
                  if (config[0] && config[0].children.length > 0) {
                    fields = config[0].children.map(field => {
                      return {
                        label: field.field,
                        value: field.field
                      }
                    })
                  }
                }
              }
              this.fieldList = fields;
            });
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    setRender (row, renderType) {
      if (row.formatter === 'ENUM' && row.dicCode && row.dicName) {
        row.render = `const enumSourceStr = localStorage.getItem('enumSource');
        let options = [];
        if (enumSourceStr) {
          let enumSource = [];
          try {
            enumSource = JSON.parse(enumSourceStr);
          } catch(error) {}
          if (enumSource && enumSource.length >= 0) {
            let fIndex = enumSource.findIndex(item => item.code === '${row.dicCode}');
            if (fIndex >= 0 && enumSource[fIndex].dicResponses && enumSource[fIndex].dicResponses.length > 0) {
              options = enumSource[fIndex].dicResponses.map(item => {
                return {
                  label: item.name ?? item.label,
                  value: item.code ?? item.value
                };
              });
            }
          }
        }
        let dicName = '';
        if (options && options.length > 0) {
          try {
          let fieldVal = JSON.parse(context.params.row.${row.prop});
          if (Array.isArray(fieldVal) && fieldVal.length > 0) {
            if (typeof fieldVal[0] === 'object') {
              dicName = fieldVal.map(item => item.name).join(',');
            }
            if (typeof fieldVal[0] === 'string') {
              let findName = options.filter(item => fieldVal.includes(item.value)).map(item => item.label);
              dicName = findName.join(',');
            }
          } else if (typeof fieldVal === 'string' || typeof fieldVal === 'boolean' || typeof fieldVal === 'number') {
            let fIndex = options.findIndex(item => item.value === context.params.row.${row.prop});
            if (fIndex >= 0) {
              dicName = options[fIndex].label;
            }
          }
        } catch(error) {
          let fIndex = options.findIndex(item => item.value === context.params.row.${row.prop});
          if (fIndex >= 0) {
            dicName = options[fIndex].label;
          }
        }
        }
        return h('span', dicName);`;
      } else {
        const appInfo = {
          appCode: ''
        };
        createRenderCode(row, renderType);
      }
    },
    getTreeData (propList, prefix = '') {
      let list = [];
      if (propList && propList.length > 0) {
        propList.forEach(prop => {
          let node = {
            label: prop.name,
            value: `${prefix}${prop.code}`,
            config: prop.config,
            dataType: prop.data_type
          };
          if (prop.children && prop.children.length > 0) {
            let chilPrefix = `${prefix}${prop.code}.`;
            let children = this.getTreeData(prop.children, chilPrefix);
            node.children = children;
          }
          list.push(node);
        });
      }
      return list;
    },
    init () {
      const { optionModel } = this;
      if (
        optionModel &&
        optionModel.requestOtherParam &&
        optionModel.requestOtherParam.type === 'model' &&
        optionModel.requestOtherParam.data &&
        optionModel.requestOtherParam.data.model_id
      ) {
        const model_id = optionModel.requestOtherParam.data.model_id;
        let url = this.mode === 'edit' ? 'postFieldFindAllAsync' : 'postFieldFindAllTreeAsync';
        this.$lowCodeRequest[url]({ model_id }).then(res => {
          if (res && res.list && res.list.length > 0) {
            if (this.mode === 'edit') {
              this.propOptions = res.list.map(item => {
                return {
                  label: item.name,
                  value: item.code,
                  config: item.config,
                  dataType: item.data_type
                };
              });
            } else {
              this.propOptions = this.getTreeData(res.list);
            }
          }
        });
      }
    },
    getModelField (model_id, row) {
      let url = '/as/modelField/findAll';
      this.$lowCodeRequest
        .postFieldFindAllAsync({ model_id })
        .then(res => {
          if (res && res.list && res.list.length > 0) {
            if (this.mode === 'edit') {
              let fieldList = res.list.map(item => {
                return {
                  id: window.$utils.guid(),
                  label: item.name,
                  value: item.code,
                  config: item.config,
                  dataType: item.data_type,
                  expression: `'${item.code}'`
                };
              });
              row.fieldList = fieldList;
            }
          }
        })
        .catch(() => {
          row.children = [];
        });
    },
    addColumn () {
      this.localVal = [...(deepClone(this.modelValue) || [])];
      this.dialogVisible = true;
      this.init();
    },
    getFormatter (columnList, formatterList = []) {
      let list = [...(formatterList || [])];
      columnList.forEach(col => {
        if (col && col.prop && col.formatter) {
          if (!col.id) {
            col.id = window.$utils.guid();
          }
          list.push({ field_id: col.id, format_code: col.formatter, format_type_code: 'ASAPP_FORMAT', path: col.prop });
        }
        if (col.children && col.children.length > 0) {
          let childList = this.getFormatter(col.children);
          list.push(...(childList || []));
        }
      });
      return list;
    },
    onSave () {
      const { localVal } = this;
      let list = this.getFormatter(localVal);
      let component_id, page_id;
      if (
        this.selectedWidget &&
        this.selectedWidget.options &&
        this.selectedWidget.options.requestOtherParam &&
        this.selectedWidget.options.requestOtherParam.data
      ) {
        component_id = this.selectedWidget.options.requestOtherParam.data.component_id;
      }
      if (this.idCollectObject && this.idCollectObject.pageType === 'detail') {
        page_id = this.idCollectObject.detail_page_id;
      } else if (this.idCollectObject && this.idCollectObject.pageType === 'edit') {
        page_id = this.idCollectObject.edit_page_id;
      }
      let param = {
        app_id: this.idCollectObject.app_id,
        page_id,
        component_id,
        list
      };
      if (page_id && component_id) {
        let loading = this.$loading({ fullscreen: true });
        this.$lowCodeRequest
          .postPageFormatUpdateAsync(param)
          .then(res => {
            loading.close();
            this.$emit('update:modelValue', localVal);
            this.dialogVisible = false;
          })
          .catch(() => {
            loading.close();
          });
      } else {
        this.$emit('update:modelValue', localVal);
        this.dialogVisible = false;
      }
    },
    //获取自动关联模型的id
    getFieldModelId (modelId, fieldName, row) {
      let param = { model_id: modelId };
      this.$lowCodeRequest.postFieldFindAllTreeAsync(param).then(res => {
        if (res && res.list) {
          let fIndex = res.list.findIndex(item => item.code === fieldName);
          if (fIndex >= 0) {
            let col = res.list[fIndex];
            if (col && col.children && col.children.length > 0) {
              let model_id = col.children[0].model_id;
              row.model_id = model_id;
              this.getModelField(model_id, row);
            }
          }
        }
      });
    },
    handleDraggableEnd (e) {
      const { newIndex, oldIndex } = e;
      const oldData = this.localVal[oldIndex];
      this.localVal.splice(oldIndex, 1);
      this.localVal.splice(newIndex, 0, oldData);
      const newList = [...(this.localVal || [])];
      this.localVal = [];
      this.$nextTick(() => {
        this.localVal = newList;
      });
    },
    getVariables (componentName = '', row = {}) {
      let children = [];
      switch (componentName) {
        case 'FuniLabel':
          children = [{ id: window.$utils.guid(), label: '长度', expression: `'length'` }];
          break;
        case 'ElInput':
          children = [
            { id: window.$utils.guid(), label: '最大长度', expression: `'maxlength'` },
            { id: window.$utils.guid(), label: '最小长度', expression: `'minlength'` },
            { id: window.$utils.guid(), label: '占位文本', expression: `'placeholder'` },
            { id: window.$utils.guid(), label: '显示清除', expression: `'clearable'` }
          ];
          break;
        case 'ElInputNumber':
          children = [
            { id: window.$utils.guid(), label: '最大值', expression: `'max'` },
            { id: window.$utils.guid(), label: '最小值', expression: `'min'` },
            { id: window.$utils.guid(), label: '计数器步长', expression: `'step'` },
            { id: window.$utils.guid(), label: '数值精度', expression: `'precision'` },
            { id: window.$utils.guid(), label: '占位文本', expression: `'placeholder'` }
          ];
          break;
        case 'FuniSelect':
          children = [
            { id: window.$utils.guid(), label: '本地字典', expression: `'isLocal'` },
            { id: window.$utils.guid(), label: '字典码', expression: `'typeCode'` },
            { id: window.$utils.guid(), label: '自定义选项', expression: `'options'` }
          ];
          break;
        case 'ElDatePicker':
          children = [
            { id: window.$utils.guid(), label: '值的格式', expression: `'valueFormat'` },
            { id: window.$utils.guid(), label: '占位文本', expression: `'placeholder'` },
            { id: window.$utils.guid(), label: '显示清除', expression: `'clearable'` }
          ];
          break;
        case 'SfcSelect':
          children = [];
          if (row && row.fieldList) {
            children = [...row.fieldList];
          }
          break;
        default:
          break;
      }
      const variables = [];
      if (children && children.length) {
        variables.push({
          id: 'props',
          label: componentName === 'SfcSelect' ? '筛选字段' : '组件属性',
          children
        });
      }
      const { propOptions } = this;
      if (componentName === 'SfcSelect' && propOptions && propOptions.length > 0) {
        let reChildren = propOptions.map(item => {
          return {
            id: window.$utils.guid(),
            label: item.label,
            expression: `context.params.row.${item.value}`
          };
        });
        this.fieldList;
        variables.push({
          id: 'reProps',
          label: '联动字段',
          children: reChildren
        });
      }
      return variables;
    }
  }
};
</script>
