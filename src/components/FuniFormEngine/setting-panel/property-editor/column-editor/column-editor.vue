<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-24 19:04:27
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-01 11:38:31
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\column-editor\column-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('extension.setting.columns')">
    <column-editor-dialog v-model="localVal"
      :mode="mode"
      :designer="designer"
      :parentConfig="parentConfig"
      :selectedWidget="selectedWidget"
      :optionModel="optionModel"
      :variables="variables" />
  </el-form-item>
</template>
<script>
import i18n from "../../../common/utils/i18n"
import ColumnEditorDialog from './column-editor-dialog.vue';
import fieldVariablesMixins from "../../property-editor/fieldVariablesMixins"
export default {
  name: "column-editor",
  mixins: [i18n, fieldVariablesMixins],
  components: {
    ColumnEditorDialog
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    parentConfig: Object,
    modelValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    /**
     * 模式：编辑模式自动添加序号和选中列
     */
    mode: {
      type: String,
      default: 'edit'
    }
  },
  data () {
    return {
      localVal: [],
      variables: []
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.localVal = newVal || [];
      },
      immediate: true
    },
    localVal: {
      handler (newVal) {
        this.$emit('update:modelValue', newVal);
      },
      immediate: true
    }
  }
}
</script>
<style lang='scss' scoped>
</style>
