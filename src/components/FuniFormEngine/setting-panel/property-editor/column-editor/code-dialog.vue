<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-01-17 17:25:06
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-01-19 14:46:18
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\column-editor\code-dialog.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-link :type="renderCode?'primary':'default'"
      icon="Edit"
      @click="handleEdit" />
    <el-dialog v-model="dialogVisible"
      title="render编辑"
      width="1000px"
      destroy-on-close
      :append-to-body="true">
      <div>
        <el-alert type="info"
          :closable="false"
          title="render:({row,index})=>{" />
        <CodeEdit :userWorker="true"
          v-model="renderCode" />
        <el-alert type="info"
          :closable="false"
          title="}" />
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary"
          @click="handleSave">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { reactive, ref, watchEffect } from 'vue';
import CodeEdit from '../../../code-editor/index.vue'
const emit = defineEmits('update:modelValue');

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const dialogVisible = ref(false);

const renderCode = ref('');

watchEffect(() => {
  let newVal = props.modelValue ?? '';
  newVal = newVal.replaceAll('@({row,index})=>{', '');
  if (newVal && newVal.length > 0 && newVal[newVal.length - 1] === '@') {
    newVal = newVal.substring(0, newVal.length - 2);
  }
  renderCode.value = newVal;
})

const handleEdit = (e) => {
  dialogVisible.value = true;
  let newVal = props.modelValue ?? '';
  newVal = newVal.replaceAll('@({row,index})=>{', '');
  if (newVal && newVal.length > 0 && newVal[newVal.length - 1] === '@') {
    newVal = newVal.substring(0, newVal.length - 2);
  }
  renderCode.value = newVal;
}

const handleSave = () => {
  dialogVisible.value = false;
  let code = `@({row,index})=>{${renderCode.value}}@`;
  emit('change', code);
  emit('update:modelValue', code);
}

</script>
<style lang='scss' scoped>
</style>