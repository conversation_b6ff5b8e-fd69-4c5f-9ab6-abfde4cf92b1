<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-23 16:29:06
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\field-time\time-format-editor.vue
 * @Description: 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.format')">
    <el-select v-model="optionModel.format"
      filterable
      allow-create>
      <el-option label="HH:mm:ss"
        value="HH:mm:ss"></el-option>
      <el-option label="HH时mm分ss秒"
        value="HH时mm分ss秒"></el-option>
      <el-option label="hh:mm:ss"
        value="hh:mm:ss"></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";

export default {
  name: "time-format-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>
</style>
