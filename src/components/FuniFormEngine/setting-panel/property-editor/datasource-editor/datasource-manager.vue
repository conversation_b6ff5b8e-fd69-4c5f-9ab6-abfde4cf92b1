<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-27 21:03:23
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-27 11:35:24
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\datasource-manager.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="datasource-manager">
    <el-row>
      <el-tabs v-model="activeName">
        <el-tab-pane v-if="designerConfig.hasModel&&idCollectObject&&idCollectObject.app_id"
          label="数据模型"
          name="model"
          style="width:100%;">
          <model-setting v-model="localVal.model"
            :isSimple="isSimple"
            :designer="designer"
            :selectedWidget="selectedWidget"
            :showLabelKey="showOptions"
            :isCurd="isCurd" />
        </el-tab-pane>
        <el-tab-pane v-if="showOptions"
          label="枚举"
          name="typeCode">
          <type-code-setting :designer="designer"
            :selectedWidget="selectedWidget" />
        </el-tab-pane>
        <el-tab-pane v-if="showOptions"
          label="自定义"
          name="custom">
          <options-setting :designer="designer"
            :selectedWidget="selectedWidget"
            :propName="propName" />
        </el-tab-pane>
        <el-tab-pane label="APIs"
          name="api">
          <api-setting :isSimple="isSimple"
            :designer="designer"
            v-model="localVal.api"
            @extract-column="e=>$emit('extract-column',e)" />
        </el-tab-pane>
        <el-tab-pane label="SQL"
          name="sql">
          <sql-setting :isSimple="isSimple"
            :designer="designer"
            v-model="localVal.sql"
            @extract="e=>$emit('extract',e)"
            @extract-column="e=>$emit('extract-column',e)" />
        </el-tab-pane>
      </el-tabs>
    </el-row>
  </div>
</template>

<script>
import ModelSetting from './model-setting.vue';
import { reactive, toRefs } from 'vue'
import ApiSetting from './api-setting.vue';
import SqlSetting from './sql-setting.vue';
import TypeCodeSetting from './typeCode-setting.vue';
import OptionsSetting from './options-setting.vue';
export default {
  name: 'datasource-manager',
  components: {
    ModelSetting,
    ApiSetting,
    SqlSetting,
    TypeCodeSetting,
    OptionsSetting
  },
  inject: ['getDesignerConfig', 'idCollectObject'],
  props: {
    designer: Object,
    selectedWidget: Object,
    isSimple: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: Object,
      default: () => {
        return {
          type: 'model',
          data: {}
        }
      }
    },
    propName: {
      type: String,
      default: 'selectOptions'
    },
    isCurd: {
      type: Boolean,
      default: false
    }
  },
  data () {
    let designerConfig = this.getDesignerConfig();
    let activeName = designerConfig.hasModel && this.idCollectObject && this.idCollectObject.app_id ? 'model' : this.selectedWidget && this.selectedWidget.type === 'funi-select' ? 'typeCode' : 'api';
    if (this.modelValue && this.modelValue.type) {
      activeName = 'model';
    }
    return {
      designerConfig,
      activeName,
      localVal: {
        model: {},
        api: {},
        sql: {}
      },
      innerChange: false
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.innerChange = true;
        if (newVal && newVal.type) {
          this.activeName = newVal.type;
          this.localVal[newVal.type] = newVal.data || {};
        } else {
          this.localVal.model = {};
          this.localVal.api = {};
          this.localVal.sql = {};
        }
        this.innerChange = false;
      },
      immediate: true
    },
    localVal: {
      handler (newVal) {
        if (!this.innerChange) {
          const { activeName } = this;
          let realData = { type: activeName, data: newVal[activeName] };
          if (activeName === 'model' && newVal[activeName]) {
            realData.modelId = newVal[activeName].model_id;
            realData.dataType = newVal[activeName].dataType;
            realData.middleModelId = newVal[activeName].middleModelId;
            realData.correlationModelId = newVal[activeName].correlationModelId;
            realData.correlationModelCode = newVal[activeName].correlationModelCode;
            realData.modelName = newVal[activeName].modelName;
          } else {
            realData.modelId = null;
            realData.dataType = null;
            realData.middleModelId = null;
            realData.correlationModelId = null;
            realData.correlationModelCode = null;
            realData.modelName = null;
          }
          this.$emit('update:modelValue', realData);
        }
      },
      deep: true
    },
    activeName: {
      handler (newVal) {
        if (!this.innerChange) {
          const { localVal } = this;
          this.$emit('update:modelValue', { type: newVal, data: localVal[newVal] });
        }
      }
    }
  },
  computed: {
    showOptions () {
      return this.selectedWidget && this.selectedWidget.type === 'funi-select';
    }
  },
  mounted () {
  },
  methods: {
  },
}
</script>
<style lang="scss" scoped>
.datasource-manager {
  width: 100%;
  :deep(.el-tabs) {
    width: 100%;
  }
}
</style>
