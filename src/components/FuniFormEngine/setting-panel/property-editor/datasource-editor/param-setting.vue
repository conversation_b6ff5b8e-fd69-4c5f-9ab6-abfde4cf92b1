<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-27 17:25:31
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-12 14:17:52
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\param-setting.vue
 * @Description:  
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-input v-model="localValStr"
      readonly
      placeholder="请配置">
      <template #append><el-button @click="onClick">···</el-button></template>
    </el-input>
    <param-setting-dialog ref="paramSettingDialogRef"
      :fieldList="fieldList"
      :isOperator="isOperator"
      :designer="designer"
      :selectedWidget="selectedWidget"
      v-model="localVal" />
  </div>
</template>

<script>
import ParamSettingDialog from './param-setting-dialog.vue';
export default {
  components: {
    ParamSettingDialog
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    //是否包含运算符
    isOperator: {
      type: Boolean,
      default: false
    },
    fieldList: {
      type: Array,
      default: () => {
        return []
      }
    },
    modelValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    valueStr: [String]
  },
  data () {
    return {
      localVal: [],
      localValStr: ''
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.localVal = newVal || [];
      },
      immediate: true
    },
    valueStr (newVal) {
      if (newVal) {
        this.localValStr = newVal;
      }
    },
    localVal (newVal) {
      if (newVal && newVal.length > 0) {
        this.localValStr = JSON.stringify((newVal || []));
      } else {
        this.localValStr = '';
      }
      this.$emit('change', newVal);
      this.$emit('update:modelValue', newVal);
    }
  },
  methods: {
    onClick () {
      this.$refs.paramSettingDialogRef.show();
    }
  }
}

</script>