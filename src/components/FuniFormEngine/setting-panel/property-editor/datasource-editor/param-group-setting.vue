<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-27 17:25:31
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-11-22 17:34:31
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\param-group-setting.vue
 * @Description:  
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-button v-if="isBtnOpen"
      link
      icon="Edit"
      :type="localVal&&localVal.length>0?'primary':'default'"
      @click="onClick" />
    <el-input v-else
      v-model="localValStr"
      readonly
      placeholder="请配置">
      <template #append><el-button @click="onClick">···</el-button></template>
    </el-input>
    <param-group-setting-dialog ref="paramGroupSettingDialogRef"
      :fieldList="fieldListComputed"
      :formWidgetList="formWidgetList"
      :formDataName="formDataName"
      :isOperator="isOperator"
      :designer="designer"
      :selectedWidget="selectedWidget"
      :isCurd="isCurd"
      v-model="localVal" />
  </div>
</template>

<script>
import ParamGroupSettingDialog from './param-group-setting-dialog.vue';
export default {
  components: {
    ParamGroupSettingDialog
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    /**
     * 是否用作为打开按钮入口
     */
    isBtnOpen: {
      type: Boolean,
      default: false
    },
    //是否包含运算符
    isOperator: {
      type: Boolean,
      default: false
    },
    fieldList: {
      type: Array,
      default: () => {
        return []
      }
    },
    //自定义表单组件
    formWidgetList: {
      type: Array
    },
    //自定义表单上下文件名称
    formDataName: {
      type: String,
      default: 'formData'
    },
    isCurd: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    //字段对应的模型
    model_id: [String],
    valueStr: [String]
  },
  data () {
    return {
      localVal: [],
      localValStr: '',
      remoteFieldList: []
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.localVal = newVal || [];
      },
      immediate: true
    },
    valueStr (newVal) {
      if (newVal) {
        this.localValStr = newVal;
      }
    },
    localVal (newVal) {
      if (newVal && newVal.length > 0) {
        this.localValStr = JSON.stringify((newVal || []));
      } else {
        this.localValStr = '';
      }
      this.$emit('change', newVal);
      this.$emit('update:modelValue', newVal);
    }
  },
  computed: {
    fieldListComputed () {
      if (this.remoteFieldList && this.remoteFieldList.length > 0) {
        return this.remoteFieldList;
      } else {
        return this.fieldList;
      }
    }
  },
  methods: {
    onClick () {
      if (this.model_id && !(this.fieldList && this.fieldList.length > 0)) {
        const loading = this.$loading({ fullscreen: true });
        let params = {
          model_id: this.model_id
        };
        this.$lowCodeRequest.postFieldFindAllTreeAsync(params)
          .then(res => {
            loading.close();
            if (res && res.list && res.list.length > 0) {
              this.remoteFieldList = res.list.map(item => {
                let tableName = '';
                if (item.config) {
                  let fieldConfig;
                  try {
                    fieldConfig = JSON.parse(item.config);
                  } catch { }
                  if (fieldConfig && fieldConfig.correlation_model) {
                    tableName = fieldConfig.correlation_model
                  }
                }
                const field = {
                  label: item.name,
                  value: item.code,
                  config: item.config,
                  dataType: item.data_type
                };
                if (item.children && item.children.length > 0) {
                  field.children = item.children.map(child => {
                    return {
                      label: child.name,
                      value: `${tableName}.${child.code}`,
                      config: child.config,
                      dataType: child.data_type
                    }
                  });
                }
                return field;
              });
            }
            this.$refs.paramGroupSettingDialogRef.show();
          })
          .catch(err => {
            loading.close();
          });
      } else {
        this.$refs.paramGroupSettingDialogRef.show();
      }
    }
  }
}

</script>