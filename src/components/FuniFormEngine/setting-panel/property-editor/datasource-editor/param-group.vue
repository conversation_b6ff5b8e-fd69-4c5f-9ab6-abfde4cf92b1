<template>
  <el-form-item :size="size"
    class="param-group">
    <el-row v-if="!hideLogic"
      class="param-group-row">
      <el-divider>
        <funi-select v-model="localVal.logicalOperator"
          class="param-group-row-group-select"
          :options="logicOptions"
          placeholder=" "
          @change="handleGoupLogChange" />
      </el-divider>
    </el-row>
    <el-row class="param-group-row param-group-condition"
      justify="space-around"
      align="middle"
      v-for="(item,index) in localVal.conditions"
      :key="index">
      <el-col :span="1">
        <span v-if="index===0"
          style="margin-left:8px;">当</span>
        <funi-select v-else
          v-model="item.logicalOperator"
          class="param-group-row-group-select"
          :options="logicOptions"
          placeholder=" " />
      </el-col>
      <el-col :span="6">
        <el-tree-select v-model="item.showKey"
          allow-create
          filterable
          :data="fieldList"
          @change="val=>handleFieldChange(val,item)"
          @current-change="node=>handleCurrentChange(node,item)" />
      </el-col>
      <el-col :span="3">
        <funi-select v-model="item.operator"
          :action="operatorAction"
          placeholder=" "
          style="width:100%;"
          @change="handleChange(item)" />
      </el-col>
      <el-col :span="6">
        <FuniVariableSetter v-if="!['IS_NULL','IS_NOT_NULL'].includes(item.operator)"
          v-model="item.value"
          :variables="variables" />
      </el-col>
      <el-col :span="1">
        <el-button type="primary"
          link
          icon="Remove"
          @click="removeCondition(index)" />
      </el-col>
    </el-row>
    <el-row>
      <el-button type="primary"
        link
        icon="Plus"
        @click="addCondition">添加条件</el-button>
    </el-row>
  </el-form-item>
</template>

<script>
import { getCurrentInstance, reactive, toRefs, watch } from 'vue';
export default {
  name: 'param-group',
  components: {
  },
  props: {
    size: {
      type: String,
      default: 'small'
    },
    variables: [Array],
    modelValue: {
      type: Object
    },
    hideLogic: {
      type: Boolean,
      default: false
    },
    fieldList: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  setup (props) {
    const instance = getCurrentInstance();
    const state = reactive({
      logicOptions: [{ label: '且', value: 'AND' }, { label: '或', value: 'OR' }]
    });
    const localVal = reactive({
      logicalOperator: '',
      conditions: [{ operator: 'EQUAL' }]
    })

    watch(props.modelValue, (newVal) => {
      //兼容老数据
      if (newVal && newVal.conditions) {
        newVal.conditions.forEach(element => {
          if (!element.showKey) {
            element.showKey = element.key;
          }
        });
      }
      //兼容结束
      let val = newVal || {};
      localVal.logicalOperator = val.logicalOperator;
      localVal.conditions = val.conditions;
    }, { immediate: true })

    const operatorAction = () => {
      return new Promise((resovle) => {
        let operatorOptions = [];
        const enumSourceStr = localStorage.getItem('enumSource');
        if (enumSourceStr) {
          let enumSource = [];
          try {
            enumSource = JSON.parse(enumSourceStr);
          } catch { }
          if (enumSource && enumSource.length >= 0) {
            let fIndex = enumSource.findIndex(item => item.code === 'ASAPP_OPERATOR');
            if (fIndex >= 0 && enumSource[fIndex].dicResponses && enumSource[fIndex].dicResponses.length > 0) {
              operatorOptions = enumSource[fIndex].dicResponses.map(item => {
                return {
                  ...item,
                  label: item.label ?? item.name,
                  value: item.value ?? item.code
                }
              })
            }
          }
        }
        resovle(operatorOptions)
      })
    }

    const addCondition = () => {
      localVal.conditions.push({ logicalOperator: 'AND', operator: 'EQUAL' });
    }

    const removeCondition = (index) => {
      localVal.conditions.splice(index, 1);
      if (!(localVal.conditions && localVal.conditions.length > 0)) {
        instance.proxy.$emit('clear')
      }
    }

    const handleGoupLogChange = (val) => {
      instance.proxy.$emit('goupLogChange', val);
    }

    const handleChange = (item) => {
      if (['IS_NULL', 'IS_NOT_NULL'].includes(item.operator)) {
        item.value = '';
      }
    }

    const handleFieldChange = (val, item) => {
      item.tableName = '';
      item.key = '';
      if (val && val.includes('.')) {
        let list = val.split('.');
        if (list && list.length >= 2) {
          item.tableName = list[0];
          item.key = list[1];
        } else {
          item.key = val;
        }
      } else {
        item.key = val;
      }
    }

    const handleCurrentChange = (node, item) => {
      if (node && node.joinFieldName) {
        item.joinFieldName = node.joinFieldName;
      } else {
        item.joinFieldName = '';
      }
    }

    return {
      ...toRefs(state),
      operatorAction,
      addCondition,
      removeCondition,
      handleGoupLogChange,
      handleChange,
      handleFieldChange,
      handleCurrentChange,
      localVal
    }
  },
  data () {
    return {

    }
  },
  mounted () {

  },
  methods: {

  },
}
</script>
<style lang="scss" scoped>
.param-group {
  width: 100%;
  padding: 0px 32px;
  &-row {
    width: 100%;
    &-group-select {
      width: 50px;
      :deep(.el-input__wrapper) {
        box-shadow: none;
      }
    }
  }
  &-condition {
    margin-bottom: 16px;
  }
}
</style>
