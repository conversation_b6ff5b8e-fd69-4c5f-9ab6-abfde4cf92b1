<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-27 21:03:23
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-19 14:52:34
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\api-setting.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-form ref="funiFormRef"
    :labelWidth="labelWidth"
    :schema="schema"
    :rules="rules" />
</template>

<script>
import { h, inject, reactive, resolveComponent, toRefs, getCurrentInstance, onMounted, ref } from 'vue';
import ParamSetting from './param-setting.vue';
export default {
  name: 'api-setting',
  inject: ['idCollectObject'],
  components: {},
  props: {
    designer: Object,
    labelWidth: [String, Number],
    isSimple: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  setup (props) {
    const instance = getCurrentInstance();
    const designerConfig = inject('getDesignerConfig')();
    const idCollectObject = inject('idCollectObject');
    let apiList = [];
    let api_url = '';
    let apiType = '';
    if (props.modelValue) {
      api_url = props.modelValue.api_url;
      apiType = props.modelValue.apiType;
    }
    const paramsFieldList = ref([]);
    const state = reactive({
      extractStatus: '',
      schema: [
        {
          prop: 'api_id',
          component: 'funi-select',
          label: 'APIs',
          props: {
            action: name => {
              return new Promise((resovle, reject) => {
                let params = { app_id: idCollectObject.app_id, pageNo: 1, pageSize: 99 };
                if (name) {
                  params.name = name;
                }
                instance.proxy.$lowCodeRequest.postApisListAsync(params).then(res => {
                  let list = [];
                  if (res && res.list && res.list.length > 0) {
                    res.list.forEach(item => {
                      list.push({ ...item, label: item.name, value: item.id });
                    });
                  }
                  apiList = list;
                  resovle(list);
                });
              });
            },
            filterable: true,
            remote: true,
            onChangeItem: val => {
              instance.proxy.innerChange = true;
              api_url = val.url;
              apiType = val.type;
              instance.proxy.localVal.api_id = val.id;
              instance.proxy.localVal.method = val.method?.toLowerCase();
              let inputConfig;
              try {
                inputConfig = JSON.parse(val.input_config);
              } catch { }

              let headers = [];
              let requestOtherParams = [];
              if (inputConfig) {
                if (inputConfig.headers && inputConfig.headers.dataList && inputConfig.headers.dataList.length > 0) {
                  inputConfig.headers.dataList.forEach(item => {
                    headers.push({ name: item.key, operator: 'EQUAL', expression: item.value });
                  });
                }
                if (inputConfig.body && inputConfig.body.dataList && inputConfig.body.dataList.length > 0) {
                  inputConfig.body.dataList.forEach(item => {
                    requestOtherParams.push({ name: item.key, operator: 'EQUAL', expression: item.value });
                  });
                }
                if (inputConfig.query && inputConfig.query.dataList && inputConfig.query.dataList.length > 0) {
                  inputConfig.query.dataList.forEach(item => {
                    requestOtherParams.push({ name: item.key, operator: 'EQUAL', expression: item.value });
                  });
                }
              }
              let outputConfig;
              try {
                outputConfig = JSON.parse(val.output_config);
              } catch { }

              let columns = [];
              if (outputConfig) {
                if (outputConfig && outputConfig.length > 0) {
                  if (outputConfig[0] && outputConfig[0].children && outputConfig[0].children.length > 0) {
                    columns = outputConfig[0].children.map(item => {
                      return { prop: item.field, label: item.name };
                    });
                  }
                }
              }
              instance.proxy.localVal.headers = headers;
              instance.proxy.localVal.requestOtherParams = requestOtherParams;
              instance.proxy.localVal.outputConfig = outputConfig;
              instance.proxy.innerChange = false;
              instance.proxy.$emit('extract-column', columns);
              getValues();
              getApiFields(val.id);
            }
          }
        },
        {
          prop: 'method',
          component: 'funi-select',
          props: {
            clearable: true,
            options: [
              { label: 'post', value: 'post' },
              { label: 'get', value: 'get' }
            ],
            style: {
              width: '100%'
            },
            onChange: () => {
              getValues();
            }
          },
          label: '调用方法'
        },
        {
          prop: 'headers',
          component: props => {
            return h(ParamSetting, { ...(props || {}) });
          },
          props: {
            style: {
              width: '100%'
            },
            designer: props.designer,
            onChange: () => {
              getValues();
            }
          },
          label: 'Headers'
        },
        {
          prop: 'requestOtherParams',
          component: props => {
            return h(ParamSetting, { ...(props || {}), fieldList: paramsFieldList.value });
          },
          props: {
            style: {
              width: '100%'
            },
            designer: props.designer,
            onChange: () => {
              getValues();
            }
          },
          label: '查询入参'
        }
        // {
        //   label: '提取字段',
        //   prop: 'extractFields',
        //   component: () => {
        //     const ElButton = resolveComponent('el-button');
        //     let icon = '';
        //     if (state.extractStatus === 'success') {
        //       icon = 'CircleCheck'
        //     } else if (state.extractStatus === 'fail') {
        //       icon = 'CircleClose'
        //     }
        //     const button = h(ElButton, {
        //       icon,
        //       type: 'primary', onClick: () => {
        //         extract();
        //       }
        //     }, '提取');
        //     return button;
        //   }
        // }
      ],
      rules: {}
    });
    if (props.isSimple) {
      state.schema.splice(4, 1);
    }

    onMounted(() => {
      if (props.modelValue && props.modelValue.api_id) {
        getApiFields(props.modelValue.api_id);
      }
    })

    const extract = () => {
      instance.proxy
        .extractFields()
        .then(() => {
          state.extractStatus = 'success';
        })
        .catch(() => {
          state.extractStatus = 'success';
        });
    };
    const getValues = () => {
      instance.proxy.$refs.funiFormRef.validate().then(({ isValid, values }) => {
        if (isValid) {
          instance.proxy.localVal = { ...values, apiType, api_url };
        }
      });
    };

    const getApiFields = async (id) => {
      let fields = [];
      const result = await instance.proxy.$lowCodeRequest.postApisInfoAsync({ id });
      if (result && result.input_config) {
        const config = JSON.parse(result.input_config);
        if (config) {
          if (config.body && config.body.dataList && config.body.dataList.length) {
            const bodyFields = config.body.dataList.map(field => {
              return {
                label: field.key,
                value: field.key
              }
            })
            fields.push(...(bodyFields || []));
          }
          if (config.query && config.query.dataList && config.query.dataList.length) {
            const queryFields = config.query.dataList.map(field => {
              return {
                label: field.key,
                value: field.key
              }
            })
            fields.push(...(queryFields || []));
          }
        }
      }
      paramsFieldList.value = fields;
    };

    return {
      ...toRefs(state)
    };
  },
  data () {
    return {
      localVal: {},
      innerChange: false
    };
  },
  mounted () { },
  watch: {
    modelValue: {
      handler (newVal) {
        this.innerChange = true;
        this.localVal = { ...(newVal || {}) };
        this.$nextTick(() => {
          if (this.$refs.funiFormRef) {
            this.$refs.funiFormRef.setValues({
              api_id: '',
              method: '',
              headers: [],
              requestOtherParams: [],
              ...(newVal || {})
            });
          }
          this.innerChange = false;
        });
      },
      immediate: true
    },
    localVal: {
      handler (newVal) {
        if (!this.innerChange) {
          if (newVal && !newVal.id) {
            newVal.id = window.$utils.guid();
            newVal.component_id = window.$utils.guid();
          }
          this.$emit('update:modelValue', newVal);
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async extractFields () {
      const { error, isValid } = await this.$refs.funiFormRef.validate();
      if (isValid && this.localVal && this.localVal.api_url && this.idCollectObject) {
        const { api_url, method, headers, requestOtherParams } = this.localVal;
        let realParams = {};
        let realHeaders = {};
        if (headers && headers.length > 0) {
          realHeaders = this.getRealParams(headers);
        }
        if (requestOtherParams && requestOtherParams.length > 0) {
          realParams = this.getRealParams(requestOtherParams);
        }
        let params = {
          url: api_url,
          method,
          headers: realHeaders,
          params: realParams
        };
        this.$lowCodeRequest.postAppCodeApiListAsynct(this.idCollectObject.app_code, params).then(res => {
          if (res && res.list && res.list.length > 0) {
            let columns = [];
            let obj = res.list[0];
            for (let key in obj) {
              columns.push(key);
            }
            this.$emit('extract', columns);
          }
        });
      }
    }
  },
  getRealParams (requestOtherParams) {
    let realParams = {};
    requestOtherParams.forEach((item, index) => {
      switch (item.type) {
        case 'string':
          realParams[item.name] = String(item.expression);
          break;
        case 'integer':
          realParams[item.name] = Number(item.expression);
          break;
        case 'number':
          realParams[item.name] = Number(item.expression);
          break;
        case 'array':
          realParams[item.name] = Array.from(item.expression);
          break;
      }
    });
    return realParams;
  }
};
</script>
