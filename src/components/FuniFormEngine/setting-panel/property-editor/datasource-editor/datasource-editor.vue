<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-27 16:07:46
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-13 16:48:33
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\datasource-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item label-width="0px"
    style="margin-bottom:0px;">
    <datasource-manager v-model="localVal"
      :isSimple="isSimple"
      :designer="designer"
      :selectedWidget="selectedWidget"
      :isCurd="isCurd"
      @extract="e=>$emit('extract',e)"
      @extract-column="e=>$emit('extract-column',e)" />
  </el-form-item>
</template>

<script>
import DatasourceManager from './datasource-manager.vue';
export default {
  name: 'datasource-editor',
  components: {
    DatasourceManager
  },
  inject: ['getDesignerConfig', 'idCollectObject'],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    isSimple: {
      type: Boolean,
      default: false
    },
    propName: {
      type: String,
      default: 'selectOptions'
    },
    modelValue: {
      type: Object,
      default: () => {
        return {
          type: 'model',
          data: {}
        }
      }
    },
    isCurd: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      designerConfig: this.getDesignerConfig(),
      localVal: {}
    }
  },
  mounted () {

  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.localVal = newVal || {};
      },
      immediate: true
    },
    localVal: {
      handler (newVal) {
        this.$emit('update:modelValue', newVal);
        this.$emit('change', newVal);
      },
      deep: true,
      immediate: true
    }
  },
  methods: {

  },
}
</script>
