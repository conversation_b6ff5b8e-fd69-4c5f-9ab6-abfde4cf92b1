<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-03-13 09:57:41
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-05-06 18:36:40
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\sort-setting.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-button :type="typeComputed"
      round
      icon="Edit"
      @click="handleEdit">编辑</el-button>
    <sort-setting-dialog ref="sortSettingDialogRef"
      :fieldList="options"
      v-model="state.localVal" />
  </div>
</template>
<script setup>
import { deepClone } from '../../../common/utils/util';
import SortSettingDialog from './sort-setting-dialog.vue';
import { computed, nextTick, reactive, ref, watch } from 'vue';

const emit = defineEmits('update:modelValue', 'change')

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => {
      return [];
    }
  },
  options: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const sortSettingDialogRef = ref(null);
let state = reactive({
  localVal: []
});

watch(() => state.localVal, (newVal) => {
  emit('update:modelValue', newVal);
  emit('change', newVal);
})

const typeComputed = computed(() => {
  return state.localVal.length > 0 || (props.modelValue && props.modelValue.length > 0) ? 'primary' : 'default';
})

const handleEdit = () => {
  state.localVal = [...(deepClone(props.modelValue) || [])];
  nextTick(() => {
    sortSettingDialogRef.value.show();
  })
}

</script>
<style lang='scss' scoped>
</style>