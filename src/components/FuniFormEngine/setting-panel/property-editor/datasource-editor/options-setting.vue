<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-10-27 13:47:19
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-10-27 17:07:00
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\options-setting.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="options-setting">
    <option-items-setting :designer="designer"
      :selected-widget="selectedWidget"
      :propName="propName" />
  </div>
</template>

<script>
import OptionItemsSetting from "../../option-items-setting.vue"
export default {
  name: 'options-setting',
  components: {
    OptionItemsSetting
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    propName: {
      type: String,
      default: 'selectOptions'
    }
  },
  data () {
    return {

    }
  },
  mounted () {

  },
  methods: {

  },
}
</script>
