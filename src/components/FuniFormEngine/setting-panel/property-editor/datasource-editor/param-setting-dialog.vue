<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-27 17:37:55
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-08-14 18:22:16
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\param-setting-dialog.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <sfc-design-dialog v-model="dialogVisible"
    destroyOnClose
    title="参数管理"
    width="1000px">
    <funi-curd :columns="columns"
      :pagination="false"
      :data="localVal">
      <template #buttonGroup>
        <el-button icon="CirclePlus"
          type="primary"
          @click="onAdd">新建</el-button>
      </template>
    </funi-curd>
    <template #footer>
      <span>
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary"
          @click="onSure">
          确定
        </el-button>
      </span>
    </template>
  </sfc-design-dialog>
</template>

<script>
import { h, reactive, resolveComponent, toRefs, watch, getCurrentInstance, inject } from 'vue';
import { findAllField, findCurdColumns } from "../../../common/utils/util";
export default {
  name: 'param-setting-dialog',
  components: {
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    fieldList: {
      type: Array,
      default: () => {
        return []
      }
    },
    modelValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    //是否包含运算符
    isOperator: {
      type: Boolean,
      default: false
    }
  },
  setup (props) {
    const instance = getCurrentInstance()
    const idCollectObject = inject('idCollectObject');
    let state = reactive({
      dialogVisible: false,
      columns: [
        {
          label: '序号',
          prop: 'serialNumber',
          width: '60px',
          render: ({ row, index }) => {
            row.serialNumber = index + 1;
            return row.serialNumber;
          }
        },
        {
          label: '参数名', prop: 'name',
          render: ({ row }) => {
            if (props.fieldList && props.fieldList.length > 0) {
              const FuniSelect = resolveComponent('funi-select');
              return h(FuniSelect, {
                modelValue: row.name,
                filterable: true,
                allowCreate: true,
                options: [...props.fieldList],
                placeholder: '请输入参数名',
                onChange: (e) => {
                  row.name = e;
                }
              })
            } else {
              const ElInput = resolveComponent('el-input');
              return h(ElInput, {
                modelValue: row.name,
                placeholder: '请输入参数名',
                onInput: (e) => {
                  row.name = e;
                }
              })
            }
          }
        },
        {
          label: '运算符', prop: 'operator',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.operator,
              placeholder: '请选择运算符',
              typeCode: 'ASAPP_OPERATOR',
              isLocal: true,
              onChange: (e) => {
                row.operator = e;
              }
            })
          }
        },
        {
          label: '参数值', prop: 'expression',
          render: ({ row }) => {
            const ElInput = resolveComponent('el-input');
            const FuniVariableSetter = resolveComponent('FuniVariableSetter');
            let fieldVariables = [];
            if (props.fieldList && props.fieldList.length > 0) {
              let fIndex = props.fieldList.findIndex(item => item.value === row.name);
              if (fIndex >= 0) {
                let configStr = props.fieldList[fIndex].config;
                let config = {};
                try {
                  config = JSON.parse(configStr);
                } catch { }
                // if (config.correlation_model) {
                //   fieldVariables.push(
                //     {
                //       id: 'correlationModel',
                //       label: '关联模型',
                //       children: [
                //         { id: 'routeId', label: 'id', expression: 'context.query.id' }
                //       ]
                //     },
                //   )
                // }
              }
            }
            if (props.designer) {
              const widgetList = findAllField(props.designer.widgetList);
              const children = widgetList.map(item => {
                return {
                  id: window.$utils.guid(), label: item.label, expression: `context.formData.${item.name}`
                }
              })
              let formChildren = children;
              if (
                idCollectObject &&
                idCollectObject.previousTabFields &&
                idCollectObject.previousTabFields.previous &&
                idCollectObject.previousTabFields.previous.length > 0
              ) {
                let stepFormChildren = [];
                let preSteps = idCollectObject.previousTabFields.previous;

                //添加前面步骤的表单字段
                preSteps.forEach(element => {
                  if (element.tabTitle && element.fields && element.fields.length > 0) {
                    let stepChildren = element.fields.map(item => {
                      return {
                        id: window.$utils.guid(),
                        label: item.name,
                        expression: `context.stepData?.step${element.tabIndex}?.${item.key}`
                      };
                    });
                    let stepFormItem = {
                      id: 'stepFormData' + element.tabIndex,
                      label: element.tabTitle,
                      children: stepChildren
                    };
                    stepFormChildren.push(stepFormItem);
                  }
                });

                formChildren = [
                  ...stepFormChildren,
                  {
                    id: 'currentFomrData',
                    label: idCollectObject.previousTabFields.activeTitle,
                    children
                  }
                ];
              }

              fieldVariables.push({
                id: 'formData',
                label: '表单字段',
                children: formChildren
              })
            }
            if (props.designer && props.designer.widgetList && props.designer.widgetList.length > 0 && props.selectedWidget) {
              let curdColumns = findCurdColumns(props.designer.widgetList, props.selectedWidget);
              if (curdColumns && curdColumns.length > 0) {
                const children = curdColumns.map(item => {
                  return {
                    id: window.$utils.guid(), label: item.label, expression: `context.params.row.${item.name}`
                  }
                })
                fieldVariables.push({
                  id: 'rowData',
                  label: '行数据',
                  children
                })
              }
            }
            return h(FuniVariableSetter, {
              modelValue: row.expression,
              variables: [
                {
                  id: 'route',
                  label: '路由参数',
                  children: [
                    { id: 'routeId', label: 'id', expression: 'context.query.id' },
                    { id: 'routeBusinessId', label: '业务id', expression: 'context.query.businessId' },
                    { id: 'routeCid', label: '关联id', expression: 'context.query.cid' },
                    { id: 'routeBizName', label: '操作类型', expression: 'context.query.bizName' }
                  ]
                },
                ...fieldVariables
              ],
              onChange: (e) => {
                row.expression = e;
              }
            });
          }
        },
        {
          label: '说明', prop: 'Instructions', render: ({ row }) => {
            const ElInput = resolveComponent('el-input');
            return h(ElInput, {
              modelValue: row.Instructions,
              placeholder: '请输入说明',
              onInput: (e) => {
                row.Instructions = e;
              }
            })
          }
        },
        {
          label: '操作', prop: 'action', width: '100px',
          render: ({ row, index }) => {
            const ELButton = resolveComponent('el-button');
            return h('div', {}, [
              h(ELButton, {
                icon: 'Remove', type: 'primary', link: true, onClick: () => {
                  state.localVal.splice(index, 1);
                }
              })
            ])
          },
        },
      ],
      localVal: []
    })

    if (!props.isOperator) {
      state.columns.splice(2, 1);
    }
    watch(props.modelValue, (newVal) => {
      state.localVal = newVal || [];
    }, { immediate: true })

    const show = () => {
      state.localVal = [...(props.modelValue || [])];
      state.dialogVisible = true;
    }
    const onAdd = () => {
      state.localVal.push({});
    }
    const onCancel = () => {
      state.dialogVisible = false;
    }
    const onSure = () => {
      instance.proxy.$emit('update:modelValue', [...(state.localVal || [])]);
      state.dialogVisible = false;
    }
    return {
      ...toRefs(state),
      onAdd,
      show,
      onCancel,
      onSure
    }
  },
  data () {
    return {

    }
  },
  mounted () {

  },
  watch: {

  },
  methods: {

  },
}
</script>
