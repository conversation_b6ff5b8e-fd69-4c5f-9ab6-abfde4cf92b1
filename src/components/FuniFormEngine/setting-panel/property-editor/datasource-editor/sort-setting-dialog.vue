<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-28 11:25:37
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-13 16:30:14
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\sort-setting-dialog.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <sfc-design-dialog v-if="dialogVisible"
    v-model="dialogVisible"
    destroy-on-close
    title="排序设置"
    width="800px"
    style="padding:0px 8px">
    <funi-curd :columns="columns"
      :pagination="false"
      :data="state.localVal">
      <template #header>
        <div style="height: 100%; display: flex; align-items: center"></div>
      </template>
      <template #buttonGroup>
        <el-button icon="CirclePlus"
          type="primary"
          @click="onAdd">新建</el-button>
      </template>
    </funi-curd>
    <template #footer>
      <span>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary"
          @click="onSave">
          保存
        </el-button>
      </span>
    </template>
  </sfc-design-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { reactive, ref, shallowReactive, resolveComponent, h, watch } from 'vue';

const emit = defineEmits('update:modelValue', 'change')

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => {
      return [];
    }
  },
  fieldList: {
    type: Array,
    default: () => {
      return []
    }
  },
});

const dialogVisible = ref(false);
let state = reactive({
  localVal: []
});

const columns = reactive([
  {
    label: '序号',
    prop: 'serialNumber',
    width: '60px',
    render: ({ row, index }) => {
      return index + 1;
    }
  },
  {
    label: '排序字段',
    prop: 'key',
    render: ({ row, index }) => {
      const FuniSelect = resolveComponent('funi-select');
      return h(FuniSelect, {
        clearable: true,
        options: [...(props.fieldList || [])],
        modelValue: row.key,
        onChange: (val) => {
          row.key = val;
        }
      })
    }
  },
  {
    label: '排序方式',
    prop: 'mode',
    render: ({ row, index }) => {
      const ElRadioGroup = resolveComponent('el-radio-group');
      const ElRadio = resolveComponent('el-radio');
      return h(ElRadioGroup, {
        modelValue: row.mode,
        onChange: (val) => {
          row.mode = val;
        }
      }, [
        h(ElRadio, {
          label: 'asc',
          value: 'asc'
        }, '升序'),
        h(ElRadio, {
          label: 'desc',
          value: 'desc'
        }, '降序')
      ])
    }
  },
  {
    label: '操作', prop: 'action', width: '100px',
    render: ({ row, index }) => {
      const ELButton = resolveComponent('el-button');
      return h('div', {}, [
        h(ELButton, {
          icon: 'Remove', type: 'primary', link: true, onClick: () => {
            state.localVal.splice(index, 1);
          }
        })
      ])
    },
  },
]);

watch(() => props.modelValue, (newVal) => {
  state.localVal = [...(newVal || [])];
}, { immediate: true })

const show = () => {
  dialogVisible.value = true;
}

const onAdd = () => {
  state.localVal.push({});
}

const onSave = () => {
  emit('update:modelValue', state.localVal);
  emit('change', state.localVal);
  dialogVisible.value = false;
}

defineExpose({ show })

</script>
<style lang='scss' scoped>
</style>