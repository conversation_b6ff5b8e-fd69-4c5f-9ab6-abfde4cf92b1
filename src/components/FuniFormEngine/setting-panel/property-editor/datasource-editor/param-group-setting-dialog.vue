<template>
  <sfc-design-dialog class="param-group-setting-dialog"
    v-model="dialogVisible"
    destroyOnClose
    append-to-body
    title="数据筛选"
    width="1000px">
    <div>
      <el-row v-for="(item,index) in localVal"
        :key="index">
        <ParamGroup :hideLogic="index===0"
          :fieldList="fieldList"
          :variables="variables"
          :modelValue="item"
          @goupLogChange="val=>item.logicalOperator=val"
          @clear="onClear(index)" />
      </el-row>
      <el-row class="param-group-setting-dialog-row">
        <el-divider>
          <el-button type="primary"
            link
            @click="addGroup">添加分组</el-button>
        </el-divider>
      </el-row>
    </div>
    <template #footer>
      <span>
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary"
          @click="onSure">
          确定
        </el-button>
      </span>
    </template>
  </sfc-design-dialog>
</template>

<script>
import { getCurrentInstance, reactive, toRefs } from 'vue';
import ParamGroup from './param-group.vue';
import fieldVariablesMixins from "../fieldVariablesMixins";
export default {
  name: 'param-group-setting-dialog',
  mixins: [fieldVariablesMixins],
  components: {
    ParamGroup
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    fieldList: {
      type: Array,
      default: () => {
        return []
      }
    },
    modelValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    //是否包含运算符
    isOperator: {
      type: Boolean,
      default: false
    }
  },
  setup (props) {
    const instance = getCurrentInstance()
    let state = reactive({
      dialogVisible: false,
      localVal: []
    })
    const show = () => {
      state.localVal = [...(props.modelValue || [])];
      if (!(state.localVal && state.localVal.length > 0)) {
        state.localVal.push({
          logicalOperator: '',
          conditions: [{ operator: 'EQUAL' }]
        });
      }
      state.dialogVisible = true;
    }

    const addGroup = () => {
      state.localVal.push({
        logicalOperator: 'AND',
        conditions: [{ operator: 'EQUAL' }]
      });
    }

    const onClear = (index) => {
      state.localVal.splice(index, 1);
    }

    const onCancel = () => {
      state.dialogVisible = false;
    }

    const onSure = () => {
      let success = true;
      let message = ''
      if (state.localVal && state.localVal.length > 0) {
        for (let i = 0; i < state.localVal.length; i++) {
          let group = state.localVal[i];
          if (group.conditions && group.conditions.length > 0) {
            for (let j = 0; j < group.conditions.length; j++) {
              const condition = group.conditions[j];
              if (!(condition && condition.key)) {
                success = false;
                message = '请完善条件的参数';
              } else if (!['IS_NULL', 'IS_NOT_NULL'].includes(condition.operator) && !condition.value) {
                success = false;
                message = '请完善条件的值';
              }
            }
            if (!success) {
              break;
            }
          } else {
            success = false;
            message = '请添加条件';
            break;
          }
        }
      }
      if (success) {
        instance.proxy.$emit('update:modelValue', [...(state.localVal || [])]);
        state.dialogVisible = false;
      } else {
        instance.proxy.$notify({ message, type: 'error' });
      }
    }
    return {
      ...toRefs(state),
      show,
      addGroup,
      onClear,
      onCancel,
      onSure
    }
  },
  data () {
    return {
      variables: []
    }
  },
  mounted () {

  },
  methods: {

  },
}
</script>
<style lang="scss" scoped>
.param-group-setting-dialog {
  &-row {
    width: 100%;
  }
}
</style>
