<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-27 21:03:23
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-19 15:44:25
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\sql-setting.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-form ref="funiFormRef"
    :labelWidth="labelWidth"
    :schema="schema"
    :rules="rules" />
</template>

<script>
import { h, inject, reactive, toRefs, resolveComponent, onMounted, getCurrentInstance, ref } from 'vue';
import { useRoute } from 'vue-router';
import ParamSetting from './param-setting.vue';
export default {
  name: 'sql-setting',
  components: {},
  inject: ['idCollectObject'],
  props: {
    labelWidth: [String, Number],
    designer: Object,
    isSimple: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: Object
    }
  },
  setup (props) {
    const instance = getCurrentInstance();
    const route = useRoute();
    const designerConfig = inject('getDesignerConfig')();
    const idCollectObject = inject('idCollectObject');
    let sqlList = [];
    let sql_url = '';
    const paramsFieldList = ref([]);
    const state = reactive({
      extractStatus: '',
      schema: [
        {
          prop: 'sql_id',
          component: 'funi-select',
          label: 'SQL',
          props: {
            action: () => {
              return new Promise((resovle, reject) => {
                instance.proxy.$lowCodeRequest.postSqlFindAllAsync({ app_id: route.query.id })
                  .then(res => {
                    let list = [];
                    if (res && res.list && res.list.length > 0) {
                      res.list.forEach(item => {
                        list.push({ ...item, label: item.name, value: item.id });
                      })
                    }
                    sqlList = list;
                    resovle(list);
                  })
              })
            },
            filterable: true,
            clearable: true,
            onChangeItem: (val) => {
              instance.proxy.innerChange = true;
              sql_url = val.content;
              instance.proxy.localVal.sql_id = val.id;
              instance.proxy.localVal.method = val.method?.toLowerCase();
              let params;
              try {
                params = JSON.parse(val.params);
              } catch { }

              let requestOtherParams = [];
              if (params && params.length > 0) {
                params.forEach(item => {
                  requestOtherParams.push({ name: item.key, operator: 'EQUAL', expression: item.value })
                })
              }
              let outputConfig;
              try {
                outputConfig = JSON.parse(val.output_config);
              } catch { }

              let columns = [];
              if (outputConfig) {
                if (outputConfig && outputConfig.length > 0) {
                  if (outputConfig[0] && outputConfig[0].children && outputConfig[0].children.length > 0) {
                    columns = outputConfig[0].children.map(item => {
                      return { prop: item.field, label: item.name }
                    })
                  }
                }
              }
              instance.proxy.localVal.requestOtherParams = requestOtherParams;
              instance.proxy.innerChange = false;
              instance.proxy.$emit('extract-column', columns);
              getValues();
              getSqlFields(val.id);
            },
            onChange: (val) => {
              if (!val) {
                instance.proxy.innerChange = true;
                instance.proxy.localVal.sql_id = '';
                instance.proxy.innerChange = false;
                getValues();
                return;
              }
            }
          }
        },
        {
          prop: 'requestOtherParams',
          component: props => {
            return h(ParamSetting, { ...(props || {}), fieldList: paramsFieldList.value });
          },
          props: {
            designer: props.designer,
            style: {
              width: '100%'
            },
            onChange: () => {
              getValues();
            }
          },
          label: '查询入参'
        },
        // {
        //   label: '提取字段',
        //   prop: 'extractFields',
        //   component: () => {
        //     const ElButton = resolveComponent('el-button');
        //     let icon = '';
        //     if (state.extractStatus === 'success') {
        //       icon = 'CircleCheck';
        //     } else if (state.extractStatus === 'fail') {
        //       icon = 'CircleClose';
        //     }
        //     const button = h(
        //       ElButton,
        //       {
        //         icon,
        //         type: 'primary',
        //         onClick: () => {
        //           extract();
        //         }
        //       },
        //       '提取'
        //     );
        //     return button;
        //   }
        // }
      ],
      rules: {
        sql_url: [
          {
            trigger: 'change',
            validator: (rule, value, callback) => {
              const exg = /UPDATE|INSERT|DELETE|CREATE|RENAME|DROP/i;
              if (exg.test(value)) {
                callback(new Error(`不能输入关键字${value.match(exg)[0]}`));
              } else {
                callback();
              }
            }
          }
        ]
      }
    });
    if (props.isSimple) {
      state.schema.splice(2, 1);
    }
    onMounted(() => {
      if (props.modelValue && props.modelValue.api_id) {
        getSqlFields(props.modelValue.api_id);
      }
    })
    const extract = () => {
      getValues();
      instance.proxy
        .sqlExtractClick()
        .then(() => {
          state.extractStatus = 'success';
        })
        .catch(() => {
          state.extractStatus = 'success';
        });
    };
    const getValues = () => {
      instance.proxy.$refs.funiFormRef.validate().then(({ isValid, values }) => {
        if (isValid) {
          instance.proxy.localVal = { sql_id: '', requestOtherParams: [], ...values, sql_url };
        }
      });
    };

    const getSqlFields = async (id) => {
      let fields = [];
      const result = await instance.proxy.$lowCodeRequest.postSqlfindByIdAsync(id);
      if (result.params && result.params) {
        const params = JSON.parse(result.params);
        const bodyFields = params.map(field => {
          return {
            label: field.key,
            value: field.key
          }
        })
        fields.push(...(bodyFields || []));
      }
      paramsFieldList.value = fields;
    };

    return {
      ...toRefs(state)
    };
  },
  data () {
    return {
      localVal: {},
      innerChange: false
    };
  },
  mounted () { },
  watch: {
    modelValue: {
      handler (newVal) {
        this.innerChange = true;
        this.localVal = { ...(newVal || {}) };
        this.$nextTick(() => {
          if (this.$refs.funiFormRef) {
            this.$refs.funiFormRef.setValues({ ...(newVal || {}) });
          }
          this.innerChange = false;
        });
      },
      immediate: true
    },
    localVal: {
      handler (newVal) {
        if (!this.innerChange) {
          if (newVal && !newVal.id) {
            if (newVal.sql_id) {
              newVal.id = newVal.sql_id;
            } else {
              newVal.id = window.$utils.guid();
            }
            newVal.component_id = window.$utils.guid();
          }
          this.$emit('update:modelValue', newVal);
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // sql提取字段
    async sqlExtractClick () {
      const { error, isValid } = await this.$refs.funiFormRef.validate();
      if (isValid && this.localVal && this.localVal.id && this.idCollectObject) {
        const { id, sql_url, requestOtherParams, component_id } = this.localVal;
        let realParams = {};
        if (requestOtherParams && requestOtherParams.length > 0) {
          requestOtherParams.forEach((item, index) => {
            switch (item.type) {
              case 'string':
                realParams[item.name] = String(item.expression);
                break;
              case 'integer':
                realParams[item.name] = Number(item.expression);
                break;
              case 'number':
                realParams[item.name] = Number(item.expression);
                break;
              case 'array':
                realParams[item.name] = Array.from(item.expression);
                break;
            }
          });
        }
        let params = {
          id,
          app_id: this.idCollectObject.app_id,
          page_id:
            this.idCollectObject.pageType === 'edit' || this.idCollectObject.pageType === 'list'
              ? this.idCollectObject.edit_page_id
              : this.idCollectObject.pageType === 'detail'
                ? this.idCollectObject.detail_page_id
                : '',
          component_id,
          content: sql_url,
          params: JSON.stringify(requestOtherParams)
        };
        this.$lowCodeRequest.postPageSqlUpdateAsync(params).then(res => {
          this.$lowCodeRequest.postSqlListAsync(this.idCollectObject.app_code, { sql_id: id, params: realParams }).then(res1 => {
            if (res1 && res1.list && res1.list.length > 0) {
              let columns = [];
              let obj = res1.list[0];
              for (let key in obj) {
                columns.push(key);
              }
              this.$emit('extract', columns);
            }
          });
        });
      }
    }
  }
};
</script>
