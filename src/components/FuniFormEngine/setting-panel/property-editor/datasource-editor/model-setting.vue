<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-27 17:00:35
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-19 10:09:35
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\model-setting.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-form ref="funiFormRef"
    :labelWidth="labelWidth"
    :schema="schema"
    :rules="rules" />
</template>

<script>
import { h, inject, reactive, toRefs, getCurrentInstance, nextTick, ref } from 'vue'
import ParamGroupSetting from './param-group-setting.vue';
import SortSetting from './sort-setting.vue';
export default {
  name: 'model-setting',
  components: {
  },
  props: {
    labelWidth: [String, Number],
    designer: Object,
    selectedWidget: Object,
    isSimple: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: Object
    },
    showLabelKey: {
      type: Boolean,
      default: false
    },
    isCurd: {
      type: Boolean,
      default: false
    },
    requestOtherParam: [Object]
  },
  setup (props) {
    const instance = getCurrentInstance();
    const designerConfig = inject('getDesignerConfig')();
    const idCollectObject = inject('idCollectObject');
    let paramFieldList = reactive([]);
    const paramSettingStr = ref('');
    const initSchema = () => {
      let list = [
        {
          prop: 'model_id',
          component: 'funi-select',
          props: {
            clearable: true,
            action: () => {
              return new Promise((resovle) => {
                window.$http.post(`${designerConfig.relevanceUrl}`, { app_id: idCollectObject.app_id }).then(res => {
                  instance.proxy.modelList = res.list;
                  resovle(res.list);
                })
              })
            },
            onChange: (e) => {
              if (e) {
                initFieldsOptions(e, true);
              } else {
                instance.proxy.$refs.funiFormRef.setValues({ sortFields: [], labelKey: '' })
              }
            },
            style: {
              width: '100%'
            }
          },
          label: '数据模型'
        },
        {
          prop: 'filterParams',
          component: () => {
            return h(ParamGroupSetting, {
              fieldList: paramFieldList,
              valueStr: paramSettingStr.value,
              designer: props.designer,
              selectedWidget: props.selectedWidget,
              isOperator: true,
              isCurd: props.isCurd,
              style: {
                width: '100%'
              },
              onChange: () => {
                getValues();
              }
            })
          },
          label: '数据筛选'
        },
        {
          prop: 'sortFields',
          component: ({ item }) => {
            console.log(item);
            return h(SortSetting, { ...(item.props || {}) });
          },
          props: {
            options: paramFieldList,
            onChange: (val) => {
              getValues();
            }
          },
          label: '排序字段'
        }
      ];
      if (props.showLabelKey) {
        list.push(
          {
            prop: 'labelKey',
            component: 'funi-select',
            props: {
              clearable: true,
              options: paramFieldList,
              style: {
                width: '100%'
              },
              onChange: () => {
                getValues();
              }
            },
            label: '显示字段'
          },
        )
        list.push(
          {
            prop: 'valueKey',
            component: 'funi-select',
            props: {
              clearable: true,
              options: paramFieldList,
              style: {
                width: '100%'
              },
              onChange: () => {
                getValues();
              }
            },
            label: '赋值字段'
          },
        )
      } else {
        list.push(
          {
            prop: 'isCorrelation',
            component: 'el-switch',
            props: {
              onChange: () => {
                getValues();
              }
            },
            label: '启用CID关联查询'
          },
        )
      }
      return list;
    };
    const state = reactive({
      schema: initSchema(),
      rules: {
        // model_id: [{ required: true, message: '请选择模型', trigger: 'blur' }]
      }
    })
    const getValues = () => {
      instance.proxy.$refs.funiFormRef.validate().then(({ isValid, values }) => {
        if (isValid) {
          instance.proxy.localVal = { ...values };
        }
      })
    }
    const initFieldsOptions = (e, isSelect = false) => {
      const config = props.modelValue;
      let modeCode;
      if (instance.proxy.modelList && instance.proxy.modelList.length > 0) {
        let fIndex = instance.proxy.modelList.findIndex(m => m.value === e);
        if (fIndex >= 0) {
          instance.proxy.modelName = instance.proxy.modelList[fIndex].label;
          modeCode = instance.proxy.modelList[fIndex].code;
        }
      }
      //存在中间表且选中的是中间表则拉取关联模型的字段
      // let model_id = config && config.dataType === 'many_to_many' && e === config.middleModelId && config.correlationModelId ? config.correlationModelId : e;
      let model_id = e;
      instance.proxy.$lowCodeRequest.postFieldFindAllTreeAsync({ model_id }).then(res => {
        let fieldList = [];
        res.list.forEach(item => {
          let tableName = '';
          if (item.config) {
            let fieldConfig;
            try {
              fieldConfig = JSON.parse(item.config);
            } catch { }
            if (item.data_type === 'many_to_many') {
              tableName = fieldConfig.association_table_model;
            } else if (fieldConfig && fieldConfig.correlation_model) {
              tableName = fieldConfig.correlation_model;
            }
          }
          let fieldItemLabel = item.name;
          let fieldItemValue = item.code;
          let fieldItem = {
            label: fieldItemLabel,
            value: fieldItemValue,
            code: item.code,
            config: item.config,
            children: (item.children || []).map(child => {
              return {
                label: child.name,
                value: `${tableName ? `${tableName}.` : ''}${child.code}`,
                code: child.code,
                config: child.config,
                joinFieldName: child.joinFieldName,
              }
            })
          };
          fieldList.push(fieldItem);
        });
        state.schema[2].props.options = fieldList;
        if (state.schema.length >= 4) {
          state.schema[3].props.options = fieldList;
          let fIndex = fieldList.findIndex(f => {
            let config = {};
            try {
              config = JSON.parse(f.config);
            } catch { }
            return config.is_primary_key === 1;
          });
          if (fIndex >= 0 && (isSelect || !(instance.proxy.localVal && instance.proxy.localVal.labelKey))) {//仅下拉的时候设置主列为显示字段
            let labelKey = fieldList[fIndex].value;
            instance.proxy.$refs.funiFormRef.setValues({ labelKey });
          }
        }
        if (state.schema.length >= 5) {
          state.schema[4].props.options = fieldList;
        }
        paramFieldList = fieldList;
        state.schema = initSchema();
        getValues();
      });
      //如果是主表一对多的情况
      if (modeCode && config.dataType !== 'many_to_many' && props.designer && props.designer.formConfig && props.designer.formConfig.modelId && !props.isSimple) {
        window.$http.post(`${designerConfig.modelFieldUrl}`, { model_id: props.designer.formConfig.modelId }).then(res1 => {
          let fIndex = res1.list.findIndex(item => {
            let isFind = false;
            if (item.config) {
              let config = {};
              try {
                config = JSON.parse(item.config);
              } catch { }
              if (modeCode === config.correlation_model) {
                isFind = true;
              }
            }
            return isFind;
          });
        })
      } else if (config && config.dataType === 'many_to_many' && e === config.middleModelId) {//找关联表id在中间表中的字段名
        window.$http.post(`${designerConfig.modelFieldUrl}`, { model_id: e })
          .then(res => {
            let mFieldName;
            if (res && res.list && res.list.length > 0) {
              let correlationCol = res.list.find(item => {
                let configObj = {};
                try {
                  configObj = JSON.parse(item.config);
                } catch { }
                return configObj.correlation_model === config.correlationModelCode;
              });
              if (correlationCol) {
                mFieldName = correlationCol.code;
              }
            }
            instance.proxy.localVal.mFieldName = mFieldName;
          })
      }
      instance.proxy.$refs.funiFormRef.setValues({ sortFields: [], labelKey: '', valueKey: '' })
    }
    return {
      ...toRefs(state),
      initFieldsOptions
    }
  },
  data () {
    return {
      localVal: {},
      modelList: [],
      modelName: '',
      innerChange: false,
      initModelId: ''
    }
  },
  mounted () {
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.innerChange = true;
        if (newVal && (newVal.isCorrelation === null || newVal.isCorrelation === undefined)) {
          //兼容一下没有设置关联的老数据
          newVal.isCorrelation = true;
        }
        this.localVal = { ...(newVal || {}) };
        if (newVal && newVal.modelName) {
          this.modelName = newVal.modelName;
        }
        this.$nextTick(() => {
          if (this.$refs.funiFormRef) {
            //每个modeId只初始化一次避免重复请求
            if (newVal && newVal.model_id && newVal.model_id !== this.initModelId) {
              this.initModelId = newVal.model_id;
              this.initFieldsOptions(newVal.model_id);
            }
            this.$refs.funiFormRef.setValues({ model_id: '', filterParams: [], sortFields: [], ...(newVal || {}) });
          }
          this.innerChange = false;
        })
      },
      immediate: true
    },
    localVal: {
      handler (newVal) {
        if (!this.innerChange) {
          const { modelName } = this;
          if (newVal && !newVal.id) {
            newVal.id = window.$utils.guid();
            newVal.component_id = window.$utils.guid();
          }
          this.$emit('update:modelValue', { ...newVal, modelName });
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {

  },
}
</script>
