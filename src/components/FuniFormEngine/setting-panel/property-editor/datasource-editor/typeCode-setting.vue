<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-10-27 13:46:47
 * @LastEditors: fengyi <EMAIL>
 * @LastEditTime: 2023-10-30 09:31:28
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\datasource-editor\typeCode-setting.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="typeCode-setting">
    <span><span class="type-code-label">字典编码:</span>
      <funi-select v-model="typeCode"
        :action="typeCodeAction"
        remote
        filterable
        clearable />
    </span>
  </div>
</template>

<script>
export default {
  name: 'typeCode-setting',
  components: {
  },
  inject: ['idCollectObject'],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    propName: {
      type: String,
      default: 'optionItems'
    }
  },
  data () {
    return {
      typeCode: this.selectedWidget?.options?.typeCode
    }
  },
  mounted () {

  },
  watch: {
    'selectedWidget.options': {
      deep: true,
      handler (val) {
        this.typeCode = this.selectedWidget.options.typeCode
      }
    },
    typeCode: {
      handler (newVal) {
        Object.assign(this.selectedWidget.options, { typeCode: newVal });
      }
    }
  },
  methods: {
    typeCodeAction (query) {

      return new Promise((resolve, reject) => {
        let isLocal = true;
        let designerConfig = {};
        const designerConfigStr = localStorage.getItem('designerConfig');
        if (designerConfigStr) {
          try {
            designerConfig = JSON.parse(designerConfigStr);
            isLocal = designerConfig.hasModel
          } catch { }
        }
        if (isLocal) {
          let list = [];
          if (this.idCollectObject && this.idCollectObject.enumSource) {
            if (query) {
              list = this.idCollectObject.enumSource.filter(item => item.name?.indexOf(query) >= 0).map(item => {
                return { label: item.name, value: item.code }
              })
            } else {
              list = this.idCollectObject.enumSource.map(item => {
                return { label: item.name, value: item.code }
              })
            }
          }
          resolve(list);
        } else {
          this.$lowCodeRequest.postOpsDicTypeListAsync({ name: query, pageNo: 1, pageSize: 20 })
            .then(res => {
              if (res && res.list && res.list.length > 0) {
                let list = res.list.map(item => {
                  return { label: item.name, value: item.code }
                })
                resolve(list);
              }
            })
        }
      })
    }
  },
}
</script>
<style lang="scss" scoped>
.typeCode-setting {
  padding-bottom: 16px;
  .type-code-label {
    color: var(--el-text-color-regular);
    margin-right: 8px;
  }
}
</style>
