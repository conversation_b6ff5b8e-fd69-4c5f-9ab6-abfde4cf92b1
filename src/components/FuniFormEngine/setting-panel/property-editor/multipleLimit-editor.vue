<template>
  <el-form-item :label="i18nt('designer.setting.multipleLimit')">
    <FuniVariableSetter class="setting-variable"
      :modelValue="optionModel.multipleLimit"
      :variables="variables"
      @change="handleChange"
      style="width: 100%" />
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"
import fieldVariablesMixins from './fieldVariablesMixins';
import { isNotNullExpression } from '../../common/utils/util';

export default {
  name: "multipleLimit-editor",
  mixins: [i18n, fieldVariablesMixins],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      variables: [],
    }
  },
  methods: {
    handleChange (e) {
      let newVal = e;
      if (e && !isNotNullExpression(e)) {
        newVal = '';
      }
      if (this.optionModel) {
        this.optionModel.multipleLimit = newVal;
      }
    }
  }
}
</script>

<style scoped>
</style>
