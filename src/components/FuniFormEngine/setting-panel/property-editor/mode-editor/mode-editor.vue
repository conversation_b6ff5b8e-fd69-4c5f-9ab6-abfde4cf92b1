<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-04-17 10:35:17
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-04-17 10:47:23
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\mode-editor\mode-editor.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.mode')">
    <funi-select v-model="optionModel.mode"
      :options="options"></funi-select>
  </el-form-item>
</template>
<script>
import i18n from "../../../common/utils/i18n";

export default {
  name: "rich-editor-contentHeight-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      options: [{ label: '普通模式', value: '1' }, { label: 'MarkDown模式', value: '2' }]
    }
  }
}
</script>
<style lang='scss' scoped>
</style>