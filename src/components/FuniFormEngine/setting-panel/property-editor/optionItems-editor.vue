<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-18 19:20:28
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\optionItems-editor.vue
 * @Description: 
-->
<template>
  <el-form-item label-width="0">
    <el-divider class="custom-divider-margin-top"><span style="font-weight:bold;">{{propName==='optionItems'?i18nt('designer.setting.optionsSetting'):'选项设置'}}</span></el-divider>
    <el-tabs v-model="activeName">
      <el-tab-pane name="typeCode"
        label="枚举">
        <span><span class="type-code-label">字典编码:</span><funi-select v-model="typeCode"
            :action="typeCodeAction"
            remote
            filterable
            clearable /></span>
      </el-tab-pane>
      <el-tab-pane name="custom"
        label="自定义">
        <option-items-setting :designer="designer"
          :selected-widget="selectedWidget"
          :propName="propName" />
      </el-tab-pane>
      <el-tab-pane v-if="apiShow"
        label="APIs"
        name="api">
        <span><span class="type-code-label">APIs:</span><funi-select v-model="apiUrl"
            :action="urlAction"
            remote
            filterable
            clearable /></span>
      </el-tab-pane>
    </el-tabs>
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"
import OptionItemsSetting from "../option-items-setting.vue"

export default {
  name: "optionItems-editor",
  mixins: [i18n],
  inject: ['idCollectObject'],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    propName: {
      type: String,
      default: 'optionItems'
    }
  },
  components: {
    OptionItemsSetting,
  },
  data () {
    return {
      key: 0,
      activeName: this.selectedWidget.options.dataSource || 'typeCode',
      typeCode: this.selectedWidget.options.typeCode,
      apiUrl: this.selectedWidget.options.api_url,
    }
  },

  watch: {
    'selectedWidget.options': {
      deep: true,
      handler (val) {
        this.activeName = this.selectedWidget.options.dataSource || 'typeCode';
        this.typeCode = this.selectedWidget.options.typeCode
      }
    },
    activeName: {
      handler (newVal) {
        Object.assign(this.selectedWidget.options, { dataSource: newVal });
      }
    },
    typeCode: {
      handler (newVal) {
        Object.assign(this.selectedWidget.options, { typeCode: newVal });
      }
    },
    apiUrl: {
      handler (newVal) {
        Object.assign(this.selectedWidget.options, { api_url: newVal });
      }
    }
  },

  computed: {
    apiShow () {
      return this.selectedWidget && ['cascader'].includes(this.selectedWidget.type);
    }
  },

  methods: {
    /**
     * 根据查询条件获取选项列表。
     * 如果本地存储中有设计者配置并且包含模型，则从本地枚举源中筛选并返回匹配的选项列表。
     * 否则，通过低代码请求从服务器获取选项列表。
     * @param {string} query - 查询条件，用于筛选选项名称。
     * @returns {Promise<Array>} - 返回一个包含选项的数组，每个选项包含 label 和 value 属性。
     */
    typeCodeAction (query) {
      return new Promise((resolve, reject) => {
        let isLocal = true;
        let designerConfig = {};
        const designerConfigStr = localStorage.getItem('designerConfig');
        if (designerConfigStr) {
          try {
            designerConfig = JSON.parse(designerConfigStr);
            isLocal = designerConfig.hasModel
          } catch { }
        }
        if (isLocal) {
          let list = [];
          if (this.idCollectObject && this.idCollectObject.enumSource) {
            if (query) {
              list = this.idCollectObject.enumSource.filter(item => item.name?.indexOf(query) >= 0).map(item => {
                return { label: item.name, value: item.code }
              })
            } else {
              list = this.idCollectObject.enumSource.map(item => {
                return { label: item.name, value: item.code }
              })
            }
          }
          resolve(list);
        } else {
          this.$lowCodeRequest.postOpsDicTypeListAsync({ name: query, pageNo: 1, pageSize: 20 })
            .then(res => {
              if (res && res.list && res.list.length > 0) {
                let list = res.list.map(item => {
                  return { label: item.name, value: item.code }
                })
                resolve(list);
              }
            })
        }
      })
    },

    urlAction (query) {
      return new Promise((resolve, reject) => {
        this.$lowCodeRequest.postApisListAsync({ app_id: this.idCollectObject.app_id, name: query, pageNo: 1, pageSize: 20 })
          .then(res => {
            if (res && res.list && res.list.length > 0) {
              let list = res.list.map(item => {
                return { label: item.name, value: item.url }
              })
              resolve(list);
            }
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.type-code-label {
  color: var(--el-text-color-regular);
  margin-right: 8px;
}
</style>
