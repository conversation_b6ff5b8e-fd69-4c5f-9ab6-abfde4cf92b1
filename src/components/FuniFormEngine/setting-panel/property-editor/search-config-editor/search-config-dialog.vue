<template>
  <div class="search-config-dialog">
    <el-button size="small"
      link
      icon="Edit"
      @click="handleClick"
      :type="localVal&&localVal.length>0?'primary':'default'">编辑</el-button>
    <sfc-design-dialog :title="title"
      v-model="dialogVisible"
      width="65%"
      destroy-on-close
      :show-close="true">
      <div>
        <funi-curd size="default"
          :columns="columns"
          :pagination="false"
          :draggable="true"
          :data="localVal"
          @draggableEnd="handleDraggableEnd">
          <template #header>
            <div style="height: 100%; display: flex; align-items: center"></div>
          </template>
          <template #buttonGroup>
            <component v-for="(item, index) in actions"
              :key="index"
              :is="item.component"
              v-bind="item.props || {}"
              v-on="item.on || {}"
              :style="item.style || {}" />
          </template>
        </funi-curd>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary"
            @click="onSave">
            确定
          </el-button>
        </span>
      </template>
    </sfc-design-dialog>
  </div>
</template>
<script setup>
import { ElButton, ElInput, ElTreeSelect, ElLink, ElSwitch, ElMessage } from 'element-plus';
import { reactive, ref, h, computed, nextTick, resolveComponent, onMounted, getCurrentInstance, watch, inject } from 'vue';
import ParamGroupSetting from '../datasource-editor/param-group-setting.vue';
import UserOrgScopeEditor from '../user-org-scope-editor/user-org-scope-editor.vue'
import { useRoute } from 'vue-router';

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  designer: Object,
  selectedWidget: Object,
  optionModel: Object,
  searchConfig: [Object],
  modelValue: {
    type: Array,
    default: () => ([])
  },
});

const instance = getCurrentInstance();
const route = useRoute();
const idCollectObject = inject('idCollectObject');

const title = ref('查询条件设置');
const dialogVisible = ref(false);
const localVal = ref([]);
const propOptions = ref([]);
const actions = reactive([{
  component: h(
    ElButton,
    {
      type: 'primary',
      icon: 'CirclePlus',
      size: 'default'
    },
    { default: () => '新建' }
  ),
  on: {
    click: () => {
      localVal.value.push({ id: window.$utils.guid() });
    }
  }
}]);

const columns = computed(() => {
  let columns = [
    {
      label: '序号',
      type: 'index',
      prop: '',
      width: '60px'
    },
    {
      label: '字段',
      prop: 'prop',
      width: '200px',
      render: ({ row, index }) => {
        return h(ElTreeSelect, {
          clearable: true,
          filterable: true,
          'allow-create': true,
          'check-strictly': true,
          modelValue: row.prop,
          data: propOptions.value,
          onChange: val => (row.prop = val),
          onCurrentChange: propObj => {
            row.label = propObj.label;
          }
        });
      }
    },
    {
      label: '标题',
      prop: 'label',
      width: '200px',
      render: ({ row, index }) => {
        if (!row.prop) {
          return '';
        }
        return h(ElInput, {
          modelValue: row.label,
          onInput: e => (row.label = e)
        });
      },
    },
    {
      label: '运算符',
      prop: 'operator',
      width: '200px',
      render: ({ row, index }) => {
        const FuniSelect = resolveComponent('FuniSelect');
        return h(FuniSelect, {
          clearable: true,
          modelValue: row.operator,
          placeholder: '请选择',
          typeCode: 'ASAPP_OPERATOR',
          isLocal: true,
          disabled: row.componentType == 'FuniRUOCLowCode' || row.componentType == 'FuniOrgSelect',
          onChange: val => (row.operator = val)
        });
      }
    },
    {
      label: '类型',
      prop: 'componentType',
      render: ({ row, index }) => {
        if (!row.props) {
          row.props = {};
        }
        const FuniSelect = resolveComponent('FuniSelect');
        const masterCom = h(FuniSelect, {
          style: { width: '120px' },
          clearable: true,
          placeholder: '请选择',
          disabled: row.isModel,
          modelValue: row.componentType,
          options: [
            { label: '文本', value: 'el-input' },
            { label: '数值', value: 'el-input-number' },
            { label: '枚举', value: 'el-select' },
            { label: '日期', value: 'el-date-picker' },
            { label: '布尔', value: 'el-switch' },
            { label: '数值区间', value: 'FuniInputNumberRange' },
            { label: '日期区间', value: 'dateInterval' },
            { label: '模型', value: 'model' },
            { label: '人员', value: 'FuniRUOCLowCode' },
            { label: '部门', value: 'FuniOrgSelect' },
            { label: '区域', value: 'FuniSearchRegion' },
            { label: 'APIs', value: 'APIs' }
          ],
          onChange: val => {
            row.componentType = val;
            row.props = {};
            delete row.component;
            delete row.typeCode;
            delete row.model_id;
            delete row.params;
            delete row.url;
          }
        });
        let children = [masterCom];
        let extendCom, thirdCom;
        switch (row.componentType) {
          case 'el-select':
            row.component = 'funi-select';
            extendCom = h(FuniSelect, {
              style: { marginLeft: '16px' },
              key: 0,
              filterable: true,
              clearable: true,
              remote: true,
              placeholder: '请输入字典名称/编码',
              modelValue: row.typeCode,
              action: (keyword) => {
                return new Promise(resovle => {
                  let params = {
                    keyword: keyword
                  };
                  const appCode = idCollectObject.app_code;
                  instance.proxy.$lowCodeRequest.postDicTypeListAsync(appCode, params)
                    .then(res => {
                      let dicList = [];
                      if (res && res.length > 0) {
                        res.forEach(item => {
                          dicList.push({ label: item.name, value: item.code });
                        });
                      }
                      resovle(dicList);
                    })
                })
              },
              onChange: val => (row.typeCode = val)
            });
            break;
          case 'model':
            row.component = 'funi-select';
            extendCom = h(FuniSelect, {
              style: { marginLeft: '16px', width: '150px' },
              key: 1,
              clearable: true,
              placeholder: '请选择模型',
              modelValue: row.model_id,
              action: (keyword) => {
                return new Promise(resovle => {
                  let params = {
                    app_id: route.query.id,
                    type: 'all'
                  };
                  instance.proxy.$lowCodeRequest.postModelFindAllAsync(params)
                    .then(res => {
                      let modelList = [];
                      if (res && res.list && res.list.length > 0) {
                        res.list.forEach(item => {
                          modelList.push({ label: item.name, value: item.id });
                        });
                      }
                      resovle(modelList);
                    })
                })
              },
              onChange: val => {
                row.model_id = val;
                let params = {
                  model_id: val
                };
                instance.proxy.$lowCodeRequest.postFieldFindAllTreeAsync(params)
                  .then(res => {
                    row.labelKey =
                      res.list.filter(item => item.config && JSON.parse(item.config).is_primary_key == '1').length > 0
                        ? res.list.filter(item => item.config && JSON.parse(item.config).is_primary_key == '1')[0].code
                        : '';
                  })
              },
              onChangeItem: (val) => {
                row.labelKey = val.labelKey;
              }
            });
            thirdCom = h(ParamGroupSetting, {
              style: { marginLeft: '16px', display: 'inline' }, key: 2, designer: props.designer,
              selectedWidget: props.selectedWidget, isBtnOpen: true, model_id: row.model_id,
              formWidgetList: (localVal.value || []).filter(item => item.id !== row.id).map(item => {
                let formWidget = {
                  id: item.id,
                  formItemFlag: true,
                  options: { name: item.prop, label: item.label }
                };
                return formWidget;
              }),
              formDataName: 'searchData',
              modelValue: row.params,
              onChange: (val) => {
                row.params = val;
              }
            });
            break;
          case 'FuniRUOCLowCode':
          case 'FuniOrgSelect':
            row.component = row.componentType;
            row.operator = 'LIKE';
            extendCom = h('span', { style: { marginLeft: '16px' }, }, [h('span', { style: { lineHeight: '32px' } }, '是否多选'), h(ElSwitch, {
              style: { marginLeft: '16px' },
              modelValue: row.props.mode,
              'active-value': 'multiple',
              'inactive-value': 'radio',
              onChange: val => (row.props.mode = val)
            })]);
            thirdCom = h(UserOrgScopeEditor, {
              style: { display: 'inline', marginLeft: '16px' },
              key: 3,
              round: false,
              plain: false,
              link: true,
              hide: {
                role: 'FuniOrgSelect' === row.componentType,
                user: true,
                org: false
              },
              modelValue: row.props.allowedScope,
              onChange: val => {
                row.props.allowedScope = val;
              }
            });
            break;
          case 'APIs':
            row.component = 'funi-select';
            extendCom = h(FuniSelect, {
              style: { marginLeft: '16px', width: '150px' },
              key: 4,
              filterable: true,
              clearable: true,
              placeholder: '请选择',
              modelValue: row.api_url,
              action: (name) => {
                return new Promise(resovle => {
                  let params = { app_id: idCollectObject.app_id, pageNo: 1, pageSize: 99 };
                  if (name) {
                    params.name = name;
                  }
                  instance.proxy.$lowCodeRequest.postApisListAsync(params)
                    .then(res => {
                      let dicList = [];
                      if (res && res.list && res.list.length > 0) {
                        res.list.forEach(item => {
                          dicList.push({ label: item.name, value: item.url });
                        });
                      }
                      resovle(dicList);
                    })
                })
              },
              onChange: val => (row.api_url = val)
            });
            thirdCom = h(ParamGroupSetting, {
              style: { marginLeft: '16px', display: 'inline' }, key: 5, designer: props.designer,
              selectedWidget: props.selectedWidget, isBtnOpen: true,
              formWidgetList: (localVal.value || []).filter(item => item.id !== row.id).map(item => {
                let formWidget = {
                  id: item.id,
                  formItemFlag: true,
                  options: { name: item.prop, label: item.label }
                };
                return formWidget;
              }),
              formDataName: 'searchData',
              modelValue: row.params,
              onChange: (val) => {
                row.params = val;
              }
            });
            break;
          case 'el-date-picker':
          case 'dateInterval':
            if (row.componentType === 'dateInterval') {
              row.component = 'el-date-picker';
            } else {
              row.component = row.componentType;
            }
            extendCom = h(FuniSelect, {
              style: { marginLeft: '16px', width: '150px' },
              key: 4,
              filterable: true,
              clearable: true,
              placeholder: '请选择',
              typeCode: 'ASAPP_DATE_FORMAT_V2',
              isLocal: true,
              modelValue: row.valueFormat,
              onChange: val => (row.valueFormat = val)
            });
            break;
          case 'FuniSearchRegion':
            row.operator = 'LIKE';
            row.component = 'FuniSearchRegion';
            break;
          default:
            row.component = row.componentType;
            break;
        }
        if (extendCom) {
          children.push(extendCom);
        }
        if (thirdCom) {
          children.push(thirdCom);
        }
        return h('span', children);
      }
    },
    {
      label: '操作',
      prop: 'action',
      width: '100px',
      align: 'center',
      fixed: 'right',
      render: ({ row, index }) => {
        return h(ElLink, {
          type: 'primary',
          icon: 'Remove',
          onClick: () => {
            let delIndex = localVal.value.findIndex(col => col.id === row.id);
            if (delIndex >= 0) {
              localVal.value.splice(delIndex, 1);
            }
          }
        })
      }
    }
  ];
  return columns;
})

onMounted(() => {
  init();
});

watch(() => props.modelValue, (val) => {
  localVal.value = val;
}, { immediate: true });

const getTreeData = (propList, prefix = '') => {
  let list = [];
  if (propList && propList.length > 0) {
    propList.forEach(prop => {
      let node = { label: prop.name, value: `${prefix}${prop.code}`, config: prop.config, dataType: prop.data_type };
      if (prop.children && prop.children.length > 0) {
        let chilPrefix = `${prefix}${prop.code}.`;
        let children = getTreeData(prop.children, chilPrefix);
        node.children = children;
      }
      list.push(node);
    });
  }
  return list;
};
const init = () => {
  const { optionModel } = props;
  if (
    optionModel &&
    optionModel.requestOtherParam &&
    optionModel.requestOtherParam.type === 'model' &&
    optionModel.requestOtherParam.data &&
    optionModel.requestOtherParam.data.model_id
  ) {
    const model_id = optionModel.requestOtherParam.data.model_id;
    let url = 'postFieldFindAllTreeAsync';
    instance.proxy.$lowCodeRequest[url]({ model_id }).then(res => {
      if (res && res.list && res.list.length > 0) {
        propOptions.value = res.list.map(item => {
          return {
            label: item.name,
            value: item.code,
            config: item.config,
            dataType: item.data_type
          };
        });
      }
    });
  }
};

const handleDraggableEnd = (e) => {
  const { newIndex, oldIndex } = e;
  const oldData = localVal.value[oldIndex];
  localVal.value.splice(oldIndex, 1);
  localVal.value.splice(newIndex, 0, oldData);
  const newList = [...(localVal.value || [])];
  localVal.value = [];
  nextTick(() => {
    localVal.value = newList;
  });
};
const handleClick = () => {
  dialogVisible.value = true;
};
const onSave = () => {
  const list = localVal.value;
  if (list && list.length > 0) {
    let pass = true;
    let message = '';
    list.forEach((item, index) => {
      if (item.componentType === 'model') {
        if (!item.model_id) {
          pass = false;
          message`第${index + 1}行请选择模型`;
        } else if (!item.labelKey) {
          pass = false;
          message`第${index + 1}行选中的模型必须包含主列`;
        }
      }
    })
    if (!pass) {
      ElMessage.error(message);
      return;
    }
  }
  emit('update:modelValue', list);
  dialogVisible.value = false;
}
</script>
<style lang='scss' scoped>
.search-config-dialog {
  display: inline;
}
</style>