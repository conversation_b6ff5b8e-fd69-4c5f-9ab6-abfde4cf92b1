<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-10-12 15:04:31
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-12 17:05:01
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\search-config-editor\search-config-editor.vue
 * @Description: 
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved. 
-->
<template>
  <div>
    <el-switch size="small"
      v-model="isShowSearchComputed"
      style="margin-right: 8px;" />
    <searchConfigDialog v-if="isShowSearchLocal"
      :designer="designer"
      :selectedWidget="selectedWidget"
      :optionModel="optionModel"
      v-model="searchConfigComputed">编辑</searchConfigDialog>
  </div>
</template>
<script setup>
import searchConfigDialog from './search-config-dialog.vue';
import { computed, ref, watch } from 'vue';

const emit = defineEmits(['show-change']);

const props = defineProps({
  designer: Object,
  selectedWidget: Object,
  optionModel: Object,
  isShowSearch: { type: Boolean, default: false },
  searchConfig: [Object]
});

const isShowSearchLocal = ref(false);
const searchConfigLocal = ref({});

const isShowSearchComputed = computed({
  get () {
    return isShowSearchLocal.value;
  },
  set (val) {
    isShowSearchLocal.value = val;
    emit('show-change', val);
  }
});

const searchConfigComputed = computed({
  get () {
    return searchConfigLocal.value;
  },
  set (val) {
    searchConfigLocal.value = val;
    emit('config-change', val);
  }
});

watch(() => props.isShowSearch, (val) => {
  isShowSearchLocal.value = val;
}, { immediate: true });

watch(() => props.searchConfig, (val) => {
  searchConfigLocal.value = val;
}, { immediate: true });

</script>
<style lang='scss' scoped>
</style>
