<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-08 09:47:43
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-02-21 16:10:00
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\disabled-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.disabled')">
    <SwitchFnSetting v-model="optionModel.disabled"
      v-model:codesValue="optionModel.disabledCode"
      :variables="variables" />
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"
import SwitchFnSetting from "../switch-fn-setting.vue";
import fieldVariablesMixins from "./fieldVariablesMixins";
export default {
  name: "disabled-editor",
  mixins: [i18n, fieldVariablesMixins],
  components: {
    SwitchFnSetting
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      variables: []
    }
  }
}
</script>

<style scoped>
</style>
