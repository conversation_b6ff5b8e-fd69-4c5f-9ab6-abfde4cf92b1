<template>
  <el-form-item :label="i18nt('designer.setting.required')">
    <SwitchFnSetting v-model="optionModel.required"
      v-model:codesValue="optionModel.requiredCode"
      :variables="variables" />
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"
import SwitchFnSetting from "../switch-fn-setting.vue";
import fieldVariablesMixins from "./fieldVariablesMixins";
export default {
  name: "required-editor",
  mixins: [i18n, fieldVariablesMixins],
  components: {
    SwitchFnSetting
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>
</style>
