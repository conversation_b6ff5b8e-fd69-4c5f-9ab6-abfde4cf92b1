<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-23 17:46:39
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\field-switch\switchWidth-editor.vue
 * @Description: 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.switchWidth')">
    <el-input-number v-model="optionModel.switchWidth"
      style="width: 100%"></el-input-number>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";

export default {
  name: "switchWidth-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>
</style>
