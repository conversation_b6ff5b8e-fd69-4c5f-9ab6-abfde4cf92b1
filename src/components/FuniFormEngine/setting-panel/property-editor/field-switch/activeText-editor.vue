<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-23 16:26:24
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\field-switch\activeText-editor.vue
 * @Description: 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.activeText')">
    <el-input v-model="optionModel.activeText"></el-input>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";

export default {
  name: "activeText-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>
</style>
