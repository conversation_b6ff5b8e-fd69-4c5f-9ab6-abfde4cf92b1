<template>
  <el-form-item :label="i18nt('designer.setting.displayType')">
    <el-select v-model="optionModel.type"
      @change="handleChange">
      <el-option label="日期范围"
        value="daterange"></el-option>
      <el-option label="日期时间范围"
        value="datetimerange"></el-option>
      <el-option label="月范围"
        value="monthrange"></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";

export default {
  name: "date-range-type-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  methods: {
    handleChange (e) {
      switch (e) {
        case 'datetimerange':
          Object.assign(this.optionModel, { format: 'YYYY-MM-DD HH:mm:ss', valueFormat: 'YYYY-MM-DD HH:mm:ss' });
          break;
        case 'daterange':
          Object.assign(this.optionModel, { format: 'YYYY-MM-DD', valueFormat: 'YYYY-MM-DD' });
          break;
        case 'monthrange':
          Object.assign(this.optionModel, { format: 'YYYY-MM', valueFormat: 'YYYY-MM' });
          break;
        default:
          break;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
