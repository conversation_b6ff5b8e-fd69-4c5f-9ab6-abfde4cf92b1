<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-24 14:02:05
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\validation-editor.vue
 * @Description: 
-->
<template>
  <el-form-item>
    <template #label>
      <span>{{i18nt('designer.setting.validation')}}
        <el-tooltip effect="light"
          :content="i18nt('designer.setting.validationHelp')">
          <svg-icon icon-class="el-info" /></el-tooltip>
      </span>
    </template>
    <el-select v-model="optionModel.validation"
      filterable
      clearable
      allow-create
      default-first-option>
      <el-option v-for="(fv, fvIdx) in fieldValidators"
        :key="fvIdx"
        :label="fv.label"
        :value="fv.value">
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"
import SvgIcon from "../../svg-icon/index.vue";

export default {
  name: "validation-editor",
  mixins: [i18n],
  components: {
    SvgIcon
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      fieldValidators: [
        { value: 'number', label: this.i18nt('designer.hint.numberValidator') },
        { value: 'letter', label: this.i18nt('designer.hint.letterValidator') },
        { value: 'letterAndNumber', label: this.i18nt('designer.hint.letterAndNumberValidator') },
        { value: 'mobilePhone', label: this.i18nt('designer.hint.mobilePhoneValidator') },
        { value: 'email', label: this.i18nt('designer.hint.emailValidator') },
        { value: 'url', label: this.i18nt('designer.hint.urlValidator') },
        { value: 'noChinese', label: this.i18nt('designer.hint.noChineseValidator') },
        { value: 'chinese', label: this.i18nt('designer.hint.chineseValidator') },
      ],
    }
  },

}
</script>

<style scoped>
</style>
