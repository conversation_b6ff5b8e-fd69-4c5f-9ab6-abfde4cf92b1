<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-26 14:56:06
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\uploadURL-editor.vue
 * @Description: 
-->
<template>
  <div>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">{{i18nt('designer.setting.uploadSetting')}}</el-divider>
    </el-form-item>
    <el-form-item :label="i18nt('designer.setting.uploadURL')">
      <el-input type="textarea"
        :rows="3"
        v-model="optionModel.uploadURL" />
    </el-form-item>
  </div>
</template>

<script>
import i18n from "../../common/utils/i18n"

export default {
  name: "uploadURL-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>
</style>
