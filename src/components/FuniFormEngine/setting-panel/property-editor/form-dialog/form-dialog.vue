<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-06 14:31:04
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-09-06 14:52:04
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\form-dialog\form-dialog.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-dialog v-if="dialogVisible"
      v-model="dialogVisible"
      title="Tips"
      width="30%"
      append-to-body
      :before-close="handleClose">
      <span>This is a message</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">Cancel</el-button>
          <el-button type="primary"
            @click="dialogVisible = false">
            Confirm
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { ElMessage } from 'element-plus';
import { defineComponent, reactive, ref, toRefs } from 'vue';
export default {
  name: 'form-dialog',
  components: {
  },
  props: {

  },
  setup (props) {
    const state = reactive({
      dialogVisible: false
    })
    const show = () => {
      state.dialogVisible = true;
    }
    return {
      ...toRefs(state),
      show
    }
  },
  data () {
    return {

    }
  },
  mounted () {

  },
  methods: {

  },
}
</script>
<style lang='scss' scoped>
</style>
