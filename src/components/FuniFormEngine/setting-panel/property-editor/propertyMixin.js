/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-28 11:25:37
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-05-14 11:35:58
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\propertyMixin.js
 * @Description:
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
export default {
  methods: {
    hasConfig(configName) {
      if (!this.designer || !this.designer.selectedWidget) {
        return false;
      }

      return this.designer.hasConfig(this.selectedWidget, configName);
    },

    emitDefaultValueChange() {
      if (!!this.designer) {
        if (!!this.designer.formWidget) {
          let fieldWidget = this.designer.formWidget.getWidgetRef(this.designer.selectedWidget.options.name);
          if (!!fieldWidget && !!fieldWidget.refreshDefaultValue) {
            fieldWidget.refreshDefaultValue();
          }
        }
      }
    },

    inputNumberHandler(value) {
      value = value.replace(/[^0-9]/gi, '');
    },

    onRemoteChange(val) {
      if (!!val) {
        this.optionModel.filterable = true;
        this.optionModel.allowCreate = false;
      }
    },

    onMultipleSelected(val) {
      if (val) {
        this.optionModel.defaultValue = []; //清空原默认值!!
      } else {
        if (!!this.optionModel.defaultValue && this.optionModel.defaultValue.length > 0) {
          this.optionModel.defaultValue = this.optionModel.defaultValue[0];
        } else {
          this.optionModel.defaultValue = '';
        }
      }
    }
  }
};
