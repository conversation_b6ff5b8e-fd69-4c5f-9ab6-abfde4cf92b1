<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-08 09:47:43
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-11-11 14:28:51
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\bool-var-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <SwitchFnSetting v-bind="$attrs"
    :variables="variables" />
</template>

<script>
import i18n from "../../common/utils/i18n"
import SwitchFnSetting from "../switch-fn-setting.vue";
import fieldVariablesMixins from "./fieldVariablesMixins";
export default {
  name: "disabled-editor",
  mixins: [i18n, fieldVariablesMixins],
  components: {
    SwitchFnSetting
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      variables: []
    }
  }
}
</script>

<style scoped>
</style>
