<template>
  <el-form-item prop="name"
    :rules="nameRequiredRule">
    <template #label>
      <span>{{designerConfig.hasModel?i18nt('designer.setting.uniqueName'):i18nt('designer.setting.fieldName')}}
        <el-tooltip effect="light"
          :content="i18nt('designer.setting.editNameHelp')">
          <svg-icon icon-class="el-info" /></el-tooltip>
      </span>
    </template>
    <template v-if="!!selectedWidget.category || noFieldList">
      <el-input type="text"
        v-model="optionModel.name"
        :readonly="widgetNameReadonly"
        @change="updateWidgetNameAndRef" />
    </template>
    <template v-else-if="isCurd">
      <el-select v-model="optionModel.name"
        allow-create
        filterable
        :disabled="widgetNameReadonly"
        @change="updatePropName"
        :title="i18nt('designer.setting.editNameHelp')">
        <el-option v-for="(sf, sfIdx) in modelFieldList"
          :key="sfIdx"
          :label="sf.label"
          :value="sf.name" />
      </el-select>
    </template>
    <template v-else>
      <el-select v-model="optionModel.name"
        allow-create
        filterable
        :disabled="widgetNameReadonly"
        @change="updateWidgetNameAndRef"
        :title="i18nt('designer.setting.editNameHelp')">
        <el-option v-for="(sf, sfIdx) in serverFieldList"
          :key="sfIdx"
          :label="sf.label"
          :value="sf.name" />
      </el-select>
    </template>
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"
import { isEmptyStr } from "../../common/utils/util"
import SvgIcon from "../../svg-icon/index.vue";

export default {
  name: "name-editor",
  mixins: [i18n],
  components: {
    SvgIcon
  },
  props: {
    isCurd: {
      type: Boolean,
      default: false
    },
    parentWidget: Object,
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  inject: ['serverFieldList', 'getDesignerConfig'],
  data () {
    return {
      designerConfig: this.getDesignerConfig(),
      nameRequiredRule: [{ required: true, message: 'name required' }],
      modelFieldList: []
    }
  },
  watch: {
    isCurd (newVal) {
      if (newVal) {
        const { parentWidget, designerConfig } = this;
        if (parentWidget && parentWidget.options) {
          if (parentWidget.options.requestOtherParam && parentWidget.options.requestOtherParam.type === 'model' && !!parentWidget.options.requestOtherParam.modelId) {
            window.$http.post(`${designerConfig.findAllTreeUrl}`, { model_id: parentWidget.options.requestOtherParam.modelId }).then(res => {
              let fieldList = res.list.map(item => {
                return {
                  ...item,
                  label: item.name,
                  name: item.code
                }
              });
              this.modelFieldList = fieldList;
            });
          } else {
            this.modelFieldList = [];
          }
        } else {
          this.modelFieldList = [];
        }
      }
    }
  },
  computed: {
    noFieldList () {
      return !this.serverFieldList || (this.serverFieldList.length <= 0)
    },

    widgetNameReadonly () {
      return !!this.getDesignerConfig().widgetNameReadonly
    }
  },
  methods: {
    updateWidgetNameAndRef (newName) {
      let oldName = this.designer.selectedWidgetName
      if (isEmptyStr(newName)) {
        this.selectedWidget.options.name = oldName
        this.$message.info(this.i18nt('designer.hint.nameRequired'))
        return
      }

      if (this.designer.formWidget) {
        let foundRef = this.designer.formWidget.getWidgetRef(newName) // 检查newName是否已存在！！
        if (foundRef) {
          this.selectedWidget.options.name = oldName
          this.$message.info(this.i18nt('designer.hint.duplicateName') + newName)
          return
        }

        let widgetInDesign = this.designer.formWidget.getWidgetRef(oldName)
        if (!!widgetInDesign && !!widgetInDesign.registerToRefList) {
          widgetInDesign.registerToRefList(oldName)  //注册组件新的ref名称并删除老的ref！！
          let newLabel = this.getLabelByFieldName(newName)
          this.designer.updateSelectedWidgetNameAndLabel(this.selectedWidget, newName, newLabel)
        }
        if (this.serverFieldList && this.serverFieldList.length > 0) {
          let fIndex = this.serverFieldList.findIndex(item => item.name === newName);
          if (fIndex >= 0) {
            //带出必须
            this.designer.updateSelectedWidgetRequired(this.selectedWidget, this.serverFieldList[fIndex].required);
            //带出枚举
            if (['funi-select', 'radio', 'checkbox'].includes(this.selectedWidget.type)) {
              const field = this.serverFieldList[fIndex];
              if (field.data_type === 'enum' && field.config) {
                try {
                  const fieldConfig = JSON.parse(field.config);
                  if (fieldConfig.association_option_set_code) {
                    this.designer.updateSelectedWidgetTypeCode(this.selectedWidget, fieldConfig.association_option_set_code);
                  }
                } catch { }
              } else if (this.serverFieldList[fIndex].correlation_model_id) {
                const field = this.serverFieldList[fIndex];
                let correlation_fields = '';
                try {
                  const fieldConfig = JSON.parse(field.config);
                  if (fieldConfig.correlation_fields) {
                    correlation_fields = fieldConfig.correlation_fields;
                  }
                } catch { }
                this.designer.updateSelectedModelOption(this.selectedWidget, this.serverFieldList[fIndex].correlation_model_id, this.serverFieldList[fIndex].correlation_model_name, correlation_fields);
              }
            } else if (this.serverFieldList[fIndex].correlation_model_id && (this.selectedWidget.type === 'funi-edit-curd' || this.selectedWidget.type === 'funi-show-curd' || this.selectedWidget.type === 'sfc-draggable-curd')) {
              let association_table_model_id = '';
              let correlation_model = '';
              try {
                const field = this.serverFieldList[fIndex];
                if (field && field.data_type === 'many_to_many' && field.config) {
                  const fieldConfig = JSON.parse(field.config);
                  association_table_model_id = fieldConfig.association_table_model_id;
                  correlation_model = fieldConfig.correlation_model;
                }
              } catch { }
              this.designer.updateSelectedWidgetRequestOtherParam(this.selectedWidget, this.serverFieldList[fIndex].correlation_model_id, this.serverFieldList[fIndex].correlation_model_name, association_table_model_id, this.serverFieldList[fIndex].data_type, correlation_model)
            }
          }
        }
      }
    },

    updatePropName (newName) {
      let oldName = this.designer.selectedWidgetName
      if (isEmptyStr(newName)) {
        this.selectedWidget.options.name = oldName
        this.$message.info(this.i18nt('designer.hint.nameRequired'))
        return
      }
      if (this.parentWidget && this.parentWidget.widgetList && this.parentWidget.widgetList.length > 0) {
        let list = this.parentWidget.widgetList.filter(item => item.options && item.options.name === newName) // 检查newName是否已存在！！
        if (list.length > 1) {
          this.selectedWidget.options.name = oldName
          this.$message.info(this.i18nt('designer.hint.duplicateName') + newName)
          return
        }
      }
      let newLabel = this.getModelLabelByFieldName(newName)
      this.designer.updateSelectedWidgetNameAndLabel(this.selectedWidget, newName, newLabel)
    },

    getLabelByFieldName (fieldName) {
      for (let i = 0; i < this.serverFieldList.length; i++) {
        if (this.serverFieldList[i].name === fieldName) {
          return this.serverFieldList[i].label
        }
      }

      return null
    },

    getModelLabelByFieldName (fieldName) {
      for (let i = 0; i < this.modelFieldList.length; i++) {
        if (this.modelFieldList[i].name === fieldName) {
          return this.modelFieldList[i].label
        }
      }

      return null
    },

  }
}
</script>

<style lang="scss" scoped>
</style>
