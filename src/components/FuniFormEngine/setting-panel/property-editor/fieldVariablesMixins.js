/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-28 11:25:37
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-26 11:48:26
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\fieldVariablesMixins.js
 * @Description:
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
import { findAllField } from '../../common/utils/util';
export default {
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    //是否curd列表
    isCurd: {
      type: Boolean,
      default: false
    },
    //是否列表拖动过来的列
    isCurdColumn: {
      type: Boolean,
      default: false
    },
    formWidgetList: {
      type: Array
    },
    formDataName: {
      type: String,
      default: 'formData'
    },
    //是否是操作列
    isCurdActions: {
      type: Boolean,
      default: false
    },
    //是否是列表功能按钮
    isCurdButtons: {
      type: Boolean,
      default: false
    }
  },
  inject: ['isH5', 'idCollectObject'],
  watch: {
    selectedWidget: {
      handler(newVal) {
        this.$nextTick(() => {
          let wgtList = [];
          if (this.formWidgetList && this.formWidgetList.length > 0) {
            wgtList = this.formWidgetList;
          } else {
            wgtList = this.designer?.widgetList || [];
          }
          const widgetList = this.isH5 ? wgtList : findAllField(wgtList, newVal);
          this.initVar(widgetList, newVal);
        });
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    initVar(widgetList, selectedWidget) {
      let selectName = '';
      const { isCurdActions, isCurdButtons } = this;
      if (selectedWidget && selectedWidget.options && selectedWidget.options.name) {
        selectName = selectedWidget.options.name;
      }
      const children = widgetList
        .filter(wgt => wgt.name !== selectName || isCurdActions || isCurdButtons)
        .map(item => {
          let child = {
            id: window.$utils.guid(),
            label: item.label,
            expression: `context.${this.formDataName}.${item.name}`
          };
          if (item.children && item.children.length > 0) {
            let isCurrentObj = item.children.find(item => selectedWidget && item.id === selectedWidget.id);
            let isCurrent = !!isCurrentObj;
            if (isCurdActions && item.name === selectName) {
              isCurrent = true;
            }
            const { isCurd, isCurdColumn } = this;
            if ((isCurd || isCurdActions) && !isCurdColumn && isCurrent) {
              child.label = child.label + '行数据';
            }
            child.children = item.children
              .filter(childWgt => childWgt.name !== selectName || isCurdActions)
              .map(childItem => {
                return {
                  id: window.$utils.guid(),
                  label: childItem.label,
                  expression:
                    (isCurd || isCurdActions) && !isCurdColumn && isCurrent
                      ? `context.params.row.${childItem.name}`
                      : `context.${this.formDataName}&&context.${this.formDataName}.${item.name}?context.${this.formDataName}.${item.name}.list?.filter(item=>item.${childItem.name}).map(item=>item.${childItem.name}):[]`
                };
              });
          }
          if (item.type === 'funi-show-curd') {
            //展示列表单独处理
            const colChildren = child.children;
            child.children = [
              {
                id: window.$utils.guid(),
                label: '选中索引',
                expression: `context.${this.formDataName}.${item.name}?.selectedRowKeys`
              },
              {
                id: window.$utils.guid(),
                label: '选中数据',
                expression: `context.${this.formDataName}.${item.name}?.selectedRows`
              },
              {
                id: window.$utils.guid(),
                label: '列表数据',
                expression: `context.${this.formDataName}.${item.name}?.list`
              },
              {
                id: window.$utils.guid(),
                label: '列表字段',
                expression: `context.${this.formDataName}.${item.name}?.list`,
                children: colChildren
              }
            ];
          }
          return child;
        });
      let rowIndex = children.findIndex(
        child => (this.isCurd || isCurdActions) && !this.isCurdColumn && child.label && child.label.includes('行数据')
      );
      if (rowIndex >= 0) {
        children.splice(rowIndex, 0, { id: window.$utils.guid(), label: '行号', expression: `index` });
      }
      let formChildren = children;
      if (
        this.idCollectObject &&
        this.idCollectObject.previousTabFields &&
        this.idCollectObject.previousTabFields.previous &&
        this.idCollectObject.previousTabFields.previous.length > 0
      ) {
        let stepFormChildren = [];
        let preSteps = this.idCollectObject.previousTabFields.previous;

        //添加前面步骤的表单字段
        preSteps.forEach(element => {
          if (element.tabTitle && element.fields && element.fields.length > 0) {
            let stepChildren = element.fields.map(item => {
              return {
                id: window.$utils.guid(),
                label: item.name,
                expression: `context.stepData?.step${element.tabIndex}?.${item.key}`
              };
            });
            let stepFormItem = {
              id: 'stepFormData' + element.tabIndex,
              label: element.tabTitle,
              children: stepChildren
            };
            stepFormChildren.push(stepFormItem);
          }
        });

        formChildren = [
          ...stepFormChildren,
          {
            id: 'currentFomrData',
            label: this.idCollectObject.previousTabFields.activeTitle,
            children
          }
        ];
      }
      this.variables = [
        {
          id: `${this.formDataName}`,
          label: '表单字段',
          children: formChildren
        },
        {
          id: 'route',
          label: '路由参数',
          children: [
            { id: 'routeId', label: 'id', expression: 'context.query.id' },
            { id: 'routeBusinessId', label: '业务id', expression: 'context.query.businessId' },
            { id: 'routeCid', label: '关联id', expression: 'context.query.cid' },
            { id: 'routeBizName', label: '操作类型', expression: 'context.query.bizName' },
            { id: 'routeBusType', label: '业务类型', expression: 'context.query.busType' }
          ]
        },
        {
          id: 'workflow',
          label: '工作流信息',
          children: [
            {
              id: 'currentActivityId',
              label: '当前节点ID',
              expression: 'context.workflow?.businessFrontendInfo?.currentActivityId'
            },
            {
              id: 'currentActivityName',
              label: '当前节点名',
              expression: 'context.workflow?.businessFrontendInfo?.currentActivityName'
            }
          ]
        }
      ];
    }
  }
};
