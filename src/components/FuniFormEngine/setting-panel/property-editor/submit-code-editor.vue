<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-08 09:47:43
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-09-12 19:14:59
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\submit-code-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.isSubmit')">
    <el-switch v-model="optionModel.isSubmit"
      @change="handleChange" />
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"

export default {
  name: "submit-code-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  methods: {
    handleChange (e) {
      if (e) {
        this.optionModel.listener = `click:()=>{context.emit('submit')}`;
      } else {
        this.optionModel.listener = '';
      }
    }
  }
}
</script>

<style scoped>
</style>