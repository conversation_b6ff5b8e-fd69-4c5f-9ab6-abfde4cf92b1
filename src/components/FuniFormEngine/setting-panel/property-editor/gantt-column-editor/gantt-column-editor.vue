<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-10-19 10:51:21
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-10-19 11:34:20
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\gantt-column-editor\gantt-column-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('extension.setting.columns')">
    <gantt-column-editor-dialog v-model="localVal" />
  </el-form-item>
</template>

<script>
import { computed, getCurrentInstance } from 'vue'
import i18n from "../../../common/utils/i18n"
import GanttColumnEditorDialog from './gantt-column-editor-dialog.vue'
export default {
  name: 'index',
  mixins: [i18n],
  components: {
    GanttColumnEditorDialog
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    modelValue: {
      type: Array,
      default: () => {
        return []
      }
    },
  },
  setup (props) {
    const instance = getCurrentInstance();
    const localVal = computed({
      get: () => {
        return props.modelValue;
      },
      set: (newVal) => {
        instance.proxy.$emit('update:modelValue', newVal);
      }
    })
    return { localVal }
  }
}
</script>
