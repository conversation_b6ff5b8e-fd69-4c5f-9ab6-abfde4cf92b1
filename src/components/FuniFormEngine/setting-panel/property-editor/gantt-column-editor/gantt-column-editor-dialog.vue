<template>
  <div class="gantt-column-editor-dialog">
    <el-button :type="typeComputed"
      icon="el-icon-edit"
      plain
      round
      @click="addColumn">{{
      i18nt('designer.setting.addCurdColumn')
    }}</el-button>
    <el-dialog v-if="dialogVisible"
      v-model="dialogVisible"
      destroy-on-close
      :title="i18nt('designer.setting.addCurdColumn')"
      width="800px">
      <funi-curd :columns="columns"
        :pagination="false"
        :draggable="true"
        :data="localVal"
        @draggableEnd="handleDraggableEnd">
        <template #header>
          <div style="height: 100%; display: flex; align-items: center"></div>
        </template>
        <template #buttonGroup>
          <component v-for="(item, index) in actions"
            :key="index"
            :is="item.component"
            v-bind="item.props || {}"
            v-on="item.on || {}"
            :style="item.style || {}" />
        </template>
      </funi-curd>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary"
            @click="onSave"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { deepClone } from '../../../common/utils/util';
import i18n from '../../../common/utils/i18n';
import { ElButton, ElInput, ElLink } from 'element-plus';
import { h, resolveComponent } from 'vue';
export default {
  name: 'gantt-column-editor-dialog',
  mixins: [i18n],
  inject: ['getDesignerConfig'],
  components: {},
  props: {
    optionModel: Object,
    modelValue: {
      type: Array,
      default: () => {
        return [];
      }
    },
    /**
     * 模式：编辑模式自动添加序号和选中列
     */
    mode: {
      type: String,
      default: 'edit'
    }
  },
  data () {
    return {
      designerConfig: this.getDesignerConfig(),
      dialogVisible: false,
      actions: [
        {
          component: h(
            ElButton,
            {
              type: 'primary',
              icon: 'CirclePlus'
            },
            { default: () => '新建' }
          ),
          on: {
            click: () => {
              this.localVal.push({ id: window.$utils.guid(), align: 'center', width: '*' });
            }
          }
        }
      ],
      localVal: [],
      propOptions: []
    };
  },
  mounted () { },
  computed: {
    columns () {
      const { dialogVisible } = this;
      if (!dialogVisible) {
        return [];
      }
      let columns = [
        {
          label: '序号',
          type: 'index',
          prop: '',
          width: '60px'
        },
        {
          label: '列名',
          prop: 'label',
          render: ({ row }) => {
            return h(ElInput, {
              modelValue: row.label,
              placeholder: '请输入列名',
              onInput: e => {
                row.label = e;
              }
            });
          }
        },
        {
          label: '列字段',
          prop: 'name',
          render: ({ row }) => {
            return h(ElInput, {
              modelValue: row.name,
              placeholder: '请输入列名',
              onInput: e => {
                row.name = e;
              }
            });
          }
        },
        {
          label: '对齐',
          prop: 'align',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.align,
              options: [
                { label: '靠左', value: 'left' },
                { label: '居中', value: 'center' },
                { label: '靠右', value: 'right' }
              ],
              placeholder: '请选择对齐方式',
              onChange: e => {
                row.align = e;
              }
            });
          }
        },
        {
          label: '宽度',
          prop: 'width',
          render: ({ row }) => {
            return h(ElInput, {
              modelValue: row.width,
              placeholder: '请输入宽度',
              onInput: e => {
                row.width = e;
              }
            });
          }
        },
        {
          label: '自定义',
          prop: 'custom',
          width: '200px',
          render: ({ row }) => {
            return h(ElInput, {
              modelValue: row.custom,
              autosize: true,
              type: 'textarea',
              placeholder: '请输入自定义列信息',
              onInput: e => {
                row.custom = e;
                if (e) {
                  try {
                    let customProps = JSON.parse(e);
                    if (customProps) {
                      Object.assign(row, customProps);
                    }
                  } catch { }
                }
              }
            });
          }
        },
        {
          label: '操作',
          prop: 'action',
          align: 'center',
          fixed: 'right',
          render: ({ row, index }) => {
            return h('div', {}, [
              h(ElLink, {
                type: 'primary',
                icon: 'Remove',
                onClick: () => {
                  this.localVal.splice(index, 1);
                }
              })
            ]);
          }
        },
      ];
      return columns;
    },
    typeComputed () {
      return this.localVal && this.localVal.length > 0 ? 'primary' : 'info';
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.localVal = [...(deepClone(newVal) || [])];
      },
      immediate: true
    }
  },
  methods: {
    addColumn () {
      this.localVal = [...(deepClone(this.modelValue) || [])];
      this.dialogVisible = true;
    },
    onSave () {
      const { localVal } = this;
      this.$emit('update:modelValue', localVal);
      this.dialogVisible = false;
    },
    handleDraggableEnd (e) {
      const { newIndex, oldIndex } = e;
      const oldData = this.localVal[oldIndex];
      this.localVal.splice(oldIndex, 1);
      this.localVal.splice(newIndex, 0, oldData);
      const newList = [...(this.localVal || [])];
      this.localVal = [];
      this.$nextTick(() => {
        this.localVal = newList;
      });
    }
  }
};
</script>