<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-24 14:01:07
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\withCredentials-editor.vue
 * @Description: 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.withCredentials')">
    <el-switch v-model="optionModel.withCredentials"></el-switch>
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"

export default {
  name: "withCredentials-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>
</style>
