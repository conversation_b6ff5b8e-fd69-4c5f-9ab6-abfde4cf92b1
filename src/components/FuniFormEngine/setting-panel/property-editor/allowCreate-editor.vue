<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-23 17:52:20
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\allowCreate-editor.vue
 * @Description: 
-->
<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-23 16:30:51
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\allowCreate-editor.vue
 * @Description: 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.allowCreate')">
    <el-switch v-model="optionModel.allowCreate"></el-switch>
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n";
import propertyMixin from "../property-editor/propertyMixin"

export default {
  name: "allowCreate-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },

}
</script>

<style scoped>
</style>
