<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-07-17 16:37:31
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-21 10:57:06
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\labelHidden-editor.vue
 * @Description: 
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved. 
-->
<template>
  <el-form-item v-show="show"
    :label="i18nt('designer.setting.labelHidden')">
    <el-switch v-model="optionModel.labelHidden" />
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"

export default {
  name: "labelHidden-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  computed: {
    show () {
      return this.selectedWidget && !['sfc-file-table', 'sfc-operation-log', 'sfc-funi-log'].includes(this.selectedWidget.type);
    }
  }
}
</script>

<style scoped>
</style>
