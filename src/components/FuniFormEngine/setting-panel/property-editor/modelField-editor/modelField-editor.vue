<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-06-15 14:20:32
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-02-20 15:35:44
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\modelField-editor\modelField-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.modelField')">
    <el-tree-select v-model="optionModel.modelField"
      clearable
      lazy
      :load="load"
      :props="props" />
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n"
import { findAllWidget } from "../../../common/utils/util.js"
export default {
  name: 'modelField-editor',
  mixins: [i18n],
  components: {
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  inject: ['getDesignerConfig', 'idCollectObject'],
  data () {
    return {
      props: {
        label: 'label',
        children: 'children',
        isLeaf: 'isLeaf',
      },
      isInit: false
    }
  },
  mounted () {

  },
  methods: {
    findSelectWidget (widgetList) {
      let realWidgetList = findAllWidget(widgetList);
      let selectWidget = realWidgetList.filter(wgt => wgt.type === 'funi-select' && wgt.options && wgt.options.modelOption && wgt.options.modelOption.modelId);
      return selectWidget;
    },
    load (node, resolve) {
      const { isInit } = this;
      if (!isInit) {
        let widgetList = this.findSelectWidget(this.designer.widgetList);
        let modelList =
          widgetList.map(wgt => { return { value: wgt.options.modelOption.modelId, label: wgt.options.label, name: wgt.options.name } });
        this.isInit = true;
        resolve([...modelList])
      } else {
        if (node.isLeaf) return resolve([])
        let config = this.getDesignerConfig();
        let id = node.data.value;
        let appId = '';
        if (this.idCollectObject) {
          appId = this.idCollectObject.app_id;
        }
        window.$http.post(`${config.dicFieldUrl}`, { app_id: appId, model_id: id }).then(res => {
          let list = [];
          if (res && res.list && res.list.length > 0) {
            res.list.forEach(element => {
              list.push({
                value: `${node.data.name}@${element.value}`,
                label: element.label,
                isLeaf: true
              })
            });
          }
          resolve(list);
        })
      }
    }
  },
}
</script>
