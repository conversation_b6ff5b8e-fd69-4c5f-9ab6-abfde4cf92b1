<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-19 10:55:22
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-05-14 10:55:42
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\hidden-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.hidden')">
    <SwitchFnSetting v-model="optionModel.hidden"
      v-model:codesValue="optionModel.hiddenCode"
      :variables="variables" />
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n";
import SwitchFnSetting from "../switch-fn-setting.vue";
import fieldVariablesMixins from "./fieldVariablesMixins";
export default {
  name: "hidden-editor",
  mixins: [i18n, fieldVariablesMixins],
  components: {
    SwitchFnSetting
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      variables: []
    }
  },
}
</script>

<style scoped>
</style>
