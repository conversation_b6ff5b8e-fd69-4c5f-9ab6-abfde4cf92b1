<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-24 14:00:32
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\container-grid\colHeight-editor.vue
 * @Description:
-->
<template>
  <div>
    <el-form-item :label="i18nt('designer.setting.gridColHeight')">
      <el-input type="number"
        v-model="optionModel.colHeight"
        @input="inputNumberHandler"
        min="0"
        class="hide-spin-button"></el-input>
    </el-form-item>
  </div>
</template>

<script>
import i18n from "../../../common/utils/i18n";
import propertyMixin from "../propertyMixin"

export default {
  name: "colHeight-editor",
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },

}
</script>

<style scoped>
</style>
