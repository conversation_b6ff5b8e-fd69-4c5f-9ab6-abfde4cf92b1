<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-25 10:09:30
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\field-time-range\time-range-defaultValue-editor.vue
 * @Description: 
-->
<template>
  <el-form-item>
    <template #label>
      <span>{{i18nt('designer.setting.defaultValue')}}
        <el-tooltip effect="light"
          content="计算公示:初始化不会联动仅展示已保存的值,数据联动:初始化加载也从联动属性计算值。">
          <svg-icon icon-class="el-info" /></el-tooltip>
      </span>
    </template>
    <el-row>
      <funi-select :options="options"
        v-model="optionModel.defaultAction"
        style="margin-bottom:8px;" />
    </el-row>
    <el-row>
      <FuniVariableSetter class="setting-variable"
        v-model="optionModel.defaultValue"
        @change="emitDefaultValueChange"
        :variables="variables" />
    </el-row>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";
import propertyMixin from "../../property-editor/propertyMixin"
import fieldVariablesMixins from "../../property-editor/fieldVariablesMixins"

export default {
  name: "time-range-defaultValue-editor",
  mixins: [i18n, propertyMixin, fieldVariablesMixins],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      variables: [],
      options: [{ label: '计算公示', value: '1' }, { label: '数据联动', value: '2' },]
    }
  },
  mounted () {

  }
}
</script>

<style scoped>
</style>
