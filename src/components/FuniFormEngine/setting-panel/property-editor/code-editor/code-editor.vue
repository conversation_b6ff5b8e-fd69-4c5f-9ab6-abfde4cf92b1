<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-30 17:42:57
 * @LastEditors: 孟雪峰 <EMAIL>
 * @LastEditTime: 2024-10-16 17:09:10
 * @FilePath: /src/components/FuniFormEngine/setting-panel/property-editor/code-editor/code-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-button :type="code ? 'primary' : 'info'" :icon="icon" :text="text" :round="round" @click="handleEdit">{{
      btnLabel
    }}</el-button>
    <sfc-design-dialog
      v-model="dialogVisible"
      v-if="dialogVisible"
      :title="title"
      :top="top"
      width="65%"
      destroy-on-close
      :append-to-body="true"
      customClass="code-editor"
      @fullChange="onFullChange"
    >
      <div
        class="code-editor-body"
        :style="{ height: `${codeMaxLines * 15 + 10}px`, paddingBottom: tip ? '20px' : '4px' }"
        :key="key"
      >
        <el-text v-if="tip" class="code-editor-alter">{{ tip }} </el-text>
        <el-text v-if="prefix" class="code-editor-alter">{{ prefix }}</el-text>
        <CodeEdit :userWorker="true" :maxLines="codeMaxLines" :mode="mode" v-model="code" />
        <el-text v-if="suffix" class="code-editor-alter">{{ suffix }}</el-text>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave"> 保存 </el-button>
        </span>
      </template>
    </sfc-design-dialog>
  </div>
</template>
<script setup>
import { computed, reactive, ref, watchEffect, getCurrentInstance } from 'vue';
import CodeEdit from '../../../code-editor/index.vue';
const emit = defineEmits('update:modelValue');

const props = defineProps({
  title: {
    type: String,
    default: '代码编辑'
  },
  btnLabel: {
    type: String,
    default: '编辑'
  },
  mode: {
    type: String,
    default: 'javascript'
  },
  tip: [String], //提示信息
  prefix: {
    type: String,
    default: '{'
  },
  suffix: {
    type: String,
    default: '}'
  },
  modelValue: {
    type: String,
    default: ''
  },
  //是否必须包含renturn 关键字
  isReturn: {
    type: Boolean,
    default: false
  },
  top: {
    type: String,
    default: '15vh'
  },
  icon: {
    type: String,
    default: 'Edit'
  },
  text: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: true
  },
  maxLines: {
    type: Number,
    default: 30
  },
  //必须包含的不返回值的函数名
  funcNames: {
    type: Array,
    default: () => []
  },
  //必须包含的返回值的函数名
  returnFuncNames: {
    type: Array,
    default: () => []
  }
});

const instance = getCurrentInstance();

const dialogVisible = ref(false);

const code = ref('');
const key = ref(0);
const codeMaxLines = ref(props.maxLines);
const fullscreen = ref(false);

watchEffect(() => {
  let newVal = props.modelValue ?? '';
  code.value = newVal;
});

const handleEdit = e => {
  dialogVisible.value = true;
  let newVal = props.modelValue ?? '';
  code.value = newVal;
};

const checkFunctionName = (classStr, functionName) => {
  // 移除字符串中的单行注释
  classStr = classStr.replace(/\/\/.*$/gm, '');
  // 移除字符串中的多行注释
  classStr = classStr.replace(/\/\*[\s\S]*?\*\//g, '');

  // 匹配类定义的正则表达式
  const classPattern = /\bclass\s+\w+\s*\{/;
  // 匹配指定函数定义的正则表达式
  const functionPattern = new RegExp(`\\b${functionName}\\b\\s*\\(`);

  // 检查是否存在类定义
  if (classPattern.test(classStr)) {
    // 提取类体
    const classBodyMatch = classStr.match(/{[\s\S]*}/);
    if (classBodyMatch) {
      // 检查类中是否包含指定函数
      return functionPattern.test(classBodyMatch[0]);
    }
  }
  return false;
};

const checkFunctionWithReturn = (classStr, functionName) => {
  // 移除字符串中的单行注释
  classStr = classStr.replace(/\/\/.*$/gm, '');
  // 移除字符串中的多行注释
  classStr = classStr.replace(/\/\*[\s\S]*?\*\//g, '');
  // 匹配类定义的正则表达式
  const classPattern = /\bclass\s+\w+\s*\{/;
  // 匹配指定函数定义的正则表达式
  const functionPattern = new RegExp(`\\b${functionName}\\b\\s*\\(`);
  // 匹配return关键字的正则表达式
  const returnPattern = /\breturn\b/;

  // 检查是否存在类定义
  if (classPattern.test(classStr)) {
    // 提取类体
    const classBodyMatch = classStr.match(/{[\s\S]*}/);
    if (classBodyMatch) {
      // 检查类中是否包含指定函数
      const functionMatch = classBodyMatch[0].match(functionPattern);
      if (functionMatch) {
        const functionRegex = new RegExp(`${functionName}\\s*\\([^)]*\\)\\s*{([^}]*)}`, 'g');
        // 提取函数体
        const functionBody = classBodyMatch[0].match(functionRegex);

        if (functionBody && returnPattern.test(functionBody[0])) {
          return true;
        }
      }
    }
  }
  return false;
};

const handleSave = () => {
  if (props.isReturn && code.value && !code.value.includes('return')) {
    instance.proxy.$notify('该函数需要返回值(return)');
    return;
  }
  if (props.funcNames.length > 0 && code.value) {
    let isFunc = true;
    let noExitNames = [];
    props.funcNames.forEach(funcName => {
      if (!checkFunctionName(code.value, funcName)) {
        noExitNames.push(funcName);
        isFunc = false;
      }
    });
    if (!isFunc && noExitNames.length > 0) {
      instance.proxy.$notify(`${noExitNames.join(',')}函数未定义`);
      return;
    }
  }
  if (props.returnFuncNames.length > 0 && code.value) {
    let isFunc = true;
    let noExitNames = [];
    props.returnFuncNames.forEach(funcName => {
      if (!checkFunctionName(code.value, funcName)) {
        noExitNames.push(funcName);
        isFunc = false;
      }
    });
    if (!isFunc && noExitNames.length > 0) {
      instance.proxy.$notify(`${noExitNames.join(',')}函数未定义或未包含 return关键字`);
      return;
    }
  }
  dialogVisible.value = false;
  emit('change', code.value);
  emit('update:modelValue', code.value);
};

const onFullChange = isFull => {
  const lines = (window.innerHeight - 150) / 15;
  codeMaxLines.value = isFull ? lines : props.maxLines;
  key.value++;
  fullscreen.value = isFull;
};
</script>
<style lang="scss">
.code-editor .el-dialog__body {
  padding: 0px !important;
}
</style>
<style lang="scss" scoped>
.code-editor-alter {
  display: block;
  margin-left: 4px;
}
</style>
