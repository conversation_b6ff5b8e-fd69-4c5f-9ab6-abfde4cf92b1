<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-24 19:04:27
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-02-29 16:37:11
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\buttons-editor\buttons-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="propName==='actions'?i18nt('extension.setting.actions'):i18nt('extension.setting.actionbuttons')">
    <buttons-editor-dialog v-model="localVal"
      :actionTypeOptions="actionTypeOptions"
      :designer="designer"
      :parentConfig="parentConfig"
      :propName="propName"
      :variables="variables"
      :selectedWidget="selectedWidget"
      :optionModel="optionModel"
      :eventName="eventName"
      :eventCode="eventCode"
      :showPosition="isCurdButtons" />
  </el-form-item>
</template>
<script>
import i18n from "../../../common/utils/i18n"
import ButtonsEditorDialog from './buttons-editor-dialog.vue';
import fieldVariablesMixins from "../../property-editor/fieldVariablesMixins"
export default {
  name: "buttons-editor",
  mixins: [i18n, fieldVariablesMixins],
  components: {
    ButtonsEditorDialog
  },
  props: {
    propName: {
      type: String,
      default: 'buttons'
    },
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    parentConfig: Object,
    modelValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    actionTypeOptions: {
      type: Array,
      default: () => {
        return []
      }
    },
    //事件名称
    eventName: {
      type: String,
      default: '点击事件'
    },
    //事件代码
    eventCode: {
      type: String,
      default: 'onClick'
    }
  },
  data () {
    return {
      localVal: [],
      variables: []
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.localVal = newVal || [];
      },
      immediate: true
    },
    localVal: {
      handler (newVal) {
        this.$emit('update:modelValue', newVal);
      },
      immediate: true
    }
  }
}
</script>
<style lang='scss' scoped>
</style>