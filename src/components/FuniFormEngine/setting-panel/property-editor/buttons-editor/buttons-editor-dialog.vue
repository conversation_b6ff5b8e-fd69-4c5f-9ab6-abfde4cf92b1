<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-08-16 18:44:38
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-01-24 11:07:05
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\buttons-editor\buttons-editor-dialog.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="column-editor-dialog">
    <el-button :type="localVal&&localVal.length>0?'primary':'info'"
      icon="el-icon-edit"
      plain
      round
      @click="addButtons">{{propName==='actions'?i18nt('designer.setting.addActions'):i18nt('designer.setting.addButtons')}}</el-button>
    <sfc-design-dialog v-if="dialogVisible"
      v-model="dialogVisible"
      destroy-on-close
      :title="i18nt('designer.setting.addButtons')"
      width="1000px">
      <funi-curd :columns="columns"
        :pagination="false"
        :draggable="true"
        :data="localVal">
        <template #header>
          <div style="height: 100%; display: flex; align-items: center"></div>
        </template>
        <template #buttonGroup>
          <component v-for="(item, index) in actions"
            :key="index"
            :is="item.component"
            v-bind="item.props || {}"
            v-on="item.on || {}"
            :style="item.style || {}" />
        </template>
      </funi-curd>
      <template #footer>
        <span>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary"
            @click="onSave">
            保存
          </el-button>
        </span>
      </template>
    </sfc-design-dialog>
  </div>
</template>

<script>
import {
  deepClone,
  randomString
} from "../../../common/utils/util"
import i18n from "../../../common/utils/i18n"
import { ElButton, ElInput, ElLink } from 'element-plus';
import { h, resolveComponent } from 'vue';
import BottonsCodeDialog from "./buttons-code-dialog.vue";
import BottonsEventDialog from "./buttons-event-dialog.vue";
import IconEditor from '../icon-editor/icon-editor.vue';
import lowcode from '../../../common/utils/lowcode';
export default {
  name: 'buttons-editor-dialog',
  mixins: [i18n],
  components: {
    IconEditor
  },
  props: {
    propName: {
      type: String,
      default: 'buttons'
    },
    designer: Object,
    parentConfig: Object,
    selectedWidget: Object,
    optionModel: Object,
    modelValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    actionTypeOptions: {
      type: Array,
      default: () => {
        return []
      }
    },
    //是否显示展示位置列
    showPosition: {
      type: Boolean,
      default: false
    },
    //事件名称
    eventName: {
      type: String,
      default: '点击事件'
    },
    //事件代码
    eventCode: {
      type: String,
      default: 'onClick'
    },
    variables: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  inject: ['idCollectObject'],
  data () {
    return {
      dialogVisible: false,
      actions: [
        {
          component: h(ElButton, {
            type: 'primary',
            icon: 'CirclePlus',
          }, { default: () => '新建' }),
          on: {
            click: () => {
              this.localVal.push({ id: window.$utils.guid(), component: 'ElButton', type: 'primary', size: 'default' });
            }
          }
        }
      ],
      localVal: []
    }
  },
  mounted () {

  },
  computed: {
    columns () {
      const { actionTypeOptions, designer, selectedWidget, showPosition, parentConfig, eventName, eventCode, variables } = this;
      let columns = [
        {
          label: '序号',
          type: 'index',
          prop: '',
          width: '60px'
        },
        {
          label: '操作类型',
          prop: 'actionType',
          width: '120px',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.actionType,
              placeholder: '请选择操作类型',
              clearable: true,
              options: [...(actionTypeOptions || [])],
              onChange: (e) => {
                row.actionType = e;
                if (actionTypeOptions && actionTypeOptions.length > 0) {
                  let fIndex = actionTypeOptions.findIndex(item => item.value === e);
                  if (fIndex >= 0) {
                    row.label = actionTypeOptions[fIndex].label;
                    Object.assign(row, actionTypeOptions[fIndex].defaultProps);
                    if (actionTypeOptions[fIndex].getDefaultAttrs) {
                      let page_id = '';
                      if (this.idCollectObject && this.idCollectObject.pageType === 'detail') {
                        page_id = this.idCollectObject.detail_page_id;
                      } else if (this.idCollectObject && this.idCollectObject.pageType === 'edit') {
                        page_id = this.idCollectObject.edit_page_id;
                      }
                      const options = { ...(this.optionModel || {}) };
                      let otherParam = {};
                      if (options && options.requestOtherParam) {
                        otherParam = {
                          lineExportApi: `/as/${this.$route.query.code}/exportList`,
                          allExportApi: `/as/${this.$route.query.code}/exportList`,
                          withdrawApi: `/as/${this.$route.query.code}/executeBusiness`,
                          deleteApi: `/as/${this.$route.query.code}/del`,
                          component_id: options.requestOtherParam.data.component_id,
                          model_id: options.requestOtherParam.data.model_id,
                          buttonId: row.id,
                          page_id
                        };
                      }
                      const jsCode = actionTypeOptions[fIndex].getDefaultAttrs({ ...options, ...otherParam })
                      let logic = {
                        actionItem: {
                          pid: "0",
                          root: true,
                          isSelected: true,
                          name: "自定义JS",
                          code: "snippet",
                          type: "SNIPPET",
                          group: {
                            id: "app",
                            label: "应用动作"
                          },
                          id: window.$utils.guid(),
                          children: [],
                          snippets: `${jsCode}`
                        },
                        snippets: `${jsCode}`
                      }
                      row.logic = logic;
                      //把代码也生成好
                      let body = lowcode.generateEvent(logic, false, '');
                      let attrsCode = `${eventCode}:${body}`;
                      const code = `@{${attrsCode}}@`;
                      row.attrs = code;
                    }
                  }
                }
              }
            })
          }
        },
        {
          label: '按钮名称',
          prop: 'label',
          width: '120px',
          render: ({ row }) => {
            return h(ElInput, {
              modelValue: row.label,
              placeholder: '请输入内容',
              onInput: (e) => {
                row.label = e;
                if (row.permission) {
                  row.permission.name = e;
                }
              }
            })
          }
        },
        {
          label: '类型',
          prop: 'componentType',
          width: '120px',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.componentType,
              placeholder: '请选择组件',
              clearable: true,
              options: [{ label: '按钮', value: 'button' }, { label: '链接', value: 'link' }],
              onChange: (e) => {
                row.componentType = e;
              }
            })
          }
        },
        {
          label: '颜色',
          prop: 'type',
          width: '120px',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.type,
              placeholder: '请选择类型',
              clearable: true,
              options: [
                { label: '主要', value: 'primary' },
                { label: '次要', value: 'default' },
                { label: '成功', value: 'success' },
                { label: '警告', value: 'warning' },
                { label: '错误', value: 'danger' },
                { label: '信息', value: 'info' },
              ],
              onChange: (e) => {
                row.type = e;
              }
            })
          }
        },
        {
          label: '尺寸',
          prop: 'size',
          width: '120px',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.size,
              placeholder: '请选择尺寸',
              options: [{ label: '大', value: 'large' }, { label: '中', value: 'default' }, { label: '小', value: 'small' }],
              onChange: (e) => {
                row.size = e;
              }
            })
          }
        },
        {
          label: '展示图标',
          prop: 'showIcon',
          width: '120px',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.showIcon,
              placeholder: '请选择',
              options: [{ label: '无图标', value: 'noIcon' }, { label: '文字加图标', value: 'txtIcon' }, { label: '纯图标', value: 'onlyIcon' }],
              onChange: (e) => {
                row.showIcon = e;
              }
            })
          }
        },
        {
          label: '图标样式',
          prop: 'icon',
          width: '120px',
          render: ({ row }) => {
            return h(IconEditor, {
              modelValue: row.icon,
              placeholder: '请选择图标',
              clearable: true,
              onChange: (e) => {
                row.icon = e;
              }
            })
          }
        },
        {
          label: '图标位置',
          prop: 'iconPosition',
          width: '120px',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.iconPosition,
              placeholder: '请选择',
              options: [{ label: '前置', value: 'left' }, { label: '后置', value: 'right' }],
              onChange: (e) => {
                row.iconPosition = e;
              }
            })
          }
        },
        {
          label: '有无权限',
          prop: 'hasAuth',
          width: '100px',
          render: ({ row, index }) => {
            const ElSwitch = resolveComponent('el-switch');
            return h(ElSwitch, {
              modelValue: row.hasAuth,
              onChange: (e) => {
                row.hasAuth = e;
                if (e === true && !row.permission) {
                  row.permission = {
                    id: window.$utils.guid(),
                    name: row.label,
                    code: `AS_AUTH_BTN_${randomString(true, 8, 10)}`,
                    type: "2",
                    type_name: "按钮"
                  }
                } else {
                  delete row.permission;
                }
              }
            })
          }
        },
        {
          label: '显示条件',
          prop: 'showConditions',
          width: '200px',
          render: ({ row }) => {
            const FuniVariableSetter = resolveComponent('FuniVariableSetter');
            return h(FuniVariableSetter, {
              modelValue: row.showConditions,
              variables,
              onChange: (e) => {
                row.showConditions = e;
              }
            })
          }
        },
        {
          label: '事件',
          prop: 'attrs',
          width: '100px',
          render: ({ row }) => {
            return h(BottonsEventDialog, {
              modelValue: row.attrs,
              logic: row.logic,
              designer: designer,
              selectedWidget,
              parentConfig,
              eventName,
              eventCode,
              variables,
              isCurd: this.propName === 'actions',
              onChange: (e) => {
                row.attrs = e;
              },
              onChangeLogic: (e) => {
                row.logic = e;
              }
            })
          }
        },
        {
          label: '操作',
          prop: 'action',
          align: 'center',
          fixed: 'right',
          render: ({ row, index }) => {
            return h('div', {}, [
              h(ElLink, {
                type: 'primary',
                icon: 'Remove',
                onClick: () => {
                  this.localVal.splice(index, 1);
                }
              })
            ]);
          }
        },
      ];
      if (!(actionTypeOptions && actionTypeOptions.length > 0)) {
        columns.splice(1, 1);
      }
      if (showPosition) {
        columns.splice(columns.length - 7, 0, {
          label: '展示位置',
          prop: 'position',
          width: '120px',
          render: ({ row }) => {
            const FuniSelect = resolveComponent('FuniSelect');
            return h(FuniSelect, {
              modelValue: row.position,
              placeholder: '请选择',
              options: [{ label: '左', value: 'left' }, { label: '右', value: 'right' }],
              onChange: (e) => {
                row.position = e;
              }
            })
          }
        })
      }
      return columns;
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.localVal = [...(deepClone(newVal) || [])];
      },
      immediate: true
    }
  },
  methods: {
    addButtons () {
      this.localVal = [...deepClone(this.modelValue || [])];
      this.dialogVisible = true;
    },
    onSave () {
      const { localVal } = this;
      this.$emit('update:modelValue', localVal);
      this.dialogVisible = false;
    }
  },
}
</script>