<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-30 17:42:57
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-12 17:09:39
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\buttons-editor\buttons-event-dialog.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-link type="primary"
      icon="Edit"
      @click="handleEdit">编辑</el-link>
    <FuniEventEditor ref="funiEventEditorRef"
      :modalList="modalList"
      :formList="formList"
      :componentList="componentList"
      :variables="computedVariables"
      :pageList="pageList"
      :assignOptions="assignOptions"
      @save="handleSave" />
  </div>
</template>
<script setup>
import { computed, onMounted, reactive, ref, watchEffect, inject, getCurrentInstance, shallowReactive } from 'vue';
import lowcode from '../../../common/utils/lowcode';
import { findAllWidget, findAllField } from '../../../common/utils/util.js';
const emit = defineEmits('change', 'update:modelValue');

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  parentConfig: [Object],
  designer: Object,
  selectedWidget: Object,
  optionModel: Object,
  parentWidget: Object,
  //事件名称
  eventName: {
    type: String,
    default: '点击事件'
  },
  //事件代码
  eventCode: {
    type: String,
    default: 'onClick'
  },
  //接口事件的参数
  argName: {
    type: String,
    default: ''
  },
  //事件对象
  logic: {
    type: Object
  },
  //是否需要包装@{}
  needWrap: {
    type: Boolean,
    default: true
  },
  //是否嵌套
  isNest: {
    type: Boolean,
    default: false
  },
  variables: {
    type: Array,
    default: () => {
      return []
    }
  },
  isCurd: {
    type: Boolean,
    default: false
  },
  eventLabel: {
    type: String,
    default: ''
  }
})
const instance = getCurrentInstance();
const funiEventEditorRef = ref();

const attrsCode = ref('');

// ID集合
const idCollectObject = inject('idCollectObject', {});

const pages = reactive([]);

const gloptions = shallowReactive([]);
const dqoptions = shallowReactive([]);
const changeVariables = shallowReactive([]);

watchEffect(() => {
  let newVal = props.modelValue ?? '';
  newVal = newVal.replaceAll('@{', '');
  if (newVal && newVal.length > 0 && newVal[newVal.length - 1] === '@') {
    newVal = newVal.substring(0, newVal.length - 2);
  }
  attrsCode.value = newVal;
})

onMounted(() => {
  if (idCollectObject && idCollectObject.app_id) {
    let params = { app_id: idCollectObject.app_id };
    instance.proxy.$lowCodeRequest.postPageFindAllAsync(params)
      .then(res => {
        let list = res.list;
        if (list && list.length > 0) {
          list.forEach(item => {
            pages.push({ name: item.name, path: `/app/${idCollectObject.app_code}/${item.id}` });
          })
        }
      })
  }
})

const modalList = computed(() => {
  let modals = [];
  if (props.parentConfig && props.parentConfig.formConfig && props.parentConfig.formConfig.dialogs && props.parentConfig.formConfig.dialogs.length > 0) {
    modals = props.parentConfig.formConfig.dialogs.map(m => {
      return {
        label: m.name,
        value: m.name
      }
    })
  } else if (!props.parentConfig) {
    if (props.designer && props.designer.formConfig && props.designer.formConfig.dialogs && props.designer.formConfig.dialogs.length > 0) {
      modals = props.designer.formConfig.dialogs.map(m => {
        return {
          label: m.name,
          value: m.name
        }
      })
    }
  }
  return modals;
})

const formList = computed(() => {
  let forms = [];
  if (props.designer && props.designer.formConfig) {
    forms = [{ label: '表单', value: props.designer.formConfig.refName }];
  }
  return forms;
})

const componentList = computed(() => {
  let components = [];
  if (props.designer && props.designer.widgetList && props.designer.widgetList.length > 0) {
    components = findAllWidget(props.designer.widgetList).filter(item => ['funi-show-curd', 'sfc-draggable-curd', 'sfc-gantt', 'funi-histogram-chart', 'sfc-ol-map'].includes(item.type))
      .map(item => {
        let methods = [];
        if (item.type === 'funi-show-curd') {
          methods.push({ key: '刷新', value: 'refresh' }, { key: '添加列表数据', value: 'pushList' }, { key: '删除', value: 'delete' }, { key: '重置列表', value: 'reloadList' });
        } else if (item.type === 'sfc-draggable-curd') {
          methods.push({ key: '添加', value: 'push' }, { key: '校验', value: 'validate' }, { key: '添加列表数据', value: 'pushList' }, { key: '删除', value: 'delete' });
        } else if (['sfc-gantt', 'funi-histogram-chart'].includes(item.type)) {
          methods.push({ key: '刷新', value: 'refresh' });
        } else if (item.type === 'sfc-ol-map') {
          methods.push({ key: '获取地图实例', value: 'getMap' }, { key: '获取地图view', value: 'getView' }, { key: '设置地图中心点', value: 'setCenter' }, { key: '设置地图zoom', value: 'setZoom' }, { key: '图斑绘制', value: 'draw' }, { key: '清除之前绘制', value: 'drawClear' },
            { key: '拆分图斑', value: 'drawSplit' }, { key: '撤销', value: 'drawRevoke' }, { key: '自适应范围', value: 'mapFit' }, { key: '获取当前视窗经纬度', value: 'getViewBox' },
            { key: '添加边界图层', value: 'addBoundAreaLayer' }, { key: '高亮要素', value: 'highLightFeatures' }, { key: '切换图层', value: 'changeLayer' }, { key: '通过sql查询高亮', value: 'highLightByFilter' },
            { key: '高亮图层清空', value: 'highLightLayerClear' }, { key: '关闭弹框', value: 'closeModal' }, { key: '添加图层', value: 'addLayer' }, { key: '移除指定图层', value: 'removeLayer' },
            { key: 'wms图层过滤', value: 'filterWms' }, { key: '同步地图', value: 'setView' }
          );
        }
        return {
          key: item.options.label,
          value: item.options.name,
          methods
        }
      });
  }
  return components;
})

const pageList = computed(() => {
  return pages;
})

const assignOptions = computed(() => {
  const { variables, isCurd, eventLabel } = props;
  let gl = [];
  let dq = [];
  if (['change', 'changeObj'].includes(eventLabel)) {
    let type = props.selectedWidget ? props.selectedWidget.type : '';
    if (['funi-select', 'sfc-select'].includes(type)) {
      gl = [...(gloptions || [])];
    } else {
      gl = [{ label: '值', value: 'e' }];
    }
  }
  if (variables && variables.length > 0) {
    let formVar = variables.find(item => item.label === '表单字段' || item.id === 'formData');
    if (formVar && formVar.children && formVar.children.length > 0) {
      if (isCurd) {
        formVar.children.forEach(item => {
          if (!['change', 'changeObj'].includes(eventLabel)) {
            gl.push({ label: item.label, value: item.expression });
          }
        })
        if (props.selectedWidget && props.selectedWidget.options) {
          const wgtName = props.selectedWidget.options.name;
          const curdVar = formVar.children.find(item => item.expression && item.expression.endsWith(wgtName));
          if (curdVar && curdVar.children && curdVar.children.length > 0) {
            curdVar.children.forEach(child => {
              if (!(child.children && child.children.length > 0)) {
                const expList = child.expression.split('context.params.');
                let value = expList.length > 1 ? expList[1] : child.expression;
                dq.push({ label: child.label, value });
              }
            })
          }
        }
      } else {
        formVar.children.forEach(item => {
          if (!['change', 'changeObj'].includes(eventLabel)) {
            gl.push({ label: item.label, value: item.expression });
          }
          if (!(item.children && item.children.length > 0)) {
            dq.push({ label: item.label, value: item.expression });
          }
        })
      }
    }
  }
  let assignOptions = {
    type: isCurd ? 'row' : 'form',
    gl,
    dq
  }
  return assignOptions;
});

const computedVariables = computed(() => {
  // return ['change', 'changeObj'].includes(props.eventLabel) ? changeVariables : props.variables;
  return props.variables;
})

const generateOptions = () => {
  gloptions.splice(0, gloptions.length);
  dqoptions.splice(0, dqoptions.length);
  changeVariables.splice(0, changeVariables.length);
  const generateVariables = () => {
    //生成变量
    if (gloptions.length > 0) {
      let vList = gloptions.map(g => {
        let item = {
          id: g.expression,
          label: g.label,
          value: g.expression,
          expression: g.expression
        };
        let children = (g.children || []).map(gc => {
          return {
            id: gc.expression,
            label: gc.label,
            value: g.expression,
            expression: gc.expression
          };
        });
        if (children && children.length > 0) {
          item.children = children;
        }
        return item;
      });
      changeVariables.push({ id: window.$utils.guid(), label: '关联字段', children: vList });
    }
    if (props.designer && props.designer.widgetList) {
      const fieldList = findAllField(props.designer.widgetList);
      if (fieldList && fieldList.length > 0) {
        const children = fieldList.map(item => {
          let child = {
            id: window.$utils.guid(),
            label: item.label,
            expression: `context.formData.${item.name}`
          };
          if (item.children && item.children.length > 0) {
            child.children = item.children.map(childItem => {
              return {
                id: window.$utils.guid(),
                label: childItem.label,
                expression: `context.formData&&context.formData.${item.name}?context.formData.${item.name}.list?.filter(item=>item.${childItem.name}).map(item=>item.${childItem.name}):[]`
              };
            });
          }
          return child;
        });
        let formChildren = children;
        if (
          idCollectObject &&
          idCollectObject.previousTabFields &&
          idCollectObject.previousTabFields.previous &&
          idCollectObject.previousTabFields.previous.length > 0
        ) {
          let stepFormChildren = [];
          let preSteps = idCollectObject.previousTabFields.previous;
          //添加前面步骤的表单字段
          preSteps.forEach(element => {
            if (element.tabTitle && element.fields && element.fields.length > 0) {
              let stepChildren = element.fields.map(item => {
                return {
                  id: window.$utils.guid(),
                  label: item.name,
                  expression: `context.stepData?.step${element.tabIndex}?.${item.key}`
                };
              });
              let stepFormItem = {
                id: 'stepFormData' + element.tabIndex,
                label: element.tabTitle,
                children: stepChildren
              };
              stepFormChildren.push(stepFormItem);
            }
          });
          formChildren = [
            ...stepFormChildren,
            {
              id: 'currentFomrData',
              label: idCollectObject.previousTabFields.activeTitle,
              children: formChildren
            }
          ];
        }
        changeVariables.push({
          id: 'formField',
          label: '表单字段',
          children: formChildren
        });
        changeVariables.push(
          {
            id: 'route',
            label: '路由参数',
            children: [
              { id: 'routeId', label: 'id', expression: 'context.query.id' },
              { id: 'routeBusinessId', label: '业务id', expression: 'context.query.businessId' },
              { id: 'routeCid', label: '关联id', expression: 'context.query.cid' },
              { id: 'routeBizName', label: '操作类型', expression: 'context.query.bizName' },
              { id: 'routeBusType', label: '业务类型', expression: 'context.query.busType' }
            ]
          },
          {
            id: 'workflow',
            label: '工作流信息',
            children: [
              {
                id: 'currentActivityId',
                label: '当前节点ID',
                expression: 'context.workflow?.businessFrontendInfo?.currentActivityId'
              },
              {
                id: 'currentActivityName',
                label: '当前节点名',
                expression: 'context.workflow?.businessFrontendInfo?.currentActivityName'
              }
            ]
          }
        );
      }
    }
    return changeVariables;
  };
  if (
    props.selectedWidget &&
    props.selectedWidget.options &&
    props.selectedWidget.options.modelOption &&
    (props.selectedWidget.options.modelOption.type === 'model' ||
      props.selectedWidget.options.modelOption.type === 'api')
  ) {
    let createFieldList = (fieldList, parentFieldName = '') => {
      let outList = fieldList.map(item => {
        let fieldId = `e.${parentFieldName}${item.code}`;
        let option = {
          label: `${item.name}`,
          config: item.config,
          value: `e.${parentFieldName}${item.code}`
        };
        if (item.children) {
          let parentName = `${parentFieldName}${item.code}?.`;
          let children = createFieldList(item.children, parentName, '');
          option.children = children;
        }
        return option;
      });
      return outList;
    };
    let model_id = props.selectedWidget.options.modelOption.modelId;
    let outputConfig = props.selectedWidget.options.modelOption.data
      ? props.selectedWidget.options.modelOption.data.outputConfig
      : null;
    if (model_id) {
      instance.proxy.$lowCodeRequest.postFieldFindAllTreeAsync({ model_id }).then(res => {
        let fieldList = createFieldList(res.list);
        gloptions.push(...fieldList);
        generateVariables();
      });
    } else if (outputConfig && outputConfig.length > 0) {
      const getFieldList = outputFieldList => {
        return outputFieldList.map(item => {
          let name = item.name;
          let code = item.field;
          let fieldObj = { name, code };
          if (item.children && item.children.length > 0) {
            const children = getFieldList(item.children);
            fieldObj.children = children;
          }
          return fieldObj;
        });
      };
      let list = getFieldList(outputConfig[0].children);
      let fieldList = createFieldList(list);
      fieldList.unshift({
        label: '标签',
        value: 'e.label',
      });
      fieldList.unshift({
        label: '值',
        value: 'e.value',
      });
      gloptions.push(...fieldList);
      generateVariables();
    }
  } else {
    gloptions.push({
      label: '标签',
      value: 'e.label',
    });
    gloptions.push({
      label: '值',
      value: 'e.value',
    });
  }
  generateVariables();
  let wgtList = [];
  if (props.isCurd) {
    if (props.parentWidget) {
      wgtList = findAllWidget(props.parentWidget.widgetList).filter(
        wgt => wgt.options.name !== props.selectedWidget.options.name
      );
    }
  } else {
    wgtList = findAllWidget(props.designer.widgetList).filter(
      wgt => wgt.options.name !== props.selectedWidget.options.name
    );
  }

  if (wgtList && wgtList.length > 0) {
    let options = wgtList.map(wgt => {
      return {
        label: wgt.options.label,
        value: wgt.options.name
      };
    });
    dqoptions.push(...options);
  }
}

const handleEdit = (e) => {
  if (['change', 'changeObj'].includes(props.eventLabel)) {
    generateOptions();
  } else {
    gloptions.splice(0, gloptions.length);
    dqoptions.splice(0, dqoptions.length);
    changeVariables.splice(0, changeVariables.length);
  }
  funiEventEditorRef.value.show({ name: props.eventName, code: props.eventCode, logic: props.logic });
  let newVal = props.modelValue ?? '';
  newVal = newVal.replaceAll('@{', '');
  if (newVal && newVal.length > 0 && newVal[newVal.length - 1] === '@') {
    newVal = newVal.substring(0, newVal.length - 2);
  }
}

const handleSave = (syncData) => {
  let body = lowcode.generateEvent(syncData, props.isNest, props.argName);
  attrsCode.value = `${props.eventCode}:${body}`;
  let code = '';
  if (props.needWrap) {
    code = `@{${attrsCode.value}}@`;
  } else {
    code = `${attrsCode.value}`;
  }
  emit('change', code);
  emit('change-body', body);
  emit('update:modelValue', code);
  emit('change-logic', syncData);
  funiEventEditorRef.value.hide();
}

</script>
<style lang='scss' scoped>
</style>