<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-30 17:42:57
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-08 15:05:59
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\buttons-editor\buttons-code-dialog.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-link type="primary"
      icon="Edit"
      @click="handleEdit">编辑</el-link>
    <sfc-design-dialog v-model="dialogVisible"
      title="属性编辑"
      width="1000px"
      destroy-on-close
      :append-to-body="true">
      <div>
        <el-alert type="info"
          :closable="false"
          title="{" />
        <CodeEdit :userWorker="true"
          v-model="attrsCode" />
        <el-alert type="info"
          :closable="false"
          title="}" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary"
            @click="handleSave">
            保存
          </el-button>
        </span>
      </template>
    </sfc-design-dialog>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { reactive, ref, watchEffect } from 'vue';
import CodeEdit from '../../../code-editor/index.vue'
const emit = defineEmits('update:modelValue');

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const dialogVisible = ref(false);

const attrsCode = ref('');

watchEffect(() => {
  let newVal = props.modelValue ?? '';
  newVal = newVal.replaceAll('@{', '');
  if (newVal && newVal.length > 0 && newVal[newVal.length - 1] === '@') {
    newVal = newVal.substring(0, newVal.length - 2);
  }
  attrsCode.value = newVal;
})

const handleEdit = (e) => {
  dialogVisible.value = true;
  let newVal = props.modelValue ?? '';
  newVal = newVal.replaceAll('@{', '');
  if (newVal && newVal.length > 0 && newVal[newVal.length - 1] === '@') {
    newVal = newVal.substring(0, newVal.length - 2);
  }
  attrsCode.value = newVal;
}

const handleSave = () => {
  dialogVisible.value = false;
  let code = `@{${attrsCode.value}}@`;
  emit('change', code);
  emit('update:modelValue', code);
}

</script>
<style lang='scss' scoped>
</style>