<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-01 13:55:49
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-18 18:27:07
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\defaultValue-editor.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item v-if="!hasConfig('optionItems')">
    <template #label>
      <span>{{ i18nt('designer.setting.defaultValue') }}
        <el-tooltip effect="light"
          content="计算公示:初始化不会联动仅展示已保存的值,数据联动:初始化加载也从联动属性计算值。">
          <svg-icon icon-class="el-info" /></el-tooltip>
      </span>
    </template>
    <el-row style="width: 100%;">
      <funi-select :options="options"
        v-model="optionModel.defaultAction"
        style="margin-bottom: 8px;" />
    </el-row>
    <el-row>
      <FuniVariableSetter class="setting-variable"
        :modelValue="optionModel.defaultValue"
        @change="handleChange"
        :variables="variables" />
    </el-row>
  </el-form-item>
</template>

<script>
import i18n from '../../common/utils/i18n';
import propertyMixin from './propertyMixin';
import fieldVariablesMixins from './fieldVariablesMixins';
import SvgIcon from '../../svg-icon/index.vue';
import { isNotNullExpression } from '../../common/utils/util';

export default {
  name: 'defaultValue-editor',
  mixins: [i18n, propertyMixin, fieldVariablesMixins],
  components: { SvgIcon },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  data () {
    return {
      variables: [],
      options: [
        { label: '计算公示', value: '1' },
        { label: '数据联动', value: '2' }
      ]
    };
  },
  mounted () { },
  methods: {
    handleChange (e) {
      let newVal = e;
      if (e && !isNotNullExpression(e)) {
        newVal = '';
      }
      if (this.optionModel) {
        this.optionModel.defaultValue = newVal;
      }
      this.emitDefaultValueChange(newVal);
    }
  }
};
</script>

<style scoped></style>
