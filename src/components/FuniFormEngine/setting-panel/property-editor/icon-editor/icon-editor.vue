<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-10-07 11:05:14
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-10-07 15:17:47
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\icon-editor\icon-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="icon-editor-container">
    <div class="defaultIcon"
      v-if="modelValue">
      <FuniIcon :icon="modelValue"
        style="font-size: 18px; margin: 0 10px" />
      <div class="delBtn"
        @click="delModelValue">
        <el-icon>
          <Close />
        </el-icon>
      </div>
    </div>
    <el-button link
      icon="Edit"
      @click="dialogVisible=true" />
    <el-dialog :close-on-click-modal="false"
      v-model="dialogVisible"
      :title="title"
      width="800"
      align-center
      append-to-body>
      <div class="iconBox">
        <span class="svgSpan"
          :class="[iconName == item ? 'clicked' : '']"
          v-for="(item,index) in epIcons"
          :key="index"
          @click="iconName = item">
          <FuniIcon :icon="item"
            style="font-size: 25px" />
        </span>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary"
          @click="handleOk"> 确定 </el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import { reactive, ref, unref, watch } from 'vue';
import { toShortLineCase } from '../../../common/utils/util.js';
const emit = defineEmits(['change', 'update:modelValue'])
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})
const epIcons = reactive([]);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  epIcons.push(`ep:${toShortLineCase(key)}`);
}
const dialogVisible = ref(false);
const title = '选择图标';
const iconName = ref(props.modelValue);

watch(() => props.modelValue, (newVal) => {
  iconName.value = newVal;
}, { immediate: true })

const handleOk = () => {
  const val = unref(iconName);
  emit('change', val);
  emit('update:modelValue', val);
  dialogVisible.value = false;
}

const delModelValue = () => {
  iconName.value = '';
  emit('change', '');
  emit('update:modelValue', '');
}

</script>
<style lang='scss' scoped>
.icon-editor-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.iconBox {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  width: calc(100% + 20px);
  margin-left: -10px;
  max-height: 500px;
  overflow: auto;
}
.svgSpan {
  display: inline-flex;
  margin: 10px;
  justify-content: center;
  align-items: center;
  padding: 3px;
  border-radius: 2px;
}
.svgSpan {
  transition: all 0.3s;
}
.svgSpan:hover {
  transform: scale(1.2);
  background-color: var(--el-color-primary-light-7);
}
.clicked {
  background-color: var(--el-color-primary-light-7);
}
.defaultIcon {
  padding: 5px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 5px;
}
.defaultIcon:hover .delBtn {
  display: flex;
}
.delBtn {
  display: none;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0px;
  right: 0px;
  font-size: 10px;
  width: 10px;
  height: 10px;
  border: 1px solid var(--el-color-info-light-3);
  color: var(--el-color-info-light-3);
  border-radius: 50%;
}
</style>