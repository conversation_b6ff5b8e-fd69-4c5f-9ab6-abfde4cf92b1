<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-04-24 14:52:26
 * @FilePath: \funi-form-engine-demo\src\components\FuniFormEngine\setting-panel\property-editor\field-date\date-defaultValue-editor.vue
 * @Description: 
-->
<template>
  <el-form-item>
    <template #label>
      <span>{{i18nt('designer.setting.defaultValue')}}
        <el-tooltip effect="light"
          content="计算公示:初始化不会联动仅展示已保存的值,数据联动:初始化加载也从联动属性计算值。">
          <svg-icon icon-class="el-info" /></el-tooltip>
      </span>
    </template>
    <el-row>
      <funi-select :options="options"
        v-model="optionModel.defaultAction"
        style="margin-bottom:8px;" />
    </el-row>
    <el-row>
      <FuniVariableSetter class="setting-variable"
        v-model="optionModel.defaultValue"
        @change="emitDefaultValueChange"
        :variables="variables" />
    </el-row>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";
import propertyMixin from "../propertyMixin"
import { findAllField } from "../../../common/utils/util";

export default {
  name: "date-defaultValue-editor",
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      variables: [],
      options: [{ label: '计算公示', value: '1' }, { label: '数据联动', value: '2' },]
    }
  },
  mounted () {

  },
  watch: {
    designer: {
      handler (newVal) {
        if (newVal) {
          const widgetList = findAllField(newVal.widgetList);
          let selectName = '';
          if (this.selectedWidget && this.selectedWidget.options && this.selectedWidget.options.name) {
            selectName = this.selectedWidget.options.name;
          }
          const children = widgetList.filter(wgt => wgt.name !== selectName).map(item => {
            return {
              id: window.$utils.guid(), label: item.label, expression: `context.formData.${item.name}`
            }
          })
          this.variables = [{
            id: 'formData',
            label: '表单字段',
            children
          }]
        } else {
          this.variables = [];
        }
      },
      deep: true,
      immediate: true
    },
    selectedWidget: {
      handler (newVal) {
        const widgetList = findAllField(this.designer.widgetList);
        let selectName = '';
        if (newVal && newVal.options && newVal.options.name) {
          selectName = newVal.options.name;
        }
        const children = widgetList.filter(wgt => wgt.name !== selectName).map(item => {
          return {
            id: window.$utils.guid(), label: item.label, expression: `context.formData.${item.name}`
          }
        })
        this.variables = [{
          id: 'formData',
          label: '表单字段',
          children
        }]
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style scoped>
</style>
