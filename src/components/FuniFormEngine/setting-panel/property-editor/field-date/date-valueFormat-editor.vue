<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-01 13:55:50
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-02-07 17:03:06
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\field-date\date-valueFormat-editor.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.valueFormat')">
    <el-select v-model="optionModel.valueFormat"
      filterable
      allow-create>
      <el-option label="YYYY-MM-DD"
        value="YYYY-MM-DD" />
      <el-option label="YYYY/MM/DD"
        value="YYYY/MM/DD" />
      <el-option label="YYYY年MM月DD日"
        value="YYYY年MM月DD日" />
      <el-option label="YYYY-MM-DD HH:mm:ss"
        value="YYYY-MM-DD HH:mm:ss" />
      <el-option label="YYYY-MM-DD hh:mm:ss"
        value="YYYY-MM-DD hh:mm:ss" />
      <el-option label="YYYY"
        value="YYYY" />
      <el-option label="YYYY年"
        value="YYYY年" />
      <el-option label="[Week] ww"
        value="[Week] ww" />
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";

export default {
  name: "date-valueFormat-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped>
</style>
