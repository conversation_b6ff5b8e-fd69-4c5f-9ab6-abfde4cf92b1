<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-01 13:55:50
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-02-07 17:13:56
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\field-date\date-type-editor.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.displayType')">
    <el-select v-model="optionModel.type"
      @change="handleChange">
      <el-option label="日期时间"
        value="datetime"></el-option>
      <el-option label="日期"
        value="date"></el-option>
      <el-option label="多选日期"
        value="dates"></el-option>
      <el-option label="年"
        value="year"></el-option>
      <el-option label="月"
        value="month"></el-option>
      <el-option label="周"
        value="week"></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";

export default {
  name: "date-type-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  methods: {
    handleChange (e) {
      switch (e) {
        case 'datetime':
          Object.assign(this.optionModel, { format: 'YYYY-MM-DD HH:mm:ss', valueFormat: 'YYYY-MM-DD HH:mm:ss' });
          break;
        case 'date': case 'dates':
          Object.assign(this.optionModel, { format: 'YYYY-MM-DD', valueFormat: 'YYYY-MM-DD' });
          break;
        case 'year':
          Object.assign(this.optionModel, { format: 'YYYY年', valueFormat: 'YYYY年' });
          break;
        case 'month':
          Object.assign(this.optionModel, { format: 'YYYY-MM', valueFormat: 'YYYY-MM' });
          break;
        case 'week':
          Object.assign(this.optionModel, { format: '[Week] ww', valueFormat: '[Week] ww' });
          break;
        default:
          break;
      }
    }
  }
}
</script>

<style scoped>
</style>
