<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-11-24 16:18:57
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-11-27 10:52:44
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\setting-panel\property-editor\field-date\date-format-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('designer.setting.format')">
    <el-select v-model="optionModel.format"
      filterable
      allow-create>
      <el-option label="YYYY-MM-DD"
        value="YYYY-MM-DD" />
      <el-option label="YYYY/MM/DD"
        value="YYYY/MM/DD" />
      <el-option label="YYYY年MM月DD日"
        value="YYYY年MM月DD日" />
      <el-option label="YYYY-MM-DD HH:mm:ss"
        value="YYYY-MM-DD HH:mm:ss" />
      <el-option label="YYYY-MM-DD hh:mm:ss"
        value="YYYY-MM-DD hh:mm:ss" />
      <el-option label="YYYY"
        value="YYYY" />
      <el-option label="YYYY年"
        value="YYYY年" />
      <el-option label="[Week] ww"
        value="[Week] ww" />
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n";

export default {
  name: "date-format-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },

}
</script>

<style scoped>
</style>
