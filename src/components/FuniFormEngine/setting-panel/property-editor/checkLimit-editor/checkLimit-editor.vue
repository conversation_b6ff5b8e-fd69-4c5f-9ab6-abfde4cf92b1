<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-10-30 10:35:37
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-30 11:10:28
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\checkLimit-editor\checkLimit-editor.vue
 * @Description: 
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved. 
-->
<template>
  <el-form-item :label="i18nt('extension.setting.checkLimit')">
    <FuniVariableSetter class="setting-variable"
      :modelValue="optionModel.checkLimit"
      :variables="variables"
      @change="handleChange"
      style="width: 100%" />
  </el-form-item>
</template>

<script>
import i18n from "../../../common/utils/i18n"
import fieldVariablesMixins from '../fieldVariablesMixins';
import { isNotNullExpression } from '../../../common/utils/util';

export default {
  name: "checkLimit-editor",
  mixins: [i18n, fieldVariablesMixins],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data () {
    return {
      variables: [],
    }
  },
  methods: {
    handleChange (e) {
      let newVal = e;
      if (e && !isNotNullExpression(e)) {
        newVal = '';
      }
      if (this.optionModel) {
        this.optionModel.checkLimit = newVal;
      }
    }
  }
}
</script>

<style scoped>
</style>
