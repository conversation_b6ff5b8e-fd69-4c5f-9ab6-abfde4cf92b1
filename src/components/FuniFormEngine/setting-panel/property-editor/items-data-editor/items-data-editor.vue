<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-13 20:19:36
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-11-07 10:02:37
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\items-data-editor\items-data-editor.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="items-data-editor">
    <el-table size="small"
      :data="localVal"
      scrollbar-always-on>
      <el-table-column v-for="(col, index) in columns"
        :key="index"
        :prop="col.prop"
        :show-overflow-tooltip="true"
        :label="col.label"
        :width="col.width?col.width:50">
        <template #default="{ row }">
          <component :is="col.type ?? 'el-input'"
            v-bind="col.props ?? {}"
            v-model="row[col.prop]" />
        </template>
      </el-table-column>
      <el-table-column label="操作"
        width="60"
        align="center">
        <template #header>
          <el-link type="primary"
            icon="Plus"
            @click="handleAdd">添加</el-link>
        </template>
        <template #default="scope">
          <el-button link
            type="primary"
            size="small"
            @click="handleDelete(scope.$index)"
            icon="CircleClose" />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { computed } from 'vue';
const props = defineProps({
  designer: Object,
  selectedWidget: Object,
  modelValue: Array,
  columns: {
    type: Array,
    default: () => []
  }
});
const emits = defineEmits(['update:modelValue']);
const localVal = computed({
  get: () => {
    return props.modelValue;
  },
  set: newVal => {
    emits('update:modelValue', newVal);
  }
});
const handleAdd = () => {
  if (!localVal.value) {
    localVal.value = [];
  }
  localVal.value = [...localVal.value, {}];
};
const handleDelete = index => {
  if (Array.isArray(localVal.value)) {
    const newItem = [...localVal.value];
    newItem.splice(index, 1);
    localVal.value = newItem;
  }
};
</script>