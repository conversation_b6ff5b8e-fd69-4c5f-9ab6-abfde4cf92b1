<template>
  <el-form-item :label="i18nt('designer.setting.fieldTooltip')"
    v-if="!noFieldTooltipSetting">
    <el-input type="textarea"
      :maxlength="200"
      v-model="optionModel.fieldTooltip" />
  </el-form-item>
</template>

<script>
import i18n from "../../common/utils/i18n"

export default {
  name: "fieldTooltip-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  computed: {
    noFieldTooltipSetting () {
      return (this.selectedWidget.type === 'static-text') || (this.selectedWidget.type === 'html-text')
      //|| (this.selectedWidget.type === 'divider')
    },

  }
}
</script>

<style lang="scss" scoped>
</style>
