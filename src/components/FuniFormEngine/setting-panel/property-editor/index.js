/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-06 18:04:44
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-09 14:22:33
 * @FilePath: \src\components\FuniFormEngine\setting-panel\property-editor\index.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
let comps = {};

const modules = import.meta.glob('./**/*.vue', { eager: true });
for (const path in modules) {
  let cname = modules[path].default.name;
  comps[cname] = modules[path].default;
}

export default comps;
