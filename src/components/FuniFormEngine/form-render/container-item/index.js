/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-07-17 16:37:31
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-09 14:21:41
 * @FilePath: \src\components\FuniFormEngine\form-render\container-item\index.js
 * @Description:
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved.
 */
const modules = import.meta.glob('./*.vue', { eager: true });

export default {
  install(app) {
    for (const path in modules) {
      let cname = modules[path].default.name;
      app.component(cname, modules[path].default);
    }
  }
};
