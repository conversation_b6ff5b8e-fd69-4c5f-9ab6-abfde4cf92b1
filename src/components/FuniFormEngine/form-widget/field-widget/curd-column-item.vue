<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-10-19 17:58:54
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-02-28 17:04:48
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\form-widget\field-widget\curd-column-item.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <ContainerItemWrapper :widget="widget"
    class="curd-column-item">
    <div class="column-label"
      :style="columnStyle"><span style="color:red;margin:4px 4px 0px 0px;"
        v-show="required">*</span>{{label}}</div>
    <div class="column-component"
      :style="columnStyle">
      <slot></slot>
    </div>
  </ContainerItemWrapper>
</template>

<script>
import emitter from '../../common/utils/emitter'
import i18n from '../../common/utils/i18n'
import refMixin from "../../form-render/refMixin"
import ContainerItemWrapper from '../../form-render/container-item/container-item-wrapper.vue'
import containerItemMixin from "../../form-render/container-item/containerItemMixin"
import { reactive, toRefs, watch } from 'vue'
export default {
  name: 'curd-column-item',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper
  },
  props: {
    label: {
      type: String
    },
    align: {
      type: String,
      default: 'center'
    },
    width: {
      type: [String, Number]
    },
    required: {
      type: Boolean,
      default: false
    },
    widget: Object,
  },
  inject: ['refList', 'globalModel'],
  created () {
    this.initRefList()
  },
  setup (props) {
    const state = reactive({
      columnStyle: {
        'justify-content': props.align ?? 'center',
      }
    })
    watch(() => props.align, (newVal) => {
      state.columnStyle['justify-content'] = newVal ?? 'center';
    })
    return { ...toRefs(state) }
  }
}
</script>
<style lang="scss" scoped>
.curd-column-item {
  display: flex;
  flex-direction: column;
  .column-label {
    border-top: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    height: 38px;
  }
  .column-component {
    padding: 8px 4px;
    display: flex;
    height: 54px;
    overflow: hidden;
    align-items: center;
  }
}
</style>
