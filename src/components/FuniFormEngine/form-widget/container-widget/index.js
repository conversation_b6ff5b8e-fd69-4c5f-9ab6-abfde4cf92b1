/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 15:25:49
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-09 14:22:14
 * @FilePath: \src\components\FuniFormEngine\form-widget\container-widget\index.js
 * @Description:
 */
const modules = import.meta.glob('./*.vue', { eager: true });

export default {
  install(app) {
    for (const path in modules) {
      let cname = modules[path].default.name;
      app.component(cname, modules[path].default);
    }
  }
};
