<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-26 15:50:39
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-21 16:44:33
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\funi-sfc-preview\index.vue
 * @Description: 
-->
<template>
  <div class="index">
    <sfc-design-dialog :title="title"
      v-model="dialogVisible"
      width="65%"
      destroy-on-close
      @close="e=>{isLoad=false;$emit('change-status',false)}">
      <div style="padding:0px 12px;">
        <div v-if="designerConfig.hasModel">
          <iframe :id="frameId"
            :src="src"
            style="width:100%;height: 500px;"
            frameborder="0"
            scrolling="auto" />
        </div>
        <form-preview v-else
          :key="key"
          :sfcCode="sfcCode"
          :isLoad="isLoad"
          :headers="headers" />
      </div>
    </sfc-design-dialog>
  </div>
</template>

<script>
import FormPreview from './FormPreview.vue';
export default {
  name: 'index',
  components: {
    FormPreview
  },
  props: {
    title: {
      type: String,
      default: '组件预览'
    },
    //表单上传请求头
    headers: {
      type: Object
    }
  },
  inject: ['getDesignerConfig'],
  data () {
    return {
      frameId: window.$utils.guid(),
      src: '/#/as/preview',
      // designerConfig: this.getDesignerConfig(),
      designerConfig: { hasModel: false },
      dialogVisible: false,
      sfcCode: '',
      key: 0,
      formDisabled: false,
      isLoad: true
    };
  },
  mounted () { },
  methods: {
    show (sfcCode) {
      this.sfcCode = sfcCode;
      this.isLoad = true;
      this.dialogVisible = true;
      this.$emit('change-status', true)
      this.key++;
      if (this.designerConfig.hasModel) {
        setTimeout(() => {
          var preWindow = document.getElementById(this.frameId).contentWindow;
          preWindow.postMessage('preview message', '*');
        }, 1000);
      }
    },

  }
};
</script>
