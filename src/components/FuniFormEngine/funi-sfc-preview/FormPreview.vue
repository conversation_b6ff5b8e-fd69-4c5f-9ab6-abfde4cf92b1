<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-03-21 10:49:18
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-22 14:32:10
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\funi-sfc-preview\FormPreview.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <div style="min-height:400px;">
      <SfcRender ref="sfcRenderRef"
        :key="key"
        :sfcCode="sfcCode"
        :headers="headers"
        :isLoad="isLoad" />
    </div>
    <el-row type="flex"
      justify="center">
      <el-button type="primary"
        @click="resetForm">重置</el-button>
      <el-button type="primary"
        @click="submitForm"> 获取数据 </el-button>
      <el-button type="primary"
        @click="toggleEditStatus">切换编辑状态</el-button>
    </el-row>
  </div>
</template>
<script>
import { ElMessage } from 'element-plus';
import SfcRender from './SfcRender.vue';
import { reactive, ref } from 'vue';
export default {
  components: {
    SfcRender
  },
  props: {
    //表单上传请求头
    headers: {
      type: Object
    },
    isLoad: {
      type: Boolean,
      default: false
    },
    key: {
      type: Number,
      default: 0
    },
    sfcCode: {
      type: String,
      default: ''
    }
  },
  data () {
    return {}
  },
  methods: {
    submitForm () {
      this.$refs.sfcRenderRef.submitForm().then(res => {
        if (res.valid) {
          ElMessage({
            message: JSON.stringify(res.data),
            type: 'success'
          });
        } else {
          ElMessage({
            message: JSON.stringify(res.errors),
            type: 'success'
          });
        }
      });
    },
    resetForm () {
      this.$refs.sfcRenderRef.resetForm();
    },
    setFormData () {
      // this.$refs.sfcRenderRef.setFormData({ funishowcurd95429: { selectedRowKeys: ['d8861f6c-c97c-471c-8f93-db556f5a7bc4'] } });
      // this.$refs.sfcRenderRef.setFormDisabled(true);
    },
    toggleEditStatus () {
      this.formDisabled = !this.formDisabled;
      this.$refs.sfcRenderRef.setFormDisabled(this.formDisabled);
    }
  }
}
</script>
<style lang='scss' scoped>
</style>