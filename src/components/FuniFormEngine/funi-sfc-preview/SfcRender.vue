<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-04-23 17:58:53
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-11-17 16:58:21
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\funi-sfc-preview\SfcRender.vue
 * @Description: 
-->
<script setup>
import { h, reactive, defineComponent, useAttrs, ref } from "vue";
import { compile } from "../common/utils/vue3SfcCompiler";
const props = defineProps({
  sfcCode: {
    type: String,
  },
  isLoad: {
    type: Boolean,
    default: false
  },
});
const childRef = ref();
const ChildComponent = compile(props.sfcCode);
const attrs = useAttrs();
const render = h(ChildComponent, { ...attrs });
const submitForm = () => {
  return childRef.value.submitForm();
}
const resetForm = () => {
  childRef.value.resetForm();
}

const setFormData = (data, attach = true) => {
  childRef.value.setFormData(data, attach);
}

const getForm = () => {
  return childRef.value;
}

const setFormDisabled = (disabled) => {
  childRef.value.setFormDisabled(disabled);
}

defineExpose({ submitForm, resetForm, setFormData, setFormDisabled });
</script>
<template>
  <div class="preview-render">
    <render v-if="isLoad"
      ref="childRef" />
  </div>
</template>
<style lang="less" scoped>
.preview-render {
  overflow: auto;
}
</style>