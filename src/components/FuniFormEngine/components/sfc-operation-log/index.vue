<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-01-17 17:25:06
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-21 15:03:28
 * @FilePath: \src\components\FuniFormEngine\components\sfc-operation-log\index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div style="width:100%;">
    <sfc-operation-table :params="params"
      v-bind="$attrs" />
  </div>
</template>
<script setup>
import SfcOperationTable from './sfc-operation-table.vue';
import { useRoute } from 'vue-router';
import { reactive, ref, inject } from 'vue';
const route = useRoute();
const tempData = inject('tempData') || {};
const mainModelId = inject('mainModelId') || '';

let id = tempData?.id;
if (!id && route && route.query && route.query.id) {
  id = route.query.id;
}
const params = reactive({
  id,
  model_id: mainModelId
})
</script>
<style lang='scss' scoped>
</style>