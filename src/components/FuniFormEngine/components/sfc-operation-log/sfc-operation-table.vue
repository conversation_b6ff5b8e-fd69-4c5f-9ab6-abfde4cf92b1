<!--办件记录-->
<template>
  <div class="sfc-operation-table">
    <funi-curd :columns="columns"
      :data="curdData"
      :loading="loading"
      :pagination="false"
      :expand-row-keys="expandRowKeys">
      <template #expand="{ row }">
        <div class="expand-table"
          style="padding-left: 40px">
          <funi-curd :columns="expandColumns"
            :data="row.recordList"
            :loading="false"
            :pagination="false" />
        </div>
      </template>
    </funi-curd>
  </div>
</template>

<script setup>
import { watch, ref, reactive, resolveComponent, h } from 'vue';
import { useRoute } from 'vue-router';
import Vrouter from '@/router';
const route = useRoute();
const router = Vrouter;

let app_code, page_id;
let hash = window.location.hash;
if (hash && hash.length > 0) {
  hash = window.location.hash.split('?')[0];
  let hashList = hash.split('/');
  if (hashList && hashList.length >= 3) {
    page_id = hashList[hashList.length - 1];
    app_code = hashList[hashList.length - 2];
  }
}

/**
 * 默认接口
 */
const listApi = `/as/${app_code}/findBusRecordList`;

defineOptions({
  name: 'SfcOperationLog',
  inheritAttrs: false
});

const props = defineProps({
  data: { type: Array, default: () => [] },
  api: { type: String, default: '' },
  params: {
    type: Object,
    default: () => ({})
  }
});

watch(
  () => props.data,
  newData => {
    if (props.data) {
      curdData.value = newData;
    }
  },
  { deep: true }
);

const curdData = ref([]);
const loading = ref(false);
const expandRowKeys = reactive([]);

if (props.data.length === 0) {
  loading.value = true;
  window.$http
    .post(props.api || listApi, props.params)
    .then(res => {
      curdData.value = res.list;
      if (res.list && res.list.length > 0) {
        expandRowKeys.push(res.list[0].id);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

const columns = ref([
  {
    type: 'expand',
    width: '50px',
    slots: {
      default: 'expand'
    }
  },
  {
    label: '业务件号',
    prop: 'business_id',
    render: ({ row, index }) => {
      const ElLink = resolveComponent('el-link');
      return h(
        ElLink,
        {
          type: 'primary',
          onClick: () => {
            const { id, business_id, bus_type, business_status_name } = row;
            let query = {
              id,
              businessId: business_id,
              bizName: '详情',
              busType: bus_type,
              statusName: business_status_name
            };
            let path = route.path;
            router.push({ path, query });
          }
        },
        row.business_id
      );
    }
  },
  {
    label: '业务类型',
    prop: 'bus_type_name'
  },
  {
    label: '业务件状态',
    prop: 'business_status_name'
  },
  {
    label: '申请时间',
    prop: 'create_time'
  }
]);

const expandColumns = ref([
  {
    label: '处理时间',
    prop: 'executeTime',
    width: '200px'
  },
  {
    label: '处理结果',
    prop: 'content',
    render: ({ row, index }) => {
      return row.content === '启动业务' ? '启动业务' : row.activityName + row.content;
    }
  },
  {
    label: '处理人员',
    prop: 'operator',
    render: ({ row, index }) => {
      return row.operator ? row.operator.operatorName : '--';
    }
  },
  {
    label: '处理意见',
    prop: 'opinion',
    width: '300'
  },
  {
    label: '备注',
    prop: 'operateRemark',
    width: '500'
  }
]);
</script>

<style lang="scss" scoped>
.sfc-operation-table {
  .expand-table {
    :deep(.funi-curd__header) {
      display: none !important;
    }
  }
}
</style>
