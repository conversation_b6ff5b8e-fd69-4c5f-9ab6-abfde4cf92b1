<template>
  <div class="sfc-curd">
    <funi-curd v-bind="$attrs"
      :columns="columnsComputed"
      :data="localVal.list">
      <template #header>
        <div>
          <funi-group-title :title="label"
            groupMargin="0px"
            style="margin:4px 0px;" />
        </div>
      </template>
      <template #buttonGroup>
        <component v-for="(item, index) in leftButtonList"
          :key="index"
          :is="item.component"
          v-bind="item.props || {}"
          v-on="item.on || {}"
          :style="item.style || {}"
          v-auth="addAuth" />
      </template>
      <template #extendButtonGroup>
        <component v-for="(item, index) in rightButtonList"
          :key="index"
          :is="item.component"
          v-bind="item.props || {}"
          v-on="item.on || {}"
          :style="item.style || {}"
          v-auth="addAuth" />
      </template>
      <template v-for="(col,index) in columnsComputed.filter(col=>!!col.prop)"
        :key="index"
        #[col.prop]>
        <span><span v-show="col&&col.required"
            style="color:red;margin:4px 4px 0px 0px;">*</span>{{col.label}}</span>
      </template>
    </funi-curd>
  </div>
</template>

<script>
import { ElNotification } from 'element-plus';
import { h, resolveComponent, resolveDirective, withDirectives } from 'vue';
export default {
  name: 'index',
  components: {
  },
  props: {
    modelValue: {
      type: Object,
      default: () => {
        return {
          list: []
        }
      }
    },
    label: {
      type: String
    },
    disabled: {
      type: Boolean,
      default: false
    },
    modelId: {
      type: String
    },
    url: {
      type: String,
      default: '/list'
    },
    columns: { type: Array, default: () => [] },
    actions: [Function],
    buttons: { type: Array, default: () => [] },
    requestOtherParam: {
      type: Object,
      default: () => {
        return {}
      }
    },
    addAuth: [String],
    minusAuth: [String],
    //是否显示序号
    showIndex: {
      type: Boolean,
      default: true
    },
    //是否主子明细 如果是主子明细则默认加查询条件cid
    isCorrelation: {
      type: Boolean,
      default: true
    }
  },
  inject: ['tempData'],
  data () {
    return {
      localVal: { list: [] },
      innerChange: false,
      app_code: '',
      page_id: ''
    }
  },
  mounted () {
    let app_code, page_id;
    let hash = window.location.hash;
    if (hash && hash.length > 0) {
      hash = window.location.hash.split('?')[0];
      let hashList = hash.split('/');
      if (hashList && hashList.length >= 3) {
        page_id = hashList[hashList.length - 1];
        app_code = hashList[hashList.length - 2];
        this.app_code = app_code;
        this.page_id = page_id;
      }
    }
    const { modelId, url } = this;
    const { cid } = this.tempData || {};
    if (modelId && cid) {
      let filter = [{ key: 'cid', value: cid }];
      if (!this.isCorrelation) {
        filter = [];
      }
      let params = [];
      let otherParam = {};
      let component_id = this.requestOtherParam?.component_id;
      let isJoinSearch = false;//是否关联查询
      if (this.requestOtherParam) {
        const { sort_key, sort_mode } = this.requestOtherParam;
        const filterParams = this.requestOtherParam.params;
        otherParam = { sort_key: sort_key, sort_mode: sort_mode };
        if (filterParams && filterParams.length > 0) {
          filterParams.forEach(p => {
            if (p.key !== 'cid' || !this.isCorrelation) {
              params.push(p);
            }
            if (p.conditions && p.conditions.length > 0) {
              let fIndex = p.conditions.findIndex(cond => !!cond.tableName);
              if (fIndex >= 0) {
                isJoinSearch = true;
              }
            }
          })
        }
      }
      //isJoin -需不需要关联查询,backJoinFields-是否返回关联表的字段
      window.$http.post(url, { page_id, model_id: modelId, isJoin: true, backJoinFields: false, component_id, filter, params, ...otherParam })
        .then(res => {
          if (res && res.list && res.list.length > 0) {
            this.localVal.list.push(...res.list);
          }
        })
        .finally(() => {
          this.$emit('init');
        })
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        this.innerChange = true;
        this.localVal = newVal || { list: [] };
        this.innerChange = false;
      },
      immediate: true
    },
    localVal: {
      handler (newVal) {
        if (!this.innerChange) {
          if (newVal && this.modelId) {
            newVal.model_id = this.modelId;
          }
          this.$emit('change', newVal);
          this.$emit('update:modelValue', newVal);
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    columnsComputed () {
      const bizName = this.$route.query.bizName;
      const { app_code, page_id, showIndex } = this;
      let realColumns = showIndex ? [{ label: '序号', prop: '', type: 'index', width: '60px', align: 'center', fixed: 'left' }] : [];
      const { columns, actions, disabled } = this;
      if (columns && columns.length > 0) {
        columns.forEach(col => {
          const { component, comProps, ...other } = col;
          let newCol = {
            ...other,
            slots: { header: other.prop }
          }
          if (component) {
            const Com = resolveComponent(component);
            newCol.render = function ({ row, index }) {
              let props = {};
              let hasChange = false;
              if (typeof (comProps) === 'function') {
                hasChange = (comProps + '').indexOf('context.params.row.') >= 0
                const orginProps = comProps(row, index);
                const { listener, modelValue, ...otherProps } = orginProps || {};
                if (listener) {
                  for (let key in listener) {
                    let evtKey = key;
                    if (key && key.length > 0 && !key.startsWith('on')) {
                      evtKey = `on${key.charAt(0).toUpperCase() + key.slice(1)}`;
                    }
                    otherProps[evtKey] = listener[key];
                  }
                }
                props = otherProps;
                if (modelValue !== null && modelValue !== undefined) {
                  row[col.prop] = modelValue;
                }
              } else {
                props = comProps || {};
              }
              let realRequestParams = {};
              let attrs = {
                modelValue: row[col.prop],
                disabled,
                ...props
              };
              if (['SfcSelect'].indexOf(component) >= 0) {
                let model_id = props.model_id ?? col.model_id;
                let url = props && props.requestParams && props.requestParams.url ? props.requestParams.url : props.url;
                if (model_id) {
                  realRequestParams = props.auto && props.requestParams ? props.requestParams : {
                    model_id
                  };
                  realRequestParams.asUrl = props.requestParams && props.requestParams.asUrl ? props.requestParams.asUrl : `/as/${app_code}/model/dicListV2`;
                  realRequestParams.page_id = page_id;
                  realRequestParams.labelKey = props.requestParams ? props.requestParams.labelKey : '';
                  realRequestParams.valueKey = props.requestParams ? props.requestParams.valueKey : '';
                  let realParams = [];
                  attrs.auto = false;
                  let propName = '';
                  if (!props.params) {
                    const { model_id, auto, ...otherParams } = props;
                    let params = [];
                    if (otherParams) {
                      for (let key in otherParams) {
                        params.push({ key, value: otherParams[key] });
                      }
                    }
                    props.params = params;
                  }
                  if (props.params && props.params.length > 0) {
                    props.params.forEach(p => {
                      if (p.value && typeof (p.value) === 'string' && p.value.indexOf('context.params.row.') >= 0) {
                        let paramList = p.value.split('context.params.row.');
                        hasChange = true;
                        propName = paramList.length >= 1 ? paramList[1] : '';
                        realParams.push({ key: p.key, value: row[propName] });
                      } else {
                        realParams.push({ key: p.key, value: p.value });
                      }
                    })
                    if (hasChange) {
                      attrs.changeField = JSON.stringify(realParams);
                    }
                    realRequestParams.params = realParams;
                  } else if (realRequestParams.params && realRequestParams.params.length > 0) {
                    attrs.changeField = JSON.stringify(realRequestParams.params);
                  }
                } else {
                  realRequestParams = props.requestParams;
                  attrs.auto = false;
                }
                attrs.requestParams = realRequestParams;
              }

              if (['ElDatePicker'].indexOf(component) >= 0) {
                attrs['onUpdate:modelValue'] = e => {
                  row[col.prop] = e;
                }
                if (!attrs.format) {
                  attrs.format = 'YYYY-MM-DD';
                }
                if (!attrs['value-format'] && !attrs['valueFormat']) {
                  attrs['value-format'] = 'YYYY-MM-DD';
                }
                if (!attrs.style) {
                  if (attrs.type === 'datetime') {
                    attrs.style = {
                      width: '200px'
                    }
                  } else if (attrs.type === 'daterange') {
                    attrs.style = {
                      width: '100%'
                    }
                  } else {
                    attrs.style = {
                      width: '150px'
                    }
                  }
                } else if (!attrs.style.width) {
                  attrs.style.width = '150px'
                }
              } else {
                attrs['onUpdate:modelValue'] = e => {
                  row[col.prop] = e;
                }
              }
              let realComponent = h(Com, attrs);
              if (disabled && attrs.disabled && ['ElInput', 'ElInputNumber', 'ElDatePicker'].indexOf(component) >= 0) {
                realComponent = h('span', row[col.prop]);
              }
              return realComponent;
            }
          }
          if (disabled && !['审核'].includes(bizName) && ['ElInput', 'ElInputNumber', 'ElDatePicker'].indexOf(component) >= 0) {
            newCol.render = undefined;
          }
          realColumns.push(newCol);
        })
      }
      if (!disabled && actions && typeof (actions) === 'function' && actions({ row: {}, index: -1 }) && actions({ row: {}, index: -1 }).length > 0) {
        realColumns.push({
          label: '操作',
          prop: 'action',
          align: 'center',
          fixed: 'right',
          render: ({ row, index }) => {
            let realActions = [];
            let actionBtns = [];
            if (actions) {
              actionBtns = actions({ row, index });
            }
            if (actionBtns && actionBtns.length > 0) {
              actionBtns.forEach(btn => {
                let actionAttrs = {};
                if (typeof (btn.attrs) === 'string') {
                  try {
                    actionAttrs = new Function(`return ${btn.attrs.replaceAll('@', '')}`)();
                  } catch (ex) { console.log(ex); }
                } else {
                  actionAttrs = btn.attrs;
                }
                if (btn.showIcon === 'noIcon') {
                  delete btn.icon;
                  delete btn.iconPosition;
                }
                if (btn.showIcon === 'onlyIcon') {
                  delete btn.label;
                }
                const button = h(resolveComponent('sfc-button'), {
                  type: btn.type,
                  componentType: btn.componentType,
                  icon: btn.icon,
                  iconPosition: btn.iconPosition,
                  label: btn.label,
                  size: btn.size,
                  ...actionAttrs
                });
                let action;
                if (btn.hasAuth && btn.permission && btn.permission.code) {
                  const authDirective = resolveDirective('auth');
                  action = withDirectives(
                    button,
                    [[authDirective, btn.permission.code]]
                  );
                } else {
                  action = button;
                }
                if (btn.show !== false) {
                  realActions.push(action);
                }
              })
            }
            return h('div', {}, realActions);
          }
        })
      }
      return realColumns;
    },
    leftButtonList () {
      const { buttons } = this;
      let realButtons = this.getButtons(buttons, 'left');
      return this.disabled ? [] : realButtons;
    },
    rightButtonList () {
      const { buttons } = this;
      let realButtons = this.getButtons(buttons, 'right');
      return this.disabled ? [] : realButtons;
    }
  },
  methods: {
    getButtons (buttons, position) {
      let realButtons = [];
      if (buttons && buttons.length > 0) {
        buttons.filter(btn => {
          if (position === 'right') {
            return btn.position === 'right'
          } else {
            return btn.position !== 'right'
          }
        }).forEach(btn => {
          let attrs = {};
          if (typeof (btn.attrs) === 'string') {
            try {
              attrs = new Function(`return ${btn.attrs.replaceAll('@', '')}`)();
            } catch (ex) { console.log(ex); }
          } else {
            attrs = btn.attrs;
          }
          if (btn.showIcon === 'noIcon') {
            delete btn.icon;
            delete btn.iconPosition;
          }
          if (btn.showIcon === 'onlyIcon') {
            delete btn.label;
          }
          const button = h(resolveComponent('sfc-button'), {
            type: btn.type,
            componentType: btn.componentType,
            icon: btn.icon,
            iconPosition: btn.iconPosition,
            label: btn.label,
            size: btn.size,
            ...attrs
          });
          let action;
          if (btn.hasAuth && btn.permission && btn.permission.code) {
            const authDirective = resolveDirective('auth');
            action = withDirectives(
              button,
              [[authDirective, btn.permission.code]]
            );
          } else {
            action = button;
          }
          if (btn.show !== false) {
            realButtons.push({

              component: action
            })
          }
        })
      }
      return this.disabled ? [] : realButtons;
    },
    validate () {
      let pass = true;
      let message = '';
      const { localVal, label, columns } = this;
      if (columns && columns.length > 0 && localVal && localVal.list && localVal.list.length > 0) {
        for (let i = 0; i < localVal.list.length; i++) {
          let row = localVal.list[i];
          let oprateName = '';
          let colName = '';
          let validationHint = '';
          for (let j = 0; j < columns.length; j++) {
            let col = columns[j];
            if (col && col.hidden) {
              continue;
            }
            if (col.required) {
              if (col.validator) {
                let requireIndex = col.validator.findIndex(v => v === '^\\S{1}[\\s\\S]*$');
                if (requireIndex < 0) {
                  col.validator.push('^\\S{1}[\\s\\S]*$');
                }
              } else {
                col.validator = ['^\\S{1}[\\s\\S]*$'];
              }
            }
            if (col.validator && col.validator.length > 0) {
              const val = row[col.prop] !== null && row[col.prop] != undefined ? row[col.prop] + '' : '';
              let fIndex = col.validator.findIndex(v => v === '^\\S{1}[\\s\\S]*$');//先找是否有必填
              for (let k = 0; k < col.validator.length; k++) {
                let vtor = col.validator[k];
                if (vtor && vtor.startsWith('/') && vtor.endsWith('/')) {//如果本来就是正则表达式则去除/
                  vtor = vtor.substring(1, vtor.length - 1);
                }
                const regex = new RegExp(vtor);
                pass = regex.test(val);
                if (fIndex < 0 && val === '') {//没有必填且空值
                  pass = true;
                }
              }
            }
            if (!pass) {
              validationHint = col.validationHint ? `${col.validationHint},` : '';
              oprateName = row[col.prop] !== null && row[col.prop] != undefined && row[col.prop] !== '' ? '更正' : '填写';
              colName = col.label ? `${col.label}` : `第${j + 1}列`;
              break;
            }
          }
          if (!pass) {
            message = `${validationHint}请${oprateName}${label ?? ''}第${i + 1}行,${colName}数据!`;
            ElNotification({
              title: '提示',
              message: `${message}`,
              type: 'error'
            });
            break;
          }
        }
      }
      return pass;
    },
    push (item = null) {
      if (item) {
        const { modelId } = this;
        this.localVal.list.push({ id: modelId ? '' : window.$utils.guid(), ...item });
      } else {
        const { modelId } = this;
        this.localVal.list.push({ id: modelId ? '' : window.$utils.guid() });
      }
    },
    delete (param = {}) {
      let delIndex = 0;
      if (param) {
        delIndex = param.index;
      }
      this.localVal.list.splice(delIndex, 1);
    },
    pushList (param = {}) {
      let itemList = [];
      if (param) {
        itemList = param.list;
      }
      this.localVal.list.push(...(itemList || []));
    }
  },
}
</script>
<style lang="scss" scoped>
.sfc-curd {
  width: 100%;
}
:deep(.funi-curd) {
  padding: 0px !important;
}
:deep(.funi-curd__header) {
  padding-left: 0px !important;
}

:deep(.el-input__wrapper .el-input__suffix) {
  display: inline !important;
  position: absolute !important;
  right: 12px;
  top: 3px;
}
</style>