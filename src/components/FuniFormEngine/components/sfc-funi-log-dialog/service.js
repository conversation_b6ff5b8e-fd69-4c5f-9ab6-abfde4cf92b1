import { createVNode, h, defineComponent, render } from 'vue';
import SfcFuniLogInnerDialog from './sfc-funi-log-inner-dialog.vue';

export function createShowLog(app) {
  return function (id) {
    const div = document.createElement('div');
    document.body.appendChild(div);
    let uploadInstance = createVNode(SfcFuniLogInnerDialog, { id });
    uploadInstance.appContext = app._context;
    render(uploadInstance, div);
  };
}
