<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-03-06 17:34:21
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-12 14:59:19
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\components\sfc-funi-log-dialog\sfc-funi-log-inner-dialog.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-config-provider :locale="locale">
    <el-dialog v-if="dialogVisible"
      v-model="dialogVisible"
      title="操作日志"
      width="1000"
      @close="handleClose">
      <funi-log :operationType="id" />
    </el-dialog>
  </el-config-provider>
</template>
<script setup>
import { reactive, ref } from 'vue';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
const emit = defineEmits('close');
const props = defineProps({
  id: [String]
})
const locale = zhCn;
const dialogVisible = ref(true);

const handleClose = () => {
  emit('close');
  dialogVisible.value = false;
}

const handleOk = () => {
  emit('ok');
  dialogVisible.value = false;
}

</script>
<style lang='scss' scoped>
</style>