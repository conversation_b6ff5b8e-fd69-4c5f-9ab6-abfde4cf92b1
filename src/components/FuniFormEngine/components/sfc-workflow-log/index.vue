<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-10-09 19:54:08
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-31 17:48:06
 * @FilePath: \src\components\FuniFormEngine\components\sfc-workflow-log\index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="">
    <PrintTable :columns="columns"
      :dataList="logList" />
  </div>
</template>
<script setup lang="jsx">
import { ref, watch, inject, nextTick } from 'vue';
import PrintTable from './printTable/index.vue';
const api = '/bpmn/businessManage/getBusinessProcessInstanceInfoByBusinessId';
const tempData = inject('tempData') || {};
let defaultBusinessId = tempData?.businessId;
const props = defineProps({
  businessId: {
    type: String,
    default: ''
  }
});
const logList = ref([]);
const columns = ref([
  {
    title: '操作人',
    dataIndex: 'operatorName',
    width: '20',
    customRender: ({ row, index }) => {
      return row.operator.operatorName;
    }
  },
  {
    title: '节点名称',
    dataIndex: 'activityName',
    width: '20'
  },
  { title: '操作时间', width: '25', dataIndex: 'executeTime' },
  { title: '内容', width: '35', dataIndex: 'content' }
]);

const rowStyle = () => {
  return {};
};

watch(()=>props.businessId, (val) => {
  let reBusinessId=val||defaultBusinessId;
  if (reBusinessId) {
    nextTick(() => {
      getInfo(reBusinessId);
    });
  }
},{immediate:true});

const getInfo = async (reBusinessId) => {
  if (!reBusinessId) {
    return;
  }
  let data = await window.$http.post(api, {
    businessId: reBusinessId
  });
  let list = [];
  data.activityInfos.forEach(item => {
    list.push(...item.businessUserRecordInfos);
  });

  logList.value = list || [];
};
</script>
<style scoped>
:deep(.funi-curd__table .activityName),
:deep(.funi-curd__table .executeTime) {
  width: 30%;
}
:deep(.funi-curd__table .content) {
  width: 40%;
}
</style>
