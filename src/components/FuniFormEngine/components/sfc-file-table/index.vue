<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-12 14:16:09
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-21 15:38:10
 * @FilePath: \src\components\FuniFormEngine\components\sfc-file-table\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="sfc-file-table">
    <funi-file-table ref="funiFileTableRef"
      v-bind="$attrs"
      :onlyShow="disabled"
      :params="params" />
  </div>
</template>
<script setup>
import { reactive, ref, inject } from 'vue';
import { useRoute } from 'vue-router';
const funiFileTableRef = ref();
const tempData = inject('tempData') || {};
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
})
let businessId = tempData?.businessId;

const route = useRoute();
if (!businessId && route && route.query && route.query.businessId) {
  businessId = route.query.businessId;
}

const params = reactive({
  businessId
})
const submit = () => {
  return funiFileTableRef.value.submit();
}
const verification = () => {
  if (props.disabled) {
    return true;
  }
  return funiFileTableRef.value.verification();
}
defineExpose({
  submit,
  verification
})
</script>
<style lang='scss' scoped>
.sfc-file-table {
  width: 100%;
}
</style>
