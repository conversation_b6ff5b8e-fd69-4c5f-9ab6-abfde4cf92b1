<!-- RenderFunctionComponent.vue -->
<script>
import { defineComponent, useAttrs, h } from 'vue'

export default defineComponent({
  props: {
    disabled: [Boolean],
    //绑定的值
    modelValue: [String, Number, Object, Array],
    // 定义函数类型的 prop 接收渲染逻辑
    renderer: [Function]
  },

  setup (props) {
    const attrs = useAttrs();
    const createElement = h;
    return props.renderer ? () => props.renderer(createElement, { ...(props || {}), ...(attrs || {}) }) : () => {
      return createElement('span', '自定义组件');
    }
  }
})
</script>