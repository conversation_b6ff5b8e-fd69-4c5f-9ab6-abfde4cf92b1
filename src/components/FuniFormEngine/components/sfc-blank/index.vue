<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-06-29 14:39:05
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-23 14:36:52
 * @FilePath: \src\components\FuniFormEngine\components\sfc-blank\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<script>
import lowcode from '../../common/utils/lowcode';
import { h, unref, defineComponent, provide, reactive } from 'vue';
import { ElNotification, ElMessageBox } from 'element-plus';
import { generateComponent } from '../../common/utils/vue3SfcCompiler';
import { useMultiTab } from '@/utils/hooks/useMultiTab.js';
export default defineComponent({
  props: {
    modelValue: { type: Object },
    code: { type: String },
    modelId: { type: String },
    mainModelId: { type: String },
    urls: {
      type: Object,
      default: () => {
        return new Object();
      }
    }
  },
  inject: ['loadingStatus'],
  data () {
    return {
      isInit: false,
      localValue: null,
      tempData: {},
    };
  },
  setup (props) {
    const tempData = reactive({});
    const updateTempData = (newObj = {}) => {
      Object.assign(tempData, newObj);
    };
    provide('modelId', props.modelId);
    provide('tempData', tempData);
    provide('updateTempData', updateTempData);
    const multiTab = useMultiTab();
    const closeCurrentPage = () => {
      multiTab.closeCurrentPage();
    };
    return { closeCurrentPage, tempData, updateTempData };
  },
  mounted () {
    const { modelValue } = this;
    if (modelValue && this.$refs.funiFormEngineRef) {
      this.$refs.funiFormEngineRef.setFormData({ ...(modelValue || {}) });
    }
    let id = this.$route.query.id;
    if (id) {
      this.updateTempData({ id });
    }
    let cid = this.$route.query.cid;
    if (cid) {
      this.updateTempData({ cid });
    }
    this.init();
  },
  watch: {
    modelValue: {
      handler (newVal) {
        if (this.$refs.funiFormEngineRef) {
          this.$nextTick(() => {
            this.$refs.funiFormEngineRef.setFormData({ ...(newVal || {}) });
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    init () {
      const { cid, id } = this.tempData;
      const { modelId, urls } = this;
      if (modelId && (cid || id)) {
        let param = { cid, id, model_id: modelId, params: [] };
        if (this.loadingStatus) {
          unref(this.loadingStatus).status = true;
        }
        window.$http
          .post(urls.detailById, param)
          .then(res => {
            if (res && res.id) {
              this.updateTempData({ id: res.id, cid: res.cid });
            }
            this.isInit = true;
            this.$nextTick(() => {
              if (this.$refs.funiFormEngineRef) {
                let newVal = res || {};
                this.$refs.funiFormEngineRef.setFormData({ ...newVal });
              }
            });
          })
          .finally(() => {
            this.isInit = true;
            if (this.loadingStatus) {
              unref(this.loadingStatus).status = false;
            }
          });
      } else {
        this.isInit = true;
      }
    },
    ts () {
      return new Promise((resovle, reject) => {
        this.store()
          .then(() => {
            ElNotification({
              title: '提示',
              message: `保存成功!`,
              type: 'success'
            });
            resovle();
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    submit (param) {
      return new Promise((resovle, reject) => {
        this.store(true)
          .then(() => {
            ElMessageBox.alert('<span style="font-size:larger">提交成功!</span>', '提示', {
              type: 'success',
              center: false,
              dangerouslyUseHTMLString: true,
              callback: () => {
                this.closeCurrentPage();
              }
            });
            resovle();
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    store (submit = false) {
      return new Promise((resolve, reject) => {
        this.$refs.funiFormEngineRef.submitForm().then(res => {
          if (res.valid) {
            const { id } = this.tempData;
            const { modelId, mainModelId, urls } = this;
            let page_id, app_code;
            if (window.location && window.location.hash) {
              let hashAndQuery = window.location.hash.split('?');
              if (hashAndQuery && hashAndQuery.length > 0) {
                let [hash] = hashAndQuery;
                if (hash && hash.length > 0) {
                  let hashList = hash.split('/');
                  if (hashList && hashList.length >= 3) {
                    page_id = hashList[hashList.length - 1];
                    app_code = hashList[hashList.length - 2];
                  }
                }
              }
            }
            const param = { submit, app_code, page_id };
            if (id) {
              param.id = id;
            }
            if (modelId) {
              param.model_id = modelId;
            }
            if (modelId && modelId === mainModelId) {
              let orginData = {};
              if (this.tempData && this.tempData.mainInfo && this.tempData.mainInfo.data) {
                orginData = this.tempData.mainInfo.data;
              }
              this.updateTempData({ mainInfo: { model_id: mainModelId, data: { ...orginData, ...(res.data || {}) } } });
            }
            let mainInfo;
            if (submit) {
              if (this.tempData && this.tempData.mainInfo) {
                mainInfo = this.tempData.mainInfo;
              }
            }
            window.$http
              .post(urls.save, { ...param, data: res.data, mainInfo })
              .then(res1 => {
                let newId = res1.id;
                if (newId) {
                  this.updateTempData({ id: newId });
                }
                resolve();
              })
              .catch(err => {
                reject(err);
              });
          } else {
            let messages = [];
            if (res && res.errors) {
              for (let key in res.errors) {
                if (res.errors[key] && res.errors[key].length > 0) {
                  messages.push(...(res.errors[key].map(err => err.message) || []));
                }
              }
            }
            if (messages && messages.length > 0) {
              messages = [...new Set([...messages])];
            }
            ElNotification({
              title: '提示',
              message: `${messages.join(',')}`,
              type: 'error'
            });
            reject('请更正数据!');
          }
        });
      });
    }
  },
  render (ctx) {
    if (ctx.code && ctx.isInit) {
      let jsCode = lowcode.decrypt(ctx.code);
      let component = generateComponent(jsCode);
      return h(component, { ref: 'funiFormEngineRef', ...ctx.attrs, onSubmit: this.ts });
    } else {
      return h('span', '');
    }
  }
});
</script>