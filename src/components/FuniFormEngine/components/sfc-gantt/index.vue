<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-10-19 17:58:54
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-06-19 18:56:16
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\components\sfc-gantt\index.vue
 * @Description: 甘特图
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="sfc-gantt">
    <funi-gantt ref="gant"
      v-bind="$attrs"
      :value="state.tasks"
      :style="{height:height?`${height}px`:'100%'}" />
  </div>
</template>
<script setup>

import httpUtil from '@/utils/httpUtil';
import { getCurrentInstance, onMounted, reactive, ref, toRefs, watch } from 'vue';

const props = defineProps({
  url: {
    type: String
  },
  height: {
    type: Number
  },
  value: {
    type: Array,
    default: () => []
  },
  requestOtherParam: {
    type: Object,
    default: () => {
      return {}
    }
  },
  converter: [Function]
})

const gant = ref();

const instance = getCurrentInstance();

const state = reactive({
  key: 0,
  tasks: []
})

onMounted(() => {
  refresh();
})

watch(() => props.height, () => {
  state.key++;
})

const initGantt = () => {
  gant.value.initGantt();
}

const refresh = (params = null) => {
  if (props.url) {
    let url = props.url;
    let params = { ...(props.requestOtherParam || {}) };
    const loading = instance.proxy.$loading({ fullscreen: true });
    window.$http.post(url, params)
      .then(res => {
        if (props.converter) {
          state.tasks = props.converter(res.list);
        } else {
          state.tasks = res.list;
        }
        loading.close();
      })
      .catch(() => {
        loading.close();
      })
  }
}

defineExpose({ initGantt, refresh })
</script>
<style lang='scss' scoped>
.sfc-gantt {
  height: 100%;
  width: 100%;
}
</style>
