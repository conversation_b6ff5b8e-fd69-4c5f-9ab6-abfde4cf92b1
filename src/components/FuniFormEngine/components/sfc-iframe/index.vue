<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-03-05 17:26:01
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-03-06 11:38:51
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\components\sfc-iframe\index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="index"
    style="width:100%">
    <div v-if="isDesign"
      style="width:100%;border:1px dotted #409eff;"
      :style="{height:computedHeight}"></div>
    <iframe v-else
      :id="frameId"
      :src="src"
      style="width:100%;"
      :style="{height:computedHeight}"
      frameborder="0"
      scrolling="auto"
      @click="handleClick" />
  </div>
</template>

<script>
import { computed } from 'vue'
export default {
  name: 'index',
  components: {
  },
  props: {
    src: [String],
    height: {
      type: [String],
      default: '500px'
    },
    isFull: {
      type: [Boolean],
      default: false
    },
    isDesign: {
      type: [Boolean],
      default: false
    }
  },
  setup (props) {
    const computedHeight = computed(() => {
      let height = props.height;
      if (props.height && !Number.isNaN(Number(height))) {
        height = height + 'px';
      }
      if (props.isFull) {
        height = 'calc(100vh - 210px)';
      }
      return height;
    })
    return {
      computedHeight
    }
  },
  data () {
    return {
      frameId: window.$utils.guid()
    }
  },
  mounted () {
  },
  methods: {
  },
}
</script>