<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-12-19 17:58:41
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-11-11 11:12:47
 * @FilePath: \src\components\FuniFormEngine\components\sfc-region\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <funi-region v-bind="$attrs"
      :lvl="lvl"
      v-model="localVal"
      :regionProps={province:{disabled:!isEdit},city:{disabled:!isEdit},district:{disabled:!isEdit},community:{disabled:!isEdit},street:{disabled:!isEdit}} />
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { computed, reactive, ref } from 'vue';
const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  modelValue: [String],
  lvl: {
    type: Number,
    default: 5
  },
  isEdit: {
    type: Boolean,
    default: true
  }
})

const localVal = computed(
  {
    get () {
      let regionStr = props.modelValue;
      let region = undefined;
      if (regionStr) {
        try {
          region = JSON.parse(regionStr);
        } catch { }
      }
      return region;
    },
    set (newVal) {
      let regionStr = '';
      if (newVal) {
        regionStr = JSON.stringify(newVal);
      }
      emit('update:modelValue', regionStr);
    }
  }
)
</script>
<style lang='scss' scoped>
</style>