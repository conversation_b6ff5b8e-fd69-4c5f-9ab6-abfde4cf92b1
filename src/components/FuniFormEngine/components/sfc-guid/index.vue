<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-27 14:12:16
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-11-01 14:41:59
 * @FilePath: \src\components\FuniFormEngine\components\sfc-guid\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="sfc-guid">
    <funi-label v-model="localVal" />
  </div>
</template>
<script setup>
import { computed, onMounted } from 'vue';
import SnowflakeId from 'snowflake-id';
const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  url: {
    type: String,
    default: '/as/common/getShortId'
  },
  isLocal: {
    type: Boolean,
    default: true
  },
  modelValue: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

onMounted(() => {
  if (!props.disabled) {
    if (props.isLocal) {
      const snowflake = new SnowflakeId();
      localVal.value = snowflake.generate();
    } else if (props.url) {
      window.$http.post(props.url)
        .then(res => {
          localVal.value = res;
        })
    }
  }
})

const localVal = computed({
  get: () => {
    return props.modelValue;
  },
  set: (newVal) => {
    emit('update:modelValue', newVal);
  }
})
</script>
<style lang='scss' scoped>
.sfc-guid {
  width: 100%;
  color: var(--el-text-color-regular);
}
</style>