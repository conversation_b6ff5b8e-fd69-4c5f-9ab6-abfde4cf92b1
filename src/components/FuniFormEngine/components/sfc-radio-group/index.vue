<template>
  <el-radio-group v-bind="$attrs"
    v-model="localVal"
    :style="displayStyle">
    <component :is="childTag"
      v-for="(item,index) in localOptions"
      :key="index"
      :border="childBorder"
      :label="item.value"
      :style="displayStyle">{{ item.label }}</component>
  </el-radio-group>
</template>
<script setup>
import { computed, onMounted, watch, shallowRef } from 'vue';

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean, Array, Object],
    default: ''
  },
  /**
   * 请求地址
   */
  url: {
    type: String,
    default: '/csops/dic/findDicByType'
  },
  /**
   * 字典码
   */
  typeCode: {
    type: String,
    default: ''
  },
  isLocal: {
    type: Boolean,
    default: false
  },
  options: {
    type: Array,
    default: () => {
      return new Array();
    }
  },
  childTag: {
    type: String,
    default: 'el-radio'
  },
  childBorder: {
    type: Boolean,
    default: false
  },
  displayStyle: {
    type: Object,
    default: () => {
      return new Object();
    }
  }
})

const localOptions = shallowRef([
  ...(props.options || [])
]);

onMounted(() => {
  if (props.typeCode && props.url) {
    requestOptions();
  }
})

watch(() => props.typeCode, (newVal) => {
  if (newVal && props.url) {
    requestOptions();
  } else {
    localOptions.value = [...(props.options || [])]
  }
})

const requestOptions = () => {
  const { typeCode, isLocal } = props;
  if (isLocal && typeCode) {
    const enumSourceStr = localStorage.getItem('enumSource');
    if (enumSourceStr) {
      let enumSource = [];
      try {
        enumSource = JSON.parse(enumSourceStr);
      } catch { }
      if (enumSource && enumSource.length >= 0) {
        let fIndex = enumSource.findIndex(item => item.code === typeCode);
        if (fIndex >= 0 && enumSource[fIndex].dicResponses && enumSource[fIndex].dicResponses.length > 0) {
          localOptions.value = enumSource[fIndex].dicResponses.map(item => {
            return {
              label: item.name ?? item.label,
              value: item.code ?? item.value
            }
          })
        }
      }
    }
  } else {
    window.$http.post(props.url, { typeCode: props.typeCode })
      .then(res => {
        if (res && res.list && res.list.length > 0) {
          localOptions.value = res.list.map(item => {
            return {
              label: item.name,
              value: item.code
            }
          })
        }
      })
  }

}

const localVal = computed({
  get: () => {
    return props.modelValue;
  },
  set: (newVal) => {
    emit('update:modelValue', newVal);
  }
})

</script>
<style lang='scss' scoped>
</style>