<template>
  <div class="sfc-show-curd"
    v-loading="loading">
    <funi-curd ref="funiCurdRef"
      v-bind="$attrs"
      :checkLimit="checkLimit"
      :columns="columnsComputed"
      :searchConfig="searchConfigComputed"
      :lodaData="loadData"
      :spanMethod="spanMethod"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange">
      <template #header>
        <div style="height:100%;display:flex;align-items:center;">
          <funi-group-title :title="label"
            groupMargin="0px"
            style="line-height: 22px;" />
        </div>
      </template>
      <template #buttonGroup>
        <component v-for="(item, index) in leftButtonList"
          :key="index"
          :is="item.component"
          v-bind="item.props || {}"
          v-on="item.on || {}"
          :style="item.style || {}" />
      </template>
      <template #extendButtonGroup>
        <component v-for="(item, index) in rightButtonList"
          :key="index"
          :is="item.component"
          v-bind="item.props || {}"
          v-on="item.on || {}"
          :style="item.style || {}"
          v-auth="addAuth" />
      </template>
    </funi-curd>
  </div>
</template>

<script>
import { h, resolveComponent, resolveDirective, withDirectives } from 'vue';
import { deepClone } from '../../common/utils/util';
export default {
  name: 'index',
  components: {
  },
  props: {
    modelValue: {
      type: [Object, Array],
      default: () => {
        return {}
      }
    },
    label: {
      type: String
    },
    disabled: {
      type: Boolean,
      default: false
    },
    filters: {
      type: Array,
      default: () => {
        return new Array();
      }
    },
    columns: { type: Array, default: () => [] },
    actions: [Function],
    buttons: { type: Array, default: () => [] },
    //搜索配置
    searchConfigGroup: { type: Array, default: () => ([]) },
    url: {
      type: String
    },
    requestOtherParam: {
      type: Object,
      default: () => {
        return {}
      }
    },
    modelId: {
      type: String
    },
    //是否直接输出数组
    isArrayVal: {
      type: Boolean,
      default: false
    },
    //是否多对多关系
    isManyToMany: {
      type: Boolean,
      default: false
    },
    //是否设计模式
    isDesign: {
      type: Boolean,
      default: false
    },
    //是否主子明细 如果是主子明细则默认加查询条件cid
    isCorrelation: {
      type: Boolean,
      default: true
    },
    //第三张表主键在第二张表中的关联字段名
    mFieldName: [String],
    checkLimit: [Number]
  },
  inject: ['tempData'],
  data () {
    const { isArrayVal } = this;
    return {
      localVal: isArrayVal ? [] : {},
      currentPage: {},
      currentRequestParams: undefined,
      loading: false,
      isLoadLocal: false, //是否加载本地数据
      innerChange: false,//是否内部触发的值改变
      conditionConfigs: {},//配置的搜索筛选条件
    }
  },
  mounted () {
  },
  watch: {
    modelValue: {
      handler (newVal) {
        if (this.innerChange)
          return;
        const { isArrayVal } = this;
        this.innerChange = true;
        this.localVal = newVal || (isArrayVal ? [] : {});
        this.$nextTick(() => {
          this.innerChange = false;
        })
        if (!isArrayVal) {
          if ((newVal && newVal.requestOtherParam) || (newVal && newVal.list && newVal.list.length > 0)) {
            if (newVal && newVal.list && newVal.list.length > 0) {
              this.isLoadLocal = true;
            }
            this.$nextTick(() => {
              this.$refs.funiCurdRef.reload();
              let selectedRowKeys = this.localVal.selectedRowKeys || [];
              let selectRows = this.$refs.funiCurdRef.tableData.filter(row => selectedRowKeys.indexOf(row.id) >= 0);
              selectRows.forEach(row => {
                this.$refs.funiCurdRef.toggleSelection(row);
              })
            })
          }
        } else if (newVal && newVal.length >= 0) {
          this.isLoadLocal = true;
          this.$nextTick(() => {
            this.$refs.funiCurdRef.reload();
          })
        }
      }
    },
    localVal: {
      handler (newVal) {
        if (this.innerChange)
          return;
        const { isArrayVal, modelId, isManyToMany, mFieldName } = this;
        if (!isArrayVal && newVal && modelId) {
          newVal.model_id = modelId;
          if (isManyToMany && newVal && newVal.list && newVal.list.length > 0) {
            newVal.list.forEach(item => {
              item[mFieldName] = item.id;
              delete item.id;
              delete item.cid;
            })
          }
        }
        this.innerChange = true;
        this.$emit('update:modelValue', newVal);
        this.$nextTick(() => {
          this.innerChange = false;
        })
      },
      immediate: true
    }
  },
  computed: {
    columnsComputed () {
      let realColumns = [];
      const { columns, actions, disabled } = this;
      if (columns && columns.length > 0) {
        columns.forEach(col => {
          const { render, ...other } = col;
          let realRender;
          if (render && typeof (render) === 'string') {
            try {
              let proxy = new Function(`return {realRender:function(resolveComponent,h){const render=${render.replaceAll('@', '')}; return render;}}`)();
              realRender = proxy.realRender(resolveComponent, h);
            } catch (ex) { console.log(ex); }
          } else {
            realRender = render;
          }
          let newCol = {
            ...other,
            render: realRender
          }
          realColumns.push(newCol);
        })
      }
      if (!disabled && actions && typeof (actions) === 'function' && actions({ row: {}, index: -1 }) && actions({ row: {}, index: -1 }).length > 0) {
        realColumns.push({
          label: '操作',
          prop: 'action',
          align: 'center',
          fixed: 'right',
          showOverflowTooltip: false,
          render: ({ row, index }) => {
            let realActions = [];
            let actionBtns = [];
            if (actions) {
              actionBtns = actions({ row, index });
            }
            if (actionBtns && actionBtns.length > 0) {
              actionBtns.forEach(btn => {
                let actionAttrs = {};
                if (typeof (btn.attrs) === 'string') {
                  try {
                    actionAttrs = new Function(`return ${btn.attrs.replaceAll('@', '')}`)();
                  } catch (ex) { console.log(ex); }
                } else {
                  actionAttrs = btn.attrs;
                }
                if (btn.showIcon === 'noIcon') {
                  delete btn.icon;
                  delete btn.iconPosition;
                }
                if (btn.showIcon === 'onlyIcon') {
                  delete btn.label;
                }
                const button = h(resolveComponent('sfc-button'), {
                  type: btn.type,
                  componentType: btn.componentType,
                  icon: btn.icon,
                  iconPosition: btn.iconPosition,
                  label: btn.label,
                  size: btn.size,
                  ...actionAttrs
                });
                let action;
                if (btn.hasAuth && btn.permission && btn.permission.code) {
                  const authDirective = resolveDirective('auth');
                  action = withDirectives(
                    button,
                    [[authDirective, btn.permission.code]]
                  );
                } else {
                  action = button;
                }
                if (btn.show !== false) {
                  realActions.push(action);
                }
              })
            }
            return h('div', {}, realActions);
          }
        })
      }
      return realColumns;
    },
    leftButtonList () {
      const { buttons } = this;
      let realActions = this.getButtons(buttons, 'left');
      return this.disabled ? [] : realActions;
    },
    rightButtonList () {
      const { buttons } = this;
      let realActions = this.getButtons(buttons, 'right');
      return this.disabled ? [] : realActions;
    },
    searchConfigComputed () {
      let groups = [];
      const { searchConfigGroup } = this;
      if (searchConfigGroup && searchConfigGroup.length > 0) {
        searchConfigGroup.forEach(searchConfig => {
          let realSearchConfig = { schema: [] };
          if (searchConfig && searchConfig.schema && searchConfig.schema.length > 0) {
            realSearchConfig.schema = searchConfig.schema.map(item => {
              this.conditionConfigs[item.prop] = item;
              let searchSchema = {
                label: item.label,
                prop: item.prop,
                props: item.props
              };
              switch (item.componentType) {
                case 'el-select':
                  searchSchema.component = item.component;
                  searchSchema.props = { clearable: true, filterable: true, typeCode: item.typeCode || item.typeName, isLocal: true };
                  break;
                case 'model':
                case 'APIs':
                  searchSchema.component = 'sfc-select';
                  break;
                default:
                  searchSchema.component = item.component;
                  break;
              }
              return searchSchema;
            })
          }
          groups.push(realSearchConfig);
        })
      }
      return groups && groups.length > 0 ? groups[0] : {};
    }
  },
  methods: {
    getButtons (buttons, position) {
      let realActions = [];
      if (buttons && buttons.length > 0) {
        buttons.filter(btn => {
          if (position === 'right') {
            return btn.position === 'right'
          } else {
            return btn.position !== 'right'
          }
        }).forEach(btn => {
          if (btn.show !== false) {
            let attrs = {};
            if (typeof (btn.attrs) === 'string') {
              try {
                attrs = new Function(`return ${btn.attrs.replaceAll('@', '')}`)();
              } catch (ex) { console.log(ex); }
            } else {
              attrs = btn.attrs;
            }
            if (btn.showIcon === 'noIcon') {
              delete btn.icon;
              delete btn.iconPosition;
            }
            if (btn.showIcon === 'onlyIcon') {
              delete btn.label;
            }
            const button = h(resolveComponent('sfc-button'), {
              type: btn.type,
              componentType: btn.componentType,
              icon: btn.icon,
              iconPosition: btn.iconPosition,
              label: btn.label,
              size: btn.size,
              ...attrs
            }, { default: () => `${btn.content}` });
            let action;
            if (btn.hasAuth && btn.permission && btn.permission.code) {
              const authDirective = resolveDirective('auth');
              action = withDirectives(
                button,
                [[authDirective, btn.permission.code]]
              );
            } else {
              action = button;
            }
            realActions.push({
              component: action
            })
          }
        })
      }
      return this.disabled ? [] : realActions;
    },
    spanMethod ({ row, column, rowIndex, columnIndex }) {
      let joinFields = [];
      if (this.requestOtherParam && this.requestOtherParam.joinFields && this.requestOtherParam.joinFields.length > 0) {
        joinFields = this.requestOtherParam.joinFields;
      }
      let parentProp = column && column.property ? column.property.split('.')[0] : '';
      if (row.rowspan >= 0 && !joinFields.includes(parentProp)) {
        return {
          rowspan: row.rowspan,
          colspan: 1,
        }
      }
    },
    validate () {
      let pass = true;
      let message = '';
      const { localVal, label, columns, isArrayVal } = this;
      if (columns && columns.length > 0 && isArrayVal && localVal && localVal.length > 0) {
        for (let i = 0; i < localVal.length; i++) {
          let row = localVal[i];
          let oprateName = '';
          let colName = '';
          for (let j = 0; j < columns.length; j++) {
            let col = columns[j];
            if (col.validator && col.validator.length > 0) {
              const val = row[col.prop] ? row[col.prop] : '';
              let fIndex = col.validator.findIndex(v => v === '^\\S+$');//先找是否有必填
              for (let k = 0; k < col.validator.length; k++) {
                let vtor = col.validator[k];
                const regex = new RegExp(vtor);
                pass = regex.test(val);
                if (fIndex < 0 && val === '') {//没有必填且空值
                  pass = true;
                }
              }
            }
            if (!pass) {
              oprateName = row[col.prop] ? '更正' : '填写';
              colName = col.label ? `${col.label}` : `第${j + 1}列`;
              break;
            }
          }
          if (!pass) {
            message = `请${oprateName}${label}第${i + 1}行,${colName}数据!`;
            this.$message.error(message);
            break;
          }
        }
      }
      return pass;
    },
    async loadData (page = { pageSize: 10, pageNo: 1 }, params) {
      if (this.isDesign) {
        return { list: [], total: 0 }
      }
      this.loading = true;
      const { cid } = this.tempData || {};
      const { url, modelId, filters, currentPage, localVal, isArrayVal, isManyToMany, isCorrelation, mFieldName, isLoadLocal } = this;
      const localList = isArrayVal ? localVal : localVal.list;
      if (isLoadLocal || isArrayVal) {
        this.loading = false;
        this.isLoadLocal = false;
        return {
          list: [...(localList || [])],
          total: (localList || []).length
        }
      }
      if (!url || (isCorrelation && !cid)) {
        this.loading = false;
        this.isLoadLocal = false;
        return {
          list: [],
          total: 0
        }
      }
      let app_code, page_id;
      let hash = window.location.hash;
      if (hash && hash.length > 0) {
        hash = window.location.hash.split('?')[0];
        let hashList = hash.split('/');
        if (hashList && hashList.length >= 3) {
          page_id = hashList[hashList.length - 1];
          app_code = hashList[hashList.length - 2];
        }
      }
      let customParams = [];
      if (!isArrayVal && this.localVal && this.localVal.requestOtherParam) {
        for (let key in this.localVal.requestOtherParam) {
          customParams.push({ conditions: [{ key, value: this.localVal.requestOtherParam[key] }] });
        }
      }
      let requestOtherParam = { params: customParams };
      if (this.requestOtherParam) {
        let realRequestOtherParam = {};
        if (this.requestOtherParam) {
          realRequestOtherParam = { page_id, ...this.requestOtherParam };
          if (this.requestOtherParam && this.requestOtherParam.data) {
            Object.assign(realRequestOtherParam, this.requestOtherParam.data);
          }
        }
        const cloneParam = deepClone(realRequestOtherParam);
        let cloneOtherParams = {};
        if (cloneParam && cloneParam.params && !Array.isArray(cloneParam.params) && !this.$lowCodeRequest.isAsDataSoureUrl(url)) {
          cloneOtherParams = cloneParam.params;
          Object.assign(requestOtherParam, cloneOtherParams);
          delete cloneParam.params;
        }
        Object.assign(requestOtherParam, cloneParam);
      }
      let realFiltersVal = {};
      for (let key in params) {
        let filter = filters.find(f => f.prop === key);
        if (filter && filter.convert) {
          let realVal = filter.convert(key, params[key]);
          Object.assign(realFiltersVal, realVal);
        } else if (params[key]) {
          realFiltersVal[key] = params[key];
        }
      }
      if (modelId || Object.entries(realFiltersVal).length > 0) {
        let conditions = [];
        for (let key in realFiltersVal) {
          let cond = { key, value: realFiltersVal[key] };
          let config = this.conditionConfigs[key];
          if (config) {
            if (config.operator) {
              cond.operator = config.operator;
            }
            if (config.tableName) {
              cond.tableName = config.tableName;
            }
            conditions.push(cond);
          }
        }
        if (conditions.length > 0) {
          if (!modelId && Object.entries(requestOtherParam.params).length <= 0) {
            requestOtherParam.filter = [];
          }
          requestOtherParam.filter = conditions;
        }
      }
      const { pageSize, pageIndex } = page;
      if (isManyToMany && modelId) {
        let cidParam = { conditions: [{ key: 'cid', operator: 'EQUAL', value: cid }] }
        if (requestOtherParam && requestOtherParam.params) {
          let isFind = false;
          for (let i = 0; i < requestOtherParam.params.length; i++) {
            let p = requestOtherParam.params[i];
            if (p.conditions && p.conditions.length > 0) {
              let fIndex = p.conditions.findIndex(p => p.key === 'cid');
              if (fIndex >= 0) {
                p.conditions[fIndex].value = cid;
                p.conditions[fIndex].operator = 'EQUAL';
                isFind = true;
              }
            }
          }
          if (!isFind && modelId) {
            cidParam.logicalOperator = 'AND';
            requestOtherParam.params.push(cidParam);
          }
        } else {
          requestOtherParam.params = [cidParam];
        }
      }
      let joinFields = [];
      if (requestOtherParam && requestOtherParam.params && requestOtherParam.params.length > 0) {
        requestOtherParam.params.forEach(item => {
          if (item && item.conditions && item.conditions.length > 0) {
            item.conditions.forEach(item1 => {
              if (item1 && item1.tableName && item1.joinFieldName) {
                joinFields.push(item1.joinFieldName);
              }
            });
          }
        });
      }
      //处理关联查询字段
      if (joinFields.length > 0) {
        if (requestOtherParam.joinFields) {
          joinFields.forEach(item => {
            if (requestOtherParam.joinFields.indexOf(item) < 0) {
              requestOtherParam.joinFields.push(item);
            }
          })
        } else {
          requestOtherParam.joinFields = joinFields;
        }
      }
      let param = { pageNo: pageIndex, pageSize, ...requestOtherParam };
      try {
        let result = await this.$lowCodeRequest.proxyOtherRequestAsync(url, param, true);
        this.currentRequestParams = param;
        Object.assign(currentPage, page);
        this.loading = false;
        let list = [];
        if (isManyToMany && result && result.list && result.list.length > 0) {//处理关联关系的字段。多对多的这种
          result.list.forEach(item => {
            let row = { ...(item || {}) };
            if (item) {
              for (let key in item) {
                if (item[key] && typeof (item[key]) === 'object' && !Array.isArray(item[key])) {
                  if (key === mFieldName) {
                    Object.assign(row, item[key])
                  }
                }
              }
            }
            list.push(row);
          })
        } else {
          list = result.list;
        }
        let mergeIds = [];
        if (list.length > 0) {
          for (let i = 0; i < list.length; i++) {
            let rowKey = this.$attrs.rowKey ?? 'id';
            let id = list[i][rowKey];
            let fIndex = mergeIds.findIndex(item => item === id);
            if (fIndex >= 0) {
              list[i].rowspan = 0;
            } else {
              let mergeLength = list.filter(item => item[rowKey] === id).length;
              if (mergeLength > 1) {
                list[i].rowspan = mergeLength;
                mergeIds.push(id);
              }
            }
          }
        }
        if (this.isArrayVal) {
          this.localVal = [...(result.list || [])];
        } else {
          if (!this.localVal) {
            this.localVal = { remoteList: [...(result.list || [])] };
          } else {
            this.localVal.list = [...(result.list || [])];
          }
        }
        this.$emit('change', { param, list, total: result.total });
        return {
          list,
          total: result.total
        };
      } catch {
        this.loading = false;
      }
    },
    doRequest (currentPage) {
      this.$refs.funiCurdRef.doRequest(currentPage);
    },
    reload () {
      this.$refs.funiCurdRef.reload();
    },
    refresh () {
      const { currentPage } = this;
      this.$refs.funiCurdRef.doRequest(currentPage);
    },
    pushList (param = {}) {
      let itemList = [];
      if (param) {
        itemList = param.list;
      }
      const { isArrayVal } = this;
      if (isArrayVal) {
        if (!this.localVal) {
          this.localVal = [];
        }
        this.localVal.push(...(itemList || []));
      } else {
        if (!this.localVal.list) {
          this.localVal.list = [];
        }
        this.localVal.list.push(...(itemList || []));
      }
      console.log('this.localVal', this.localVal);
      this.reload();
    },
    reloadList (param = {}) {
      let itemList = [];
      if (param) {
        itemList = param.list;
      }
      const { isArrayVal } = this;
      if (isArrayVal) {
        this.localVal = [...(itemList || [])];
      } else {
        this.localVal = { list: itemList };
      }
      this.reload();
    },
    delete (param = {}) {
      let delIndex = 0;
      if (param) {
        delIndex = param.index;
      }
      const { isArrayVal } = this;
      if (isArrayVal) {
        this.localVal.splice(delIndex, 1);
      } else {
        this.localVal.list.splice(delIndex, 1);
      }
      this.isLoadLocal = true;
      this.reload();
    },

    // 获取请求参数
    getParams () {
      return { ...(this.currentRequestParams || {}) };
    },
    handleSelectionChange (selectedRows) {
      this.$emit('selection-change', selectedRows);
      const { columns, isArrayVal } = this;
      if (!isArrayVal && columns && columns.length > 0) {
        let fIndex = columns.findIndex(col => col.type === 'selection')
        if (fIndex >= 0) {
          const selectedRowKeys = selectedRows.map(row => row[this.$attrs.rowKey ?? 'id'])
          if (this.localVal) {
            this.localVal.selectedRowKeys = selectedRowKeys;
          } else {
            this.localVal = { selectedRowKeys };
          }
          this.localVal.selectedRows = selectedRows;
        }
      }
    },
    handleCurrentChange (selectedRow) {
      this.$emit('current-change', selectedRow);
      const { columns, isArrayVal } = this;
      if (!isArrayVal && columns && columns.length > 0) {
        let fIndex = columns.findIndex(col => col.type === 'radio')
        if (fIndex >= 0) {
          const selectedRows = [selectedRow];
          const selectedRowKeys = selectedRows.map(row => row[this.$attrs.rowKey ?? 'id'])
          if (this.localVal) {
            this.localVal.selectedRowKeys = selectedRowKeys;
          } else {
            this.localVal = { selectedRowKeys };
          }
          this.localVal.selectedRows = selectedRows;
        }
      }
    }
  },
}
</script>
<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
.sfc-show-curd {
  width: 100%;
  :deep() {
    @include el.b(form-item) {
      background-color: transparent !important;
      border: 0px !important;
      margin-bottom: 14px !important;
      &::before {
        width: 0px !important;
      }
    }
  }
}
:deep(.funi-curd__header) {
  padding-left: 0px !important;
}
:deep(.funi-curd) {
  padding: 0px !important;
}
</style>