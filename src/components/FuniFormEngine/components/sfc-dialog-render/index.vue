<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-06-08 10:40:18
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-09-28 16:15:33
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\components\sfc-dialog-render\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<script>
import { h } from 'vue';
import lowcode from '../../common/utils/lowcode';
export default {
  name: 'sfc-dialog-render',
  components: {
  },
  props: {
    code: {
      type: String,
    },
  },
  data () {
    return {

    }
  },
  mounted () {

  },
  methods: {
    show (data = null, attach = true) {
      this.$refs.childRef.show(data, attach);
    },
    close () {
      this.$refs.childRef.close();
    },
    submitForm () {
      return this.$refs.childRef.submitForm();
    },
    resetForm () {
      this.$refs.childRef.resetForm();
    },
    setFormData (data, attach = true) {
      this.$refs.childRef.setFormData(data, attach);
    },
    setFormDisabled (disabled) {
      this.$refs.childRef.setFormDisabled(disabled);
    }
  },
  render () {
    const { code } = this;
    let jsCode = lowcode.decrypt(code);
    const ChildComponent = lowcode.jsToComponent(jsCode);
    return h(ChildComponent, { ...this.$attrs, ref: 'childRef' });
  }
}
</script>
