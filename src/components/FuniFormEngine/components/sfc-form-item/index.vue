<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-07-31 20:19:34
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-01-21 16:39:58
 * @FilePath: \src\components\FuniFormEngine\components\sfc-form-item\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-form-item class="sfc-form-item"
    v-show="!(auth&&!auth.read)"
    v-bind="$attrs">
    <template v-if="$attrs['label-width']!==0&&$attrs['label-width']!=='0'"
      #label="params">
      <div class="sfc-form-item-label-container">
        <div class="sfc-form-item-label-label">
          <slot name="label"
            v-bind="{...(params || {})}">
            <span>{{params.label}}
              <el-tooltip v-if="tooltip"
                effect="light"
                :content="tooltip">
                <svg-icon icon-class="el-question" /></el-tooltip>
            </span>
          </slot>
        </div>
        <div v-if="showExtra($attrs.widgetType)&&extra&&extra.label&&extra.data&&extra.data[attrs.prop]"
          class="sfc-form-item-label-extra"
          style="line-height:16px;font-size:10px;"
          :style="getExtraStyle($attrs.widgetType)"><span style="color:#FF9C18;">{{extra.label}}</span></div>
      </div>
    </template>
    <template v-for="(_, slot) in computedSlots"
      #[slot]="params">
      <div v-if="slot === 'default'"
        style="width:100%">
        <div style="width:100%">
          <funi-label v-if="showLabel($attrs.widgetType) && !(!$attrs.disabled||auth&&auth.write) && slot === 'default'"
            v-model="$attrs.modelValue"
            :key="slot" />
          <div v-else-if="showHtmlDiv($attrs.widgetType) && !(!$attrs.disabled||auth&&auth.write)&& slot === 'default'"
            v-html="xssHtml($attrs.modelValue)"
            :key="slot+'1'"
            style="line-height:1;width: 100%;"></div>
          <slot v-else
            :name="slot"
            v-bind="{...(params || {}),disabled:!(!$attrs.disabled||auth&&auth.write)}"></slot>
        </div>
        <div v-if="extra&&extra.label&&extra.data&&extra.data[attrs.prop]"
          style="line-height:16px;">
          <component :is="getExtra" />
        </div>
      </div>
      <slot v-else
        :name="slot"
        v-bind="{...(params || {}),disabled:!(!$attrs.disabled||auth&&auth.write)}"></slot>
    </template>
  </el-form-item>
</template>
<script setup>
import { inject, onMounted, reactive, useAttrs, watch, useSlots, computed, h, resolveComponent } from "vue";
import { deepClone } from '../../common/utils/util';
import SvgIcon from "../../svg-icon/index.vue";
import xss from 'xss';
const fieldAuth = inject('fieldAuth');
const modelId = inject('modelId');
const mainModelId = inject('mainModelId');
const extra = inject('sfcForm.extra');
const attrs = useAttrs();
const slots = useSlots();

const props = defineProps({
  /**字段备注提示信息 */
  tooltip: {
    type: String,
    default: ''
  }
})

const computedSlots = computed(() => {
  let newSlots = {};
  if (slots) {
    Object.keys(slots).forEach(slot => {
      if (slot !== 'label') {
        newSlots[slot] = slots[slot];
      }
    })
  }
  return newSlots;
})

let auth = reactive({ read: !modelId, write: !modelId });
watch(fieldAuth, (newVal) => {
  if (mainModelId === modelId) {
    if (attrs && attrs.prop && newVal && newVal.length > 0) {
      let fIndex = newVal.findIndex(item => item.fieldName === attrs.prop);
      if (fIndex >= 0) {
        Object.assign(auth, fieldAuth[fIndex]);
      } else {
        Object.assign(auth, { read: true, write: false });
      }
    } else {
      Object.assign(auth, { read: true, write: false });
    }
  } else {
    Object.assign(auth, { read: true, write: false });
  }
}, { immediate: true })

/**
 * 渲染变更额外数据的组件
 */

const getExtraStyle = (widgetType) => {
  let styleObj = {};
  if (['funi-money-input'].includes(widgetType)) {
    styleObj = { marginTop: '30px' }
  }
  return styleObj;
}

const getExtra = () => {
  let extraNode = h('span', '');
  if (['funi-select'].includes(attrs.widgetType)) {
    const SfcSelect = resolveComponent('sfc-select');
    if (slots.default) {
      const extraControl = slots.default({})[0];
      const childProps = deepClone(extraControl.props);
      childProps.modelValue = extra.data[attrs.prop];
      childProps.disabled = true;
      childProps.isExtra = true;
      extraNode = h(SfcSelect, childProps);
    }
  } else if (showExtra(attrs.widgetType)) {
    extraNode = h('span', { style: { color: '#FF9C18', fontSize: '12px' } }, getExtraVal(extra.data[attrs.prop], attrs.widgetType));
  }
  return extraNode;
}

const getExtraVal = (val, widgetType) => {
  if (['sfc-user', 'sfc-org'].includes(widgetType)) {
    let showVal = '';
    if (val) {
      try {
        const list = JSON.parse(val);
        if (list && list.length > 0) {
          showVal = list.map(item => item.name).join(',');
        }
      } catch {
        showVal = val;
      }
    }
    return showVal;
  } else {
    return val;
  }
}

const showExtra = widgetType => {
  const showTypes = ['input', 'textarea', 'number', 'date', 'time', 'funi-select', 'sfc-user', 'sfc-org', 'funi-money-input'];
  return showTypes.indexOf(widgetType) >= 0;
}

const showLabel = widgetType => {
  const showTypes = ['input', 'textarea', 'number', 'date', 'time'];
  return showTypes.indexOf(widgetType) >= 0;
};

const showHtmlDiv = widgetType => {
  const showTypes = ['rich-editor', 'funi-editor'];
  return showTypes.indexOf(widgetType) >= 0;
};

const xssHtml = (val) => {
  const options = {
    css: false,
    onTag (tag, html, options) {
      if (tag === 'iframe') {
        return html.replace(/javascript:?/, '')
      }
    },
    // 避免把页面样式过滤掉
    onIgnoreTagAttr (tag, name, value, isWhiteAttr) {
      // 过滤掉标签上的事件
      if (/^[^on]/.test(name)) {
        return name + '="' + xss.escapeAttrValue(value) + '"'
      }
    },
  }
  const filter = new xss.FilterXSS(options)
  return filter.process(val)
}
</script>
<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
.sfc-form-item {
  .sfc-form-item-label-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    .sfc-form-item-label-label::after {
      content: ':';
      color: el.getCssVar('text-color', 'regular');
      margin-left: 4px;
    }
    .sfc-form-item-label-extra::after {
      content: ':';
      color: #ff9c18;
      margin-left: 4px;
    }
  }
}
</style>