<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-03-15 10:16:03
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-11-05 09:46:41
 * @FilePath: \src\components\FuniFormEngine\components\sfc-chart\index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="sfc-chart"
    style="width:100%">
    <component ref="chart"
      :is="chartName"
      v-bind="$attrs"
      v-bind:[dataName]="chartData.data"
      :custom="custom" />
  </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted } from 'vue';
const props = defineProps({
  //请求地址
  url: {
    type: String
  },
  //组件名
  chartName: {
    type: String
  },
  //数据属性名
  dataName: {
    type: String,
    default: 'data'
  },
  //值列表
  value: {
    type: Array,
    default: () => []
  },
  //请求参数
  requestOtherParam: {
    type: Object,
    default: () => {
      return {}
    }
  },
  converter: [Function]
})

const chart = ref();

const instance = getCurrentInstance();

const chartData = reactive({
  data: []
})

const custom = reactive({});

onMounted(() => {
  refresh();
})

const refresh = (params = null) => {
  if (props.url) {
    let url = props.url;
    let params = { ...(props.requestOtherParam || {}) };
    const loading = instance.proxy.$loading({ fullscreen: true });
    window.$http.post(url, params)
      .then(res => {
        if (props.converter) {
          let result = props.converter(res.list);
          if (Array.isArray(result)) {
            chartData.data = result;
          } else if (result.isCustom) {
            Object.assign(custom, result);
          } else {
            Object.assign(chartData, result);
          }
        } else {
          chartData.data = res.list;
        }
        loading.close();
      })
      .catch(() => {
        loading.close();
      })
  }
}
defineExpose({ refresh })
</script>
<style lang='scss' scoped>
</style>