<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-11-18 16:19:59
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-11-26 17:21:48
 * @FilePath: \src\components\FuniFormEngine\components\sfc-hyper-link\index.vue
 * @Description: 
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved. 
-->
<template>
  <el-button type="primary"
    link
    @click="goToLink"
    v-auth="auth || void 0"> {{ modelValue || '' }} </el-button>
</template>
<script setup>
import { useRouter } from 'vue-router';
const router = useRouter();
const emit = defineEmits(['click']);
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  src: [String],
  pathName: String,
  auth: String,
  pathQuery: {
    type: Object,
    default: () => ({})
  },
});

function goToLink () {
  if (props.src) {
    window.open(props.src);
    return;
  }
  if (!props.pathName) {
    emit('click', props.modelValue);
  } else {
    router.push({
      name: props.pathName,
      query: props.pathQuery
    });
  }

}
</script>
<style scoped></style>