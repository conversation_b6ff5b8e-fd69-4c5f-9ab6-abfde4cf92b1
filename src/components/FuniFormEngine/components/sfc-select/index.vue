<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-02-21 17:43:33
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-26 19:19:09
 * @FilePath: \src\components\FuniFormEngine\components\sfc-select\index.vue
 * @Description: 下拉框
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-label v-if="$attrs.disabled"
    :length="20"
    v-model="localValName"
    :styleObj="isExtra ? { color: '#FF9C18', fontSize: '12px' } : { color: '#606266' }" />
  <el-select v-else
    v-bind="$attrs"
    :multiple="multiple"
    v-model="localVal"
    @change="val=>handleChange(val,true)">
    <el-option v-for="item in localOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value" />
  </el-select>
</template>
<script setup>
import { ref, watchEffect, onMounted, shallowRef, watch, useAttrs, getCurrentInstance } from 'vue';
const emit = defineEmits(['update:modelValue', 'change', 'change-obj', 'change-options']);
const attrs = useAttrs();
const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean, Array, Object],
    default: ''
  },
  /**
   * 请求地址
   */
  url: {
    type: String,
    default: '/csops/dic/findDicByType'
  },
  requestParams: {
    type: Object,
    default: () => {
      return {};
    }
  },
  auto: {
    type: Boolean,
    default: true
  },
  changeField: [String],
  /**
   * 字典码
   */
  typeCode: {
    type: String,
    default: ''
  },
  isLocal: {
    type: Boolean,
    default: false
  },
  options: {
    type: Array,
    default: () => {
      return new Array();
    }
  },
  isStringVal: {
    type: Boolean,
    default: false
  },
  multiple: {
    type: Boolean,
    default: false
  },
  /**
   * 初始化是否触发change事件,初始化需要给表单其它字段赋值的时候打开
   */
  isInitUpdate: {
    type: Boolean,
    default: true
  },
  /**
   * 仅一个选项时默认选中
   */
  checkedOnlyOne: {
    type: Boolean,
    default: false
  },
  /**
   * 是否作为变更额外参数显示
   */
  isExtra: {
    type: Boolean,
    default: false
  }
});
const localVal = ref('');
const localValName = ref('');
const ObjVal = ref('');
const localOptions = shallowRef([]);
const instance = getCurrentInstance();
let isFirstChangeOptions = true; //是否首次改变选项

onMounted(() => {
  let onlyOneHasCondition = false; //选中唯一项打开时是否有条件,有条件就默认请求
  if (
    attrs.disabled &&
    props.requestParams &&
    props.requestParams.asUrl &&
    props.requestParams.model_id &&
    props.checkedOnlyOne
  ) {
    if (props.requestParams && props.requestParams.params && props.requestParams.params.length > 0) {
      onlyOneHasCondition =
        props.requestParams.params.filter(p => {
          let hasCondition = false;
          if (p.conditions && p.conditions.length > 0) {
            hasCondition =
              p.conditions.filter(c => c.value !== null && c.value !== undefined && c.value !== '').length > 0;
          }
          return hasCondition;
        }).length > 0;
    }
  }
  let isRequestOptions =
    !(attrs.disabled && props.requestParams && props.requestParams.asUrl && props.requestParams.model_id) ||
    onlyOneHasCondition;
  if (isRequestOptions) {
    requestOptions();
  }
});

watch(
  () => props.modelValue,
  () => {
    let newVal = props.modelValue;
    if (props.isStringVal && props.multiple && typeof newVal === 'string') {
      if (newVal) {
        try {
          if (!newVal.startsWith('[')) {
            newVal = [newVal];
          } else {
            newVal = JSON.parse(newVal);
          }
        } catch (e) {
          console.log(e);
        }
      } else {
        newVal = [];
      }
    }
    if (props.multiple) {
      if (!Array.isArray(newVal) && !props.isStringVal) {
        localValName.value = newVal;
        return;
      }
      localVal.value = newVal;
      if (attrs.disabled && localOptions && localOptions.value.length > 0) {
        let labelList = [];
        if (newVal && newVal.length > 0) {
          newVal.forEach(val => {
            let fIndex = localOptions.value.findIndex(opt => opt.value === val);
            if (fIndex >= 0) {
              labelList.push(localOptions.value[fIndex]?.label);
            }
          });
        }
        if (labelList && labelList.length > 0) {
          localValName.value = labelList.join(',');
        }
      } else {
        localValName.value = newVal?.join(',');
      }
    } else {
      localVal.value = newVal;
      if (attrs.disabled && localOptions && localOptions.value.length > 0) {
        let fIndex = localOptions.value.findIndex(opt => opt.value === newVal);
        if (fIndex >= 0) {
          localValName.value = localOptions.value[fIndex]?.label;
        }
      } else {
        localValName.value = newVal;
      }
    }
    if (props.isInitUpdate && localOptions && localOptions.value.length >= 0) {
      handleChange(newVal);
    }
    if (
      attrs.disabled &&
      newVal &&
      props.requestParams &&
      props.requestParams.asUrl &&
      props.requestParams.model_id &&
      !props.checkedOnlyOne
    ) {
      requestOptions();
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => localOptions,
  () => {
    if (localOptions.value && localOptions.value && localOptions.value.length > 0) {
      emit('change-options', { isFirstChangeOptions, options: [...(localOptions.value || [])] });
      isFirstChangeOptions = false;
    }
    if (attrs.disabled && localOptions && localOptions.value.length > 0) {
      if (props.isStringVal && props.multiple && (typeof props.modelValue === 'string' || Array.isArray(props.modelValue))) {
        let newVal = props.modelValue || [];
        if (newVal) {
          if (typeof props.modelValue === 'string') {
            try {
              if (!newVal.startsWith('[')) {
                newVal = [newVal];
              } else {
                newVal = JSON.parse(newVal);
              }
            } catch (e) {
              console.log(e);
            }
          }
          let selectLabels = localOptions.value.filter(opt => newVal.includes(opt.value)).map(opt => opt.label);
          localValName.value = selectLabels.join(',');

        } else {
          localValName.value = props.modelValue;
        }
      } else {
        let fIndex = localOptions.value.findIndex(opt => opt.value === localVal.value);
        if (fIndex >= 0) {
          localValName.value = localOptions.value[fIndex]?.label;
        }
      }
    }
    if (props.isInitUpdate && props.modelValue && localOptions && localOptions.value.length >= 0) {
      handleChange(props.modelValue);
    }
    if (props.checkedOnlyOne && localOptions.value.length === 1) {
      handleChange(localOptions.value[0].value);
    } else if (attrs.disabled && props.checkedOnlyOne && localOptions.value.length === 0) {
      let val;
      //没有选项的时候就清空显示值
      if (props.isStringVal && props.multiple) {
        val = [];
      } else {
        val = '';
      }
      handleChange(val);
    }
  },
  { immediate: true, deep: true }
);

watchEffect(() => {
  const { typeCode } = props;
  if (!typeCode) {
    if (props.options && props.options.length !== 0) {
      localOptions.value = props.options;
    }
  }
});

if (props.auto) {
  watch(
    () => props.requestParams,
    (newVal1, oldVal1) => {
      if (!attrs.disabled && localVal.value) {
        let newVal = '';
        if (props.isStringVal && props.multiple) {
          localVal.value = [];
          newVal = null;
        } else {
          localVal.value = '';
        }
        changeObj(localVal.value);
        emit('change', newVal);
        emit('update:modelValue', newVal);
      }
      requestOptions();
    }
  );
} else {
  watch(
    () => props.changeField,
    newVal1 => {
      if (!attrs.disabled || props.checkedOnlyOne) {
        let newVal = '';
        if (props.isStringVal && props.multiple) {
          localVal.value = [];
          newVal = null;
        } else {
          localVal.value = '';
        }
        changeObj(localVal.value);
        emit('change', newVal);
        emit('update:modelValue', newVal);
        requestOptions();
      }
    }
  );
}

function requestOptions () {
  const { typeCode, isLocal, requestParams, modelValue } = props;
  if (isLocal && typeCode) {
    const enumSourceStr = localStorage.getItem('enumSource');
    if (enumSourceStr) {
      let enumSource = [];
      try {
        enumSource = JSON.parse(enumSourceStr);
      } catch { }
      if (enumSource && enumSource.length >= 0) {
        let fIndex = enumSource.findIndex(item => item.code === typeCode);
        if (fIndex >= 0 && enumSource[fIndex].dicResponses && enumSource[fIndex].dicResponses.length > 0) {
          localOptions.value = enumSource[fIndex].dicResponses.map(item => {
            return {
              ...item,
              label: item.label ?? item.name,
              value: item.value ?? item.code
            };
          });
        }
      }
    }
  } else {
    let requestUrl = props.url;
    let params;
    if (typeCode) {
      params = { typeCode };
    } else if (requestParams) {
      const { asUrl, ...other } = requestParams;
      requestUrl = asUrl;
      params = other;
      let joinFields = [];
      if (!props.multiple && requestUrl && modelValue && attrs.disabled && !props.checkedOnlyOne) {
        let val = modelValue;
        if (props.isStringVal && props.multiple) {
          try {
            val = JSON.parse(val);
          } catch (e) {
            console.log(e);
          }
        }
        if (params && params.params && params.params.length > 0) {
          let isContain = false;
          params.params.forEach(item => {
            if (item && item.conditions && item.conditions.length > 0) {
              item.conditions.forEach(item1 => {
                if (item1 && item1.key === (params.valueKey ?? 'id')) {
                  isContain = true;
                  item1.value = val;//用当前值精确查询
                }
              });
            }
          });
          if (!isContain) {
            params.params.push({
              logicalOperator: 'AND',
              conditions: [
                {
                  key: params.valueKey ?? 'id',
                  operator: props.multiple ? 'EQUAL' : 'EQUAL',
                  value: val
                }
              ]
            });
          }
        } else {
          params.params = [
            {
              conditions: [
                {
                  key: params.valueKey ?? 'id',
                  operator: props.multiple ? 'EQUAL' : 'EQUAL',
                  value: val
                }
              ]
            }
          ];
        }
      }
      if (params && params.params && params.params.length > 0) {
        params.params.forEach(item => {
          if (item && item.conditions && item.conditions.length > 0) {
            item.conditions.forEach(item1 => {
              if (item1 && item1.tableName && item1.joinFieldName) {
                joinFields.push(item1.joinFieldName);
              }
            });
          }
        });
      }
      if (joinFields && joinFields.length > 0) {
        params.joinFields = joinFields;
      }
    }
    if (requestUrl) {
      instance.proxy.$lowCodeRequest.proxyOtherRequestAsync(requestUrl, params).then(res => {
        if (res && res.list && res.list.length > 0) {
          localOptions.value = res.list.map(item => {
            return {
              ...item,
              label: item.label ?? item.name,
              value: item.value ?? item.code
            };
          });
        } else {
          localOptions.value = [];
        }
      });
    }
  }
}

function handleChange (val, emptyTrigger = false) {
  let newVal = val;
  if (props.isStringVal && props.multiple && typeof val !== 'string') {
    if (newVal && newVal.length > 0) {
      newVal = JSON.stringify(val);
    } else {
      newVal = null;
    }
  }
  changeObj(val, emptyTrigger);
  emit('change', newVal);
  if (!props.disabled) {
    emit('update:modelValue', newVal);
  }
}

function changeObj (val, emptyTrigger = false) {
  let newValObj;
  if (localOptions.value && localOptions.value.length > 0) {
    if (Array.isArray(val) && val && val.length > 0) {
      newValObj = localOptions.value.filter(item => val.includes(item.value));
    } else {
      newValObj = localOptions.value.find(item => item.value === val);
    }
  }
  if (newValObj && ObjVal.value !== newValObj.value) {
    ObjVal.value = newValObj.value;
    emit('change-obj', newValObj);
  } else if (emptyTrigger) {//手动清除的时候触发
    ObjVal.value = '';
    emit('change-obj', {});
  }
}
</script>
<style lang="scss" scoped></style>
