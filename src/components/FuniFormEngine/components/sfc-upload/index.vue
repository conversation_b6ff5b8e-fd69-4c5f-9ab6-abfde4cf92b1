<template>
  <div class="sfc-upload"
    :class="computedClass">
    <el-upload :key="key"
      v-bind="$attrs"
      :disabled="disabled"
      :limit="limit"
      :action="urlComputed"
      :headers="realHeaders"
      :file-list="localFileList"
      :listType="listType"
      :with-credentials="true"
      :on-preview="onPreview"
      :http-request="handleHttpRequest"
      @success="onSuccess"
      @remove="onRemove">
      <template #tip>
        <slot name="tip"
          v-bind="params || {}"></slot>
      </template>
      <template #default>
        <div v-if="downloadTip&&disabled&&listType!=='picture-card'">
          <span style="margin-left:10px;">{{localFileList&&localFileList.length>0?downloadTip:''}}</span>
        </div>
        <slot v-else
          name="default"
          v-bind="params || {}"></slot>
      </template>
    </el-upload>
    <funi-image-view ref="fivRef"
      :urlList="urlList" />
  </div>
</template>

<script setup>
import { reactive, watchEffect, watch, nextTick, ref, computed, onMounted } from 'vue';
import { ElLoading } from 'element-plus';

const emit = defineEmits(['update:modelValue', 'success', 'remove']);

const props = defineProps({
  modelValue: {
    type: [Array, String],
    default: () => {
      return [];
    }
  },
  pictureSize: {
    type: String,
    default: 'default'
  },
  isStringVal: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  listType: [String],
  fileList: {
    type: Array,
    default: () => {
      return [];
    }
  },
  downloadTip: {
    type: String,
    default: '点击下载'
  },
  limit: {
    type: Number,
    default: 3
  },
  action: {
    type: String,
    default: '/csccs/file/upload'
  },
  urlPrefix: {
    type: String,
    default: '/csccs/file/read?id='
  },
  headers: {
    type: Object,
    default: () => {
      return new Object()
    }
  }
});
let innerChange = false;
const key = ref(0);
const fivRef = ref(null);
const urlList = ref([]);
let localVal = reactive([]);
let localValRes = reactive([]);
let localFileList = reactive(...(props.fileList || []));
const uploadHide = ref(false);
const urlComputed = computed(() => {
  return `${props.action}`;
});
const computedClass = computed(() => {
  let classStr = props.disabled || uploadHide.value ? 'sfc-upload-disabled' : 'sfc-upload';
  if (props.listType === 'picture-card' && props.pictureSize === 'small') {
    classStr += ' sfc-upload-small';
  }
  if (!props.downloadTip && props.disabled && props.listType != 'picture-card') {
    classStr += ' sfc-upload-nodownloadtip';
  }
  return classStr;
});

let defaultHeaders = {
  'X-FuniPaas-Authorization': sessionStorage.getItem('token')
};
let realHeaders = reactive({});
Object.assign(realHeaders, defaultHeaders, props.headers);

onMounted(() => {
  if (props.modelValue) {
    initVal(props.modelValue);
  }
})

watch(() => localVal, nVal => {
  let newVal;
  if (props.isStringVal) {
    if (nVal && nVal.length > 0) {
      newVal = JSON.stringify((nVal || []));
    }
  } else {
    newVal = nVal;
  }
  innerChange = true;
  emit('update:modelValue', newVal);

}, { deep: true });

watch(
  () => props.modelValue,
  nVal => {
    if (innerChange) {
      innerChange = false;
      return;
    }
    initVal(nVal);
  }
);

const initVal = (nVal) => {
  let newVal;
  if (props.isStringVal && typeof (nVal) === 'string') {
    newVal = JSON.parse(nVal);
  } else {
    newVal = nVal;
  }
  if (newVal && newVal.length >= 0) {
    getFiles(newVal).then(fileList => {
      if (localVal && localVal.length > 0) {
        localVal.splice(0, localVal.length);
      }
      localVal.push(...(newVal || []));
      localFileList = [...fileList];
      localValRes = localFileList.map(item => {
        return {
          id: item.id,
          name: item.name,
          url: item.url
        };
      });
      key.value++;
    });
  } else {
    localVal.splice(0, localVal.length);
    localFileList = [];
    localValRes = [];
  }
  uploadHide.value = props.limit > 0 && newVal && newVal.length >= props.limit;
}

const getFiles = async newVal => {
  let fileList = [];
  for (let i = 0; i < newVal.length; i++) {
    let item = newVal[i];
    let id = '', name = '';
    if (typeof (item) === 'object') {
      id = item.id;
      name = item.name;
    } else {
      id = item;
      name = item;
    }
    let res = '';
    if (props.urlPrefix && props.listType === 'picture-card') {
      res = await window.$http.post(
        `${props.urlPrefix}${id}`,
        {},
        {
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/octet-stream;charset=utf-8'
          }
        }
      );
    }
    fileList.push({
      id: id,
      name: name,
      url: res ? window.URL.createObjectURL(res) : ''
    });
  }
  return fileList;
};

const handleHttpRequest = (options) => {
  let formData = new FormData();
  formData.append('file', options.file);
  if (options.data) {
    for (let key in options.data) {
      formData.append(key, options.data[key]);
    }
  }
  return window.$http.upload(options.action, formData);
}

const onSuccess = (res, file, fileList) => {
  if (res && !res.id && res.fileInfo) {
    res.id = res.fileInfo.fileInstanceInfoId;
  }
  localVal.push({ id: res.id, name: file.name });
  localValRes.push({ id: res.id, name: file.name, uid: file.uid });
  if (localFileList) {
    localFileList.push({ id: res.id, url: file.url, name: file.name });
  }
  uploadHide.value = props.limit > 0 && localValRes && localValRes.length >= props.limit;
  emit('success', res, file, fileList);
};

const onRemove = (file, fileList) => {
  let fIndex = localValRes.findIndex(f => f.uid === file.uid || f.url === file.url);
  if (fIndex >= 0) {
    let res = localValRes[fIndex];
    if (localFileList) {
      let del1 = localFileList.findIndex(item => {
        return item.url === res.url || item.uid === res.uid;
      });
      if (del1 >= 0) {
        localFileList.splice(del1, 1);
      }
    }
    if (localVal) {
      let del2 = localVal.findIndex(item => {
        if (typeof (item) === 'object') {
          return item.id === res.id;
        } else {//兼容指存id的情况
          return item === res.id
        }
      });
      if (del2 >= 0) {
        localVal.splice(del2, 1);
      }
      localValRes.splice(fIndex, 1);
    }
    uploadHide.value = props.limit > 0 && localValRes && localValRes.length >= props.limit;
    emit('remove', file, fileList);
  }
};

const onPreview = (file) => {
  if (props.listType === 'picture-card') {
    urlList.value = [file.url];
    fivRef.value.showViewer();
  } else {
    const id = file.id;
    const loading = ElLoading.service({ fullscreen: true });
    window.$http.downloadFile(`${props.urlPrefix}${id}`, { id })
      .then(() => {
        loading.close();
      })
      .catch(() => {
        loading.close();
      })
  }
}
</script>
<style lang="scss" scoped>
.sfc-upload-disabled {
  :deep(.el-upload--picture-card) {
    display: none;
  }
}

.sfc-upload-nodownloadtip {
  :deep(.el-upload--text) {
    display: none;
  }
}

.sfc-upload-small {
  :deep(.el-upload--picture-card) {
    --el-upload-picture-card-size: 50px;
  }
  :deep(.el-upload-list--picture-card) {
    --el-upload-list-picture-card-size: 50px;
  }
}
</style>
