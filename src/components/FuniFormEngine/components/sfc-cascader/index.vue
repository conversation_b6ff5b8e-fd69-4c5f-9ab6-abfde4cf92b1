<template>
  <el-cascader v-bind="$attrs"
    :options="computedOptions" />
</template>
<script setup>

import { computed, reactive, ref, watch } from 'vue';

const props = defineProps({
  apiUrl: [String],
  options: {
    type: Array,
    default: () => []
  }
})

const localOptions = ref([]);

const computedOptions = computed(() => {
  return props.apiUrl ? localOptions.value : props.options;
});

watch(
  () => props.apiUrl,
  (val) => {
    if (val) {
      window.$http.post(val)
        .then(res => {
          if (res && res.list) {
            localOptions.value = res.list;
          } else {
            localOptions.value = [];
          }
        })
    }
  }, { immediate: true })

</script>
<style lang='scss' scoped>
</style>
