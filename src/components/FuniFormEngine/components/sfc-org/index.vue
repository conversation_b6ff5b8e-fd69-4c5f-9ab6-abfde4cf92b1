<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-08-22 10:32:37
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-04-02 11:14:29
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\components\sfc-org\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-label v-if="disabled"
    v-model="nameComputed" />
  <FuniRUOCLowCode v-else
    :mode="mode"
    :disabled="disabled"
    :propsConfig="propsConfig"
    v-model="ids"
    @update:modelValue="updateModelValue" />
</template>

<script>
import { computed, getCurrentInstance, reactive, toRefs, watch, nextTick, onMounted } from 'vue';
import { useFormItem } from 'element-plus';
export default {
  name: 'index',
  components: {
  },
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    mode: {
      type: String,
      default: 'multiple'
    },
    //选择范围
    allowedScope: {
      type: Array,
      default: () => {
        return []
      }
    },
    allowedSelf: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  setup (props) {
    const instance = getCurrentInstance();
    const { formItem } = useFormItem();
    const state = reactive({
      ids: [],
      allowedOrg: [],
      allowedRole: [],
      allowedSelf: props.allowedSelf
    })
    watch(() => state.ids, (newVal) => {
      if (newVal && newVal.length > 0) {
        newVal.forEach(item => {
          delete item.component;
          delete item.mode;
        })
      }
      let newValStr = newVal && newVal.length > 0 ? JSON.stringify(newVal || []) : '';
      instance.proxy.$emit('change', newValStr);
      instance.proxy.$emit('update:modelValue', newValStr);
    }, { deep: true })
    watch(() => props.modelValue, (newVal) => {
      let ids = [];
      if (newVal && typeof (newVal) === 'string') {
        try {
          ids = JSON.parse(newVal);
        } catch (ex) { console.log(ex) }
      } else if (newVal) {
        ids = [...newVal];
      }
      state.ids = ids;
    }, { immediate: true, deep: true })

    watch(() => props.allowedScope, (newVal) => {
      if (newVal && newVal.length > 0) {
        state.allowedOrg = newVal.filter(item => item.type === 'org')?.map(item => item.id);
        state.allowedRole = newVal.filter(item => item.type === 'role')?.map(item => item.id);
      } else {
        state.allowedOrg = [];
        state.allowedRole = [];
      }
    }, { immediate: true, deep: true })

    watch(() => props.allowedSelf, (newVal) => {
      state.allowedSelf = newVal;
    })

    const nameComputed = computed(() => {
      let names = '';
      if (state.ids && state.ids.length > 0) {
        let ids = [];
        if (state.ids && typeof (state.ids) === 'string') {
          try {
            ids = JSON.parse(state.ids);
          } catch (ex) { console.log(ex) }
        } else if (state.ids) {
          ids = [...state.ids];
        }
        names = ids.map(item => item.name).toString();
      }
      return names;
    })

    const propsConfig = computed(() => {
      let config = {
        type: 'org',
        mode: props.mode,
        allowedOrg: [],
        allowedRole: [],
        allowedSelf: props.allowedSelf
      }
      Object.assign(config, { allowedOrg: state.allowedOrg, allowedRole: state.allowedRole, allowedSelf: state.allowedSelf });
      return config;
    })

    const updateModelValue = () => {
      nextTick(() => {
        formItem?.validate('change');
      })
    }

    onMounted(() => {
      if (!(props.allowedScope && props.allowedScope.length > 0) && state.ids.length <= 0) {
        state.ids.push({ type: 'org', id: $funi.auth.user.unit.id, name: $funi.auth.user.unit.name });
      }
    })

    return {
      ...toRefs(state),
      propsConfig,
      nameComputed,
      updateModelValue
    }
  },
  data () {
    return {
    }
  },
  mounted () {

  },
  methods: {

  },
}
</script>