<template>
  <funi-label v-bind="$attrs"
    v-model="localVal" />
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String]
  }
})

const localVal = computed((newVal) => {
  let val = '';
  try {
    if (props.modelValue) {
      const org = JSON.parse(props.modelValue);
      if (org) {
        val = org.name;
      }
    }
  } catch {
    val = props.modelValue;
  }
  return val;
})

</script>
<style lang='scss' scoped>
</style>