<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-02-27 16:11:47
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-01-22 10:32:44
 * @FilePath: \src\components\FuniFormEngine\components\sfc-form\index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-form :id="printId"
      class="sfc-form"
      ref="elFormRef"
      v-bind="$attrs"
      :rules="rulesComputed">
      <template v-for="(_, slot) in $slots"
        #[slot]="params">
        <slot :name="slot"
          v-bind="{...(params || {}),extraLabel}"></slot>
      </template>
    </el-form>
    <div ref="printBtn"
      v-print="printObj"></div>
  </div>
</template>
<script>
import { computed, getCurrentInstance, provide, onMounted, reactive, inject, watch, ref } from 'vue'

export default {
  name: 'SfcForm',
  props: {
    rules: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  setup (props, ctx) {
    const extra = reactive({ label: '变更前', data: {} });
    provide('sfcForm.extra', extra);
    const instance = getCurrentInstance();
    const fieldAuth = inject('fieldAuth');
    const modelId = inject('modelId');
    const mainModelId = inject('mainModelId');
    const authRules = reactive({});
    const printId = ref(window.$utils.guid());

    const rulesComputed = computed(() => {
      let rules = props.rules || {};
      if (authRules) {
        for (let key in authRules) {
          if (rules[key]) {
            const fIndex = rules[key].findIndex(r => r.required);
            if (fIndex < 0) {
              rules[Key].push({ required: true, message: '必填' });
            }
          } else {
            rules[key] = authRules[key];
          }
        }
      }
      return rules;
    });

    watch(fieldAuth, (newVal) => {
      if (mainModelId === modelId) {
        if (newVal && newVal.length > 0) {
          newVal.forEach(element => {
            if (element.read && element.required && !authRules[element.fieldName]) {
              authRules[element.fieldName] = [{ required: true, message: '必填' }];
            }
          });
          setTimeout(() => {
            if (instance.proxy.$refs.elFormRef) {
              instance.proxy.$refs.elFormRef.clearValidate();
            }
          }, 20);
        }
      }
    }, { immediate: true })

    onMounted(() => {
      for (let key in instance.proxy.$refs.elFormRef) {
        if (!instance.proxy[key]) {
          instance.proxy[key] = instance.proxy.$refs.elFormRef[key];
        }
      }
    })

    const setFormExtraData = (currentData, config) => {
      window.$http
        .post(config.url, config.param)
        .then(lastData => {
          if (lastData) {
            let extraData = {};
            let keys = Object.keys(currentData);
            if (keys && keys.length > 0) {
              keys.forEach(key => {
                if (currentData[key] !== lastData[key]) {
                  extraData[key] = lastData[key];
                }
              })
            }
            Object.assign(extra, { label: '变更前', data: extraData });
          }
        })
    }

    const print = () => {
      instance.proxy.$refs.printBtn.click();
    }

    const printObj = {
      id: printId.value,
      popTitle: '打印',
    }

    return {
      rulesComputed,
      setFormExtraData,
      printId,
      printObj,
      print
    }
  }
}
</script>
<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
.sfc-form {
  margin-top: 1px;
  margin-left: 1px;
  :deep(.el-date-editor) {
    width: 100% !important;
  }
  :deep(.el-tabs__header.is-top) {
    display: flex !important;
  }
  :deep(.el-tabs__nav-wrap) {
    position: static;
  }
  :deep(.el-tabs__content) {
    padding-top: 1px !important;
    padding-left: 1px !important;
  }
  :deep() {
    .static-content-item {
      margin-left: -1px;
      margin-top: -1px;
    }
    .static-content-item {
      border: none !important;
      margin-top: 0px !important;
      .#{el.$namespace}-form-item__content {
        padding: 0px !important;
      }
      &::before {
        width: 0px !important;
      }
    }
    @include el.b(form-item) {
      margin-bottom: 0;
      transition: all 0.1s;
      background-color: var(--funi-form-label-bgc);
      align-items: stretch;
      border: 1px solid var(--funi-form-border-color);
      margin-left: -1px;
      margin-top: -1px;
      &.is-error > .#{el.$namespace}-form-item__content {
        padding-bottom: 18px;
        > .#{el.$namespace}-form-item__error {
          top: unset;
          bottom: 3px;
          left: 10px;
        }
      }

      &::before {
        content: '';
        display: inline-block;
        width: 1px;
        background: var(--funi-form-border-color);
        order: 2;
      }

      > .#{el.$namespace}-form-item__content {
        padding: 6px 9px;
        order: 3;
        background-color: #fff;
      }

      > .#{el.$namespace}-form-item__label-wrap {
        align-items: center;
        order: 1;
      }
      .#{el.$namespace}-form-item__label {
        padding: 0 3px 0 10px;
        height: unset;
        align-items: center;
      }
    }
  }
}

@media print {
  .sfc-form {
    width: 98.5%;
    :deep() {
      @include el.b(form-item) {
        border: 1px solid var(--el-border-color-lighter);
        background-color: #f8f8f8;
        -webkit-print-color-adjust: exact;
        &::before {
          background: var(--el-border-color-lighter);
        }
      }
    }
  }
}
</style>
