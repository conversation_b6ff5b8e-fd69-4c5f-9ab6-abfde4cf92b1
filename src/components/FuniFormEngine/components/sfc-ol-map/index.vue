<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-11-01 10:15:26
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-06 10:10:41
 * @FilePath: \src\components\FuniFormEngine\components\sfc-ol-map\index.vue
 * @Description: 
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved. 
-->
 <template>
  <funi-ol-map ref="funiOlMapRef"
    v-bind="$attrs" />
</template>
 <script setup>

import { ref } from 'vue';

const funiOlMapRef = ref(null);

const getMap = () => {
  return funiOlMapRef.value.getMap();
}

const getView = () => {
  return funiOlMapRef.value.getView();
}

const setCenter = ({ center }) => {
  funiOlMapRef.value.setCenter(center);
}

const setZoom = ({ zoom }) => {
  funiOlMapRef.value.setZoom(zoom);
}

const draw = ({ type = 'Polygon', isClear = true }) => {
  funiOlMapRef.value.draw(type, isClear);
}

const drawClear = () => {
  funiOlMapRef.value.drawClear();
}

function mapFit ({ extent, duration, padding }) {
  funiOlMapRef.value.mapFit(extent, duration, padding);
}

function drawSplit ({ callback, isClear = true }) {
  funiOlMapRef.value.mapFit(callback, isClear);
}

function drawRevoke ({ }) {
  funiOlMapRef.value.drawRevoke();
}

function getViewBox ({ }) {
  funiOlMapRef.value.getViewBox();
}

function addBoundAreaLayer ({ list, styleObj }) {
  funiOlMapRef.value.addBoundAreaLayer(list, styleObj);
}

function highLightFeatures (param = {}) {
  funiOlMapRef.value.highLightFeatures(param);
}

function changeLayer ({ item, group = 'group' }) {
  funiOlMapRef.value.changeLayer(item, group);
}

function highLightByFilter (param = {}) {
  funiOlMapRef.value.highLightByFilter(param);
}

function highLightLayerClear () {
  funiOlMapRef.value.highLightLayerClear();
}

function closeModal () {
  funiOlMapRef.value.closeModal();
}

function addLayer ({ layers }) {
  funiOlMapRef.value.addLayer(layers);
}

function removeLayer ({ layers }) {
  funiOlMapRef.value.removeLayer(layers);
}

function filterWms ({ sqpString, layerName }) {
  funiOlMapRef.value.filterWms(sqpString, layerName);
}

function setView ({ mapId }) {
  funiOlMapRef.value.setView(mapId);
}

function isShowBaseLayer ({ isShow }) {
  funiOlMapRef.value.isShowBaseLayer(isShow);
}

function drawEnd (param = {}) {
  funiOlMapRef.value.drawEnd(param);
}

function showModal () {
  funiOlMapRef.value.showModal();
}

defineExpose({
  getMap,
  getView,
  setCenter,
  setZoom,
  draw,
  drawClear,
  drawSplit,
  drawRevoke,
  mapFit,
  getViewBox,
  addBoundAreaLayer,
  highLightFeatures,
  changeLayer,
  highLightByFilter,
  highLightLayerClear,
  closeModal,
  addLayer,
  removeLayer,
  filterWms,
  setView,
  isShowBaseLayer,
  drawEnd,
  showModal
});
 </script>
 <style lang='scss' scoped>
</style>
