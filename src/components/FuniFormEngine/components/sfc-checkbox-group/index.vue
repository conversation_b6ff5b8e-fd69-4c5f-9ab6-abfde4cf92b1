<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-14 11:20:40
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-01-03 19:06:06
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\components\sfc-checkbox-group\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-checkbox-group v-bind="$attrs"
    v-model="localVal"
    :style="displayStyle">
    <component :is="childTag"
      v-for="(item,index) in localOptions"
      :key="index"
      :border="childBorder"
      :label="item.value"
      :style="displayStyle">{{ item.label }}</component>
  </el-checkbox-group>
</template>
<script setup>
import { computed, onMounted, shallowRef, watch } from 'vue';

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean, Array, Object],
    default: ''
  },
  /**
   * 请求地址
   */
  url: {
    type: String,
    default: '/csops/dic/findDicByType'
  },
  /**
   * 字典码
   */
  typeCode: {
    type: String,
    default: ''
  },
  isLocal: {
    type: Boolean,
    default: false
  },
  options: {
    type: Array,
    default: () => {
      return new Array();
    }
  },
  childTag: {
    type: String,
    default: 'el-checkbox'
  },
  childBorder: {
    type: Boolean,
    default: false
  },
  displayStyle: {
    type: Object,
    default: () => {
      return new Object();
    }
  },
  isStringVal: {
    type: Boolean,
    default: false
  },
})

const localOptions = shallowRef([
  ...(props.options || [])
]);

onMounted(() => {
  if (props.typeCode && props.url) {
    requestOptions();
  }
})

watch(() => props.typeCode, (newVal) => {
  if (newVal && props.url) {
    requestOptions();
  } else {
    localOptions.value = [...(props.options || [])]
  }
})

const requestOptions = () => {
  const { typeCode, isLocal } = props;
  if (isLocal && typeCode) {
    const enumSourceStr = localStorage.getItem('enumSource');
    if (enumSourceStr) {
      let enumSource = [];
      try {
        enumSource = JSON.parse(enumSourceStr);
      } catch { }
      if (enumSource && enumSource.length >= 0) {
        let fIndex = enumSource.findIndex(item => item.code === typeCode);
        if (fIndex >= 0 && enumSource[fIndex].dicResponses && enumSource[fIndex].dicResponses.length > 0) {
          localOptions.value = enumSource[fIndex].dicResponses.map(item => {
            return {
              label: item.name ?? item.label,
              value: item.code ?? item.value
            }
          })
        }
      }
    }
  } else {
    window.$http.post(props.url, { typeCode: props.typeCode })
      .then(res => {
        if (res && res.list && res.list.length > 0) {
          localOptions.value = res.list.map(item => {
            return {
              label: item.name,
              value: item.code
            }
          })
        }
      })
  }
}
const localVal = computed({
  get: () => {
    let newVal = [];
    if (props.isStringVal && props.modelValue && typeof (props.modelValue) === 'string') {
      newVal = JSON.parse(props.modelValue);
    } else {
      newVal = props.modelValue || [];
    }
    return newVal;
  },
  set: (newVal) => {
    let realNewVal = newVal;
    if (props.isStringVal) {
      if (newVal && newVal.length > 0) {
        realNewVal = JSON.stringify(newVal);
      } else {
        realNewVal = JSON.stringify([]);
      }
    }
    emit('update:modelValue', realNewVal);
  }
})

</script>
<style lang='scss' scoped>
</style>