<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-06-29 14:39:05
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-01-23 11:18:39
 * @FilePath: \src\components\FuniFormEngine\components\sfc-step\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<script>
import lowcode from '../../common/utils/lowcode';
import { h, unref, defineComponent, provide } from 'vue';
import { ElNotification, ElMessageBox } from 'element-plus';
import { generateComponent } from '../../common/utils/vue3SfcCompiler';
import { decodeFormula, createEXpFun } from '../../common/utils/util.js';
import { useMultiTab } from '@/utils/hooks/useMultiTab.js';
import { useAppStore } from '@/stores/useAppStore';

export default defineComponent({
  props: {
    modelValue: { type: Object },
    code: { type: String },
    editable: { type: Boolean, default: true },
    serialNumber: { type: String },
    detailExtendId: { type: String },
    saveExtendId: { type: String },
    modelId: { type: String },
    mainModelId: { type: String },
    urls: {
      type: Object,
      default: () => {
        return new Object();
      }
    }
  },
  inject: ['tempData', 'updateTempData', 'loadingStatus', 'getExtendParams', 'busObj', 'fieldAuth'],
  data () {
    return {
      isInit: false,
      localValue: null,
      isSave: false, //用于区分是否变更首次提交
      id: '',
      lastId: '', //上手业务件id
    };
  },
  setup (props) {
    provide('modelId', props.modelId);
    provide('mainModelId', props.mainModelId);
    const multiTab = useMultiTab();
    const appStore = useAppStore();
    const closeCurrentPage = () => {
      multiTab.closeCurrentPage();
    };
    return { closeCurrentPage, appStore };
  },
  mounted () {
    const busType = this.$route.query.busType;
    const { modelValue } = this;
    if (modelValue && this.$refs.funiFormEngineRef) {
      this.$refs.funiFormEngineRef.setFormData({ ...(modelValue || {}) });
    }
    if (this.$refs.funiFormEngineRef) {
      this.$refs.funiFormEngineRef.setFormDisabled(!this.editable);
    }
    let id, cid, businessId;
    if (this.tempData && this.tempData.id) {
      if (this.serialNumber !== '1') {
        id = this.tempData.id;
      }
      //有缓存用缓存的id，第二步的这种情况
      const bizName = this.$route.query.bizName;
      if (['CHANGE', 'CANCEL'].includes(busType) && ['新建', 'add'].includes(bizName)) {
        cid = this.$route.query.cid;
      } else {
        id = this.tempData.id;
        cid = this.tempData.cid;
        businessId = this.tempData.businessId;
      }
    } else {
      id = this.$route.query.id;
      cid = this.$route.query.cid;
      businessId = this.$route.query.businessId;
      if (!['CHANGE', 'CANCEL'].includes(busType)) {
        //不是变更注销就存到缓存
        if (id) {
          this.updateTempData({ id });
        }
        if (cid) {
          this.updateTempData({ cid });
        }
        if (businessId) {
          this.updateTempData({ businessId });
        }
      }
    }
    if (this.tempData && !this.tempData.stepData) {
      this.updateTempData({ stepData: {} });
    }
    if (id || cid || businessId) {
      this.init({ id, cid, businessId });
    } else {
      this.isInit = true;
    }
  },
  watch: {
    modelValue: {
      handler (newVal) {
        if (this.$refs.funiFormEngineRef) {
          this.$nextTick(() => {
            this.$refs.funiFormEngineRef.setFormData({ ...(newVal || {}) });
          });
        }
      },
      immediate: true
    },
    editable: {
      handler (val) {
        if (this.$refs.funiFormEngineRef) {
          this.$refs.funiFormEngineRef.setFormDisabled(!val);
        }
      },
      immediate: true
    }
  },
  methods: {
    init (idObj = {}) {
      const bizName = this.$route.query.bizName;
      const busType = this.$route.query.busType;
      const { id, cid, businessId } = idObj;
      const { serialNumber, modelId, mainModelId, urls, detailExtendId } = this;
      let extendData = {};
      if (detailExtendId && this.getExtendParams) {
        extendData = this.getExtendParams(detailExtendId);
      }
      let detailUrl = id ? urls.detailById : urls.detail;
      let isNotAs = true;
      if (detailUrl) {
        isNotAs = !(detailUrl.startsWith('/as/') || detailUrl.startsWith('as/'));
      }
      if (modelId || isNotAs) {
        let param = { id, cid, businessId, serialNumber, model_id: modelId, extendData };
        if (this.loadingStatus) {
          unref(this.loadingStatus).status = true;
        }
        let url = id ? urls.detailById : urls.detail;
        if (!url) {
          //兼容老数据
          url = urls.detail;
        }
        window.$http
          .post(url, param)
          .then(res => {
            let newVal = res || {};
            if (newVal && (modelId === mainModelId || serialNumber === 1)) {
              //主模型的时候更新缓存id
              this.lastId = newVal.id;
              if (bizName === '编辑' && ['CHANGE', 'CANCEL'].includes(busType)) {
                //变更数据编辑的时候
                this.lastId = newVal.lastId;
              }
              this.updateTempData({ cid: newVal.cid, lastId: this.lastId, businessId: newVal.business_id });
            }
            this.isInit = true;
            this.$nextTick(() => {
              if (this.$refs.funiFormEngineRef) {
                this.$refs.funiFormEngineRef.setFormData({ ...newVal });
                let stepName = `step${this.serialNumber}`;
                let stepData = {};
                if (this.tempData && this.tempData.stepData) {
                  stepData = this.tempData.stepData;
                  stepData[stepName] = { ...newVal };
                  Object.assign(this.tempData.stepData, stepData);
                }
                if (modelId === mainModelId) {
                  this.$emit('changeHead', newVal);
                  if (
                    (['审核'].includes(bizName) || (['详情'].includes(bizName) && ['CHANGE'].includes(busType))) &&
                    newVal &&
                    ['CHANGE'].includes(newVal.bus_type) &&
                    newVal.lastId
                  ) {
                    //变更业务审核时显示变更数据
                    let config = {
                      url: urls.detailById,
                      param: { id: newVal.lastId, serialNumber, model_id: modelId }
                    };
                    this.$refs.funiFormEngineRef.setFormExtraData(newVal, config);
                  }
                }
              }
            });
          })
          .finally(() => {
            this.isInit = true;
            if (this.loadingStatus) {
              unref(this.loadingStatus).status = false;
            }
          });
      } else {
        this.isInit = true;
      }
    },
    ts () {
      return new Promise((resovle, reject) => {
        this.store()
          .then(() => {
            ElNotification({
              title: '提示',
              message: `暂存成功!`,
              type: 'success'
            });
            resovle();
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    nextStep () {
      return this.store();
    },
    //更新缓存步骤数据
    updateStepData (data = {}) {
      let stepName = `step${this.serialNumber}`;
      let stepData = {};
      if (this.tempData && this.tempData.stepData) {
        stepData = this.tempData.stepData;
        stepData[stepName] = data;
        Object.assign(this.tempData.stepData, stepData);
      }
    },
    submit (param) {
      return new Promise((resovle, reject) => {
        this.store(true)
          .then(async () => {
            const businessId = this.tempData.businessId;
            const workflow = { businessId, businessExecutionType: 'SUBMIT' };
            await this.afterSubmit(workflow);
            ElMessageBox.alert('<span style="font-size:larger">提交成功!</span>', '提示', {
              type: 'success',
              center: false,
              dangerouslyUseHTMLString: true,
              callback: () => {
                this.closeCurrentPage();
                if (this.urls.list) {
                  this.$router.push({
                    path: this.urls.list,
                  })
                }
              }
            });
            resovle();
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    /**
     * afterSubmit 业务提交,工作流完成后回调
     */
    async afterSubmit (workflow) {
      let result
      try {
        if (this.$refs.funiFormEngineRef && this.$refs.funiFormEngineRef.afterSubmit) {
          result = await this.$refs.funiFormEngineRef.afterSubmit(workflow);
        }
      } catch (err) { console.log(err) }
      return result;
    },
    //获取工作流表达式执行后的值
    async getVar (context, page_id) {
      let businessVariable = {};
      const { modelId, mainModelId, busObj } = this;
      if (this.tempData && this.tempData.FunctionDynamicOrg) {
        businessVariable.FunctionDynamicOrg = this.tempData.FunctionDynamicOrg
      } else if (
        modelId &&
        modelId === mainModelId &&
        this.$refs.funiFormEngineRef &&
        this.$refs.funiFormEngineRef.getWorkflowVar
      ) {
        let workflowObj = this.$refs.funiFormEngineRef.getWorkflowVar(true) || {};
        if (!Array.isArray(workflowObj)) {
          businessVariable.FunctionDynamicOrg = workflowObj.workflowOrgId;
          this.updateTempData({ FunctionDynamicOrg: businessVariable.FunctionDynamicOrg })
        }
      }
      const busType = this.$route.query.busType ?? 'ADD';
      let businessConfigCode = busObj?.businessCode;
      let ruleRes;
      if (!businessConfigCode) {
        ruleRes = await this.$lowCodeRequest.fetchPageRulesAsync(page_id, busType); //先获取业务配置code
      }
      if (businessConfigCode || (ruleRes && ruleRes.list && ruleRes.list.length > 0)) {
        if (!businessConfigCode) {
          businessConfigCode = ruleRes.list[0].bus_code;
        }
        let sysId = this.appStore.system.id;
        let exRes = await this.$lowCodeRequest.postQueryExpressionVariableForEggAsync({ businessConfigCode, sysId });
        if (exRes && exRes.decodeExpressions && exRes.decodeExpressions.length > 0) {
          for (let i = 0; i < exRes.decodeExpressions.length; i++) {
            let v = exRes.decodeExpressions[i];
            if (v.decodeExpressionString) {
              let key = v.decodeExpressionId;
              let expObj = decodeFormula(v.decodeExpressionString);
              if (expObj.expression) {
                const fn = createEXpFun(expObj.expression);
                let val = await fn()(context);
                businessVariable[key] = val === true ? '1' : '0';
              }
            }
          }
        }
        return businessVariable;
      } else {
        return businessVariable;
      }
    },

    //获取变更业务时获取操作备注
    async getOperateRemark (currentFormData) {
      const busType = this.$route.query.busType;
      const { modelId, mainModelId, urls, fieldAuth } = this;
      let operateRemark = this.tempData.operateRemark;
      let lastOptionsLabel;
      if (modelId && modelId === mainModelId && ['CHANGE', 'CANCEL'].includes(busType)) {
        //主模型且变更业务
        if (this.$refs.funiFormEngineRef && this.$refs.funiFormEngineRef.getOperateRemarkData) {
          //兼容老页面,包含getOperateRemarkData这个方法才执行
          let url = urls.detailById;
          let param = { id: this.lastId, model_id: modelId };
          let res = await window.$http.post(url, param);
          let lastData = res || {};
          let remrkData = this.$refs.funiFormEngineRef.getOperateRemarkData();
          let selectOption = remrkData.selectOption;
          let labelProps = remrkData.labelProps;
          let keys = Object.keys(labelProps);
          let remarkKeys = (fieldAuth || []).filter(item => item.print).map(item => item.fieldName);
          if (currentFormData && lastData && keys && keys.length > 0) {
            let remarks = [];
            keys.forEach(key => {
              if (
                currentFormData[key] !== lastData[key] &&
                ['string', 'number', 'undefined'].includes(typeof currentFormData[key])
              ) {
                if (!lastOptionsLabel) {
                  lastOptionsLabel = {};
                }
                let lastVal = lastData[key];
                let currentVal = currentFormData[key];
                if (selectOption[key]) {
                  //翻译下拉
                  const lastValObj = selectOption[key].first.find(item => item.value === lastVal);
                  if (lastValObj) {
                    lastVal = lastValObj.label;
                    lastOptionsLabel[key] = lastVal;
                  } else if (lastOptionsLabel[key]) {
                    //如果当前没有找到选项则再看后端存的数据
                    lastVal = lastOptionsLabel[key];
                  }
                  const currentValObj = selectOption[key].current.find(item => item.value === currentVal);
                  if (currentValObj) {
                    currentVal = currentValObj.label;
                  }
                }
                if (remarkKeys && remarkKeys.length > 0) {
                  if (remarkKeys.includes(key)) {
                    remarks.push(
                      `${labelProps[key]}由"${lastVal ? lastVal : '空'}"变更为"${currentVal ? currentVal : '空'}"`
                    );
                  }
                } else {
                  remarks.push(
                    `${labelProps[key]}由"${lastVal ? lastVal : '空'}"变更为"${currentVal ? currentVal : '空'}"`
                  );
                }
              }
            });
            if (remarks && remarks.length > 0) {
              operateRemark = remarks.join(',');
              this.updateTempData({
                operateRemark
              });
            }
          }
        }
      }
      return lastOptionsLabel;
    },
    getWorkflowVar () {
      let workflowObj = {};
      const { modelId, mainModelId } = this;
      if (
        modelId &&
        modelId === mainModelId &&
        this.$refs.funiFormEngineRef &&
        this.$refs.funiFormEngineRef.getWorkflowVar
      ) {
        workflowObj = this.$refs.funiFormEngineRef.getWorkflowVar() || {};
      }
      return workflowObj;
    },
    store (submit = false) {
      return new Promise((resolve, reject) => {
        this.$refs.funiFormEngineRef.submitForm().then(res => {
          if (res.valid) {
            const { cid, businessId } = this.tempData;
            const { id, serialNumber, modelId, mainModelId, urls, saveExtendId } = this;
            let extendData = {};
            if (saveExtendId && this.getExtendParams) {
              extendData = this.getExtendParams(saveExtendId);
            }
            if (modelId === mainModelId) {
              let workflowObj = this.getWorkflowVar();
              if (Array.isArray(workflowObj) && workflowObj.length > 0) {
                let customAssigneeList = workflowObj;
                this.updateTempData({
                  customAssigneeList
                });
              } else if (workflowObj && (workflowObj.workflowUserId || workflowObj.workflowUserName)) {
                let customAssigneeList = [
                  {
                    assigneeType: 'U',
                    assigneeId: workflowObj.workflowUserId,
                    assigneeName: workflowObj.workflowUserName
                  }
                ];
                this.updateTempData({
                  customAssigneeList
                });
              }
            }
            let page_id, app_code;
            if (window.location && window.location.hash) {
              let hashAndQuery = window.location.hash.split('?');
              if (hashAndQuery && hashAndQuery.length > 0) {
                let [hash] = hashAndQuery;
                if (hash && hash.length > 0) {
                  let hashList = hash.split('/');
                  if (hashList && hashList.length >= 3) {
                    page_id = hashList[hashList.length - 1];
                    app_code = hashList[hashList.length - 2];
                  }
                }
              }
            }
            if (!modelId && urls.save && (urls.save.startsWith('/as/') || urls.save.startsWith('as/'))) {
              const bizName = this.$route.query.bizName;
              if (businessId && submit && !['修改'].includes(bizName)) {
                let mainInfo;
                if (this.tempData && this.tempData.mainInfo) {
                  mainInfo = this.tempData.mainInfo;
                }
                let customAssigneeList = this.tempData.customAssigneeList;
                let submitUrl = this.urls.executeBusiness
                  ? this.urls.executeBusiness
                  : `/as/${app_code}/executeBusiness`;
                window.$http
                  .post(submitUrl, { businessId, page_id, mainInfo, customAssigneeList })
                  .then(() => {
                    this.updateStepData(res.data)
                    resolve();
                  })
                  .catch(err => {
                    reject(err);
                  });
              } else {
                this.updateStepData(res.data)
                resolve();
              }
            } else {
              const param = { submit, app_code, page_id };
              if (id) {
                param.id = id;
              }
              if (cid) {
                param.cid = cid;
                param.businessId = businessId;
              }
              if (businessId) {
                param.businessId = businessId;
              }
              if (serialNumber) {
                param.serialNumber = serialNumber;
              }
              if (modelId) {
                param.model_id = modelId;
              }
              if (modelId && modelId === mainModelId) {
                let orginData = {};
                if (this.tempData && this.tempData.mainInfo && this.tempData.mainInfo.data) {
                  orginData = this.tempData.mainInfo.data;
                }
                this.updateTempData({
                  mainInfo: { model_id: mainModelId, data: { ...orginData, ...(res.data || {}), id } }
                });
              }
              let mainInfo;
              if (this.tempData && this.tempData.mainInfo) {
                mainInfo = this.tempData.mainInfo;
              }
              const data = { ...(res.data || {}) };
              const busType = this.$route.query.busType ?? 'ADD';
              param.bus_type = busType;
              if (modelId && modelId === mainModelId) {
                data.bus_type = busType;
                if (mainInfo.data) {
                  mainInfo.data.bus_type = busType;
                }
              }
              const deleteField = () => {
                //删除由后端维护的字段
                delete data.business_id;
                delete data.business_status;
                delete data.business_status_name;
                delete data.bus_doing;
                delete data.bus_doing_name;
                delete data.failure;
                delete data.failure_name;
                delete data.bus_type_name;
                delete data.create_time;
                delete data.creator_id;
                if (mainInfo && mainInfo.data) {
                  delete mainInfo.data.id;
                  delete mainInfo.data.cid;
                  delete mainInfo.data.business_id;
                  delete mainInfo.data.business_status;
                  delete mainInfo.data.business_status_name;
                  delete mainInfo.data.bus_doing;
                  delete mainInfo.data.bus_doing_name;
                  delete mainInfo.data.failure;
                  delete mainInfo.data.failure_name;
                  delete mainInfo.data.bus_type_name;
                  delete mainInfo.data.create_time;
                  delete mainInfo.data.creator_id;
                }
              };
              const bizName = this.$route.query.bizName;
              if (id) {
                data.id = id;
                if (['CHANGE', 'CANCEL'].includes(busType)) {
                  //变更注销
                  deleteField();
                  if (modelId && modelId === mainModelId) {
                    //主模型重置id
                    data.id = id;
                    param.id = id;
                    data.lastId = this.lastId;
                  }
                  data.cid = cid;
                  data.businessId = businessId;
                  param.cid = cid;
                  param.businessId = businessId;
                  if (mainInfo && mainInfo.data) {
                    if (modelId && modelId === mainModelId) {
                      //主模型重置id
                      mainInfo.data.id = id;
                      mainInfo.data.lastId = this.lastId;
                    }
                    mainInfo.data.cid = cid;
                    mainInfo.data.businessId = businessId;
                  }
                }
              } else if (this.serialNumber === '1' && ['CHANGE', 'CANCEL'].includes(busType) && bizName === '新建' && !this.isSave) {
                //第一个页签变更注销没有保存过的时候
                if (modelId && modelId === mainModelId) {
                  //主模型删除id添加字段
                  delete data.id;
                  delete data.cid;
                  delete data.businessId;
                  delete param.id;
                  delete param.cid;
                  delete param.businessId;
                  data.lastId = this.lastId;
                  deleteField();
                }
              }
              let context = {};
              if (mainInfo) {
                context = {
                  formData: mainInfo.data
                };
              } else {
                context = {
                  formData: res.data
                };
              }
              let extendParams = {
                serviceId: sessionStorage.getItem('s_id'),
                clientId: sessionStorage.getItem('c_id'),
                appCode: app_code,
                modelId,
                sysCode: 'as'
              };
              this.getOperateRemark({ ...(res.data || {}) }).then(lastOptionsLabel => {
                this.getVar(context, page_id).then(businessVariable => {
                  window.$http
                    .post(urls.save, { ...param, data, mainInfo, businessVariable, extendParams, extendData })
                    .then(async res1 => {
                      if (res1) {
                        this.isSave = true;
                        let newId = res1.id;
                        let newCid = res1.cid;
                        let newBusinessId = res1.businessId;
                        if (newId) {
                          this.id = newId;
                          this.updateTempData({ id: newId });
                        }
                        if (newCid) {
                          this.updateTempData({ cid: newCid });
                        }
                        if (newBusinessId) {
                          this.updateTempData({ businessId: newBusinessId });
                        }
                        this.updateStepData({ ...(res.data || {}), id: newId });
                        if (modelId && modelId === mainModelId) {
                          let orginData = {};
                          if (this.tempData && this.tempData.mainInfo && this.tempData.mainInfo.data) {
                            orginData = this.tempData.mainInfo.data;
                          }
                          this.updateTempData({
                            mainInfo: {
                              model_id: mainModelId,
                              data: { ...orginData, ...(data || {}), id: newId, cid: newCid }
                            }
                          });
                        }
                      }
                      const bizName = this.$route.query.bizName;
                      if (res1.businessId && submit && !['修改'].includes(bizName)) {
                        //修改不执行工作流
                        let customAssigneeList = this.tempData.customAssigneeList;
                        let submitUrl = this.urls.executeBusiness
                          ? this.urls.executeBusiness
                          : `/as/${app_code}/executeBusiness`;
                        let operateRemark = this.tempData.operateRemark;
                        let beforeSubmitSuccess = true;
                        if (this.$refs.funiFormEngineRef.beforeSubmit) {
                          try {
                            beforeSubmitSuccess = await this.$refs.funiFormEngineRef.beforeSubmit();
                          } catch (err) {
                            beforeSubmitSuccess = false;
                          }
                        }
                        if (beforeSubmitSuccess) {
                          window.$http
                            .post(submitUrl, {
                              businessId: res1.businessId,
                              page_id,
                              mainInfo,
                              customAssigneeList,
                              operateRemark
                            })
                            .then(() => {
                              resolve();
                            })
                            .catch(err1 => {
                              reject(err1);
                            });
                        } else {
                          reject();
                        }
                      } else {
                        let afterSaveSuccess = true;
                        let saveErr;
                        //如果存在保存后回调
                        if (this.$refs.funiFormEngineRef.afterSave) {
                          try {
                            afterSaveSuccess = await this.$refs.funiFormEngineRef.afterSave(res1.id);
                          } catch (err) {
                            afterSaveSuccess = false;
                            saveErr = err;
                            if (saveErr && saveErr.message) {
                              this.$notify.error(saveErr.message);
                            }
                          }
                        }
                        if (afterSaveSuccess) {
                          resolve();
                        } else {
                          reject(saveErr);
                        }
                      }
                    })
                    .catch(err => {
                      reject(err);
                    });
                });
              });
            }
          } else {
            let messages = [];
            if (res && res.errors) {
              for (let key in res.errors) {
                if (res.errors[key] && res.errors[key].length > 0) {
                  messages.push(...(res.errors[key].map(err => err.message) || []));
                }
              }
            }
            if (messages && messages.length > 0) {
              messages = [...new Set([...messages])];
            }
            if (messages.length > 0) {
              ElNotification({
                title: '提示',
                message: `${messages.join(',')}`,
                type: 'error'
              });
            }
            reject('请更正数据!');
          }
        });
      });
    },
    submitForm () {
      return new Promise((resolve, reject) => {
        this.$refs.funiFormEngineRef.submitForm().then(res => {
          if (res.valid) {
            resolve({ ...(res.data || {}), id: this.tempData.id, cid: this.tempData.cid });
          } else {
            let messages = [];
            if (res && res.errors) {
              for (let key in res.errors) {
                if (res.errors[key] && res.errors[key].length > 0) {
                  messages.push(...(res.errors[key].map(err => err.message) || []));
                }
              }
            }
            if (messages && messages.length > 0) {
              messages = [...new Set([...messages])];
            }
            if (messages.length > 0) {
              ElNotification({
                title: '提示',
                message: `${messages.join(',')}`,
                type: 'error'
              });
            }
            reject('请更正数据!');
          }
        });
      });
    },
    getOperateRemarkData () {
      let operateRemarkData = {};
      if (this.$refs.funiFormEngineRef && this.$refs.funiFormEngineRef.getOperateRemarkData) {
        //兼容老页面,包含getOperateRemarkData这个方法才执行
        operateRemarkData = this.$refs.funiFormEngineRef.getOperateRemarkData();
      }
      return operateRemarkData;
    },
    exeChildMethod (refName, methodName, params) {
      if (this.$refs['funiFormEngineRef'] && this.$refs['funiFormEngineRef']['exeChildMethod']) {
        this.$refs['funiFormEngineRef']['exeChildMethod'](refName, methodName, params);
      } else {
        return Promise.reject('未找到方法');
      }
    }
  },
  render (ctx) {
    if (ctx.code && ctx.isInit) {
      let jsCode = lowcode.decrypt(ctx.code);
      let component = generateComponent(jsCode);
      return h(component, {
        ref: 'funiFormEngineRef',
        ...ctx.attrs,
        disabled: !ctx.editable,
        style: { margin: '0px 8px' }
      });
    } else {
      return h('span', '');
    }
  }
});
</script>
