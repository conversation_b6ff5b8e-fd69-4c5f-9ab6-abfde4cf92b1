<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-02 15:27:24
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2025-02-11 16:52:45
 * @FilePath: \src\components\FuniFormEngine\components\sfc-upload-dialog\sfc-upload-inner-dialog.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-config-provider :locale="locale">
    <el-dialog v-if="dialogVisible"
      v-model="dialogVisible"
      title="文件上传"
      width="30%"
      @close="handleClose">
      <el-upload ref="elUploadRef"
        class="upload-demo"
        v-bind="uploadOptions"
        v-model:file-list="fileList"
        :http-request="handleHttpRequest"
        :on-success="handleSuccess"
        :on-error="handleError">
        <div v-if="uploadOptions && uploadOptions.accept">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            拖动文件或点击上传 <el-button type="primary"
              link
              @click="downloadTemplate">下载模板</el-button>
          </div>
        </div>
        <template #tip>
          <div v-if="uploadOptions && uploadOptions.accept"
            class="el-upload__tip">
            请上传{{ uploadOptions.accept }}文件
          </div>
        </template>
      </el-upload>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary"
          @click="handleOk"> 确定 </el-button>
      </template>
    </el-dialog>
  </el-config-provider>
</template>

<script>
import { ElDialog, ElUpload, ElButton, ElIcon, ElLoading, ElNotification } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import request from '../../common/request';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
export default {
  name: 'sfc-upload-inner-dialog',
  components: {
    ElDialog,
    ElButton,
    ElUpload,
    ElIcon,
    UploadFilled
  },
  props: {
    modelValue: {
      type: Boolean,
      default: true
    },
    uploadOptions: {
      type: Object,
      default: () => {
        return {};
      }
    },
    ids: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      locale: zhCn,
      fileList: [],
      dialogVisible: true
    };
  },
  methods: {
    handleClose () {
      this.$emit('close');
    },
    handleOk () {
      this.$refs.elUploadRef.submit();
    },
    handleHttpRequest (options) {
      const loading = ElLoading.service({ fullscreen: true });
      let formData = new FormData();
      formData.append('file', options.file);
      if (options.data) {
        for (let key in options.data) {
          formData.append(key, options.data[key]);
        }
      }
      return new Promise((resolve, reject) => {
        window.$http.upload2(options.action, formData)
          .then((res) => {
            loading.close();
            resolve(res);
          })
          .catch((err) => {
            loading.close();
            reject(err);
          })
      })
    },
    handleSuccess (response) {
      if (response && [0, 200].includes(response.status)) {
        this.dialogVisible = false;
        ElNotification({ title: '成功', message: response?.message ?? '导入成功', type: 'success' });
        this.$emit('success');
      } else {
        this.fileList.forEach(item => {
          item.status = 'ready';
        });
        ElNotification({ title: '失败', message: response?.message ?? '导入失败', type: 'error' });
      }
    },
    handleError (e) {
      this.$emit('error', e);
    },
    downloadTemplate (e) {
      const { ids, uploadOptions } = this;
      if (uploadOptions && uploadOptions.downloadUrl) {
        const loading = ElLoading.service({ fullscreen: true });
        window.$http
          .downloadFile(uploadOptions.downloadUrl, {})
          .then(res => {
            loading.close();
            ElNotification({ title: '成功', message: '下载成功', type: 'success' });
          })
          .catch(() => {
            loading.close();
            ElNotification({ title: '失败', message: '下载失败', type: 'error' });
          });
        e.stopPropagation();
      } else {
        const { app_code, ...other } = ids;
        const loading = ElLoading.service({ fullscreen: true });
        request.downloadExportListAsync(ids.app_code, {
          ...other,
          template: true
        })
          .then(res => {
            loading.close();
            ElNotification({ title: '成功', message: '下载成功', type: 'success' });
          })
          .catch(() => {
            loading.close();
            ElNotification({ title: '失败', message: '下载失败', type: 'error' });
          });
        e.stopPropagation();
      }
    }
  }
};
</script>
