/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-04 14:44:29
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-09-05 13:58:52
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\components\sfc-upload-dialog\service.js
 * @Description:上传要件服务
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { createApp, h, defineComponent } from 'vue';
import SfcUploadInnerDialog from './sfc-upload-inner-dialog.vue';
export function showSfcUpload(ids = {}, options = {}) {
  const { onError, onSuccess, ...otherOptions } = options || {};
  let uploadOptions = {
    drag: true,
    accept: '.xls,.xlsx',
    limit: 1,
    multiple: false,
    autoUpload: false
  };
  if (!otherOptions.headers) {
    otherOptions.headers = {};
  }
  if (!Object.keys(otherOptions.headers).includes('X-FuniPaas-Authorization')) {
    otherOptions.headers['X-FuniPaas-Authorization'] = sessionStorage.getItem('token');
  }
  Object.assign(uploadOptions, otherOptions);
  const div = document.createElement('div');
  document.body.appendChild(div);
  let uploadInstance;
  const StaticUploadDialog = defineComponent({
    render() {
      return h(SfcUploadInnerDialog, {
        modelValue: true,
        ids,
        uploadOptions,
        onError,
        onSuccess
      });
    }
  });
  uploadInstance = createApp(StaticUploadDialog);
  uploadInstance.mount(div);
}
