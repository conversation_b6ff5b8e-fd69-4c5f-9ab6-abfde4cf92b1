<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-01-17 17:25:06
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-31 17:42:21
 * @FilePath: \src\components\FuniFormEngine\components\sfc-funi-operation-log\index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div style="width:100%;">
    <funi-operation-log :params="params"
      v-bind="$attrs" />
  </div>
</template>
<script setup>
import { useAppStore } from '@/stores/useAppStore';
import { useRoute } from 'vue-router';
import { reactive, ref, inject } from 'vue';
const appStore = useAppStore();
const route = useRoute();
const tempData = inject('tempData') || {};

let businessId = tempData?.businessId;
if (!businessId && route && route.query && route.query.businessId) {
  businessId = route.query.businessId;
}

const params = reactive({
  businessId,
  sysId: appStore.system.id
})
</script>
<style lang='scss' scoped>
</style>