/*
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-11-26 13:45:44
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-11-26 16:45:54
 * @FilePath: \src\components\FuniFormEngine\components\sfc-dialog\service.js
 * @Description:
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved.
 */
import { createVNode, h, defineComponent, render } from 'vue';
import SfcDialog from './index.vue';

export function createCustomDialog(app) {
  return function (attrs = {}) {
    const div = document.createElement('div');
    document.body.appendChild(div);
    let uploadInstance = createVNode(SfcDialog, { defaultVisible: true, ...(attrs || {}) });
    uploadInstance.appContext = app._context;
    render(uploadInstance, div);
  };
}
