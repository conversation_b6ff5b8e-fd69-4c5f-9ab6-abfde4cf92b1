<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-06-01 11:03:13
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-12-04 11:58:25
 * @FilePath: \src\components\FuniFormEngine\components\sfc-dialog\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-dialog v-if="dialogVisibe"
      v-bind="$attrs"
      v-model="dialogVisibe">
      <template #default>
        <component v-if="contentRender"
          :is="contentVnode" />
        <div v-else
          style="margin:0px 16px;">
          <slot></slot>
        </div>
      </template>
      <template #footer>
        <div v-if="buttons&&buttons.length>0">
          <component v-for="(btn,index) in buttonsComputed"
            :is="btn.component"
            :key="index"
            :style="{marginLeft:'8px'}"
            v-bind="btn.attrs" />
        </div>
        <div v-if="okVisible||cancelVisible">
          <el-button v-if="cancelVisible"
            @click="handleCancle">{{ cancelLabel }}</el-button>
          <el-button type="primary"
            @click="handleOk">
            {{ okLabel }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { resolveComponent, h, computed, ref, getCurrentInstance } from 'vue';
export default {
  name: 'sfc-dialog',
  props: {
    buttons: {
      type: Array,
      default: () => {
        return []
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    defaultVisible: {
      type: Boolean,
      default: false
    },
    contentRender: {
      type: Function
    },
    okLabel: {
      type: String,
      default: '确定'
    },
    okVisible: {
      type: Boolean,
      default: false
    },
    cancelLabel: {
      type: String,
      default: '取消'
    },
    cancelVisible: {
      type: Boolean,
      default: false
    }
  },
  setup (props) {
    const instance = getCurrentInstance();
    const dialogVisibe = ref(props.defaultVisible);
    const buttonsComputed = computed(() => {
      const buttons = props.buttons || [];
      let realActions = [];
      if (buttons && buttons.length > 0) {
        buttons.forEach(btn => {
          let attrs = {};
          if (typeof (btn.attrs) === 'string') {
            try {
              attrs = new Function(`return ${btn.attrs.replaceAll('@', '')}`)();
            } catch (ex) { console.log(ex); }
          } else {
            attrs = btn.attrs;
          }
          if (btn.showIcon === 'noIcon') {
            delete btn.icon;
            delete btn.iconPosition;
          }
          if (btn.showIcon === 'onlyIcon') {
            delete btn.label;
          }
          const button = h(resolveComponent('sfc-button'), {
            type: btn.type,
            componentType: btn.componentType,
            icon: btn.icon,
            iconPosition: btn.iconPosition,
            label: btn.label,
            size: btn.size,
            ...attrs
          });
          realActions.push({ component: button });
        })
      }
      return props.disabled ? [] : realActions;
    })

    const show = () => {
      dialogVisibe.value = true;
    }

    const close = () => {
      dialogVisibe.value = false;
    }

    const handleCancle = () => {
      dialogVisibe.value = false;
      instance.proxy.$emit('cancel');
    }

    const handleOk = () => {
      instance.proxy.$emit('ok', () => {
        dialogVisibe.value = false;
      });
    }
    let contentVnode = ref(null);
    if (props.contentRender) {
      contentVnode.value = props.contentRender({ show, close });
    }
    return {
      dialogVisibe,
      buttonsComputed,
      handleCancle,
      handleOk,
      show,
      close,
      contentVnode
    }
  }
}
</script>
<style lang='scss' scoped>
</style>