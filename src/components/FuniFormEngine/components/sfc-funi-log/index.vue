<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-03-04 14:48:43
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-21 15:34:24
 * @FilePath: \src\components\FuniFormEngine\components\sfc-funi-log\index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="sfc-funi-log">
    <funi-group-title v-if="title"
      :title="title"
      groupMargin="0px 0px" />
    <funi-log v-bind="$attrs"
      :operationType="id" />
  </div>
</template>
<script setup>
import { useRoute } from 'vue-router';
import { inject } from 'vue';
const route = useRoute();
const tempData = inject('tempData') || {};
const id = tempData?.id;

const props = defineProps({
  title: [String]
})

</script>
<style lang='scss' scoped>
</style>
