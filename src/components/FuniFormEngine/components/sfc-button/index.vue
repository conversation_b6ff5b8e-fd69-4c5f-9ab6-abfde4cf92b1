<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-19 10:27:47
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-10-08 10:29:21
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniFormEngine\components\sfc-button\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-button v-bind="$attrs"
    v-auth="auth"
    :link="componentType==='link'"
    :type="type"
    :size="size"
    :plain="plain">
    <funi-icon v-if="(iconPosition==='left'||!iconPosition)&&icon"
      :icon="icon"
      style="margin-right:4px;" />
    {{ label }}
    <funi-icon v-if="iconPosition==='right'&&icon"
      :icon="icon"
      style="margin-left:4px;" />
  </el-button>
</template>
<script setup>
const props = defineProps({
  label: [String],
  type: {
    type: String,
    default: 'default'
  },
  size: {
    type: String,
    default: 'default'
  },
  plain: {
    type: Boolean,
    default: false
  },
  icon: [String],
  iconPosition: [String],
  auth: [String],
  componentType: {
    type: String,
    default: 'button'
  }
})
</script>
<style lang='scss' scoped>
</style>