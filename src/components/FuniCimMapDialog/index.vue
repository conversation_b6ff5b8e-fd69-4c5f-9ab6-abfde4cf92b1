<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-06-07 09:52:07
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-06-12 17:08:14
 * @FilePath: \funi-cloud-cddasp-ui\src\components\FuniCimMapDialog\index.vue
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-dialog :title="title"
    :size="size"
    destroy-on-close
    v-model="visible"
    v-bind="$attrs">
    <div id="cim-map"
      v-if="visible"
      style="height:75vh">
    </div>
    <template #footer>
      <slot name="footer"
        v-bind="params || {}">
        <div class="funi-dialog__footer">
          <el-button @click="handleCancel"> {{cancelText}} </el-button>
          <el-button type="primary"
            @click="handleConfirm"> {{confirmText}} </el-button>
        </div>
      </slot>
    </template>
  </funi-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { nextTick, reactive, ref } from 'vue';
defineOptions({
  name: 'FuniCimMapDialog',
  inheritAttrs: false
});
const emit = defineEmits('cancel', 'confirm');

const props = defineProps({
  title: {
    type: String,
    default: '地图服务'
  },
  size: {
    type: String,
    default: 'max'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  confirmText: {
    type: String,
    default: '确定'
  }
})

const visible = ref(false);
const id = ref('');

let mapData = ref(null);

const show = () => {
  mapData.value = null;
  id.value = window.$utils.guid();
  visible.value = true;

  nextTick(() => {
    const mapInit = () => {
      funiArcgisMapSdk.draw.init('cim-map');
      funiArcgisMapSdk.draw.onGetData(res => {
        mapData.value = res;
      });
      /**
       * 示例代码
      funiArcgisMapSdk.init('domId');
      funiArcgisMapSdk.setFeature({
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [104.07382965087902, 30.652885437011697]
            }
          }
        ]
      });
      funiArcgisMapSdk.draw.setData({
        type: "polygon",//面：polygon
        latlng: [
          [30.52368166856466, 104.2465209541843],
          [30.52368166856466, 103.8317870674656],
          [30.86700442247091, 103.9581298409031],
        ]
      })
      */
    };
    let mapSdkPath = process.env.NODE_ENV === 'production' ? 'js/funiArcgisMapSdk.umd.js' : '/js/funiArcgisMapSdk.umd.js';
    var mapcript = document.querySelector(`script[src='${mapSdkPath}']`);
    if (!mapcript) {
      const script = document.createElement('script');
      script.src = mapSdkPath;
      script.onload = () => {
        mapInit();
      };
      document.head.appendChild(script);
    } else {
      mapInit();
    }
  })
}

const handleConfirm = () => {
  visible.value = false;
  console.log('---------------mapData', mapData.value);
  emit('confirm', mapData.value);
}

const handleCancel = () => {
  visible.value = false;
  emit('cancel');
}

defineExpose({ show });
</script>
<style lang='scss' scoped>
</style>