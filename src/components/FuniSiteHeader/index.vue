<template>
    <div class="header_box">
        <div class="nav_list">
            <div v-for="item in props.navList" :key="item.label" class="item" :class="{ active: active == item.label }">
                {{ item.label }}
            </div>
        </div>
        <!-- <div>返回</div> -->
    </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
    navList: {
        type: Array,
        default: () => [
            {
                label: '用户登录'
            }
        ]
    }
});
let active = ref();
</script>

<style lang="scss" scoped>
.header_box {
    display: flex;
    justify-content: space-between;
    padding: 10px 0px 5px;
    width: 1000px;
    margin: 0 auto;
    font-size: 14px;

    .nav_list {
        display: flex;

        .item {
            border-bottom: 2px solid #ff4208;
            padding: 0 15px 10px;
        }

        .active {
            border-bottom: 2px solid #ff4208;
            font-size: bold;
        }
    }
}
</style>
