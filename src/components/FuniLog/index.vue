<!--
 * @Author: <EMAIL>
 * @Date: 2024-02-26 17:05:28
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-02-26 17:05:28
 * @Description: 操作日志
 * @FilePath: /funi-bpaas-as-ui/src/apps/as/components/operationLog/index.vue
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div>
    <funi-curd :isShowSearch="false" ref="refCurd" :loading="loading" :columns="data.columns" :lodaData="loadData"/>
  </div>
</template>

<script setup lang="jsx">
import {reactive, ref} from 'vue';

const listApi = "/csccs/log/list";
const loading = ref(false)

const props = defineProps({
  //接口url
  api: {
    type: String,
    default: "",
  },
  //对象
  params: {
    type: Object,
    default: () => {
    }
  },
  //操作类型
  operationType: {
    type: String,
    default: "",
  },
  //操作纬度
  operationLatitude: {
    type: String,
    default: ""
  },
  //系统编码
  systemCode: {
    type: String,
    default: ""
  },
  //日志内容
  content: {
    type: String,
    default: ""
  },
  //操作人id/姓名
  userIdOrName: {
    type: String,
    default: ""
  },
  //操作时间范围
  startTime: {
    type: String,
    default: ""
  },
  //操作时间范围
  endTime: {
    type: String,
    default: ""
  },
  //ip地址
  ipAddress: {
    type: String,
    default: ""
  },
});

const data = reactive({
  columns: [
    {
      label: "操作类型",
      prop: "operation",
    },
    {
      label: "日志内容",
      prop: "arguments",
      width: 500,
    },
    {
      label: "操作人ID",
      prop: "userId",
    },
    {
      label: "操作人用户名",
      prop: "userName",
    },
    {
      label: "操作日期",
      prop: "operationTime",
    },
    {
      label: "执行结果",
      prop: "success",
      render: ({row, index}) => {
        return row.success ? "成功" : "失败";
      }
    },
  ],
});

const loadData = async (page, params) => {
  let queryParam = {...params, ...props.params};
  if (props.operationType) {
    queryParam.operationType = props.operationType;
  }
  if (props.operationLatitude) {
    queryParam.operationLatitude = props.operationLatitude;
  }
  if (props.systemCode) {
    queryParam.systemCode = props.systemCode;
  }
  if (props.content) {
    queryParam.content = props.content;
  }
  if (props.userIdOrName) {
    queryParam.userIdOrName = props.userIdOrName;
  }
  if (props.startTime) {
    queryParam.startTime = props.startTime;
  }
  if (props.endTime) {
    queryParam.endTime = props.endTime;
  }
  if (props.ipAddress) {
    queryParam.ipAddress = props.ipAddress;
  }

  loading.value = true;
  const resData = await $http.post(props.api || listApi, {
    pageNo: page.pageIndex,
    pageSize: page.pageSize,
    ...queryParam
  });
  loading.value = false;
  return resData;
};

</script>

<style lang="less" scoped>
</style>
