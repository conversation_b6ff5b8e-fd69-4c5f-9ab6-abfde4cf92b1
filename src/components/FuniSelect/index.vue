<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-02-21 17:43:33
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-01-30 18:53:36
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniSelect\index.vue
 * @Description: 下拉框
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-select v-bind="$attrs"
    v-model="localVal"
    :remoteMethod="handleRemoteMethod"
    @change="handleChange">
    <el-option v-for="item in localOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value" />
  </el-select>
</template>
<script setup>
import { ref, watchEffect, onMounted, shallowRef } from 'vue';
const emit = defineEmits(['update:modelValue', 'change']);
const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean, Array, Object],
    default: ''
  },
  /**
   * 请求地址
   */
  url: {
    type: String,
    default: '/csops/dic/findDicByType'
  },
  /**
   * 字典码
   */
  typeCode: {
    type: String,
    default: ''
  },
  /**
   * 是否从本地缓存获取字典
   */
  isLocal: {
    type: Boolean,
    default: false
  },
  /**
   * 本地缓存的key
   */
  storageKey: {
    type: String,
    default: 'enumSource'
  },
  options: {
    type: Array,
    default: () => {
      return new Array();
    }
  },
  //action要求返回promise 结果结构为[{label:'',value:''}]
  action: {
    type: Function
  }
})
const localVal = ref('');
const localOptions = shallowRef([]);

onMounted(() => {
  const { typeCode, isLocal, storageKey, action } = props;
  if (typeCode) {
    if (isLocal) {
      const enumSourceStr = localStorage.getItem(storageKey);
      if (enumSourceStr) {
        let enumSource = [];
        try {
          enumSource = JSON.parse(enumSourceStr);
        } catch { }
        if (enumSource && enumSource.length >= 0) {
          let fIndex = enumSource.findIndex(item => item.code === typeCode);
          if (fIndex >= 0 && enumSource[fIndex].dicResponses && enumSource[fIndex].dicResponses.length > 0) {
            localOptions.value = enumSource[fIndex].dicResponses.map(item => {
              return {
                label: item.name ?? item.label,
                value: item.code ?? item.value
              }
            })
          }
        }
      }
    } else {
      window.$http.post(props.url, { typeCode })
        .then(res => {
          if (res && res.list && res.list.length > 0) {
            localOptions.value = res.list.map(item => {
              return {
                label: item.name,
                value: item.code
              }
            })
          }
        })
    }
  } else if (action) {
    action('')
      .then(list => {
        localOptions.value = [...list];
      })
  }
})

watchEffect(() => {
  // if (props.modelValue) {
  localVal.value = props.modelValue;
  // }
})

watchEffect(() => {
  const { typeCode } = props;
  if (!typeCode) {
    if (props.options && props.options.length !== 0) {
      localOptions.value = props.options;
    }
  }
})

function handleRemoteMethod (query) {
  const { action } = props;
  if (action) {
    action(query)
      .then(list => {
        localOptions.value = [...list];
      })
  }
}

function handleChange (val) {
  emit('change', val);
  emit('update:modelValue', val);
  if (localOptions.value && localOptions.value.length > 0) {
    let fIndex = localOptions.value.findIndex(item => item.value === val);
    if (fIndex >= 0) {
      emit('change-item', localOptions.value[fIndex]);
    }
  }
}
</script>
<style lang='scss' scoped>
</style>