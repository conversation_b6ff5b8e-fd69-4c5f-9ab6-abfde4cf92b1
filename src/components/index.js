import { cellRender } from './FuniSearchFormV3/cell-render';
import { defineAsyncComponent } from 'vue';

export default {
  install(app, options) {
    const modules = import.meta.glob(['./*/index.vue'], {
      eager: true,
      import: 'default'
    });

    for (const path in modules) {
      const module = modules[path];
      if (module) {
        const moduleName = module.name || path.replace(/\.\/(.*)\/index\.vue/g, '$1');
        app.component(moduleName, module);
      }
    }

    app.config.globalProperties.$searchFormCellRender = cellRender;
  }
};
