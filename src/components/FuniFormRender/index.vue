<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-06 11:19:03
 * @LastEditors: 云下鱼 <EMAIL>
 * @LastEditTime: 2023-07-03 17:39:29
 * @FilePath: \funi-paas-csccs-ui\src\components\FuniFormRender\index.vue
 * @Description: 表单渲染组件
-->
<script setup>
import { ref, h, reactive, useAttrs } from 'vue';
import { Base64 } from 'js-base64';
import lowCode from '../FuniFormEngine/common/utils/lowcode.js';
const props = defineProps({
  code: {
    type: String
  }
});
let jsCode = Base64.decode(window.$utils.SM.sm4.decrypt(props.code, 'c8502bd294000ef49777ec31c54e6ebb'));
const childRef = ref();
const ChildComponent = lowCode.jsToComponent(jsCode);
const attrs = useAttrs();
const render = h(ChildComponent, { ...attrs });
const submitForm = () => {
  return childRef.value.submitForm();
};
const resetForm = () => {
  childRef.value.resetForm();
};

const setFormData = (data, attach = true) => {
  childRef.value.setFormData(data, attach);
};

const getForm = () => {
  return childRef.value;
};

const setFormDisabled = disabled => {
  childRef.value.setFormDisabled(disabled);
};

defineExpose({ submitForm, resetForm, setFormData, setFormDisabled });
</script>
<template>
  <div class="preview-render">
    <render ref="childRef" />
  </div>
</template>
<style lang="less" scoped>
.preview-render {
  overflow: auto;
}
</style>
