<template>
  <svg :style="iconStyle" aria-hidden="true">
    <use :xlink:href="symbolId" />
  </svg>
</template>

<script setup>
import { computed } from 'vue';

defineOptions({ name: 'FuniSvg' });

const props = defineProps({
  name: { type: String, required: true },
  prefix: { type: String, default: 'icon' },
  iconStyle: { type: Object, default: () => ({ width: '16px', height: '16px' }) }
});

const symbolId = computed(() => `#${props.prefix}-${props.name}`);
</script>
