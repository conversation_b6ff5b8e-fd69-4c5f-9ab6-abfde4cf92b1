gantt.config = {
	auto_scheduling_move_projects: '定义是否将整个项目移动（请参阅下面的详细信息）',
	preserve_scroll: '在重新绘制甘特图时保留垂直和水平滚动的当前位置',
	auto_scheduling_descendant_links: '允许或禁止创建从父任务（项目）到子任务的链接',
	task_date: '设置灯箱“时间段”部分中日期标签的格式',
	resource_render_empty_cells: '告诉资源时间线渲染元素并为未分配的单元格调用模板',
	tooltip_offset_x: '设置工具提示位置的右侧（如果为正）偏移',
	drag_links: '允许通过拖放创建依赖链接',
	root_id: '设置虚拟根元素的 id',
	readonly: '激活甘特图的只读模式',
	drag_multiple: '可以一次拖动多个选定的任务',
	scale_unit: '设置时间刻度的单位（X 轴）',
	task_scroll_offset: '设置距离时间轴左边框最近的任务的偏移量（以像素为单位）',
	fit_tasks: '甘特图自动扩展时间尺度以适应所有显示的任务',
	editor_types: '包含内联编辑器定义的对象',
	touch: '启用 / 禁用对甘特图的触摸支持',
	datastor: '一组数据存储方法',
	start_date: '设置时间刻度的起始值',
	details_on_create: '在通过单击“+”按钮创建新事件时打开灯箱',
	time_picker: '设置灯箱中时间下拉选择器的格式',
	layer_attribute: '设置任务层的 DOM 元素的属性名称',
	resource_attribute: '更改甘特图用来查找资源网格/时间线中的任务行所指的资源的属性的名称',
	min_column_width: '设置时间轴区域中列的最小宽度',
	version: '返回 dhtmlxGantt 的版本',
	quick_info_detached: '定义任务表单是从屏幕的左侧/右侧出现还是在所选任务附近出现',
	row_height: '设置表格行的默认高度',
	auto_scheduling_compatibility: '禁用任务的时间限制',
	keep_grid_width: '在调整列大小时保留初始网格的宽度',
	editable_property: '更改影响只读甘特图中任务/链接编辑能力的属性名称',
	lightbox: '指定灯箱对象',
	project_end: '指定项目的结束日期',
	touch_feedback: '在触摸设备上拖放之前/之后返回振动反馈',
	start_on_monday: '设置一周的开始日期',
	undo_actions: '设置撤消操作将恢复的操作',
	order_branch: '激活“分支”模式，允许在同一树级别内垂直重新排序任务',
	link_attribute: '设置将指定链接的 HTML 元素的 id 的属性的名称',
	link_line_width: '设置时间线区域中依赖链接的宽度',
	task_height: '设置时间线区域中任务栏的高度',
	rtl: '将甘特图切换到从右到左模式',
	tooltip_hide_timeout: '设置工具提示隐藏之前的时间长度，以毫秒为单位',
	container_resize_timeout: '指定在调整容器大小时重绘甘特图之前的延迟（以毫秒为单位）',
	utils: '各种辅助模块',
	scales: '定义时间刻度的配置设置',
	show_grid: '显示甘特图的网格区域',
	placeholder_task: '在任务列表的末尾添加一个空行以简化通过键盘编辑任务',
	open_tree_initially: '最初打开所有分支',
	drag_resize: '可以通过拖放来调整任务大小',
	env: '一组描述当前环境的标志',
	reorder_grid_columns: '可以通过拖放重新排列网格列 - 左侧树',
	smart_rendering: '为甘特图的任务和链接渲染启用智能渲染模式',
	keyboard_navigation_cells: '启用按单元格的键盘导航',
	resize_rows: '启用通过拖放调整行高的能力',
	sort: '启用表中的排序',
	drag_move: '允许通过拖放移动任务',
	drag_project: '允许拖放项目类型的项目',
	work_time: '可以在工作时间而不是日历时间计算任务的持续时间',
	button_left: '存储位于灯箱左下角的按钮集合',
	end_date: '设置时间刻度的结束值',
	constraint_types: '包含了所有可用的约束类型',
	branch_loading: '在甘特图中启用动态加载',
	auto_scheduling: '启用自动调度',
	task_grid_row_resizer_attribute: '',
	grid_resizer_attribute: '设置网格调整器的 DOM 元素的属性名称',
	min_grid_column_width: '在调整大小时设置网格的最小宽度（以像素为单位）',
	date_scale: '设置时间刻度的格式（X 轴）',
	auto_scheduling_strict: '启用自动调度模式，在这种模式下，任务将始终被重新安排到最早的日期',
	config: '定义日期、比例、控件的配置选项',
	deepcopy_on_parse: '定义甘特图是否对传递给 gantt.parse() 方法的数据对象执行深度复制',
	layout: '指定布局对象',
	quickinfo_buttons: '存储驻留在弹出任务的详细信息表单中的按钮集合',
	details_on_dblclick: '双击任务后打开灯箱',
	step: '设置时间刻度（X 轴）的步长',
	autoscroll: '在将任务或链接拖出当前浏览器屏幕时启用自动滚动',
	lightbox_additional_height: '增加灯箱的高度',
	project_start: '指定项目的开始日期',
	scroll_on_click: '指定在选择显示所选任务时是否滚动时间线区域',
	quickinfo_icons: '重新定义甘特图按钮的默认点击行为',
	calendar: '工作日历对象的接口',
	grid_width: '设置网格的宽度',
	date: '一组日期格式化方法',
	i18n: '一组用于甘特图本地化的方法',
	show_task_cells: '启用/禁用在(任务)图表区域中显示列边框',
	process_resource_assignments: '启用/禁用资源分配的解析',
	resource_property: '定义任务对象的属性，该对象存储与 resourceGrid/Timeline/Histogram/Calendar 关联的资源 ID',
	resource_assignment_store: '指定存储资源分配的数据存储的名称',
	date_format: '设置用于解析数据集中的数据并将日期发送回服务器的日期格式',
	container_resize_method: '定义甘特图是否应以时间间隔跟踪容器的大小调整',
	drag_lightbox: '可以通过标题拖动灯箱',
	show_chart: '显示甘特图的图表（时间线）区域',
	drag_mode: '存储可用拖放模式的类型',
	subscales: '指定第二个时间尺度（已弃用）',
	oldxml: '指定 dhtmlxGantt 1.0 的 XML 格式的序列化和解析',
	show_tasks_outside_timescale: '启用在甘特图中显示指定日期范围之外的任务',
	order_branch_free: '激活“分支”模式，允许在整个甘特图中重新排序任务',
	xml_date: '定义用于从数据集中解析数据并将数据发送到服务器的日期格式',
	autosize_min_width: '设置甘特图在水平“自动调整大小”模式下可以采用的最小宽度（以像素为单位）',
	button_right: '存储位于灯箱右下角的按钮集合',
	date_grid: '设置表格“开始时间”列中的日期格式',
	show_quick_info: '激活/禁用“quick_info”扩展（弹出任务的详细信息表单）',
	schedule_from_end: '启用反向调度',
	skins: '返回可用皮肤的对象',
	constants: '存储各种常量以减少代码中幻数的使用',
	multiselect: '在甘特图中启用/禁用多任务选择',
	redo: '启用甘特图的重做功能',
	duration_unit: '设置持续时间单位',
	touch_feedback_duration: '定义在触摸设备上拖放之前/之后振动反馈的持续时间（以毫秒为单位）',
	drag_timeline: '配置 drag_timeline 扩展的行为',
	drag_progress: '可以通过拖动进度旋钮来更改任务进度',
	horizontal_scroll_key: '通过 Shift|Alt|Meta 键 + 鼠标滚轮移动启用/禁用水平滚动',
	static_background_cells: '在 static_background 模式下启用突出显示的单元格的渲染',
	click_drag: '启用高级拖放',
	auto_scheduling_initial: '定义甘特图是否对数据加载/解析进行自动调度',
	grid_elastic_columns: '调整可滚动网格内列的宽度',
	autosize: '强制甘特图自动更改其大小以显示所有任务而无需滚动',
	bar_height: '设置时间线区域中任务栏的高度',
	ajax: '甘特图 ajax 模块',
	branch_loading_property: '指定任务有尚未从后端加载的子任务',
	templates: '定义甘特图中日期、标题、工具提示的格式模板',
	touch_drag: '定义用于区分长触摸手势和滚动手势的时间段（以毫秒为单位）',
	autoscroll_speed: '定义将任务或链接拖出当前浏览器屏幕时自动滚动的速度（以毫秒为单位）',
	show_errors: '在出现意外行为时启用显示错误警报',
	license: '返回 dhtmlxGantt 的许可证名称',
	calendar_property: '更改影响日历绑定到任务/任务组的属性的名称',
	prevent_default_scroll: '指定甘特容器是否应该阻止鼠标滚轮事件，或者应该将其传播到窗口元素',
	tooltip_offset_y: '设置工具提示位置的顶部（如果为正）偏移',
	show_links: '启用/禁用在甘特图中显示链接',
	inherit_scale_class: '指定子尺度是否默认使用 scale_cell_class 模板',
	scroll_size: '设置垂直（宽度）和水平（高度）滚动的大小',
	inline_editors_date_processing: '在编辑任务的开始/结束期间保持任务的持续时间不变',
	treeDatastore: '一组 treeDatastore 方法',
	select_task: '启用甘特图中的任务选择',
	xml: '指定 XML 序列化和解析',
	smart_scales: '指定仅在屏幕上呈现时间刻度的可见部分',
	wai_aria_attributes: '启用 WA:ARIA 支持以使屏幕阅读器可识别组件',
	wheel_scroll_sensitive: '指定鼠标滚轮滚动甘特图的速度',
	resource_calendars: '定义一组可以分配给特定资源（例如用户）的工作日历',
	external_render: '将外部组件渲染到 DOM 中',
	readonly_property: '更改影响任务/链接只读行为的属性的名称',
	duration_step: '设置与“duration”数据属性的一个单位对应的“gantt.config.duration_unit”单位数。',
	tooltip_timeout: '在显示任务的工具提示之前以毫秒为单位设置超时',
	show_markers: '在页面上显示/隐藏标记',
	scale_offset_minimal: '将最小比例单位（如果使用多个比例）设置为前导/关闭空白空间的间隔',
	ext: '一个存储各种扩展的对象',
	min_duration: '设置可以在调整大小期间为任务设置的最短持续时间（以毫秒为单位）。',
	keyboard_navigation: '在甘特图中启用键盘导航',
	json: '指定 JSON 序列化和解析',
	round_dnd_dates: '允许将任务的开始和结束日期四舍五入到最近的刻度线',
	grid_resizer_column_attribute: '设置列调整器的 DOM 元素的属性名称。该属性表示列的索引',
	locale: '甘特图的当前区域设置对象（特定于区域的标签）',
	keys: '定义甘特图的热键',
	skip_off_time: '从时间尺度隐藏非工作时间',
	auto_types: '自动将带有子任务的任务转换为项目，将没有子任务的项目转换回任务',
	time_step: '设置任务时间值的最小步长（以分钟为单位）',
	inherit_calendar: '定义任务是否应从其摘要父级继承工作日历',
	undo_types: '设置将应用撤消操作的实体类型',
	server_utc: '在将数据发送到服务器的同时将服务器端日期从 UTC 转换为本地时区（和向后）',
	show_unscheduled: '启用显示未计划的任务',
	cascade_delete: '允许的嵌套任务和链接级联删除',
	grid_resize: '通过拖动右侧网格的边框来调整网格的大小',
	right_work_time: '允许将任务的开始和结束日期调整为工作时间（拖动时）',
	min_task_grid_row_height: '设置在调整大小期间可以为任务设置的最小行高',
	static_background: '为时间线区域生成背景图像，而不是渲染实际的列和行行',
	csp: '定义日期格式化方法代码的内部实现',
	link_arrow_size: '设置链接箭头的大小',
	resource_store: '指定连接到 resourceGrid/resourceTimeline/resourceHistogram 视图的 dataStore 的名称',
	dynamic_resource_calendars: '启用将多个资源日历自动合并为一个',
	type_renderers: '重新定义负责显示不同类型任务的函数',
	scale_height: '设置时间刻度的高度和网格的标题',
	links: '存储链接依赖的类型',
	undo: '启用甘特图的撤消功能',
	task_attribute: '设置将指定任务的 HTML 元素的 id 的属性的名称',
	show_progress: '启用在任务栏中显示进度',
	columns: '配置表的列',
	initial_scroll: '设置时间线区域是否最初滚动以显示最早的任务',
	types: '存储灯箱结构的名称（用于不同类型的任务）',
	skin: '返回甘特图的当前皮肤',
	Wide_form: '将节及其标签设置在同一行',
	link_wrapper_width: '设置对点击敏感的区域的宽度（在链接上）',
	highlight_critical_path: '显示图表中的关键路径',
	multiselect_one_level: '指定多任务选择将在一个或任何级别内可用',
	undo_steps: '设置应由 undo 方法恢复的步骤数',
	autofit: '允许将网格的列自动调整为网格的宽度',
	open_split_tasks: '通过单击 +/- 按钮启用展开/折叠拆分任务的可能性'
}