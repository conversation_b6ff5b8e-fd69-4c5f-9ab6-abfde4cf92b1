<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-08 14:00:51
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-13 16:40:53
 * @FilePath: \src\components\FuniGantt\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="funi-gantt">
    <div ref="gantt"
      class="gantt-container">
    </div>
  </div>
</template>
<script>
import { gantt } from 'dhtmlx-gantt'; // 引入dhtmlx-gantt
export default {
  name: 'FuniGantt',
  components: {
  },
  props: {
    /**
     * 配置信息
     */
    config: {
      type: Object,
      default: () => {
        return {}
      }
    },
    /**
     * 任务
     */
    value: {
      type: Array,
      default: () => []
    },
    /**
     * 列
     */
    columns: {
      type: Array,
      default: () => []
    },
    /**
     * 是否开启提示工具
     */
    tooltip: {
      type: Boolean,
      default: true
    },
    /**
     * 左侧表格最小宽度
     */
    minGridColumnWidth: {
      type: Number
    },
    tooltipText: {
      type: Function,
      default: function (start, end, task) {
        return (
          "<b>标题:</b>" +
          task.text +
          "<br/><span>开始:</span>" +
          gantt.templates.tooltip_date_format(start) +
          "<br/><span>结束:</span>" +
          gantt.templates.tooltip_date_format(end)
        )
      }
    }

  },
  data () {
    return {
      tasks: []
    }
  },
  watch: {
    value: {
      handler (newVal) {
        this.tasks = newVal || [];
        this.$nextTick(() => {
          this.initGantt()
        })
      },
      immediate: true
    },
    config (newVal) {
      this.$nextTick(() => {
        this.initGantt()
      })
    },
    columns (newVal) {
      this.$nextTick(() => {
        this.initGantt()
      })
    },
    minGridColumnWidth (newVal) {
      this.$nextTick(() => {
        this.initGantt()
      })
    }
  },
  methods: {
    initGantt () {
      let { tooltip, minGridColumnWidth, tooltipText, config, tasks, columns } = this;
      let defaultConfig = {
        show_task_cells: true,
        fit_tasks: true,
        min_grid_column_width: minGridColumnWidth ?? 100,
        min_column_width: 10,
        auto_types: true,
        xml_date: "%Y-%m-%d",
        step: 1,
        start_on_monday: true,
        row_height: 22,
        scale_height: 60,
        autoscroll: true,
        show_errors: false,
        scales: [
          { unit: 'year', step: 1, format: '%Y' },
          { unit: 'month', step: 1, format: '%M' },
          { unit: 'day', step: 1, format: '%j' },
          { unit: 'day', step: 1, format: '%D' },
        ],
        readonly: true,
        ...(config || {})
      }
      if (tooltip) {
        gantt.plugins({
          tooltip: true
        });
        if (tooltipText && typeof (tooltipText) === 'function') {
          gantt.templates.tooltip_text = tooltipText;
        }
      }
      Object.assign(gantt.config, defaultConfig);
      gantt.clearAll();
      gantt.i18n.setLocale('cn');
      gantt.init(this.$refs.gantt);
      gantt.parse({ data: tasks });
      gantt.config.columns = columns;
    }
  },
}
</script>
<style lang="scss" scoped>
.funi-gantt {
  width: 100%;
  height: 100%;
  min-height: 200px;
  background-color: #ffffff;
  .gantt-container {
    height: 100%;
    width: 100%;
  }
}
:deep(.gantt_task_line) {
  border: 0px solid transparent;
  border-radius: 8px;
}
</style>
