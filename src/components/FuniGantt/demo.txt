<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-09-01 15:56:27
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-09-08 17:40:34
 * @FilePath: \funi-bpaas-as-ui\src\apps\as\appMent\views\appManagement\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="index">
    <funi-gantt :tasks="tasks"
      :columns="columns" />
  </div>
</template>

<script>
export default {
  name: 'index',
  components: {
  },
  props: {

  },
  data () {
    return {
      tasks: [
        { id: 1, name: '张三', text: '', render: 'split' },
        { id: 101, parent: 1, text: 'Task 1', start_date: '2023-07-02', end_date: '2023-07-25', color: '#D0E4FD' },
        { id: 102, parent: 1, text: 'Task 4', start_date: '2023-08-02', end_date: '2023-08-25', color: '#D0E4FD' },
        { id: 2, name: '李四', text: 'Task 3', start_date: '2023-08-10', end_date: '2023-09-01', color: '#D0E4FD' },
        { id: 3, name: '王二', text: 'Task 2', start_date: '2023-07-06', end_date: '2023-09-01' }
      ],
      columns: [
        { name: 'name', label: '姓名', tree: true, width: '*' },
        { name: 'start_date', label: '开始日期', align: 'center', width: '100' },
        { name: 'duration', label: '持续时间', align: 'center', width: '100' },
      ]
    }
  },
  mounted () {

  },
  methods: {

  },
}
</script>
