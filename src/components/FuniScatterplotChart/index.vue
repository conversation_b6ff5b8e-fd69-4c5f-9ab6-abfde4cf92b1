<template>
  <div style="width: 100%">
    <div :id="uid" :style="{ height: props.height + 'px' }"></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { ElMessage } from 'element-plus';
import { cloneDeep } from 'lodash-es';
import { onMounted, ref, markRaw, watch, nextTick, onBeforeUnmount, computed } from 'vue';
import FuniJs from '@funi-lib/utils';
defineOptions({
  name: 'FuniScatterplotChart'
});
const chart = ref(null);
const uid = ref(null);
uid.value = FuniJs.guid();
const initData = ref({
  title: {
    text: '散点图'
  },
  legend: {
    top: 20,
    width: '70%',
    type: 'scroll',
    textStyle: {
      fontSize: 10
    }
  },
  grid: {
    left: '3%',
    right: '7%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    scale: true,
    axisLabel: {
      formatter: '{value} cm'
    },
    splitLine: {
      show: false
    }
  },
  yAxis: {
    type: 'value',
    scale: true,
    axisLabel: {
      formatter: '{value} kg'
    },
    splitLine: {
      show: false
    }
  },
  series: [
    {
      name: 'Male',
      type: 'scatter',
      data: [
        [161.2, 51.6],
        [167.5, 59.0],
        [159.5, 49.2],
        [157.0, 63.0],
        [155.8, 53.6],
        [170.0, 59.0],
        [159.1, 47.6],
        [166.0, 69.8],
        [176.2, 66.8],
        [160.2, 75.2],
        [172.5, 55.2],
        [170.9, 54.2],
        [172.9, 62.5],
        [153.4, 42.0],
        [160.0, 50.0],
        [147.2, 49.8],
        [168.2, 49.2],
        [175.0, 73.2],
        [157.0, 47.8],
        [167.6, 68.8],
        [159.5, 50.6],
        [175.0, 82.5],
        [166.8, 57.2],
        [176.5, 87.8],
        [170.2, 72.8],
        [174.0, 54.5],
        [173.0, 59.8],
        [179.9, 67.3],
        [170.5, 67.8],
        [160.0, 47.0],
        [154.4, 46.2],
        [162.0, 55.0],
        [176.5, 83.0],
        [160.0, 54.4],
        [152.0, 45.8],
        [162.1, 53.6],
        [170.0, 73.2],
        [160.2, 52.1],
        [161.3, 67.9],
        [166.4, 56.6],
        [168.9, 62.3],
        [163.8, 58.5],
        [167.6, 54.5],
        [160.0, 50.2],
        [161.3, 60.3]
      ],
      symbol: 'circle',
      symbolSize: 10,
      label: {
        show: false
      },
      itemStyle: {
        color: '#768dd1'
      }
    }
  ]
});
const defaultData = initData.value.series[0];
const props = defineProps({
  scatterOptions: {
    type: Object,
    default: () => {
      return {};
    }
  },
  custom: {
    //自定义参数
    type: Object,
    default: () => {
      return {};
    }
  },
  width: {
    type: Number,
    default: 0
  },
  height: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    default: ''
  },
  xAxisText: {
    type: String,
    default: ''
  },
  XsplitLine: {
    type: Boolean,
    default: false
  },
  yAxisText: {
    type: String,
    default: ''
  },
  YsplitLine: {
    type: Boolean,
    default: false
  },
  data: {
    type: String,
    default: ''
  },
  dataName: {
    type: String,
    default: ''
  },
  color: {
    type: String,
    default: ''
  },
  bubbleStyle: {
    type: String,
    default: ''
  },
  bubbleSize: {
    type: Number,
    default: 0
  },
  label: {
    type: Boolean,
    default: false
  }
});

watch(
  () => props.scatterOptions,
  nval => {
    if (nval.hasOwnProperty('data')) {
      if (nval.data instanceof Array) {
        initData.value.series.splice(0);
        nval.data.map(e => {
          let dataItem = {
            ...defaultData,
            name: e.dataLabel,
            type: 'scatter',
            data: e.dataValue
          };
          initData.value.series.push(dataItem);
        });
      } else {
        ElMessage.error('请传入正确数据类型');
      }
    }
    if (nval.hasOwnProperty('title')) {
      initData.value.title.text = nval.title;
    }
    if (nval.hasOwnProperty('legendFontSize')) {
      initData.value.legend.textStyle.fontSize = nval.legendFontSize; //图列字体大小
    }
    if (nval.hasOwnProperty('xAxisUnit')) {
      initData.value.xAxis.axisLabel.formatter = '{value}' + nval.xAxisUnit; //x轴单位
    }
    if (nval.hasOwnProperty('xAxisFontSize')) {
      initData.value.xAxis.axisLabel.fontSize = nval.xAxisFontSize; //x轴字体大小
    }
    if (nval.hasOwnProperty('xSplitLine') && nval.xSplitLine === true) {
      initData.value.xAxis.splitLine.show = nval.xSplitLine; //分割线
    }
    if (nval.hasOwnProperty('yAxisUnit')) {
      initData.value.yAxis.axisLabel.formatter = '{value}' + nval.yAxisUnit; //y轴单位
    }
    if (nval.hasOwnProperty('yAxisFontSize')) {
      initData.value.yAxis.axisLabel.fontSize = nval.yAxisFontSize; //y轴字体大小
    }
    if (nval.hasOwnProperty('ySplitLine') && nval.ySplitLine === true) {
      initData.value.yAxis.splitLine.show = nval.ySplitLine; //分割线
    }
    if (nval.hasOwnProperty('bubbleColor')) {
      initData.value.series[0].itemStyle.color = nval.bubbleColor; //气泡颜色
    }
    if (nval.hasOwnProperty('bubbleStyle') && nval.bubbleStyle.hasOwnProperty('type')) {
      if (nval.bubbleStyle.hasOwnProperty('isImg') && nval.bubbleStyle.isImg === true) {
        initData.value.series[0].symbol = 'image://' + nval.bubbleStyle.type;
      } else {
        initData.value.series[0].symbol = nval.bubbleStyle.type; //气泡样式
      }
    }
    if (nval.hasOwnProperty('bubbleSize')) {
      initData.value.series[0].symbolSize = nval.bubbleSize; //气泡大小
    }
    if (nval.hasOwnProperty('isShowLabel') && nval.isShowLabel === true) {
      initData.value.series[0].label.show = true; //是否显示标签
    }
  },
  { deep: true, immediate: true }
);
watch(
  () => initData.value,
  async nval => {
    console.log('nval---- 初始化', nval);
    await nextTick(() => {
      if (!chart.value) {
        chart.value = init();
        draw();
      } else {
        chart.value.setOption({ ...nval, ...props.custom }, true);
      }
    });
  },
  { deep: true, immediate: true }
);

watch(
  () => props.title,
  newVal => {
    initData.value.title.text = newVal;
  },
  { deep: true }
);
watch(
  () => props.xAxisText,
  newVal => {
    initData.value.xAxis.axisLabel.formatter = '{value}';
    initData.value.xAxis.axisLabel.formatter += newVal;
  },
  { deep: true }
);
watch(
  () => props.XsplitLine,
  newVal => {
    if (newVal) {
      initData.value.xAxis.splitLine.show = newVal;
    } else {
      initData.value.xAxis.splitLine.show = false;
    }
  },
  { deep: true }
);
watch(
  () => props.yAxisText,
  newVal => {
    initData.value.yAxis.axisLabel.formatter = '{value}';
    initData.value.yAxis.axisLabel.formatter += newVal;
  },
  { deep: true }
);
watch(
  () => props.YsplitLine,
  newVal => {
    if (newVal) {
      initData.value.yAxis.splitLine.show = newVal;
    } else {
      initData.value.yAxis.splitLine.show = false;
    }
  },
  { deep: true }
);
// watch(
//   () => props.data,
//   newVal => {
//     // console.log(newVal);
//     props.option.series[0].data.splice(0);

//     let str = newVal.replace(/\s/g, '');
//     let pattern =
//       /^\[\d+(\.\d+)?,\s?\d+(\.\d+)?\](,\[\d+(\.\d+)?,\s?\d+(\.\d+)?\])*$/;
//     let isValid = pattern.test(str);

//     if (isValid) {
//       let arr = JSON.parse('[' + str + ']');
//       props.option.series[0].data = arr;
//     } else {
//       alert('请参照输入正确数组格式！');
//     }
//   },
//   { deep: true }
// );
watch(
  () => props.dataName,
  newVal => {
    initData.value.series[0].name = newVal;
  },
  { deep: true }
);
watch(
  () => props.color,
  newVal => {
    initData.value.series[0].itemStyle.color = newVal;
  },
  { deep: true }
);
watch(
  () => props.bubbleStyle,
  newVal => {
    initData.value.series[0].symbol = newVal;
  },
  { deep: true }
);
watch(
  () => props.bubbleSize,
  newVal => {
    initData.value.series[0].symbolSize = newVal;
  },
  { deep: true }
);
watch(
  () => props.label,
  newVal => {
    if (newVal) {
      initData.value.series[0].label.show = newVal;
    } else {
      initData.value.series[0].label.show = false;
    }
  },
  { deep: true }
);
watch(
  () => props.width,
  newValue => {
    chart.value.resize({
      width: newValue
    });
  },
  { deep: true }
);
watch(
  () => props.height,
  newValue => {
    chart.value.resize({
      height: newValue
    });
  },
  { deep: true }
);
const init = () => {
  return markRaw(echarts.init(document.getElementById(uid.value)));
};
const draw = () => {
  chart.value.setOption({ ...initData.value, ...props.custom });
};
const resize = () => {
  chart.value.resize();
};
onMounted(() => {
  window.addEventListener('resize', () => {
    resize();
  });
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', () => {
    resize();
  });
});
</script>

<style></style>
