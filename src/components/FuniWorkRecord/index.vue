<!--
 * @Author: 古加文 <EMAIL>
 * @Date: 2023-04-28 10:40:37
 * @LastEditors: 古加文 <EMAIL>
 * @LastEditTime: 2023-10-19 11:23:14
 * @FilePath: \funi-paas-cs-web-cli\src\components\FuniWorkRecord\index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <funi-curd
    v-if="isObject"
    ref="FwrRef"
    rowKey="businessId"
    :columns="columns"
    :loading="loading"
    :lodaData="_lodaData"
    :pagination="false"
    @row-click="
      obj => {
        _click(obj.row);
      }
    "
  >
    <template #fileCurd="props">
      <div id="curdView">
        <funi-curd
          rowKey="businessUserRecordInfoId"
          :columns="columns2"
          :data="props.row.businessUserRecordInfos"
          :pagination="false"
          size="small"
          :stripe="false"
        ></funi-curd>
      </div>
    </template>
  </funi-curd>
  <funi-curd
    v-else
    rowKey="businessUserRecordInfoId"
    :columns="columns2"
    :loading="loading"
    :lodaData="_lodaData2"
    :pagination="false"
  ></funi-curd>
</template>

<script setup lang="jsx">
import { ref } from 'vue';

defineOptions({
  name: 'FuniWorkRecord',
  inheritAttrs: false
});

const loading = ref(false);
const FwrRef = ref();
const props = defineProps({
  isObject: { type: Boolean, default: true },
  params: { type: Object, default: {} },
  objectListUrl: String,
  busListUrl: String
});

const emit = defineEmits(['onClick']);

const columns = ref([
  { type: 'expand', slots: { default: 'fileCurd' }, width: '50' },
  {
    label: '审批编号',
    prop: 'businessId',
    render: ({ row, index }) => {
      return (
        <div>
          <el-button
            type="primary"
            link
            onClick={() => {
              _click(row);
            }}
          >
            {row.businessId}
          </el-button>
        </div>
      );
    }
  },
  { label: '审批类型', prop: 'dicBusinessTypeName' },
  { label: '审批状态', prop: 'businessStatusName' },
  { label: '申请时间', prop: 'createTime' }
]);

const columns2 = ref([
  {
    label: '处理结果',
    prop: 'content',
    render: ({ row, index }) => {
      return row.content === '启动业务' ? '启动业务' : row.activityName + row.content;
    }
  },
  { label: '处理时间', prop: 'executeTime' },
  {
    label: '处理人员',
    prop: 'operator',
    render: ({ row, index }) => {
      return row.operator ? row.operator.operatorName : '--';
    }
  },
  { label: '处理意见', prop: 'opinion', width: '400' }
]);

//获取对象工作记录列表
const _lodaData = () => {
  if (props.params) {
    loading.value = true;
    return $http
      .post(props.objectListUrl, props.params)
      .then(res => {
        return res;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

//获取业务件工作记录列表
const _lodaData2 = () => {
  if (props.params) {
    loading.value = true;
    return $http
      .post(props.busListUrl, props.params)
      .then(res => {
        return res;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

//点击业务件号
const _click = row => {
  emit('onClick', row);
};
</script>

<style lang="scss" scoped>
#curdView {
  :deep(.funi-curd__header) {
    margin-bottom: 0;
  }

  :deep(.funi-curd) {
    padding: 0 !important;
    padding-left: 50px !important;
  }
}
</style>
