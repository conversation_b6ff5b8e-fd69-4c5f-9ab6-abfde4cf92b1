<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2022-12-13 14:50:56
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2025-04-30 10:14:27
 * @FilePath: /src/components/FuniCurd/index.vue
 * @Description:
 * Copyright (c) 2022 by tao.yang <EMAIL>, All Rights Reserved.
-->

<template>
  <funi-teleport to=".layout-content__wrap" :disabled="!teleported">
    <div :class="['funi-curd__wrap', { teleported }]" :style="fixedHeightStyle" v-loading="loading" ref="curdWrapRef">
      <!-- search-form -->
      <div class="funi-curd__search" v-if="isShowSearch">
        <funi-search-form-v3
          v-if="useSearchV2 && env.useSearchV2"
          v-bind="searchConfig"
          @search="handleSearch"
          @reset="() => handleSearch({})"
        >
          <template v-for="(_, slot) in $slots" #[slot]="params">
            <slot :name="slot" v-bind="params || {}" />
          </template>
        </funi-search-form-v3>
        <funi-search-form
          v-else
          v-bind="searchConfig"
          @search="handleSearch"
          :colNumber="props.colNumber"
          @reset="() => handleSearch({})"
        >
          <template v-for="slotName in searchFormSlots" #[slotName]="params">
            <slot :name="slotName" v-bind="params"></slot>
          </template>
        </funi-search-form>
      </div>
      <!-- table -->
      <div :class="['card funi-curd']" style="padding: 0px 8px 10px">
        <!-- 表格头部 操作按钮 -->
        <!-- <FuniActions v-if="fixedButtons" v-bind="actionsProps || {}">
          <div class="funi-curd__fixed-button-group">
            <slot name="buttonGroup"></slot>
          </div>
        </FuniActions> -->
        <div class="curd-header">
          <div class="curd-header-append-row">
            <slot name="header-append-row"></slot>
          </div>
          <div class="curd-header-bar">
            <div class="curd-header-title">
              <div v-if="$slots.header" class="curd-header-custom">
                <slot name="header"></slot>
              </div>
              <div class="curd-header-button-group">
                <slot name="buttonGroup"></slot>
              </div>
            </div>
            <div class="curd-header-button-group__extend">
              <slot name="extendButtonGroup"></slot>
            </div>
            <div class="curd-header-tools" v-if="useTools">
              <el-tooltip content="刷新" placement="top">
                <el-button link @click="() => reload({ resetPage: false })">
                  <funi-svg name="refresh" />
                </el-button>
              </el-tooltip>
              <el-tooltip content="斑马纹" placement="top">
                <el-button link @click="handleStripe">
                  <funi-svg name="stripe" />
                </el-button>
              </el-tooltip>
              <el-tooltip content="紧凑度" placement="top">
                <el-dropdown
                  trigger="click"
                  size="large"
                  class="ml-[8px] mr-[8px] align-middle"
                  @command="handleResize"
                >
                  <el-button link><funi-svg name="resize" /></el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :style="activeDropdownItemStyle('large')" command="large"
                        >宽松</el-dropdown-item
                      >
                      <el-dropdown-item :style="activeDropdownItemStyle('default')" command="default"
                        >默认</el-dropdown-item
                      >
                      <el-dropdown-item :style="activeDropdownItemStyle('small')" command="small"
                        >紧凑</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-tooltip>
              <CurdColumnSetting :colSettings="colSettings" @col-setting="handleColSetting" />
            </div>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="funi-curd__table">
          <el-table
            ref="curdRef"
            v-bind="$attrs"
            border
            :data="visibleData"
            :rowKey="rowKey"
            :stripe="stripe"
            :scrollbar-always-on="scrollbarAlwaysOn"
            :size="size"
            table-layout="auto"
            v-draggable="draggableOptions"
            style="height: 100%"
            show-overflow-tooltip
            :cell-style="cellStyle"
            :cell-class-name="cellClassName"
            @row-click="handleRowClick"
            @row-dblclick="handleRowDoubleClick"
            @select="handleSelect"
            @select-all="handleSelectAll"
          >
            <template #default>
              <FuniCurdColumn v-for="column in computedColumns" :key="column.prop" :column="column">
                <template v-if="column.slots?.header" #[column.slots.header]="params">
                  <slot :name="column.slots.header" v-bind="params || {}" />
                </template>
                <template v-if="column.slots?.default" #[column.slots.default]="params">
                  <slot :name="column.slots.default" v-bind="params || {}" />
                </template>
              </FuniCurdColumn>
            </template>

            <template #empty>
              <slot name="empty"></slot>
            </template>

            <template #append>
              <slot name="append"></slot>
            </template>
          </el-table>
        </div>
        <!-- 分页 -->
        <Pagination
          ref="pageRef"
          class="funi-curd__pagination"
          v-if="pagination"
          style="margin-top: 10px"
          :total="dataTotal"
          :pageSizes="pageSizes"
          :pagerCount="pagerCount"
          @pageChange="doRequest"
        >
          <template #default="params">
            <slot name="pagination_extra" v-bind="params"></slot>
          </template>
        </Pagination>
      </div>
    </div>
  </funi-teleport>
</template>

<script lang="jsx" setup>
import Pagination from './Pagination.vue';
import {
  computed,
  isVNode,
  onActivated,
  ref,
  unref,
  watch,
  nextTick,
  toRaw,
  useAttrs,
  watchEffect,
  onUnmounted
} from 'vue';
import env from '@/utils/env';
import { useDraggable } from './useDraggable';
import useResetSummaryWidth from './useResetSummaryWidth.js';
import useThemeConfigStore from '@/layout/components/theme/hooks/setTheme.js';
import { useExportTable } from '@/utils/hooks/useExportTable.js';
import CurdColumnSetting from '@/components/FuniCurdV2/components/CurdColumnSetting.vue';

const themeConfigStore = useThemeConfigStore();

defineOptions({
  name: 'FuniCurd',
  inheritAttrs: false
});

const props = defineProps({
  isShowSearch: { type: Boolean, default: false },
  searchConfig: { type: Object, default: () => ({}) },
  data: { type: Array, default: () => [] },
  lodaData: Function,
  rowKey: { type: [String, Function], default: 'id' },
  columns: { type: Array, default: () => [] },
  pagination: { type: Boolean, default: true },
  defaultPage: { type: Object, default: () => ({ pageSize: 10, pageNo: 1 }) },
  fixedButtons: Boolean,
  pageSizes: Array,
  pagerCount: { type: Number, default: 7 },
  actionsProps: Object,
  rowSelection: {
    type: [String, Boolean],
    default: 'click',
    validator: val => !val || ['click', 'dblclick'].includes(val)
  },
  loading: Boolean,
  teleported: Boolean,
  /** 是用4.0模式高级查询 */
  useSearchV2: { type: Boolean, default: false },
  colNumber: { type: Number, default: 4 },
  scrollbarAlwaysOn: { type: Boolean, default: true },
  draggable: { type: Boolean, default: false },
  reloadOnActive: Boolean,
  /**
   * @description: 数据处理回调，用于自定义列表数据
   */
  dataCallback: { type: Function, default: ({ list, total }) => Promise.resolve(list) },
  // 表格是否支持横向滚动
  horizontalScrolling: { type: Boolean, default: true },
  /** 是否使用跨页复选 */
  useReserveSelection: Boolean,
  checkLimit: { type: Number, default: Number.MAX_SAFE_INTEGER },
  fixedHeight: { type: Boolean, default: false },
  settings: { type: Object, default: () => ({}) },
  useTools: { type: Boolean, default: false }
});

// Emits
const emit = defineEmits([
  'get-curd',
  'beforeRequest',
  'afterRequest',
  'requestError',
  'row-click',
  'row-dblclick',
  'draggableEnd',
  'col-setting-change'
]);

const { handleExport } = useExportTable(props);

const curdRef = ref();
const pageRef = ref();
const tableData = ref([]);
const dataTotal = ref(0);
const visibleData = ref([]);
const searchParams = ref({});
const currentRowKey = ref('');
const currentRow = ref(null);
const reserveSelection = ref(new Map());
const attrs = useAttrs();
const selection = ref([]);
const stripe = ref(false);
const colSettings = ref([]);
const dynamicColumnMap = ref({});
const size = ref('default');
let skipReloadCount = 1;

const curdWrapRef = ref();
const curdWrapHeight = ref();
const resizeObserver = ref(null);

const { draggableOptions, draggableColumn } = useDraggable(props, emit);

const computedColumns = computed(() => {
  const columns = $utils.clone(props.columns || [], true).filter(item => {
    const column = dynamicColumnMap.value[item.prop || item.label] || item;
    return column.dynamicHidden !== true;
  });
  if (props.draggable && !columns.some(column => column.prop.includes('draggable_'))) {
    columns.unshift(draggableColumn);
  }

  return $utils.mapTree(columns, column => {
    if (column.type === 'selection') {
      return Object.assign({}, column, {
        width: 40,
        slots: {},
        'label-class-name': visibleData.value.length > props.checkLimit ? 'funi-curd-column__selection_header' : '',
        selectable: (row, index) =>
          (column.selectable?.(row, index) ?? true) &&
          (selection.value.some(item => getRowKeyValue(item) === getRowKeyValue(row)) ||
            selection.value.length < props.checkLimit)
      });
    }
    if (column.type === 'radio') {
      return Object.assign({}, column, {
        width: 40,
        slots: {},
        type: 'radio',
        render: ({ row, index }) => (
          <el-radio
            onClick={e => e.preventDefault()}
            modelValue={rowSelected(row)}
            label={true}
            disabled={!rowSelectable(row)}
          >
            {' '}
          </el-radio>
        )
      });
    }
    column.hidden && (column.labelClassName = 'funi-curd-column__hidden');
    column.hidden && (column.className = 'funi-curd-column__hidden');
    const style = {};
    column.maxWidth && (style.maxWidth = `${column.maxWidth}px`);
    column.width = column.hidden ? 0.00000001 : column.width;
    return Object.assign({ slots: {}, showOverflowTooltip: true, style }, column);
  });
});

const searchFormSlots = computed(() => {
  if (!props.isShowSearch || !props.searchConfig.schema) return [];

  const slotNames = props.searchConfig.schema.map(item => Object.values(item.slots || {})).flat();
  return Array.from(new Set(slotNames));
});

const reserveSelectionRows = computed(() => Array.from(reserveSelection.value.values()).map(toRaw));

const fixedHeightStyle = computed(() => {
  return props.fixedHeight && curdWrapHeight.value ? { height: `${curdWrapHeight.value}px` } : {};
});

watch(curdWrapRef, (newValue, oldValue) => {
  if (newValue && props.fixedHeight) {
    const layoutWrap = document.querySelector('.layout-content__wrap');
    resizeObserver.value = new ResizeObserver(entries => {
      const rect = curdWrapRef.value.getBoundingClientRect();
      const layoutWrapRect = layoutWrap.getBoundingClientRect();
      curdWrapHeight.value = layoutWrapRect.height - (rect.top - layoutWrapRect.top) - 10;
    });
    resizeObserver.value.observe(layoutWrap);
  }
});

watch(
  () => unref(curdRef)?.store?.states?.selection?.value,
  newValue => {
    selection.value = newValue ?? [];
  },
  { deep: true, immediate: true }
);

watch(
  () => props.data,
  newData => {
    if (!props.lodaData) {
      reload();
    }
  },
  { deep: true }
);

//watch curdRef
watch(curdRef, (newValue, oldValue) => {
  if (!oldValue && !!newValue) {
    emit('get-curd', unref(curdRef));
    !props.pagination && doRequest(props.defaultPage);
  }
});
watch(
  () => themeConfigStore.themeConfig.table,
  () => {
    setTimeout(() => useResetSummaryWidth(unref(curdRef)));
  }
);

watch(() => props.settings, setupColSettings, { immediate: true });

onActivated(() => {
  if (skipReloadCount === 0) {
    const { refreshOnActive, resetPage = false } = history.state;
    (props.reloadOnActive || refreshOnActive) && reload({ resetPage });
  }
  skipReloadCount = Math.max(0, skipReloadCount - 1);
});

onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
    resizeObserver.value = null;
  }
});

function contentRender(render, scope) {
  const content = render({ row: unref(scope.row), index: scope.$index });
  return isVNode(content) ? content : <span>{content || '--'}</span>;
}

function defaultRenderCell({ row, column, $index }) {
  const property = column.property;
  const value = property && getProp(row, property).value;
  if (column && column.formatter && typeof column.formatter === 'function') {
    return column.formatter(row, column, value, $index) || '--';
  }
  return $utils.isNil(value) ? '--' : value.toString();
}

const getProp = (obj, path, defaultValue) => {
  return {
    get value() {
      return $utils.get(obj, path, defaultValue);
    },
    set value(val) {
      $utils.set(obj, path, val);
    }
  };
};

const handleStripe = () => (stripe.value = !stripe.value);
const handleResize = val => (size.value = val);

function setupColSettings() {
  if (!!props.settings.columns && !!props.settings.columns.length) {
    const settingsMap = props.settings.columns.reduce((map, item) => {
      map[item.prop || item.label] = item;
      return map;
    }, {});
    colSettings.value = $utils.clone(props.columns || [], true).map(item => {
      const column = settingsMap[item.prop || item.label] || item;
      return { ...item, hidden: column.hidden };
    });
    handleColSetting(colSettings.value);
  } else {
    colSettings.value = $utils.clone(props.columns || [], true);
  }
}

function handleColSetting(settings) {
  console.debug('col setting change', settings);
  const rawSettings = settings.map(setting => toRaw(setting));

  dynamicColumnMap.value = Object.fromEntries(
    Object.entries($utils.groupBy(rawSettings, i => i.prop || i.label)).map(([key, value]) => [
      key,
      Object.assign(value[0], { dynamicHidden: value[0].hidden })
    ])
  );
  const newColumnSettings = $utils.clone(props.columns || [], true).map(i => {
    const column = dynamicColumnMap.value[i.prop || i.label];
    return { ...i, hidden: column.dynamicHidden };
  });
  emit('col-setting-change', newColumnSettings);
}

const activeDropdownItemStyle = command => {
  return {
    backgroundColor: size.value === command ? 'var(--el-nav-color-light-hover)' : '',
    color: size.value === command ? 'var(--el-menu-active-color)' : ''
  };
};

async function doRequest(page) {
  try {
    emit('beforeRequest');
    let list = props.data || [];
    let total;
    if (!!props.lodaData && $utils.isFunction(props.lodaData)) {
      const remoteData = await props.lodaData(
        props.pagination ? { ...page, pageIndex: page.pageNo } : {},
        unref(searchParams)
      );
      list = remoteData.list || [];
      total = remoteData.total;
    }

    // tableData.value = !!props.dataCallback ? await props.dataCallback({ list: list || [], total }) : list || [];
    tableData.value = list || [];

    if (!props.pagination) {
      visibleData.value = unref(tableData);
    } else if ($utils.isNil(total)) {
      const start = (page.pageNo - 1) * page.pageSize;
      const offset = page.pageSize;
      visibleData.value = unref(tableData).slice(start, start + offset);
      dataTotal.value = unref(tableData).length;
    } else {
      visibleData.value = unref(tableData);
      dataTotal.value = total;
    }
    setCurrentRowByKey(unref(currentRowKey));
    emit('afterRequest', list);
    await nextTick();
    useResetSummaryWidth(unref(curdRef));
    visibleData.value.forEach(row => {
      if (reserveSelection.value.has(getRowKeyValue(row))) {
        unref(curdRef).toggleRowSelection(row, true);
      }
    });
  } catch (error) {
    emit('requestError', error);
    console.error('doRequest - ', error);
  }
}

function reload({ resetPage = true } = {}) {
  if (!props.pagination) {
    doRequest(props.defaultPage);
  } else if (resetPage) {
    resetCurrentRow();
    if (pageRef.value) {
      pageRef.value.resetPageIndex();
    }
  } else {
    doRequest({ pageNo: pageRef.value.currentPage, pageSize: pageRef.value.pageSize });
  }
}

function handleSearch(params) {
  if (props.searchConfig.setParams && typeof props.searchConfig.setParams == 'function') {
    searchParams.value = props.searchConfig.setParams(params);
  } else {
    searchParams.value = params;
  }
  reload();
}

function handleRowClick(row, column, event) {
  if (!rowSelectable(row)) return;

  if (props.rowSelection === 'click') {
    toggleSelection(row);
    setCurrentRow(row);
  }
  emit('row-click', {
    column,
    row,
    selection: unref(curdRef).getSelectionRows(),
    reserveSelectionRows: reserveSelectionRows.value,
    currentRow: currentRow.value
  });
}

function handleRowDoubleClick(row, column, event) {
  if (!rowSelectable(row)) return;

  if (props.rowSelection === 'dblclick') {
    toggleSelection(row);
    setCurrentRow(row);
  }
  emit('row-dblclick', {
    column,
    row,
    selection: unref(curdRef).getSelectionRows(),
    reserveSelectionRows: reserveSelectionRows.value,
    currentRow: currentRow.value
  });
}

function handleSelect(selection, row) {
  if (props.useReserveSelection) {
    if (selection.some(item => getRowKeyValue(item) === getRowKeyValue(row))) {
      reserveSelection.value.set(getRowKeyValue(row), row);
    } else {
      reserveSelection.value.delete(getRowKeyValue(row));
    }
  }
  attrs.onSelect?.(selection);
}

function handleSelectAll(selection) {
  if (props.useReserveSelection) {
    if (selection.length) {
      selection.forEach(row => reserveSelection.value.set(getRowKeyValue(row), row));
    } else {
      visibleData.value
        .filter(row => rowSelectable(row))
        .forEach(row => reserveSelection.value.delete(getRowKeyValue(row)));
    }
  }
  attrs.onSelectAll?.(selection);
}

function getRowKeyValue(row) {
  return row[props.rowKey || 'id'];
}

function clearSelection() {
  reserveSelection.value.clear();
  unref(curdRef).clearSelection();
}

function rowSelectable(row) {
  if (!row) return false;

  const selectionColumn = unref(computedColumns).find(column => ['selection', 'radio'].includes(column.type));
  if (!!selectionColumn) {
    const selectable = selectionColumn.selectable;
    if ($utils.isBoolean(selectable)) return selectable;
    if ($utils.isFunction(selectable)) {
      const rowKey = props.rowKey || 'id';
      const rowIndex = unref(visibleData).findIndex(item => item[rowKey] === row[rowKey]);
      return selectable(row, rowIndex);
    }
  }
  return true;
}

function columnSelectable(column) {
  return column.rowClick !== false;
}

function toggleSelection(row) {
  // 复选
  unref(curdRef).toggleRowSelection(row);
  const rowKey = getRowKeyValue(row);
  reserveSelection.value.has(rowKey) ? reserveSelection.value.delete(rowKey) : reserveSelection.value.set(rowKey, row);
}

/**
 * @description: 设置当前行
 */
function setCurrentRow(row) {
  if (!row || !props.rowKey) return;

  currentRowKey.value = row[props.rowKey];
  currentRow.value = row;
}

/**
 * @description: 通过RowKey设置当前行
 */
function setCurrentRowByKey(key) {
  if (!key || !props.rowKey) return;
  setCurrentRow(unref(visibleData).find(item => item[props.rowKey] === key));
}

function rowSelected(row) {
  return !!unref(currentRowKey) && !!row ? unref(currentRowKey) === row[props.rowKey] : false;
}

function resetCurrentRow() {
  currentRowKey.value = '';
  currentRow.value = undefined;
}

function cellStyle({ row, column, rowIndex, columnIndex }) {
  return computedColumns.value.find(item => item.prop === column.property)?.style || {};
}
function cellClassName({ row, column, rowIndex, columnIndex }) {
  const cell = $utils.findTree(computedColumns.value, item => item.prop === column.property)?.item;
  const widthClass = !props.horizontalScrolling || !!cell?.width ? '' : 'auto-width-cell';
  const wrapClass = cell?.wrap ? 'wrap-cell' : '';
  return [wrapClass, widthClass].filter(Boolean).join(' ');
}

defineExpose({
  ref: curdRef,
  reload,
  doRequest,
  tableData,
  visibleData,
  currentRow,
  resetCurrentRow,
  setCurrentRow,
  setCurrentRowByKey,
  toggleSelection,
  pageRef,
  searchParams,
  handleExport,
  reserveSelectionRows,
  clearSelection
});
</script>

<style lang="less" scoped>
.funi-curd__fixed-button-group {
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
}

.funi-curd {
  display: flex;
  flex-direction: column;

  &__header {
    flex-shrink: 0;
    display: flex;
    margin: 10px 0;
    padding-left: 12px;
    justify-content: space-between;

    // .header-custom {
    //   flex-grow: 1;
    // }

    // .header-button-group {
    //   flex-shrink: 0;
    //   display: flex;
    //   flex-direction: row;
    //   justify-content: flex-end;
    //   flex-wrap: nowrap;
    // }
  }

  &__table {
    flex-grow: 1;
    overflow-y: auto;

    :deep(.el-popper) {
      max-width: 50vw;
    }
  }

  &__pagination {
    flex-shrink: 0;
  }

  .draggable-handle {
    cursor: grab;
  }
}
</style>
<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as *;

:deep(.funi-curd__table) {
  .funi-curd-column__hidden,
  .funi-curd-column__hidden > .cell {
    display: none;
  }
  .funi-curd-column__selection_header > .cell {
    display: none;
  }
  @include b(table) {
    @include m(border) {
      &::before {
        width: var(--funi-curd-border-around-width);
      }

      &::after {
        width: var(--funi-curd-border-around-width);
      }

      .#{$namespace}-table__cell {
        border-right: var(--funi-curd-border-right) !important;
        padding: 8px 2px 8px 0;

        .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: keep-all;
        }
      }
      .auto-width-cell .cell {
        width: 100% !important;
      }

      .wrap-cell .cell {
        white-space: normal !important;
        word-break: break-all !important;
      }
    }

    th.#{$namespace}-table__cell {
      @include set-css-var-value('table-header-bg-color', rgba(248, 248, 249, 1));
      background-color: getCssVar('table', 'header-bg-color') !important;
    }

    @include e(border-left-patch) {
      width: 0px;
    }
  }
}
</style>
<style lang="scss" scoped>
.funi-curd__wrap {
  display: flex;
  flex-direction: column;

  .funi-curd__search {
    flex-shrink: 0;
  }

  .funi-curd {
    flex-grow: 1;
  }
}

.funi-curd__wrap.teleported {
  height: 100%;
}

.curd-header {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  margin-bottom: 12px;
  margin-top: 12px;

  &-append-row {
    margin-bottom: 10px;
  }

  &-bar {
    display: flex;
    > div + div {
      margin-left: 12px;
    }
  }

  &-title {
    flex-grow: 1;
    display: flex;
  }

  &-custom {
    flex-grow: 1;
  }

  &-button-group,
  &-button-group__extend {
    flex-shrink: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    flex-wrap: nowrap;
  }

  &-button-group__extend {
    flex-direction: row-reverse;
    :deep(.el-button + .el-button) {
      margin-right: 8px;
      margin-left: 0;
    }
    &:empty {
      display: none;
    }
  }

  &-tools {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    :deep(.el-button + .el-button) {
      margin-left: 8px;
    }

    :deep(.el-button) {
      padding: 2px 0;
      border: none;
    }
  }
}
</style>
