/*
 * @Author: coutinho <EMAIL>
 * @Date: 2024-03-18 16:31:36
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-03-18 18:14:29
 * @FilePath: /funi-cloud-web-gsbms/src/components/FuniCurd/useResetSummaryWidth.js
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
import { onBeforeUnmount } from 'vue'

export default function (tableRef) {
    if (!tableRef?.context) return
    const { tableBody, footerWrapper } = tableRef.context.refs
    if (!footerWrapper) return
    const footerColgroup = footerWrapper.querySelector('colgroup')
    if (!footerColgroup) return
    let footerColgroupCol = footerColgroup.querySelectorAll('col')
    if (footerColgroupCol) {
        let cols = tableBody.querySelector('colgroup').querySelectorAll('col')
        cols.forEach((element, index) => {
            footerColgroupCol[index].style.width = window.getComputedStyle(element).width
        })
        const { scrollBarRef } = tableRef
        const reSetScrollLeft = (e) => {
            footerWrapper.scrollLeft = e.target.scrollLeft
        }
        const deleteFn = () => {
            scrollBarRef.wrapRef.removeEventListener('scroll', reSetScrollLeft)
        }
        deleteFn()
        scrollBarRef.wrapRef.addEventListener('scroll', reSetScrollLeft)
        onBeforeUnmount(deleteFn)
    }

}