<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-21 18:50:07
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-06-28 16:00:49
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniHighlightCode/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div :style="themeStyle">
    <Transition>
      <div v-show="showCode" class="funi-htmlText-code-highlight" v-html="htmlText || onlyCode"></div>
    </Transition>
  </div>
</template>
<script setup>
import { ref, watch, computed, nextTick, onMounted } from 'vue';
// import 'highlight.js/styles/base16/flat.css';
import './index.css';
import hljs from './common.js';
import { ElMessage } from 'element-plus';

const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  language: {
    type: String,
    default: ''
  },
  autodetect: {
    type: Boolean,
    default: true
  },
  ignoreIllegals: {
    type: Boolean,
    default: true
  },
  htmlText: {
    type: String,
    default: ''
  }
});
const language = ref(props.language);
let showCode = ref(false);
let onlyCode = ref('');
let themeStyle = ref({
  '--heighLight-theme': '#2c3e50',
  '--heighLight-color': '#9fa2a6'
});
watch(
  () => props.language,
  newLanguage => {
    language.value = newLanguage;
  },
  {
    immediate: true
  }
);
let htmlLength = computed(() => props.htmlText.length);
let codeLength = computed(() => props.code.length);
let viewBool = computed(() => props.htmlText.length + props.code.length);

const setHtmlHighlightedCode = async () => {
  await nextTick();
  let dom = document.getElementsByClassName('funi-htmlText-code-highlight')[0];
  let codeList = dom.querySelectorAll('code');
  let preList = dom.querySelectorAll('pre');
  setCopyIcon(preList);
  setCodeHighlight(codeList);
};

const setCodeHighlight = codeList => {
  for (let i = 0; i < codeList.length; i++) {
    let str = escapeHtml(codeList[i].innerHTML);
    let result = hljs.highlightAuto(str);
    let language = result.language ?? '';
    codeList[i].className = language ? `funi-hljs hljs ${language} blockImprotant` : 'funi-hljs hljs notKonwLanguage';
    if (!result.language) {
      result = hljs.highlight(str, {
        language: 'java',
        ignoreIllegals: props.ignoreIllegals
      });
    }

    codeList[i].innerHTML = result.value;
    if (codeList[i]?.parentNode?.tagName !== 'pre' && codeList[i]?.parentNode?.tagName !== 'PRE') {
      codeList[i].style.display = ' inline-block';
      codeList[i].className = 'funi-hljs hljs';
    }
  }
};

const setOnlyCodeHighlight = () => {
  let result = hljs.highlightAuto(props.code);
  let language = result.language ?? '';
  onlyCode.value = `<div class="prefather" style="width:100%"><div class="ulBox"></div> <pre><code class="hljs ${language} blockImprotant">${result.value}</code></pre></div> `;
};
const setCopyIcon = preList => {
  for (let i = 0; i < preList.length; i++) {
    if (preList[i].querySelector('code')) {
      var tmp = document.createElement('span');
      tmp.innerHTML = `<svg width="28" height="25" viewBox="0 0 256 256"><path fill="#9aa5ce" d="M224 40v144a8 8 0 0 1-16 0V48H72a8 8 0 0 1 0-16h144a8 8 0 0 1 8 8Zm-32 32v144a8 8 0 0 1-8 8H40a8 8 0 0 1-8-8V72a8 8 0 0 1 8-8h144a8 8 0 0 1 8 8Zm-16 8H48v128h128Z"/></svg>`;
      tmp.setAttribute('data-src', escapeHtml(preList[i].querySelector('code').innerHTML));
      tmp.className = 'copyIcon';
      preList[i].appendChild(tmp);

      let divDom = document.createElement('div');
      divDom.className = 'prefather';
      preList[i].parentNode.replaceChild(divDom, preList[i]);
      let divUlDom = document.createElement('div');
      divUlDom.className = 'ulBox';

      // let
      // divUlDom.appendChild();
      divDom.appendChild(divUlDom);
      divDom.appendChild(preList[i]);
    }
  }
  let copyIconList = document.getElementsByClassName('copyIcon');
  for (let j = 0; j < copyIconList.length; j++) {
    copyIconList[j].onclick = () => {
      copyContent(copyIconList[j].getAttribute('data-src'));
    };
  }
};
const setUlLi = async () => {
  showCode.value = true;
  await nextTick();
  let dom = document.getElementsByClassName('funi-htmlText-code-highlight')[0];
  let preList = dom.querySelectorAll('pre');
  for (let i = 0; i < preList.length; i++) {
    if (preList[i].querySelector('.hljs')) {
      let len = (preList[i].clientHeight - 32) / 24;
      let domUl = document.getElementsByClassName('ulBox')[i];
      for (let j = 0; j < len; j++) {
        let liDom = document.createElement('span');
        liDom.innerHTML = j + 1;
        domUl.appendChild(liDom);
      }
    }
  }
};
//
const copyContent = e => {
  const textareaEle = document.createElement('textarea');
  document.body.appendChild(textareaEle);
  textareaEle.value = e;
  textareaEle.select();
  textareaEle.readOnly = 'readOnly';
  document.execCommand('copy');
  document.body.removeChild(textareaEle);

  ElMessage({
    message: '复制成功',
    type: 'success'
  });
};

const escapeHtml = value => {
  return value
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'");
};

watch(
  () => viewBool.value,
  async () => {
    showCode.value = false;
    if (viewBool.value && htmlLength.value) {
      setHtmlHighlightedCode();
    } else if (viewBool.value && codeLength.value) {
      setOnlyCodeHighlight();
    }
    await nextTick();
    setUlLi();
  },
  {
    immediate: true
  }
);
</script>
<style scoped>
.v-enter-active {
  animation: code-in 0.8s;
}

.v-leave-active {
  animation: code-in 0.8s reverse;
}
@keyframes code-in {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.funi-htmlText-code-highlight {
  background-color: #fff !important;
  width: 100%;
}
:deep(code) {
  background-color: unset !important;
  background: var(--heighLight-theme) !important;
}
:deep(code.notKonwLanguage) {
  line-height: 24px !important;
}
:deep(code.blockImprotant) {
  display: block !important;
  width: auto !important;
  background-color: unset !important;
  line-height: 24px !important;
}
:deep(pre > code.blockImprotant > span) {
  display: inline-block;
  height: 24px;
  line-height: 24px;
}
:deep(pre) {
  /* width: 100%; */
  width: calc(100% - 40px) !important;
  overflow-x: auto;
  background: var(--heighLight-theme) !important;
  box-sizing: border-box;
  margin: 0 !important;
}
:deep(pre code.hljs) {
  padding: 0 !important;
}
:deep(.prefather) {
  /* display: inline-block; */
  position: relative;
  width: 800px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: var(--heighLight-theme) !important;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}
:deep(.prefather:hover .copyIcon) {
  opacity: 1;
  transition: all 0.4s;
}
:deep(.copyIcon) {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  padding: 3px 0;
  top: 10px;
  right: 10px;
  background-color: #292d3e;
  opacity: 0;
  border-radius: 5px;
  transition: all 0.4s;
}

:deep(.ulBox) {
  width: 39px;
  height: 100%;
  box-sizing: border-box;
  padding: 16px 0;
  color: #9aa5ce;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;
}
:deep(.ulBox > span) {
  display: inline-block;
  width: 100%;
  height: 24px;
  line-height: 24px;
  text-align: right;
  font-size: 12px;
  box-sizing: border-box;
  padding-right: 5px;
  border-right: 1px solid #9aa5ce;
}
</style>

<style>
@media (prefers-color-scheme: dark) {
  .markdown-body {
    color-scheme: light !important;
    --color-prettylights-syntax-comment: #6e7781 !important;
    --color-prettylights-syntax-constant: #0550ae !important;
    --color-prettylights-syntax-entity: #8250df !important;
    --color-prettylights-syntax-storage-modifier-import: #24292f !important;
    --color-prettylights-syntax-entity-tag: #116329 !important;
    --color-prettylights-syntax-keyword: #cf222e !important;
    --color-prettylights-syntax-string: #0a3069 !important;
    --color-prettylights-syntax-variable: #953800 !important;
    --color-prettylights-syntax-brackethighlighter-unmatched: #82071e !important;
    --color-prettylights-syntax-invalid-illegal-text: #f6f8fa !important;
    --color-prettylights-syntax-invalid-illegal-bg: #82071e !important;
    --color-prettylights-syntax-carriage-return-text: #f6f8fa !important;
    --color-prettylights-syntax-carriage-return-bg: #cf222e !important;
    --color-prettylights-syntax-string-regexp: #116329 !important;
    --color-prettylights-syntax-markup-list: #3b2300 !important;
    --color-prettylights-syntax-markup-heading: #0550ae !important;
    --color-prettylights-syntax-markup-italic: #24292f !important;
    --color-prettylights-syntax-markup-bold: #24292f !important;
    --color-prettylights-syntax-markup-deleted-text: #82071e !important;
    --color-prettylights-syntax-markup-deleted-bg: #ffebe9 !important;
    --color-prettylights-syntax-markup-inserted-text: #116329 !important;
    --color-prettylights-syntax-markup-inserted-bg: #dafbe1 !important;
    --color-prettylights-syntax-markup-changed-text: #953800 !important;
    --color-prettylights-syntax-markup-changed-bg: #ffd8b5 !important;
    --color-prettylights-syntax-markup-ignored-text: #eaeef2 !important;
    --color-prettylights-syntax-markup-ignored-bg: #0550ae !important;
    --color-prettylights-syntax-meta-diff-range: #8250df !important;
    --color-prettylights-syntax-brackethighlighter-angle: #57606a !important;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f !important;
    --color-prettylights-syntax-constant-other-reference-link: #0a3069 !important;
    --color-fg-default: #24292f !important;
    --color-fg-muted: #57606a !important;
    --color-fg-subtle: #6e7781 !important;
    --color-canvas-default: #ffffff !important;
    --color-canvas-subtle: #f6f8fa !important;
    --color-border-default: #d0d7de !important;
    --color-border-muted: hsla(210, 18%, 87%, 1) !important;
    --color-neutral-muted: rgba(175, 184, 193, 0.2) !important;
    --color-accent-fg: #0969da !important;
    --color-accent-emphasis: #0969da !important;
    --color-attention-subtle: #fff8c5 !important;
    --color-danger-fg: #cf222e !important;
  }
}
</style>
