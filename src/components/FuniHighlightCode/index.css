/*!
  Theme: Flat
  Author: <PERSON> (http://chriskempson.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
pre code.funi-hljs.hljs {
    display: block;
    overflow-x: auto;
    padding: 1em
}

code.funi-hljs.hljs {
    padding: 3px 5px
}

.funi-hljs.hljs {
    color: #e0e0e0;
    background: #2c3e50
}

.funi-hljs.hljs ::selection,
.funi-hljs.hljs::selection {
    background-color: #7f8c8d;
    color: #e0e0e0
}

.funi-hljs .hljs-comment {
    color: #95a5a6
}

.funi-hljs .hljs-tag {
    color: #bdc3c7
}

.funi-hljs .hljs-operator,
.funi-hljs .hljs-punctuation,
.funi-hljs .hljs-subst {
    color: #e0e0e0
}

.funi-hljs .hljs-operator {
    opacity: .7
}

.funi-hljs .hljs-bullet,
.funi-hljs .hljs-deletion,
.funi-hljs .hljs-name,
.funi-hljs .hljs-selector-tag,
.funi-hljs .hljs-template-variable,
.funi-hljs .hljs-variable {
    color: #e74c3c
}

.funi-hljs .hljs-attr,
.funi-hljs .hljs-link,
.funi-hljs .hljs-literal,
.funi-hljs .hljs-number,
.funi-hljs .hljs-symbol,
.funi-hljs .hljs-variable.constant_ {
    color: #e67e22
}

.funi-hljs .hljs-class .hljs-title,
.funi-hljs .hljs-title,
.funi-hljs .hljs-title.class_ {
    color: #f1c40f
}

.funi-hljs .hljs-strong {
    font-weight: 700;
    color: #f1c40f
}

.funi-hljs .hljs-addition,
.funi-hljs .hljs-code,
.funi-hljs .hljs-string,
.funi-hljs .hljs-title.class_.inherited__ {
    color: #2ecc71
}

.funi-hljs .hljs-built_in,
.funi-hljs .hljs-doctag,
.funi-hljs .hljs-keyword.hljs-atrule,
.funi-hljs .hljs-quote,
.funi-hljs .hljs-regexp {
    color: #1abc9c
}

.funi-hljs .hljs-attribute,
.funi-hljs .hljs-function .hljs-title,
.funi-hljs .hljs-section,
.funi-hljs .hljs-title.function_,
.funi-hljs .ruby .hljs-property {
    color: #3498db
}

.funi-hljs .diff .hljs-meta,
.funi-hljs .hljs-keyword,
.funi-hljs .hljs-template-tag,
.funi-hljs .hljs-type {
    color: #9b59b6
}

.funi-hljs .hljs-emphasis {
    color: #9b59b6;
    font-style: italic
}

.funi-hljs .hljs-meta,
.funi-hljs .hljs-meta .hljs-keyword,
.funi-hljs .hljs-meta .hljs-string {
    color: #be643c
}

.funi-hljs .hljs-meta .hljs-keyword,
.funi-hljs .hljs-meta-keyword {
    font-weight: 700
}