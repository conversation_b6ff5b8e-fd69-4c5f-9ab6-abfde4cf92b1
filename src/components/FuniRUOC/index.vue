<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-07-17 16:00:54
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-02 15:23:28
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOC/index.vue
 * @Description: 若要封装成表单的 item ，请自行再次封装一次 请注意在表单的时候验证： import { useFormItem } from 'element-plus';const formTiem = useFormItem();formTiem?.formItem?.validate();
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div
    :style="{
      '--tabPaneHeight': tabPaneHeight,
      padding: '10px'
    }"
  >
    <div class="showValue">
      <div v-for="(item, index) in allList" :key="item.id" class="itemBlock">
        <span style="font-size: 15px"> <funi-icon :icon="iconClass[item.type]" /> </span>
        <span>{{ item.name }}</span>
        <span class="delItem" v-if="edit" @click="delItem(item)">
          <funi-icon icon="iconamoon:close-thin" />
        </span>
      </div>
    </div>
    <div v-if="edit">
      <el-tabs v-model="activeNameVal">
        <el-tab-pane v-if="!hide.role" label="角色" name="role">
          <Role
            ref="roleBanner"
            v-model="roleList"
            :defaultProps="{ id: 'id', name: 'roleName' }"
            searchName="roleName"
            :mode="chooseMode.role"
            v-bind="roleProps"
          ></Role>
        </el-tab-pane>
        <el-tab-pane v-if="!hide.user" label="人员" name="user">
          <User
            ref="userBanner"
            v-model="userList"
            :orgDefaultProps="{ name: 'orgName' }"
            :userDefaultProps="{ name: 'name', id: 'accountId' }"
            :mode="chooseMode.user"
            v-bind="userProps"
          ></User>
        </el-tab-pane>
        <el-tab-pane v-if="!hide.org" label="管理主体" name="org">
          <Org
            ref="orgBanner"
            v-model="orgList"
            :defaultProps="{ name: 'orgName' }"
            searchName="orgName"
            :mode="chooseMode.org"
            v-bind="orgProps"
          ></Org>
        </el-tab-pane>
        <el-tab-pane v-for="(item, index) in extendTabs" :key="item.name" :label="item.label" :name="item.name">
          <slot :name="item.name"></slot>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import Role from './role.vue';
import Org from './org.vue';
import User from './user.vue';
onMounted(() => {
  resetModelList();
});
const orgBanner = ref(null);
const roleBanner = ref(null);
const userBanner = ref(null);
const props = defineProps({
  mode: {
    type: Object,
    // single
    default: {
      user: 'multiple',
      role: 'multiple',
      org: 'multiple'
    }
  },
  modelValue: {
    type: Array,
    default: () => []
  },
  edit: {
    type: Boolean,
    default: true
  },
  hide: {
    type: Object,
    default: {
      role: false,
      user: false,
      org: false
    }
  },
  extendTabs: {
    type: Array,
    default: () => []
  },
  activeName: {
    type: String,
    default: 'role'
  },
  tabPaneHeight: {
    type: String,
    default: '500px'
  },

  roleProps: Object,
  userProps: Object,
  orgProps: Object
});

const iconClass = computed(() => {
  let arr = {};
  props.extendTabs.forEach(item => {
    arr[item.name] = item.icon;
  });
  return {
    user: 'humbleicons:user',
    role: 'la:user-tie',
    org: 'fluent-mdl2:org',
    ...arr
  };
});
const activeNameVal = props.activeName;
const roleList = ref([]);
const userList = ref([]);
const orgList = ref([]);
const allList = ref([]);
const flag = ref(true);
const emits = defineEmits(['update:modelValue', 'delete']);
const chooseMode = computed(() => {
  return Object.assign(
    {
      user: 'multiple',
      role: 'multiple',
      org: 'multiple'
    },
    props.mode
  );
});

const resetModelList = () => {
  flag.value = false;
  if (orgBanner.value || roleBanner.value || userBanner.value) {
    orgList.value = allList.value.filter(item => item.type === 'org');
    roleList.value = allList.value.filter(item => item.type === 'role');
    userList.value = allList.value.filter(item => item.type === 'user');
  }
  nextTick(() => (flag.value = true));
};

const delItem = item => {
  let type = item.type;
  let index = void 0;
  if (type === 'role') {
    index = roleList.value.findIndex(el => el.id === item.id);
    roleList.value.splice(index, 1);
  } else if (type === 'user') {
    index = userList.value.findIndex(el => el.id === item.id);
    userList.value.splice(index, 1);
  } else if (type === 'org') {
    index = orgList.value.findIndex(el => el.id === item.id);
    orgList.value.splice(index, 1);
  } else {
    emits('delete', item);
    return;
  }
};

watch(
  [roleList, userList, orgList],
  newVal => {
    if (flag.value) {
      for (let i = 0; i < allList.value.length; i++) {
        if (['user', 'role', 'org'].indexOf(allList.value[i].type) > -1) {
          allList.value.splice(i, 1);
          i--;
        }
      }
      newVal.map(item => {
        allList.value.push(...item);
      });

      emits('update:modelValue', allList.value);
    }
  },
  {
    deep: true
  }
);
watch(
  () => props.modelValue,
  newVal => {
    allList.value = newVal;

    resetModelList();
  },
  {
    deep: true,
    immediate: true
  }
);
</script>

<style scoped>
@import url('./style/itemBlock.css');
* {
  box-sizing: border-box;
}

.showValue {
  width: 100%;
  height: 60px;
  /* max-height: 150px; */
  border: 1px solid rgba(247, 247, 250, 1);
  padding: 5px;
  border-radius: 5px;
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
  flex-wrap: wrap;
  gap: 10px;
  overflow: hidden;
  overflow-y: auto;
}
</style>
