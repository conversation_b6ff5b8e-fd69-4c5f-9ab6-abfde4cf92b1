<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-07-17 16:32:54
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-26 18:33:47
 * @FilePath: /funi-cloud-erm-ui/src/components/FuniRUOC/role.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div
    v-loading="loading"
    :style="{
      '--mode-border-radius': mode === 'multiple' ? 'var(--el-checkbox-border-radius)' : '50%'
    }"
  >
    <div style="height: 35px">
      <el-input v-model="roleName" :validateEvent="false" @input="search" placeholder="请搜索角色"></el-input>
    </div>
    <div class="roleList">
      <el-checkbox-group :validateEvent="false" v-model="checkedRole" @change="checkedChange">
        <el-checkbox
          :validateEvent="false"
          v-for="(item, index) in roleList"
          :key="item[roleProps.id]"
          :id="item[roleProps.id]"
          :label="item[roleProps.id]"
        >
          <span>{{ item[roleProps.name] }}</span>
        </el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed, nextTick, watch } from 'vue';
const props = defineProps({
  request: {
    type: Object,
    default: () => {
      return {
        api: '/csccs/roleList/roleList',
        method: 'post',
        param: { flag: false }
      };
    }
  },
  defaultProps: {
    type: Object,
    default: () => {
      return {
        id: 'id',
        name: 'name'
      };
    }
  },
  searchName: {
    type: String,
    default: 'keyword'
  },
  modelValue: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'multiple'
  }
});
const roleList = ref([]);
const checkedRole = ref([]);
const roleName = ref(void 0);
const timer = ref(null);
const loading = ref(false);
const roleProps = computed(() => {
  return Object.assign(
    {
      id: 'id',
      name: 'name'
    },
    props.defaultProps
  );
});

const allRoleList = ref([]);
const selectedRoleId = ref([]);
const emits = defineEmits(['update:modelValue', 'updateRoleList']);

onMounted(() => {
  getRoleList();
});

const search = async e => {
  await nextTick();
  clearTimeout(timer.value);
  timer.value = setTimeout(() => {
    clearTimeout(timer.value);
    getRoleList();
  }, 600);
};
const getRoleList = async () => {
  loading.value = true;
  let { list } = await $http[props.request.method](props.request.api, {
    ...props.request.param,
    [props.searchName]: roleName.value
  }).finally(() => {
    loading.value = false;
  });
  checkedRole.value = [];
  list = list.map(item => {
    return {
      ...item,
      checked: false
    };
  });
  roleList.value = list;
  if (roleName.value === '' || roleName.value === void 0 || roleName.value === null) {
    allRoleList.value = list;
  }
  emits('updateRoleList', allRoleList.value);
  await nextTick();
  setDefaultSelected();
};
const checkedChange = v => {
  if (props.mode !== 'multiple' && v && v.length) {
    v = [v[v.length - 1]];
  }
  roleList.value.forEach(item => {
    for (let i = 0; i < selectedRoleId.value.length; i++) {
      if (selectedRoleId.value[i] === item[roleProps.value.id]) {
        selectedRoleId.value.splice(i, 1);
        i--;
      }
    }
  });
  selectedRoleId.value.push(...v);
  let arr = [];
  selectedRoleId.value.forEach(item => {
    let a = allRoleList.value.filter(ele => item === ele[roleProps.value.id]);
    arr.push({
      id: a[0][roleProps.value.id],
      name: a[0][roleProps.value.name],
      type: 'role'
    });
  });
  emits('update:modelValue', arr);
};
const setDefaultSelected = () => {
  checkedRole.value = [];
  roleList.value.forEach(item => {
    if (selectedRoleId.value.indexOf(item[roleProps.value.id]) > -1) {
      checkedRole.value.push(item[roleProps.value.id]);
    }
  });
};
const setValue = newVal => {
  if (props.mode !== 'multiple') {
    selectedRoleId.value = newVal && newVal.length ? [newVal[newVal.length - 1].id] : [];
  } else {
    selectedRoleId.value = newVal.map(item => item.id);
  }
  setDefaultSelected();
};

watch(
  () => props.modelValue,
  newVal => {
    setValue(newVal);
  },
  {
    deep: true
  }
);
defineExpose({
  setValue
});
</script>
<style scoped>
.roleList {
  width: 100%;
  height: calc(var(--tabPaneHeight) - 35px);
  overflow-y: auto;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
:deep(.el-checkbox__inner) {
  border-radius: var(--mode-border-radius);
}
</style>
