
import { ref, computed, watch ,nextTick} from 'vue'

let timer = null
const potentialLabelWidthArr = ref([])
const formBoxElement = ref(null)
const formLabels = ref([])
const labelPosition = ref('right')


const autoLabelWidth = computed(() => {
    if (!potentialLabelWidthArr.value.length) return 0
    const max = Math.max(...potentialLabelWidthArr.value)
    return max
})
/**
 * @description 更新label的宽度
 * @param {HTMLElement} formBox
 * @param {'default' | 'border'} formTheme
 * @param {'right' | 'left'} labelPos
 * @param {boolean} bool
 * **/
const updateLabelWidth = (formBox, formTheme, labelPos, bool = false) => {
    if (!formBox || formTheme === 'default') return;
    labelPosition.value = labelPos
    formBoxElement.value = formBox
    clearTimeout(timer)
    // const s = bool ? 0 : 600
    if (bool) return registerLabelWidth()
    timer = setTimeout(() => {
        clearTimeout(timer)
        registerLabelWidth()
    }, 600)
}

/**
 * @description 注册所有label宽度
 * **/
const registerLabelWidth = () => {
    potentialLabelWidthArr.value = []
    formLabels.value = formBoxElement.value.querySelectorAll('.el-form-item__label-wrap')
    for (let i = 0; i < formLabels.value.length; i++) {
        let width = getLabelWidth(formLabels.value[i]) || 0

        potentialLabelWidthArr.value.push(width)
    }
}

const getLabelWidth = (el) => {
    if (el?.firstElementChild) {
        const width = window.getComputedStyle(el.firstElementChild).width
        return Number.parseFloat(width)
    } else {
        return 0
    }
}

watch(potentialLabelWidthArr,async value => {
    if (!value?.length || value.length !== formLabels.value.length) return
    await nextTick()
    formLabels.value.forEach((item, index) => {
        const marginWidth = Math.max(
            0,
            Number.parseFloat(autoLabelWidth.value, 10) - potentialLabelWidthArr.value[index]
        )
        const marginStyle = labelPosition.value === 'right' ? 'marginLeft' : 'marginRight'
        item.style[marginStyle] = `${marginWidth}px`
    })

})

export default updateLabelWidth