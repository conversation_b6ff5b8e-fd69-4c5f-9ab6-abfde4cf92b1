<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2024-10-08 09:44:58
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-10-22 17:31:29
 * @FilePath: \src\components\FuniReportView\index.vue
 * @Description: 
 * Copyright (c) 2024 by <EMAIL>, All Rights Reserved. 
-->
<template>
  <div style="height: 100%">
    <iframe :id="frameId"
      :src="src"
      :style="{ width: '100%', height: computedHeight }"
      frameborder="0"
      scrolling="auto"></iframe>
  </div>
</template>
<script setup>
import { reactive, ref, computed, onMounted } from 'vue';
const props = defineProps({
  /**
   * 链接
   */
  src: {
    type: String,
    default: ''
  },
  /**
   * 高度
   */
  height: {
    type: String,
    default: 'calc(100% - 88px)'
  },
  /**
   * 是否全屏
   */
  isFull: {
    type: Boolean,
    default: false
  }
});

const frameId = ref(window.$utils.guid());

const computedHeight = computed(() => {
  let height = props.height;
  if (props.height && !Number.isNaN(Number(height))) {
    height = height + 'px';
  }
  if (props.isFull) {
    height = 'calc(100vh - 120px)';
  }
  return height;
});

//  打印
const print = e => {
  const Iframe = document.getElementById(frameId.value);

  Iframe.contentWindow.printBtnClicked(e);
};

//  导出
const exportBtnMonitor = e => {
  const Iframe = document.getElementById(frameId.value);

  // Iframe.contentWindow.exportFileFunction({ type: 'PDF' });
  Iframe.contentWindow.exportBtnMonitor(e);
};

onMounted(() => {

});

defineExpose({
  frameId: frameId.value,
  print,
  exportBtnMonitor
});
</script>
<style lang="scss" scoped></style>
