<template>
  <funi-teleport to=".layout-content__wrap" :disabled="!teleported">
    <el-tabs v-model="activeTab" :class="['funi_list_page-v2', 'bg-white', { hide_tab: hideTab, teleported }]">
      <el-tab-pane :name="item.key" v-for="(item, index) in tabs" :key="item.key">
        <template #label>
          <el-badge :hidden="!item.badge || !badgeCountMap[item.key]" :value="badgeCountMap[item.key] || 0" :max="99">
            <span>{{ item.label }}</span>
          </el-badge>
        </template>
        <funi-curd-v2
          v-on="item.curdOption.on || {}"
          :loading="loading[index]"
          :lodaData="item.api || item.curdOption.api ? (...args) => getData(item.key, ...args) : undefined"
          ref="curd"
          :isShowSearch="isShowSearch"
          :searchConfig="getSearchConfig()"
          :actionsProps="{ disabled: activeTab !== item.key }"
          :use-tools="true"
          :queryFields="queryFieldsMap[item.key]"
          :sortFields="sortFieldsMap[item.key]"
          :columnFilters="columnFiltersMap[item.key]"
          v-bind="item.curdOption"
          :settings="colSettings[tabCurdId(item.key)]"
          @beforeRequest="beforeRequest"
          @afterRequest="afterRequest"
          @requestError="requestError"
          @col-setting-change="handleColSettingChange"
          v-if="!item.slot"
        >
          <!-- 表格标题 -->
          <template #header v-if="item.curdOption.header">
            <component :is="contentRender(item.curdOption.header)" />
          </template>
          <!-- 表格按钮 -->
          <template #buttonGroup="scope">
            <template v-for="btn in item.curdOption.btns">
              <el-button
                v-if="!btn.component"
                type="primary"
                v-bind="btn"
                v-auth="btn.auth"
                :key="btn.key"
                @click="headBtnClick(btn)"
              >
                {{ btn.label }}
              </el-button>
              <component v-else :is="btn.component()"></component>
            </template>
          </template>
          <!-- 表格列头插槽 -->
          <template
            #[column.slots.header]="scope"
            v-for="column in item.curdOption.columns.filter(x => x.slots?.header)"
          >
            <slot :name="column.slots.header" v-bind="scope"></slot>
          </template>
          <!-- 表格列插槽 -->
          <template
            #[column.slots.default]="scope"
            v-for="column in item.curdOption.columns.filter(x => x.slots?.default)"
          >
            <slot :name="column.slots.default" v-bind="scope"></slot>
          </template>
          <!-- 空数据插槽 -->
          <template #empty>
            <slot name="empty"></slot>
          </template>
          <!-- append插槽 -->
          <template #append>
            <slot name="append"></slot>
          </template>
          <template #pagination_extra="params">
            <component
              v-if="!!item.curdOption.paginationExtra"
              :is="contentRender(item.curdOption.paginationExtra(params))"
            ></component>
          </template>
          <template v-for="(_, slot) in $slots" #[slot]="params">
            <slot :name="slot" v-bind="params || {}" />
          </template>
        </funi-curd-v2>
        <slot v-else :name="item.slot"></slot>
      </el-tab-pane>
    </el-tabs>
  </funi-teleport>
</template>
<script setup lang="jsx">
import { computed, reactive, ref, isVNode, watch, nextTick, onActivated } from 'vue';
import { useRoute } from 'vue-router';
import { useAppStore } from '@/stores/useAppStore';
import AppApis from '@/apis/app';
import lzString from 'lz-string';

const emit = defineEmits(['headBtnClick', 'beforeRequest', 'afterRequest', 'requestError', 'badgeChange']);
const props = defineProps({
  cardTab: {
    type: Array,
    default() {
      return [];
    }
  },
  isShowSearch: { type: Boolean, default: true },
  active: { type: String },
  showTab: {
    type: Boolean
  },
  teleported: { type: Boolean, default: true },
  reloadOnActive: Boolean
});

const appStore = useAppStore();
const route = useRoute();
const routePath = route.path;
const tabs = computed(() => {
  return props.cardTab.map((item, index) => ({
    ...item,
    key: item.key || `${routePath}_list_page_tab_index-${index}`
  }));
});

//data
const activeTab = ref(props.active || tabs.value[0]?.key);
const colSettings = reactive({});
const tabCurdId = key => `${routePath}/${key}/curd`;
const activeTabCurdId = computed(() => tabCurdId(activeTab.value));
const activeCurd = computed(() => {
  const activeTabIndex = tabs.value.findIndex(x => x.key == activeTab.value);
  return curd.value?.[activeTabIndex];
});

//curd实例
let curd = ref();
let loading = reactive([]);
let skipReloadCount = 1;

// 高级查询、列排序配置
const queryFieldsMap = reactive({});
const sortFieldsMap = reactive({});
const columnFiltersMap = reactive({});
const badgeCountMap = reactive({});

//是否隐藏tab
const hideTab = computed(() => {
  if (tabs.value.length > 1) {
    return props.showTab !== false;
  }
  return props.showTab !== true;
});

watch(activeTab, autoReloadActiveCurd);
watch(activeTab, fetchAdvancedQueryConfig, { immediate: true });
watch(activeTab, findCurdCustomization, { immediate: true });

onActivated(() => {
  skipReloadCount === 0 && autoReloadActiveCurd();
  skipReloadCount = Math.max(0, skipReloadCount - 1);
});

/**
 * 配置curd自动刷新，curdOption.reloadOnActive优先级高于props.reloadOnActive
 * 1. curdOption.reloadOnActive为true时，自动刷新
 * 2. curdOption.reloadOnActive为false时，不自动刷新
 * 3. curdOption.reloadOnActive未配置时，根据props.reloadOnActive判断是否自动刷新
 */
function autoReloadActiveCurd() {
  const activeTabIndex = tabs.value.findIndex(x => x.key == activeTab.value);
  const curdOption = tabs.value[activeTabIndex]?.curdOption;
  if (!activeCurd.value) return;

  const reloadOnActive =
    curdOption.reloadOnActive === true || (curdOption.reloadOnActive !== false && props.reloadOnActive);
  reloadOnActive && nextTick(() => activeCurd.value.reload({ resetPage: false }));
}

//请求函数
function getData(tabKey, page, searchParams = {}) {
  let index = props.cardTab.findIndex(x => x.key == tabKey);
  const activeTabItem = tabs.value[index];
  //api
  let api = activeTabItem.api || activeTabItem.curdOption.api;
  //requestParams
  let requestParams = activeTabItem.requestParams || activeTabItem.curdOption.requestParams;
  if (api) {
    loading[index] = true;
    let _searchParams = searchParams;
    //外部整理搜索参数
    if (typeof activeTabItem.fixSearchParams == 'function') {
      _searchParams = activeTabItem.fixSearchParams(searchParams);
    }
    return $http
      .post(api, {
        ...page,
        ...(requestParams || {}),
        ..._searchParams
      })
      .then(res => {
        updateBadgeCount(tabKey, res?.total || 0);
        return res;
      })
      .finally(() => {
        loading[index] = false;
      });
  }
}

//搜索配置
function getSearchConfig() {
  return (
    tabs.value.find(x => x.key == activeTab.value).searchConfig || {
      schema: [
        {
          prop: 'keyword',
          label: '关键字',
          component: 'el-input'
        }
      ]
    }
  );
}

//头部按钮点击事件
function headBtnClick(item) {
  emit('headBtnClick', item.key);
}

//搜索
function searchOutput(arr) {
  reload();
}

//vNode渲染
function contentRender(header) {
  return isVNode(header) ? header : <span>{header || ''}</span>;
}

//刷新
function reload({ resetPage = true } = {}) {
  let index = tabs.value.findIndex(x => x.key == activeTab.value);
  if (curd.value) {
    curd.value[index].reload({ resetPage });
  }
}

//请求前事件
function beforeRequest() {
  let index = tabs.value.findIndex(x => x.key == activeTab.value);
  loading[index] = true;
  emit('beforeRequest');
}

//请求后事件
function afterRequest(list) {
  let index = tabs.value.findIndex(x => x.key == activeTab.value);
  loading[index] = false;
  emit('afterRequest', list);
}

//请求抛错事件
function requestError(error) {
  let index = tabs.value.findIndex(x => x.key == activeTab.value);
  loading[index] = false;
  emit('requestError', error);
}

/**
 * 查询高级查询、列排序配置
 */
function fetchAdvancedQueryConfig() {
  if (!!activeTab.value && (!queryFieldsMap[activeTab.value] || !sortFieldsMap[activeTab.value])) {
    const searchConfig = tabs.value.find(x => x.key == activeTab.value).searchConfig || {};
    const position = { form: 'form', head: 'head', adv: 'adv' };
    AppApis.findSQLFieldByPageCode({ pageCode: searchConfig.pageCode || route.name }).then(res => {
      columnFiltersMap[activeTab.value] = (res || [])
        .filter(item => item.position === position.head)
        .map(i => Object.assign({}, i, { attribute: JSON.parse(i.attribute || '{}') }));

      queryFieldsMap[activeTab.value] = (res || [])
        .filter(i => i.position === position.form)
        .sort((a, b) => a.sort - b.sort)
        .map(item => {
          Object.assign(item, { attribute: JSON.parse(item.attribute || '{}') });
          if (['AUTOCOMPLETE', 'COMBO'].includes(item.type)) {
            item.attribute = Object.assign(item.attribute, { url: item.attribute.dataSourceUrl });
          } else if (item.type === 'SELECT') {
            item.attribute = Object.assign(item.attribute || {}, { dynamicUrl: item.attribute.dataSourceUrl });
          } else if (item.type === 'SLOT') {
            item.attribute = Object.assign(item.attribute || {}, { slotUrl: item.attribute.dataSourceUrl });
          }
          item.field = item.field || item.attribute?.fieldCode || item.id;
          Object.assign(item.attribute || {}, {
            params: item.attribute.params || searchConfig.filterParams || {}
          });
          item.attribute.defaultValue = searchConfig.defaultValues?.[item.field];
          return item;
        });
    });
    AppApis.findSQLSortByPageCode({ pageCode: searchConfig.pageCode || route.name }).then(res => {
      sortFieldsMap[activeTab.value] = res || [];
    });
  }
}

function findCurdCustomization() {
  if (!colSettings[activeTabCurdId.value]) {
    AppApis.findCurdCustomization({ tableId: activeTabCurdId.value }).then(res => {
      const jsonObj = JSON.parse(res?.tableTitleJson || '{}');
      if (jsonObj.compressed) {
        colSettings[activeTabCurdId.value] = JSON.parse(lzString.decompress(jsonObj.compressed) || '{}');
      }
    });
  }
}

function handleColSettingChange(settings) {
  const compressedString = lzString.compress(JSON.stringify({ columns: settings }));
  AppApis.newCurdCustomization({
    tableId: activeTabCurdId.value,
    tableTitleJson: JSON.stringify({ compressed: compressedString })
  });
}

const badgeHidden = item => !item.badge || !badgeCountMap[item.key];
const updateBadgeCount = (tabKey, count) => {
  badgeCountMap[tabKey] = count;
  emit('badgeChange', tabKey, count);
};

defineExpose({
  reload,
  activeTab,
  activeCurd,
  updateBadgeCount
});
</script>
<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;

.hide_tab {
  :deep(.el-tabs__header) {
    display: none;
  }
}

.funi_list_page-v2 {
  :deep(.el-tabs__item) {
    padding: 0 20px !important;
  }

  .search_box {
    padding: 8px 16px;
    background: white;
    margin-bottom: 16px;
  }

  .title_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: white;

    .table_title {
      font-size: 16px;
      color: #515151;
    }

    .btn_box {
      display: flex;

      .btn {
        min-width: 80px;
      }
    }
  }
  :deep() {
    .#{el.$namespace}-tabs__header {
      margin-bottom: 0px;
    }
    .#{el.$namespace}-badge__content.is-fixed {
      right: calc(-4px + var(--el-badge-size) / 2);
    }
  }
}
</style>

<style lang="scss" scoped>
// 强制FuniListPage高度100%，curd分页置底，内容自动拉满
.funi_list_page-v2.teleported {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(> .el-tabs__header) {
    flex-shrink: 0;
  }

  :deep(> .el-tabs__content) {
    flex-grow: 1;
    overflow: auto;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
      > .funi-curd__wrap-v2 {
        // height: 100%;
        flex-grow: 1;
      }
    }
  }
}
</style>
