<template>
  <el-autocomplete
    ref="autocompleteRef"
    v-model="state"
    clearable
    fit-input-width
    style="width: 100%"
    :placeholder="placeholder"
    value-key="key"
    :debounce="400"
    :fetch-suggestions="suggestions"
    :popper-class="popperClass"
    @select="handleSelect"
    @clear="handleClear"
    @input="handleInput"
    @focus="handleFocus"
  >
    <template #default="{ item }">
      <el-tooltip placement="left" :disabled="tooltipDisabledMap[item.key]">
        <template #content>
          <div class="max-w-[240px]">{{ item.key }}</div>
        </template>
        <template #default>
          <span :ref="el => handleToolContentRef(el, item)">{{ item.key }}</span>
        </template>
      </el-tooltip>
    </template>
  </el-autocomplete>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue';

const props = defineProps({
  placeholder: { type: String, default: '请输入' },
  modelValue: String,
  url: String,
  requestParams: { type: Object, default: () => ({}) },
  keywordKey: { type: String, default: 'keyword' },
  valueKey: { type: String, default: 'value' },
  labelKey: { type: String, default: 'key' },
  primaryKey: { type: String }
});

const emit = defineEmits(['update:modelValue', 'select']);

const autocompleteRef = ref();

const state = ref();
const selectedItem = ref({});
const pageNo = ref(1);
const suggestions = ref([]);
const loadMore = ref(true);
const tooltipDisabledMap = reactive({});

const popperClass = 'funi-autocomplete-' + $utils.guid();

const querySearchAsync = async (queryString, params) => {
  if (!props.url || !loadMore.value) return;

  const keyword = props.keywordKey || 'keyword';
  const requestParams = {
    pageNo: pageNo.value,
    pageSize: 10,
    [keyword]: queryString,
    ...props.requestParams,
    ...params
  };

  const res = await $http.post(props.url, requestParams);
  if (res.total === 0) {
    suggestions.value = [{ key: '暂无数据', value: -999 }];
  } else {
    const list = res.list.map(i => ({
      ...i,
      key: i[props.labelKey],
      value: i[props.primaryKey || props.valueKey]
    }));
    suggestions.value = pageNo.value === 1 ? list : [...suggestions.value, ...list];
  }
  autocompleteRef.value.suggestions = suggestions.value;
  loadMore.value = suggestions.value.length < res.total;
  suggestionWrapDom.value.querySelector('.empty-placeholder').style.display = res.total === 0 ? '' : 'none';
};

const suggestionWrapDom = computed(() => {
  const popper = document.querySelector(`.${popperClass}`);
  const suggestionDom = popper?.querySelector(`.${popperClass}` + ' > .el-autocomplete-suggestion');
  const scrollbarDom = suggestionDom?.querySelector('.el-autocomplete-suggestion > .el-scrollbar');
  const wrapDom = scrollbarDom?.querySelector('.el-scrollbar > .el-autocomplete-suggestion__wrap');
  return wrapDom;
});

watch(
  () => props.modelValue,
  async val => {
    if (!val) {
      handleClear();
    } else if (props.url && !!val && val !== selectedItem.value[props.valueKey]) {
      console.debug('props.modelValue', val);
      await querySearchAsync('', { [props.valueKey]: val });
      handleSelect(suggestions.value[0]);
      state.value = selectedItem.value.key;
    }
  },
  { immediate: true }
);

onMounted(() => {
  suggestionWrapDom.value?.addEventListener(
    'scroll',
    e => {
      const { scrollTop, clientHeight, scrollHeight } = e.target || {};
      if (loadMore.value && scrollTop >= 60 && scrollTop + clientHeight >= scrollHeight) {
        pageNo.value++;
        querySearchAsync(state.value);
      }
    },
    false
  );

  const emptyPlaceholder = document.createElement('div');
  emptyPlaceholder.className = 'empty-placeholder';
  emptyPlaceholder.style.position = 'absolute';
  emptyPlaceholder.style.inset = 0;
  emptyPlaceholder.style.display = 'none';
  suggestionWrapDom.value.style.position = 'relative';
  suggestionWrapDom.value?.appendChild(emptyPlaceholder);
});

onUnmounted(() => {
  suggestionWrapDom.value?.removeEventListener('scroll', () => {}, false);
});

const handleFocus = () => {
  loadMore.value = true;
  if (state.value) querySearchAsync(state.value);
};

const handleInput = value => {
  pageNo.value = 1;
  loadMore.value = true;
  !value ? handleClear() : querySearchAsync(value);
};

const handleSelect = item => {
  if (item[props.valueKey] !== selectedItem.value[props.valueKey]) {
    pageNo.value = 1;
    selectedItem.value = item;
    loadMore.value = true;
    emit('update:modelValue', item[props.valueKey]);
    emit('select', item);
  }
};

// const handleBlur = () => {
//   pageNo.value = 1;
//   suggestions.value = [];
//   loadMore.value = true;
//   if (state.value !== selectedItem.value.key) {
//     selectedItem.value = {};
//     emit('update:modelValue', state.value);
//   }
// };

function handleClear() {
  state.value = '';
  pageNo.value = 1;
  suggestions.value = [];
  selectedItem.value = {};
  loadMore.value = true;
  emit('update:modelValue', '');
}

const isOverflow = el => {
  const range = document.createRange();
  range.setStart(el, 0);
  range.setEnd(el, el.childNodes.length);
  const rangeWidth = range.getBoundingClientRect().width;

  const parentStyle = window.getComputedStyle(el.parentElement, null);
  const paddingLeft = Number.parseInt(parentStyle.paddingLeft, 10) || 0;
  const paddingRight = Number.parseInt(parentStyle.paddingRight, 10) || 0;
  const horizontalPadding = paddingLeft + paddingRight;

  const { offsetWidth, scrollWidth } = el.parentElement;
  return rangeWidth + 2 > offsetWidth || scrollWidth > offsetWidth + horizontalPadding;
};

const handleToolContentRef = (el, item) => {
  if (el) {
    tooltipDisabledMap[item.key] = !isOverflow(el);
  }
};
</script>
