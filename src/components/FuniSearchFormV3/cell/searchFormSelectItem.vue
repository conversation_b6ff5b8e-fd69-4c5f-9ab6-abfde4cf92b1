<template>
  <el-select
    clearable
    v-model="selectValue"
    :multiple="attribute.multipleChoice"
    style="width: 100%"
    placeholder="请选择"
    :loading="loading"
    @visible-change="handleVisibleChange"
  >
    <el-option v-for="item in dynamicOptions" :key="item.value" :label="item.key" :value="item.value" />
  </el-select>
</template>

<script setup>
import { ref, watchEffect, inject, computed, watch } from 'vue';

defineOptions({
  name: 'SearchFormSelectItem'
});

const props = defineProps({
  modelValue: [String, Array, Number, Object, Boolean],
  field: String,
  attribute: { type: Object, default: () => ({}) }
});

const emit = defineEmits(['update:modelValue']);

const selectValue = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
});

const { getFilters } = inject($utils.symbols.searchForm);

const options = ref([]);
const dynamicOptions = ref([]);

const loading = ref(false);

watchEffect(async () => {
  if (!!props.attribute.dataSourceUrl) {
    await fetchOptions();
    dynamicOptions.value = options.value;
  }
});

async function fetchOptions() {
  try {
    const res = await $http.fetch(props.attribute.dataSourceUrl);
    if (res && res.list && res.list.length > 0) {
      options.value = (res.list || []).map(i =>
        Object.assign({}, i, {
          key: i[props.attribute.labelKey || 'key'],
          value: i[props.attribute.valueKey || 'value']
        })
      );
    }
  } catch (error) {
    options.value = [];
  }
  return Promise.resolve();
}

async function fetchDynamicOptions() {
  try {
    if (!options.value.length) {
      await fetchOptions();
    }

    if (props.attribute?.dynamic && !!props.attribute?.dynamicUrl) {
      const filters = getFilters().filter(item => item.field !== props.field);
      if (!!filters.length) {
        const requestParams = { queryField: props.field, filters, ...props.attribute?.params };
        const res = await $http.post(props.attribute?.dynamicUrl, requestParams);
        const dynamicValues = (res.list || []).map(item => item[props.attribute.valueKey || 'value']);
        dynamicOptions.value = options.value.filter(item => dynamicValues.includes(item.value));
      } else {
        dynamicOptions.value = options.value || [];
      }
    }
  } catch (error) {
    dynamicOptions.value = [];
  }

  return Promise.resolve();
}

async function handleVisibleChange(visible) {
  if (visible && props.attribute?.dynamic) {
    loading.value = true;
    await fetchDynamicOptions();
    loading.value = false;
  } else if (visible) {
    dynamicOptions.value = options.value;
  }
}
</script>
