<template>
  <el-date-picker
    style="width: 240px"
    placeholder="请选择"
    range-separator="~"
    start-placeholder="开始"
    end-placeholder="结束"
    :type="dateType"
    :format="subType.format"
    :date-format="subType.dateFormat"
    :time-format="subType.timeFormat"
    :value-format="subType.format"
  />
</template>

<script setup>
import { ref, computed } from 'vue';
const SubType = {
  YEAR: { type: 'year', format: 'YYYY' },
  MONTH: { type: 'month', format: 'YYYY-MM' },
  DAY: { type: 'date', format: 'YYYY-MM-DD' },
  HOUR: { type: 'datetime', format: 'YYYY-MM-DD HH', timeFormat: 'HH', dateFormat: 'YYYY-MM-DD' },
  MINUTE: { type: 'datetime', format: 'YYYY-MM-DD HH:mm', timeFormat: 'HH:mm', dateFormat: 'YYYY-MM-DD' },
  SECOND: { type: 'datetime', format: 'YYYY-MM-DD HH:mm:ss', timeFormat: 'HH:mm:ss', dateFormat: 'YYYY-MM-DD' }
};

const props = defineProps({
  operate: String,
  attribute: { type: Object, default: () => ({}) },
  range: Boolean
});

const subType = computed(() => SubType[props.attribute.subType || 'DAY']);
const dateType = computed(() => {
  return [subType.value.type, props.range ? 'range' : ''].join('');
});
</script>
