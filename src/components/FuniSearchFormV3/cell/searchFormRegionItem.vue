<template>
  <el-cascader
    ref="cascader"
    clearable
    style="width: 240px"
    placeholder="请输入"
    :props="cascaderProps"
    @change="handleValuesChange"
  />
</template>

<script setup>
import { computed, ref } from 'vue';

defineOptions({
  name: 'SearchFormRegionItem'
});

const props = defineProps({
  modelValue: { type: Object, default: () => ({}) },
  attribute: { type: Object, default: () => ({}) }
});

const emit = defineEmits(['update:modelValue']);
const cascader = ref();
const regionConfig = computed(() => JSON.parse(props.attribute.region || '{}'));
const rootRegionURL = computed(() => props.attribute.dataSourceUrl || '/csccs/region/findRootRegion');
const sonRegionURL = computed(() => props.attribute.dataSourceUrl || '/csccs/region/findSonRegionTreeNoRestriction');

const cascaderProps = {
  lazy: true,
  value: 'code',
  label: 'name',
  checkStrictly: true,
  emitPath: false,
  lazyLoad(node, resolve) {
    if (node.level === 0) {
      $http
        .post(rootRegionURL.value)
        .then(res => resolve(res.list))
        .catch(err => resolve([]));
    } else {
      $http
        .post(sonRegionURL.value, { code: node.value, businessConfigCode: node.value })
        .then(res => {
          resolve(
            (res.list || []).map(item => ({ ...item, leaf: item.level === parseInt(regionConfig.value.maxLevel) }))
          );
        })
        .catch(err => resolve([]));
    }
  }
};

const handleValuesChange = () => {
  const [node] = cascader.value.getCheckedNodes();
  const cascadeColumn = regionConfig.value.cascadeColumn || [];
  const columnKey = cascadeColumn.find(item => parseInt(item.level) === node.data.level)?.column;
  !!columnKey && emit('update:modelValue', { [columnKey]: node.value });
};
</script>
