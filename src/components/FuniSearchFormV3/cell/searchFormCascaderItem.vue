<template>
  <el-cascader
    ref="cascader"
    clearable
    style="width: 240px"
    placeholder="请输入"
    :props="cascaderProps"
    v-model="cascaderValue"
    @change="handleValuesChange"
  />
</template>

<script setup>
import { ref, watch, computed, nextTick, reactive } from 'vue';

defineOptions({
  name: 'SearchFormCascaderItem'
});

const props = defineProps({
  modelValue: [Object, String],
  attribute: { type: Object, default: () => ({}) },
  field: String
});

const emit = defineEmits(['update:modelValue']);
const cascader = ref();
const cascaderValue = ref();
const requestUrl = computed(() => props.attribute?.dataSourceUrl.split('?')[0] ?? '');
const requestParamKey = computed(() => {
  const params = new URLSearchParams(props.attribute?.dataSourceUrl.split('?')[1] ?? '');
  const keys = new Set();
  params.forEach((_, key) => keys.add(key));
  return [...keys];
});
const requestParamDefaultValue = computed(() => {
  const params = new URLSearchParams(props.attribute?.dataSourceUrl.split('?')[1] ?? '');
  const kv = {};
  params.forEach((value, key) => Object.assign(kv, { [key]: value }));
  return kv;
});

const checkStrictly = computed(() => props.attribute?.checkStrictly ?? true);

watch(
  () => props.modelValue,
  () => {
    if (!props.modelValue) {
      cascaderValue.value = '';
    } else if (Array.isArray(props.modelValue)) {
      cascaderValue.value = props.modelValue;
      nextTick(() => {
        if (props.attribute.levels.length > 0) {
          const field = props.attribute.levels.find(i => i.level === cascaderValue.value.length)?.field;
          !!field && emit('update:modelValue', { [field]: cascaderValue.value[cascaderValue.value.length - 1] });
        } else {
          emit('update:modelValue', { [props.field]: cascaderValue.value[cascaderValue.value.length - 1] });
        }
      });
    }
  },
  { immediate: true }
);

const cascaderProps = reactive({
  lazy: true,
  value: 'value',
  label: 'key',
  checkStrictly: checkStrictly.value,
  emitPath: false,
  lazyLoad(node, resolve) {
    if (requestUrl.value) {
      /**
       * 返回数据结构
       * { list: [{ key: '显示名', value: 'xx' }] }
       * leaf: true - 表示子集无数据, false - 表示子集存在数据
       */
      let params = {};
      if (node.level === 0) {
        params = requestParamDefaultValue.value;
      } else {
        params = requestParamKey.value.reduce(
          (previousValue, currentValue) => Object.assign(previousValue, { [currentValue]: node.data[currentValue] }),
          { level: node.level, code: node.value }
        );
      }

      $http
        .post(requestUrl.value, params)
        .then(res => {
          const list = Array.isArray(res) ? res : Array.isArray(res.list) ? res.list : [];
          resolve(
            list.map(i => {
              const isLeaf = !$utils.isNil(i.leaf) && i.leaf;
              const isMaxLevel = !!props.attribute?.optionalLevels
                ? node.level + 1 >= props.attribute.optionalLevels
                : false;
              return Object.assign({}, i, {
                key: i[props.attribute.labelKey || 'key'],
                value: i[props.attribute.valueKey || 'value'],
                leaf: isLeaf || isMaxLevel
              });
            })
          );
        })
        .catch(() => resolve([]));
    } else {
      resolve([]);
    }
  }
});

const handleValuesChange = () => {
  const [node] = cascader.value.getCheckedNodes();
  if (props.attribute?.multistage) {
    const level = $utils.isNil(node.data.level) ? node.level : node.data.level;
    const field = props.attribute?.levels.find(i => i.level === level)?.field;
    !!field && emit('update:modelValue', { [field]: node.value });
  } else {
    emit('update:modelValue', { [props.field]: node.value });
  }
};
</script>
