<template>
  <div shadow="never" class="card">
    <search-form
      ref="formRef"
      v-bind="$attrs"
      :col="colNumber"
      :schema="computedSchema"
      :inline="false"
      :expand="expand"
      @keyup.enter.native="search('change')"
      @change="search('change')"
    >
      <template v-for="(_, slot) in $slots" #[slot]="params">
        <slot :name="slot" v-bind="params"></slot>
      </template>
      <template #toolbar="{ item }">
        <div class="w-full flex" :class="toolbarAlignClass">
          <el-button type="primary" @click="search('search')"> 查询</el-button>
          <el-button @click="reset"> 重置</el-button>
          <el-link v-if="item.expandable" type="primary" :underline="false" class="ml-19px" @click="setVisible">
            {{ expand ? '收起' : '高级查询' }}
          </el-link>
        </div>
      </template>
    </search-form>
  </div>
</template>

<script setup lang="jsx">
import { ref, computed, unref, provide } from 'vue';
import { cellRender } from './cell-render.jsx';
import SearchForm from './form.vue';

defineOptions({
  name: 'FuniSearchFormV3',
  inheritAttrs: false
});

const emit = defineEmits(['search', 'reset', 'change']);
const props = defineProps({
  colNumber: { type: Number, default: 4 },
  queryFields: { type: Array, default: () => [] }
});

provide($utils.symbols.searchForm, { getFilters });

const formRef = ref();
const expand = ref(false);
const colNumber = props.colNumber;

const operateMap = computed(() => {
  const map = new Map();
  props.queryFields.forEach(item => {
    const operateOptions = (item.operates.split(',') || []).map(i => ({ value: i }));
    const operate = (operateOptions || [])[0] || { value: '' };
    !!operate.value && map.set(item.field, operate.value);
  });
  return map;
});

/**
 * 1.每行固定4列
 * 2.第一行最后一列固定为操作按钮
 * 3.默认展开高级查询
 */
const computedSchema = computed(() => {
  const schema = props.queryFields.map((item, index) => {
    let type = item.type.toUpperCase();
    const operate = operateMap.value.get(item.field) || '';
    const attribute = item.attribute || {};

    if (type === 'NUMBER' && operate === 'BETWEEN') {
      type = 'NUMBERRANGE';
    }

    const renderName = type === 'SLOT' ? attribute.key || item.field : type;
    const render = cellRender.get(renderName);

    return {
      label: item.name,
      prop: item.field,
      component: ({ item, formModel, value }) => {
        return !!render ? render(item, formModel, value) : '';
      },
      hidden: index > colNumber - 2 && !unref(expand),
      required: attribute.required,
      props: {
        field: item.field,
        operate: operate,
        attribute: attribute,
        prompt: item.prompt
      }
    };
  });
  const toolbarSchema = {
    prop: 'toolbar',
    labelHidden: false,
    expandable: schema.length > colNumber - 1,
    slots: { default: 'toolbar' }
  };
  schema.splice(schema.length, 0, toolbarSchema);
  return schema;
});

const toolbarAlignClass = computed(() => {
  return !expand.value && props.colNumber < 4 ? 'justify-start' : 'justify-end';
});

const search = async (event = 'search') => {
  try {
    await unref(formRef).validate();
    const filters = getFilters();
    emit(event, !!filters.length ? { filters } : {});
  } catch (error) {
    console.error(error);
  }
};

function getFilters() {
  const values = unref(formRef).getValues();
  return Object.entries(values)
    .filter(([key, value]) => {
      if (!(!!key && !$utils.isNil(value) && !!operateMap.value.get(key))) return false;
      if ($utils.isArray(value)) return !!value.filter(i => !!i).length;
      return true;
    })
    .map(([key, value]) => {
      const newItem = {
        field: key,
        operate: operateMap.value.get(key) || '',
        value: value
      };
      const field = props.queryFields.find(item => item.field === key);
      const type = field.type.toUpperCase();
      if (type === 'REGION') {
        Object.assign(newItem, { field: Object.keys(value)[0], value: Object.values(value)[0] });
      }
      if ($utils.isArray(value)) {
        if (type === 'NUMBERRANGE') {
          Object.assign(newItem, { value: [value[0] || '0', value[1] || '9999999999'].join(',') });
        } else if (type === 'CONTACTS') {
          Object.assign(newItem, {
            value: value
              .map(i => i.id)
              .filter(i => !!i)
              .join(',')
          });
        } else {
          Object.assign(newItem, { value: value.filter(i => !!i).join(',') });
        }
      }

      if (type === 'CASCADE') {
        const [field, cascadeValue] = Object.entries(value)[0];
        Object.assign(newItem, { field, value: cascadeValue });
      }
      return newItem;
    });
}

const reset = async () => {
  unref(formRef).resetFields();
  // emit('reset');
};

const setVisible = () => {
  expand.value = !unref(expand);
};

defineExpose({
  getValues: () => unref(formRef).getValues(),
  setValues: values => unref(formRef).setValues(values),
  resetFields: () => unref(formRef).resetFields()
});
</script>

<style lang="less" scoped>
.card {
  padding: 2px 16px;
}
.funi-search-form__card {
  :deep(.el-card__body) {
    padding-bottom: 2px;
  }
}
</style>
