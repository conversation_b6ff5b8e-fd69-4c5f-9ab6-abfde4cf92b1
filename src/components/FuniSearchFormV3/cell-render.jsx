import SearchFormDateItem from './cell/searchFormDateItem.vue';
import SearchFormInputItem from './cell/searchFormInputItem.vue';
import SearchFormSelectItem from './cell/searchFormSelectItem.vue';
import SearchFormBooleanItem from './cell/searchFormBooleanItem.vue';
import SearchFormRegionItem from './cell/searchFormRegionItem.vue';
import SearchFormCascaderItem from './cell/searchFormCascaderItem.vue';
import SearchFormInputNumberItem from './cell/searchFormInputNumberItem.vue';
import SearchFormAutocompleteItem from './cell/searchFormAutocompleteItem.vue';
import SearchFormInputNumberRangeItem from './cell/searchFormInputNumberRangeItem.vue';
import SearchFormContactsItem from './cell/searchFormContactsItem/index.vue';

const cellMap = {
  DATE: () => <SearchFormDateItem />,
  DATERANGE: () => <SearchFormDateItem range={true} />,
  TEXT: () => <SearchFormInputItem />,
  SELECT: () => <SearchFormSelectItem />,
  BOOLEAN: () => <SearchFormBooleanItem />,
  REGION: () => <FuniSearchRegion />,
  NUMBER: () => <SearchFormInputNumberItem />,
  AUTOCOMPLETE: () => <SearchFormAutocompleteItem />,
  COMBO: () => <SearchFormAutocompleteItem />,
  NUMBERRANGE: () => <SearchFormInputNumberRangeItem />,
  CASCADE: () => <SearchFormCascaderItem />,
  CONTACTS: () => <SearchFormContactsItem />
};

/**
 * 内置渲染器
 */
const innerRenderKeys = [
  'DATE',
  'TEXT',
  'SELECT',
  'REGION',
  'NUMBER',
  'NUMBERRANGE',
  'AUTOCOMPLETE',
  'CASCADE',
  'COMBO',
  'BOOLEAN',
  'CONTACTS'
];

export const cellRender = {
  get(name) {
    return cellMap[name] || null;
  },

  add(name, render) {
    if (name && !innerRenderKeys.includes(name) && render) {
      cellMap[name] = render;
    }
    return cellRender;
  }
};
