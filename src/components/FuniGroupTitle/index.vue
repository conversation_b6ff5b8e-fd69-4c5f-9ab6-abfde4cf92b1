<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-08-14 19:55:24
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-04-01 18:18:45
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniGroupTitle\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="funi-group-title"
    :style="borderStyle">
    <slot :title="titleComputed"
      :titleStyle="titleStyle">
      <div class="title"
        :style="titleStyle">
        {{ titleComputed }}
      </div>
    </slot>
  </div>
</template>

<script>
import { computed } from 'vue'
export default {
  name: 'FuniGroupTitle',
  components: {

  },
  props: {
    modelValue: [String],
    title: [String],
    titleStyle: {
      type: Object,
      default: () => {
        return {
          marginLeft: '8px',
          fontSize: '18px'
        }
      }
    },
    groupMargin: {
      type: String,
      default: '16px 0px'
    }
  },
  setup (props) {
    const titleComputed = computed(() => {
      return props.modelValue ?? props.title;
    })
    const borderStyle = { margin: props.groupMargin };
    if (props.titleStyle && props.titleStyle.color) {
      borderStyle['border-left-color'] = props.titleStyle.color;
    }
    return {
      titleComputed,
      borderStyle
    }
  }
}
</script>
<style lang="scss" scoped>
.funi-group-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-left: 4px solid;
  line-height: 24px;
  color: var(--el-color-primary);
  border-left-color: var(--el-color-primary);
}
</style>