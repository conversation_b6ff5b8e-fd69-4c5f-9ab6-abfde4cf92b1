<!--表单填写流程底部按钮-->
<template>
  <div v-if="currentStep <= submitStep && currentStep >= 0"
    class="btnGroup"
    :style="{ padding: '0px 12px',height:'50px' }">
    <div>
      <!-- 返回 -->
      <el-button style="margin-right: 10px"
        type="primary"
        v-for="button in backButtons"
        :key="button.value || button.key"
        v-bind="button.props || {}"
        @click="clickBtnEvent(button)">
        {{ button.key }}
      </el-button>
      <el-button v-if="showLastStep && currentStep > 0"
        :style="{ marginRight: preservable ? '10px' : '0px' }"
        :disabled="disabledFunc"
        @click="clickBtnEvent({ value: 'lastStep', type: 'lastStep' })">
        上一步
      </el-button>
    </div>
    <el-button v-show="preservable"
      :disabled="disabledFunc"
      @click="clickBtnEvent({ type: 'ts', value: 'ts' })">暂存</el-button>
    <el-button v-show="currentStep < submitStep"
      class="ml-10"
      type="primary"
      :disabled="disabledFunc"
      @click="clickBtnEvent({ type: 'nextStep', value: 'nextStep' })">
      下一步
    </el-button>
    <!-- 提交 -->
    <el-button class="ml-10"
      type="primary"
      v-for="button in computedSubmitButtons"
      :key="button.value || button.key"
      :disabled="disabledFunc"
      v-bind="button.props || {}"
      @click="clickBtnEvent(button)">
      {{ button.key }}
    </el-button>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    submitStep: {
      //提交步
      type: Number,
      default: 0
    },
    currentStep: {
      //当前步
      type: Number,
      default: 0
    },
    preservable: {
      type: Boolean,
      default: false
    },
    isNeedSubmit: {
      type: Boolean,
      default: true
    },
    //显示上一步
    showLastStep: {
      type: Boolean,
      default: true
    },
    backButtons: {
      type: Array,
      default: () => []
    },
    submitButtons: {
      type: Array,
      default: () => [{ key: '提交', value: 'submit', type: 'submit', props: {} }]
    },
    loadingStatus: {
      type: Object,
      default: {
        type: void 0,
        status: false
      }
    }
  },
  created () { },
  data () {
    return {};
  },
  computed: {
    computedSubmitButtons () {
      if (this.currentStep != this.submitStep || !this.isNeedSubmit) return [];
      return this.submitButtons;
    },
    disabledFunc () {
      return this.loadingStatus.type && this.loadingStatus.status === true;
    }
  },
  mounted () { },
  methods: {
    //点击事件回传
    clickBtnEvent (button) {
      this.$emit('clickBtnEvent', button);
    }
  }
};
</script>

<style scoped lang="scss">
.btnGroup {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: space-between;
}
</style>
