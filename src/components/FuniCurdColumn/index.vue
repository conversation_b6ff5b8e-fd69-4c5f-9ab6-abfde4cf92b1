<template>
  <el-table-column v-bind="column">
    <template v-if="column.slots?.header" #header>
      <slot :name="column.slots?.header"></slot>
    </template>
    <template #default="scope">
      <FuniCurdColumn
        v-if="column.children?.length"
        v-for="childrenColumn in column.children"
        :key="childrenColumn.prop"
        :column="childrenColumn"
      ></FuniCurdColumn>
      <slot v-else-if="column.slots?.default" :name="column.slots?.default" v-bind="scope" />
      <component v-else-if="column.render" :is="contentRender(column.render, scope)" />
      <template v-else-if="!['selection', 'index', 'expand'].includes(column.type)">
        {{ defaultRenderCell(scope) }}
      </template>
    </template>
  </el-table-column>
</template>

<script setup lang="jsx">
import { isVNode, unref } from 'vue';

defineOptions({
  name: 'FuniCurdColumn',
  inheritAttrs: false
});

const props = defineProps({
  column: { type: Object, default: () => ({}) }
});

const getProp = (obj, path, defaultValue) => {
  return {
    get value() {
      return $utils.get(obj, path, defaultValue);
    },
    set value(val) {
      $utils.set(obj, path, val);
    }
  };
};

function contentRender(render, scope) {
  const content = render({ row: unref(scope.row), index: scope.$index });
  return isVNode(content) ? content : <span>{content || '--'}</span>;
}

function defaultRenderCell({ row, column, $index }) {
  const property = column.property;
  const value = property && getProp(row, property).value;
  if (column && column.formatter && typeof column.formatter === 'function') {
    return column.formatter(row, column, value, $index) || '--';
  }
  return $utils.isNil(value) ? '--' : value.toString();
}
</script>
