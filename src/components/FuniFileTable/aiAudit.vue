<template id="aiAudit">
  <el-dialog
    v-model="dialogFormVisible"
    :destroy-on-close="true"
    @close="cancel"
    title="智能审批"
    width="80%"
    style="--el-dialog-margin-top: 5vh; margin: var(--el-dialog-margin-top, 5vh) auto 50px"
  >
    <div class="groupView">
      <div class="leftView">
        <FuniPreivew
          ref="fp"
          :url="fileParams.fileUrl"
          :title="fileParams.attachmentName"
          :type="fileParams.attachmentFileType"
        ></FuniPreivew>
      </div>
      <div class="rightView">
        <el-space direction="vertical" :fill-ratio="100" fill>
          <el-alert title="本信息由人工智能读取生成，用于辅助要件审批，仅供参考！" type="warning" :closable="false" />
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <span>AI摘要信息</span>
                <div v-if="abstractRefresh" class="right-header">
                  <el-input
                    v-model="abstractKeyword"
                    style="width: 240px"
                    placeholder="指定提取信息，重新生成摘要"
                  ></el-input>
                  <el-button type="primary" text @click="getAbstract(true)">重新生成</el-button>
                </div>
              </div>
            </template>
            <div class="textItem" v-html="abstractText" @click="handleClick"></div>
          </el-card>
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <span>AI预审结果</span>
                <div v-if="resultRefresh" class="right-header">
                  <el-input
                    v-model="resultKeyword"
                    style="width: 240px"
                    placeholder="指定预审信息，重新生成预审结果"
                  ></el-input>
                  <el-button type="primary" text @click="getResult(true)">重新生成</el-button>
                </div>
              </div>
            </template>
            <div class="textItem" v-html="resultText || resultLoadingText"></div>
          </el-card>
        </el-space>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-alert v-if="reason" :title="reason" type="error" center show-icon :closable="false" />
        <div v-else>
          <el-button @click="cancel">取消</el-button>
          <el-button
            type="primary"
            :disabled="!resultText || !resultRefresh || cLoading"
            :loading="cLoading"
            @click="createReason"
            >生成退件原因</el-button
          >
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { marked } from 'marked';
import FuniPreivew from '../FuniPreview/index.vue';
import { fetchEventSource } from '@microsoft/fetch-event-source';

class FatalError extends Error {}
const findConfigUrl = '/ai/bus/config'; //获取配置信息
const findContextUrl = '/ai/review/fileContent'; //获取文件文本内容
// const findAbstractUrl = '/ai/review/digest'; //获取文件摘要信息
const findAbstractUrl = '/ai/review/flux/digest'; //获取文件摘要信息
// const findResultUrl = '/ai/review/comparison'; //获取预审结果
const findResultUrl = '/ai/review/flux/comparison'; //获取预审结果
const createReasonUrl = '/ai/review/conclusion'; //根据预审结果生产原因
const participleUrl = '/ai/review/participle'; //分词

const dialogFormVisible = ref(false);
const abstractRefresh = ref(false);
const resultRefresh = ref(false);
const cLoading = ref(false);
const config = ref({});
const fileParams = ref({});
const emit = defineEmits(['confirm']);
const fp = ref();
const props = defineProps({
  //ai辅助数据
  aiAuditData: {
    type: Object,
    default: () => ({})
  }
});
//摘要信息
const abstractText = ref();
//预审信息
const resultText = ref();
const resultLoadingText = ref();
//退件原因
const reason = ref();
//摘要信息关键字
const abstractKeyword = ref('');
//预审信息关键字
const resultKeyword = ref('');
//摘要信息定时器
let abstractInterval = null;
//预审信息定时器
let resultInterval = null;

//显示
const show = async params => {
  config.value = {};
  reason.value = '';
  abstractText.value = '';
  resultText.value = '';
  abstractKeyword.value = '';
  resultKeyword.value = '';
  fileParams.value = params;
  dialogFormVisible.value = true;
  cLoading.value = false;
  await getConfig();
  getData();
};

//获取配置信息
const getConfig = async () => {
  let res = await $http.fetch(findConfigUrl, {
    busCode: fileParams.value.fileConfigCode
  });
  if (res) {
    config.value = { ...res, params: res.params ? JSON.parse(res.params) : [] };
  }
};

//获取数据
const getData = async () => {
  let res = await $http.post(findContextUrl, {
    id: `${fileParams.value.businessId}-${fileParams.value.attachmentInfoId}`,
    files: [
      {
        fileName: fileParams.value.attachmentName,
        fileId: fileParams.value.attachmentPath,
        fileType: fileParams.value.attachmentFileType,
        refresh: false
      }
    ]
  });
  if (res && res.length > 0) {
    getAbstract();
    getResult();
  }
};

//获取摘要信息
const getAbstract = async (refresh = false) => {
  clearInterval(abstractInterval);
  let response = '';
  let html;
  let end = false;
  // 创建一个新的 EventSource 实例
  fetchEventSource(
    `${window.location.origin}/${
      process.env.NODE_ENV === 'development' ? 'api/' : ''
    }${$utils.getTenantID()}${findAbstractUrl}?fp_rat=${sessionStorage.getItem('token')}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: `${fileParams.value.businessId}-${fileParams.value.attachmentInfoId}`,
        type: `${config.value.type || ''}`,
        stats: `${abstractKeyword.value || config.value.stats || ''}`,
        refresh: refresh
      }),
      onmessage(ev) {
        console.log(ev);
        if (ev.event === 'FatalError') {
          throw new FatalError(ev.data);
        } else {
          // 处理数据展示
          response += ev.data;
        }
      },
      onclose() {
        console.log('连接关闭');
        end = true;
      },
      onerror(err) {
        console.error('连接错误', err);
        end = true;
        if (err instanceof FatalError) {
          throw err; // rethrow to stop the operation
        }
      }
    }
  );

  let text = '';
  let index = 0;
  let loadingText = '';
  let speed = 80;
  async function typeCharacter() {
    if (response.length === 0) {
      if (loadingText.length < 5) {
        loadingText += '.';
      } else {
        loadingText = '';
      }
      abstractText.value = loadingText;
    } else if (index < response.length) {
      text += response[index];
      html = marked(text);
      abstractText.value = html;
      index++;
    } else if (end) {
      clearInterval(abstractInterval); // 停止打字
      abstractText.value = await replaceTextWithLinks(html, text);
      abstractRefresh.value = true;
    }
  }

  abstractInterval = setInterval(typeCharacter, speed);
};

//添加关键词点击事件
const replaceTextWithLinks = async (html, text) => {
  // // 使用正则表达式匹配最后一个中文字符串
  // const lastChineseTitle = title => {
  //   const chineseMatch = title.match(/[^\.]+$/); // 匹配最后一个`.`之后的所有字符
  //   return chineseMatch ? chineseMatch[0].trim() : ''; // 返回去掉前后空格的匹配结果
  // };
  // // 映射并过滤标题
  // const matchTitles = config.map(item => lastChineseTitle(item.title)).filter(title => title);
  function filterStrings(strings) {
    // 创建一个数组来存储最终的结果
    const result = [];

    // 遍历数组中的每个字符串
    for (let i = 0; i < strings.length; i++) {
      const currentString = strings[i];
      // 标记该字符串是否为其他字符串的子串
      let isSubstring = false;

      // 检查该字符串是否是数组中其他字符串的子串
      for (let j = 0; j < strings.length; j++) {
        if (i !== j && strings[j].includes(currentString)) {
          isSubstring = true;
          break;
        }
      }

      // 如果它不是其他字符串的子串，则将其添加到结果数组中
      if (!isSubstring) {
        result.push(currentString);
      }
    }
    return result;
  }

  let arr = [];
  let data = await $http.fetch(participleUrl, {
    content: text
  });
  if (data) {
    arr = JSON.parse(data);
    arr = Array.from(new Set(arr));
    arr = filterStrings(arr);
  }

  // 如果没有需要匹配的title，则直接返回原始html
  if (arr.length === 0) {
    return html;
  }
  let newHtml = html;
  for (let title of arr) {
    const link = `<a href="#${title}" style="color: black">${title}</a>`;
    newHtml = newHtml.replaceAll(title, link);
  }
  // 使用replace方法和回调函数来替换匹配到的文本

  return newHtml;
};

//获取预审结果
const getResult = async (refresh = false) => {
  clearInterval(resultInterval);
  resultText.value = '';
  let response = '';
  let end = false;
  // let bus = encodeURI(JSON.stringify(transformData(props.aiAuditData, config.value.params)));
  // 创建一个新的 EventSource 实例
  fetchEventSource(
    `${window.location.origin}/${
      process.env.NODE_ENV === 'development' ? 'api/' : ''
    }${$utils.getTenantID()}${findResultUrl}?fp_rat=${sessionStorage.getItem('token')}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: `${fileParams.value.businessId}-${fileParams.value.attachmentInfoId}`,
        type: `${config.value.type || ''}`,
        stats: `${resultKeyword.value || config.value.stats || ''}`,
        bus: document.querySelector('.funi-detail').innerHTML,
        refresh: refresh
      }),
      onmessage(ev) {
        console.log(ev);
        if (ev.event === 'FatalError') {
          throw new FatalError(ev.data);
        } else {
          // 处理数据展示
          response += ev.data;
        }
      },
      onclose() {
        console.log('连接关闭');
        end = true;
      },
      onerror(err) {
        console.error('连接错误', err);
        end = true;
        if (err instanceof FatalError) {
          throw err; // rethrow to stop the operation
        }
      }
    }
  );

  let text = '';
  let index = 0;
  let loadingText = '';
  let speed = 80;
  function typeCharacter() {
    if (response.length === 0) {
      if (loadingText.length < 5) {
        loadingText += '.';
      } else {
        loadingText = '';
      }
      resultLoadingText.value = loadingText;
    } else if (index < response.length) {
      text += response[index];
      resultText.value = marked(text);
      index++;
    } else if (end) {
      resultRefresh.value = true;
      clearInterval(resultInterval); // 停止打字
    }
  }

  resultInterval = setInterval(typeCharacter, speed);
};

//生成退件原因
const createReason = async () => {
  cLoading.value = true;
  let response = await $http.post(createReasonUrl, {
    id: `${fileParams.value.businessId}-${fileParams.value.attachmentInfoId}`,
    busId: fileParams.value.businessId,
    content: resultText.value,
    refresh: false
  });
  reason.value = response;
  cLoading.value = false;
};

//转化获取bus数据
function transformData(businessData, config) {
  const result = {};

  config.forEach(item => {
    const keys = item.key.split('.');
    const title = item.title.split('.');
    const valuePath = keys.slice(0, -1); // 除去最后一个key，用于定位数组或对象
    const valueKey = keys[keys.length - 1]; // 最后一个key，用于提取值
    const titlePath = title.slice(0, -1); // 除去最后一个title部分，用于构建嵌套对象
    const finalKey = title[title.length - 1]; // 最后一个title部分，作为新对象的键

    // 遍历businessData以找到正确的位置
    let currentData = businessData;
    let currentResult = result;
    let lastTitleSegment = ''; // 用于记录上一个title段，以便在需要时创建新的嵌套对象

    valuePath.forEach((key, index) => {
      if (currentData[key] !== undefined) {
        currentData = currentData[key];

        // 更新lastTitleSegment，用于后续可能的嵌套对象创建
        lastTitleSegment = titlePath[index] || '';

        // 如果还有剩余的title路径，并且当前段与上一个段不同（避免重复嵌套），则继续嵌套
        if (
          index < titlePath.length - 1 &&
          titlePath[index + 1] &&
          titlePath[index] !== titlePath[index + 1].split('.')[0]
        ) {
          if (!(lastTitleSegment in currentResult)) {
            currentResult[lastTitleSegment] = {};
          }
          currentResult = currentResult[lastTitleSegment];
        }
      } else {
        // 如果路径中的任何一部分不存在，则跳过此配置项
        return;
      }
    });

    // 处理最后一个title段（可能是嵌套对象的根）
    const lastTitle = lastTitleSegment || (titlePath.length > 0 ? titlePath.join('.') : '');
    if (lastTitle && !(lastTitle in currentResult)) {
      currentResult[lastTitle] = {};
    }
    currentResult = currentResult[lastTitle] || result; // 如果lastTitle已经存在，则继续在其下设置值，否则回到result顶层（理论上不会发生）

    // 如果currentData现在是数组，我们需要遍历数组并提取每个元素的valueKey
    if (Array.isArray(currentData)) {
      currentResult[finalKey] = currentData.map(item => ({ [finalKey]: item[valueKey] }));
    } else if (currentData[valueKey] !== undefined) {
      // 直接提取值并放入结果对象中
      currentResult[finalKey] = currentData[valueKey];
    }
  });

  return result;
}

//拦截html中的摘要关键词点击事件
const handleClick = event => {
  // 检查点击的元素是否是 <a> 标签
  if (event.target.tagName.toLowerCase() === 'a') {
    // 阻止默认行为（即链接跳转）
    event.preventDefault();
    const text = event.target.innerText;
    fp.value.highLightKeyword(text, true);
    console.log('被点击的文字:', text);
  }
};

//取消
const cancel = function () {
  clearInterval(abstractInterval);
  clearInterval(resultInterval);
  dialogFormVisible.value = false;
};

defineExpose({
  show
});
</script>

<style lang="scss" scoped>
.groupView {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .leftView {
    width: 50%;
  }

  .rightView {
    width: calc(50% - 18px);
    display: flex;
    flex-direction: column;

    .box-card {
      .card-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .right-header {
          display: flex;
          flex-direction: row;
          justify-content: flex-end;
          align-items: center;
        }
      }

      .textItem {
        height: 23vh;
        overflow-y: auto;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
</style>
