<!--
 * @Author: 古加文 <EMAIL>
 * @Date: 2023-06-05 17:14:02
 * @LastEditors: 古加文 <EMAIL>
 * @LastEditTime: 2024-12-24 09:55:37
 * @FilePath: \src\components\FuniFileTable\upload.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    @close="cancel"
    :close-on-press-escape="false"
    title="文件上传"
    width="30%"
    center
  >
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      drag
      :action="url"
      :data="uploadData"
      :accept="uploadAccept"
      :http-request="request"
      multiple
      :on-error="onError"
      :on-success="onSuccess"
      :before-remove="beforeRemove"
      :on-change="onChange"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">拖拽文件 或 <em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip">
          {{ uploadAcceptText ? `${uploadAcceptText}` : '' }}
          {{ uploadAcceptText && needNumText ? `,${needNumText}` : needNumText }}
        </div>
      </template>
    </el-upload>
    <!-- <template #footer>
            <span class="dialog-footer">
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="confirm">
                    确定
                </el-button>
            </span>
        </template> -->
  </el-dialog>
</template>

<script setup>
import { UploadFilled } from '@element-plus/icons-vue';
import { ref } from 'vue';
import { ElNotification } from 'element-plus';
const props = defineProps({
  url: String,
  type: String,
  removeFile: Function
});

const dialogVisible = ref(false);

const uploadRef = ref();
const uploadData = ref({});
const uploadAccept = ref();
const uploadAcceptText = ref();
const needNumText = ref();
const defaultFileList = ref();
const allFileList = ref();

const emit = defineEmits(['on-close']);

const onError = function (error, uploadFile, uploadFiles) {
  // ElNotification({
  //   title: '上传失败',
  //   message: error.message,
  //   type: 'error'
  // });
  uploadFiles.splice(
    uploadFiles.findIndex(item => item.uid == uploadFile.uid),
    1
  );
  allFileList.value.splice(
    allFileList.value.findIndex(item => item.uid == uploadFile.uid),
    1
  );
};

const onChange = function (uploadFile, uploadFiles) {
  if (uploadFile.status == 'ready') {
    //添加准备上传文件 判断容器和外部文件中是否有重复文件
    let isRepeat = allFileList.value.some(obj => obj.name == uploadFile.name || obj.attachmentName == uploadFile.name);
    if (isRepeat) {
      ElNotification({
        title: '附件重复，请重新选择',
        type: 'warning'
      });
      uploadFiles.splice(
        uploadFiles.findIndex(item => item.uid == uploadFile.uid),
        1
      );
      uploadRef.abort(uploadFile);
      return;
    }
    uploadFile.status = 'uploading';
    uploadFile.percentage = 30;
    allFileList.value = defaultFileList.value.concat(uploadFiles);
  }
};

const onSuccess = function (response, uploadFile, uploadFiles) {
  uploadFile.attachmentInfoId = response.attachmentInfoId;
  uploadFile.status = 'uploading';
  uploadFile.percentage = 75;
  setTimeout(() => {
    uploadFile.percentage = 90;
    setTimeout(() => {
      uploadFile.status = 'success';
    }, 400);
  }, 400);
};

const request = function (params) {
  let formData = new FormData();
  formData.append('file', params.file);
  for (const [key, value] of Object.entries(params.data)) {
    formData.append(key, value);
  }
  return $http.upload(params.action, formData);
};

const show = function (params = {}, accept = '', needNum, fileList = []) {
  uploadData.value = params;
  defaultFileList.value = fileList;
  allFileList.value = fileList;
  uploadAcceptText.value = accept ? accept.split(',').join('/') : '';
  needNumText.value = needNum ? `需要${needNum}份` : '';
  uploadAccept.value = accept
    ? accept
        .split(',')
        .map(item => '.' + item)
        .join(',')
    : '';
  dialogVisible.value = true;
};

const cancel = function () {
  emit('on-close');
  uploadRef.value.clearFiles();
  dialogVisible.value = false;
};

const confirm = function () {
  emit('on-close');
  uploadRef.value.clearFiles();
  dialogVisible.value = false;
};

const beforeRemove = async function (uploadFile, uploadFiles) {
  if (props.removeFile) {
    let result = await props.removeFile(uploadFile);
    if (result) {
      allFileList.value.splice(
        allFileList.value.findIndex(item => item.uid == uploadFile.uid),
        1
      );
    }
    return result;
  }
  return false;
};

defineExpose({
  show
});
</script>

<style lang="scss" scoped></style>
