<template>
  <div class="headBtn">
    <el-button v-if="!onlyShow && !hideAddBtn" type="primary" @click="_addEvent">添加</el-button>
  </div>
  <div id="FileCurd">
    <funi-curd
      ref="FuniFileCurdRef"
      rowKey="businessFileInstanceInfoId"
      :columns="columns"
      :loading="loading"
      :lodaData="_lodaData"
      :pagination="false"
      default-expand-all
      :header-cell-style="{ 'background-color': '#f9f9f9' }"
      :header-row-style="{ 'background-color': '#f9f9f9' }"
    >
      <template #fileCurd="props">
        <div id="fileCurdView">
          <funi-curd
            rowKey="attachmentInfoId"
            :show-header="false"
            :columns="columns2"
            :data="
              props.row.attachmentInfos.map(item =>
                Object.assign(item, {
                  index: props.$index,
                  isByAiCheck: props.row.isByAiCheck,
                  isEdit: props.row.isEdit,
                  fileConfigCode: props.row.fileConfigCode
                })
              )
            "
            :pagination="false"
            :stripe="false"
          ></funi-curd>
        </div>
      </template>
    </funi-curd>
  </div>
  <funi-image-view ref="fivRef" :key="imageKey" :urlList="urlList"></funi-image-view>
  <upload
    ref="uploadRef"
    :url="props.uploadFileUrl || uploadFileUrl"
    :remove-file="_deleteFile"
    @on-close="_refreshData"
  >
  </upload>
  <add ref="addRef" @confirm="_refreshData"></add>
  <aiAudit ref="aiAuditRef" :aiAuditData="aiAuditData" @confirm="_refreshData"></aiAudit>
</template>

<script setup lang="jsx">
import { nextTick, ref, unref, getCurrentInstance, computed } from 'vue';
import upload from './upload.vue';
import add from './add.vue';
import aiAudit from './aiAudit.vue';
import { useAppStore } from '@/stores/useAppStore';
import { ElNotification, ElLoading } from 'element-plus';
import xss from 'xss';

defineOptions({
  name: 'FuniFileTable',
  inheritAttrs: false
});

const instance = getCurrentInstance();
const appStore = useAppStore();
const props = defineProps({
  //仅显示
  onlyShow: {
    type: Boolean,
    default: false
  },
  //隐藏下载按钮
  hideDownload: {
    type: Boolean,
    default: false
  },
  //隐藏添加按钮
  hideAddBtn: {
    type: Boolean,
    default: false
  },
  //隐藏列字段集合
  hideColumns: {
    type: Array,
    default: []
  },
  //查看按钮点击方法
  checkFileFn: Function,
  params: { type: Object, default: {} },
  //附件列表查询接口地址
  fileListUrl: String,
  //删除附件接口地址
  delFileUrl: String,
  //上传附件接口地址
  uploadFileUrl: String,
  //上传附件额外参数
  uploadFileParams: {
    type: Object,
    default: () => ({})
  },
  //预览附件接口地址
  checkFileUrl: String,
  //预览附件接口参数
  checkFileParams: {
    type: Object,
    default: () => ({})
  },
  //下载附件接口地址
  downloadFileUrl: String,
  //下载附件接口入参
  downloadParams: {
    type: Object,
    default: () => ({})
  },
  //是否校验
  isVerification: { type: Boolean, default: true },
  callbackFun: {
    type: Function,
    default: () => {}
  },
  //ai辅助数据
  aiAuditData: {
    type: Object,
    default: () => ({})
  },
  //强制允许编辑要件编码数组
  forceEditFileCode: {
    type: Array,
    default: () => []
  }
});

//查询要件
const fileListUrl = '/bpmn/fileManage/queryBusinessFileInstanceList';
//删除文件
const delFileUrl = '/bpmn/fileManage/deleteAttachment';
//上传文件
const uploadFileUrl = '/bpmn/fileManage/uploadAttachment';
//下载文件
const downloadFileUrl = '/bpmn/businessManage/downloadAttachmentForFile';
//查看文件
const checkFileUrl = '/bpmn/fileManage/downloadAttachment';
//删除要件类型
const delFileType = '/bpmn/fileManage/delBusinessFileInstance';

const loading = ref(false);
const sysId = ref(props.params.sysId);
const fivRef = ref();
const uploadRef = ref();
const addRef = ref();
const aiAuditRef = ref();
const FuniFileCurdRef = ref();
const urlList = ref([]);
const imageKey = ref($utils.guid());
const aiFileType = [
  'img',
  'IMG',
  'jpg',
  'JPG',
  'png',
  'PNG',
  'jpeg',
  'JPEG',
  'pdf',
  'PDF',
  'doc',
  'DOC',
  'docx',
  'DOCX',
  'ppt',
  'PPT',
  'pptx',
  'PPTX'
];

const columns = ref([
  { type: 'expand', slots: { default: 'fileCurd' }, width: '50' },
  {
    label: '附件类型',
    prop: 'businessFileName',
    width: '450',
    render: ({ row, index }) => {
      if (row.isNeed == '1' || row.isNeed == 1) {
        return (
          <div>
            <span style="color:red">*</span>
            {row.businessFileName}
          </div>
        );
      } else {
        return row.businessFileName;
      }
    }
  },
  { label: '支持格式', prop: 'fileType', width: '300' },
  {
    label: '是否必收',
    prop: 'isNeed',
    render: ({ row, index }) => (row.isNeed == '1' || row.isNeed == 1 ? '是' : '否')
  },
  { label: '需要份数', prop: 'needNumber' },
  { label: '已传份数', prop: 'attachmentAmount' },
  { label: '备注', prop: 'remark', width: '180' }
]);

columns.value.push({
  label: '操作',
  prop: 'operList',
  align: 'center',
  fixed: 'right',
  width: '180px',
  render: ({ row, index }) => {
    if (
      (!props.onlyShow && (row.isEdit || row.isEdit == undefined)) ||
      props.forceEditFileCode.includes(row.fileConfigCode)
    ) {
      return (
        <div>
          <el-button
            type="primary"
            link
            onClick={() => {
              _upload(row);
            }}
          >
            上传
          </el-button>
          {row.isCustom ? (
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                _delete(row);
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            ></el-popconfirm>
          ) : null}
        </div>
      );
    } else {
      return ' ';
    }
  }
});

//过滤隐藏列
columns.value = columns.value.filter(item => !props.hideColumns.includes(item.prop));
//如果弹性宽度列都被隐藏则设置第一列宽度弹性
if (
  props.hideColumns.includes('isNeed') &&
  props.hideColumns.includes('needNumber') &&
  props.hideColumns.includes('attachmentAmount')
) {
  delete columns.value[1].width;
}

const columns2 = computed(() => {
  return [
    {
      label: '1',
      prop: '1',
      width: '50',
      render: () => {
        return ' ';
      }
    },
    {
      label: '文件名',
      prop: 'attachmentName',
      render: ({ row, index }) => {
        return (
          <div>
            <el-button
              type="primary"
              link
              onClick={() => {
                _check(row);
              }}
            >
              {row.attachmentName}
            </el-button>
          </div>
        );
      }
    },
    {
      label: '操作',
      prop: 'operList',
      align: 'center',
      // fixed: 'right',
      width: '200',
      render: ({ row, index }) => {
        return (
          <div>
            {Object.keys(props.aiAuditData).length > 0 &&
            row.isByAiCheck &&
            aiFileType.includes(row.attachmentFileType) ? (
              <el-button
                type="primary"
                link
                size="small"
                onClick={() => {
                  _aiAudit(row);
                }}
              >
                智能审批
              </el-button>
            ) : null}
            <el-button
              type="primary"
              link
              size="small"
              onClick={() => {
                _check(row);
              }}
            >
              查看
            </el-button>
            {!props.hideDownload ? (
              <el-button
                type="primary"
                link
                size="small"
                onClick={() => {
                  _download(row);
                }}
              >
                下载
              </el-button>
            ) : null}
            {(!props.onlyShow && (row.isEdit || row.isEdit == undefined)) ||
            props.forceEditFileCode.includes(row.fileConfigCode) ? (
              <el-popconfirm
                title="确认删除该条数据吗？"
                onConfirm={() => {
                  _deleteFile(row, true);
                }}
                v-slots={{
                  reference: () => (
                    <el-button type="danger" link size="small">
                      删除
                    </el-button>
                  )
                }}
              ></el-popconfirm>
            ) : null}
          </div>
        );
      }
    }
  ];
});

//添加附件类型
const _addEvent = async () => {
  addRef.value.show({ businessId: props.params?.businessId, sysId: sysId.value });
};

//ai智能审批
const _aiAudit = async row => {
  let params = {
    attachmentInfoId: row.attachmentInfoId,
    ...props.checkFileParams
  };
  //文件预览url
  let fileCheckUrl = `${window.location.origin}/${$utils.getTenantID()}${
    props.checkFileUrl || checkFileUrl
  }?${json2GetParams(params)}`;
  aiAuditRef.value.show({
    businessId: props.params?.businessId,
    sysId: sysId.value,
    fileUrl: fileCheckUrl,
    ...row
  });
};

//获取附件列表
const _lodaData = async () => {
  if (props.params?.businessId || props.params?.businessConfigCode || props.params?.preBusinessId) {
    loading.value = true;
    if (!sysId.value) {
      let sysInfo = appStore.system;
      sysId.value = sysInfo.id;
    }
    /**
     * 入参场景：
     * 1：已生成业务件查询需提供businessId/sysId
     * 2：未生成业务件查询需提供businessConfigCode/sysId
     * 3：继承上比业务件查询需提供preBusinessId/preSysId
     */
    return $http
      .post(props.fileListUrl || fileListUrl, {
        businessId: props.params.businessId,
        businessConfigCode: props.params.businessConfigCode,
        preBusinessId: props.params.preBusinessId,
        preSysId: props.params.preSysId || sysId.value,
        sysId: sysId.value
      })
      .then(res => {
        return res;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

//上传文件
const _upload = row => {
  uploadRef.value.show(
    {
      businessFileInstanceInfoId: row.businessFileInstanceInfoId,
      fileConfigCode: row.fileConfigCode,
      businessId: props.params?.businessId,
      sysId: sysId.value,
      ...props.uploadFileParams
    },
    row.fileType,
    row.needNumber,
    row.attachmentInfos
  );
};

//刷新数据
const _refreshData = () => {
  FuniFileCurdRef.value.reload();
};

//下载文件
const _download = row => {
  const loading = ElLoading.service({ fullscreen: true });
  let url = props.downloadFileUrl
    ? `${window.location.origin}/${$utils.getTenantID()}${props.downloadFileUrl}`
    : `${window.location.origin}/${$utils.getTenantID()}${downloadFileUrl}?attachmentInfoId=${row.attachmentInfoId}`;
  window.$http
    .downloadFile(
      url,
      props.downloadFileUrl
        ? {
            attachmentInfoId: row.attachmentInfoId,
            ...props.downloadParams
          }
        : {}
    )
    .then(() => {
      loading.close();
    })
    .catch(() => {
      loading.close();
    });
};

//查看文件
const _check = row => {
  console.log('row====>', row);
  //用户自己实现查看功能
  if (props.checkFileFn) {
    props.checkFileFn(row);
    return;
  }
  let params = {
    attachmentInfoId: row.attachmentInfoId,
    ...props.checkFileParams
  };
  let url = `${window.location.origin}/${$utils.getTenantID()}${props.checkFileUrl || checkFileUrl}?${json2GetParams(
    params
  )}`;
  if (isImage(row.attachmentFileType)) {
    imageKey.value = $utils.guid();
    nextTick(() => {
      urlList.value = [url];
      fivRef.value.showViewer();
    });
  } else {
    window.open(
      xss(
        `${window.location.href.split('#')[0]}#/common/filePreview?src=${encodeURIComponent(url)}&type=${
          row.attachmentFileType
        }`
      )
    );
  }
};

const json2GetParams = obj =>
  Object.entries(obj)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

//是否图片格式
const isImage = ext => {
  return (
    ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'gif', 'GIF', 'webp', 'WEBP', 'svg', 'SVG'].indexOf(
      ext.toLowerCase()
    ) !== -1
  );
};

//删除附件类型
const _delete = row => {
  if (row.attachmentInfos && row.attachmentInfos.length > 0) {
    ElNotification({
      title: '请先删除该类型下的附件',
      type: 'error'
    });
    return;
  }
  const loading = instance.proxy.$loading({ fullscreen: true });
  return $http
    .post(delFileType, {
      businessFileInstanceInfoId: row.businessFileInstanceInfoId,
      businessId: props.params.businessId,
      sysId: sysId.value
    })
    .then(res => {
      FuniFileCurdRef.value.reload();
      loading.close();
      return true;
    })
    .catch(() => {
      loading.close();
      return false;
    });
};

//删除附件
const _deleteFile = (row, isRefresh) => {
  const loading = instance.proxy.$loading({ fullscreen: true });
  return $http
    .post(props.delFileUrl || delFileUrl, {
      attachmentInfoId: row.attachmentInfoId,
      businessId: props.params.businessId,
      sysId: sysId.value
    })
    .then(res => {
      if (isRefresh) {
        FuniFileCurdRef.value.reload();
      }
      loading.close();
      return true;
    })
    .catch(() => {
      loading.close();
      return false;
    });
};

//提供外部调用, 验证必传件是否都有数据
const verification = () => {
  let judge = true;
  const dataList = FuniFileCurdRef.value.tableData;
  dataList &&
    dataList.forEach(item => {
      if (item.isNeed && item.attachmentInfos.length < 1) {
        judge = false;
      }
    });
  return judge;
};

const submit = async () => {
  let flag = true;
  if (props.isVerification) {
    flag = verification();
  }
  if (flag) {
    await props.callbackFun();
  } else {
    ElNotification({
      title: '请上传必传件',
      type: 'warning'
    });
  }
  return Promise.resolve({});
};

//获取所有要件类型下的文件id
const getAllFileId = () => {
  const dataList = FuniFileCurdRef.value.tableData;
  const attachmentInfoIds = [];
  for (const item of dataList) {
    if (item.attachmentInfos) {
      for (const attachmentInfo of item.attachmentInfos) {
        attachmentInfoIds.push(attachmentInfo.attachmentInfoId);
      }
    }
  }
  return attachmentInfoIds;
};

defineExpose({
  submit,
  verification,
  getAllFileId
});
</script>

<style lang="scss" scoped>
.headBtn {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding-right: 8px;
  margin-top: 16px;
}

#FileCurd {
  :deep(.funi-curd) {
    .el-table__expanded-cell {
      padding: 0px;
    }
  }
}

#fileCurdView {
  :deep(.funi-curd__header) {
    margin-bottom: 0;
  }

  :deep(.curd-header) {
    display: none;
  }

  :deep(.funi-curd) {
    padding: 0 !important;
    // padding-left: 50px !important;
  }
}
</style>
