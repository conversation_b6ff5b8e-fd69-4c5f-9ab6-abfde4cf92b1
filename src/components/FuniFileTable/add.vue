<template>
  <funi-dialog v-model="dialogFormVisible" title="添加附件类型" @confirm="_confirm">
    <FuniForm ref="file_add_form" :col="2" :schema="schema" :rules="rules"></FuniForm>
  </funi-dialog>
</template>

<script lang="jsx" setup>
import { reactive, ref, getCurrentInstance, unref, computed, toRaw } from 'vue';

const instance = getCurrentInstance();
const addUrl = '/bpmn/fileManage/addBusinessFileInstance';
const emit = defineEmits(['confirm']);
const dialogFormVisible = ref(false);
const file_add_form = ref(); // FuniForm 引用
const addData = ref({});
const rules = {
  //表单校验规则
  businessFileName: [
    {
      required: true,
      message: '必填',
      trigger: 'change'
    }
  ]
};
const options = {
  // 要件类型选项
  fileType: [
    {
      label: 'jpg',
      value: 'jpg'
    },
    {
      label: 'jpeg',
      value: 'jpeg'
    },
    {
      label: 'png',
      value: 'png'
    },
    {
      label: 'pdf',
      value: 'pdf'
    },
    {
      label: 'doc',
      value: 'doc'
    },
    {
      label: 'docx',
      value: 'docx'
    },
    {
      label: 'rar',
      value: 'rar'
    },
    {
      label: 'zip',
      value: 'zip'
    },
    {
      label: 'xls',
      value: 'xls'
    },
    {
      label: 'xlsx',
      value: 'xlsx'
    },
    {
      label: 'ofd',
      value: 'ofd'
    }
  ]
};

//表单配置
const schema = computed(() => [
  {
    label: '要件名称',
    component: () => <el-input placeholder="请输入"></el-input>,
    prop: 'businessFileName',
    props: {}
  },
  {
    label: '要件类型',
    component: () => (
      <el-select placeholder="请选择" multiple clearable style="width: 100%;">
        {options.fileType.map(item => (
          <el-option key={item.value} label={item.label} value={item.value}></el-option>
        ))}
      </el-select>
    ),
    prop: 'fileType',
    props: {}
  },
  {
    label: '备注',
    colProps: {
      span: 24
    },
    component: () => <el-input placeholder="请输入" rows="3" type="textarea"></el-input>,
    prop: 'remark',
    props: {}
  }
]);

//显示
const show = async params => {
  addData.value = params;
  dialogFormVisible.value = true;
  $nextTick(() => {
    file_add_form.value.setValues({});
  });
};

//确认
const _confirm = function () {
  const formRef = unref(file_add_form);
  formRef.validate().then(res => {
    if (res.isValid) {
      add(toRaw(formRef.getValues()));
    }
  });
};

//添加
const add = data => {
  const loading = instance.proxy.$loading({ fullscreen: true });
  data.fileType = data.fileType.join(',');
  $http
    .post(addUrl, { ...addData.value, ...data })
    .then(res => {
      loading.close();
      emit('confirm');
      dialogFormVisible.value = false;
    })
    .catch(() => {
      loading.close();
    });
};

defineExpose({
  show
});
</script>

<style lang="scss" scoped></style>
