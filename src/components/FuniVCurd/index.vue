<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2024-03-14 18:43:40
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-05-31 10:44:46
 * @Description:
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
-->

<template>
  <el-table ref="curdRef" v-bind="tableAttrs">
    <template #default>
      <FuniCurdColumn v-for="column in computedColumns" :key="column.prop" :column="column">
        <template v-if="column.slots?.header" #[column.slots.header]="params">
          <slot :name="column.slots.header" v-bind="params || {}" />
        </template>
        <template v-if="column.slots?.default" #[column.slots.default]="params">
          <slot :name="column.slots.default" v-bind="params || {}" />
        </template>
      </FuniCurdColumn>
    </template>

    <template #empty>
      <slot name="empty"></slot>
    </template>

    <template #append>
      <slot name="append"></slot>
    </template>
  </el-table>
</template>

<script lang="jsx" setup>
import { computed, ref } from 'vue';

import { useRender } from '@/components/FuniCurdV2/hooks/useRender.jsx';

defineOptions({ name: 'FuniVCurd', inheritAttrs: false });

// Props
const props = defineProps({
  // 数据
  data: { type: Array, default: () => [] },

  // Table配置
  rowKey: { type: [String, Function], default: 'id' },
  columns: { type: Array, default: () => [] }
});

const curdRef = ref();

// 表格配置
const tableAttrs = computed(() => ({ rowKey: props.rowKey, data: props.data, tableLayout: 'auto' }));

// hooks
const { contentRender, defaultRenderCell } = useRender();

const computedColumns = computed(() => {
  const columns = props.columns || [];

  return $utils.mapTree(columns, column => {
    column.hidden && (column.labelClassName = 'funi-curd-column__hidden');
    column.hidden && (column.className = 'funi-curd-column__hidden');
    const style = {};
    column.maxWidth && (style.maxWidth = `${column.maxWidth}px`);
    column.width = column.hidden ? 0.00000001 : column.width;
    return Object.assign({ slots: {}, showOverflowTooltip: true, style }, column);
  });
});
</script>
