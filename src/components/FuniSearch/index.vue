<template>
  <div class="funi_search_box">
    <div>
      <div
        class="funi_search"
        v-for="(item, index) in searchList"
        :key="item.id"
      >
        <funi-search-item
          ref="form"
          :key="item.id"
          :index="index"
          :list="filters"
        ></funi-search-item>
        <el-icon
          color="#40a9ff"
          size="20px"
          class="icon mr-20px"
          v-if="maxConditionCount > index + 1"
          @click="addSearchItem"
          ><CirclePlus
        /></el-icon>
        <el-icon
          color="#40a9ff"
          size="20px"
          class="icon mr-20px"
          v-if="index > 0"
          @click="delSearchItem(index)"
          ><Remove
        /></el-icon>
      </div>
    </div>

    <el-button
      type="primary"
      html-type="submit"
      @click="_queryForm"
      icon="search"
    >
      {{ searchBtnText }}
    </el-button>
    <el-button @click="_resetForm" class="reset-btn2" icon="refresh-right">
      重置
    </el-button>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, unref, reactive, watchEffect } from 'vue';
import FuniSearchItem from './FuniSearchItem.vue';
// Props
const props = defineProps({
  /* pageCode */
  pageCode: { type: String, default: '' },
  /* 查询按钮文本 */
  searchBtnText: { type: String, default: '查询' },
  /* 是否允许空条件查询 */
  searchNull: { type: Boolean, default: true },
  /* 最大条件数 */
  maxConditionCount: { type: Number, default: Number.MAX_VALUE },
  /* 请求url */
  url: { type: String, default: '' },
  //
  filters: {
    type: Array,
    default() {
      return [
        // {
        //   name: '房屋区域',
        //   type: 'el-select',
        //   column:"a",
        //   attribute:{fixed:false},
        //   operateOptions: [
        //     {
        //       key: '等于',
        //       value: 'EQ'
        //     },
        //     {
        //       key: '包含',
        //       value: 'CONTAIN'
        //     },
        //     {
        //       key: '不包含',
        //       value: 'NOT_CONTAIN'
        //     }
        //   ]},{
        //   name: '名称',
        //   type: 'el-input',
        //   column:"b",
        //   attribute:{fixed:false},
        //   operateOptions: [
        //     {
        //       key: '等于',
        //       value: 'EQ'
        //     },
        //     {
        //       key: '包含',
        //       value: 'CONTAIN'
        //     },
        //     {
        //       key: '不包含',
        //       value: 'NOT_CONTAIN'
        //     }
        //   ]}
      ];
    }
  }
});

//emit
const emit = defineEmits(['output']);

//data
let id = ref(0);
let form = ref();
let searchList = ref([]);

onMounted(() => {
  addSearchItem();
  console.log(props.filters);
});

//function
//添加
function addSearchItem() {
  id.value++;
  let item = { id: id.value };
  searchList.value.push(item);
}
//删除
function delSearchItem(index) {
  searchList.value.splice(index, 1);
}
//查询
function _queryForm() {
  let arr = [];
  let isPass = true;
  form.value.forEach(item => {
    item.getFormValue().then(res => {
      if (!res) {
        isPass = false;
      } else {
        arr.push(res);
      }
    });
  });
  if (isPass) {
    emit('output', arr);
  }
}
//重置
function _resetForm() {
  form.value.forEach(item => {
    item.resetForm();
  });
}
</script>

<style lang="scss" scoped>
.funi_search_box {
  display: flex;
  .funi_search {
    display: flex;
    align-items: center;
    &:not(:first-child) {
      margin-top: 10px;
    }
    .icon {
      cursor: pointer;
    }
  }
}
</style>
