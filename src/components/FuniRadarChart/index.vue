<template>
  <div style="width: 100%">
    <div :id="uid" :style="{ height: props.height + 'px' }"></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { ElMessage } from 'element-plus';
import { onMounted, ref, markRaw, watch, nextTick, onBeforeUnmount } from 'vue';
import FuniJs from '@funi-lib/utils';
defineOptions({
  name: 'FuniRadarChart'
});
const chart = ref(null);
const uid = ref(null);
uid.value = FuniJs.guid();
const initData = ref({
  title: {
    text: '雷达图'
  },
  legend: {
    top: 20,
    width: '70%',
    type: 'scroll',
    textStyle: {
      fontSize: 10
    }
  },
  radar: {
    shape: 'polygon',
    name: {
      fontSize: 10
    },
    splitNumber: 3,
    radius: '56%',
    indicator: [
      { name: 'Sales', max: 6500 },
      { name: 'Administration', max: 16000 },
      { name: 'Information Technology', max: 30000 },
      { name: 'Customer Support', max: 38000 },
      { name: 'Development', max: 52000 },
      { name: 'Marketing', max: 25000 }
    ],
    center: ['50%', '60%'],
    nameGap: 10,
    splitArea: {
      show: true
    }
  },
  series: [
    {
      type: 'radar',
      symbol: 'circle',
      emphasis: {
        focus: 'self',
        blurScope: 'coordinateSystem'
      },
      data: [
        {
          value: [4200, 3000, 20000, 35000, 50000, 18000],
          name: 'Allocated Budget'
        },
        {
          value: [5000, 14000, 28000, 26000, 42000, 21000],
          name: 'Actual Spending'
        }
      ]
    }
  ]
});

const props = defineProps({
  radarOptions: {
    type: Object,
    default: () => {
      return {};
    }
  },
  width: {
    type: Number,
    default: 0
  },
  height: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    default: ''
  },
  shape: {
    type: String,
    default: ''
  },
  splitNumber: {
    type: Number,
    default: 0
  },
  indicator: {
    type: Array,
    default: () => {
      return [];
    }
  },
  radarFocus: {
    type: String,
    default: ''
  },
  radarFocusSize: {
    type: Number,
    default: 0
  },
  fill: {
    type: Boolean,
    default: false
  },
  // data: {
  //   type: Array,
  //   default: () => {
  //     return [];
  //   }
  // },
  showNumbers: {
    type: Boolean,
    default: false
  },
  custom: {
    //自定义参数
    type: Object,
    default: () => {
      return {};
    }
  }
});

watch(
  () => props.radarOptions,
  nval => {
    if (nval.hasOwnProperty('indicatorData')) {
      //指示器数据
      if (nval.indicatorData instanceof Array) {
        initData.value.radar.indicator.splice(0);
        nval.indicatorData.map(e => {
          let indicator = { name: e.dataLabel, max: e.dataValue };
          initData.value.radar.indicator.push(indicator);
        });
      } else {
        ElMessage.error('请传入正确数据类型');
      }
    }
    if (nval.hasOwnProperty('data')) {
      if (nval.data instanceof Array) {
        initData.value.series[0].data.splice(0);
        nval.data.map(e => {
          let dataItem = { value: e.dataValue, name: e.dataLabel };
          initData.value.series[0].data.push(dataItem);
        });
      } else {
        ElMessage.error('请传入正确数据类型');
      }
    }
    if (nval.hasOwnProperty('title')) {
      initData.value.title.text = nval.title;
    }
    if (nval.hasOwnProperty('legendFontSize')) {
      initData.value.legend.textStyle.fontSize = nval.legendFontSize; //图列字体大小
    }
    if (nval.hasOwnProperty('isCircle') && nval.isCircle == true) {
      initData.value.radar.shape = 'circle'; //绘制样式是否圆形图表
    }
    if (nval.hasOwnProperty('splitNumber')) {
      initData.value.radar.splitNumber = nval.splitNumber; //雷达圈数
    }
    if (nval.hasOwnProperty('radarFocusType') && nval.radarFocusType.type) {
      //焦点样式     可取值//'rect','circle', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow', 'none' 还可以是图片路径
      if (nval.radarFocusType.hasOwnProperty('isImg') && nval.radarFocusType.isImg === true) {
        initData.value.series[0].symbol = 'image://' + nval.radarFocusType.type;
      } else {
        initData.value.series[0].symbol = nval.radarFocusType.type;
      }
    }
    if (nval.hasOwnProperty('radarFocusSize')) {
      initData.value.series[0].symbolSize = nval.radarFocusSize; //焦点大小
    }
    if (nval.hasOwnProperty('isNumber') && nval.isNumber === true) {
      initData.value.series[0].data.forEach(e => {
        e.label = { show: true };
      });
    }
  },
  { deep: true, immediate: true }
);
watch(
  () => initData.value,
  async nval => {
    await nextTick(() => {
      if (!chart.value) {
        chart.value = init();
        draw();
      } else {
        chart.value.setOption({ ...nval, ...props.custom }, true);
      }
    });
  },
  { deep: true, immediate: true }
);
watch(
  () => props.title,
  newVal => {
    initData.value.title.text = newVal;
  },
  { deep: true }
);
watch(
  () => props.shape,
  newVal => {
    initData.value.radar.shape = newVal;
  },
  { deep: true }
);
watch(
  () => props.splitNumber,
  newVal => {
    initData.value.radar.splitNumber = newVal;
  },
  { deep: true }
);
watch(
  () => props.indicator,
  newVal => {
    initData.value.radar.indicator = newVal;
  },
  { deep: true }
);
watch(
  () => props.radarFocus,
  newVal => {
    initData.value.series[0].symbol = newVal;
  },
  { deep: true }
);
watch(
  () => props.radarFocusSize,
  newVal => {
    initData.value.series[0].symbolSize = newVal;
  },
  { deep: true }
);
watch(
  () => props.fill,
  newVal => {
    if (newVal) {
      initData.value.series[0].areaStyle = {};
    } else if (!newVal) {
      delete initData.value.series[0].areaStyle;
    }
  }
);
watch(
  () => props.data,
  newVal => {
    initData.value.series[0].data.splice(0);
    newVal.map(e => {
      if (Object.keys(e).length != 0) {
        if (e.hasOwnProperty('value') && e.value.constructor === String) {
          let dataItem = {
            value: e.value.split(',').map(Number),
            name: e.name,
            label: {
              show: false,
              position: 'bottom',
              color: 'inherit',
              formatter: function (params) {
                return params.value;
              }
            }
          };
          initData.value.series[0].data.push(dataItem);
        } else {
          let dataItem = {
            value: e.value,
            name: e.name,
            label: {
              show: false,
              position: 'bottom',
              color: 'inherit',
              formatter: function (params) {
                return params.value;
              }
            }
          };
          initData.value.series[0].data.push(dataItem);
        }
      }
    });
  },
  { deep: true }
);
watch(
  () => props.showNumbers,
  newVal => {
    if (newVal) {
      initData.value.series[0].data.map(e => {
        e.label = { show: true };
      });
    } else {
      initData.value.series[0].data.map(e => {
        e.label = { show: false };
      });
    }
  },
  { deep: true }
);
watch(
  () => props.width,
  newValue => {
    chart.value.resize({
      width: newValue
    });
  },
  { deep: true }
);
watch(
  () => props.height,
  newValue => {
    chart.value.resize({
      height: newValue
    });
  },
  { deep: true }
);
const init = () => {
  return markRaw(echarts.init(document.getElementById(uid.value)));
};
const draw = () => {
  chart.value.setOption({ ...initData.value, ...props.custom });
};
const resize = () => {
  chart.value.resize();
};
onMounted(() => {
  window.addEventListener('resize', () => {
    resize();
  });
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', () => {
    resize();
  });
});
</script>

<style></style>
