<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-02-09 14:13:23
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-04-17 11:28:30
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniEditor\VditorEditor.vue
 * @Description: 支持markdown的编辑器
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="vditor-editor">
    <div id="vditor"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watchEffect, nextTick } from 'vue';
import Vditor from 'vditor';
import 'vditor/dist/index.css';
import VditorMethod from 'vditor/dist/method.min'

const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  height: {
    type: [Number, String],
    default: 400
  },
  lang: {
    type: String,
    default: 'zh_CN'
  },
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: ''
  }
});

const vditor = ref(null);
const content = ref('');

onMounted(() => {
  vditor.value = new Vditor('vditor', {
    ...props,
    after: () => {
      nextTick(() => {
        if (props.modelValue) {
          content.value = vditor.value.html2md(props.modelValue);
        }
        vditor.value.setValue(content.value);
      })
    },
    blur: (value) => {
      VditorMethod.md2html(value)
        .then(htmlContent => {
          content.value = value;
          emit('update:modelValue', htmlContent);
        })
    }
  });
});
</script>

<style lang="scss" scoped>
.vditor-editor {
  width: 100%;
  height: 500px;
}
.vditor-editor :deep(.vditor-reset) {
  padding: 4px !important;
}

.vditor-editor :deep(.vditor-toolbar) {
  padding: 0px !important;
}
</style>