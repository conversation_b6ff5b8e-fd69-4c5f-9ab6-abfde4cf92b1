<template>
  <div class="funi-editor">
    <el-row v-if="showMode">
      <el-radio-group v-model="localMode">
        <el-radio-button label="1"
          size="small">富文本编辑器</el-radio-button>
        <el-radio-button label="2"
          size="small">MarkDown编辑器</el-radio-button>
      </el-radio-group>
    </el-row>
    <el-row>
      <tiny-mce-editor v-if="localMode==='1'"
        v-bind="$attrs" />
      <vditor-editor v-else
        v-bind="$attrs" />
    </el-row>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { reactive, ref, watch, watchEffect } from 'vue';
import TinyMceEditor from './TinyMceEditor.vue';
import VditorEditor from './VditorEditor.vue'
/**
 * 功能方法
 */
const props = defineProps({
  mode: {
    type: String,
    default: '1'
  },
  showMode: {
    type: <PERSON>olean,
    default: true
  },
})

const localMode = ref('');

watchEffect(() => {
  localMode.value = props.mode;
})


</script>
<style lang='scss' scoped>
.funi-editor {
  width: 100%;
}
</style>