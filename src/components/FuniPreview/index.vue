<template>
  <div class="funi_preview_box" ref="funiPreviewBox">
    <canvas v-if="isImage(type)" ref="canvasRef" width="500" height="300"></canvas>
    <div v-else style="height: 100%" id="docEditorBox">
      <DocumentEditor ref="iframeRef" id="docEditor" :documentServerUrl="documentServerUrl" :config="config" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';

const emits = defineEmits(['outputImageOcrData']);
const props = defineProps({
  type: {
    default: 'png'
  },
  url: String,
  title: String
});

watch(
  props,
  () => {
    nextTick(() => {
      init();
    });
  },
  { immediate: true }
);

//预览窗口
let funiPreviewBox = ref();
//office 预览地址
let config = ref();
//iframe dom
let iframeRef = ref();
//canvas dom
let canvasRef = ref();
const documentServerUrl = process.env.NODE_ENV === 'production' ? location.origin : 'https://bpaas.funi.com';
//canvas 上下文
let ctx;
//图片对象
let image;
//图片ocr接口返回
let extractTextRes;
//当前搜索文本
let currentSearchKeyword;
async function init() {
  funiPreviewBox.value.style.height = funiPreviewBox.value.parentElement.offsetHeight + 'px';
  if (isImage(props.type)) {
    canvasRef.value.width = canvasRef.value.parentElement.offsetWidth - 8;
    ctx = canvasRef.value.getContext('2d');
    image = new Image();
    image.setAttribute('crossOrigin', 'Anonymous');
    image.src = props.url;
    image.onload = async () => {
      canvasRef.value.height = image.naturalHeight / (image.naturalWidth / canvasRef.value.clientWidth);
      drawImage();
      let base64Image = canvasRef.value.toDataURL('image/png');
      extractTextRes = await extractText(base64Image);
      emits('outputImageOcrData', extractTextRes);
    };
  } else {
    config.value = {
      document: {
        fileType: props.type,
        title: props.title,
        url: props.url + '&fp_rat=' + sessionStorage.getItem('token')
      },
      // type: 'embedded',
      editorConfig: {
        mode: 'view',
        lang: 'zh-CN'
      }
    };
  }
}

/**
 * 绘制图片
 */
function drawImage() {
  ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
  let scale = image.naturalWidth / canvasRef.value.clientWidth;
  ctx.drawImage(image, 0, 0, image.naturalWidth / scale, image.naturalHeight / scale);
}
/**
 * 高亮关键字
 * @param keyword 关键字
 * @param isLike 是否模糊查询--ocr暂不支持
 */
function highLightKeyword(keyword, isLike, color = '#3386998a') {
  if (isImage(props.type)) {
    drawImage();
    //接口返回为第二层
    extractTextRes[0]?.forEach(item => {
      let isMatch = item.length == 2 && item[1][0] == keyword;
      if (isLike) {
        isMatch = item.length == 2 && item[1][0]?.includes(keyword);
      }
      if (isMatch) {
        ctx.fillStyle = color;
        ctx.beginPath();
        item[0].forEach((pxArr, index) => {
          if (index == 0) {
            ctx.moveTo(pxArr[0], pxArr[1]);
          } else {
            ctx.lineTo(pxArr[0], pxArr[1]);
          }
        });
        ctx.closePath();
        ctx.fill();
      }
    });
  } else {
    pdfKeywordSearch(keyword);
  }
}

/**
 *
 * @param base64Image 图片识别方法
 */
function extractText(base64Image) {
  return $http.post('/cssw/imageOcr/extractText', { base64Image });
}

/**
 * pdf关键字搜索
 */
function pdfKeywordSearch(keyword) {
  let _iframeWindow = document.querySelector('#docEditorBox iframe').contentWindow;
  var style = _iframeWindow.document.createElement('style');
  style.innerHTML = '.search-bar { display:none !important; }';
  _iframeWindow.document.head.appendChild(style);

  if (currentSearchKeyword != keyword) {
    _iframeWindow.Common.NotificationCenter.trigger('search:show');
    _iframeWindow.document.querySelector('#search-bar-text').value = keyword;
    let event = new Event('input', { bubbles: true });
    _iframeWindow.document.querySelector('#search-bar-text').dispatchEvent(event);
  } else {
    let event = new Event('keydown', { bubbles: true });
    event = Object.assign(event, {
      ctrlKey: false,
      metaKey: false,
      altKey: false,
      which: 13,
      keyCode: 13,
      key: 'Enter',
      code: 'Enter'
    });
    _iframeWindow.document.querySelector('#search-bar-text').dispatchEvent(event);
  }
  currentSearchKeyword = keyword;
}

//是否图片格式
const isImage = ext => {
  return (
    ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'gif', 'GIF', 'webp', 'WEBP', 'svg', 'SVG'].indexOf(
      ext.toLowerCase()
    ) !== -1
  );
};
defineExpose({
  highLightKeyword,
  pdfKeywordSearch
});
</script>

<style lang="scss" scoped>
.funi_preview_box {
  width: 100%;
  overflow-y: auto;
}
</style>
