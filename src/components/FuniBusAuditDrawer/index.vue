<template>
  <div id="auditDrawer" :key="key">
    <el-drawer @close="close" v-model="drawer" :with-header="false" :size="size">
      <div v-if="!isHideUnfold && drawer && size > 1" class="unfold2" @click="close">
        <div class="fold2" @click="drawer = false">
          <funi-icon icon="icon-park-solid:right-one"></funi-icon>
        </div>
      </div>
      <iframe
        ref="myIframe"
        style="height: calc(100%); width: 100%"
        :src="url"
        frameborder="0"
        scrolling="auto"
      ></iframe>
    </el-drawer>
    <div v-if="(!isHideUnfold && !drawer) || size < 2" class="unfold">
      <div class="fold" @click="btnClick()">
        <funi-icon icon="icon-park-solid:left-one"></funi-icon>
      </div>
      <div class="info">
        <!-- <div class="item" @click="show('AUDIT')">
          <img class="img" src="../../assets/icon_search_eye.png" />
          <div class="label">审核</div>
        </div> -->
        <div class="item" @click="btnClick()">
          <!-- <img class="img" src="../../assets/icon_file_history.png" /> -->
          <funi-icon icon="fluent:document-bullet-list-clock-20-regular"></funi-icon>
          <div class="label">日志</div>
        </div>
        <div
          class="item"
          v-if="!isAuthFixedBtn || infoDetails.isInBusiness == '1'"
          @click="btnClick({ businessExecutionType: 'COPY_TO_OTHER' })"
        >
          <!-- <img class="img" src="../../assets/icon_send_plane.png" /> -->
          <funi-icon icon="mingcute:send-line"></funi-icon>
          <div class="label">抄送</div>
        </div>
        <div
          class="item"
          v-if="!isAuthFixedBtn || infoDetails.isInBusiness == '1'"
          @click="btnClick({ businessExecutionType: 'COMMENT' })"
        >
          <!-- <img class="img" src="../../assets/ic_comment.png" /> -->
          <funi-icon icon="mdi:comment-processing-outline"></funi-icon>
          <div class="label">评论</div>
        </div>
      </div>
    </div>
    <div v-if="!isHideUnfold && !onlyShow">
      <Teleport to="#teleportBtns">
        <div class="btnView">
          <template v-for="(itemBtn, index) in infoDetails.currentUserExecution" :key="index">
            <el-button
              :size="getSize(itemBtn)"
              :type="getPrimary(itemBtn)"
              :plain="itemBtn.iconType == '2'"
              :text="itemBtn.iconType == '3'"
              :link="itemBtn.iconType == '4'"
              @click="btnClick(itemBtn)"
            >
              <funi-icon
                style="margin-right: 5px"
                v-if="itemBtn.iconShow && itemBtn.iconShow != '1' && itemBtn.iconLocation == '1'"
                :icon="itemBtn.icon"
              />
              <span v-if="itemBtn.iconShow != '3'">{{ itemBtn.customName || itemBtn.name }}</span>
              <funi-icon
                style="margin-left: 5px"
                v-if="itemBtn.iconShow && itemBtn.iconShow != '1' && itemBtn.iconLocation == '2'"
                :icon="itemBtn.icon"
              />
            </el-button>
          </template>
        </div>
      </Teleport>
      <div v-if="isFormOpinion">
        <Teleport to="#teleportAuditContainer">
          <funi-group-title title="审核意见" />
          <el-input
            placeholder="请输入审核意见"
            resize="none"
            :autosize="{ minRows: 4, maxRows: 4 }"
            show-word-limit
            maxlength="1000"
            v-model="opinion"
            type="textarea"
          ></el-input>
        </Teleport>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, unref, getCurrentInstance, nextTick, watch } from 'vue';
import { useAppStore } from '@/stores/useAppStore';
import { ElMessage } from 'element-plus';
import { useMultiTab } from '@/utils/hooks/useMultiTab';

const multiTab = useMultiTab();
const instance = getCurrentInstance();
const appStore = useAppStore();
const props = defineProps({
  //仅显示
  onlyShow: {
    type: Boolean,
    default: true
  },
  //是否执行默认操作
  isDefaultOperate: {
    type: Boolean,
    default: true
  },
  //是否隐藏展开功能
  isHideUnfold: {
    type: Boolean,
    default: false
  },
  //是否鉴权固定按钮（评论、抄送）
  isAuthFixedBtn: {
    type: Boolean,
    default: false
  },
  //是否业务表单中提供审核意见  如果是则点击审批按钮（同意、退件、拒绝、退回）不弹出抽屉
  isFormOpinion: {
    type: Boolean,
    default: false
  },
  //业务件id
  businessId: String,
  //系统id
  sysId: String,
  //审核前执行方法
  beforeAuditFn: Function
});

defineOptions({
  name: 'FuniBusAuditDrawer',
  inheritAttrs: false
});
const actionBtnsUrl = '/bpmn/businessManage/getBusinessProcessInstanceInfoByBusinessId';
const executeBusinessUrl = '/bpmn/businessManage/executeBusiness';
const size = ref(400);
const emit = defineEmits(['change', 'auditEvent', 'drawerChange', 'workChange']);
const myIframe = ref();
const drawer = ref(false);
const key = ref();
const url = ref('');
const opinion = ref('');
const infoDetails = ref('');

//仅工作台调用使用
const show = (sysId = undefined, businessId = undefined) => {
  emit('drawerChange', true);
  url.value = `${$utils.getServerBaseApi()}/sdkapp/?${Math.floor(
    Math.random() * 90000
  )}#/newAudit?cookie=${sessionStorage.getItem('token')}&businessId=${businessId || props.businessId}&sysId=${
    sysId || props.sysId || appStore.system.id
  }&onlyShow=${props.onlyShow || !props.isHideUnfold}&isDefaultOperate=${props.isDefaultOperate}&isLoading=2`;
  key.value = $utils.guid();
  drawer.value = true;
};

const close = () => {
  emit('drawerChange', false);
  emit('change', true);
};

watch(
  () => infoDetails,
  newVal => {
    emit('workChange', newVal);
  },
  { immediate: true }
);

function getPrimary(item) {
  if (item.color == '1' || !item.color) return 'primary';
  if (item.color == '2') return 'primary';
  if (item.color == '3') return 'warning';
  if (item.color == '4') return 'danger';
}

function getSize(item) {
  if (item.size == '1') return 'small';
  if (item.size == '2' || !item.color) return 'default';
  if (item.size == '3') return 'large';
}

onMounted(async () => {
  window.addEventListener('message', auditCallback);
  if (!props.isHideUnfold) {
    size.value = 0.1;
    drawer.value = true;
    if (!props.onlyShow) {
      infoDetails.value = await $http.post(actionBtnsUrl, {
        sysId: props.sysId,
        businessId: props.businessId
      });
    }
    url.value = `${$utils.getServerBaseApi()}/sdkapp/#/newAudit?cookie=${sessionStorage.getItem('token')}&businessId=${
      props.businessId
    }&sysId=${props.sysId || appStore.system.id}&onlyShow=${props.onlyShow || !props.isHideUnfold}&isDefaultOperate=${
      props.isDefaultOperate
    }&isLoading=2`;
    key.value = $utils.guid();
    setTimeout(() => {
      drawer.value = false;
      setTimeout(() => {
        size.value = 400;
      }, 300);
    }, 500);
  }
});

//按钮点击事件
const btnClick = async event => {
  let timeout = 0;
  if (!event) {
    if (!drawer.value) {
      timeout = 500;
      emit('drawerChange', true);
      drawer.value = true;
    }
    setTimeout(() => {
      unref(myIframe).contentWindow.submit({ businessExecutionType: 'AUDIT' });
    }, timeout);
  } else {
    if (props.beforeAuditFn) {
      props.beforeAuditFn(event).then(res => {
        if (res) {
          event.businessJson = res;
          event.opinion = res.opinion;
          //指定下一审核人  仅同意操作可指定
          if (event.businessExecutionType == 'AGREE') {
            if (res.customAssigneeList) event.customAssigneeList = res.customAssigneeList;
            if (res.customCopyToOtherIds) event.customCopyToOtherIds = res.customCopyToOtherIds;
            if (res.operateRemark) event.operateRemark = res.operateRemark;
            if (res.businessVariableMap) event.businessVariableMap = res.businessVariableMap;
          }
        }
        if (
          props.isFormOpinion &&
          ['AGREE', 'REMOVE_PROCESS', 'REJECT_FIRST_NODE', 'REJECT_LAST_NODE'].includes(event.businessExecutionType)
        ) {
          //业务表单中采集审批意见  仅支持同意、拒绝、退件、退回操作
          auditEvent(event);
        } else {
          if (!drawer.value) {
            timeout = 500;
            emit('drawerChange', true);
            drawer.value = true;
          }
          setTimeout(() => {
            unref(myIframe).contentWindow.submit(event);
          }, timeout);
        }
      });
    } else {
      if (
        props.isFormOpinion &&
        ['AGREE', 'REMOVE_PROCESS', 'REJECT_FIRST_NODE', 'REJECT_LAST_NODE'].includes(event.businessExecutionType)
      ) {
        //业务表单中采集审批意见  仅支持同意、拒绝、退件、退回操作
        auditEvent(event);
      } else {
        if (!drawer.value) {
          timeout = 500;
          emit('drawerChange', true);
          drawer.value = true;
        }
        setTimeout(() => {
          unref(myIframe).contentWindow.submit(event);
        }, timeout);
      }
    }
  }
};

//审核【同意、退件、拒绝、退回】
const auditEvent = event => {
  const loading = instance.proxy.$loading({ fullscreen: true });
  $http
    .post(executeBusinessUrl, {
      opinion: event.opinion || opinion.value || '',
      sysId: props.sysId || appStore.system.id || '',
      businessId: props.businessId || '',
      businessExecutionType: event.businessExecutionType,
      businessExecutionName: event.businessExecutionName,
      customAssigneeList: event.customAssigneeList,
      customCopyToOtherIds: event.customCopyToOtherIds,
      operateRemark: event.operateRemark,
      businessVariableMap: event.businessVariableMap,
      businessJson: event.businessJson || {}
    })
    .then(res => {
      if (res.isSuccess) {
        ElMessage.success('操作成功');
        emit('auditEvent', event);
      }
      loading.close();
    })
    .catch(() => {
      loading.close();
    });
};

//审核完成刷新
const refresh = () => {
  emit('auditEvent');
  //刷新按钮
  if (!props.onlyShow) {
    infoDetails.value = $http.post(actionBtnsUrl, {
      sysId: props.sysId,
      businessId: props.businessId
    });
  }
};

//sdk审核回调
let loading;
const auditCallback = event => {
  if (event?.data?.cmd) {
    switch (event?.data?.cmd) {
      case 'closeModal': //关闭抽屉
        setTimeout(() => {
          emit('drawerChange', false);
          drawer.value = false;
        }, 500);
        refresh();
        break;
      case 'requestStart': //审核请求开始
        loading = instance.proxy.$loading({ fullscreen: true });
        break;
      case 'requestEnd': //审核请求结束
        loading.close();
        break;
    }
  }
};

defineExpose({ show, auditCallback, drawer });
</script>

<style lang="scss" scoped>
#auditDrawer {
  :deep(.el-drawer__body) {
    padding: 0;
  }

  :deep(.el-overlay) {
    background-color: #00000000;
    z-index: 2009 !important;
  }

  :deep(.el-drawer) {
    box-shadow: -2px 0px 4px rgba(0, 0, 0, 0.07);
    height: calc(100% - var(--header-height, 0%) - var(--multi-tab-height, 0%));
    top: calc(var(--header-height, 0%) + var(--multi-tab-height, 0%));
  }
}

.btnView {
  height: 50px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.fold {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 80px;
  opacity: 1;
  border-radius: 4px 0px 0px 4px;
  background: rgba(254, 254, 255, 1);
  border-width: 1px 0px 1px 1px;
  border-style: solid;
  border-color: #eeeeee;
  box-shadow: -2px 0px 4px rgba(0, 0, 0, 0.07);
  margin-right: -1px;
  z-index: 2001;
  cursor: pointer;
}

.fold2 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 80px;
  opacity: 1;
  border-radius: 4px 0px 0px 4px;
  background: rgba(254, 254, 255, 1);
  border-width: 1px 0px 1px 1px;
  border-style: solid;
  border-color: #eeeeee;
  box-shadow: -2px 0px 4px rgba(0, 0, 0, 0.07);
  z-index: 2000;
  cursor: pointer;
}
.fold2:hover,
.fold:hover {
  color: var(--el-color-primary);
}

.unfold2 {
  position: fixed;
  top: calc(var(--header-height, 0%) + var(--multi-tab-height, 0%));
  height: calc(100% - var(--header-height, 0%) - var(--multi-tab-height, 0%));
  z-index: 2000;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  right: 400px !important;
  width: 20px !important;
  transition: all var(--el-transition-duration);
}

.unfold {
  position: fixed;
  right: 0;
  top: calc(var(--header-height, 0%) + var(--multi-tab-height, 0%));
  height: calc(100% - var(--header-height, 0%) - var(--multi-tab-height, 0%));
  z-index: 2000;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transition: all var(--el-transition-duration);

  .info {
    width: 40px;
    height: 100%;
    background: #ffffff;
    opacity: 1;
    background: rgba(254, 254, 255, 1);
    border-width: 1px 0px 1px 1px;
    border-style: solid;
    border-color: #eeeeee;
    box-shadow: -2px 0px 4px rgba(0, 0, 0, 0.07);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .item {
      padding: 10px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: var(--el-text-color-primary);
      cursor: pointer;
      .label {
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 22px;
        text-align: center;
        vertical-align: top;
      }
    }

    .item:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
