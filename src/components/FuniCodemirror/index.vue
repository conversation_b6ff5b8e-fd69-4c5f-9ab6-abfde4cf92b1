<template>
  <codemirror
    v-model="code"
    :placeholder="placeholder"
    :style="style"
    :disabled="disabled"
    :autofocus="true"
    :indent-with-tab="true"
    :tab-size="2"
    :extensions="extensions"
    @ready="handleReady"
    @change="handleChange"
    @blur="emit('blur', $event)"
    @focus="emit('focus', $event)"
    @update="emit('update', $event)"
  />
</template>
<script setup>
import { ref, shallowRef, watch } from 'vue';
import { Codemirror } from 'vue-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { oneDark } from '@codemirror/theme-one-dark';

defineOptions({
  name: 'FuniCodemirror',
  inheritAttrs: false
});

const props = defineProps({
  modelValue: String,
  disabled: Boolean,
  placeholder: { type: String, default: 'Code goes here...' },
  style: {
    type: Object,
    default: () => ({ height: 'auto', minHeight: '400px' })
  }
});

const emit = defineEmits([
  'update:modelValue',
  'ready',
  'change',
  'update',
  'focus',
  'blur'
]);

const code = ref('');
const extensions = [javascript(), oneDark];

watch(
  () => props.modelValue,
  newCode => {
    code.value = newCode;
  },
  { immediate: true }
);

const view = shallowRef();
const handleReady = payload => {
  view.value = payload.view;
  emit('ready', payload);
};

const handleChange = e => {
  emit('update:modelValue', e);
  emit('change', e);
};
</script>
