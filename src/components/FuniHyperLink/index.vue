<template>
  <el-button type="primary" link @click="goToLink" v-auth="auth || void 0"> {{ label || '' }} </el-button>
</template>
<script setup>
import { useRouter } from 'vue-router';
const router = useRouter();
const props = defineProps({
  label: String,
  pathName: String,
  auth: String,
  pathQuery: {
    type: Object,
    default: () => ({})
  },
  externalLink: String
});

function goToLink() {
  if (props.externalLink) {
    window.open(props.externalLink);
    return;
  }
  if (!props.pathName) return;
  router.push({
    name: props.pathName,
    query: props.pathQuery
  });
}
</script>
<style scoped></style>
