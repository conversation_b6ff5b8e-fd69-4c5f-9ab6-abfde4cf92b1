/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-07 19:32:55
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-04-25 18:18:30
 * @FilePath: /funi-paas-bpmn-ui/src/apps/bpmn/bpmn-uilt/funi-bpmn-dataFunc/funi-bpmn-dataFunc.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
/**
 * @description 获取属性 以及扩展属性
 * @param object 数据对象
 * @param type extension:funi扩展数据  other:其他扩展属性
 * **/
export const getShapeExtensionData = (object, type) => {
    object = JSON.parse(JSON.stringify(object))
    let funiExtensionElements = {};
    let extensionElements = []
    let documentation = ''
    let conditionExpression = ''
    // console.log('getShapeExtensionData',object)
    // 获取节点描述
    if (object.documentation) {
        documentation = object.documentation[0].text
    }
    if(object?.conditionExpression?.body){
        conditionExpression = object?.conditionExpression?.body
    }

    // 获取扩展属性
    if (
        object?.extensionElements &&
        object?.extensionElements?.values?.length

    ) {
        object?.extensionElements?.values.map(item => {
            if (item.$type == 'camunda:FuniProperty') {
                funiExtensionElements[item.name] = item.value;
            } else if (item.$type == 'camunda:Properties') {
                extensionElements = item.values.map(el => {
                    let { name, value } = el
                    return {
                        name, value
                    }
                })
            }
        });
    }



    let bease = {
        id: object.id ? object.id : '',
        name: object.name ? object.name : '',
        documentation,
        conditionExpression
    };


    if (type == 'funiExtension')
        return {
            ...bease,
            ...funiExtensionElements,
        };

    else
        return {
            ...bease,
            ...funiExtensionElements,
            funiExtendInfo: extensionElements || []
        };

};


export const funiBpmnProcessID = () => {
    return 'FuniProcess_' + S4()+S4()
}
export const funiBpmnStartEventID = () => {
    return 'FuniStart_' + S4()
}
export const funiBpmnEndEventID = () => {
    return 'FuniEnd_' + S4()
}
export const funiBpmnElementID = () => {
    return 'FuniElement_' + S4()
}

function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
}
