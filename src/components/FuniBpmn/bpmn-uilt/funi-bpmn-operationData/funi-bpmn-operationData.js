import { data_bpmn } from './../../api/api.js'
import modelUtil from "@funi/bpmn-js/bpmn-utils/funi-bpmn-utlis";
import { funiBpmnAddDefaultListener } from './funi-bpmn-default-listener'
import { setUserTaskRuleFunc } from './funi-bpmn-setNativeProperty.js'
const prefix = data_bpmn.defaultSettings.processEngine
let modeling = null
/**
 * @description 获取const modeling = store.bpmnObject.get('modeling');

 * **/
export const getModeling = () => {
    modeling = data_bpmn.bpmnObject.get('modeling');
    return modeling
}

/**
 * @description 设置基础属性值
 * @param element bpmn element
 * @param param:Object 基本属性
 * **/

export const updateProperties = (element, param, callback) => {
    getModeling()
    modeling.updateProperties(element, param)
    callback && callback(element)
}

/**
 * @description 设置自定义Funi属性
 * @param element bpmn element
 * @param params:Object 基本属性
 * **/
export const updateFuniModdleProperties = (element, params, callback) => {
    let cloneParams = []
    for (let key in params) {
        cloneParams.push({
            name: key,
            value: params[key]
        })
    }
    getModeling()
    const businessObject = getRelevantBusinessObject(element);
    // 判断 extensionElements
    let extensionElements = businessObject.get("extensionElements");

    if (!extensionElements) {
        extensionElements = createModdleElement("bpmn:ExtensionElements", { values: [] }, businessObject);
        modeling.updateModdleProperties(element, businessObject, { extensionElements });
    }
    let allVal = extensionElements?.get("values")
    for (let i = 0; i < allVal.length; i++) {
        if (allVal[i].$type == "camunda:FuniProperty") {
            allVal.splice(i, 1)
            i--
        }
    }

    if (cloneParams.length > 0) {
        cloneParams.map((item, index) => {
            const newProperty = createModdleElement(`${prefix}:FuniProperty`, item, extensionElements);
            let values = index == 0 ? [...allVal, newProperty] : [...extensionElements?.get("values"), newProperty]
            modeling.updateModdleProperties(element, extensionElements, {
                values
            });
        })
    } else {
        let values = [...allVal]
        modeling.updateModdleProperties(element, extensionElements, {
            values
        });
    }
    if (element.type == 'bpmn:UserTask' && params.hasOwnProperty('taskMultiInstanceType')) {
        setUserTaskRuleFunc(element, params['taskMultiInstanceType'])
    }
    callback && callback(element)
}


/**
 * @description 设置自定义属性
 * @param element bpmn element
 * @param params:[] 基本属性
 * **/
export const updateModdleProperties = (element, params, callback) => {
    let cloneParams = params
    getModeling()
    const businessObject = getRelevantBusinessObject(element);
    // 判断 extensionElements
    let extensionElements = businessObject.get("extensionElements");
    if (!extensionElements) {
        extensionElements = createModdleElement("bpmn:ExtensionElements", { values: [] }, businessObject);
        modeling.updateModdleProperties(element, businessObject, { extensionElements });
    }
    // 判断 extensionElements 是否有 properties
    let properties = getProperties(businessObject);
    if (!properties) {
        properties = createModdleElement(`${prefix}:Properties`, { values: [] }, extensionElements);
        modeling.updateModdleProperties(element, extensionElements, {
            values: [...extensionElements.get("values"), properties]
        });
    }

    // 清空旧属性
    modeling.updateModdleProperties(element, properties, { values: [] });
    if (params.length > 0) {
        // 创建新属性并添加
        cloneParams.map(item => {
            const newProperty = createModdleElement(`${prefix}:Property`, item, properties);
            modeling.updateModdleProperties(element, properties, {
                values: [...properties?.get("values"), newProperty]
            });
        })
    }
    callback && callback(element)
}


export const setFuniBpmnDefaultListener = (element) => {
    getModeling()
    const businessObject = getRelevantBusinessObject(element);
    // 判断 extensionElements
    let extensionElements = businessObject.get("extensionElements");
    if (!extensionElements) {
        extensionElements = createModdleElement("bpmn:ExtensionElements", { values: [] }, businessObject);
        modeling.updateModdleProperties(element, businessObject, { extensionElements });
    }
    let listener = getListener(businessObject);
    if (!listener && element.type !== 'bpmn:UserTask') {
        funiBpmnAddDefaultListener(element, extensionElements, 'default')
    }
    let userTaskListener = getUserTaskListener(businessObject);
    if (!userTaskListener && element.type == 'bpmn:UserTask') {
        funiBpmnAddDefaultListener(element, extensionElements, 'userTask')

    }




}




/**
 * @description 
 * **/
export const setConditionExpression = (element, value) => {
    getModeling()
    const businessObject = getRelevantBusinessObject(element);
    const conditionExpression = createModdleElement("bpmn:FormalExpression", {
        body: value
    }, businessObject);
    modeling.updateProperties(element, {
        conditionExpression: conditionExpression,
    })

}

/**
 * @description 创建元素
 * @param elementType 元素类型
 * @param properties 属性
 * @param parent 父元素
 * **/
export function createModdleElement(elementType, properties, parent) {
    const moddle = data_bpmn.bpmnObject.get("moddle");
    const element = moddle.create(elementType, properties);
    parent && (element.$parent = parent);

    return element;
}








/**
 * @description 获取节点的 BusinessObject
 * @param element 
 * **/
export function getRelevantBusinessObject(element) {
    const businessObject = modelUtil.getBusinessObject(element);
    if (modelUtil.is(element, "bpmn:Participant")) {
        return businessObject.get("processRef");
    }
    return businessObject;
}
function getPropertiesList(bo) {
    const properties = getProperties(bo);
    return properties && properties.get("values");
}
function getProperties(bo) {
    return getExtensionElementsList(bo, 'camunda:Properties')[0];
}
function getListener(bo) {
    return getExtensionElementsList(bo, 'camunda:ExecutionListener')[0];
}
function getUserTaskListener(bo) {
    return getExtensionElementsList(bo, 'camunda:TaskListener')[0];
}

export function getExtensionElementsList(businessObject, type) {

    const extensionElements = businessObject?.get("extensionElements");
    if (!extensionElements) return [];

    const values = extensionElements.get("values");
    if (!values || !values.length) return [];


    if (type) return values.filter((value) => modelUtil.is(value, type));

    return values;
}

