/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-03-08 09:49:48
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-04-25 17:09:49
 * @FilePath: /funi-paas-bpmn-ui/src/apps/bpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-default-listener.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import { data_bpmn } from './../../api/api.js'
import { createModdleElement } from './funi-bpmn-operationData'
const prefix = data_bpmn.defaultSettings.processEngine
let modeling = null
/**
 * @description 获取const modeling = store.bpmnObject.get('modeling');

 * **/
const getModeling = () => {
    modeling = data_bpmn.bpmnObject.get('modeling');
    return modeling
}


export const funiBpmnAddDefaultListener = (element, extensionElements, type) => {
    getModeling()
    let defaultParams = [
        { delegateExpression: '${taskExecutionListener}', event: 'start', elementName: `${prefix}:ExecutionListener` },
        { delegateExpression: '${taskExecutionListener}', event: 'end', elementName: `${prefix}:ExecutionListener` },

    ]
    let userTaskParams = [
        { delegateExpression: '${taskExecutionListener}', event: 'create', elementName: `${prefix}:TaskListener` },
        { delegateExpression: '${taskExecutionListener}', event: 'assignment', elementName: `${prefix}:TaskListener` },
        { delegateExpression: '${taskExecutionListener}', event: 'complete', elementName: `${prefix}:TaskListener` },
        { delegateExpression: '${taskExecutionListener}', event: 'delete', elementName: `${prefix}:TaskListener` },
        { delegateExpression: '${taskExecutionListener}', event: 'update', elementName: `${prefix}:TaskListener` },
    ]
    let creatList = type == 'default' ? defaultParams : [...defaultParams, ...userTaskParams]
    creatList.map(item => {
        let elementName = item.elementName
        let { delegateExpression, event } = item
        const newProperty = createModdleElement(elementName, { delegateExpression, event }, extensionElements);
        modeling.updateModdleProperties(element, extensionElements, {
            values: [...extensionElements.get("values"), newProperty]
        });
    })

}