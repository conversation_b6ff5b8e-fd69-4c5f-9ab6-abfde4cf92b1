/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-08 12:44:33
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-04-25 17:10:30
 * @FilePath: /funi-paas-bpmn-ui/src/apps/bpmn/bpmn-uilt/funi-bpmn-operationData/funi-bpmn-setNativeProperty.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import { data_bpmn } from './../../api/api.js'
import { createModdleElement, getRelevantBusinessObject } from './funi-bpmn-operationData.js'

let modeling = null
let bpmnFactory = null
/**
 * @description 获取const modeling = store.bpmnObject.get('modeling');

 * **/
const getModeling = () => {
    modeling = data_bpmn.bpmnObject.get('modeling');
    bpmnFactory = data_bpmn.bpmnObject.get('bpmnFactory');
}


export const setUserTaskRuleFunc = (element, type) => {
    getModeling()
    let param = {}
    let completionCondition = undefined
    if (type == 'taskMultiInstanceType-3' || type == 'taskMultiInstanceType-1') { // 多人任意  单人办理
        param = {
            'camunda:collection': "assigneeList_" + element.id,
            'camunda:elementVariable': 'assignee'
        }
        completionCondition = bpmnFactory.create("bpmn:FormalExpression", {
            body: type == 'taskMultiInstanceType-3' ? '${nrOfCompletedInstances>0}' : '${nrOfCompletedInstances==1}'
        });
        completionCondition.$parent = element
    } else if (type == 'taskMultiInstanceType-2') { // 多人顺序
        param = {
            'camunda:collection': "assigneeList_" + element.id,
            'camunda:elementVariable': 'assignee',
            'isSequential': true
        }
    } else if (type == 'taskMultiInstanceType-4') { // 多人并行
        param = {
            'camunda:collection': "assigneeList_" + element.id,
            'camunda:elementVariable': 'assignee',
        }
    }
    let multiInstanceLoopCharacteristics = createModdleElement("bpmn:MultiInstanceLoopCharacteristics", {
        ...param
    });
    if (completionCondition) {
        multiInstanceLoopCharacteristics['completionCondition'] = completionCondition
    }

    modeling.updateProperties(element, { loopCharacteristics: multiInstanceLoopCharacteristics, 'camunda:assignee': "${assignee}" });

}




// 设置备注
export const setDocumentation = (element, value, callback) => {
    getModeling()
    let documentation = bpmnFactory.create("bpmn:Documentation", {
        text: value
    });
    const businessObject = getRelevantBusinessObject(element);
    return modeling.updateModdleProperties(element, businessObject, {
        documentation: [documentation]
    });

    callback && callback(element)
}

