<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-07 17:35:34
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-03-27 16:03:16
 * @FilePath: /funi-paas-bpmn-ui/src/apps/bpmn/component/funiModule/prevPerson/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div :style="{ '--addWidth': props.addWidth }" class="bpmnSelect">
    <el-radio-group :modelValue="data.mVal" @change="selectCahnge">
      <el-radio :label="true">上一执行人</el-radio>
      <el-radio :label="false">自动选人</el-radio>
    </el-radio-group>
  </div>
</template>
<script setup>
import { reactive, onMounted, watch } from 'vue';

const emit = defineEmits(['update:modelValue', 'change']);
const data = reactive({
  mVal: false
});
const props = defineProps({
  modelValue: {
    type: String,
    default: false
  },
  addWidth: {
    type: String,
    default: '0px'
  }
});
watch(
  () => props.modelValue,
  () => {
    data.mVal = !!props.modelValue;
  }
);
onMounted(() => {});

const selectCahnge = e => {
  emit('update:modelValue', e);
  emit('change', e);
};
</script>
<style scoped>
.topLabel {
  width: 120px;
  /* color: var(--el-text-color-regular); */
}
.bpmnSelect {
  width: calc(100% + var(--addWidth));
  display: flex;
  /* position: absolute; */
  /* margin-bottom: 15px; */
}
</style>
