<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-27 16:40:11
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-07-24 12:39:05
 * @FilePath: /funi-paas-bpmn-ui/src/components/FuniBpmn/component/funiModule/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="dataGroup">
    <div class="nodeName">{{ data.title || '--' }}</div>
    <el-tabs type="card" v-model="activeName" class="demo-tabs">
      <el-tab-pane v-for="(item, index) in tabs" :label="item.label" :name="item.name" :key="item.name"> </el-tab-pane>
    </el-tabs>
    <div class="formBox" :key="activeName">
      <funi-form
        :label-width="activeName == 'other' ? '60px' : 'auto'"
        :schema="schema"
        @get-form="setForm"
        :rules="rules"
        :border="false"
      >
      </funi-form>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { computed, reactive, watch, ref, nextTick, inject } from 'vue';
import funiBpmnDataFunc from './../../bpmn-uilt/funi-bpmn-dataFunc/index.js';

const data = reactive({
  funi_form: null,
  shapeObj: {},
  formSaveObj: {},
  taskMultiInstanceType: '',
  timer: '',
  title: ''
});

let activeName = ref('');
const emit = defineEmits(['updateData']);
const props = defineProps({
  shape: {
    type: Object,
    default: null
  },
  type: {
    type: String,
    default: 'add'
  },
  tabs: {
    type: Array,
    default: () => [
      {
        name: 'bease',
        label: '基本信息',
        schema: []
      }
    ]
  }
});
let { typeCode } = inject('funi-bpmn-data');
const schema = computed(() => {
  let arr = props.tabs.filter(item => item.name == activeName.value);
  let schemaList = arr && arr[0] ? arr[0].schema(saveAttrs, data.taskMultiInstanceType) : [];
  if (typeCode === 'bc') {
    schemaList.map((item, index) => {
      if (item.prop == 'name') {
        schemaList[index].component = null;
      }
    });
  }

  return schemaList;
});

watch(
  () => props.tabs,
  async () => {
    let index = props.tabs.findIndex(item => item.name == activeName.value);
    if (!activeName.value || index < 0) {
      activeName.value = '';
      await nextTick();
      activeName.value = props.tabs[0].name;
    }
  },
  {
    immediate: true
  }
);
watch(
  activeName,
  async () => {
    await nextTick();
    // debugger;
    resetFromFun();
  },
  {
    immediate: true
  }
);

/**
 * @description 设置值
 * **/

const resetFromFun = bool => {
  if (props.shape && props.shape.id && activeName.value) {
    data.title = props.shape?.name;
    !bool && setFormValue();
  } else {
    data.funi_form && data.funi_form.resetFields();
  }
};

const setFormValue = async () => {
  data.funi_form && (await data.funi_form.resetFields());
  let shapeData = funiBpmnDataFunc.getShapeExtensionData(props.shape);

  let obj = {};
  schema.value.forEach(item => {
    if (item.component == 'el-switch' && !shapeData[item.prop]) {
      obj[item.prop] = false;
    } else {
      obj[item.prop] = shapeData[item.prop] || '';
    }

    if (item.prop == 'userAndrole') {
      obj[item.prop] = {
        user: getArrType(shapeData['funiUserTask_user']),
        role: getArrType(shapeData['funiUserTask_role']),
        org: getArrType(shapeData['funiUserTask_org'])
      };
    }
    if (item.prop == 'userAndroleCopy') {
      obj[item.prop] = {
        user: getArrType(shapeData['funiUserTask_copy_user']),
        role: getArrType(shapeData['funiUserTask_copy_role']),
        org: getArrType(shapeData['funiUserTask_copy_org'])
      };
    }

    if (item.prop == 'btnGroup' && shapeData['btnGroup']) {
      let list = shapeData['btnGroup'].split(';');
      obj['btnGroup'] = [];
      for (let i = 0; i < list.length; i++) {
        obj['btnGroup'].push({});
        let arr = list[i].split(',');
        for (let j = 0; j < arr.length; j++) {
          let spArr = arr[j].split(':');
          let key = spArr[0];
          let val = spArr.slice(1).join(':');
          if (val == 'true') {
            val = true;
          } else if (val == 'false') {
            val = false;
          }
          obj['btnGroup'][i][key] = val;
        }
      }
    }
  });

  data.funi_form && data.funi_form.setValues(obj);
};

/**
 *
 * @description 外部提供接口调用
 * **/
const setFormValueEnum = async obj => {
  if (activeName.value === 'other') {
    data.funi_form && (await data.funi_form.setValues(obj));
  }
};

const setForm = e => {
  data.funi_form = e;
};

/**
 * @description  保存数据
 * @param {enum} object表单数据
 * **/
const saveAttrs = async object => {
  if (props?.shape?.id) {
    let obj = {};
    let mergeArrList = [];
    props.tabs.forEach(item => {
      mergeArrList.push(...item.schema());
    });
    let extensionData = funiBpmnDataFunc.getShapeExtensionData(props.shape);
    let fromData = JSON.parse(JSON.stringify(data.funi_form.getValues()));
    // 特殊数据处理 start
    data.taskMultiInstanceType = fromData?.taskMultiInstanceType
      ? fromData?.taskMultiInstanceType
      : extensionData?.taskMultiInstanceType
      ? extensionData?.taskMultiInstanceType
      : '';
    // 特殊数据处理 end

    for (let key in extensionData) {
      // 判断是否是表单的值 过滤其他属性
      let index = mergeArrList.findIndex(item => item.prop == key);
      if (key == 'funiUserTask_role' || key == 'funiUserTask_user' || key == 'funiUserTask_org') {
        index = 0;
      }
      if (!fromData.hasOwnProperty(key) && index > -1) {
        obj[key] = extensionData[key];
      }
    }

    // 用户角色选择
    if (fromData.hasOwnProperty('userAndrole')) {
      let userArr = fromData.userAndrole['user'].map((item, index) => {
        if (index == 0) {
          return item.id + '-TO-' + item.name;
        } else {
          return ';' + item.id + '-TO-' + item.name;
        }
      });
      let roleArr = fromData.userAndrole['role'].map((item, index) => {
        if (index == 0) {
          return item.id + '-TO-' + item.name;
        } else {
          return ';' + item.id + '-TO-' + item.name;
        }
      });
      let orgArr = fromData.userAndrole['org'].map((item, index) => {
        if (index == 0) {
          return item.id + '-TO-' + item.name;
        } else {
          return ';' + item.id + '-TO-' + item.name;
        }
      });
      obj['funiUserTask_user'] = userArr.join('');
      obj['funiUserTask_role'] = roleArr.join('');
      obj['funiUserTask_org'] = orgArr.join('');
    }

    // 抄送
    if (fromData.hasOwnProperty('userAndroleCopy')) {
      let userArr = fromData.userAndroleCopy['user'].map((item, index) => {
        if (index == 0) {
          return item.id + '-TO-' + item.name;
        } else {
          return ';' + item.id + '-TO-' + item.name;
        }
      });
      let roleArr = fromData.userAndroleCopy['role'].map((item, index) => {
        if (index == 0) {
          return item.id + '-TO-' + item.name;
        } else {
          return ';' + item.id + '-TO-' + item.name;
        }
      });
      let orgArr = fromData.userAndroleCopy['org'].map((item, index) => {
        if (index == 0) {
          return item.id + '-TO-' + item.name;
        } else {
          return ';' + item.id + '-TO-' + item.name;
        }
      });
      obj['funiUserTask_copy_user'] = userArr.join('');
      obj['funiUserTask_copy_role'] = roleArr.join('');
      obj['funiUserTask_copy_org'] = orgArr.join('');
    }

    // 按钮选择

    if (fromData.hasOwnProperty('btnGroup') && fromData['btnGroup'].length) {
      let strList = fromData.btnGroup.map((item, index) => {
        let str = '',
          i = 0;
        for (let key in item) {
          let val = item[key] === '' || item[key] === undefined || item[key] === null ? '' : item[key];

          if (i == 0) {
            str = `${key}:${val}`;
          } else {
            str += `,${key}:${val}`;
          }
          i++;
        }
        return str;
      });
      fromData['btnGroup'] = strList.join(';');
    }

    data.formSaveObj = {
      oldId: props.shape.id,
      type: props.shape.$type,
      ...obj,
      ...fromData
    };

    let cloneData = JSON.parse(JSON.stringify(data.formSaveObj));
    Reflect.deleteProperty(cloneData, 'userAndrole');
    Reflect.deleteProperty(cloneData, 'userAndroleCopy');
    if (cloneData.hasOwnProperty('prevPerson')) {
      cloneData.prevPerson = cloneData.prevPerson === 'true' || cloneData.prevPerson === true ? true : false;
      let prevPerson = mergeArrList.filter(item => item.prop == 'prevPerson');
      prevPerson[0].hidden({
        formModel: { taskMultiInstanceType: data.taskMultiInstanceType }
      })
        ? Reflect.deleteProperty(cloneData, 'prevPerson')
        : '';
    }
    if (object && object.hasOwnProperty('taskMultiInstanceType') && activeName.value !== 'other') {
      cloneData.taskMultiInstanceType = object.taskMultiInstanceType;
    }

    emit('updateData', cloneData);
  }
};

/**
 * @description 数组化
 * **/
const getArrType = str => {
  let arr = [];
  let getObject = list => {
    for (let i = 0; i < list.length; i++) {
      arr.push({
        id: list[i].split('-TO-')[0],
        name: list[i].split('-TO-')[1]
      });
    }
  };
  if (str && str.indexOf('-TO-')) {
    let arrFirst = str.split(';');
    arrFirst.length > 0 && getObject(arrFirst);
  }

  return arr;
};

// 初始化activeName
const resetActiveName = () => {
  activeName.value = props.tabs[0].name;
};
defineExpose({
  formSaveObj: data.formSaveObj,
  resetActiveName,
  saveAttrs,
  setFormValueEnum,
  resetFromFun
});
const rules = computed(() => {
  return {
    name: [{ required: props?.shape?.$type !== 'bpmn:SequenceFlow', message: '必填', trigger: 'change' }]
  };
});
</script>

<style scoped lang="scss">
.nodeName {
  width: 100%;
  padding: 20px 0;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
}
.formBox {
  /* border: 1px solid rgba(177, 177, 177, 0.345); */
  border-radius: 10px;
  box-sizing: border-box;
  padding: 10px;
  height: calc(100% - 110px);
  overflow-y: auto;
}

.btnGroup {
  width: 100%;
  display: flex;
  justify-content: center;
}
:deep(.el-tabs--card > .el-tabs__header) {
  height: 24px;
}
:deep(.el-tabs__item) {
  padding: 0 3px !important;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
}
:deep(.el-tabs__item:last-child) {
  padding: 0 3px !important;
}
.dataGroup {
  width: 100%;
  height: 100%;
}

:deep(.el-form-item .el-form-item__label:after) {
  content: '' !important;
}
</style>
