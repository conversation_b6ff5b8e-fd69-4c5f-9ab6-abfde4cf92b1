<template>
  <div class="roleChoose">
    <div class="roleList">
      <FuniCurd
        ref="funi_Curd"
        @get-curd="
          e => {
            data.funi_curd = e;
          }
        "
        height="calc(66vh - 100px)"
        :columns="data.columns"
        :pagination="false"
        :data="data.roleList"
        @select="_roleCurdSelect"
        @select-all="_roleCurdSelect"
        @row-click="
          ({ selection }) => {
            _roleCurdSelect(selection);
          }
        "
        size="small"
      >
        <template #header>
          <div>
            <el-input v-model="data.keyWord" style="width: 180px; margin-right: 10px" placeholder="角色名" />
            <el-button type="primary" @click="findRoleList">搜索</el-button>
          </div>
        </template>
      </FuniCurd>
    </div>
    <div class="chooseBtn">
      <el-button type="primary" @click="chooseFun" size="small">
        绑定
        <el-icon class="el-icon--right"><ArrowRight /></el-icon>
      </el-button>
      <el-button type="primary" size="small" @click="cancelFun" :icon="ArrowLeft">取消</el-button>
    </div>
    <div class="roleList">
      <FuniCurd
        ref="funi_Curd"
        @get-curd="
          e => {
            data.funi_curd = e;
          }
        "
        height="calc(66vh - 100px)"
        :columns="data.columns"
        :pagination="false"
        :data="data.roleListED"
        @select="_roleCurdSelectED"
        @select-all="_roleCurdSelectED"
        @row-click="
          ({ selection }) => {
            _roleCurdSelectED(selection);
          }
        "
        size="small"
      >
        <template #header>
          <div style="height: 32px; font-size: 16px; font-weight: bolder">已选择</div>
        </template>
      </FuniCurd>
    </div>
  </div>
</template>
<script setup>
import { reactive, nextTick, watch } from 'vue';
import { roleListAllHttp } from './../../../api/api.js';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
const props = defineProps({
  value: {
    type: Array,
    default: () => []
  }
});
const emit = defineEmits(['saveDate']);
const data = reactive({
  roleSelection: [],
  roleSelectionED: [],
  funi_curd: null,
  keyWord: undefined,
  keyWordED: undefined,
  roleList: [],
  roleListED: [],
  roleListEDShow: [],
  columns: [
    {
      type: 'selection',
      width: '55px'
    },
    {
      label: '名称',
      prop: 'name'
    },
    {
      label: '编码',
      prop: 'code'
    }
  ]
});
watch(
  () => props.value,
  () => {
    data.roleListED = props.value ? props.value : [];
  },
  {
    deep: true,
    immediate: true
  }
);
watch(
  () => data.roleListED,
  val => {
    data.roleListEDShow = val;
  }
);
const init = () => {
  data.keyWord = undefined;
  data.keyWordED = undefined;
  data.roleList = [];

  if (!props.value.length) {
    data.roleListED = [];
  }

  data.roleSelection = [];
  data.roleSelectionED = [];
  findRoleList();
};
const findRoleList = async () => {
  let { list } = await roleListAllHttp.value({ name: data.keyWord });
  data.roleSelection = [];
  data.roleList = [];
  data.roleList = list
    .filter(item => {
      let index = data.roleListED.findIndex(el => el.id == item.id);
      return index < 0;
    })
    .map(el => {
      return { name: el.name, id: el.id, code: el.code };
    });
};
/**
 * @description 设置已选择的角色
 * **/

const _roleCurdSelect = e => {
  data.roleSelection = e;
};
const _roleCurdSelectED = e => {
  data.roleSelectionED = e;
};

const chooseFun = () => {
  dataOpertion('roleList', 'roleListED', 'roleSelection');
  data.keyWordED = undefined;
};

const cancelFun = () => {
  dataOpertion('roleListED', 'roleList', 'roleSelectionED');
  data.keyWord = undefined;
};

const dataOpertion = async (list, listED, sele) => {
  data[listED].push(...JSON.parse(JSON.stringify(data[sele])));
  for (let i = 0; i < data[list].length; i++) {
    let index = data[sele].findIndex(item => item.id == data[list][i].id);
    if (index > -1) {
      data[list].splice(i, 1);
      i--;
    }
  }
  data[sele] = [];
  await nextTick();
  emit('saveDate');
};
defineExpose({
  init,
  getRoleList: () => data.roleListED
});
</script>
<style scoped>
@import url('./index.css');
</style>
