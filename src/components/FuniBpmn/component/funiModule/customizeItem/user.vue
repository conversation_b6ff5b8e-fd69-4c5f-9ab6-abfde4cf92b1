<template>
  <div class="userChoose">
    <div class="tree">
      <div style="height: 32px; font-size: 16px; font-weight: bolder; margin-bottom: 15px">组织机构</div>
      <div class="treeBox">
        <el-tree :data="data.treeData" :props="data.defaultProps" @node-click="nodeClick"></el-tree>
      </div>
    </div>
    <div class="userList">
      <FuniCurd
        height="calc(66vh - 100px)"
        :columns="data.columns"
        :pagination="false"
        :data="data.userList"
        @select="_userCurdSelect"
        @select-all="_userCurdSelect"
        @row-click="
          ({ selection }) => {
            _userCurdSelect(selection);
          }
        "
        size="small"
      >
        <template #header>
          <div style="height: 32px; font-size: 16px; font-weight: bolder">待选择</div>
        </template>
      </FuniCurd>
    </div>
    <div class="chooseBtn">
      <el-button type="primary" size="small" @click="chooseFun">
        绑定
        <el-icon class="el-icon--right"><ArrowRight /></el-icon>
      </el-button>
      <el-button type="primary" size="small" @click="cancelFun" :icon="ArrowLeft">取消</el-button>
    </div>
    <div class="userList">
      <FuniCurd
        ref="funi_Curd"
        @get-curd="
          e => {
            data.funi_curd = e;
          }
        "
        height="calc(66vh - 100px)"
        :columns="data.columns"
        :pagination="false"
        :data="data.userListED"
        @select="_userCurdSelectED"
        @select-all="_userCurdSelectED"
        @row-click="
          ({ selection }) => {
            _userCurdSelectED(selection);
          }
        "
        size="small"
      >
        <template #header>
          <div style="height: 32px; font-size: 16px; font-weight: bolder">已选择</div>
        </template>
      </FuniCurd>
    </div>
  </div>
</template>
<script setup>
import { reactive, nextTick, watch } from 'vue';
import { findOrgTreeHttp, findUsersByOrgIdHttp } from './../../../api/api.js';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
const props = defineProps({
  value: {
    type: Array,
    default: () => []
  }
});
const emit = defineEmits(['saveDate']);
const data = reactive({
  funi_curd: null,
  keyWord: undefined,
  userList: [],
  userListED: [],
  userSelectList: [],
  userSelectListED: [],
  treeData: [],
  defaultProps: {
    children: 'childrens',
    label: 'name'
  },
  orgId: '',
  columns: [
    {
      type: 'selection',
      width: '55px'
    },
    // {
    //   label: 'ID',
    //   prop: 'id'
    // },
    {
      label: '姓名',
      prop: 'name'
    }
  ]
});
const init = async () => {
  let { list } = await findOrgTreeHttp.value();
  data.userList = [];
  data.orgId = '';
  if (!props.value.length) {
    data.userListED = [];
  } else {
    data.userListED = props.value;
  }
  data.userSelectList = [];
  data.userSelectListED = [];
  data.treeData = list;
};
const getOrgUser = async id => {
  let { list } = await findUsersByOrgIdHttp.value({
    orgId: id,
    queryChildren: false
  });
  data.userList = [];

  data.userList = list
    .filter(item => {
      let index = data.userListED.findIndex(el => el.id == item.id);
      return index < 0;
    })
    .map(item => {
      return {
        id: item.id,
        name: item.nickName
      };
    });
};
const nodeClick = e => {
  data.orgId = e.id;
  getOrgUser(e.id);
};
const _userCurdSelectED = e => {
  data.userSelectListED = e;
};
const _userCurdSelect = e => {
  data.userSelectList = e;
};

const chooseFun = () => {
  dataOpertion('userList', 'userListED', 'userSelectList');
};

const cancelFun = () => {
  dataOpertion('userListED', 'userList', 'userSelectListED', true);
};

const dataOpertion = async (list, listED, sele, bool) => {
  if (!bool) {
    data[listED].push(...JSON.parse(JSON.stringify(data[sele])));
  }
  for (let i = 0; i < data[list].length; i++) {
    let index = data[sele].findIndex(item => item.id == data[list][i].id);
    if (index > -1) {
      data[list].splice(i, 1);
      i--;
    }
  }
  bool && data.orgId && getOrgUser(data.orgId);
  data[sele] = [];

  await nextTick();
  emit('saveDate');
};
defineExpose({
  init,
  getUserList: () =>
    data.userListED.map(item => {
      return { id: item.id, name: item.name };
    })
});
</script>
<style scoped>
@import url(./index.css);
</style>
