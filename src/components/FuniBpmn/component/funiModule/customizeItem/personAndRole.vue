<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-06 17:16:07
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-07-03 10:50:00
 * @FilePath: /funi-paas-bpmn-ui/src/components/FuniBpmn/component/funiModule/customizeItem/personAndRole.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="personAndRole">
    <div class="showBox" v-html="data.showText"></div>
    <div class="setIcon">
      <el-icon @click="showDialog"><Setting /></el-icon>
      <el-icon @click="delFun"><Delete /></el-icon>
    </div>

    <funi-dialog v-model="dialogVisible" title="候选人" size="large" :hideFooter="true">
      <div class="chooseBox">
        <el-tabs v-model="activeName" type="card" class="demo-tabs">
          <el-tab-pane label="角色选择" name="role">
            <Role ref="roleTable" @saveDate="saveDate" :value="modelValue.role ? modelValue.role : []"></Role>
          </el-tab-pane>
          <el-tab-pane label="人员选择" name="user">
            <User ref="userTable" @saveDate="saveDate" :value="modelValue.user ? modelValue.user : []"></User>
          </el-tab-pane>
          <el-tab-pane label="机构选择" name="org">
            <Org ref="orgTree" @saveDate="saveDate" :value="modelValue.org ? modelValue.org : []"></Org>
          </el-tab-pane>
        </el-tabs>
      </div>
    </funi-dialog>
  </div>
</template>
<script setup>
import { ref, reactive, nextTick, watch } from 'vue';
import Role from './role.vue';
import User from './user.vue';
import Org from './org.vue';
const dialogVisible = ref(false);
const activeName = ref('role');
const roleTable = ref(null);
const userTable = ref(null);
const orgTree = ref(null);
const data = reactive({
  showText: ''
});
const emit = defineEmits(['update:modelValue', 'change']);
const props = defineProps({
  modelValue: {
    type: Object,
    default: {
      user: [],
      role: [],
      org: []
    }
  },
  defaultText: {
    type: String,
    default: ''
  }
});

watch(
  () => props.modelValue,
  () => {
    data.showText = props.defaultText;
    if (props.modelValue && props.modelValue.user && props.modelValue.role) {
      if (props.modelValue.user.length == 0) {
        data.showText += '<span>【用户】：</span>';
      }

      props.modelValue.user.map((item, index) => {
        if (index == 0) {
          data.showText += '<span>【用户】：</span>' + item.name;
        } else {
          data.showText += ',' + item.name;
        }
      });
      if (props.modelValue.role.length == 0) {
        data.showText += '<br/><span>【角色】：</span>';
      }
      props.modelValue.role.map((item, index) => {
        if (index == 0) {
          data.showText += data.showText
            ? '<br/><span>【角色】：</span>' + item.name
            : '<span>【角色】：</span>' + item.name;
        } else {
          data.showText += ',' + item.name;
        }
      });
      if (props.modelValue.org.length == 0) {
        data.showText += '<br/><span>【机构】：</span>';
      }

      props.modelValue.org.map((item, index) => {
        if (index == 0) {
          data.showText += data.showText
            ? '<br/><span>【机构】：</span>' + item.name
            : '<span>【机构】：</span>' + item.name;
        } else {
          data.showText += ',' + item.name;
        }
      });
    }
  },
  {
    immediate: true
  }
);

const showDialog = async () => {
  activeName.value = 'role';
  dialogVisible.value = true;
  await nextTick();
  roleTable.value.init();
  userTable.value.init();
};
const delFun = () => {
  emit('update:modelValue', {
    user: [],
    role: []
  });
  emit('change', {
    user: [],
    role: []
  });
};

/**
 * @description 保存数据
 * **/
const saveDate = () => {
  let roleList = roleTable.value.getRoleList();
  let userList = userTable.value.getUserList();
  let orgList = orgTree.value.gerOrgList();
  let obj = {
    user: userList,
    role: roleList,
    org: orgList
  };
  emit('update:modelValue', obj);
  emit('change', obj);
};
</script>
<style scoped>
@import url('./index.css');
</style>
