<template>
  <div>
    <el-tree
      ref="orgTree"
      :data="treeData"
      check-strictly
      show-checkbox
      node-key="id"
      :props="defaultProps"
      :defaultExpandedKeys="defaultExpandedKeys"
      @check="treeCheck"
    />
  </div>
</template>
<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { findOrgTreeHttp } from './../../../api/api.js';
onMounted(() => {
  console.log('onMounted');
  init();
});
const props = defineProps({
  value: {
    type: Array,
    default: []
  }
});
const defaultExpandedKeys = ref([]);
const emit = defineEmits(['saveDate']);
const orgTree = ref(null);
const defaultProps = {
  children: 'childrens',
  label: 'name'
};
const treeData = ref([]);
const init = async () => {
  let { list } = await findOrgTreeHttp.value();
  treeData.value = list;
  await nextTick();

  defaultExpandedKeys.value = [];
  props.value.map(item => {
    defaultExpandedKeys.value.push(item.id);
    orgTree.value.setChecked(item.id, true, false);
  });
};
const treeCheck = async () => {
  await nextTick();
  emit('saveDate');
};
const gerOrgList = () => {
  return orgTree.value.getCheckedNodes().map(item => {
    return {
      id: item.id,
      name: item.name
    };
  });
};
defineExpose({
  gerOrgList
});
</script>
