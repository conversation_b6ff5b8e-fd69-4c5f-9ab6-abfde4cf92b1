.personAndRole:deep(.el-dialog__body) {
    padding: 10px var(--el-dialog-padding-primary);
}

.personAndRole:deep(.el-tabs--card > .el-tabs__header) {
    height: 33px;
}

.personAndRole:deep(.el-tabs__item) {
    padding: 0 20px !important;
    font-size: 14px;
    height: 33px;
    line-height: 33px;
}

.personAndRole:deep(.el-tabs__item:last-child) {
    padding: 0 20px !important;
}

.personAndRole:deep(.card) {
    border: unset;
    padding: 0;
}

.personAndRole {
    width: 100%;
    height: 200px;
    position: relative;
    overflow: hidden;
}

.showBox {
    width: 100%;
    height: 100%;
    border: 1px solid var(--el-border-color-light);
    border-radius: 5px;
    overflow-y: auto;
    padding: 5px;
    box-sizing: border-box;
    font-size: 12px;
    line-height: 20px;
    color: var(--el-text-color-regular);
}

.showBox:deep(span) {
    color: black;
}

.personAndRole:hover .setIcon {
    display: inline-flex;
}

.setIcon {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
    padding: 0 5px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: -1px 1px 4px 2px #cbcbcb75;
    background-image: radial-gradient(circle, #fff 40%, #cbcbcb2c 100%);
    height: 30px;
    align-items: center;
    color: black;
}

.setIcon>i:last-child {
    margin-left: 8px;
}

.setIcon>i:hover {
    color: var(--el-color-primary);
}

.chooseBox {
    width: 100%;
    height: 66vh;
    overflow-y: auto;
}



/* 弹窗 */


.roleChoose {
    width: 100%;
    height: calc(66vh - 50px);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.userChoose {
    display: grid;
    grid-template-columns: 250px 1fr 100px 1fr;
    grid-auto-rows: calc(66vh - 100px)
}

.roleList {
    width: calc(50% - 50px);
    height: 100%;
    overflow: hidden;
}

.chooseBtn {
    width: 100px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.chooseBtn:deep(button) {
    margin: 10px 0;
}



.userList {
    overflow: hidden;
}

.tree {
    overflow: auto;
}

.treeBox {
    width: 100%;
    height: calc(100% - 49px);
    box-sizing: border-box;
    padding: 5px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 2px;
    overflow: auto;
}