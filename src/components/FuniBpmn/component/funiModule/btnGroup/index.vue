<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-06 17:16:07
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-05-30 11:34:24
 * @FilePath: /funi-paas-bpmn-ui/src/components/FuniBpmn/component/funiModule/btnGroup/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="btnGroup">
    <div class="showBox" v-html="data.showText"></div>
    <div class="setIcon">
      <el-icon @click="showDialog"><Setting /></el-icon>
      <el-icon @click="delFun"><Delete /></el-icon>
    </div>
  </div>
  <funi-dialog v-model="dialogVisible" title="按钮" size="large">
    <div v-loading="data.loading">
      <funi-curd
        :columns="data.columns"
        row-key="uuid"
        @get-curd="
          e => {
            data.funi_curd = e;
          }
        "
        :data="data.btnList"
        :show-overflow-tooltip="false"
        :pagination="false"
        :rowSelection="false"
        height="60vh"
        size="small"
      >
        <template #buttonGroup>
          <el-button type="primary" @click="addBtn">新增 </el-button>
        </template>
      </funi-curd>
    </div>
    <template #footer>
      <div>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveDate"> 确认 </el-button>
      </div>
    </template>
  </funi-dialog>
</template>
<script setup lang="jsx">
import { ref, reactive, nextTick, watch, onMounted, computed, isVNode, unref } from 'vue';
import { getDefaultTaskBehaviorHttp } from './../../../api/api.js';
import IconSet from './iconSet/index.vue';

import { ElNotification } from 'element-plus';
const dialogVisible = ref(false);

const data = reactive({
  showText: '',
  btnList: [],
  funi_curd: null,
  loading: false,
  columns: [
    {
      type: 'selection',
      width: '40px',
      fixed: 'left',
      showOverflowTooltip: false,
      selectable: (row, index) => {
        return !row.disabled;
      }
    },

    {
      label: '序号',
      prop: '',
      width: 60,
      render: ({ row, index }) => {
        return <span>{index + 1}</span>;
      }
    },
    {
      label: '默认名称',
      prop: 'name'
    },
    {
      label: '自定义名称',
      prop: 'customName',
      width: 200,
      render: ({ row, index }) => {
        return (
          <div class={row.disabled ? 'must' : 'customName'}>
            <el-input
              modelValue={row.customName}
              onInput={e => {
                iptChange(index, 'customName', e);
              }}
              style={{ width: row.disabled ? 'calc(100% - 12px)' : '100%' }}
            ></el-input>
          </div>
        );
      }
    },
    {
      label: '按钮类型',
      prop: 'type',
      width: 250,
      render: ({ row, index }) => {
        return (
          <div class={row.disabled ? 'must' : 'bunType'}>
            {!row.isDefault ? (
              <el-input
                modelValue={row.type}
                onInput={e => {
                  iptChange(index, 'type', e);
                }}
                style={{ width: 'calc(100% - 12px)' }}
              ></el-input>
            ) : (
              row.type
            )}
          </div>
        );
      }
    },
    {
      label: '意见',
      prop: 'isOpinion',
      render: ({ row, index }) => {
        return (
          <el-select
            modelValue={row.isOpinion}
            onChange={e => {
              iptChange(index, 'isOpinion', e);
            }}
          >
            <el-option label="非必填" value="1" />
            <el-option label="必填" value="2" />
          </el-select>
        );
      }
    },
    {
      label: '排序',
      prop: 'order',
      width: 150,
      showOverflowTooltip: false,
      render: ({ row, index }) => {
        return (
          <el-input-number
            style="width:100px"
            min={0}
            precision={0}
            modelValue={row.order}
            onInput={e => {
              iptChange(index, 'order', e);
            }}
          ></el-input-number>
        );
      }
    },
    {
      label: '图标',
      align: 'center',
      prop: 'icon',
      showOverflowTooltip: false,
      render: ({ row, index }) => {
        return (
          <IconSet
            modelValue={row.icon}
            index={index}
            onChange={(e, i) => {
              iptChange(i, 'icon', e);
            }}
          ></IconSet>
        );
      }
    },
    {
      label: '操作',
      prop: '',
      align: 'center',
      fixed: 'right',
      showOverflowTooltip: false,
      render: ({ row, index }) => {
        return !row.isDefault ? (
          <el-button
            link
            type="primary"
            onClick={() => {
              delRow(row, index);
            }}
          >
            删除
          </el-button>
        ) : (
          ''
        );
      }
    }
  ]
});

const emit = defineEmits(['update:modelValue', 'change']);
const props = defineProps({
  modelValue: {
    type: Object,
    default: {
      user: [],
      role: []
    }
  }
});

watch(
  () => props.modelValue,
  () => {
    if (props.modelValue && props.modelValue.length) {
      data.showText = props.modelValue.map(item => {
        return item.customName || item.name;
      });
    } else {
      data.showText = '';
    }
  }
);

/**
 * @description 选择图标
 * **/

/**
 * 输入框数据变更
 * @param index:number 当前行的index
 * @param name:string 当前输入框的keyName
 * @param e:any 当前输入框返回值
 * **/
const iptChange = (index, name, e) => {
  data.btnList[index][name] = e;
};

const addBtn = async () => {
  data.btnList.push({
    name: '自定义按钮',
    customName: '',
    order: '',
    icon: '',
    type: '',
    isOpinion: '1',
    isDefault: false,
    disabled: true,
    checked: true,
    uuid: $utils.guid()
  });
  await nextTick();
  setChecked();
};
const getDefaultBtn = async bool => {
  data.loading = true;
  let { list } = await getDefaultTaskBehaviorHttp.value().finally(() => {
    data.loading = false;
  });
  let btnList = list.map(item => {
    return { ...item, isDefault: true, disabled: false, checked: false };
  });
  if (!bool) {
    data.btnList = btnList;
  } else {
    for (let i = 0; i < btnList.length; i++) {
      let index = data.btnList.findIndex(item => item.uuid == btnList[i].uuid);
      if (index > -1) {
        btnList.splice(i, 1);
        i--;
      }
    }
    data.btnList.push(...btnList);
  }
  data.btnList = $utils.orderBy(data.btnList, 'order');
  await nextTick();
  setChecked();
};

const setChecked = () => {
  data.btnList.map(row => {
    if (row.checked === true) {
      data.funi_curd.toggleRowSelection(row, true);
    }
  });
};

const delFun = () => {
  emit('update:modelValue', []);
  emit('change', []);
};

const showDialog = async () => {
  if (props.modelValue && props.modelValue.length) {
    data.btnList = props.modelValue;

    getDefaultBtn(true);
  } else {
    getDefaultBtn();
  }

  dialogVisible.value = true;
};

const delRow = (row, index) => {
  data.btnList.splice(index, 1);
};

/**
 * @description 保存数据
 * **/
const saveDate = () => {
  let list = data.funi_curd.getSelectionRows().map(item => {
    item['checked'] = true;
    return item;
  });

  for (let i = 0; i < list.length; i++) {
    if (!list[i].isDefault) {
      if (list[i].customName === '' || list[i].customName === null || list[i].customName === undefined) {
        ElNotification({
          title: '提示',
          message: `自定义按钮自定义名称必填`,
          type: 'warning'
        });
        return;
      }
      if (list[i].type === '' || list[i].type === null || list[i].type === undefined) {
        ElNotification({
          title: '提示',
          message: `自定义按钮的按钮类型必填`,
          type: 'warning'
        });
        return;
      }
    }
  }
  emit('update:modelValue', list);
  emit('change', list);
  dialogVisible.value = false;
};
</script>
<style scoped>
@import url('./index.css');
</style>
