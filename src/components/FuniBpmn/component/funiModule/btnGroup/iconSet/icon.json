["carbon:ibm-cloud-sysdig-secure", "ic:twotone-menu-book", "mdi:user-details-outline", "material-symbols:route-outline", "radix-icons:activity-log", "carbon:cloud-service-management", "fluent-mdl2:dictionary", "mingcute:safety-certificate-line", "uis:process", "fluent-mdl2:processing", "codicon:server-process", "uil:processor", "vaadin:file-process", "carbon:ibm-process-mining", "tabler:server-bolt", "ion:server-outline", "material-symbols:data-alert", "material-symbols:data-check", "mdi:user", "mdi:user-check-outline", "circum:calendar-date", "bi:calendar2-date", "mdi:dictionary", "material-symbols:phone-in-talk-outline", "material-symbols:phone-android-rounded", "ph:gear", "carbon:network-public", "ic:round-public", "material-symbols:house-outline-rounded", "ant-design:audit-outlined", "carbon:cloud-auditing", "icon-park-outline:audit", "fluent-mdl2:party-leader", "icon-park-outline:transport", "iconoir:security-pass", "material-symbols:disabled-by-default-outline", "material-symbols:location-disabled", "material-symbols:next-plan-outline-rounded", "material-symbols:queue-play-next-outline-rounded", "carbon:previous-outline", "mdi:page-previous", "mdi:calendar-start-outline", "mdi:contain-start", "material-symbols:text-select-jump-to-end-rounded", "ic:baseline-pin-end", "gg:smile-mouth-open", "icon-park-outline:folder-failed", "icon-park-solid:database-fail", "material-symbols:door-open-outline", "material-symbols:lock-open-outline-rounded", "material-symbols:tab-close-outline-rounded", "ph:push-pin-bold", "ph:push-pin-slash-fill", "mdi:push-notification-outline", "ic:round-call-split", "material-symbols:climate-mini-split", "material-symbols:vertical-split-rounded", "ph:split-horizontal-bold", "mdi:rhombus-split", "material-symbols:add", "material-symbols:cancel-schedule-send-outline-rounded", "ph:function-fill", "la:java", "fluent:javascript-16-regular", "ph:paper-plane-right-bold", "carbon:logo-react", "material-symbols:type-specimen-outline-rounded", "bxl:typescript", "bi:filetype-xml", "ic:baseline-wechat", "ic:outline-science", "icon-park-outline:sport", "material-symbols:edit-document-outline", "material-symbols:refresh-rounded", "material-symbols:pause-outline-rounded", "iconoir:submit-document", "ic:round-save-as", "material-symbols:auto-delete-outline-rounded", "material-symbols:arrow-back-ios-rounded", "material-symbols:arrow-forward-ios-rounded", "material-symbols:arrow-back-rounded", "material-symbols:arrow-forward-rounded", "material-symbols:arrow-insert-rounded", "material-symbols:arrow-outward-rounded", "material-symbols:arrow-range-rounded", "material-symbols:home-outline-rounded", "ion:earth", "mdi:map-marker-minus-outline", "ic:twotone-architecture", "mdi:building", "ic:outline-directions-car-filled", "material-symbols:vpn-key-outline-rounded", "mdi:cargo-ship", "mdi:docker", "la:jenkins", "ph:github-logo", "material-symbols:image-outline", "ic:outline-search", "ph:ruler", "codicon:horizontal-rule", "mdi:dice-multiple", "material-symbols:bedroom-parent-outline-rounded", "ri:parent-line", "icon-park-outline:protection", "ion:build-outline", "fluent-mdl2:build-definition", "material-symbols:camera-indoor-outline-rounded", "material-symbols:linked-camera", "ant-design:windows-filled", "mdi:linux-mint", "tabler:brand-android", "wpf:iphone", "ic:baseline-satellite-alt", "carbon:satellite-weather", "ic:outline-cloud-queue", "material-symbols:cloud-sync-outline-rounded", "ph:office-chair-bold", "mdi:bridge", "clarity:computer-line", "material-symbols:back-hand-outline-rounded"]