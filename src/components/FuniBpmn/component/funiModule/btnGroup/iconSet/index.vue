<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-01-16 16:36:43
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-03-23 16:55:59
 * @FilePath: /funi-paas-bpmn-ui/src/apps/bpmn/component/funiModule/btnGroup/iconSet/index.vue
 * @Description: 
 * Copyright (c) 2023 <NAME_EMAIL>, All Rights Reserved. 
-->
<template>
  <div>
    <div class="defaultShow">
      <div class="defaultIcon" v-if="modelValue">
        <FuniIcon :icon="modelValue" style="font-size: 18px; margin: 0 10px"></FuniIcon>
        <div class="delBtn" @click="delModelValue">
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
      <el-button type="primary" link @click="show"> 图标 </el-button>
    </div>

    <Teleport to="body">
      <funi-dialog
        :close-on-click-modal="false"
        v-model="data.dialogVisible"
        :title="data.title"
        width="800"
        align-center
      >
        <div class="iconBox">
          <span
            class="svgSpan"
            :class="[data.iconName == item ? 'clicked' : '']"
            v-for="item in iconObj"
            @click="data.iconName = item"
          >
            <FuniIcon :icon="item" style="font-size: 25px"></FuniIcon>
          </span>
        </div>
        <template #footer>
          <span>
            <el-button @click="data.dialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="okFunc"> 确定 </el-button>
          </span>
        </template>
      </funi-dialog>
    </Teleport>
  </div>
</template>
<script setup lang="jsx">
import { reactive, onMounted, watch } from 'vue';
import iconObj from './icon.json';
const data = reactive({
  dialogVisible: false,
  title: '图标',
  iconList: [],
  iconName: ''
});
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  index: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

onMounted(() => {});
const show = () => {
  data.iconName = props.modelValue || '';
  data.dialogVisible = true;
};

const okFunc = () => {
  emit('update:modelValue', data.iconName, props.index);
  emit('change', data.iconName, props.index);
  data.dialogVisible = false;
};
const delModelValue = () => {
  emit('change', '', props.index);
  emit('update:modelValue', '', props.index);
};
defineExpose({ show });
</script>
<style scoped>
.iconBox {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  width: calc(100% + 20px);
  margin-left: -10px;
}
.svgSpan {
  display: inline-flex;
  margin: 10px;
  justify-content: center;
  align-items: center;
  padding: 3px;
  border-radius: 2px;
}
.svgSpan {
  transition: all 0.3s;
}
.svgSpan:hover {
  transform: scale(1.2);
  background-color: var(--el-color-primary-light-7);
}

.clicked {
  background-color: var(--el-color-primary-light-7);
}
.defaultShow {
  display: flex;
  justify-content: center;
  align-items: center;
}
.defaultIcon {
  padding: 5px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 5px;
}
.defaultIcon:hover .delBtn {
  display: flex;
}
.delBtn {
  display: none;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0px;
  right: 0px;
  font-size: 10px;
  width: 10px;
  height: 10px;
  border: 1px solid var(--el-color-info-light-3);
  color: var(--el-color-info-light-3);
  border-radius: 50%;
}
</style>
