.btnGroup:deep(.el-dialog__body) {
    padding: 10px var(--el-dialog-padding-primary);
}

.btnGroup:deep(.el-tabs--card > .el-tabs__header) {
    height: 33px;
}

.btnGroup:deep(.el-tabs__item) {
    padding: 0 20px;
    font-size: 14px;
    height: 33px;
    line-height: 33px;
}

.btnGroup:deep(.el-tabs__item:last-child) {
    padding: 0 20px !important;
}

.btnGroup:deep(.card) {
    border: unset;
    padding: 0;
}

.btnGroup {
    width: 100%;
    height: 150px;
    position: relative;
    overflow: hidden;
}

.showBox {
    width: 100%;
    height: 150px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 5px;
    overflow-y: auto;
    padding: 5px;
    box-sizing: border-box;
    font-size: 12px;
    line-height: 20px;
    color: var(--el-text-color-regular);
}

.showBox:deep(span) {
    color: black;
}

.btnGroup:hover .setIcon {
    display: block;
}

.setIcon {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
    padding: 0 5px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: -1px 1px 4px 2px #cbcbcb75;
    background-image: radial-gradient(circle, #fff 40%, #cbcbcb2c 100%);
}

.setIcon>i:last-child {
    margin-left: 8px;
}

.setIcon>i:hover {
    color: var(--el-color-primary);
}

.chooseBox {
    width: 100%;
    height: 66vh;
    overflow-y: auto;
}


:deep(.must::before) {
    content: "*";
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: red;
    width: 12px;
}

:deep(.customName) {
    display: flex;
    justify-content: end;
    align-items: center;
}