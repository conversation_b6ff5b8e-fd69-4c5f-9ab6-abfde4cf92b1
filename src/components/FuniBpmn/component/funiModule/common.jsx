/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-02 16:07:03
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-07-24 13:53:43
 * @FilePath: /funi-paas-bpmn-ui/src/components/FuniBpmn/component/funiModule/common.jsx
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import PersonAndRole from './customizeItem/personAndRole.vue'
import SelectBpmn from './selectBpmn/index.vue'
import PrevPerson from './prevPerson/index.vue'
import ExtendInfo from './extendInfo/index.vue'
import BtnGroup from './btnGroup/index.vue'
import FunctionBanner from './function/index.vue'

// 基本信息
export const beaseInfo = (object) => {
    return {
        name: 'bease',
        label: '基本信息',
        schema: (saveAttrs = () => { }) => {
            let { type } = object
            let arr = []
            arr.push(
                ...[{
                    label: type === 'bpmn:Process' ? '流程ID' : '节点ID',
                    component: null,
                    prop: 'id',
                    props: {
                        style: {
                            fontSize: '12px',
                            wordBreak: 'break-all',
                            'line-height': '20px'
                        }
                    }
                },
                {
                    label: type === 'bpmn:Process' ? '流程名称' : '节点名称',
                    component: 'el-input',
                    prop: 'name',
                    on: {
                        input: saveAttrs,
                        change: saveAttrs
                    },
                    props: {
                        placeholder: '名称'
                    }
                },
                ]
            )

            if (type == 'bpmn:UserTask' || type == 'bpmn:ServiceTask') {
                arr.push(
                    ...[
                        {
                            label: '通知主题',
                            component: 'el-input',
                            prop: 'funiServiceTopic',
                            on: {
                                input: saveAttrs,
                                change: saveAttrs
                            },
                            props: {
                                placeholder: '通知主题'
                            }
                        }
                    ]
                )
            }
            if (type == 'bpmn:UserTask') {
                arr.push(...[
                    {
                        label: '申请人执行权限',
                        component: () => <el-switch active-value="1"></el-switch>,
                        prop: 'funiUserTask_currentApplicant',
                        on: {
                            input: saveAttrs,
                            change: saveAttrs
                        },
                        props: {
                            style: {
                                width: '100%'
                            },
                        }
                    }
                ])

            }


            if (type !== 'bpmn:SequenceFlow') {
                arr.push(
                    ...[
                        {
                            label: '按钮',
                            component: () => <BtnGroup></BtnGroup>,
                            prop: 'btnGroup',
                            on: {
                                input: saveAttrs,
                                change: saveAttrs
                            },
                            props: {
                                placeholder: '按钮'
                            }
                        }
                    ]
                )
            }

            if (type == 'bpmn:SequenceFlow') {
                arr.push(
                    ...[
                        {
                            label: '条件表达式',
                            component: () => <el-input></el-input>,
                            prop: 'conditionExpression',
                            on: {
                                input: saveAttrs,
                                change: saveAttrs
                            },
                            props: {
                                placeholder: '条件表达式'
                            }
                        }
                    ]
                )
            }

            arr.push(
                ...[
                    {
                        label: '备注',
                        component: 'el-input',
                        prop: 'documentation',
                        on: {
                            input: saveAttrs,
                            change: saveAttrs
                        },
                        props: {
                            type: 'textarea',
                            placeholder: '备注'
                        }
                    }
                ]
            )

            return arr
        }
    }
};


// 人员角色
export const personRole = (type) => {
    console.log('type', type)
    return {
        name: 'other',
        label: type == 'UserTask' ? '人员/角色' : type == 'ServiceTask' ? '外部任务' : '',
        schema: (saveAttrs = () => { }) => {
            return type == 'UserTask' ? [
                {
                    label: '候选人',
                    component: () => (
                        <PersonAndRole></PersonAndRole>
                    ),
                    prop: 'userAndrole',
                    on: {
                        change: saveAttrs
                    },
                    props: {
                        placeholder: '角色',
                        clearable: true
                    }
                },
                {
                    label: '抄送',
                    component: () => (
                        <PersonAndRole></PersonAndRole>
                    ),
                    prop: 'userAndroleCopy',
                    on: {
                        change: saveAttrs
                    },
                    props: {
                        placeholder: '角色',
                        clearable: true
                    }
                },



                {
                    label: '',
                    component: () => {
                        return <SelectBpmn></SelectBpmn>
                    },
                    prop: 'taskMultiInstanceType',
                    on: {

                        change: saveAttrs
                    },
                    'label-width': '0px',
                    props: {
                        style: {
                            width: '100%'
                        },
                        param: {
                            typeCode: 'funi-bpmn-handle-rule'
                        }
                    }
                },
                {
                    label: '',
                    component: () => <PrevPerson></PrevPerson>,
                    prop: 'prevPerson',
                    on: {
                        change: saveAttrs
                    },
                    'label-width': '0px',
                    hidden: (obj) => {
                        return obj.formModel?.taskMultiInstanceType !== 'taskMultiInstanceType-1'
                    },
                    props: {
                        style: {
                            width: '100%'
                        },
                    }
                },

            ] : type == 'ServiceTask' ? [
                {
                    label: '外部任务',
                    component: 'el-input',
                    prop: 'externalTask',
                    on: {
                        input: saveAttrs,
                        change: saveAttrs
                    },
                    'label-width': '80px',
                    props: {
                        placeholder: '外部任务',
                        clearable: true
                    }
                },
            ] : [];
        }
    }
}

export const extendInfo = () => {
    return {
        name: 'extendInfo',
        label: '扩展信息',
        schema: (saveAttrs = () => { }) => {
            return [
                {
                    label: '',
                    component: () => <ExtendInfo></ExtendInfo>,
                    prop: 'funiExtendInfo',
                    'label-width': '0px',
                    on: {
                        change: saveAttrs
                    },
                    props: {

                    }
                }
            ]
        }
    }
}