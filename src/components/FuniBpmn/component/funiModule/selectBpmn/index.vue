<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-07 17:35:34
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-04-25 17:24:15
 * @FilePath: /funi-paas-bpmn-ui/src/apps/bpmn/component/funiModule/selectBpmn/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div :style="{ '--addWidth': props.addWidth }" class="bpmnSelect">
    <div class="topLabel">多人办理规则:</div>
    <el-select
      v-bind="$attrs"
      @change="selectCahnge"
      :modelValue="props.modelValue"
    >
      <el-option
        v-for="item in data.dicList"
        :key="item.code"
        :label="item.name"
        :value="item.code"
      />
    </el-select>
  </div>
</template>
<script setup>
import { reactive, onMounted } from 'vue';
import {data_bpmn} from './../../../api/api.js'
const emit = defineEmits(['update:modelValue', 'change']);
const data = reactive({
  dicList: []
});
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  addWidth: {
    type: String,
    default: '0px'
  },
  url: {
    type: String,
    default: '/csops/dic/selectDicByType'
  },
  param: {
    type: Object,
    default: () => {}
  }
});
onMounted(() => {
  data.dicList = data_bpmn.defaultSettings.dicTaskMultiInstanceType;
});

const selectCahnge = e => {
  emit('update:modelValue', e);
  emit('change', e);
};
</script>
<style scoped>
.bpmnSelect {
  width: calc(100% + var(--addWidth));
  /* position: absolute; */
  /* margin-bottom: 15px; */
}
</style>
