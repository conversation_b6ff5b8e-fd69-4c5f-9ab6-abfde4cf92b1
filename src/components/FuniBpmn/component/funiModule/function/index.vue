<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-06 17:16:07
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-07-03 17:00:25
 * @FilePath: /funi-paas-bpmn-ui/src/components/FuniBpmn/component/funiModule/function/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="funcBanner">
    <div class="showBox" v-html="data.showText"></div>
    <div class="setIcon">
      <el-icon @click="showDialog"><Setting /></el-icon>
      <el-icon @click="delFun"><Delete /></el-icon>
    </div>
    <funi-dialog v-model="dialogVisible" title="函数" size="large" :hideFooter="true">
      <div class="chooseBox">
        <div class="funcBox">
          <!-- <div></div> -->
        </div>
        <div class="funcItem" v-for="item in data.funcList">
          <div class="func-name">
            <span>
              <funi-icon icon="tabler:math-function" />
              {{ item.functionName }}</span
            >
            <el-button type="primary" text="primary" link size="small" @click="addFunc">
              <el-icon><Plus /></el-icon>
              添加
            </el-button>
          </div>
          <div class="func-description">
            {{ item.description }}
          </div>
        </div>
      </div>
    </funi-dialog>

    <funi-dialog v-model="dialogVisible1" title="参数" size="large" :hideFooter="true">
      <div class="chooseBox2">
        <div>参数一：</div>

        <div>参数二：</div>
      </div>
    </funi-dialog>
  </div>
</template>
<script setup>
import { ref, reactive, nextTick, watch } from 'vue';
import { getDefaultAssigneeFunctionTypeHttp } from './../../../api/api.js';

const dialogVisible = ref(false);
const dialogVisible1 = ref(false);
const data = reactive({
  showText: '',
  funcList: []
});
const emit = defineEmits(['update:modelValue', 'change']);
const props = defineProps({
  modelValue: {
    type: Object,
    default: {}
  },
  defaultText: {
    type: String,
    default: ''
  }
});

watch(
  () => props.modelValue,
  () => {
    data.showText = props.defaultText;
  },
  {
    immediate: true
  }
);

const showDialog = async () => {
  dialogVisible.value = true;
  init();
};
const init = async () => {
  let { list } = await getDefaultAssigneeFunctionTypeHttp.value();

  data.funcList = list;
};
const addFunc = () => {
  dialogVisible1.value = true;
};
const delFun = () => {};
</script>
<style scoped>
@import url('./index.css');
</style>
