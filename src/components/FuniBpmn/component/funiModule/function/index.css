* {
    box-sizing: border-box;
}

.funcBanner {
    width: 100%;
    height: 100px;
    position: relative;
    overflow: hidden;
}

.showBox {
    width: 100%;
    height: 100%;
    border: 1px solid var(--el-border-color-light);
    border-radius: 5px;
    overflow-y: auto;
    padding: 5px;
    box-sizing: border-box;
    font-size: 12px;
    line-height: 20px;
    color: var(--el-text-color-regular);
}

.showBox:deep(span) {
    color: black;
}

.funcBanner:hover .setIcon {
    display: inline-flex;
}

.setIcon {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
    padding: 0 5px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: -1px 1px 4px 2px #cbcbcb75;
    background-image: radial-gradient(circle, #fff 40%, #cbcbcb2c 100%);
    height: 30px;
    align-items: center;
    color: black;
}

.setIcon>i:last-child {
    margin-left: 8px;
}

.setIcon>i:hover {
    color: var(--el-color-primary);
}

.chooseBox {
    width: 100%;
    height: 50vh;
    overflow-y: auto;
}

.chooseBox2 {
    width: 100%;
    height: 300px;
    overflow-y: auto;
}

.funcBox {
    width: 100%;
    height: 70px;
    border: 1px solid var(--el-border-color-light);
    margin-bottom: 10px;
    border-radius: 4px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
}

.funcItem {
    width: 100%;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    padding: 5px 10px;
}

.func-name {
    font-size: 16px;
}

.func-description {
    font-size: 14px;
    color: var(--el-text-color-placeholder);
}