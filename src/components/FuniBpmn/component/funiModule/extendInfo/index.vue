<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-09 11:20:19
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-03-30 19:44:46
 * @FilePath: /funi-paas-bpmn-ui/src/apps/bpmn/component/funiModule/extendInfo/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="Tablebox">
    <div style="padding-bottom: 8px">
      <span>扩展属性</span>
      <el-button style="margin-left: 15px" type="primary" :icon="Plus" size="small" round @click="addObj"
        >新增</el-button
      >
    </div>
    <div class="columsTable">
      <ListTable :columns="columns" :dataList="dataList"> </ListTable>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, watch } from 'vue';
import ListTable from './listTable.vue';
import { Plus, Minus } from '@element-plus/icons-vue';
let dataList = ref([]);
const emit = defineEmits(['update:modelValue', 'change']);
const props = defineProps({
  modelValue: {
    type: Array,
    default: []
  }
});
watch(
  () => props.modelValue,
  () => {
    dataList.value = props.modelValue || [];
  }
);
const columns = computed(() => {
  return [
    {
      title: '名称',
      dataIndex: 'name',
      width: '30',
      customRender: ({ row, index }) => {
        return (
          <input
            value={row.name}
            onInput={e => {
              inputValue(e, 'name', index);
            }}
            class="tdInput"
          ></input>
        );
      }
    },
    {
      title: '值',
      dataIndex: 'value',
      width: '50',
      customRender: ({ row, index }) => {
        return (
          <input
            value={row.value}
            onInput={e => {
              inputValue(e, 'value', index);
            }}
            class="tdInput"
          ></input>
        );
      }
    },
    {
      title: '操作',
      dataIndex: '',
      align: 'center',
      width: '20',
      customRender: ({ row, index }) => {
        return (
          // <span style="font-size:13px;color:var(--el-color-primary);cursor: pointer;">删除</span>
          <el-popconfirm
            title="确定删除当前属性？"
            width="220"
            onConfirm={() => {
              delNowRow(row, index);
            }}
          >
            {{
              reference: () => <span style="font-size:13px;color:var(--el-color-primary);cursor: pointer;">删除</span>
            }}
          </el-popconfirm>
        );
      }
    }
  ];
});

const delNowRow = async (row, index) => {
  dataList.value.splice(index, 1);
  await nextTick();
  emitFunc();
};
const inputValue = async (e, dataIndex, index) => {
  dataList.value[index][dataIndex] = e.target.value;
  await nextTick();
  emitFunc();
  // if (checkData(dataList.value[index])) {
  //   emitFunc();
  // }
};

// 统一 emit 函数
const emitFunc = () => {
  emit('update:modelValue', dataList.value);
  emit('change', dataList.value);
};

// 校验数据
const checkData = row => {
  for (let key in row) {
    if (row[key] === '' || row[key] === null || row[key] === undefined) {
      return false;
    }
  }
  return true;
};
const addObj = async () => {
  dataList.value.push({
    name: '',
    value: ''
  });
  await nextTick();
  emitFunc();

  //   emit('update:modelValue', dataList.value);
  //   emit('change', dataList.value);
};
</script>
<style scoped>
.Tablebox {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.columsTable {
  width: 100%;
  height: min(600px, 60vh);
}
:deep(.tdInput) {
  width: calc(100% - 6px);
  height: 30px;
  border: 0;
  font-size: 14px;
  color: #606266;
}
</style>
