<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-10 00:28:45
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-03-23 17:43:10
 * @FilePath: /funi-paas-bpmn-ui/src/apps/bpmn/component/bpmn/previewXML.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-dialog v-model="dialogVisible" :align-center="true" :title="processName" size="max">
    <div class="codeBox markdown-body">
      <FuniHighlightCode preFatherWidth="auto" :code="codeXML"></FuniHighlightCode>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDate"> 确认 </el-button> -->
      </span>
    </template>
  </funi-dialog>
</template>

<script setup>
import { nextTick, ref } from 'vue';

let codeXML = ref('');
let processName = ref('');
let dialogVisible = ref(false);
const show = async (code, title) => {
  dialogVisible.value = true;
  await nextTick();
  codeXML.value = code;
  //   console.log(codeXML.value);
  processName.value = title;
};

defineExpose({
  show
});
</script>

<style scoped>
.codeBox {
  max-height: 60vh;
  overflow-y: auto;
  border-radius: 10px;
}
</style>
