/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-21 10:25:29
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-07-03 17:02:25
 * @FilePath: /funi-paas-bpmn-ui/src/components/FuniBpmn/component/bpmn/businessFunc.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import { ElNotification } from 'element-plus';
import { saveBpmnTemplateHttp, deployHttp, queryFuniProcessTemplateListHttp, setStarterByDeploymentIdHttp, deleteBpmnTemplateHttp } from './../../api/api.js';


/**
 * @description 保存当前xml
 * @param {object} bpmn
 * @param {object} process
 * @param {string} id
 * @param {Function} setLoading
 * @param {Function} callback
 * **/
const saveDiagram = (bpmn, process, id, setLoading, callback) => {
    let elementRegistry = bpmn.get('elementRegistry');
    let all = elementRegistry.getAll();
    // console.log('all',all)
    for (let i = 0; i < all.length; i++) {
        if (all[i].type !== 'bpmn:SequenceFlow' && all[i].id && all[i].id.indexOf('Funi') > -1 && !all[i].businessObject.name) {
            ElNotification({
                title: '提示',
                message: `有节点或者流程名称未填`,
                type: 'warning'
            });
            return;
        }
    }
    // 把传入的done再传给bpmn原型的saveXML函数调用;
    if (process.processKey) {
        bpmn.saveXML({ format: true }, function (err, xml) {

            let xmlFile = new File([xml], 'test.bpmn');
            const fileReader = new FileReader();
            fileReader.readAsDataURL(xmlFile);
            fileReader.onload = function () {
                let bease = this.result.split('base64,')[1];
                saveBpmnTemplateHttpFunc(bease, process, id, setLoading, callback);
            };
        });
    }
};


/**
 * @description 保存HTTP
 * @param {string} result xml的base64
 * @param {object} process 流程id、name对象
 * @param {string} id 版本id
 * @param {Function} setLoading loading函数
 * @param {Function} callback 回调函数
 * **/
const saveBpmnTemplateHttpFunc = async (result, process, id, setLoading, callback) => {
    setLoading(true)
    let res = await saveBpmnTemplateHttp.value({
        processName: process.processName,
        processKey: process.processKey,
        template: result,
        processTemplateId: id
    }).finally(() => {
        setLoading(false)
    });

    if (res.isSuccess) {
        ElNotification({
            title: '成功',
            type: 'success'
        });
        callback && callback(true);
    }
};


/**
 * @description 部署当前模板
 * @param {string} id 
 * @param {Function} setLoading loading函数
 * @param {Function} callback 回调函数
 * **/
const deployToEngine = async (id, setLoading, callback) => {
    setLoading(true)
    let res = await deployHttp.value({
        id
    }).finally(() => {
        setLoading(false)
    });

    if (res + '') {
        ElNotification({
            title: '成功',
            type: 'success'
        });
        callback && callback(true);
    }
}


/**
 * @description 获取所有版本
 * @param {string} key 流程key
 * **/
const getAllTemplateV = async (key) => {
    let { list } = await queryFuniProcessTemplateListHttp.value({
        processKey: key
    })
    return list
}


/**
 * @description 保存启动权限
 * @param {object} obj
 * @param {Function} setLoading loading函数
 * @param {Function} callback 回调函数
 * **/
const savePerRol = async (obj, id, setLoading, callback) => {

    let list = []
    obj.user.map(item => {
        list.push({
            assigneeType: 'U',
            assigneeId: item.id,
            assigneeName: item.name
        })
    })
    obj.role.map(item => {
        list.push({
            assigneeType: 'R',
            assigneeId: item.id,
            assigneeName: item.name
        })
    })
    obj.org.map(item => {
        list.push({
            assigneeType: 'O',
            assigneeId: item.id,
            assigneeName: item.name
        })
    })
    setLoading(true)
    let data = await setStarterByDeploymentIdHttp.value({ processInitiatorInfos: list, deploymentId: id }).finally(() => {
        !callback && setLoading(false)
    })
    if (data.authSuccess) {
        callback && callback()
    }
}

/**
 * @description 删除模板
 * @param {string} id 模板id
 * @param {Function} setLoading loading函数
 * @param {Function} callback 回调函数
 * **/
const deleteBpmnTemplate = async (id, setLoading, callback) => {
    setLoading(true)
    let data = await deleteBpmnTemplateHttp.value({
        bpmnTemplateId: id
    }).finally(() => {
        setLoading(false)
    })
    if (data) {
        ElNotification({
            title: '删除成功',
            type: 'success'
        });
        callback && callback(true);
    }
}

export {
    saveDiagram,
    getAllTemplateV,
    deployToEngine,
    savePerRol,
    deleteBpmnTemplate
}