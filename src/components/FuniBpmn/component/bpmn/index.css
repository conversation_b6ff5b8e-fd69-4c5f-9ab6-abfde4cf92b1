.containers {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #fff;
}

.funi-bpmn-canvas {
    width: calc(100vw - var(--menuBannerWidth));
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+') repeat !important;
}

#properties {
    width: 350px;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    box-shadow: -2px 0 7px 0 rgb(153 155 168 / 13%);
    background-color: #fff;
    box-sizing: border-box;
    padding: 20px 10px;
}

.btnGroup {
    position: absolute;
    top: 10px;
    right: calc(var(--menuBannerWidth) + 10px);
    display: flex;
    align-items: center;
}

.perRole {
    width: 350px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    box-sizing: border-box;
    padding: 20px 10px;
    height: 100%;
    box-shadow: -2px 0 7px 0 rgb(153 155 168 / 13%);
    color: var(--el-text-color-regular);
    font-size: 14px;
}

.nodeName {
    width: 100%;
    padding: 20px 0;
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    color: black;
}

.infoItem {
    display: flex;
    line-height: 30px;
    margin-bottom: 10px;
    width: 100%;
}

.infoLabel {
    display: inline-block;
    width: 80px;
    text-align: right;
    flex: 0 0 auto;
    padding-right: 10px;
    box-sizing: border-box;
}

.infoVal {
    display: inline-block;
    font-size: 12px;
    word-break: break-all;
    color: black;
    width: calc(100% - 80px);
}


.closeIcon {
    position: fixed;
    top: 15px;
    right: 15px;
    z-index: 999;
    color: var(--el-color-info);
}

.closeIcon:hover {
    color: var(--el-color-primary);
}