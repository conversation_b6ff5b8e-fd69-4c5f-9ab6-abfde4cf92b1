<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-27 13:50:25
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-07-24 13:56:08
 * @FilePath: /funi-paas-bpmn-ui/src/components/FuniBpmn/component/bpmn/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div
    class="containers"
    :style="{
      '--menuBannerWidth': bpmnType !== 'info' ? '350px' : '350px'
    }"
    v-loading="loading"
  >
    <div class="funi-bpmn-canvas" ref="funiBpmnCanvas"></div>
    <div id="properties" v-if="bpmnType !== 'info'">
      <funiModule
        :key="shapeObject?.id || '0000'"
        ref="funi_module"
        :shape="shapes"
        :type="type"
        @updateData="updateData"
        :tabs="tabs"
      ></funiModule>
    </div>
    <div class="btnGroup">
      <slot name="btns">
        <template v-if="bpmnType === 'info' && nowBpmn?.status == 1">
          <el-button type="primary" v-if="data_bpmn.sysId" @click="editFunc('bc')">
            <FuniIcon icon="material-symbols:edit-document-outline" style="margin-right: 3px" />
            修改</el-button
          >
          <el-button type="primary" @click="upgrade">
            <FuniIcon icon="ic:baseline-pin-end" style="margin-right: 3px" />
            升级版本
          </el-button>
        </template>

        <template v-if="bpmnType === 'info' && nowBpmn?.status == 0">
          <el-button type="primary" @click="editFunc">
            <FuniIcon icon="material-symbols:edit-document-outline" style="margin-right: 3px" />
            修改</el-button
          >
          <el-button type="primary" @click="deployFunc">
            <FuniIcon icon="mdi:cargo-ship" style="margin-right: 3px" />
            部署</el-button
          >
          <el-popconfirm width="260px" title="确定删除当前流程？" @confirm="delFunc">
            <template #reference>
              <el-button type="primary">
                <FuniIcon icon="material-symbols:auto-delete-outline-rounded" style="margin-right: 3px" />
                删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
        <template v-if="bpmnType !== 'info'">
          <el-button type="primary" @click="saveFunc">
            <FuniIcon icon="iconoir:submit-document" style="margin-right: 3px" />
            保存</el-button
          >
          <el-button type="primary" @click="previewXML">
            <FuniIcon icon="bi:filetype-xml" style="margin-right: 3px" />
            预览XML</el-button
          >
        </template>
        <el-button v-if="bpmnType === 'edit'" type="primary" @click="backFunc">
          <FuniIcon icon="material-symbols:arrow-back-ios-rounded" style="margin-right: 3px" />
          返回</el-button
        >
        <div v-if="bpmnType === 'info'" style="margin-left: 15px">
          <el-select style="width: 250px" :modelValue="nowBpmn?.id" @change="bpmnSeleCahnge">
            <el-option
              v-for="item in bpmnList"
              :key="item.id"
              :label="`${item.processName}：v${item.version ? item.version : '--'}`"
              :value="item.id"
            />
          </el-select>
        </div>
      </slot>
    </div>
    <div class="perRole" v-if="bpmnType == 'info'">
      <div class="nodeName">{{ nowBpmn?.processName || '--' }}</div>
      <div class="infoItem">
        <div class="infoLabel">流程编码</div>
        <div class="infoVal">{{ nowBpmn?.processKey || '--' }}</div>
      </div>
      <div class="infoItem">
        <div class="infoLabel">部署ID</div>
        <div class="infoVal">
          <span style="display: inline-block; line-height: 16px">{{ nowBpmn?.deploymentId || '--' }}</span>
        </div>
      </div>
      <div class="infoItem">
        <div class="infoLabel" style="font-size: 13px">流程定义ID</div>
        <div class="infoVal">
          <span style="display: inline-block; line-height: 16px">{{ nowBpmn?.processDefinitionId || '--' }}</span>
        </div>
      </div>
      <div class="infoItem">
        <div class="infoLabel">版本</div>
        <div class="infoVal">{{ nowBpmn?.version || '--' }}</div>
      </div>
      <div class="infoItem">
        <div class="infoLabel">状态</div>
        <div class="infoVal">{{ nowBpmn?.status == 1 ? '已部署' : '未部署' }}</div>
      </div>
      <div class="infoItem" v-if="nowBpmn?.status == 1">
        <div class="infoLabel">启动权限</div>
        <div class="infoVal">
          <PersonAndRole :modelValue="perAndRol" @change="perRolChange"></PersonAndRole>
          <!-- defaultText="<span style='font-size: 14px;font-weight: bold;margin-bottom: 5px;display:inline-block'>启动权限：</span><br/>" -->
        </div>
      </div>
    </div>
    <PreviewXML ref="preview_xml"></PreviewXML>

    <el-popconfirm width="260px" title="流程未保存，请确认是否关闭？" @confirm="setFullFun">
      <template #reference>
        <div class="closeIcon" @click="!hasChange && setFullFun()">
          <el-icon><Close /></el-icon>
        </div>
      </template>
    </el-popconfirm>
  </div>
</template>
<script setup>
import '@funi/bpmn-js/bpmn-utils/funi-bpmn-theme/index.css';
import { ref, onMounted, watch, reactive, nextTick, computed, inject } from 'vue';
import { beaseInfo, personRole, extendInfo } from '../funiModule/common.jsx';
import { saveDiagram, getAllTemplateV, deployToEngine, savePerRol, deleteBpmnTemplate } from './businessFunc.js';
import funiBpmnInit from '@funi/bpmn-js/bpmn-utils/funi-bpnm-init';
import funiBpmnOperation from '../../bpmn-uilt/funi-bpmn-operationData/index.js';
import funiBpmnDataFunc from './../../bpmn-uilt/funi-bpmn-dataFunc/index.js';
import funiModule from '../funiModule/index.vue';
import PreviewXML from './previewXML.vue';
import PersonAndRole from '../funiModule/customizeItem/personAndRole.vue';
import { decode } from './../../api/js-base64.js';
import { data_bpmn, setValue, setBpmnObject } from './../../api/api.js';
const funi_module = ref('');
const preview_xml = ref('');

const props = defineProps({
  loadFun: {
    type: Function,
    default: () => {}
  },
  saveFun: {
    type: Function,
    default: () => {}
  },
  type: {
    type: String,
    default: 'add'
  },
  processKey: {
    type: String,
    default: ''
  },
  isBack: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['clickElement', 'close', 'upgrade', 'back', 'editFunc']);
let hasChange = ref(false);
let shapeObject = null;

let funiBpmnCanvas = ref();
let funiBpmn = ref();
let bpmnType = ref('');
let nowBpmn = ref(null);
let bpmnList = ref([]);
let loading = ref(false);
let elementClicked = ref(false);
let firstLoading = ref(true);
let shapes = ref(null);
let perAndRol = ref(null);
let shapeUserTaskIsSequential = ref({
  id: '',
  sequential: false
});
let process = reactive({
  processName: '',
  processKey: ''
});
let updateTime = 0;
let changeTime = 0;
let { typeCode } = inject('funi-bpmn-data');

const tabs = computed(() => {
  let type = shapes.value?.$type || '';
  let arr = [beaseInfo({ type })];
  if (type === 'bpmn:UserTask') {
    arr.push(personRole('UserTask'));
  } else if (type == 'bpmn:ServiceTask') {
    arr.push(personRole('ServiceTask'));
  }
  arr.push(extendInfo());
  return arr;
});

onMounted(async () => {
  setBpmnObject(props.type, funiBpmnCanvas.value, typeCode);
  let xmlStr = '';
  if (props.type == 'info') {
    setLoading(true);
    bpmnList.value = await getAllTemplateV(props.processKey).finally(() => {
      setLoading(false);
    });

    nowBpmn.value = props.isBack ? data_bpmn.nowBpmn : bpmnList.value[0];
    setValue('nowBpmn', nowBpmn.value);
  } else if (props.type == 'edit') {
    nowBpmn.value = data_bpmn.nowBpmn;
  }
  xmlStr = nowBpmn.value?.template ? decode(nowBpmn.value?.template) : '';
  await nextTick();
  funiBpmn.value = data_bpmn.bpmnObject;
  funiBpmnInit.initBpmn(funiBpmn.value, xmlStr, firstSet);
  addEventBusListener();
  addModelerListener();
  document.getElementsByClassName('bjs-powered-by')[0].style.display = 'none';
  setPalette();
  setPerRol(nowBpmn.value?.processInitiatorInfos);
});

/**
 * @description 格式化启动权限
 * @param {array} arr
 * **/
const setPerRol = arr => {
  if (!arr) {
    return;
  }
  perAndRol.value = {
    user: [],
    role: [],
    org: []
  };
  arr.map(item => {
    if (item.assigneeType == 'R') {
      perAndRol.value.role.push({
        id: item.assigneeId,
        name: item.assigneeName
      });
    } else if (item.assigneeType == 'U') {
      perAndRol.value.user.push({
        id: item.assigneeId,
        name: item.assigneeName
      });
    } else if (item.assigneeType == 'O') {
      perAndRol.value.org.push({
        id: item.assigneeId,
        name: item.assigneeName
      });
    }
  });
};

//

/**
 * @description 切换版本
 * @param {string} e
 * **/
const bpmnSeleCahnge = e => {
  let list = bpmnList.value.filter(item => item.id == e);
  if (list.length) {
    nowBpmn.value = list[0];
    setValue('nowBpmn', nowBpmn.value);
    let xmlStr = nowBpmn.value?.template ? decode(nowBpmn.value?.template) : '';
    funiBpmnInit.initBpmn(funiBpmn.value, xmlStr, firstSet);
    setPerRol(nowBpmn.value?.processInitiatorInfos);
  }
};

/**
 * @description 设置当前type
 * @param {string} type的值
 * **/
const setBpmnType = e => {
  // store.bpmnType = e;
  bpmnType.value = e;
};

/**
 * @description 初始化
 * **/
const firstSet = async reset => {
  firstLoading.value = false;
  if (reset) {
    shapeObject = null;
    shapes.value = null;
  }
  await nextTick();
  var elementRegistry = funiBpmn.value.get('elementRegistry');
  let all = elementRegistry.getAll();
  if (props.type == 'add' && !reset) {
    initData(all);
  }

  for (let i = 0; i < all.length; i++) {
    if (all[i].type == 'bpmn:Process') {
      // console.log('all', all[i].businessObject);
      setShapes({
        element: all[i]
      });
      break;
    }
  }
};

// 新增功能初始化
const initData = all => {
  for (let i = 0; i < all.length; i++) {
    if (all[i].type === 'bpmn:Process') {
      funiBpmnOperation.updateProperties(
        all[i],
        {
          id: funiBpmnDataFunc.funiBpmnProcessID(),
          name: '默认名称'
        },
        element => {
          setShapes({ element });
        }
      );
    }
    if (all[i].type === 'bpmn:StartEvent') {
      funiBpmnOperation.updateProperties(
        all[i],
        {
          id: funiBpmnDataFunc.funiBpmnStartEventID(),
          name: '开始'
        },
        element => {
          setShapes({ element });
        }
      );
    }
  }
};

/**
 * @description 设置shape
 * @param {object} e
 * @param {boolean} bool
 * **/
const setShapes = async (e, bool) => {
  let elementRegistry = funiBpmn.value.get('elementRegistry');
  if (e.element.id && elementRegistry.get(e.element.id)) {
    if (!bool) {
      setFuniBpmnID(e);
      setFuniUserTaskRule(e);
    }
    shapeObject = e.element ? elementRegistry.get(e.element.id) : undefined;
    shapes.value = funiBpmnOperation.getRelevantBusinessObject(e.element);
    await nextTick();
    funi_module.value && funi_module.value.resetFromFun(true);
    if (e.element.type == 'bpmn:Process') {
      process.processKey = shapeObject?.businessObject.id;
      process.processName = shapeObject?.businessObject.name || '';
    }
    changeShape(e);
  }
};

/**
 * @description 切换元素节点的时候重置属性
 * @param {object} e 节点
 * **/
const resetShapes = e => {
  changeShape(e);
  let elementRegistry = funiBpmn.value.get('elementRegistry');
  let eleObject = e.element ? elementRegistry.get(e.element.id) : undefined;
  // 更新基本属性
  funiBpmnOperation.updateProperties(eleObject, {
    id: eleObject?.businessObject.id,
    name: eleObject?.businessObject.name
  });

  // 重置 funi
  funiBpmnOperation.updateFuniModdleProperties(eleObject, {});
  // 更新扩展属性
  funiBpmnOperation.updateModdleProperties(eleObject, []);

  // 更新描述
  funiBpmnOperation.setDocumentation(e.element, '', element => {
    setShapes({ element });
  });
};

/**
 * @description 元素设置funiID
 * @param {object} e 节点
 * **/
const setFuniBpmnID = e => {
  let elementRegistry = funiBpmn.value.get('elementRegistry');
  let shapeObj = e.element ? elementRegistry.get(e.element.id) : undefined;
  let funName = shapeObj.type == 'bpmn:Process' ? 'funiBpmnProcessID' : 'funiBpmnElementID';
  if (props.type !== 'info' && e.element.id.indexOf('Funi') < 0) {
    funiBpmnOperation.updateProperties(
      shapeObj,
      {
        id: funiBpmnDataFunc[funName]()
      },
      element => {
        setShapes({ element });
      }
    );
  }
};

/**
 * @description 设置userTask 规则
 * @param {object} e
 * **/
const setFuniUserTaskRule = e => {
  if (e.element.type == 'bpmn:UserTask') {
    let busData = funiBpmnOperation.getRelevantBusinessObject(e.element);
    let extensionData = funiBpmnDataFunc.getShapeExtensionData(busData, 'funiExtension');
    Reflect.deleteProperty(extensionData, 'id');
    Reflect.deleteProperty(extensionData, 'name');
    if (!extensionData.hasOwnProperty('taskMultiInstanceType')) {
      let elementRegistry = funiBpmn.value.get('elementRegistry');
      let shapeObj = e.element ? elementRegistry.get(e.element.id) : undefined;
      funiBpmnOperation.updateFuniModdleProperties(
        shapeObj,
        {
          ...extensionData,
          taskMultiInstanceType: 'taskMultiInstanceType-2'
        },
        element => {
          setShapes({ element }, true);
        }
      );
    }
  }
};

/**
 * @description 升级版本
 * **/
const upgrade = () => {
  emit('upgrade');
};
const editFunc = code => {
  emit('editFunc', code);
};

/**
 * @description 全屏设置
 * **/
const setFullFun = () => {
  emit('close');
};

/**
 * @description 返回
 * {boolean} bool 返回后是否更新选择数据
 * **/
const backFunc = bool => {
  emit('back', bool);
};
/**
 * @description 设置loading函数
 * @param {boolean} bool
 * **/
const setLoading = bool => {
  loading.value = bool;
};

/**
 * @description 预览xml
 * **/
const previewXML = () => {
  funiBpmn.value.saveXML({ format: true }, function (err, xml) {
    preview_xml.value.show(xml, process.processName);
  });
};

/**
 * @description 暂存本地shape
 * @param {object} e 节点
 * **/
const changeShape = e => {
  let obj = funiBpmnOperation.getRelevantBusinessObject(e.element);
  let copyObj = JSON.parse(JSON.stringify(obj));
  shapeUserTaskIsSequential.value.id = copyObj.id;
  shapeUserTaskIsSequential.value.type = copyObj.$type;
  shapeUserTaskIsSequential.value.sequential = copyObj?.loopCharacteristics?.isSequential || false;
};

/**
 * @description 保存启动权限
 * @param {object} obj
 * **/
const perRolChange = obj => {
  let id = nowBpmn.value?.deploymentId;
  savePerRol(obj, id, setLoading, perRolBack);
};

const perRolBack = async () => {
  let id = nowBpmn.value?.id;
  bpmnList.value = await getAllTemplateV(props.processKey).finally(() => {
    setLoading(false);
  });
  let list = bpmnList.value.filter(item => item.id == id);
  nowBpmn.value = list.length ? list[0] : null;
  setValue('nowBpmn', nowBpmn.value);
  bpmnSeleCahnge(id);
};

/**
 * @description 删除
 * **/
const delFunc = () => {
  let id = nowBpmn.value.id || undefined;
  deleteBpmnTemplate(id, setLoading, backFunc);
};

/**
 * @description 保存xml
 * **/
const saveFunc = () => {
  let id = props.isEdit ? nowBpmn.value.id : void 0;
  let func = props.type == 'add' ? setFullFun : backFunc;
  saveDiagram(funiBpmn.value, process, id, setLoading, () => {
    hasChange.value = false;
    func();
  });
};

/**
 * @description  部署时间
 * **/
const deployFunc = () => {
  deployToEngine(nowBpmn.value.id, setLoading, backFunc);
};

/**
 * @description 设置Palette 显示隐藏**/
const setPalette = () => {
  let value = bpmnType.value !== 'info' && typeCode !== 'bc' ? 'block' : 'none';
  funiBpmnInit.hidePalette(funiBpmnCanvas.value, value);
};

/**
 * @description 更新节点属性
 * @param {object} obj 节点需要更新的数据
 *
 * **/
const updateData = obj => {
  hasChange.value = true;
  if (obj.type === 'bpmn:UserTask') {
    updateTime++;
  }
  if (shapeObject && obj.oldId && obj.oldId === shapeObject.id) {
    if (obj.type === 'bpmn:Process') {
      process.processKey = obj.id;
      process.processName = obj.name || '';
      obj.isExecutable = true;
    }
    Reflect.deleteProperty(obj, 'oldId');
    Reflect.deleteProperty(obj, 'type');
    let extendObject = {};
    let isNotExtend = [
      'id',
      'name',
      'type',
      '$type',
      'isExecutable',
      'funiExtendInfo',
      'documentation',
      'conditionExpression'
    ];

    /**
     * @description 写入funi属性
     * **/
    for (let key in obj) {
      if (isNotExtend.indexOf(key) < 0) {
        extendObject[key] = obj[key];
      }
    }

    // 更新funi属性
    if (Object.keys(extendObject).length > 0) {
      funiBpmnOperation.updateFuniModdleProperties(
        shapeObject,
        {
          ...extendObject
        },
        element => {
          nextTick(() => {
            updateTime = 0;
            changeTime = 0;
            changeShape({ element });
          });
        }
      );
    }

    // 更新扩展属性
    if (obj.funiExtendInfo && obj.funiExtendInfo.length > 0) {
      funiBpmnOperation.updateModdleProperties(shapeObject, obj.funiExtendInfo);
    }

    // 更新描述
    if (obj.documentation) {
      funiBpmnOperation.setDocumentation(shapeObject, obj.documentation);
    }
    // 更新线的条件表达式
    if (obj.conditionExpression) {
      funiBpmnOperation.setConditionExpression(shapeObject, obj.conditionExpression);
    }
    // 更新基本属性
    funiBpmnOperation.updateProperties(
      shapeObject,
      {
        id: obj.id,
        name: obj.name
      },
      element => {
        setShapes({ element });
      }
    );
  }
};

/**********************************BPMN LISTENER START************************************/
/**
 * @description  addEventBusListener addModelerListener 两大监听事件
 * **/
const addEventBusListener = () => {
  const eventBus = funiBpmn.value.get('eventBus');
  const eventTypes = ['element.click', 'element.changed'];
  eventTypes.forEach(function (eventType) {
    eventBus.on(eventType, async function (e) {
      if (!e || e.element.type == 'bpmn:SequenceFlow') {
        shapeObject = null;
        shapes.value = null;
        setShapes(e);
        return;
      }
      if (!e || e.element.type == 'bpmn:Process') {
        setShapes(e);
        return;
      }
      elementClicked.value = true;
      if (eventType === 'element.changed') {
        hasChange.value = true;
        if (
          e.element.id == shapeUserTaskIsSequential.value.id &&
          e.element.type !== shapeUserTaskIsSequential.value.type
        ) {
          resetShapes(e);
        }

        if (
          e.element.type == 'bpmn:UserTask' &&
          e.element.id == shapeUserTaskIsSequential.value.id &&
          shapeUserTaskIsSequential.value.sequential !== e.element.businessObject.loopCharacteristics.isSequential &&
          changeTime == updateTime
        ) {
          let isSequential = e.element.businessObject.loopCharacteristics.isSequential;
          let val = isSequential ? 'taskMultiInstanceType-2' : 'taskMultiInstanceType-3';
          funi_module.value &&
            (await funi_module.value.setFormValueEnum({
              taskMultiInstanceType: val
            }));
          shapeUserTaskIsSequential.value.sequential = isSequential || false;
          funi_module.value &&
            (await funi_module.value.saveAttrs({
              taskMultiInstanceType: val
            }));
        }
      } else if (eventType === 'element.click') {
        funi_module.value && funi_module.value.resetActiveName();
        setShapes(e);
      }
    });
  });
};
const addModelerListener = () => {
  const events = ['shape.removed', 'shape.added'];
  events.forEach(function (event) {
    funiBpmn.value.on(event, e => {
      if (event === 'shape.removed' && e.element.type !== 'label') {
        firstSet(true);
      } else if (event === 'shape.added' && !firstLoading.value && e.element.type !== 'label') {
        setTimeout(
          obj => {
            setShapes(obj);
          },
          undefined,
          e
        );
      }
    });
  });
};
/**********************************BPMN LISTENER END************************************/

/**************************WATCH START**************************/
watch(
  () => props.xmlStr,
  () => {
    if (props.xmlStr) {
      funiBpmn.value ? funiBpmnInit.transformCanvas(funiBpmn.value, props.xmlStr, firstSet) : '';
    }
  },
  {
    immediate: true
  }
);

watch(
  () => props.type,
  () => {
    setBpmnType(props.type);
  },
  {
    immediate: true
  }
);
/**************************WATCH END**************************/
</script>
<style scoped>
@import url('./index.css');
</style>
