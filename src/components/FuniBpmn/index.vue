<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-13 16:24:47
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-06-12 16:13:11
 * @FilePath: /funi-paas-bpmn-ui/src/components/FuniBpmn/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <Transition>
    <div v-if="data.showBpmn" class="bpmnBox">
      <Bpmn
        v-if="data.bpmnKey"
        :type="data.type"
        :processKey="data.processKey"
        @close="close"
        @upgrade="upgrade"
        @back="back"
        @editFunc="editFunc"
        :isBack="data.isBack"
        :isEdit="data.isEdit"
      ></Bpmn>
    </div>
  </Transition>
</template>
<script setup lang="jsx">
import { reactive, onMounted, nextTick, provide } from 'vue';
import Bpmn from './component/bpmn/index.vue';
import { setValue } from './api/api.js';
const emit = defineEmits(['updateCurd']);
const data = reactive({
  type: 'add',
  id: '',
  processKey: '',

  showBpmn: false,
  bpmnKey: false,
  timer: null,
  isBack: false,
  isEdit: false,
  typeCode: void 0
});
provide('funi-bpmn-data', data);
onMounted(() => {});
const show = async ({ id = '', type = 'add', processKey = '', sysId = undefined, operator = undefined }) => {
  setValue('sysId', sysId);
  setValue('operator', operator);
  data.bpmnKey = false;
  data.id = id;
  data.type = type;
  data.showBpmn = true;
  data.processKey = processKey;
  if (type !== 'add' && data.id) {
    await nextTick();
    clearTimeout(data.timer);
    data.timer = setTimeout(() => {
      clearTimeout(data.timer);
      data.bpmnKey = true;
    }, 100);
  } else {
    data.bpmnKey = true;
  }
};

/**
 * @description 版本升级
 * **/
const upgrade = async () => {
  data.bpmnKey = false;
  data.isBack = false;
  data.isEdit = false;
  data.type = 'edit';
  data.typeCode = void 0;
  clearTimeout(data.timer);
  data.timer = setTimeout(() => {
    clearTimeout(data.timer);
    data.bpmnKey = true;
  }, 100);
};

/**
 * @description 修改 正对未部署数据
 * **/
const editFunc = code => {
  data.bpmnKey = false;
  data.isBack = false;
  data.isEdit = true;
  data.type = 'edit';
  data.typeCode = code;
  clearTimeout(data.timer);
  data.timer = setTimeout(() => {
    clearTimeout(data.timer);
    data.bpmnKey = true;
  }, 100);
};

/**
 * @description 编辑返回详情
 * {boolean} bool 是否更新当前选择数据
 * **/

const back = bool => {
  data.bpmnKey = false;
  data.isBack = bool ? false : true;
  data.type = 'info';
  data.typeCode = void 0;
  clearTimeout(data.timer);
  data.timer = setTimeout(() => {
    clearTimeout(data.timer);
    data.bpmnKey = true;
  }, 100);
};

const close = () => {
  emit('updateCurd');
  data.showBpmn = false;
  data.bpmnKey = false;
};

defineExpose({
  show
});
</script>
<style scoped>
.bpmnBox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2001;
  background-color: #fff;
}
:deep(.el-affix--fixed) {
  bottom: 16px !important;
}
.v-enter-active {
  animation: bpmn-in 0.3s;
}

.v-leave-active {
  animation: bpmn-in 0.3s reverse;
}
@keyframes bpmn-in {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
