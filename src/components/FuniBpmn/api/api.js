/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-28 10:57:43
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-07-03 14:42:06
 * @FilePath: /funi-paas-bpmn-ui/src/components/FuniBpmn/api/api.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import { reactive, computed, unref } from 'vue'
import funiBpmnModeler from '@funi/bpmn-js';
import funiBpmnCamunda from '@funi/bpmn-js/bpmn-utils/funi-bpmn-camunda'
import funiBpmnPalette from '@funi/bpmn-js/bpmn-utils/funi-bpmn-palette'
import { funiContextPadProvider, funiContextPadProviderEmpty } from '@funi/bpmn-js/bpmn-utils/funi-bpmn-contextPadProvider'
import ReplaceMenuProvider from '@funi/bpmn-js/bpmn-utils/funi-bpmn-popupMenu'



export const data_bpmn = reactive({
    sysId: undefined,
    operator: undefined,
    nowBpmn: null,
    bpmnObject: null,
    defaultSettings: {
        processEngine: "camunda",
        dicTaskMultiInstanceType: [
            { name: '单人办理', code: 'taskMultiInstanceType-1' },
            { name: '多人顺序', code: 'taskMultiInstanceType-2' },
            { name: '多人任意', code: 'taskMultiInstanceType-3' },
            { name: '多人并行', code: 'taskMultiInstanceType-4' },
        ]
    }

})
export const setValue = (name, value) => {
    data_bpmn[name] = value
}
export const setBpmnObject = (bpmnType, container, typeCode) => {
    let provider = (bpmnType !== 'info' && typeCode !== 'bc') ? funiContextPadProvider : funiContextPadProviderEmpty
    let object = new funiBpmnModeler({
        container: container,
        additionalModules: [funiBpmnPalette, provider, ReplaceMenuProvider],
        moddleExtensions: { camunda: funiBpmnCamunda }
    });
    data_bpmn.bpmnObject = object
}

const apiUrl = {
    roleListAll: '/csccs/role/listAll',
    findOrgTree: '/csccs/org/findOrgTree',
    findUsersByOrgId: '/csccs/user/findUsersByOrgId',
    getDefaultTaskBehavior: 'bpmn/config/getDefaultTaskBehavior',
    queryBpmnTemplate: 'bpmn/config/queryLastFuniProcessTemplateList',
    queryFuniProcessTemplateList: 'bpmn/config/queryFuniProcessTemplateList',
    saveBpmnTemplate: 'bpmn/config/setBpmnTemplate',
    deploy: 'bpmn/engine/deploy',
    setStarterByDeploymentId: 'bpmn/config/setStarterByDeploymentId',
    deleteBpmnTemplate: 'bpmn/config/deleteBpmnTemplate',
    getDefaultAssigneeFunctionType: 'bpmn/config/getDefaultAssigneeFunctionType'

}


export const roleListAllHttp = computed(() => {
    return (param) => {
        return $http.post(apiUrl.roleListAll, { ...param, sysId: data_bpmn.sysId })
    }
})
export const findOrgTreeHttp = computed(() => {
    return (param) => {
        return $http.fetch(apiUrl.findOrgTree, { ...param, sysId: data_bpmn.sysId })
    }
})
export const findUsersByOrgIdHttp = computed(() => {
    return (param) => {
        return $http.post(apiUrl.findUsersByOrgId, { ...param, sysId: data_bpmn.sysId })
    }
})
export const getDefaultTaskBehaviorHttp = computed(() => {
    return (params) => {
        const { operator, sysId } = data_bpmn
        return $http.post(apiUrl.getDefaultTaskBehavior, { ...params, operator, sysId })
    }
})
export const queryBpmnTemplateHttp = computed(() => {
    return (params) => {
        const { operator, sysId } = data_bpmn
        return $http.post(apiUrl.queryBpmnTemplate, { ...params, operator, sysId })
    }
})
export const queryFuniProcessTemplateListHttp = computed(() => {
    return (params) => {
        const { operator, sysId } = data_bpmn
        return $http.post(apiUrl.queryFuniProcessTemplateList, { ...params, operator, sysId })
    }
})
export const saveBpmnTemplateHttp = computed(() => {
    return (params) => {
        const { operator, sysId } = data_bpmn
        return $http.post(apiUrl.saveBpmnTemplate, { ...params, operator, sysId })
    }
})
export const deployHttp = computed(() => {
    return (params) => {
        const { operator, sysId } = data_bpmn
        return $http.post(apiUrl.deploy, { ...params, operator, sysId })
    }
})
export const setStarterByDeploymentIdHttp = computed(() => {
    return (params) => {
        const { operator, sysId } = data_bpmn
        return $http.post(apiUrl.setStarterByDeploymentId, { ...params, operator, sysId })
    }
})
export const deleteBpmnTemplateHttp = computed(() => {
    return (params) => {
        const { operator, sysId } = data_bpmn
        return $http.post(apiUrl.deleteBpmnTemplate, { ...params, operator, sysId })
    }
})
export const getDefaultAssigneeFunctionTypeHttp = computed(() => {
    return (params) => {
        const { operator, sysId } = data_bpmn
        return $http.post(apiUrl.getDefaultAssigneeFunctionType, { ...params, operator, sysId })
    }
})



