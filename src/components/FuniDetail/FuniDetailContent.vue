<!--
 * @Author:
 * @Date: 2023-04-20 16:05:58
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-09-23 11:25:33
 * @FilePath: \src\components\FuniDetail\FuniDetailContent.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<!--详情页内容组件-->
<template>
  <div class="funi-detail-content">
    <el-row v-if="bizName === '编辑' || bizName === 'edit' || bizName === '新建' || bizName === 'add'"
      style="display: block">
      <el-row v-if="steps && steps.length > 1"
        justify="center"
        class="funi-detail-content-stepbox">
        <FuniSteps :steps="steps"
          :active="current" />
      </el-row>
      <el-row class="funi-detail-content__step-container">
        <keep-alive>
          <component ref="stepRef"
            :is="steps[current].type || 'span'"
            v-bind="steps[current].props || {}"
            v-on="steps[current].on || {}" />
        </keep-alive>
      </el-row>
    </el-row>
    <el-row v-else
      style="display: block">
      <el-tabs class="funi-detail-content-tabbox"
        :class="steps&&steps.length===1?'funi-detail-content-single-tab':'funi-detail-content-multi-tab'"
        v-model="activeIndex"
        :before-leave="beforeLeave">
        <el-tab-pane v-for="(step, index) in steps"
          :key="index"
          :label="step.title"
          :name="index">
          <div class="display:flex;">
            <div>
              <component ref="stepRef"
                :is="step.type || 'span'"
                v-bind="step.props || {}"
                v-on="step.on || {}" />
            </div>
            <div v-if="index===0">
              <slot></slot>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <funi-teleport to=".funi-detail-content-tabbox > .el-tabs__header.is-top">
        <div class="funi-detail-content-tabbox__buttons">
          <component v-for="item in buttons"
            :key="item.name"
            :is="item.type || 'el-button'"
            v-auth.menu="item.menuAuth"
            v-bind="item.props"
            v-on="item.on">
            {{ item.name }}
          </component>
        </div>
      </funi-teleport>
    </el-row>
  </div>
</template>

<script setup>
import { ref, unref, watchEffect } from 'vue';
import FuniSteps from './FuniSteps.vue';
const props = defineProps({
  //步骤
  steps: {
    type: Array,
    default: () => {
      return new Array();
    }
  },
  //当前步骤索引
  current: {
    type: Number,
    default: 0
  },
  //业务操作:编辑、新增、详情、操作
  bizName: {
    type: String,
    default: '编辑'
  },
  buttons: {
    type: Array,
    default: () => []
  },
  /**自定义操作 */
  actions: {
    type: Array,
    default: () => []
  },
  /**
   * 切换标签之前的钩子函数， 若返回 false  或者返回被 reject 的 Promise，则阻止切换。
   */
  beforeLeave: {
    type: Function,
    default: () => {
      return true;
    }
  }
});
// console.log('props', props.steps);
const emit = defineEmits(['tabChange']);

const stepRef = ref(null); //步骤子组件
const activeIndex = ref(props.current);
if (['编辑', 'edit', '新建', 'add'].indexOf(props.bizName) >= 0) {
  watchEffect(() => {
    emit('tabChange', props.current);
  });
} else {
  watchEffect(() => {
    emit('tabChange', unref(activeIndex));
  });
}
//执行相应按钮的方法
function excute (param) {
  //如果子组件暴露了相应的方法,则执行该方法。该方法必须返回Promise
  if (stepRef.value && stepRef.value[param.type]) {
    return stepRef.value[param.type](param);
  } else {
    return new Promise(resolve => {
      resolve();
    });
  }
}

function setCurrent (cur) {
  activeIndex.value = cur;
}

//暴露给父组件使用
defineExpose({ excute, stepRef, setCurrent });
</script>
<style scoped lang="less">
.funi-detail-content {
  background: #ffffff;
  border-radius: 4px;

  &__step-container {
    margin: 16px;
    display: block;

    & > *:only-child {
      padding: 0px;
    }
  }

  &-stepbox {
    border-bottom: 1px dotted #e9e9e9;
    &-step {
      margin: 0 0 16px;
      width: 90%;
    }
  }

  :deep(&-tabbox) {
    --el-tabs-header-height: 36px;

    .el-tabs__header.is-top {
      margin-bottom: 0px;

      .el-tabs__nav-wrap:after {
        height: 1px;
      }
    }

    &__buttons {
      box-sizing: border-box;
      position: absolute;
      right: 0;
      bottom: 0;
      height: 100%;
      padding: 8px 16px;
      display: flex;
    }

    .el-tabs__content {
      padding: 0px 16px;
      .el-tab-pane > div:only-child {
        padding: 0px;
      }
    }

    .el-tabs__item {
      padding: 8px 16px !important;
    }

    .el-tabs__nav-scroll {
      padding-top: 8px;
    }
  }

  :deep(&-single-tab) {
    .el-tabs__header.is-top {
      display: none;
      height: 8px;
    }
  }
}
</style>
