<!--详情页组件-->
<template>
  <funi-teleport to=".layout-content__wrap">
    <div class="funi-detail">
      <div ref="fdcontent"
        class="funi-detail__content"
        :style="
          !!businessId && ['详情', 'detail', '审核', 'audit'].includes(bizName)
            ? { marginRight: scrolly ? '35px' : '40px' }
            : {}
        ">
        <FuniDetailHead v-if="showHeadComputed"
          class="mb-[12px]"
          :bizName="bizName"
          :homeRouter="homeRouter"
          :parentRouter="parentRouter"
          :option="detailHeadOption">
          <template v-for="item in ['headTitle', 'headStatus']"
            #[item]="{ option, bizName }">
            <slot :name="item"
              :option="option"
              :bizName="bizName"></slot>
          </template>
        </FuniDetailHead>

        <FuniDetailContent ref="contentRef"
          :steps="steps"
          :current="current"
          :bizName="bizName"
          :buttons="detailHeadOption.btns"
          @tab-change="onTabChange"
          :beforeLeave="beforeLeave">
          <div v-if="bizName === '审核' || bizName === 'audit'"
            id="teleportAuditContainer"
            :style="auditContainerStyle">
          </div>
        </FuniDetailContent>
      </div>

      <div v-if="bizName !== '详情' && bizName !== 'detail'"
        class="funi-action__btnGroup"
        :class="drawer ? 'drawer-open' : 'drawer-close'"
        :style="!!businessId && ['详情', 'detail', '审核', 'audit'].includes(bizName) ? { marginRight: '40px' } : {}">
        <slot v-if="bizName === '审核' || bizName === 'audit'"
          name="auditbtns"
          :steps="steps"
          :isNeedSubmit="isNeedSubmit"
          :showLastStep="showLastStep"
          :current="current"
          :auditButtons="auditButtons">
          <FuniAuditButtomBtn :auditButtons="auditButtons"
            @audit-click="e => $emit('auditClick', e)" />
          <div id="teleportBtns"
            style="z-index: 2010"></div>
        </slot>

        <template v-else>
          <slot name="editbtns"
            :steps="steps"
            :isNeedSubmit="isNeedSubmit"
            :showLastStep="showLastStep"
            :current="current"
            :backButtons="backButtons"
            :submitButtons="submitButtons">
            <FuniProcessBottomBtn :preservable="steps[current].preservable"
              :isNeedSubmit="isNeedSubmit"
              :showLastStep="showLastStep"
              :current-step="current"
              :submit-step="steps.length - 1"
              :submit-buttons="submitButtons"
              :back-buttons="backButtons"
              :loading-status="loadingStatus"
              @clickBtnEvent="clickBtnEvent" />
          </slot>
        </template>
      </div>
    </div>
    <div :class="{ 'funi-detail__loading': loadingStatus.status }"
      v-loading="loadingStatus.status"></div>
    <funi-bus-audit-drawer v-if="!!businessId && ['详情', 'detail', '审核', 'audit'].includes(bizName)"
      ref="fbadRef"
      @auditEvent="e => $emit('auditEvent',e)"
      @drawerChange="e => (drawer = e)"
      @workChange="handleWorkChange"
      :isAuthFixedBtn="isAuthFixedBtn"
      :isFormOpinion="isFormOpinion"
      :sysId="sysId"
      :businessId="businessId"
      :beforeAuditFn="beforeAuditFn"
      :onlyShow="bizName !== '审核' && bizName !== 'audit'" />
  </funi-teleport>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { ref, reactive, provide, toRef, computed, watch, watchEffect, onMounted, onBeforeUnmount } from 'vue';
import FuniDetailHead from './FuniDetailHead.vue';
import FuniDetailContent from './FuniDetailContent.vue';
import FuniProcessBottomBtn from '@/components/FuniProcessBottomBtn/index.vue';
import FuniAuditButtomBtn from '@/components/FuniAuditButtomBtn/index.vue';
import useMutationObserver from '@/utils/hooks/useMutationObserver.js';
const props = defineProps({
  //详情头部参数
  detailHeadOption: {
    type: Object,
    default: () => { }
  },
  //主页地址
  homeRouter: {
    type: Object,
    default: () => {
      return {
        path: '/'
      };
    }
  },
  //父菜单地址
  parentRouter: {
    type: Object,
    default: () => {
      let title, path;
      const router = useRouter();
      const routes = router.getRoutes();
      const backPath = (window.history.state.back || '').split('?')[0];
      if (routes && routes.length && backPath) {
        let backRoute = routes.find(r => r.path === backPath);
        if (backRoute && backRoute.meta) {
          title = backRoute.meta.title;
          path = backRoute.path;
        }
      }
      return {
        title,
        path
      };
    }
  },
  //业务操作
  bizName: {
    type: String,
    default: '编辑'
  },
  //步骤
  steps: {
    type: Array,
    default: () => {
      return new Array();
    }
  },
  //是否需要提交
  isNeedSubmit: {
    type: Boolean,
    default: true
  },
  //显示上一步
  showLastStep: {
    type: Boolean,
    default: true
  },
  //显示头部
  showHead: {
    type: Boolean,
    default: undefined
  },
  //返回位置按钮组
  backButtons: {
    type: Array,
    default: () => []
  },
  //提交
  submitButtons: {
    type: Array,
    default: () => [{ key: '提交', value: 'submit', type: 'submit', props: {} }]
  },
  //表单内置审核意见容器样式
  auditContainerStyle: {
    type: Object,
    default: () => {
      return {
        padding: '0px 0px'
      }
    }
  },
  //审核按钮
  auditButtons: {
    type: Array
  },
  //控制按钮组传送的属性
  actionsProps: Object,
  //业务件id 工作流用以及控制工作流组件显示隐藏,控制右边margin-right:64px;有工作流必传
  businessId: [String],
  //系统id,不传则自动获取当前sysId
  sysId: [String],
  //审核前执行的逻辑,要求返回promise 入参为审核按钮相关参数。resove 继续审核 reject中断
  beforeAuditFn: [Function],
  //审核抽屉固定按钮是否鉴权
  isAuthFixedBtn: {
    type: Boolean,
    default: true
  },
  //是否业务表单中提供审核意见  如果是则点击审批按钮（同意、退件、拒绝、退回）不弹出抽屉
  isFormOpinion: {
    type: Boolean,
    default: false
  },
  /**
   * 切换标签之前的钩子函数， 若返回 false  或者返回被 reject 的 Promise，则阻止切换。
   */
  beforeLeave: {
    type: Function,
    default: () => {
      return true;
    }
  }
});

const emit = defineEmits(['tabChange', 'clickBtnEvent']);
const fdcontent = ref(null);

const scrolly = ref(false);

const current = ref(0);
const drawer = ref(false);
const contentRef = ref(null);
const fbadRef = ref(null);
const tempData = reactive({});
let mutation = void 0;
const loadingStatus = ref({
  type: void 0,
  status: false
});

watch(fdcontent, () => {
  if (fdcontent.value) {
    mutation && mutation?.disconnect();
    mutation = useMutationObserver(fdcontent.value, void 0, setScrolly);
  }
});
onBeforeUnmount(() => {
  Object.assign(tempData, { workflow: {} });
  mutation && mutation?.disconnect();
});

//注入临时数据
provide('tempData', tempData);
provide('updateTempData', updateTempData);
provide('loadingStatus', loadingStatus);

const setScrolly = () => {
  scrolly.value = fdcontent.value && fdcontent.value.scrollHeight > fdcontent.value.clientHeight;
};

const showHeadComputed = computed(() => {
  if (props.showHead === null || props.showHead === undefined) {
    return ['新建', 'add'].indexOf(props.bizName) < 0;
  } else {
    return props.showHead;
  }
});

//点击按钮触发。如果子组件有与button.type同名的方法暴露出来将执行该方法。约定暴露的方法必须返回Promise。resolve继续执行下一步上一步操作。reject则中断操作
function clickBtnEvent (button) {
  if (button.triggerEvent) {
    emit('clickBtnEvent', button);
    return;
  }
  const { type } = button;
  const param = { type, current: current.value };
  loadingStatus.value.type = type;
  loadingStatus.value.status = true;
  contentRef.value
    .excute(param)
    .then(res => {
      switch (type) {
        case 'lastStep':
          if (current.value > 0) {
            current.value--;
          }
          break;
        case 'nextStep':
          if (current.value < props.steps.length - 1) {
            current.value++;
          }
          break;
        default:
          break;
      }
    })
    .finally(() => {
      loadingStatus.value.type = void 0;
      loadingStatus.value.status = false;
    });
}

function onTabChange (tabIndex) {
  current.value = tabIndex;
  emit('tabChange', tabIndex);
}

//更新临时数据
function updateTempData (tempObj) {
  Object.assign(tempData, tempObj || {});
}

function handleWorkChange (workflowInfo) {
  Object.assign(tempData, { workflow: workflowInfo || {} });
}

function setCurrent (cur) {
  current.value = cur;
  contentRef.value.setCurrent(cur);
}

function showLoading () {
  loadingStatus.value.status = true;
}

function hideLoading () {
  loadingStatus.value.status = false;
}

defineExpose({ updateTempData, contentRef, setCurrent, showLoading, hideLoading });
</script>

<style scoped lang="less">
.funi-detail {
  height: 100%;
  overflow: hidden;
  background-color: transparent;
  display: flex;
  flex-direction: column;

  &__content {
    flex-grow: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }

  &-head {
    flex-shrink: 0;
  }

  &-content {
    flex-grow: 1;
    // overflow: auto;
  }
}

.funi-detail__loading {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  pointer-events: none;
}

.funi-action__btnGroup {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
  padding-top: 10px;
  background-color: #ffffff;
}

.drawer-close {
  padding-right: 20px;
  transition: padding-right 0.3s;
}

.drawer-open {
  padding-right: 352px;
  transition: padding-right 0.3s;
}
</style>
