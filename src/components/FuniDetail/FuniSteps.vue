<template>
  <div
    class="funi-setps__box"
    :style="{
      '--steps-len': steps.length,
      '--step-columns': stepsColumns,
      '--step-customizeWidth': customizeWidth
    }"
  >
    <template v-for="(step, index) in steps">
      <div class="funi-step__item">
        <!-- {{ active }} -->
        <FuniIcon v-if="step.icon" :icon="step.icon" style="font-size: 18px"></FuniIcon>
        <span
          class="defaultIcon"
          :class="{
            'funi-step__finish': active > index,
            'funi-step__being': active == index
          }"
          v-else
        >
          <FuniIcon
            v-if="active > index"
            icon="material-symbols:check-small-rounded"
            style="font-size: 20px"
          ></FuniIcon>
          <span v-else style="font-size: 12px">{{ index + 1 }}</span>
        </span>
        <span
          class="funi-step__title"
          :class="{
            'funi-step__title__light': active > index || active == index
          }"
        >
          {{ step.title }}
        </span>
      </div>
      <div v-if="index !== steps.length - 1" class="chevron-right-rounded">
        <FuniIcon icon="material-symbols:chevron-right-rounded" style="font-size: 28px"></FuniIcon>
      </div>
    </template>
  </div>
</template>
<script setup lang="jsx">
import { computed } from 'vue';
import FuniIcon from './../FuniIcon/index.vue';
const props = defineProps({
  steps: {
    type: Array,
    default: () => []
  },
  active: {
    type: Number,
    default: 0
  }
});
const stepsColumns = computed(() => {
  let arr = props.steps.map(item => {
    return '1fr';
  });
  return arr.join(' 40px ');
});
const customizeWidth = computed(() => {
  let width = 0;
  let len = props.steps.length - 1;
  props.steps.map((item, index) => {
    let w = item.width || 200;
    width += w * 1;
    if (index !== len) {
      width += 40;
    }
  });

  return width + 'px';
});
</script>
<style scoped>
.funi-setps__box {
  width: min(calc(100% - 40px), var(--step-customizeWidth));
  display: grid;
  grid-template-columns: var(--step-columns);
  margin: 10px 0;
}
.defaultIcon {
  display: inline-flex;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.3);
  justify-content: center;
  align-items: center;
  transition: all 0.5s;
}
.funi-step__item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  transition: all 0.5s;
}
.funi-step__title {
  display: inline-block;
  margin-left: 12px;
}
.chevron-right-rounded {
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.3);
}

.funi-step__finish {
  color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
  transition: all 0.5s;
}
.funi-step__being {
  color: #fff;
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary);
  transition: all 0.5s;
}
.funi-step__title__light {
  color: var(--el-text-color-primary);
  font-weight: bolder;
}
</style>
