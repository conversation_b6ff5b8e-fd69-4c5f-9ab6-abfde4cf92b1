<!--详情页头部组件-->
<template>
  <div class="funi-detail-head">
    <slot name="headTitle" :option="option" :bizName="bizName">
      <span class="biz-title">
        {{ option.title }}
      </span>
    </slot>
    <slot name="headStatus" v-if="!option.hideStatusBar" :option="option" :bizName="bizName">
      <el-row justify="space-between">
        <span>
          <span class="biz-status">
            <span>{{ option.serialName ? option.serialName : '业务编号' }}:</span>
            <span class="ml-[12px]">{{ option.no ? option.no : '' }}</span>
          </span>
          <span class="biz-status" v-if="!option.hideStatusName">
            <span>{{ option.statusName ? option.statusName : '状态' }}:</span>
            <span class="ml-[12px]">{{ option.status ? option.status : '' }}</span>
          </span>
          <span class="biz-status" v-for="item in option.links" :key="item.name">
            <span>{{ item.title }}</span>
            <el-button class="ml-[12px]" v-bind="item.props" v-on="item.on">{{ item.name }}</el-button>
          </span>
        </span>
        <!-- <span>
          <el-button v-for="item in option.btns" :key="item.name" v-bind="item.props" v-on="item.on">{{
            item.name
          }}</el-button>
        </span> -->
      </el-row>
    </slot>
  </div>
</template>

<script setup>
const props = defineProps({
  //主页地址
  homeRouter: {
    type: Object,
    default: () => {
      return {
        path: ''
      };
    }
  },
  //父菜单地址
  parentRouter: {
    type: Object,
    default: () => {
      return {
        title: '',
        path: ''
      };
    }
  },
  //头部设置选项
  option: {
    type: Object,
    default: () => {
      return new Object();
    }
  },
  //业务操作标识
  bizName: {
    type: String,
    default: '编辑'
  }
});
</script>
<style scoped lang="less">
@color-text-gray: #1a233b;
@color-text-gray2: #8c8c8c;
.funi-detail-head {
  padding: 20px 20px 20px 16px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
  box-shadow: inset 0px -1px 0px #e8eaec;

  .breadcrumb-top {
    color: @color-text-gray2;
  }
  .biz-title {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    color: #1a233b;
    text-align: left;
    vertical-align: top;
  }
  .biz-status {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    text-align: left;
    vertical-align: top;
    color: @color-text-gray;
  }

  .biz-status + .biz-status {
    margin-left: 30px;
  }
  .mg-32 {
    margin-left: 30px;
  }
  .mg-16 {
    margin-left: 16px;
  }

  .mg-4 {
    margin-left: 4px;
  }
}
</style>
