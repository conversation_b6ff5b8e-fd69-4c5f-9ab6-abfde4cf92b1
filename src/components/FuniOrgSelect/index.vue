<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-25 18:30:00
 * @LastEditors: 孟雪峰 <EMAIL>
 * @LastEditTime: 2024-05-23 10:00:04
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniOrgSelect/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="checkBox">
    <el-tree-select
      v-model="value"
      :default-expanded-keys="defaultExpand"
      :multiple="props.mode !== 'radio'"
      style="width: 90%"
      :data="selectData"
      :props="prop"
      node-key="id"
      highlight-current
      check-strictly
      clearable
      filterable
      :expand-on-click-node="false"
      @node-click="nodeClick"
      :filter-node-method="filterNodeMethod"
    />
  </div>
</template>
<script setup lang="jsx">
import { ref, onMounted, nextTick, watch, inject, computed } from 'vue';
import { gsbmsGlobalApi } from './config/config.jsx';
import { orgListTreeHttp, specialQueryOrgTreeHttp, ccsOrgListTree } from './config/config.jsx';

const data = ref([]);
defineOptions({
  typeName: 'SLOT_ORG'
});
const props = defineProps({
  modelValue: [String, Array],
  mode: {
    type: Boolean,
    type: String,
    default: 'radio'
  },
  searchConfig: Object,
  searchFormModel: Object,
  attribute: Object,
  operate: Object,
  selectRange: {
    type: Array,
    default: () => {
      return [];
    }
  }
});
const emit = defineEmits(['update:modelValue']);
const value = ref('');
const defaultExpand = ref([]);
const prop = {
  value: 'id',
  label: 'orgShowName',
  children: 'children'
};

watch(
  () => props.searchFormModel,
  () => {
    if (props.attribute.dynamic && judgment()) {
      getFilterOrg();
    } else {
      _init();
    }
  },
  {
    deep: true
  }
);

const judgment = () => {
  let arr = [];
  if (arr.length > 1) {
    return true;
  } else if (arr.length == 1) {
    return arr[0].column !== props.searchConfig.prop;
  }
  return false;
};

const getFilterOrg = async () => {
  let { list } = await $http.post(props?.attribute?.slotUrl, {
    filters: [],
    queryField: props.searchConfig.prop,
    flag: false,
    ...(props?.attribute?.params || {})
  });
  let { list: orgList } = await $http.post(gsbmsGlobalApi.findOrgKVByOrgIds, {
    orgIds: list.map(item => item.value),
    flag: false
  });

  data.value = orgList.map(item => {
    return {
      children: [],
      id: item.value,
      orgName: item.key,
      orgShowName: item.key
    };
  });
};

onMounted(() => {
  value.value = props.modelValue;
  _init();
});

const _init = async () => {
  // let { list } = await specialQueryOrgTreeHttp();
  let { list } = await ccsOrgListTree();
  setOrgShowName(list);
  data.value = list;
};
const setOrgShowName = list => {
  list.forEach(item => {
    if (item.level === 0) defaultExpand.value.push(item.id);
    if (item.orgName) {
      item['orgShowName'] = item.orgName;
    } else if (item.orgShortName !== null && item.orgShortName !== void 0 && item.orgShortName !== '') {
      item['orgShowName'] = item.orgShortName;
    }
    let foo = props.selectRange.map(item => item.id);
    if (!foo.includes(item.id)) {
      item.disabled = true;
    }
    if (item.children && item.children.length) {
      setOrgShowName(item.children);
    }
  });
};

watch(
  () => props.modelValue,
  () => {
    (!props.modelValue || !props.modelValue.length) && (value.value = '');
  },
  { immediate: true }
);
watch(value, () => {
  emit('update:modelValue', value.value);
});

const selectData = computed(() => {
  return data.value;
  /* if (props.selectRange.length > 0) {
    return props.selectRange.map(item => ({ ...item, orgName: item.name, orgShowName: item.name }));
  } else {
    return data.value;
  } */
});

const filterNodeMethod = (value, data) => {
  let list = props.selectRange.map(item => item.id);
  if (list && list.length > 0) {
    return list.includes(data.id);
  } else {
    return true;
  }
};
const nodeClick = e => {
  nextTick(() => {
    emit('update:modelValue', value.value);
  });
};
</script>
<style scoped>
.checkBox {
  width: 240px;
  position: relative;
}
</style>
