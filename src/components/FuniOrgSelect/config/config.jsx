/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-16 09:52:36
 * @LastEditors: 孟雪峰 <EMAIL>
 * @LastEditTime: 2024-03-12 15:10:24
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniOrgSelect/config/config.jsx
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
// gsbms 公共接口
import { ElNotification } from 'element-plus';

const api = 'gsbms';
const gsbmsGlobalApi = {
  dictList: 'gsbms/common/dictList',
  findRootRegion: '/csccs/region/findRootRegion',
  findAccountPersonnel: '/csccs/accountList/findAccountPersonnel',
  findSonRegionTreeNoRestriction: '/csccs/region/findSonRegionTreeNoRestriction',
  getOrgByUserId: 'csccs/org/getOrgByUserId',
  findOrgAllPersonnel: 'csccs/orgList/findOrgAllPersonnel',
  getCurrentSonOrg: 'csccs/org/getCurrentSonOrg',
  getCurrentName: 'gsbms/common/getCurrentName',
  orgListTree: 'csccs/orgList/orgTree',
  orgListTree_: '/gsbms/common/specialQueryOrgTree',
  downloadImportTemplate: 'gsbms/common/downloadImportTemplate',
  attachList: 'gsbms/common/attachList',
  fileUpload: '/csccs/file/upload',
  saveAttachFile: 'gsbms/common/saveAttachFile',

  startBusiness: api + '/bus/startBusiness',
  executeBusiness: api + '/bus/executeBusiness',
  businessButton: api + '/bus/businessButton',
  commonButton: api + '/common/commonButton',
  workRecord: 'gsbms/bus/workRecord', //公用组件 审批记录

  contractDialogSale: api + '/contractSellList/queryContractSellListOther',
  contractDialogProcure: api + '/contractPurchaseList/queryContractPurchaseListOther',
  contractDialogotherContract: api + '/contract/allContract',
  getOperateLogList: api + '/operateLogList/getOperateLogList', //操作日志
  getOperateBusLogList: api + '/operateLogList/getOperateBusLogList', //操作日志
  getBusinessRecordList: api + '/common/queryBusinessUserRecordInfoList', //审批记录

  isJump: api + '/common/infoJurisdiction', //跳转前判断
  getCurrentCompany: '/csccs/org/getCurrentCompany', //获取用户所属公司
  getUserOrgTree: 'csccs/org/getUserOrgTree',

  getPerCustomerList: 'gsbms/perCustomerList/getPerCustomerList',
  getPerSupplierList: 'gsbms/perSupplierList/getPerSupplierList',

  getResourceTransfer: '/gsbms/common/resourceTransfer', //资源转移
  getSystemId: '/gsbms/common/systemInfo', //获取系统id
  roleList: '/csccs/roleList/roleList', //风险规则-角色列表
  findOrgList: '/csccs/orgGeneral/findOrg', //风险规则-规则适用范围-二极公司
  isPermission: 'csccs/roleList/isPermission', // 根据权限编码判断当前登录人是否存在该权限
  // specialQueryOrgTree: 'gsbms/common/specialQueryOrgTree', // 获取特殊orgtree

  getCompany: '/gsbms/common/getCurrentCompany', // 销售/采购合同 获取机构信息
  getOrgFields: '/gsbms/common/getOrgFields',
  findOrgKVByOrgIds: 'csccs/orgList/findOrgKVByOrgIds', // 高级搜索org交集处理
  findAccountKVByAccountIds: 'csccs/accountList/findAccountKVByAccountIds',
  downloadAttachment: '/gsbms/common/fileManage/downloadAttachment', //文件查看、下载记录接口
  updateColumn: 'gsbms/common/updateColumn', //详情查询字段是否有变更记录

  startPreBus: '/gsbms/bus/startPreBusiness' //预启动工作流
};

export const updateColumn = params => {
  return $http.post(gsbmsGlobalApi.updateColumn, params);
};

export const findAccountPersonnelHttp = params => {
  return $http.post(gsbmsGlobalApi.findAccountPersonnel, params);
};
export const getResourceTransferHttp = params => {
  return $http.post(gsbmsGlobalApi.getResourceTransfer, params);
};
export const getSystemIdHttp = params => {
  return $http.post(gsbmsGlobalApi.getSystemId, params);
};

export function isJump(params) {
  return $http.fetch(gsbmsGlobalApi.isJump, params).then(res => {
    return res;
  });
}

export function getCurrentCompany() {
  return $http.post(gsbmsGlobalApi.getCurrentCompany).then(res => {
    return {
      name: res.orgName,
      id: res.id
    };
  });
}

export function getContractCompany() {
  return $http.post(gsbmsGlobalApi.getCompany).then(res => {
    return {
      name: res.orgName,
      id: res.id
    };
  });
}

export const getOneList = params => {
  return $http.post(gsbmsGlobalApi.dictList, params);
};
export const contractDialogSale = params => {
  return $http.post(gsbmsGlobalApi.contractDialogSale, params);
};
export const contractDialogProcure = params => {
  return $http.post(gsbmsGlobalApi.contractDialogProcure, params);
};
export const contractDialogotherContract = params => {
  return $http.post(gsbmsGlobalApi.contractDialogotherContract, params);
};
const findRootRegionHttp = params => {
  return $http.post(gsbmsGlobalApi.findRootRegion, params);
};
const findSonRegionTreeNoRestrictionHttp = params => {
  return $http.post(gsbmsGlobalApi.findSonRegionTreeNoRestriction, params);
};
const getCurrentSonOrgHttp = params => {
  return $http.fetch(gsbmsGlobalApi.getCurrentSonOrg, params);
};
const getCurrentNameHttp = params => {
  return $http.fetch(gsbmsGlobalApi.getCurrentName, params);
};

const ccsOrgListTree = params => {
  return $http.fetch(gsbmsGlobalApi.orgListTree, params);
};

const orgListTreeHttp = params => {
  return $http.fetch(gsbmsGlobalApi.orgListTree_, params);
};
const getOrgByUserIdHttp = params => {
  return $http.fetch(gsbmsGlobalApi.getOrgByUserId, params);
};
const downloadImportTemplateHttp = params => {
  return $http.post(
    gsbmsGlobalApi.downloadImportTemplate + '?downloadTemplate=' + params.downloadTemplate,
    {},
    {
      responseType: 'blob'
    }
  );
};
const saveAttachFileHttp = params => {
  return $http.post(gsbmsGlobalApi.saveAttachFile, params);
};
const attachListHttp = params => {
  return $http.fetch(gsbmsGlobalApi.attachList, params);
};

export const startBusinessHttp = params => {
  return $http.fetch(gsbmsGlobalApi.startBusiness, params);
};
export const executeBusinessHttp = params => {
  return $http.post(gsbmsGlobalApi.executeBusiness, params);
};

export const startPreBusiness = params => {
  return $http.fetch(gsbmsGlobalApi.startPreBus, {
    dicBusinessTypeEnum: params.dicBusinessTypeEnum,
    preId: params.preId,
    lastBusinessId: params.lastBusinessId
  });
};
export const businessButtonHttp = params => {
  return $http.fetch(gsbmsGlobalApi.businessButton, params);
};

export const getOperateLogList = params => {
  return $http.fetch(gsbmsGlobalApi.getOperateLogList, params);
};

export const getOperateBusLogList = params => {
  return $http.post(gsbmsGlobalApi.getOperateBusLogList, params);
};

export const getUserOrgTreeHttp = params => {
  return $http.fetch(gsbmsGlobalApi.getUserOrgTree, params);
};
export const getPerCustomerListHttp = params => {
  return $http.post(gsbmsGlobalApi.getPerCustomerList, params);
};
export const getPerSupplierListHttp = params => {
  return $http.post(gsbmsGlobalApi.getPerSupplierList, params);
};

export const commonButton = params => {
  return $http.post(gsbmsGlobalApi.commonButton, params);
};
export const findOrgAllPersonnelHttp = params => {
  return $http.post(gsbmsGlobalApi.findOrgAllPersonnel, params);
};
export const specialQueryOrgTreeHttp = params => {
  return $http.post(gsbmsGlobalApi.specialQueryOrgTree, params);
};
export const getOrgFieldsHttp = params => {
  return $http.fetch(gsbmsGlobalApi.getOrgFields, params);
};

export const gInfoBtn = async params => {
  // let btnList = []
  // commonButton(params).then(res=>{
  //   res.forEach(item=>{
  //       btnList.push(item.businessExecutionType)
  //     })
  //   return btnList
  // }).catch(()=>{return []})
  let list = await commonButton(params);
  let btnList = [];
  list.forEach(item => {
    btnList.push(item.businessExecutionType);
  });
  return btnList;
};

export const getTodayDate = () => {
  const timeOne = new Date();
  const year = timeOne.getFullYear();
  let month = timeOne.getMonth() + 1;
  let day = timeOne.getDate();
  month = month < 10 ? '0' + month : month;
  day = day < 10 ? '0' + day : day;
  const nowDate = `${year}-${month}-${day}`;
  return nowDate;
};

export const getMonth = () => {
  const timeOne = new Date();
  const year = timeOne.getFullYear();
  let month = timeOne.getMonth() + 1;
  month = month < 10 ? '0' + month : month;
  const nowDate = `${year}-${month}`;
  return nowDate;
};

export const getTodayTime = () => {
  let date = new Date();
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let hour = date.getHours();
  let minute = date.getMinutes();
  let second = date.getSeconds();
  if (month < 10) {
    month = '0' + month;
  }
  if (day < 10) {
    day = '0' + day;
  }
  if (hour < 10) {
    hour = '0' + hour;
  }
  if (minute < 10) {
    minute = '0' + minute;
  }
  if (second < 10) {
    second = '0' + second;
  }
  return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
};

// 字典编码
const dicCode = {
  /**
   * @description 最初始**/
  cus_stage: 'cus_stage', //客户阶段
  cus_property: 'cus_property', //客户属性
  industry: 'industry', //所属行业
  project_type: 'project_type', //项目类型
  cooperate_mode: 'cooperate_mode', //项目合作模式
  project_stage: 'project_stage', //项目阶段
  cus_dimension: 'cus_dimension', //项目客户维度
  industry_sector: 'industry_sector', //项目所属行业领域
  org_role: 'org_role', //牵头机构角色
  product_classify: 'product_classify', //产品分类
  industry_direction: 'industry_direction', //产品行业方向
  gender: 'gender', //性别
  item_territory: 'item_territory', //行业领域
  patentType: 'patent_type', //类型
  lawType: 'law_status', //法律状态
  /**
   * @description 2023-05阶段
   * **/
  gsbms_cus_kind: 'gsbms_cus_kind', //客户种类
  gsbms_cus_source: 'gsbms_cus_source', //客户来源
  gsbms_cus_label: 'gsbms_cus_label', //标签
  con_settle: 'con_settle', //结款方式
  gsbms_cus_trade: 'gsbms_cus_trade', //客户行业
  gsbms_cus_import: 'gsbms_cus_import', //重要性

  income_type: 'cus_property',
  expenses_type: 'expenses_type', //费用类型
  income: 'income', //收入类型
  income_child: 'income_child', //收入类型
  cost: 'cost', //成本类型
  cost_child: 'cost_child', //成本类型
  receipt_way: 'receipt_way', //收款方式
  con_payment: 'con_payment', //付款方式
  bus_status: 'bus_status', //业务状态
  receipt_status: 'receipt_status', //收付款状态
  invoice: 'invoice', //开票
  payment_status: 'payment_status', //付款状态
  payment_way: 'payment_way', //付款方式
  milestone_node: 'milestone_node', //风险里程碑节点
  risk_level: 'risk_level', //风险等级
  clue_sources: 'clue_sources', //线索来源
  clue_stage: 'clue_stage', //线索阶段
  clue_level: 'clue_level', //线索等级
  coo_status: 'coo_status', //事项状态
  opp_sources: 'opp_sources', //商机来源
  opp_status: 'opp_status', //商机状态
  opp_stage: 'opp_stage', //商机阶段
  opportunity_level: 'opportunity_level', //商机等级
  cost_type: 'cost_type', //成本类型
  dic_certificate_type_code: 'certificate_type', //内部人员管理-人才资质-证书类别

  risk_type: 'risk_type', //风险类型
  bind_business: 'bind_business', //绑定业务
  source_table: 'source_table', //数据表
  source_field: 'source_field', //数据字段
  risk_operation_number: 'risk_operation_number', //运算类型
  risk_operation_date: 'risk_operation_date', //运算类型
  risk_measure: 'risk_measure', //计量单位
  item_examine: 'item_examine', //审批类型
  share_item_examine: 'share_item_examine', //投资股权项目审批类型
  share_type: 'share_type', //投资股权项目类型
  match_frequency: 'match_frequency' //计算频次
};

// 模板下载code
const downloadCode = {
  sc: 'SELL_CLUE', //线索资源信息导入模板
  so: 'SELL_OPPORTUNITY', //商机资源信息导入模板
  ii: 'ITEM_INFO', //项目立项信息导入模板
  ig: 'ITEM_GAIN', //项目获取信息导入模板
  ic$: 'ITEM_CHECK', //项目验收信息导入模板
  ir: 'ITEM_RECRUITING', //项目选聘归档信息导入模板
  ip: 'ITEM_PURCHASE', //项目采购归档信息导入模板
  ic: 'ITEM_COST', //项目成本信息导入模板
  pc: 'PER_CUSTOMER', //客户信息导入模板
  ps: 'PER_SUPPLIER', //供应商信息导入模板
  pi: 'PRODUCT_INFO', //产品信息导入模板
  ps$: 'PRODUCT_SCIENCE', //科改信息导入模板
  fc: 'FINANCE_COST', //计提成本导入模板
  fi: 'FINANCE_INCOME', //计提收入导入模板
  fe: 'FINANCE_EXPENSES', //其他关键指标导入模板
  fr$: 'FINANCE_RECEIVABLE', //应收导入模板
  fr: 'FINANCE_RECEIPT', //实收导入模板
  fp: 'FINANCE_PAYMENT', //应付导入模板
  fa: 'FINANCE_ACTUALLY', //实付导入模板
  ri: 'RISK_INFO', //风险事件导入模板
  riskRule: 'RISK_RULES_CONFIG', //风险规则
  cs: 'CONTRACT_SELL', //销售合同信息
  co: 'CONTRACT_OPP', //采购合同信息
  cg: 'EQUITY_INVEST_CONTRACT', //股权投资合同
  cq: 'OTHER_CONTRACT', //其他合同
  ta: 'PER_TALENT', //人才资质
  ex: 'PER_EXPERT', //内部专家
  exhis: 'PER_HISTORY_EXPERT', //内部专家 历史
  pa: 'PER_PARTNERS', //合作伙伴 partner
  in: 'PER_EQUITY_PARTNERS', //合作伙伴 股权 interest
  ap: 'ITEM_BUILD', //建设立项
  aps: 'ITEM_EFFECT_BUILD', //建设立项
  plan: 'ITEM_PLAN', //项目计划
  plans: 'ITEM_EFFECT_PLAN', //项目计划
  task: 'ITEM_TASK', //项目任务
  tasks: 'ITEM_EFFECT_TASK', //项目任务
  demand: 'ITEM_DEMAND', //需求
  demands: 'ITEM_EFFECT_DEMAND', //需求
  sdc: 'EQUITY_INVEST_CONTRACT', //股权投资合同
  oc1: 'OTHER_CONTRACT_1', //其他合同-合作框架协议
  oc2: 'OTHER_CONTRACT_2', //其他合同-培训服务协议
  oc3: 'OTHER_CONTRACT_3', //其他合同-战略合同
  oc: 'OTHER_CONTRACT', //其他合同-资源移交
  per: 'ITEM_PRE', //项目预立项
  sii: 'SHARE_PROJECT', //股权投资项目预立项
  sir: 'SHARE_REC', //股权投资选聘审批归档
  sip: 'SHARE_PUR', //股权投资选聘结果归档
  shpl: 'SHARE_PLAN', //股权投资方案审批
  shef: 'SHARE_EFFECT', //股权投资项目实施
  company: 'PER_QUALIFIED', //公司资质
  eco: 'PER_ECOLOGY_PARTNERS' //公司资质
};

const hideColumns = ['fileType', 'isNeed', 'needNumber', 'attachmentAmount', 'remark'];

/**
 * @description 格式化金额数字
 * @param {Number} num
 * **/
const gsbms_intl = num => {
  if (num === null || num === '' || num === undefined) {
    return '--';
  } else {
    num = num === void 0 || num === null || num === '' ? 0 : num;
    let isNegativeNumber = num < 0;
    const formatter = new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      // 输出人民币
      currency: 'CNY',
      // currencySign选项启用记帐格式
      currencySign: 'accounting'
    });
    let m = formatter.format(Math.abs(num));
    const insertStr = (source, start, newStr) => {
      return source.slice(0, start) + newStr + source.slice(start);
    };
    return isNegativeNumber ? insertStr(m, 1, '-') : m;
  }
};

// 导出表格
const expotrFunction = async ({ url, params, method = 'post', FileName, FileType = 'xls' }) => {
  let resData = await $http[method](url, params, {
    responseType: 'blob'
  });

  if (resData.type == 'application/json') {
    const reader = new FileReader();
    reader.onload = function () {
      const result = JSON.parse(reader.result); //此处的msg就是后端返回的msg内容
      if (result.success === false) {
        ElNotification({ title: result.message, type: 'error' });
      }
    };
    reader.readAsText(resData);
  } else {
    let downloadElement = document.createElement('a');
    let href = window.URL.createObjectURL(resData); //创建下载的链接
    downloadElement.href = href;
    downloadElement.download = FileName + '.' + FileType; //下载后文件名
    document.body.appendChild(downloadElement);
    downloadElement.click(); //点击下载
    document.body.removeChild(downloadElement); //下载完成移除元素
  }
};

// 组织数懒加载
const load = (node, resolve) => {
  let id = node?.data?.id || '';
  let param = { pid: id, currentType: '2' };
  getCurrentSonOrgHttp(param).then(res => {
    resolve(
      res.map(item => {
        return {
          ...item
        };
      })
    );
  });
};

const infoFormCommon = (creatorName = 'creatorName', createTime = 'createTime', updateTime = 'updateTime') => {
  return [
    // {
    //   label: '创建人',
    //   prop: creatorName,
    //   colProps: {
    //     span: 8
    //   },
    // },
    // {
    //   label: '创建时间',
    //   prop: createTime,
    //   colProps: {
    //     span: 8
    //   },
    // },
    {
      label: '更新时间',
      prop: updateTime,
      colProps: {
        span: 8
      }
    }
  ];
};
/**
 * @description 行点击权限校验
 * **/

export {
  gsbmsGlobalApi,
  dicCode,
  downloadCode,
  hideColumns,
  gsbms_intl,
  infoFormCommon,
  findRootRegionHttp,
  findSonRegionTreeNoRestrictionHttp,
  getCurrentSonOrgHttp,
  getCurrentNameHttp,
  expotrFunction,
  orgListTreeHttp,
  getOrgByUserIdHttp,
  downloadImportTemplateHttp,
  load,
  attachListHttp,
  saveAttachFileHttp,
  ccsOrgListTree
};
