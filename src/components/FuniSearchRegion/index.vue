<template>
  <el-cascader
    v-model="cascaderValue"
    ref="cascader"
    clearable
    style="width: 240px"
    placeholder="请输入"
    :props="cascaderProps"
    @change="handleValuesChange"
  />
</template>

<script setup>
import { computed, ref, watch } from 'vue';

defineOptions({
  name: 'FuniSearchRegion'
});

const props = defineProps({
  modelValue: [String, Number, Object],
  attribute: { type: Object, default: () => ({}) }
});

const emit = defineEmits(['update:modelValue']);
const cascader = ref();
const regionConfig = computed(() => JSON.parse(props.attribute.region || '{}'));
const rootRegionURL = computed(() => props.attribute.url || '/csccs/region/findRootRegion');
const sonRegionURL = computed(() => props.attribute.url || '/csccs/region/findSonRegionTreeNoRestriction');
const cascaderValue = ref();

const cascaderProps = computed(() => {
  return {
    lazy: true,
    value: 'code',
    label: 'name',
    checkStrictly: true,
    emitPath: false,
    lazyLoad(node, resolve) {
      if (node.level === 0) {
        $http
          .post(rootRegionURL.value)
          .then(res =>
            resolve(
              ((Array.isArray(res) ? res : res.list) || []).map(item =>
                Object.assign({}, item, { level: item.level || node.level + 1 })
              )
            )
          )
          .catch(err => resolve([]));
      } else {
        $http
          .post(sonRegionURL.value, { code: node.value, businessConfigCode: node.value })
          .then(res => {
            resolve(
              ((Array.isArray(res) ? res : res.list) || []).map(item => ({
                ...item,
                level: item.level || node.level + 1,
                leaf: (item.level || node.level + 1) === parseInt(regionConfig.value.maxLevel)
              }))
            );
          })
          .catch(err => resolve([]));
      }
    }
  };
});

watch(
  () => props.modelValue,
  () => {
    console.debug('props.modelValue', props.modelValue);
    if (!props.modelValue) {
      cascaderValue.value = '';
    }
  },
  { immediate: true }
);

const handleValuesChange = () => {
  const [node] = cascader.value.getCheckedNodes();
  const cascadeColumn = regionConfig.value.cascadeColumn || [];
  const columnKey = cascadeColumn.find(item => parseInt(item.level) === node.data.level)?.column;
  !!columnKey && emit('update:modelValue', { [columnKey]: node.value });
};
</script>
