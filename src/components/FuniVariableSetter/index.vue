<template>
  <div class="funi-variable-setter">
    <el-tooltip :content="displayCodes" :disabled="!displayCodes.length" placement="top">
      <el-input disabled class="funi-variable-setter__input" v-model="displayCodes" @input="handleInputEvent">
        <template #append>
          <el-tooltip :content="iconTooltip">
            <el-button class="funi-variable-setter__dialog-trigger" link @click="triggerDialog">
              <funi-icon color="black" icon="cil:functions" font-size="16" />
            </el-button>
          </el-tooltip>
        </template>
      </el-input>
    </el-tooltip>
    <BindVariableDialog ref="bindVariableDialog" :title="dialogTitle" :variables="variables" @confirm="handleConfirm" />
  </div>
</template>

<script setup>
import BindVariableDialog from './BindVariableDialog.vue';
import { ref, watch } from 'vue';
import { decodeFormula } from './utils';

defineOptions({ name: 'FuniVariableSetter' });

const props = defineProps({
  dialogTitle: { type: String, default: '表达式' },
  iconTooltip: { type: String, default: '表达式' },
  modelValue: { type: String, defaut: '{}' },
  variables: { type: Array, default: () => [] }
});
const emit = defineEmits(['update:modelValue', 'change']);

const codes = ref('');
const displayCodes = ref('');
const isExpress = ref(false);
const bindVariableDialog = ref();
const expression = ref('');

watch(
  () => props.modelValue,
  newVal => {
    let modelValueObj = {};
    try {
      modelValueObj = JSON.parse(newVal || '{}');
    } catch (ex) {
      console.log(ex);
    }
    codes.value = modelValueObj.codes ?? '';
    displayCodes.value = modelValueObj.label ?? '';
    isExpress.value = modelValueObj.isExpress;
  },
  { immediate: true }
);

const triggerDialog = () => {
  bindVariableDialog.value.show(isExpress.value ? codes.value : '');
};

const handleConfirm = value => {
  const { label, expression: expressionValue } = decodeFormula(value);
  codes.value = value;
  displayCodes.value = label;
  expression.value = expressionValue;
  isExpress.value = true;
  const modelValueJson = JSON.stringify({
    codes: codes.value,
    label: label,
    isExpress: true,
    expression: expression.value
  });
  emit('update:modelValue', modelValueJson);
  emit('change', modelValueJson);
};

const handleInputEvent = value => {
  codes.value = '';
  displayCodes.value = value;
  expression.value = `\`${value}\``;
  isExpress.value = false;
  const modelValueJson = JSON.stringify({
    codes: codes.value,
    label: value,
    isExpress: false,
    expression: expression.value
  });
  emit('update:modelValue', modelValueJson);
  emit('change', modelValueJson);
};

defineExpose({
  codes: codes,
  expression: expression,
  isExpress: isExpress
});
</script>

<style lang="scss" scoped>
.funi-variable-setter {
  width: 100%;
  display: flex;
  position: relative;
  padding-right: 22px;
  box-sizing: border-box;

  &__input {
    width: 100%;
  }

  // &__dialog-trigger {
  //   top: 0;
  //   right: 0;
  //   bottom: 0;
  //   width: 22px;
  //   position: absolute;
  //   box-sizing: border-box;
  // }
  &__dialog-trigger {
    width: 34px;
    box-sizing: border-box;
    margin: 0 !important;
  }

  :deep(.el-input-group__append) {
    padding: 0;
  }

  :deep(.el-input__inner) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
