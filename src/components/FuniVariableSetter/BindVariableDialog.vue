<template>
  <funi-dialog
    class="funi-variable-setter-dialog"
    :title="title"
    v-model="visible"
    size="large"
    append-to-body
    @confirm="handleConfirm"
  >
    <div class="funi-express-editor">
      <!-- <el-button type="primary" @click="handleButtonClick">测试按钮</el-button> -->
      <div class="funi-express-editor__main">
        <CodemirrorEditor ref="editor" v-model="code" />
        <div v-if="activeNode && activeNode.func" class="funi-express-editor__desc">
          <p style="line-height: 1.6em">函数示例：{{ activeNode.example }}</p>
          <p style="line-height: 1.6em">函数说明：{{ activeNode.description }}</p>
        </div>
      </div>
      <div class="funi-express-editor__side">
        <el-collapse v-model="activeNames">
          <el-collapse-item v-for="item in variablesValue" :title="item.label" :name="item.id" :key="item.id">
            <el-tree :expand-on-click-node="false" :data="item.children" @node-click="handleNodeClick">
              <template #default="{ node }">
                <div
                  style="display: flex; cursor: pointer; width: 100%; font-size: 12px"
                  @mouseenter="handleHover(node)"
                >
                  <span class="name" style="flex: 1 1 0%; overflow: hidden; text-overflow: ellipsis">
                    {{ node.label }}&nbsp;
                    <!-- <span class="weak-text">({{ node.data.description }})</span> -->
                  </span>
                  <!-- <span class="type-desc weak-text">{{ node.data.typeDesc }}</span> -->
                </div>
              </template>
            </el-tree>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </funi-dialog>
</template>
<script setup>
import { ref, computed, onMounted, inject } from 'vue';
import CodemirrorEditor from './CodemirrorEditor/index.vue';
import { useFunctionVariables } from '@funi/fodash/useFunctionVariables';
import useClientConfig from '@/utils/hooks/useClientConfig';
const idCollectObject = inject('idCollectObject', {});

const emit = defineEmits(['confirm']);
const props = defineProps({
  variables: { type: Array, default: () => [] },
  title: { type: String, default: '表达式' }
});

const editor = ref();
const code = ref('');
const visible = ref(false);
const sysParams = ref([]);

onMounted(async () => {
  if (idCollectObject?.app_code) {
    const apps = await $http.post('/as/app/findAppByCode', { appCodes: [idCollectObject.app_code] });
    const lowcodeClientIds = apps.reduce((pre, item) => ({ ...pre, [item.code]: item.ccsclient_id }), {});
    await useClientConfig(lowcodeClientIds[idCollectObject.app_code]);
    sysParams.value = $utils.getClientConfig(lowcodeClientIds[idCollectObject.app_code]);
  }
});

const statusMap = array => {
  return array.map(item => {
    return {
      id: $utils.guid(),
      label: item.label,
      expression: JSON.stringify(Number.isNaN(Number(item.value)) ? item.value : Number(item.value))
    };
  });
};

const busStatus = window.$funi.app.as?.getDicOption
  ? [
      {
        id: 'ASAPP_STATUS',
        label: '业务件状态',
        children: statusMap(window.$funi.app.as.getDicOption('ASAPP_STATUS'))
      },
      {
        id: 'ASAPP_FAILURE',
        label: '失效状态',
        children: statusMap(window.$funi.app.as.getDicOption('ASAPP_FAILURE'))
      },
      {
        id: 'ASAPP_BUS_DOING',
        label: '在办业务',
        children: statusMap(window.$funi.app.as.getDicOption('ASAPP_BUS_DOING'))
      },
      {
        id: 'ASAPP_BUS_TYPE',
        label: '业务类型',
        children: statusMap(window.$funi.app.as.getDicOption('ASAPP_BUS_TYPE'))
      }
    ]
  : [];

const activeNames = ref([]);
const activeNode = ref();
const { functionVariables } = useFunctionVariables();

const defaultVariables = [
  {
    id: 'user',
    label: '用户信息',
    children: [
      { id: $utils.guid(), label: '用户id', expression: '$funi.auth.user.userId' },
      { id: $utils.guid(), label: '用户类型', expression: '$funi.auth.user.userType' },
      { id: $utils.guid(), label: '联系电话', expression: '$funi.auth.user.phoneNumber' },
      { id: $utils.guid(), label: '证件类型', expression: '$funi.auth.user.cardNumber' },
      { id: $utils.guid(), label: '证件号码', expression: '$funi.auth.user.phoneNumber' },
      { id: $utils.guid(), label: '账号', expression: '$funi.auth.user.account' },
      { id: $utils.guid(), label: '名称', expression: '$funi.auth.user.username' },
      { id: $utils.guid(), label: '授权管理主体', expression: '$funi.auth.user.accountUnitVos' },
      {
        id: $utils.guid(),
        label: '所属管理主体',
        expression: '$funi.auth.user.unit',
        children: [
          { id: $utils.guid(), label: 'id', expression: '$funi.auth.user.unit.id' },
          { id: $utils.guid(), label: 'isMainUnit', expression: '$funi.auth.user.unit.isMainUnit' },
          { id: $utils.guid(), label: '编码', expression: '$funi.auth.user.unit.code' },
          { id: $utils.guid(), label: '名称', expression: '$funi.auth.user.unit.name' }
        ]
      },
      { id: $utils.guid(), label: '用户角色', expression: '$funi.auth.user.roleClientVos' },
      {
        id: $utils.guid(),
        label: '归属区域',
        expression: '$funi.auth.user.district',
        children: [
          { id: $utils.guid(), label: '区域id', expression: '$funi.auth.user.district.id' },
          { id: $utils.guid(), label: '区域编码', expression: '$funi.auth.user.district.code' },
          { id: $utils.guid(), label: '区域名称', expression: '$funi.auth.user.district.name' }
        ]
      }
    ]
  },
  {
    id: 'orgs',
    label: '组织架构',
    children: $utils.mapTree($funi.auth.orgs, item =>
      Object.assign({}, item, { expression: `$utils.findTree($funi.auth.orgs, i => i.id === '${item.id}').item` })
    ),
    hidePath: true
  },
  {
    id: 'roles',
    label: '用户角色',
    children: $utils.mapTree($funi.auth.roles, item =>
      Object.assign({}, item, { expression: `$utils.findTree($funi.auth.roles, i => i.id === '${item.id}').item` })
    ),
    hidePath: true
  }
];
const clientConfig = computed(() => {
  return {
    id: 'sysConfig',
    label: '应用参数配置',
    children: sysParams.value.map(item => {
      return { id: $utils.guid(), label: item.label, expression: `$utils.getClientConfigByKey('${item.key}')` };
    }),
    hidePath: true
  };
});
const variablesValue = computed(() => {
  return [...defaultVariables, ...busStatus, ...functionVariables, ...props.variables, clientConfig.value];
});

const show = codes => {
  code.value = codes || '';
  visible.value = true;
};

const handleNodeClick = node => {
  const newNode = $utils.clone(node, true);
  if (!newNode.func) {
    const variable = $utils.findTree(variablesValue.value, i => i.id === node.id);
    if (!!variable) {
      const [rootNode] = variable.nodes || [];
      !!rootNode && !rootNode.hidePath && Object.assign(newNode, { label: variable.nodes.map(i => i.label).join('.') });
    }
  }
  editor.value.updateValue(newNode);
};

const handleConfirm = () => {
  emit('confirm', editor.value.getValue().toString());
  visible.value = false;
};

const handleHover = node => {
  activeNode.value = node.data;
  console.debug('handleHover', node.data);
};
defineExpose({ show });
</script>

<style lang="scss" src="./BindVariableDialog.scss"></style>
