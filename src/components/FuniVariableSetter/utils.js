export const FormulaRegex = /(@var|@func){@id{(.*?)}:@expression{(.*?)}:@label{(.*?)}}/g;

export function encodeFormula(item) {
  const { id, label, expression } = item || {};
  const variable = `@var{@id{${id || ''}}:@expression{${expression || ''}}:@label{${label || ''}}}`;
  const func = `@func{@id{${id || ''}}:@expression{${expression || ''}}:@label{${label || ''}}}()`;
  return item.func ? func : variable;
}

export function decodeFormula(formula) {
  const matches = [];
  while (1) {
    const match = FormulaRegex.exec(formula);
    if (!match) break;
    matches.push(match);
  }
  return matches.reduce(
    (prev, curr) => {
      const [detail, __, id, expression, label] = curr;
      return {
        label: prev.label.replace(detail, ` ${label}`).replace('( ', '(').trim(),
        expression: prev.expression
          .replace(detail, ` ${expression || id}`)
          .replace('( ', '(')
          .trim()
      };
    },
    { label: formula, expression: formula }
  );
}
