export const useFunctionVariables = () => {
  const functionVariables = [
    {
      id: 'fucntions',
      label: '基础函数',
      children: [
        { id: $utils.guid(), label: '输出对象', expression: '$funi.utils.createObjectFromEntries', func: true },
        { id: $utils.guid(), label: '键值对', expression: '$funi.utils.createEntry', func: true }
      ]
    },
    {
      id: $utils.guid(),
      label: '数学函数',
      children: [
        {
          id: $utils.guid(),
          label: 'ABS',
          example: 'ABS(-5)可以返回5，也就是-5的绝对值',
          description: '返回数字的绝对值',
          usage: 'ABS(数字)',
          expression: '$funi.utils.fodash.ABS',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'AVG',
          example: 'AVG(V)，V含有100、90、80，则返回90为平均值',
          description: '返回所有参数的平均值，参数V是明细表（子表）的某一个数字控件',
          usage: 'AVG(V)',
          expression: '$funi.utils.fodash.AVG',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'CEILING',
          example: 'CEILING(number,significance)',
          description:
            '将数字number向上舍入（沿绝对值增大的方向）为最接近基数significance的倍数，例如CEILING(22.5,3)=24',
          usage: null,
          expression: '$funi.utils.fodash.CEILING',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'COS',
          example: 'COS(0)返回1',
          description: '返回-1到1之间的余弦值，参数A为角度',
          usage: 'COS(A)',
          expression: '$funi.utils.fodash.COS',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'COUNT',
          example: 'COUNT(V)，V含有数学、语文、英语，则返回3',
          description: '统计参数列表中选项值的个数，参数V是明细表(子表)的某一个控件',
          usage: null,
          expression: '$funi.utils.fodash.COUNT',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'COUNTIF',
          example: 'COUNTIF(range, criteria)',
          description:
            '统计符合条件的子表参数v的个数，参数range是子表的某个控件，criteria以数字、文本或表达式形式的条件，条件可以表示为68、"68"、">68"或"abc"',
          usage: null,
          expression: '$funi.utils.fodash.COUNTIF',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'FIXED',
          example: 'FIXED(number,[decimals])',
          description:
            '将数字舍入到指定的小数位，并以文本形式返回结果；number为要进行舍入并转换为文本的数字；decimals可选，小数点右边的位数',
          usage: null,
          expression: '$funi.utils.fodash.FIXED',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'FLOOR',
          example: 'FLOOR(number,significance)',
          description: '将数字number向下舍入（沿绝对值减小的方向）为最接近基数significance的倍数，例如FLOOR(22.5,3)=21',
          usage: null,
          expression: '$funi.utils.fodash.FLOOR',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'INT',
          example: 'INT(3.1415)返回3，也就是3.1415的整数部分',
          description: '将数字向下取整为最接近的整数',
          usage: 'INT(数字)',
          expression: '$funi.utils.fodash.INT',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'LARGE',
          example: 'LARGE(v,k)',
          description:
            '返回参数列表中第k个最大值，参数v是子表的某一个数字控件，k为返回的数据在数组v里的位置（从大到小）',
          usage: null,
          expression: '$funi.utils.fodash.LARGE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'LOG',
          example: 'LOG(number,[base])',
          description: '根据底数返回指定数字的对数，如LOG(9, 3)返回的结果是2',
          usage: 'LOG(数字,[底数])',
          expression: '$funi.utils.fodash.LOG',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'MAX',
          example: 'MAX(V)，V含有100、90、80，则返回100为最大值',
          description: '返回参数列表中的最大值，参数V是明细表（子表）的某一个数字字段',
          usage: 'MAX(V)',
          expression: '$funi.utils.fodash.MAX',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'MIN',
          example: 'MIN(V)，V含有100、90、80，则返回80为最小值',
          description: '返回参数列表中的最小值，参数V是明细表（子表）的某一个数字字段',
          usage: 'MIN(V)',
          expression: '$funi.utils.fodash.MIN',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'MOD',
          example: 'MOD(4,3)返回1，也就是4/3的余数',
          description: '返回两数相除的余数，数字1是被除数，数字2是除数',
          usage: 'MOD(数字,数字)',
          expression: '$funi.utils.fodash.MOD',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'PI',
          example: 'PI() 返回3.141596......',
          description: '圆周率3.1415...',
          usage: 'PI()',
          expression: '$funi.utils.fodash.PI',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'POWER',
          example: 'POWER(number,power)',
          description: '返回指定数字的乘幂，如POWER(3，2)返回9，即3的2次方',
          usage: 'POWER(数字,指数)',
          expression: '$funi.utils.fodash.POWER',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'RAND',
          example: 'RAND()',
          description: '返回大于等于0且小于1的均匀分布随机实数，每一次触发计算都会变化',
          usage: null,
          expression: '$funi.utils.fodash.RAND',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'ROUND',
          example: 'ROUND(3.1485,2)返回3.15',
          description: '将数字四舍五入到指定的位数',
          usage: 'ROUND(数字,小数位数)',
          expression: '$funi.utils.fodash.ROUND',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'SIN',
          example: 'SIN(30) 返回sin值0.5',
          description: '返回-1到1之间的正弦值，参数A为角度',
          usage: 'SIN(A)',
          expression: '$funi.utils.fodash.SIN',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'SMALL',
          example: 'SMALL(v,k)',
          description:
            '返回参数列表中第k个最小值，参数v是子表的某一个数字控件，k为返回的数据在数组v里的位置（从小到大）',
          usage: null,
          expression: '$funi.utils.fodash.SMALL',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'SQRT',
          example: 'SQRT(9)返回3，也就是9的正平方根',
          description: '获取一个数字的正平方根，数字为非负数',
          usage: 'SQRT(数字)',
          expression: '$funi.utils.fodash.SQRT',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'SUM',
          example: 'SUM(V),V含有10、20、30，则返回60',
          description: '统计输入参数的数值之和，参数V是明细表（子表）的某一个数字字段',
          usage: 'SUM(V)',
          expression: '$funi.utils.fodash.SUM',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'SUMIF',
          example: 'SUMIF(range,criteria,sum_range)',
          description:
            '对符合条件的子表参数求和，参数range是用于判断条件的控件，criteria以数字、文本或表达式形式的条件，参数sum_range是需要求和的子表某个数字型控件，条件可以表示为68、"68"、">68"或"abc"',
          usage: null,
          expression: '$funi.utils.fodash.SUMIF',
          func: true
        }
      ]
    },
    {
      id: $utils.guid(),
      label: '文本函数',
      children: [
        {
          id: $utils.guid(),
          label: 'CONTAINS',
          example: 'CONTAINS("ABC","B")返回true',
          description: '判断参数1是否包含参数2的值，包含则返回true，不包含则返回false',
          usage: 'CONTAINS(参数1,参数2)',
          expression: '$funi.utils.fodash.CONTAINS',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'LEFT',
          example: 'LEFT("数飞应用搭建工具",2)返回"数飞"，也就是"数飞应用搭建工具"的从左往右的前2个字符',
          description: '从一个文本的第一个字符开始返回指定个数的字符',
          usage: 'LEFT(文本,文本长度)',
          expression: '$funi.utils.fodash.LEFT',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'LEN',
          example: 'LEN("数飞应用搭建工具")返回8，因为文本中有8个字符',
          description: '获取文本中的字符个数',
          usage: 'LEN(文本)',
          expression: '$funi.utils.fodash.LEN',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'LOWER',
          example: 'LOWER("ABC")返回"abc"',
          description: '将一个文本中的所有大写字母转换为小写字母',
          usage: 'LOWER(文本)',
          expression: '$funi.utils.fodash.LOWER',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'MID',
          example: 'MID("数飞应用搭建工具",5,4)返回"搭建工具"',
          description: 'MID返回文本中从指定位置开始的指定数目的字符',
          usage: 'MID(文本,开始位置_数字,指定数目)',
          expression: '$funi.utils.fodash.MID',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'REPLACE',
          example: 'REPLACE("数飞应用搭建工具",3,6,"企业数据管理平台")返回"数飞企业数据管理平台"',
          description: '根据指定的字符数，将部分文本替换为不同的文本',
          usage: 'REPLACE(文本,开始位置,替换长度,新文本)',
          expression: '$funi.utils.fodash.REPLACE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'RIGHT',
          example: 'RIGHT("数飞应用搭建工具",4)返回"搭建工具"，也就是"数飞应用搭建工具"从右往左的前4个字符',
          description: '从文本字符串的最后一个字符开始返回指定个数的字符，若不填指定个数则取默认值1',
          usage: 'RIGHT(文本,指定个数)',
          expression: '$funi.utils.fodash.RIGHT',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'SEARCH',
          example: 'SEARCH("工具","数飞应用搭建工具",1)返回7',
          description: '获取文本1在文本2中的开始位置，其中开始位置编码为在文本2中第几个位置开始查找',
          usage: 'SEARCH(文本1,文本2,开始位置编码)',
          expression: '$funi.utils.fodash.SEARCH',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'STARTSWITH',
          example: 'STARTSWITH("数飞应用搭建工具","数飞")返回true',
          description: '判断文本字符串是否以特定字符串开始，是则返回true，否则返回false',
          usage: 'STARTSWITH(文本,特定字符串)',
          expression: '$funi.utils.fodash.STARTSWITH',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'SUBSTITUTE',
          example: 'SUBSTITUTE("数飞低代码搭建工具","低代码","应用",1)，返回"数飞应用搭建工具"',
          description: '将文本字符串中的部分字符替换成新字符串',
          usage: 'SUBSTITUTE(原文本,要替换的文本,新的文本,替换次数)',
          expression: '$funi.utils.fodash.SUBSTITUTE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'TEXT',
          example: 'TEXT(123)',
          description: '将数字转换成文本',
          usage: 'TEXT(123)返回"123"',
          expression: '$funi.utils.fodash.TEXT',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'TRIM',
          example: 'TRIM(" 数飞 ")返回"数飞"',
          description: '删掉文本首尾的空格',
          usage: 'TRIM(文本)',
          expression: '$funi.utils.fodash.TRIM',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'UNION',
          example: 'UNION(参数1,[参数2...])',
          description:
            '合并多个文本数组，返回结果去重，参数可以是多个主表控件，如UNION(文本1,文本2,文本3...)，也可以是数组如UNION(子表控件)',
          usage: null,
          expression: '$funi.utils.fodash.UNION',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'UPPER',
          example: 'UPPER("abc")返回"ABC"',
          description: '将一个文本中的所有小写字母转换成大写字母',
          usage: 'UPPER(文本)',
          expression: '$funi.utils.fodash.UPPER',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'VALUE',
          example: 'VALUE("123")',
          description: '将文本转换为数字',
          usage: null,
          expression: '$funi.utils.fodash.VALUE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'MONEY_CAPITAL',
          example: 'MONEY_CAPITAL(123)',
          description: '将金额转大写，例如：MONEY_CAPITAL(123333) 输出：壹拾贰万叁仟叁佰叁拾叁元整',
          usage: null,
          expression: '$funi.utils.moneyCapital',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'AMOUNT_INTL',
          example: 'AMOUNT_INTL(123333)',
          description:
            '将金额按千分位格式化，例如：AMOUNT_INTL(123333) 输出："¥123,333.00"，无符号：AMOUNT_INTL(123333, false) 输出："123,333.00"',
          usage: null,
          expression: '$funi.utils.amountIntl',
          func: true
        }
      ]
    },
    {
      id: $utils.guid(),
      label: '时间函数',
      children: [
        {
          id: $utils.guid(),
          label: 'ADDDAY',
          example: 'ADDDAY(2020-01-15,5)返回2020-01-20',
          description: '将指定日期加/减指定天数，当指定天数为负数时在日期上减去此天数',
          usage: 'ADDDAY(指定日期,指定天数)',
          expression: '$funi.utils.fodash.ADDDAY',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'ADDMONTH',
          example: 'ADDMONTH(2020-01-15,5)返回2020-06-15',
          description: '将指定日期加/减指定月数，当指定月数为负数时在此日期上减去此月数',
          usage: 'ADDMONTH(指定日期,指定月数)',
          expression: '$funi.utils.fodash.ADDMONTH',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'ADDYEAR',
          example: 'ADDYEAR(2020-01-15,1)返回2021-01-15',
          description: '将指定日期加/减指定年数，当指定年数为负数时在此日期上减去此年数',
          usage: 'ADDYEAR(指定日期,指定年数)',
          expression: '$funi.utils.fodash.ADDYEAR',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'DATE',
          example: '略',
          description: '将年月日时分秒转换为日期',
          usage: 'DATE(年-月-日[时:分:秒])',
          expression: '$funi.utils.fodash.DATE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'DAY',
          example: '略',
          description: '返回日期的天数，值为介于1到31之间的整数',
          usage: 'DAY(日期)',
          expression: '$funi.utils.fodash.DAY',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'DAYS',
          example: 'DAYS("2023-08-23","2023-08-01")，返回天数22',
          description:
            '第一个为结束日期，第二个为开始日期，返回两个日期之间的天数差值。如录入【生产时间】、【到期时间】，通过DAYS函数自动填入【有效天数】。',
          usage: 'DAYS(结束日期,开始日期)',
          expression: '$funi.utils.fodash.DAYS',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'HOUR',
          example: '略',
          description: '返回时间的小时部分',
          usage: 'HOUR(时间)',
          expression: '$funi.utils.fodash.HOUR',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'HOURS',
          example: '略',
          description: '返回两个时间之间的小时数，精确到两位小数',
          usage: 'HOURS(结束时间,开始时间)',
          expression: '$funi.utils.fodash.HOURS',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'MINUTE',
          example: '略',
          description: '返回时间的分钟部分',
          usage: 'MINUTE(时间)',
          expression: '$funi.utils.fodash.MINUTE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'MINUTES',
          example: '略',
          description: '返回两个时间之间的分钟数，精确到两位小数',
          usage: 'MINUTES(结束时间,开始时间)',
          expression: '$funi.utils.fodash.MINUTES',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'MONTH',
          example: '略',
          description: '返回日期月份，值为介于1到12之间的整数',
          usage: 'MONTH(日期）',
          expression: '$funi.utils.fodash.MONTH',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'NOW',
          example: 'NOW()，直接返回当前时间：2023-08-22 14:02:23',
          description: '返回当前时间，精确到时分秒，格式为yyyy-MM-dd hh:mm:ss',
          usage: 'NOW()',
          expression: '$funi.utils.fodash.NOW',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'QUARTER',
          example: '略',
          description: '返回日期的所属季度，值为介于1到4的整数',
          usage: 'QUARTER(日期)',
          expression: '$funi.utils.fodash.QUARTER',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'TODAY',
          example: 'TODAY()，直接返回当天日期：2023-08-22',
          description: '返回今天的日期，格式为：yyyy-MM-dd',
          usage: 'TODAY()',
          expression: '$funi.utils.fodash.TODAY',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'WEEKDAY',
          example: '略',
          description: '返回指定日期为星期几',
          usage: 'WEEKDAY(日期)',
          expression: '$funi.utils.fodash.WEEKDAY',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'WEEKNUM',
          example: '略',
          description: '返回一个数字，该数字代表指定日期是一年中的第几周',
          usage: 'WEEKNUM(日期)',
          expression: '$funi.utils.fodash.WEEKNUM',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'YEAR',
          example: '略',
          description: '返回日期的年份',
          usage: 'YEAR(日期)',
          expression: '$funi.utils.fodash.YEAR',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'YEARS',
          example: ' YEARS("2023-01-31","2020-01-31")返回3',
          description: '返回两个日期之间的年数差值，精确到两位小数',
          usage: 'YEARS(结束日期,开始日期)',
          expression: '$funi.utils.fodash.YEARS',
          func: true
        }
      ]
    },
    {
      id: $utils.guid(),
      label: '逻辑函数',
      children: [
        {
          id: $utils.guid(),
          label: 'CASE',
          example: 'CASE(条件表达式1,条件表达式1为true返回该值,条件表达式2,条件表达式2为true返回该值,...)',
          description: '判断是否满足一个或多个条件，且返回符合第一个TRUE条件的值，CASE可以取代多个IF语句嵌套。',
          usage: null,
          expression: '$funi.utils.fodash.CASE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'IF',
          example: 'IF(语文成绩>60,"及格","不及格")，当语文成绩>60是返回及格，否则返回不及格',
          description: '判断一个条件能否满足；如果满足返回一个值，如果不满足则返回另外一个值',
          usage: 'IF(按条件进行逻辑比较,满足返回一个值,不满足返回另一个值)',
          expression: '$funi.utils.fodash.IF',
          func: true
        }
      ]
    },
    {
      id: $utils.guid(),
      label: '其他函数',
      children: [
        {
          id: $utils.guid(),
          label: 'GETADDRESS',
          example: '略',
          description: '将地址或位置字段的值转换为文本',
          usage: 'GETADDRESS(地址字段)',
          expression: '$funi.utils.fodash.GETADDRESS',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'ISNULL',
          example: '略',
          description: '判断是否为空，为空则返回true，不为空则返回false，可用于判断具体值或者某个字段',
          usage: 'ISNULL(文本或字段)',
          expression: '$funi.utils.fodash.ISNULL',
          func: true
        }
      ]
    },
    {
      id: $utils.guid(),
      label: '组织机构函数',
      children: [
        {
          id: $utils.guid(),
          label: 'CONTAINSROLE',
          example: 'CONTAINS("ABC","B")返回true',
          description: '判断是否包含指定角色',
          usage: 'GETADDRESS(地址字段)',
          expression: '$funi.utils.fodash.CONTAINSROLE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETUNITNAME',
          example: 'GETUNITNAME(人员部门控件)',
          description: '文本格式返回指定人员或部门（包括单选和多选）控件对应的人员或部门名称',
          usage: null,
          expression: '$funi.utils.fodash.GETUNITNAME',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'SEARCHROLE',
          example: 'SEARCHROLE(用户id)',
          description: '传入一个用户id 返回当前用户绑定的角色列表',
          usage: null,
          expression: '$funi.utils.fodash.SEARCHROLE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETORGID',
          example: 'GETORGID(管理主体)',
          description: '获取管理主体ID',
          usage: null,
          expression: '$funi.utils.fodash.GETORGID',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETORGNAME',
          example: 'GETORGNAME(管理主体)',
          description: '获取管理主体NAME',
          usage: null,
          expression: '$funi.utils.fodash.GETORGNAME',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETORGCODE',
          example: 'GETORGCODE(管理主体)',
          description: '获取管理主体CODE',
          usage: null,
          expression: '$funi.utils.fodash.GETORGCODE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETROLEID',
          example: 'GETROLEID(角色)',
          description: '获取角色ID',
          usage: null,
          expression: '$funi.utils.fodash.GETROLEID',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETROLENAME',
          example: 'GETROLENAME(角色)',
          description: '获取角色NAME',
          usage: null,
          expression: '$funi.utils.fodash.GETROLENAME',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETROLECODE',
          example: 'GETROLECODE(角色)',
          description: '获取角色CODE',
          usage: null,
          expression: '$funi.utils.fodash.GETROLECODE',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETUSERID',
          example: 'GETUSERID(用户)',
          description: '获取用户ID',
          usage: null,
          expression: '$funi.utils.fodash.GETUSERID',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETUSERNAME',
          example: 'GETUSERNAME(用户)',
          description: '获取用户NAME',
          usage: null,
          expression: '$funi.utils.fodash.GETUSERNAME',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETURCORRELATIONORG',
          example: 'GETURCORRELATIONORG(入参1：用户id,入参2：角色id):管理主体[]',
          description: '根据用户 和 角色 获取关联的管理主体',
          usage: null,
          expression: '$funi.utils.fodash.GETURCORRELATIONORG',
          func: true
        },
        {
          id: $utils.guid(),
          label: 'GETORGIDS',
          example: 'GETORGIDS(入参1：管理主体[]):管理主体id[]',
          description: '根据管理主体集合返回管理主体id集合',
          usage: null,
          expression: '$funi.utils.fodash.GETORGIDS',
          func: true
        }
      ]
    }
  ];
  return { functionVariables };
};
