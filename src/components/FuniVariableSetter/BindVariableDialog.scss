.funi-variable-setter-dialog {
  .funi-express-editor {
    display: flex;
    position: relative;
    height: calc(100vh - 240px);
    border: 0.5px solid rgb(227, 230, 235);

    &__main {
      flex-grow: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: scroll;

      .v-codemirror {
        flex: 1;
        .cm-editor {
          height: 100%;
        }
        .cm-gutters {
          display: none;
        }
        .cm-line {
          padding: 2px 2px 2px 6px;
        }
      }

      .funi-express-editor__desc {
        flex-shrink: 0;
        height: 120px;
        border-top: 1px solid rgb(227, 230, 235);

        padding: 6px;

        p {
          line-height: 1.6em;
          margin: 6px 0;
        }
      }
    }

    &__side {
      flex-shrink: 0;
      min-width: 300px;
      box-sizing: border-box;
      border-left: 0.5px solid rgb(227, 230, 235);
      padding: 0 8px;
      border-radius: 0 0 3px;
      max-height: 100%;
      overflow: overlay;
      .el-collapse {
        border: none;
        &-item {
          &__header {
            height: 36px;
            border: none;
          }

          &__wrap {
            border: none;
          }

          &__content {
            padding: 0px;

            .el-tree {
              .weak-text {
                color: #97a3b7 !important;
              }

              .type-desc {
                margin-left: 14px;
                display: inline-block;
                vertical-align: middle;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 100%;
              }
            }
          }
        }
      }
    }
  }
}
