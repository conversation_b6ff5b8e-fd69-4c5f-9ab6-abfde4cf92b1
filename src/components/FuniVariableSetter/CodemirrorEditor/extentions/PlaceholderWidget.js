import { WidgetType } from '@codemirror/view';

export class PlaceholderWidget extends WidgetType {
  constructor(match) {
    super();
    const [input, type, id, expression, label] = match;
    this.input = input;
    this.type = type;
    this.id = id;
    this.expression = expression;
    this.label = label;
  }
  eq(other) {
    return this.id == other.id;
  }
  toDOM() {
    let el = document.createElement('span');
    el.textContent = this.label;
    if (this.type === '@var') {
      el.className = 'funi-cm-placeholder__param';
    } else if (this.type === '@func') {
      el.className = 'funi-cm-placeholder__func';
    }
    return el;
  }
  ignoreEvent() {
    return false;
  }
}
