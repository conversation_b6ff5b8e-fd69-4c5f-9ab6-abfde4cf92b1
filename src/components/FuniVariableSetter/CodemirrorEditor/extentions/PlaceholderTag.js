import { Decoration, EditorView, MatchDecorator, ViewPlugin } from '@codemirror/view';
import { PlaceholderWidget } from './PlaceholderWidget';

export const PlaceholderTag = ViewPlugin.fromClass(
  class {
    placeholders;
    placeholderMatcher;
    constructor(view) {
      this.placeholderMatcher = new MatchDecorator({
        regexp: /(@var|@func){@id{(.*?)}:@expression{(.*?)}:@label{(.*?)}}/g,
        decoration: match => Decoration.replace({ widget: new PlaceholderWidget(match) })
      });
      this.placeholders = this.placeholderMatcher.createDeco(view);
    }
    update(update) {
      this.placeholders = this.placeholderMatcher.updateDeco(update, this.placeholders);
    }
  },
  {
    decorations: instance => instance.placeholders,
    provide: plugin => EditorView.atomicRanges.of(view => view.plugin(plugin)?.placeholders || Decoration.none)
  }
);
