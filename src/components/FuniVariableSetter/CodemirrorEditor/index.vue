<template>
  <codemirror
    v-model="code"
    :placeholder="placeholder"
    :style="style"
    :disabled="disabled"
    :autofocus="true"
    :indent-with-tab="true"
    :tab-size="2"
    :extensions="extensions"
    @ready="handleReady"
    @change="handleChange"
    @blur="emit('blur', $event)"
    @focus="emit('focus', $event)"
    @update="emit('update', $event)"
  />
</template>
<script setup>
import { ref, shallowRef, watch } from 'vue';
import { Codemirror } from 'vue-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { keymap } from '@codemirror/view';
import { insertBlankLine } from '@codemirror/commands';
import { PlaceholderTag } from './extentions/PlaceholderTag';
import { encodeFormula } from '../utils';

defineOptions({
  name: 'CodemirrorEditor',
  inheritAttrs: false
});

const props = defineProps({
  modelValue: String,
  disabled: Boolean,
  placeholder: { type: String, default: '' },
  style: {
    type: Object,
    default: () => ({ height: 'auto', minHeight: '400px' })
  }
});

const emit = defineEmits(['update:modelValue', 'ready', 'change', 'update', 'focus', 'blur']);

const code = ref('');

const extensions = [
  javascript(),
  PlaceholderTag,
  keymap.of([{ key: 'Enter', run: insertBlankLine, shift: insertBlankLine }])
];

watch(
  () => props.modelValue,
  newCode => (code.value = newCode),
  { immediate: true }
);

const codemirrorView = shallowRef();
const handleReady = payload => {
  codemirrorView.value = payload.view;
  emit('ready', payload);
};

const handleChange = e => {
  emit('update:modelValue', e);
  emit('change', e);
};

const updateValue = item => {
  codemirrorView.value.dispatch(codemirrorView.value.state.replaceSelection(encodeFormula(item)));
  codemirrorView.value.focus();
  if (item.func) {
    const anchor = codemirrorView.value.state.selection.main.anchor - 1;
    anchor > 0 && codemirrorView.value.dispatch({ selection: { anchor } });
  }
};

const getValue = () => codemirrorView.value.state.doc;

defineExpose({
  updateValue,
  getValue
});
</script>

<style lang="scss" src="./index.scss"></style>
