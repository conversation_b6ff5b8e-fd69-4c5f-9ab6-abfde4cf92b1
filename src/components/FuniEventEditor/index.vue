<template>
  <el-drawer
    v-model="visible"
    size="90%"
    direction="btt"
    destroy-on-close
    class="event-editor-drawer"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <template #header="{ close, titleId, titleClass }">
      <h3 :id="titleId" :class="['event-editor-drawer__title', titleClass]">
        添加事件: {{ event.name }} <span style="color: rgb(151, 163, 183)">({{ event.code }})</span>
      </h3>
      <el-button type="default" @click="close"> 取消 </el-button>
      <el-button type="primary" @click="save"> 保存 </el-button>
    </template>
    <ActionDetailPanel v-if="!!rootAction" ref="actionDetailPanel" :root-action="rootAction" @reset="resetRootAction" />
    <ActionGridPanel v-else @select="e => (rootAction = e)" />
  </el-drawer>
</template>

<script setup>
import { ref, reactive, nextTick, provide, readonly, computed, inject, onMounted } from 'vue';
import ActionGridPanel from './ActionGridPanel.vue';
import ActionDetailPanel from './ActionDetailPanel.vue';
import { EVENT_EDITOR_INJECTION } from './symbols';

const props = defineProps({
  variables: { type: Array, default: () => [] },
  eventVariables: { type: Array, default: () => [] },
  pageList: { type: Array, default: () => [] },
  modalList: { type: Array, default: () => [] },
  formList: { type: Array, default: () => [] },
  componentList: { type: Array, default: () => [] },
  /**
   * 赋值选项配置
   * @property {Object} assignOptions - 赋值选项对象
   * @property {string} assignOptions.type - 赋值类型,可选值:'row'(行) | 'form'(表单)
   * @property {Array} assignOptions.gl - 关联字段列表
   * @property {Array} assignOptions.dq - 当前字段列表
   */
  assignOptions: {
    type: Object,
    default: () => ({
      type: 'row', //'form',
      gl: [],
      dq: []
    })
  }
});

const emit = defineEmits(['save']);

const visible = ref(false);
/**
 * 有值时直接显示配置页面，无值时先选择一个动作再进入配置页面
 */
const rootAction = ref(null);
const actionDetailPanel = ref();
const event = reactive({ name: '', code: '' });
const idCollectObject = inject('idCollectObject', {});

/**
 * 行为配置页面所需参数
 */
const computedModals = computed(() => readonly(props.modalList));
const computedVariables = computed(() => readonly(props.variables));
const computedForms = computed(() => readonly(props.formList));
const computedEventVariables = computed(() => readonly(props.eventVariables));
const computedComponents = computed(() => readonly(props.componentList));
const computedAssignOptions = computed(() => readonly(props.assignOptions));
provide(EVENT_EDITOR_INJECTION, {
  modals: computedModals,
  variables: computedVariables,
  forms: computedForms,
  eventVariables: computedEventVariables,
  components: computedComponents,
  app_id: idCollectObject.app_id,
  assignOptions: computedAssignOptions
});

const show = ({ name, code, logic } = {}) => {
  event.name = name;
  event.code = code;
  rootAction.value = logic?.actionItem;
  visible.value = true;
};

const hide = () => {
  rootAction.value = null;
  visible.value = false;
};

/**
 * 保存编辑器设置
 * 1.保存左侧动作树
 * 2.保存右侧当前面板设置
 */
const save = async () => {
  if (!rootAction.value) {
    emit('save', null);
    return;
  }
  const syncData = await actionDetailPanel.value.sync();
  console.debug('syncData', syncData);
  nextTick(() => emit('save', syncData));
};
const resetRootAction = () => {
  rootAction.value = null;
};
defineExpose({ show, hide });
</script>

<style lang="scss">
.event-editor-drawer {
  .el-drawer__header {
    padding: 0px 20px !important;
    border-bottom: 0.5px solid rgb(227, 230, 235);
    box-sizing: border-box;
    margin-bottom: 0;
  }

  &__title {
    font-size: 14px !important;
    line-height: 48px !important;
    color: #000000d9;
  }
}
</style>
