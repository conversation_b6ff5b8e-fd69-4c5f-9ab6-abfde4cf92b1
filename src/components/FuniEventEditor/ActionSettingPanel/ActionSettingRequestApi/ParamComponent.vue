<template>
  <el-divider>{{ title }}</el-divider>
  <el-form-item v-for="item in _params" :key="item.prop" :label="item.name">
    <funi-variable-setter class="!w-[calc(50%+22px)]" v-model="values[item.prop]" :variables="variables" />
    <el-link icon="Delete" :underline="false" @click="handleRemoveParam(item)"></el-link>
  </el-form-item>
  <el-dropdown v-if="options.length" trigger="click" placement="bottom" @command="onNewParam">
    <el-link type="primary"><funi-icon icon="ep:circle-plus" />&nbsp;添加{{ title }}</el-link>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item v-for="option in options" :key="option.key" :command="option">
          {{ option.key }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { computed, ref, watch } from 'vue';

const props = defineProps({
  type: { type: String, default: '' },
  params: { type: Array, default: () => [] },
  title: { type: String, default: '' },
  paramOptions: { type: Array, default: () => [] },
  variables: { type: Array, default: () => [] }
});

const _params = ref([]);
const values = ref({});
const options = computed(() => {
  const paramKeys = _params.value.map(item => item.prop);
  return props.paramOptions.filter(item => !paramKeys.includes(item.key));
});

watch(
  () => props.params,
  () => {
    _params.value = props.params;
    values.value = props.params.reduce((acc, item) => Object.assign(acc, { [item.prop]: item.rightVal }), {});
  },
  { immediate: true }
);

const onNewParam = option => {
  _params.value.push({ prop: option.key, name: option.key });
  values.value[option.key] = option.value;
};

const handleRemoveParam = item => {
  _params.value = _params.value.filter(i => i.prop !== item.prop);
  delete values.value[item.prop];
};

const getValues = () => {
  return Promise.resolve(
    _params.value.map(param => {
      return { prop: param.prop, rightVal: values.value[param.prop], name: param.name, type: props.type };
    })
  );
};

defineExpose({ getValues });
</script>
