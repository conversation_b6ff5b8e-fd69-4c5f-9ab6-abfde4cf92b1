<template>
  <el-form label-width="120px">
    <el-form-item label="API">
      <funi-autocomplete
        class="!w-[calc(50%+22px)]"
        labelKey="name"
        valueKey="url"
        primaryKey="id"
        placeholder="通过API名称搜索"
        url="/as/apis/list"
        :model-value="apiInfo.url"
        @select="handleSelectApi"
      />
    </el-form-item>

    <el-form-item label="URL">
      <el-input :model-value="apiInfo.url" disabled />
    </el-form-item>

    <el-form-item label="服务类型">
      <el-input :model-value="ApiType[apiInfo.type]" disabled />
    </el-form-item>

    <el-form-item label="调用方法">
      <el-input :model-value="apiInfo.method" disabled />
    </el-form-item>

    <ParamComponent
      v-for="config in inputConfigs"
      ref="paramComponentRefs"
      :key="`${config.key}-${apiInfo.id}`"
      :type="config.key"
      :title="config.title"
      :paramOptions="config.paramOptions"
      :variables="variables"
      :params="config.params"
    />
  </el-form>
</template>

<script setup>
import { computed, inject, toRaw, ref, watch } from 'vue';
import { EVENT_EDITOR_INJECTION } from '../../symbols';
import { ExeType } from '../../Logic/types';
import ParamComponent from './ParamComponent.vue';

defineOptions({ name: 'ActionSettingRequestApi' });

const ApiType = { daas: 'DAAS', isadmin: '业务协同', other: '其他' };
const ApiParamType = { query: 'Query参数', body: 'Body参数', headers: 'Header参数' };

const props = defineProps({ action: Object });

const { variables } = inject(EVENT_EDITOR_INJECTION);

const apiInfo = ref({});
const paramComponentRefs = ref([]);

const inputConfigs = computed(() => {
  const config = JSON.parse(apiInfo.value.input_config || '{}');
  return Object.entries(config)
    .filter(([key, value]) => value?.dataList?.length)
    .map(([key, value]) => ({
      key,
      title: ApiParamType[key],
      paramOptions: value.dataList,
      params: apiInfo.value[key]
    }));
});

watch(
  () => props.action,
  () => {
    if (!!props.action.request) {
      apiInfo.value = props.action.request;
    }
  },
  { immediate: true }
);

const getValues = async () => {
  const values = await Promise.all(paramComponentRefs.value.map(item => item.getValues())).then(res => res.flat());
  return Promise.resolve({
    ...apiInfo.value,
    headers: values.filter(item => item.type === 'headers'),
    query: values.filter(item => item.type === 'query'),
    body: values.filter(item => item.type === 'body')
  });
};

const handleSelectApi = item => {
  if (apiInfo.value.id !== item.id) {
    apiInfo.value = item;
  }
};

defineExpose({ getValues });
</script>
<style lang="scss" scoped>
:deep(.el-divider) {
  .el-divider__text {
    color: #000000d9;
    font-weight: 400;
    font-size: 12px;
  }
}

:deep(.el-input.is-disabled) {
  width: calc(50% + 22px);

  .el-input__inner {
    -webkit-text-fill-color: unset !important;
  }
}
</style>
