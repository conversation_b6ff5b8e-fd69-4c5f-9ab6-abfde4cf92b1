<template>
  <el-empty :image="emptyImage" description="无配置项" />
</template>

<script setup>
import emptyImage from '../assets/image-empty.png';
import { ACTION } from '../enums.js';

defineOptions({ name: 'ActionSettingEmpty' });

const props = defineProps({
  action: { type: Object, default: () => ({}) }
});

const getValues = () => {
  switch (props.action.code) {
    case ACTION.NAVIGATE_BACK.code:
      break;
    case ACTION.RELAUNCH_HOME.code:
      break;
    case ACTION.SHOW_TOAST.code:
      break;
    case ACTION.SHOW_LOADING.code:
      break;
    case ACTION.HIDE_LOADING.code:
      break;
    case ACTION.SHOW_MODAL.code:
      break;
    default:
      break;
  }
  return Promise.resolve({});
};

defineExpose({ getValues });
</script>
