<template>
  <el-form label-width="120px">
    <el-form-item label="标题">
      <funi-variable-setter class="!w-[calc(50%+22px)]" v-model="form.title" :variables="variables" />
    </el-form-item>
    <el-form-item label="内容">
      <funi-variable-setter class="!w-[calc(50%+22px)]" v-model="form.message" :variables="variables" />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { inject, toRaw, reactive, watch, ref } from 'vue';
import { EVENT_EDITOR_INJECTION } from '../symbols';
import { ExeType } from '../Logic/types';

const props = defineProps({ action: Object });

const { variables } = inject(EVENT_EDITOR_INJECTION);

const form = ref({});

watch(
  () => props.action,
  () => {
    if (!!props.action?.paramExpList) {
      form.value = props.action?.paramExpList?.reduce((obj, item) => {
        obj[item.prop] = item.rightVal;
        return obj;
      }, {});
    }
  },
  { immediate: true }
);

const getValues = () => {
  return Promise.resolve({
    exeExpList: [{ needReturn: false, type: ExeType.Confirm }],
    paramExpList: Object.entries(toRaw(form.value)).map(([key, value]) => ({ prop: key, rightVal: value }))
  });
};

defineExpose({ getValues });
</script>
<style lang="scss" scoped>
:deep(.el-divider) {
  .el-divider__text {
    color: #000000d9;
    font-weight: 400;
    font-size: 12px;
  }
}
</style>
