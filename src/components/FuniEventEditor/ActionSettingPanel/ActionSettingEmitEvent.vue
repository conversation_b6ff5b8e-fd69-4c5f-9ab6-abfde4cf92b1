<template>
  <el-form :model="form" label-width="120px">
    <el-form-item label="触发事件">
      <el-input class="w-1/2" v-model="form.eventName" placeholder="请输入事件名称" />
    </el-form-item>
    <el-divider v-if="params.length">传入事件参数</el-divider>
    <el-form-item v-for="param in params" :key="param.key" :label="param.name">
      <funi-variable-setter class="!w-[calc(50%+22px)]" v-model="paramValues[param.name]" :variables="eventVariables" />
    </el-form-item>
  </el-form>

  <el-popover ref="popoverRef" :width="300" trigger="click">
    <template #reference>
      <el-link type="primary"><funi-icon icon="ep:circle-plus" />&nbsp;触发事件参数</el-link>
    </template>
    <div class="popover-content">
      <el-form label-width="auto" label-position="top" size="small">
        <el-form-item label="参数名称">
          <el-input v-model="paramNameTemp" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="newParamDisabled" @click="onNewParam">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-popover>
</template>

<script setup>
import { computed, inject, toRaw, reactive, ref, watch } from 'vue';
import { EVENT_EDITOR_INJECTION } from '../symbols';
import { ExeType } from '../Logic/types';

defineOptions({ name: 'ActionSettingEmitEvent' });

const props = defineProps({ action: Object });

const { eventVariables } = inject(EVENT_EDITOR_INJECTION);

const popoverRef = ref();
const paramNameTemp = ref();
const params = ref([]);
const form = reactive({ eventName: '' });
const paramValues = reactive({});

const newParamDisabled = computed(() => {
  return !!params.value.find(item => item.name === paramNameTemp.value);
});

watch(
  () => props.action,
  () => {
    if (!!props.action) {
      const [exeExp] = props.action?.exeExpList ?? [];
      params.value = props.action?.paramExpList?.map(item => ({ name: item.prop, key: item.rightVal })) ?? [];
      params.value.forEach(item => (paramValues[item.name] = item.key));
      form.eventName = exeExp?.eventName ?? '';
    }
  },
  { immediate: true }
);

const onNewParam = () => {
  params.value.push({
    key: $utils.guid(),
    name: paramNameTemp.value
  });
  popoverRef.value.hide();
};

const getValues = () => {
  return Promise.resolve({
    exeExpList: [{ needReturn: false, type: ExeType.Event, methodName: 'emit', eventName: form.eventName }],
    paramExpList: Object.entries(toRaw(paramValues)).map(([key, value]) => ({ prop: key, rightVal: value }))
  });
};

defineExpose({ getValues });
</script>
<style lang="scss" scoped>
:deep(.el-divider) {
  .el-divider__text {
    color: #000000d9;
    font-weight: 400;
    font-size: 12px;
  }
}
</style>
