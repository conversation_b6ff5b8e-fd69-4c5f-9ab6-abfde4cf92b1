<template>
  <el-form :model="form" label-width="120px">
    <el-form-item label="选择弹窗">
      <funi-select class="w-1/2" v-model="form.dialogName" placeholder="请选择弹窗" :action="modalAction" />
    </el-form-item>
    <el-divider v-if="params.length">传入页面参数</el-divider>
    <el-form-item v-for="param in params" :key="param.prop" :label="param.name">
      <funi-variable-setter class="!w-[calc(50%+22px)]" v-model="paramValues[param.prop]" :variables="variables" />
      <el-link icon="Delete" :underline="false" @click="handleRemoveParam(param)"></el-link>
    </el-form-item>
  </el-form>
  <el-popover ref="popoverRef" :width="300" trigger="click">
    <template #reference>
      <el-link type="primary"><funi-icon icon="ep:circle-plus" />&nbsp;添加参数</el-link>
    </template>
    <div class="popover-content">
      <el-form label-width="auto" label-position="top" size="small">
        <el-form-item label="参数名称">
          <el-input v-model="paramNameTemp" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="newParamDisabled" @click="onNewParam">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-popover>
</template>

<script setup>
import { computed, inject, toRaw, reactive, ref, watch } from 'vue';
import { EVENT_EDITOR_INJECTION } from '../symbols';
import { ExeType } from '../Logic/types';

defineOptions({ name: 'ActionSettingShowModal' });

const props = defineProps({
  action: { type: Object, default: () => ({}) }
});

const { modals, variables } = inject(EVENT_EDITOR_INJECTION);
const modalAction = () => Promise.resolve(modals.value.map(i => ({ label: i.key, value: i.value })));

const popoverRef = ref();
const paramNameTemp = ref();
const params = ref([]);
const form = reactive({});
const paramValues = ref({});

const newParamDisabled = computed(() => {
  return params.value.some(item => item.name === paramNameTemp.value);
});

watch(
  () => props.action,
  () => {
    if (!!props.action) {
      const [exeExp] = props.action?.exeExpList ?? [];
      const paramExpList = props.action?.paramExpList ?? [];
      form.dialogName = exeExp?.dialogName ?? '';
      params.value = paramExpList;
      paramValues.value = paramExpList.reduce((acc, item) => Object.assign(acc, { [item.prop]: item.rightVal }), {});
    }
  },
  { immediate: true }
);

const onNewParam = () => {
  params.value.push({ prop: paramNameTemp.value, name: paramNameTemp.value });
  popoverRef.value.hide();
};

const getValues = () => {
  return Promise.resolve({
    exeExpList: [
      {
        needReturn: false,
        methodName: 'show',
        type: ExeType.Dialog,
        dialogName: form.dialogName
      }
    ],
    paramExpList: params.value.map(param => {
      return { prop: param.prop, rightVal: paramValues.value[param.prop], name: param.name };
    })
  });
};

const handleRemoveParam = param => {
  const index = params.value.findIndex(g => g.prop === param.prop);
  params.value.splice(index, 1);
  delete paramValues.value[param.prop];
};

defineExpose({ getValues });
</script>
