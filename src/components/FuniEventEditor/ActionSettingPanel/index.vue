<template>
  <div class="action-setting-panel">
    <keep-alive>
      <component ref="settingForm" :is="componentNode" :key="`${data.id}_${data.code}`" :action="data" />
    </keep-alive>
  </div>
</template>

<script>
export default {
  name: 'ActionSettingPanel',
  inheritAttrs: false
};
</script>

<script setup>
import { ACTION } from '../enums.js';
import { ref, computed } from 'vue';
import ActionSettingEmpty from './ActionSettingEmpty.vue';
import ActionSettingSetState from './ActionSettingSetState/index.vue';
import ActionSettingEmitEvent from './ActionSettingEmitEvent.vue';
import ActionSettingShowModal from './ActionSettingShowModal.vue';
import ActionSettingCloseModal from './ActionSettingCloseModal.vue';
import ActionSettingNavigateTo from './ActionSettingNavigateTo.vue';
import ActionSettingCallMethod from './ActionSettingCallMethod.vue';
import ActionSettingValidateForm from './ActionSettingValidateForm.vue';
import ActionSettingSnippet from './ActionSettingSnippet.vue';
import ActionSettingConfirm from './ActionSettingConfirm.vue';
import ActionSettingRequestApi from './ActionSettingRequestApi/index.vue';

const props = defineProps({
  data: { type: Object, default: () => ({}) }
});

const settingForm = ref();

const componentMap = {
  [ACTION.NAVIGATE_TO.code]: ActionSettingNavigateTo,
  [ACTION.SET_STATE.code]: ActionSettingSetState,
  [ACTION.EMIT_EVENT.code]: ActionSettingEmitEvent,
  [ACTION.SHOW_MODAL.code]: ActionSettingShowModal,
  [ACTION.CLOSE_MODAL.code]: ActionSettingCloseModal,
  [ACTION.VALIDATE_FORM.code]: ActionSettingValidateForm,
  [ACTION.CALL_METHOD.code]: ActionSettingCallMethod,
  [ACTION.SNIPPET.code]: ActionSettingSnippet,
  [ACTION.CONFIRM.code]: ActionSettingConfirm,
  [ACTION.REQUEST_API.code]: ActionSettingRequestApi
};

const componentNode = computed(() => {
  return componentMap[props.data.code] || ActionSettingEmpty;
});

const saveData = () => {
  return !!settingForm.value.getValues ? settingForm.value.getValues() : Promise.resolve({});
};

defineExpose({
  saveData
});
</script>
<style lang="scss" scoped>
.action-setting-panel {
  padding: 24px 32px 24px 24px;
  --el-font-size-base: 12px;
  :deep() {
    .el-input {
      --el-input-bg-color: #f1f2f5 !important;
    }
  }
}
</style>
