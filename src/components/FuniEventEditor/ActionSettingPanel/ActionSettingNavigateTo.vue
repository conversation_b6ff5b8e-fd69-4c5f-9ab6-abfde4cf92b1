<template>
  <el-form label-width="120px">
    <el-form-item label="选择页面">
      <funi-autocomplete
        class="w-1/2"
        labelKey="name"
        valueKey="url"
        primaryKey="id"
        placeholder="请选择页面"
        url="/as/page/findAll"
        :model-value="routerPath"
        :requestParams="pageRequestParams"
        @select="handleSelectPage"
      />
    </el-form-item>
    <el-divider v-if="params.length">传入页面参数</el-divider>
    <el-form-item v-for="param in params" :key="param.prop" :label="param.name">
      <funi-variable-setter class="!w-[calc(50%+22px)]" v-model="paramValues[param.prop]" :variables="variables" />
      <el-link icon="Delete" :underline="false" @click="handleRemoveParam(param)"></el-link>
    </el-form-item>
  </el-form>

  <el-popover ref="popoverRef" :width="300" trigger="click">
    <template #reference>
      <el-link type="primary"><funi-icon icon="ep:circle-plus" />&nbsp;新建页面 URL 参数</el-link>
    </template>
    <div class="popover-content">
      <el-form label-width="auto" label-position="top" size="small">
        <el-form-item label="参数名称">
          <el-input v-model="paramNameTemp" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="newParamDisabled" @click="onNewParam">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-popover>
</template>

<script setup>
import { computed, inject, toRaw, ref, watch } from 'vue';
import { EVENT_EDITOR_INJECTION } from '../symbols';
import { ExeType } from '../Logic/types';

const props = defineProps({ action: Object });

const { app_id, variables } = inject(EVENT_EDITOR_INJECTION);
const pageRequestParams = computed(() => ({ app_id }));

const popoverRef = ref();
const paramNameTemp = ref();
const params = ref([]);
const paramValues = ref({});
const routerPath = ref('');

const newParamDisabled = computed(() => {
  return params.value.some(item => item.name === paramNameTemp.value);
});

watch(
  () => props.action,
  action => {
    if (!!action) {
      const [exeExp] = action.exeExpList ?? [];
      const paramExpList = action.paramExpList ?? [];
      const path = exeExp?.routerPath ?? '';
      params.value = paramExpList;
      paramValues.value = paramExpList.reduce((acc, item) => Object.assign(acc, { [item.prop]: item.rightVal }), {});
      routerPath.value = path;
    }
  },
  { immediate: true }
);

const onNewParam = () => {
  params.value.push({ prop: paramNameTemp.value, name: paramNameTemp.value });
  popoverRef.value.hide();
};

const getValues = () => {
  return Promise.resolve({
    exeExpList: [{ needReturn: false, methodName: 'push', type: ExeType.Link, routerPath: routerPath.value }],
    paramExpList: params.value.map(param => {
      return { prop: param.prop, rightVal: paramValues.value[param.prop], name: param.name };
    })
  });
};

const handleRemoveParam = param => {
  const index = params.value.findIndex(g => g.prop === param.prop);
  params.value.splice(index, 1);
  delete paramValues.value[param.prop];
};

const handleSelectPage = page => {
  if (routerPath.value !== page.url) {
    routerPath.value = page.url;
    const pageParams = JSON.parse(page.page_params || '[]').map(item => ({
      name: `${item.name}（${item.Instructions}）`,
      prop: item.name
    }));
    params.value = pageParams;
    paramValues.value = pageParams.reduce((acc, item) => Object.assign(acc, { [item.prop]: '' }), {});
  }
};

defineExpose({ getValues });
</script>
<style lang="scss" scoped>
:deep(.el-divider) {
  .el-divider__text {
    color: #000000d9;
    font-weight: 400;
    font-size: 12px;
  }
}
</style>
