<template>
  <el-form :model="form" label-width="120px">
    <el-form-item label="选择表单">
      <funi-select class="w-1/2" v-model="form.formValue" placeholder="请选择表单" :action="modalAction" />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { inject, reactive, watch } from 'vue';
import { EVENT_EDITOR_INJECTION } from '../symbols';
import { ExeType } from '../Logic/types';

defineOptions({ name: 'ActionSettingValidateForm' });

const props = defineProps({
  action: { type: Object, default: () => ({}) }
});

const { forms } = inject(EVENT_EDITOR_INJECTION);
const modalAction = () => Promise.resolve(forms.value.map(i => ({ label: i.key, value: i.value })));

const form = reactive({});

watch(
  () => props.action,
  () => {
    if (!!props.action) {
      const [exeExp] = props.action?.exeExpList ?? [];
      form.formValue = exeExp?.formValue ?? '';
      form.params = props.action?.paramExpress;
    }
  },
  { immediate: true }
);

const getValues = () => {
  return Promise.resolve({
    exeExpList: [
      {
        needReturn: false,
        type: ExeType.Component,
        component: { value: form.formValue, method: { value: 'submitForm' } }
      }
    ]
  });
};

defineExpose({ getValues });
</script>
