<template>
  <el-form :model="form" label-width="120px">
    <el-form-item label="选择组件">
      <funi-select class="w-1/2" v-model="form.component" placeholder="请选择组件" :action="componentAction" />
    </el-form-item>
    <el-form-item label="选择组件方法">
      <funi-select class="w-1/2" v-model="form.method" placeholder="请选择组件方法" :options="activeMethods" />
    </el-form-item>
    <el-divider v-if="params.length">传入页面参数</el-divider>
    <el-form-item v-for="param in params" :key="param.key" :label="param.name">
      <funi-variable-setter class="!w-[calc(50%+22px)]" v-model="paramValues[param.name]" :variables="variables" />
    </el-form-item>
  </el-form>

  <el-popover ref="popoverRef" :width="300" trigger="click">
    <template #reference>
      <el-link type="primary"><funi-icon icon="ep:circle-plus" />&nbsp;新建页面 URL 参数</el-link>
    </template>
    <div class="popover-content">
      <el-form label-width="auto" label-position="top" size="small">
        <el-form-item label="参数名称">
          <el-input v-model="paramNameTemp" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="newParamDisabled" @click="onNewParam">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-popover>
</template>

<script setup>
import { computed, inject, toRaw, reactive, ref, watch } from 'vue';
import { EVENT_EDITOR_INJECTION } from '../symbols';
import { ExeType } from '../Logic/types';

defineOptions({ name: 'ActionSettingCallMethod' });

const props = defineProps({ action: Object });

const { components, variables } = inject(EVENT_EDITOR_INJECTION);

const popoverRef = ref();
const paramNameTemp = ref();
const params = ref([]);
const form = reactive({ component: '', method: '' });
const paramValues = reactive({});

const componentAction = () => Promise.resolve(components.value.map(i => ({ label: i.key, value: i.value, ...i })));
const activeMethods = computed(() =>
  (components.value.find(i => i.value === form.component)?.methods ?? []).map(i => ({ label: i.key, value: i.value }))
);

const newParamDisabled = computed(() => !!params.value.find(item => item.name === paramNameTemp.value));

watch(
  () => props.action,
  () => {
    if (!!props.action) {
      const [exeExp] = props.action?.exeExpList ?? [];
      params.value = props.action?.paramExpList?.map(item => ({ name: item.prop, key: item.rightVal })) ?? [];
      params.value.forEach(item => (paramValues[item.name] = item.key));
      if (!!exeExp?.component && $utils.isPlainObject(exeExp?.component)) {
        form.component = exeExp.component.value ?? '';
        form.method = exeExp.component.method?.value ?? '';
      }
    }
  },
  { immediate: true }
);

const onNewParam = () => {
  params.value.push({
    key: $utils.guid(),
    name: paramNameTemp.value
  });
  popoverRef.value.hide();
};

const getValues = () => {
  return Promise.resolve({
    exeExpList: [
      {
        needReturn: false,
        type: ExeType.Component,
        component: { value: form.component, method: activeMethods.value.find(i => i.value === form.method) }
      }
    ],
    paramExpList: Object.entries(toRaw(paramValues)).map(([key, value]) => ({ prop: key, rightVal: value }))
  });
};

defineExpose({ getValues });
</script>
<style lang="scss" scoped>
:deep(.el-divider) {
  .el-divider__text {
    color: #000000d9;
    font-weight: 400;
    font-size: 12px;
  }
}
</style>
