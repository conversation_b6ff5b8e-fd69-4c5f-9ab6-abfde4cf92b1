<template>
  <div class="action-setting-set-state-container">
    <ActionSettingSetStateRowItem
      v-for="(item, index) in rows"
      ref="rowRefs"
      :key="item.guid"
      :item="item"
      :enable-remove="rows.length > 1"
      :variables="variables"
      :options="assignOptions"
      @add="onAdd"
      @remove="onRemove"
    />
  </div>
</template>

<script setup>
import { watch, ref } from 'vue';
import { inject } from 'vue';
import { EVENT_EDITOR_INJECTION } from '../../symbols';
import ActionSettingSetStateRowItem from './ActionSettingSetStateRowItem.vue';

const { assignOptions, variables } = inject(EVENT_EDITOR_INJECTION);

const props = defineProps({
  action: { type: Object, default: () => ({}) }
});
const rowRefs = ref([]);
const rows = ref([{ guid: $utils.guid(), gl: '', dq: '' }]);

watch(
  () => props.action,
  () => {
    if (props.action?.assign) {
      rows.value = props.action.assign.map(item => ({ guid: $utils.guid(), gl: item.rightValCode, dq: item.leftVal }));
    }
  },
  { immediate: true }
);

const getValues = () => {
  console.log(rowRefs.value.map(item => item.getValues()));
  return Promise.resolve(rowRefs.value.map(item => item.getValues()));
};

const onAdd = () => {
  rows.value.push({ guid: $utils.guid(), gl: '', dq: '' });
};

const onRemove = guid => {
  rows.value = rows.value.filter(item => item.guid !== guid);
};

defineExpose({ getValues });
</script>
<style lang="scss" scoped>
.action-setting-set-state-container {
  display: flex;
  flex-direction: column;

  .action-setting-set-state-row + .action-setting-set-state-row {
    margin-top: 16px;
  }
}
</style>
