<template>
  <div class="action-setting-set-state-row">
    <div class="action-setting-set-state-row-item flex-1 gl-group">
      <el-tree-select :data="glOptions" placeholder="关联表单字段" filterable allow-create clearable v-model="gl" />
      <el-tooltip content="关联表单字段">
        <el-button class="funi-variable-setter__dialog-trigger" link @click="triggerDialog">
          <funi-icon color="black" icon="cil:functions" font-size="16" />
        </el-button>
      </el-tooltip>
      <BindVariableDialog
        ref="bindVariableDialog"
        title="关联表单字段"
        :variables="variables"
        @confirm="handleConfirm"
      />
    </div>
    <div class="action-setting-set-state-row-item flex-shrink-0">
      <span>填充到当前{{ isCurd ? '行' : '表单' }}</span>
    </div>
    <div class="action-setting-set-state-row-item flex-1">
      <funi-select :options="options.dq" placeholder="字段" clearable v-model="dq" />
    </div>
    <div class="action-setting-set-state-row-item flex-shrink-0 operation-btn-group">
      <el-link type="primary" style="margin: 0px 16px" @click="onAdd">添加</el-link>
      <el-link v-if="enableRemove" type="primary" @click="onRemove()">删除</el-link>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, watch } from 'vue';
import BindVariableDialog from '@/components/FuniVariableSetter/BindVariableDialog.vue';
import { decodeFormula } from '@/components/FuniVariableSetter/utils';

const props = defineProps({
  item: { type: Object, default: () => ({}) },
  enableRemove: { type: Boolean, default: true },
  variables: { type: Array, default: () => [] },
  options: { type: Object, default: () => ({}) }
});
const emit = defineEmits(['add', 'remove']);
const isCurd = computed(() => props.options.type === 'row');

const gl = ref();
const dynamicGlOptions = ref([]);
const glOptions = computed(() => [...props.options.gl, ...dynamicGlOptions.value]);

const dq = ref(props.item.dq);

const bindVariableDialog = ref(null);

const triggerDialog = () => {
  bindVariableDialog.value.show(gl.value);
};

const handleConfirm = val => {
  if (!glOptions.value.find(item => item.value === val)) {
    const { label } = decodeFormula(val);
    dynamicGlOptions.value = [{ label, value: val }];
  }
  gl.value = val;
};

watch(() => props.item.gl, handleConfirm, { immediate: true });

const onAdd = () => {
  emit('add');
};

const onRemove = () => {
  emit('remove', props.item.guid);
};

const getValues = () => {
  const { expression } = decodeFormula(gl.value);
  return { leftVal: dq.value, operator: '=', rightVal: expression, rightValCode: gl.value };
};

defineExpose({ getValues });
</script>
<style lang="scss" scoped>
.action-setting-set-state-row {
  display: flex;
  align-items: center;

  &-item > span {
    color: rgb(112, 118, 130);
    font-size: 15px;
  }

  &-item + &-item {
    margin-left: 16px;
  }
  .gl-group {
    display: flex;
    align-items: center;
  }
  .operation-btn-group {
    display: flex;
    align-items: center;
    width: 120px;
    margin-left: 0px;
  }
}
</style>
