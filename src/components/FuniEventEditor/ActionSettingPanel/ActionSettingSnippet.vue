<template>
  <code-editor mode="javascript" :readonly="false" :user-worker="false" v-model="snippets" />
</template>

<script setup>
import { ref, watch } from 'vue';
import CodeEditor from '@/components/FuniFormEngine/code-editor/index.vue';

defineOptions({ name: 'ActionSettingSnippet' });

const props = defineProps({ action: Object });

const snippets = ref('');

watch(
  () => props.action,
  () => {
    !!props.action && (snippets.value = props.action.snippets);
  },
  { immediate: true }
);

const getValues = () => {
  return Promise.resolve(snippets.value);
};

defineExpose({ getValues });
</script>
<style lang="scss" scoped>
:deep(.el-divider) {
  .el-divider__text {
    color: #000000d9;
    font-weight: 400;
    font-size: 12px;
  }
}
</style>
