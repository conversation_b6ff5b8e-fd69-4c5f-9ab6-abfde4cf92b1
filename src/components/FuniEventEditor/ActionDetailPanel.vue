<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-10-25 17:42:45
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-10-23 10:25:46
 * @FilePath: /src/components/FuniEventEditor/ActionDetailPanel.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->

<template>
  <div class="action-detail">
    <div class="action-detail__start">
      <ActionContext ref="actionContext" :rootAction="rootAction" @activeChange="handleActiveChange" />
    </div>
    <div class="action-detail__end">
      <ActionSettingPanel ref="actionSettingPanel" :data="activeAction" />
    </div>
  </div>
</template>

<script setup>
import { Logic } from './Logic';
import { ref, nextTick } from 'vue';
import { ACTION_TYPE } from './enums.js';
import ActionContext from './ActionContext/index.jsx';
import ActionSettingPanel from './ActionSettingPanel/index.vue';

const props = defineProps({
  rootAction: { type: Object, default: () => ({}) }
});
const emit = defineEmits(['reset']);

const activeAction = ref();
const actionContext = ref();
const actionSettingPanel = ref();

/**
 * 切换左侧动作树选中项，右侧面板跟随变化
 * 1.保存当前面板内容
 * 2.切换至最新面板
 * @param {*} action
 */
const handleActiveChange = async action => {
  if (!action) {
    activeAction.value = null;
    emit('reset');
    return;
  }
  await nextTick();
  // 保存当前面板内容数据
  await syncSettingPanelData(activeAction?.value);
  // 切换至最新面板
  console.debug('NewAction', action);
  activeAction.value = action;
};

const syncSettingPanelData = async action => {
  try {
    if (!!action) {
      const settingData = await actionSettingPanel.value.saveData();
      let key;
      switch (action.type) {
        case ACTION_TYPE.ASSIGN:
          key = 'assign';
          break;
        case ACTION_TYPE.NET:
          key = 'request';
          break;
        case ACTION_TYPE.SNIPPET:
          key = 'snippets';
          break;
        default:
          key = '';
          break;
      }
      actionContext.value.setData({ settingData, action, key });
    }
    return Promise.resolve();
  } catch (error) {
    return Promise.reject(error);
  }
};

const sync = async () => {
  try {
    await syncSettingPanelData(activeAction.value);
    const logicObj = new Logic(actionContext.value.dataTree[0]);
    return Promise.resolve(logicObj);
  } catch (error) {
    return Promise.reject(error);
  }
};

defineExpose({
  sync
});
</script>

<style lang="scss" scoped>
.action-detail {
  display: flex;
  height: 100%;

  &__start {
    flex: 0 1 30%;
    padding: 16px;
    box-sizing: border-box;
    max-width: 400px;
    min-width: 220px;
    border-right: 1px solid #e3e6eb;
    overflow: auto;
  }

  &__end {
    flex: 1 1 0px;
    border-right: 1px solid #e3e6eb;
    overflow: auto;
  }
}
</style>
