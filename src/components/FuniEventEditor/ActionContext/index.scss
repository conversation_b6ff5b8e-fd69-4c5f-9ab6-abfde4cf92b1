@font-face {
  font-family: wedaeditor;
  src: url(/wedaeditor.ttf) format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: block;
}

[class*=' icon-'],
[class^='icon-'] {
  font-family: wedaeditor !important;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  font-size: 16px;
  min-width: 16px;
  min-height: 16px;
  width: auto;
  height: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-delete:hover {
  cursor: pointer;
}

.icon-popout {
  min-width: 12px;
  min-height: 12px;
  width: 12px;
  height: 12px;
  font-size: 12px;
}

.icon-outline-tree-data:before {
  content: '\e905';
}

.icon-outline-tree-class:before {
  content: '\e906';
}

.icon-outline-tree-style:before {
  content: '\e907';
}

.icon-outline-tree-action:before {
  content: '\e908';
}

.icon-outline-tree-if:before {
  content: '\e909';
}

.icon-outline-tree-for:before {
  content: '\e90a';
}

.icon-fullscreen-exit:before {
  content: '\e9c5';
}

.icon-popout:before {
  content: '\e9b8';
}

.icon-copy-path:before {
  content: '\e9c4';
}

.icon-fullscreen:before {
  content: '\e96f';
}

.icon-git-fork:before {
  content: '\e904';
}

.icon-file-add:before {
  content: '\e903';
}

.icon-json-file:before {
  content: '\e900';
}

.icon-refresh:before {
  content: '\e901';
}

.icon-history:before {
  content: '\e902';
}

.icon-logout:before {
  content: '\e90b';
}

.icon-express-layout:before {
  content: '\e9b7';
}

.icon-resize:before {
  content: '\e9b9';
}

.icon-adopt:before {
  content: '\e9b5';
}

.icon-done:before {
  content: '\e9b6';
}

.icon-check-circle-filled:before {
  content: '\e9b3';
}

.icon-error-circle-filled:before {
  content: '\e9b4';
}

.icon-bind-data:before {
  content: '\e90c';
}

.icon-fn-var:before {
  content: '\e90d';
}

.icon-chat:before {
  content: '\e9b2';
}

.icon-swap:before {
  content: '\e9b1';
}

.icon-border-less:before {
  content: '\e991';
}

.icon-font-y:before {
  content: '\e90e';
}

.icon-font-x:before {
  content: '\e90f';
}

.icon-font-a:before {
  content: '\e910';
}

.icon-font-b:before {
  content: '\e911';
  color: #4b5b76;
}

.icon-layer-bottom:before {
  content: '\e942';
}

.icon-layer-top:before {
  content: '\e943';
}

.icon-layer-down:before {
  content: '\e949';
}

.icon-layer-up:before {
  content: '\e951';
}

.icon-array-font:before {
  content: '\e941';
}

.icon-object-font:before {
  content: '\e9a4';
}

.icon-number-font:before {
  content: '\e9a6';
}

.icon-bool:before {
  content: '\e9a8';
}

.icon-string-font:before {
  content: '\e9aa';
}

.icon-style-align-vertical-spacing:before {
  content: '\e912';
}

.icon-style-border-right:before {
  content: '\e98f';
}

.icon-style-line-height:before {
  content: '\e9a0';
}

.icon-straw:before {
  content: '\e98d';
}

.icon-style-border-bottom:before {
  content: '\e98c';
}

.icon-style-border-left:before {
  content: '\e98e';
}

.icon-style-border-top:before {
  content: '\e990';
}

.icon-style-border-width:before {
  content: '\e992';
}

.icon-style-border-radius-top-right:before {
  content: '\e993';
}

.icon-style-border-radius-top-left:before {
  content: '\e994';
}

.icon-style-border-radius-bottom-right:before {
  content: '\e995';
}

.icon-style-border-radius-bottom-left:before {
  content: '\e996';
}

.icon-style-img-cover:before {
  content: '\e997';
}

.icon-style-align-vertical-center:before {
  content: '\e998';
}

.icon-style-align-top:before {
  content: '\e999';
}

.icon-style-align-right:before {
  content: '\e99a';
}

.icon-style-align-left:before {
  content: '\e99b';
}

.icon-style-align-horizontal-spacing:before {
  content: '\e99c';
}

.icon-style-align-horizontal-center:before {
  content: '\e99d';
}

.icon-style-align-bottom:before {
  content: '\e99e';
}

.icon-style-img-position-top-right:before {
  content: '\e99f';
}

.icon-style-img-position-top-left:before {
  content: '\e9a1';
}

.icon-style-img-position-top-center:before {
  content: '\e9a3';
}

.icon-style-img-position-center:before {
  content: '\e9a5';
}

.icon-style-img-position-center-right:before {
  content: '\e9a7';
}

.icon-style-img-position-center-left:before {
  content: '\e9a9';
}

.icon-style-img-position-bottom-right:before {
  content: '\e9ab';
}

.icon-style-img-position-bottom-left:before {
  content: '\e9ad';
}

.icon-style-img-position-bottom-center:before {
  content: '\e9af';
}

.icon-clear:before {
  content: '\e91e';
}

.icon-logic-flow:before {
  content: '\e980';
}

.icon-folder:before {
  content: '\e930';
}

.icon-var-datasource:before {
  content: '\e94b';
}

.icon-var-null:before {
  content: '\e981';
}

.icon-var-page:before {
  content: '\e982';
}

.icon-system:before {
  content: '\e983';
}

.icon-connector:before {
  content: '\e97f';
}

.icon-style-text-align-center:before {
  content: '\e913';
}

.icon-style-text-align-left:before {
  content: '\e914';
}

.icon-style-text-align-right:before {
  content: '\e915';
}

.icon-style-text-align-spacing:before {
  content: '\e916';
}

.icon-style-border-radius:before {
  content: '\e917';
}

.icon-style-img-repeat:before {
  content: '\e918';
}

.icon-association:before {
  content: '\e919';
}

.icon-swap-right:before {
  content: '\e97d';
}

.icon-arrowdown--fill:before {
  content: '\e97e';
}

.icon-pencil:before {
  content: '\e91a';
}

.icon-git-pull:before {
  content: '\e91b';
}

.icon-tag:before {
  content: '\e91c';
}

.icon-git-commit:before {
  content: '\e91d';
}

.icon-local:before {
  content: '\e976';
}

.icon-plus-rectangle:before {
  content: '\e974';
}

.icon-minus-rectangle:before {
  content: '\e975';
}

.icon-flash-circle:before {
  content: '\e91f';
}

.icon-view-list:before {
  content: '\e920';
}

.icon-if:before {
  content: '\e971';
}

.icon-folder-add:before {
  content: '\e987';
}

.icon-export-package:before {
  content: '\e988';
}

.icon-cloud-sync:before {
  content: '\e989';
}

.icon-multi-terminal:before {
  content: '\e98a';
}

.icon-nav:before {
  content: '\e98b';
}

.icon-login:before {
  content: '\e96a';
}

.icon-version:before {
  content: '\e969';
}

.icon-arrowup--line:before {
  content: '\e921';
}

.icon-arrowdown--line:before {
  content: '\e922';
}

.icon-drag:before {
  content: '\e964';
}

.icon-file-list:before {
  content: '\e965';
}

.icon-more-vertical:before {
  content: '\e966';
}

.icon-feedback:before {
  content: '\e923';
}

.icon-miniprogram-pure:before {
  content: '\e962';
}

.icon-documents:before {
  content: '\e961';
}

.icon-img-l:before {
  content: '\e960';
}

.icon-redo:before {
  content: '\e924';
}

.icon-component:before {
  content: '\e925';
}

.icon-template-guide:before {
  content: '\e95e';
}

.icon-back:before {
  content: '\e95d';
}

.icon-editor:before {
  content: '\e95a';
}

.icon-hotkey:before {
  content: '\e95b';
}

.icon-json:before {
  content: '\e95c';
}

.icon-creator:before {
  content: '\e955';
}

.icon-material:before {
  content: '\e956';
}

.icon-upload:before {
  content: '\e957';
}

.icon-user:before {
  content: '\e958';
}

.icon-variable:before {
  content: '\e959';
}

.icon-age:before {
  content: '\e926';
}

.icon-copy:before {
  content: '\e927';
}

.icon-dashboard:before {
  content: '\e928';
}

.icon-delete:before {
  content: '\e929';
}

.icon-var-font:before {
  content: '\e92a';
}

.icon-gray:before {
  content: '\e933';
}

.icon-master-slave:before {
  content: '\e934';
}

.icon-radio:before {
  content: '\e939';
}

.icon-save:before {
  content: '\e940';
}

.icon-tree:before {
  content: '\e94e';
}

.icon-associate:before {
  content: '\e92b';
}

.icon-const:before {
  content: '\e92c';
}

.icon-date:before {
  content: '\e945';
}

.icon-email:before {
  content: '\e946';
}

.icon-field:before {
  content: '\e947';
}

.icon-name:before {
  content: '\e948';
}

.icon-relative-amount:before {
  content: '\e94f';
}

.icon-rich-text:before {
  content: '\e950';
}

.icon-telephone:before {
  content: '\e952';
}

.icon-time:before {
  content: '\e953';
}

.icon-url:before {
  content: '\e954';
}

.icon-plus-circle:before {
  content: '\e92d';
}

.icon-minus-circle:before {
  content: '\e92e';
}

.icon-fix:before {
  content: '\e92f';
}

.icon-layout:before {
  content: '\e931';
}

.icon-console:before {
  content: '\e932';
}

.icon-link:before {
  content: '\e935';
}

.icon-packup:before {
  content: '\e936';
}

.icon-component-custom:before {
  content: '\e937';
}

.icon-component-default:before {
  content: '\e938';
}

.icon-manage-library:before {
  content: '\e93a';
}

.icon-notice:before {
  content: '\e93b';
}

.icon-update:before {
  content: '\e95f';
}

.icon-action-click:before {
  content: '\e93c';
}

.icon-action-blur:before {
  content: '\e93d';
}

.icon-action-focus:before {
  content: '\e93e';
}

.icon-action-more:before {
  content: '\e93f';
}

.icon-zoom-in:before {
  content: '\e944';
}

.icon-zoom-out:before {
  content: '\e94a';
}

.icon-search:before {
  content: '\e94c';
}

.icon-undo:before {
  content: '\e94d';
}

.icon-guide:before {
  content: '\e963';
}

.icon-refresh1:before {
  content: '\e967';
}

.icon-image:before {
  content: '\e968';
}

.icon-setting:before {
  content: '\e96b';
}

.icon-weda:before {
  content: '\e96c';
}

.icon-bug:before {
  content: '\e96e';
}

.icon-ide:before {
  content: '\e96d';
}

.icon-menu:before {
  content: '\e970';
}

.icon-plus:before {
  content: '\e972';
}

.icon-dragin:before {
  content: '\e973';
}

.icon-checked:before {
  content: '\e977';
}

.icon-show:before {
  content: '\e978';
}

.icon-hide:before {
  content: '\e979';
}

.icon-unfold-more:before {
  content: '\e97a';
}

.icon-arrowleft--line:before {
  content: '\e97b';
}

.icon-arrowright--line:before {
  content: '\e97c';
}

.icon-var-param:before {
  content: '\e984';
}

.icon-var-state:before {
  content: '\e985';
}

.icon-circle-check:before {
  content: '\e986';
}

.icon-publish:before {
  content: '\e9a2';
}

.icon-form:before {
  content: '\e9ac';
}

.icon-app:before {
  content: '\e9ae';
}

.icon-landing:before {
  content: '\e9b0';
}

.icon-list:before {
  content: '\e9ba';
}

.icon-docs:before {
  content: '\e9bb';
}

.icon-pagehome:before {
  content: '\e9bc';
}

.icon-pagenormal:before {
  content: '\e9bd';
}

.icon-preview:before {
  content: '\e9be';
}

.icon-alert-close:before {
  content: '\e9bf';
}

.icon-phone:before {
  content: '\e9c0';
}

.icon-open-preview:before {
  content: '\e9c1';
}

.icon-miniprogram:before {
  content: '\e9c2';
}

.icon-pc:before {
  content: '\e9c3';
}

.tea-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-position: inherit;
}

.action-context-tree {
  font-size: 12px;
  color: #626364;

  .icon-if {
    &:after {
      content: '';
      display: block;
      width: 1px;
      position: absolute;
      top: 10px;
      left: 13psx;
      bottom: 10px;
    }

    &.is-success {
      color: #00a870;

      &:after {
        background-color: #00a870;
      }
    }

    &.is-error {
      color: #e34d59;
      &:after {
        background-color: #e34d59;
      }
    }
  }
}

// action-context-tree__item
.action-context-tree__item {
  position: relative;
  min-width: 150px;
}

.action-context-tree__item .no-hover {
  background: #fff;
}

.action-context-tree__item.is-status > .action-context-tree__subtree {
  padding-left: 21px;
}

.action-context-tree__item-tips {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-top: 1px solid #e3e6eb;
  margin: 0 12px;
}

.action-context-tree__item-tips > img {
  margin-right: 8px;
}

// action-context-tree__item-label
.action-context-tree__item-label .is-selected {
  color: #0052d9;
}

.action-context-tree__item-label {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  border-radius: 3px;
  background-color: #f1f2f5;
  margin-bottom: 8px;
  cursor: pointer;
}

.action-context-tree__item-label:hover {
  background-color: #f1f2f5;
}

.action-context-tree__item-label:hover .action-context-tree__op {
  visibility: visible;
}

.action-context-tree__item.is-selected > .action-context-tree__item-label {
  background-color: #d4e3fc;
}

.action-context-tree__item.is-status > .action-context-tree__item-label {
  background-color: transparent;
}

.action-context-tree__item.is-status > .action-context-tree__item-label:hover {
  background-color: #f1f2f5;
  cursor: default;
}

//

.action-context-tree__icon {
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.action-context-tree__icon:hover {
  background-color: #f1f2f5;
}

.action-context-tree__text {
  height: 26px;
  line-height: 26px;
  flex: 1 1 0px;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.action-context-tree__op {
  flex: 0 0 auto;
  visibility: hidden;
}

.action-context-tree__op .op-btn--icon {
  height: 24px;
  width: 24px;
  padding: 4px;
  font-size: 16px;
  margin: 0 !important;
}

.action-context-tree__op .op-btn--icon:hover {
  background-color: #e3e6eb;
}

.action-context-tree__subtree--blank {
  position: relative;
}

.action-context-tree__subtree--blank:after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  border: 1px dashed #e3e6eb;
  z-index: -1;
}

.action-context-tree__icon {
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.action-context-tree__icon:hover {
  background-color: #f1f2f5;
}

.tea-text-label {
  color: #4b5b76 !important;
}
