<template>
  <div :class="['action-context-tree__item', { 'is-selected': isSelected }]">
    <div class="action-context-tree__item-label" @click="emit('select')">
      <span :class="['action-context-tree__text', { 'is-selected': isSelected }]">{{ title }}</span>
      <div class="action-context-tree__op">
        <el-popover v-model:visible="gridPanelVisible" placement="right" width="auto" trigger="click">
          <template #reference>
            <el-button class="op-btn op-btn--icon" link @click.stop>
              <funi-icon icon="ant-design:swap-outlined" />
            </el-button>
          </template>
          <ActionGridPanel @select="handleActionSelect" />
        </el-popover>
        <el-button class="op-btn op-btn--icon" link @click.stop="emit('remove')">
          <funi-icon icon="mdi:minus-circle-outline" />
        </el-button>
      </div>
    </div>
    <div class="action-context-tree__subtree">
      <ActionContextStatusItem is-success @insert="e => handleInsertSubItem({ isSuccess: true, action: e })">
        <slot name="success" />
      </ActionContextStatusItem>
      <ActionContextStatusItem @insert="e => handleInsertSubItem({ isError: true, action: e })">
        <slot name="error" />
      </ActionContextStatusItem>
    </div>
  </div>
</template>
<script setup>
import ActionContextStatusItem from './ActionContextStatusItem.vue';
import ActionGridPanel from '../ActionGridPanel.vue';
import { ref } from 'vue';

const props = defineProps({
  title: String,
  root: Boolean,
  isSelected: Boolean
});

const gridPanelVisible = ref(false);
const emit = defineEmits(['insertSubItem', 'remove', 'select', 'swap']);

const handleActionSelect = action => {
  gridPanelVisible.value = false;
  emit('swap', action);
};

const handleInsertSubItem = ({ isSuccess, isError, action }) => {
  emit('insertSubItem', { isSuccess, isError, action });
};
</script>
