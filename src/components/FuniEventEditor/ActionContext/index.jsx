import { computed, nextTick, onMounted, reactive, ref, toRaw, watch } from 'vue';
import ActionContextItem from './ActionContextItem.vue';
import './index.scss';

export default {
  name: 'ActionContext',
  props: {
    rootAction: { type: Object, default: () => ({}) }
  },
  emits: ['activeChange'],
  setup(props, { emit, expose }) {
    const data = ref([]);
    const rootId = ref();

    const dataTree = computed(() => {
      return $utils.toArrayTree(data.value, { parentKey: 'pid' });
    });

    const selectedItem = computed(() => data.value.find(i => i.isSelected));

    watch(
      () => props.rootAction,
      () => {
        if (!!props.rootAction) {
          const rootAction = reactive({
            pid: '0',
            root: true,
            isSelected: true,
            ...props.rootAction,
            id: props.rootAction.id || $utils.guid()
          });
          rootId.value = rootAction.id;
          data.value = $utils.toTreeArray([rootAction]).map(item => {
            return { ...item, isSelected: item.id === rootAction.id };
          });
          resetSelected();
          emit('activeChange', rootAction);
        }
      },
      { immediate: true }
    );

    function handleItemSelect(item) {
      if (!!item && item.id !== selectedItem.value?.id) {
        !!selectedItem.value && (selectedItem.value.isSelected = false);
        item.isSelected = true;
      }

      emit('activeChange', item);
    }

    function removeItem(item) {
      const findChidren = (item, arr) => {
        const children = arr.filter(i => i.pid === item.id);
        return [item, ...children.map(i => findChidren(i, arr)).flat()];
      };
      const childrenItems = findChidren(item, data.value).map(i => i.id);
      data.value = data.value.filter(i => !childrenItems.includes(i.id));
      resetSelected();
    }

    function resetSelected() {
      handleItemSelect(data.value.find(i => i.id === rootId.value));
    }

    function insertSubItem({ isSuccess, isError, parent, action }) {
      const item = {
        ...action,
        id: $utils.guid(),
        pid: parent.id,
        isSelected: false,
        success: isSuccess,
        error: isError
      };
      data.value.push(item);
      nextTick(() => handleItemSelect(item));
    }

    function handleSwapItem({ action, item }) {
      Object.assign(item, action);

      delete item.exeExpList;
      delete item.logicExpList;
      delete item.paramExpList;
      delete item.paramExpress;

      handleItemSelect(item);
    }

    function setData({ settingData, action, key = 'data' } = {}) {
      const validAction = data.value.find(i => i.id === action.id);
      if (!validAction) return;
      Object.assign(validAction, !!key ? { [key]: settingData } : settingData);
    }

    const renderItem = item => {
      return (
        <ActionContextItem
          key={item.id}
          title={item.name}
          root={item.root}
          isSelected={item.isSelected}
          onSelect={() => handleItemSelect(item)}
          onSwap={e => handleSwapItem({ action: e, item })}
          onRemove={() => removeItem(item)}
          onInsertSubItem={e => insertSubItem({ ...e, parent: item })}
        >
          {{
            success: () => renderSubItems((item.children || []).filter(i => i.success)),
            error: () => renderSubItems((item.children || []).filter(i => i.error))
          }}
        </ActionContextItem>
      );
    };

    const renderSubItems = items => items.map(item => renderItem(item));

    expose({ data, dataTree, setData });

    return () => {
      return <div class="action-context-tree">{dataTree.value.map(item => renderItem(item))}</div>;
    };
  }
};
