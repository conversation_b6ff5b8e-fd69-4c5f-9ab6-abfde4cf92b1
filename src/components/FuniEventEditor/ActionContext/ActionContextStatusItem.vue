<template>
  <div class="action-context-tree__item is-status">
    <!-- status-item-label -->
    <div class="action-context-tree__item-label">
      <span class="action-context-tree__icon">
        <i :class="['tea-icon', 'icon-if', { 'is-success': isSuccess, 'is-error': !isSuccess }]"></i>
      </span>
      <span class="action-context-tree__text">
        <span class="tea-text-label">{{ title }}</span>
      </span>
      <div class="action-context-tree__op">
        <el-popover v-model:visible="gridPanelVisible" placement="right" width="auto" trigger="click">
          <template #reference>
            <el-button class="op-btn op-btn--icon" link>
              <funi-icon icon="ep:plus" />
            </el-button>
          </template>
          <ActionGridPanel @select="handleActionSelect" />
        </el-popover>
      </div>
    </div>
    <!-- subtree -->
    <div class="action-context-tree__subtree">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { computed, ref } from 'vue';
import ActionGridPanel from '../ActionGridPanel.vue';

const props = defineProps({
  isSuccess: Boolean
});

const gridPanelVisible = ref(false);

const title = computed(() => {
  return props.isSuccess ? '成功时' : '失败时';
});

const emit = defineEmits(['insert']);

const handleActionSelect = action => {
  gridPanelVisible.value = false;
  emit('insert', action);
};
</script>
