function generateEvent(funConfig, isNest = false) {
  let funCode = '';
  let assignExpList = []; //赋值表达式
  let paramExpList = []; //组装参数表达式
  let exeExpList = [];
  if (funConfig.assignExpList && funConfig.assignExpList.length > 0) {
    funConfig.assignExpList.forEach(item => {
      let expStr = `${item.declaration} ${item.leftVal} ${item.operator} ${item.rightVal};`;
      assignExpList.push(expStr);
    });
  }

  if (funConfig.paramExpList && funConfig.paramExpList.length >= 0) {
    funConfig.paramExpList.forEach(item => {
      let expStr = `param.${item.prop} = ${item.rightVal};`;
      paramExpList.push(expStr);
    });
  }
  if (funConfig.exeExpList && funConfig.exeExpList.length > 0) {
    funConfig.exeExpList.forEach(item => {
      let rightExp = '';
      if (item.type === 'dialog') {
        rightExp = `${item.dialogName}.${item.methodName}(param);`;
      } else if (item.type === 'router') {
        rightExp = `context.router.${item.methodName}({path:'${item.pageid}',query:param})`;
      } else if (item.type === 'link') {
        let routerStr;
        if (item.routerName) {
          routerStr = `name:'${item.routerName}'`;
        } else if (item.routerPath) {
          routerStr = `path:'${item.routerPath}'`;
        }
        rightExp = `context.router.${item.methodName}({${routerStr},query:param})`;
      } else if (['alert', 'message', 'notify'].indexOf(item.type) >= 0) {
        if (item.type === 'alert') {
          rightExp = `
            const {title,message} = param;
            context['${item.type}'](message,title);
            `;
        } else if (item.type === 'notify') {
          rightExp = `
            context['${item.type}'](param);
            `;
        } else {
          rightExp = `
            const {message} = param;
            context['${item.type}'][param.type](message);
            `;
        }
      } else if (item.type === 'log') {
        rightExp = `console.log(param)`;
      }
      let expStr = `${item.needReturn ? 'return' : ''}${rightExp}`;
      exeExpList.push(expStr);
    });
    funCode = isNest
      ? `
      ${assignExpList.join('')}
        ${paramExpList.join('')}
        ${exeExpList.join('')}
      `
      : `()=>{
        ${assignExpList.join('')}
        let param={};
        ${paramExpList.join('')}
        ${exeExpList.join('')}
      }`;
  } else if (funConfig.logicStringVal && funConfig.logicStringVal.length > 0) {
    let successCode = '';
    let failCode = '';
    if (funConfig.success) {
      successCode = this.generateEvent(funConfig.success, true);
    }
    if (funConfig.fail) {
      failCode = this.generateEvent(funConfig.fail, true);
    }
    funCode = isNest
      ? `${assignExpList.join('')}
        ${paramExpList.join('')}
        if(${funConfig.logicStringVal}){
          ${successCode}
        } else {
          ${failCode}
        }`
      : `()=>{
        ${assignExpList.join('')}
        let param={};
        ${paramExpList.join('')}
        if(${funConfig.logicStringVal}){
          ${successCode}
        } else {
          ${failCode}
        }
      }`;
  } else if (funConfig.logicExpList && funConfig.logicExpList.length > 0) {
    let successCode = '';
    let failCode = '';
    if (funConfig.success) {
      successCode = this.generateEvent(funConfig.success, true);
    }
    if (funConfig.fail) {
      failCode = this.generateEvent(funConfig.fail, true);
    }
    let logicExpList = [];
    funConfig.logicExpList.forEach(item => {
      let logicExpStr = `${item.leftVal}${item.operator}${item.rightVal}${item.logicOperator}`;
      logicExpList.push(logicExpStr);
    });
    funCode = isNest
      ? `${assignExpList.join('')}
        ${paramExpList.join('')}
        if(${logicExpList.join('')}){
          ${successCode}
        } else {
          ${failCode}
        }`
      : `()=>{
        ${assignExpList.join('')}
        let param={};
        ${paramExpList.join('')}
        if(${logicExpList.join('')}){
          ${successCode}
        } else {
          ${failCode}
        }
      }`;
  } else if (funConfig.request) {
    let successCode = '';
    let failCode = '';
    if (funConfig.success) {
      successCode = this.generateEvent(funConfig.success, true);
    }
    if (funConfig.fail) {
      failCode = this.generateEvent(funConfig.fail, true);
    }
    let requestStr = `window.$http.${funConfig.request.method ?? 'post'}(${funConfig.request.url},param)
      .then(${funConfig.request.responseName ?? '()'}=>{
        ${successCode}
      })
      .catch(${funConfig.request.errorName ?? '()'}=>{
        ${failCode}
      })
      `;
    funCode = requestStr;
  }
  return funCode;
}
