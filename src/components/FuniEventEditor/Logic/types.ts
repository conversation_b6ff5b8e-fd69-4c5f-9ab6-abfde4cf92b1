export type ActionItem = {
  code: string;
  type: string;
  error?: boolean;
  success?: boolean;

  data?: any;
  paramExpList?: Array<ParamExp>;
  paramExpress?: string;
  exeExpList?: Array<ExeExp>;
  assign?: AssignExp[];
  request?: RequestExp;
  snippets?: string;

  children: Array<ActionItem>;
};

export type RequestExp = {
  /** 请求地址 */
  url: string;
  /** 请求方法，默认post */
  method?: string;
  /** 响应变量名称 */
  responseName?: string;
  /** 错误变量名称 */
  errorName?: string;
  /** Headers */
  headers?: Array<ParamExp>;
  /** Query */
  query?: Array<ParamExp>;
  /** Body */
  body?: Array<ParamExp>;
};

/** 赋值表达式 - 用于执行的表达式 */
export type AssignExp = {
  /** 声明部分, let / const */
  declaration?: string;

  /** 左侧变量或常量 - 只能选context.data */
  leftVal: string;

  /** 运算符 */
  operator: string;

  /** 右侧变量或常量 */
  rightVal: string;
};

/** 组装参数表达式 - 用于组装参数的表达式 */
export type ParamExp = {
  /**
   * 参数的属性名
   */
  prop: string;
  /**
   * 右边常量或变量
   */
  rightVal: any;
};

export enum ExeType {
  Log = 'log',
  Link = 'link',
  Dialog = 'dialog',
  Router = 'router',
  Notification = 'notification',
  Event = 'event',
  Component = 'comp',
  Confirm = 'confirm'
}

/** 执行方法的表达式 */
export type ExeExp = {
  /** 是否需要返回参数 */
  needReturn: boolean;

  /** 方法名 - 不需要括号。固定把组装的param作为入参 */
  methodName: string;

  /** 动作类型 */
  type?: ExeType;

  /** 页面id - router时传入 */
  pageid?: string;

  /** 对话框名称 - dialog时传入 */
  dialogName?: string;

  /** 路由Name - link时传入,路由Path和Name二选一 */
  routerName?: string;

  /** 路由Path - link时传入,路由Path和Name二选一 */
  routerPath?: string;
};

export type LogicExp = {
  /** 逻辑操作, let / const */
  logicOperator?: string;

  /** 左侧变量或常量 - 只能选context.data */
  leftVal: string;

  /** 操作符 != == > < */
  operator: string;

  /** 右侧变量或常量 */
  rightVal: string;
};
