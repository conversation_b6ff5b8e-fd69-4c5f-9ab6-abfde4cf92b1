import { ACTION, ACTION_TYPE } from '../enums.js';
import { ExeType } from './types';
import type { ActionItem, RequestExp, AssignExp, ParamExp, ExeExp, LogicExp } from './types';

export class Logic {
  actionItem?: ActionItem;

  /** 赋值表达式 - 用于执行的表达式 */
  assignExpList?: Array<AssignExp>;

  /** 组装参数表达式 - 用于组装参数的表达式 */
  paramExpList?: Array<ParamExp>;

  /** 组装参数表达式 - 用于组装参数的表达式 */
  paramExpress?: string;

  /** 逻辑表达式列表 - 用于if判断的逻辑表达式 */
  logicExpList?: Array<LogicExp>;

  /** 网络请求，存在网络请求时逻辑判断不生效 */
  request?: RequestExp;

  /** 自定义JS */
  snippets?: string;

  /** 执行方法的表达式，执行方法和嵌套逻辑只应存在一个，同时存在则执行方法 */
  exeExpList?: Array<ExeExp>;

  /** 成功或没有表达式执行 */
  success?: Logic;

  /** 失败时执行 */
  fail?: Logic;

  constructor(action: ActionItem) {
    this.actionItem = action;
    switch (action.type) {
      case ACTION_TYPE.ASSIGN:
        this.assignExpList = action.assign;
        break;
      case ACTION_TYPE.EXE:
        this.exeExpList = action.exeExpList ?? [];
        this.paramExpList = action.paramExpList ?? [];
        this.paramExpress = action.paramExpress;
        break;
      case ACTION_TYPE.REQUEST:
        this.request = action.request as RequestExp;
        break;
      case ACTION_TYPE.SNIPPET:
        this.snippets = action.snippets;
        break;
      default:
        break;
    }

    if (!!action.children && !!action.children.length) {
      this.success = action.children.filter(i => i.success).map(i => new Logic(i))[0];
      this.fail = action.children.filter(i => i.error).map(i => new Logic(i))[0];
    }
  }
}
