export const ACTION_TYPE = {
  // 赋值
  ASSIGN: 'ASSIGN_EXP',
  // 执行方法
  EXE: 'EXE_EXP',
  // 网络请求
  NET: 'NET_EXP',
  // 自定义JS
  SNIPPET: 'SNIPPET'
};

export const ACTION_GROUP = {
  app: { id: 'app', label: '应用动作' },
  page: { id: 'page', label: '页面交互' }
};

export const ACTION = {
  SET_STATE: { name: '表单赋值', code: 'setState', type: ACTION_TYPE.ASSIGN, group: ACTION_GROUP.app },
  NAVIGATE_TO: { name: '打开页面', code: 'navigateTo', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  NAVIGATE_BACK: { name: '返回上一页', code: 'navigateBack', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  RELAUNCH_HOME: { name: '返回首页', code: 'relaunchHome', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  SHOW_TOAST: { name: '显示消息提示', code: 'showToast', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  SHOW_LOADING: { name: '显示加载中', code: 'showLoading', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  HIDE_LOADING: { name: '隐藏加载中', code: 'hideLoading', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  SHOW_MODAL: { name: '打开弹窗', code: 'showModal', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  CLOSE_MODAL: { name: '关闭弹窗', code: 'closeModal', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  EMIT_EVENT: { name: '触发事件', code: 'emitEvent', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  VALIDATE_FORM: { name: '校验表单', code: 'validateForm', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  CALL_METHOD: { name: '调用方法', code: 'callMethod', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  SNIPPET: { name: '自定义JS', code: 'snippet', type: ACTION_TYPE.SNIPPET, group: ACTION_GROUP.app },
  // SHOW_POP_CONFIRM: { name: '气泡确认框', code: 'showPopConfirm', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  CONFIRM: { name: '全屏确认框', code: 'confirm', type: ACTION_TYPE.EXE, group: ACTION_GROUP.page },
  REQUEST_API: { name: '调用APIs', code: 'requestApi', type: ACTION_TYPE.NET, group: ACTION_GROUP.app }
};
