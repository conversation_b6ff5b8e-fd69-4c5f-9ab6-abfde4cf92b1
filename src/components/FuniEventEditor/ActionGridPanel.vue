<template>
  <div class="action-panel">
    <div class="action-panel__tips">
      <div class="action-panel__tips-img">
        <img src="./assets/arrow.svg" alt="" />
      </div>
      <div class="action-panel__tips-info">
        <p class="">请选择下方的执行动作</p>
        <p class="tea-text-weak">
          选择后将对该动作进行配置，当【输入改变
          <span style="color: rgb(151, 163, 183)">(change)</span>】应用将执行此动作
        </p>
      </div>
    </div>
    <div class="action-group" v-for="group in actionGroup" :key="group.id">
      <div class="action-group-title">{{ group.label }}</div>
      <div class="action-group-box">
        <el-button
          class="action-group-box__item"
          v-for="item in group.items"
          :key="item.code"
          @click="handleActionClick(item)"
        >
          {{ item.name }}<span class="action-group-box__item-code">({{ item.code }})</span>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ACTION } from './enums';

const actionGroup = Object.values($utils.groupBy(Object.values(ACTION), action => action.group.id)).reduce(
  (pre, cur) => {
    pre.push({ ...cur[0].group, items: cur });
    return pre;
  },
  []
);

const emit = defineEmits(['select']);

const handleActionClick = item => {
  emit('select', item);
};
</script>
<style lang="scss" scoped>
.action-panel__tips {
  display: flex;
  margin-bottom: 24px;

  p {
    margin: 0;
    color: #626364;
  }

  .tea-text-weak {
    color: #97a3b7 !important;
  }
}

.action-panel__tips-img {
  flex: 0 0 48px;
  margin-right: 8px;
}

.action-panel__tips-info {
  font-size: 12px;
  line-height: 20px;
}

.action-panel__tips-info p:first-child {
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
  margin-bottom: 4px;
}
.action-panel {
  overflow: auto;
  max-width: 600px;
  margin: auto;
  .action-group {
    margin-bottom: 22px;
    display: flex;
    flex-direction: column;

    &-title {
      font-size: 12px;
      line-height: 20px;
      color: #97a3b7;
      margin-bottom: 10px;
      font-weight: 400;
    }

    &-box {
      display: grid;
      grid-gap: 8px;
      grid-template-columns: 1fr 1fr 1fr;

      &__item {
        font-size: 12px;
        line-height: 18px;
        justify-content: flex-start;

        &-code {
          color: #97a3b7;
        }
      }

      :deep(.el-button + .el-button) {
        margin-left: 0;
      }
    }
  }
}
</style>
