import { h, resolveComponent, ComponentOptions } from 'vue';
import XEUtils from 'xe-utils';
import {
  VXETableCore,
  VxeTableDefines,
  VxeColumnPropTypes,
  VxeGlobalRendererHandles,
  VxeGlobalInterceptorHandles,
  FormItemRenderOptions,
  FormItemContentRenderParams
} from 'vxe-table';

function getComponentName(name: string) {
  return name.replace('funi', 'el');
}

function isEmptyValue(cellValue: any) {
  return cellValue === null || cellValue === undefined || cellValue === '';
}

function getOnName(type: string) {
  return 'on' + type.substring(0, 1).toLocaleUpperCase() + type.substring(1);
}

function getModelProp(renderOpts: VxeGlobalRendererHandles.RenderOptions) {
  return 'modelValue';
}

function getModelEvent(renderOpts: VxeGlobalRendererHandles.RenderOptions) {
  return 'update:modelValue';
}

function getChangeEvent(renderOpts: VxeGlobalRendererHandles.RenderOptions) {
  let type = 'change';
  switch (renderOpts.name) {
    case 'ElAutocomplete':
      type = 'select';
      break;
    case 'ElInput':
    case 'ElInputNumber':
      type = 'input';
      break;
  }
  return type;
}

function getCellEditFilterProps(
  renderOpts: VxeGlobalRendererHandles.RenderOptions,
  params: VxeGlobalRendererHandles.RenderEditParams | VxeGlobalRendererHandles.RenderFilterParams,
  value: any,
  defaultProps?: { [prop: string]: any }
) {
  return XEUtils.assign({}, defaultProps, renderOpts.props, { [getModelProp(renderOpts)]: value });
}

function getItemProps(
  renderOpts: VxeGlobalRendererHandles.RenderOptions,
  params: FormItemContentRenderParams,
  value: any,
  defaultProps?: { [prop: string]: any }
) {
  return XEUtils.assign({}, defaultProps, renderOpts.props, { [getModelProp(renderOpts)]: value });
}

function getOns(
  renderOpts: VxeGlobalRendererHandles.RenderOptions,
  params: VxeGlobalRendererHandles.RenderParams,
  inputFunc?: Function,
  changeFunc?: Function
) {
  const { events } = renderOpts;
  const modelEvent = getModelEvent(renderOpts);
  const changeEvent = getChangeEvent(renderOpts);
  const isSameEvent = changeEvent === modelEvent;
  const ons: { [type: string]: Function } = {};
  XEUtils.objectEach(events, (func: Function, key: string) => {
    ons[getOnName(key)] = function (...args: any[]) {
      func(params, ...args);
    };
  });
  if (inputFunc) {
    ons[getOnName(modelEvent)] = function (targetEvnt: any) {
      inputFunc(targetEvnt);
      if (events && events[modelEvent]) {
        events[modelEvent](params, targetEvnt);
      }
      if (isSameEvent && changeFunc) {
        changeFunc(targetEvnt);
      }
    };
  }
  if (!isSameEvent && changeFunc) {
    ons[getOnName(changeEvent)] = function (...args: any[]) {
      changeFunc(...args);
      if (events && events[changeEvent]) {
        events[changeEvent](params, ...args);
      }
    };
  }
  return ons;
}

function getEditOns(
  renderOpts: VxeGlobalRendererHandles.RenderOptions,
  params: VxeGlobalRendererHandles.RenderEditParams
) {
  const { $table, row, column } = params;
  return getOns(
    renderOpts,
    params,
    (value: any) => {
      // 处理 model 值双向绑定
      XEUtils.set(row, column.field, value);
    },
    () => {
      // 处理 change 事件相关逻辑
      $table.updateStatus(params);
    }
  );
}

function getFilterOns(
  renderOpts: VxeGlobalRendererHandles.RenderOptions,
  params: VxeGlobalRendererHandles.RenderFilterParams,
  option: VxeTableDefines.FilterOption,
  changeFunc: Function
) {
  return getOns(
    renderOpts,
    params,
    (value: any) => {
      // 处理 model 值双向绑定
      option.data = value;
    },
    changeFunc
  );
}

function getItemOns(renderOpts: VxeGlobalRendererHandles.RenderOptions, params: FormItemContentRenderParams) {
  const { $form, data, field } = params;
  return getOns(
    renderOpts,
    params,
    (value: any) => {
      // 处理 model 值双向绑定
      XEUtils.set(data, field, value);
    },
    () => {
      // 处理 change 事件相关逻辑
      $form.updateStatus(params);
    }
  );
}

function getSelectCellValue(
  renderOpts: VxeColumnPropTypes.EditRender,
  params: VxeGlobalRendererHandles.RenderCellParams
) {
  const { options = [], optionGroups, props = {}, optionProps = {}, optionGroupProps = {} } = renderOpts;
  const { $table, rowid, row, column } = params;
  const { filterable, multiple } = props;
  const labelProp = optionProps.label || 'label';
  const valueProp = optionProps.value || 'value';
  const groupOptions = optionGroupProps.options || 'options';
  const cellValue = XEUtils.get(row, column.field);
  const colid = column.id;
  let cellData: any;
  if (filterable) {
    const { internalData } = $table;
    const { fullAllDataRowIdData } = internalData;
    const rest: any = fullAllDataRowIdData[rowid];
    if (rest) {
      cellData = rest.cellData;
      if (!cellData) {
        cellData = rest.cellData = {};
      }
    }
    if (rest && cellData[colid] && cellData[colid].value === cellValue) {
      return cellData[colid].label;
    }
  }
  if (!isEmptyValue(cellValue)) {
    const selectlabel = XEUtils.map(
      multiple ? cellValue : [cellValue],
      optionGroups
        ? value => {
            let selectItem: any;
            for (let index = 0; index < optionGroups.length; index++) {
              selectItem = XEUtils.find(optionGroups[index][groupOptions], item => item[valueProp] === value);
              if (selectItem) {
                break;
              }
            }
            return selectItem ? selectItem[labelProp] : value;
          }
        : value => {
            const selectItem = XEUtils.find(options, item => item[valueProp] === value);
            return selectItem ? selectItem[labelProp] : value;
          }
    ).join(', ');
    if (cellData && options && options.length) {
      cellData[colid] = { value: cellValue, label: selectlabel };
    }
    return selectlabel;
  }
  return '';
}

function createEditRender(defaultProps?: { [key: string]: any }) {
  return function (
    renderOpts: VxeColumnPropTypes.EditRender & { name: string },
    params: VxeGlobalRendererHandles.RenderEditParams
  ) {
    const { row, column } = params;
    const { name, attrs } = renderOpts;
    const cellValue = XEUtils.get(row, column.field);
    return [
      h(resolveComponent(getComponentName(name)), {
        ...attrs,
        ...getCellEditFilterProps(renderOpts, params, cellValue, defaultProps),
        ...getEditOns(renderOpts, params)
      })
    ];
  };
}

function createFilterRender(defaultProps?: { [key: string]: any }) {
  return function (
    renderOpts: VxeColumnPropTypes.FilterRender & { name: string },
    params: VxeGlobalRendererHandles.RenderFilterParams
  ) {
    const { column } = params;
    const { name, attrs } = renderOpts;
    return [
      h(
        'div',
        {
          class: 'vxe-table--filter-element-wrapper'
        },
        column.filters.map((option, oIndex) => {
          const optionValue = option.data;
          return h(resolveComponent(getComponentName(name)), {
            key: oIndex,
            ...attrs,
            ...getCellEditFilterProps(renderOpts, params, optionValue, defaultProps),
            ...getFilterOns(renderOpts, params, option, () => {
              // 处理 change 事件相关逻辑
              handleConfirmFilter(params, !!option.data, option);
            })
          });
        })
      )
    ];
  };
}

function handleConfirmFilter(
  params: VxeGlobalRendererHandles.RenderFilterParams,
  checked: boolean,
  option: VxeTableDefines.FilterOption
) {
  const { $panel } = params;
  $panel.changeOption(null, checked, option);
}

/**
 * 模糊匹配
 * @param params
 */
function defaultFuzzyFilterMethod(params: VxeGlobalRendererHandles.FilterMethodParams) {
  const { option, row, column } = params;
  const { data } = option;
  const cellValue = XEUtils.get(row, column.field);
  return XEUtils.toValueString(cellValue).indexOf(data) > -1;
}

function renderOptions(options: any[], optionProps: VxeGlobalRendererHandles.RenderOptionProps) {
  const labelProp = optionProps.label || 'label';
  const valueProp = optionProps.value || 'value';
  return XEUtils.map(options, (item, oIndex) => {
    return h(resolveComponent('el-option'), {
      key: oIndex,
      value: item[valueProp],
      label: item[labelProp],
      disabled: item.disabled
    });
  });
}

function createFormItemRender(defaultProps?: { [key: string]: any }) {
  return function (renderOpts: FormItemRenderOptions & { name: string }, params: FormItemContentRenderParams) {
    const { data, field } = params;
    const { name } = renderOpts;
    const { attrs } = renderOpts;
    const itemValue = XEUtils.get(data, field);
    return [
      h(resolveComponent(getComponentName(name)), {
        ...attrs,
        ...getItemProps(renderOpts, params, itemValue, defaultProps),
        ...getItemOns(renderOpts, params)
      })
    ];
  };
}

function createExportMethod(getExportCellValue: Function) {
  return function (params: VxeGlobalRendererHandles.ExportMethodParams) {
    const { row, column, options } = params;
    return options && options.original
      ? XEUtils.get(row, column.field)
      : getExportCellValue(column.editRender || column.cellRender, params);
  };
}

/**
 * 检查触发源是否属于目标节点
 */
function getEventTargetNode(evnt: any, container: HTMLElement, className: string) {
  let targetElem;
  let target = evnt.target;
  while (target && target.nodeType && target !== document) {
    if (
      className &&
      target.className &&
      target.className.split &&
      target.className.split(' ').indexOf(className) > -1
    ) {
      targetElem = target;
    } else if (target === container) {
      return { flag: className ? !!targetElem : true, container, targetElem: targetElem };
    }
    target = target.parentNode;
  }
  return { flag: false };
}

/**
 * 事件兼容性处理
 */
function handleClearEvent(
  params:
    | VxeGlobalInterceptorHandles.InterceptorClearFilterParams
    | VxeGlobalInterceptorHandles.InterceptorClearActivedParams
    | VxeGlobalInterceptorHandles.InterceptorClearAreasParams
) {
  const { $event } = params;
  const bodyElem = document.body;
  if (
    // 远程搜索
    getEventTargetNode($event, bodyElem, 'el-autocomplete-suggestion').flag ||
    // 下拉框
    getEventTargetNode($event, bodyElem, 'el-select-dropdown').flag ||
    // 级联
    getEventTargetNode($event, bodyElem, 'el-cascader__dropdown').flag ||
    getEventTargetNode($event, bodyElem, 'el-cascader-menus').flag ||
    // 日期
    getEventTargetNode($event, bodyElem, 'el-time-panel').flag ||
    getEventTargetNode($event, bodyElem, 'el-picker-panel').flag ||
    // 颜色
    getEventTargetNode($event, bodyElem, 'el-color-dropdown').flag
  ) {
    return false;
  }
}

/**
 * 基于 vxe-table 表格的适配插件，用于兼容 element-ui 组件库
 */
export const VXETablePluginElement = {
  install(vxetablecore: VXETableCore) {
    const { interceptor, renderer } = vxetablecore;

    const renderers = {
      'funi-input': {
        autofocus: 'input.funi-input__inner',
        renderDefault: createEditRender(),
        renderEdit: createEditRender(),
        renderCell: createEditRender(),
        renderFilter: createFilterRender(),
        defaultFilterMethod: defaultFuzzyFilterMethod,
        renderItemContent: createFormItemRender()
      },
      'funi-select': {
        renderEdit(renderOpts, params) {
          const { options = [], optionGroups, optionProps = {}, optionGroupProps = {} } = renderOpts;
          const { row, column } = params;
          const { attrs } = renderOpts;
          const cellValue = XEUtils.get(row, column.field);
          const props = getCellEditFilterProps(renderOpts, params, cellValue);
          const ons = getEditOns(renderOpts, params);
          if (optionGroups) {
            const groupOptions = optionGroupProps.options || 'options';
            const groupLabel = optionGroupProps.label || 'label';
            return [
              h(
                resolveComponent('el-select') as ComponentOptions,
                {
                  ...attrs,
                  ...props,
                  ...ons
                },
                {
                  default: () => {
                    return XEUtils.map(optionGroups, (group, gIndex) => {
                      return h(
                        resolveComponent('el-option-group') as ComponentOptions,
                        {
                          key: gIndex,
                          label: group[groupLabel]
                        },
                        {
                          default: () => renderOptions(group[groupOptions], optionProps)
                        }
                      );
                    });
                  }
                }
              )
            ];
          }
          return [
            h(
              resolveComponent('el-select') as ComponentOptions,
              {
                ...props,
                ...attrs,
                ...ons
              },
              {
                default: () => renderOptions(options, optionProps)
              }
            )
          ];
        },
        renderCell(renderOpts, params) {
          // return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));
          const { options = [], optionGroups, optionProps = {}, optionGroupProps = {} } = renderOpts;
          const { row, column } = params;
          const { attrs } = renderOpts;
          const cellValue = XEUtils.get(row, column.field);
          const props = getCellEditFilterProps(renderOpts, params, cellValue);
          const ons = getEditOns(renderOpts, params);
          if (optionGroups) {
            const groupOptions = optionGroupProps.options || 'options';
            const groupLabel = optionGroupProps.label || 'label';
            return [
              h(
                resolveComponent('el-select') as ComponentOptions,
                {
                  ...attrs,
                  ...props,
                  ...ons
                },
                {
                  default: () => {
                    return XEUtils.map(optionGroups, (group, gIndex) => {
                      return h(
                        resolveComponent('el-option-group') as ComponentOptions,
                        {
                          key: gIndex,
                          label: group[groupLabel]
                        },
                        {
                          default: () => renderOptions(group[groupOptions], optionProps)
                        }
                      );
                    });
                  }
                }
              )
            ];
          }
          return [
            h(
              resolveComponent('el-select') as ComponentOptions,
              {
                ...props,
                ...attrs,
                ...ons
              },
              {
                default: () => renderOptions(options, optionProps)
              }
            )
          ];
        },
        renderFilter(renderOpts, params) {
          const { options = [], optionGroups, optionProps = {}, optionGroupProps = {} } = renderOpts;
          const groupOptions = optionGroupProps.options || 'options';
          const groupLabel = optionGroupProps.label || 'label';
          const { column } = params;
          const { attrs } = renderOpts;
          return [
            h(
              'div',
              {
                class: 'vxe-table--filter-element-wrapper'
              },
              optionGroups
                ? column.filters.map((option, oIndex) => {
                    const optionValue = option.data;
                    const props = getCellEditFilterProps(renderOpts, params, optionValue);
                    return h(
                      resolveComponent('el-select') as ComponentOptions,
                      {
                        key: oIndex,
                        ...attrs,
                        ...props,
                        ...getFilterOns(renderOpts, params, option, () => {
                          // 处理 change 事件相关逻辑
                          handleConfirmFilter(
                            params,
                            props.multiple ? option.data && option.data.length > 0 : !XEUtils.eqNull(option.data),
                            option
                          );
                        })
                      },
                      {
                        default: () => {
                          return XEUtils.map(optionGroups, (group, gIndex) => {
                            return h(
                              resolveComponent('el-option-group') as ComponentOptions,
                              {
                                key: gIndex,
                                label: group[groupLabel]
                              },
                              {
                                default: () => renderOptions(group[groupOptions], optionProps)
                              }
                            );
                          });
                        }
                      }
                    );
                  })
                : column.filters.map((option, oIndex) => {
                    const optionValue = option.data;
                    const props = getCellEditFilterProps(renderOpts, params, optionValue);
                    return h(
                      resolveComponent('el-select') as ComponentOptions,
                      {
                        key: oIndex,
                        ...attrs,
                        ...props,
                        ...getFilterOns(renderOpts, params, option, () => {
                          // 处理 change 事件相关逻辑
                          handleConfirmFilter(
                            params,
                            props.multiple ? option.data && option.data.length > 0 : !XEUtils.eqNull(option.data),
                            option
                          );
                        })
                      },
                      {
                        default: () => renderOptions(options, optionProps)
                      }
                    );
                  })
            )
          ];
        },
        defaultFilterMethod(params) {
          const { option, row, column } = params;
          const { data } = option;
          const { field, filterRender: renderOpts } = column;
          const { props = {} } = renderOpts;
          const cellValue = XEUtils.get(row, field);
          if (props.multiple) {
            if (XEUtils.isArray(cellValue)) {
              return XEUtils.includeArrays(cellValue, data);
            }
            return data.indexOf(cellValue) > -1;
          }
          /* eslint-disable eqeqeq */
          return cellValue == data;
        },
        renderItemContent(renderOpts, params) {
          const { options = [], optionGroups, optionProps = {}, optionGroupProps = {} } = renderOpts;
          const { data, field } = params;
          const { attrs } = renderOpts;
          const itemValue = XEUtils.get(data, field);
          const props = getItemProps(renderOpts, params, itemValue);
          const ons = getItemOns(renderOpts, params);
          if (optionGroups) {
            const groupOptions = optionGroupProps.options || 'options';
            const groupLabel = optionGroupProps.label || 'label';
            return [
              h(
                resolveComponent('el-select') as ComponentOptions,
                {
                  ...attrs,
                  ...props,
                  ...ons
                },
                {
                  default: () => {
                    return XEUtils.map(optionGroups, (group, gIndex) => {
                      return h(
                        resolveComponent('el-option-group') as ComponentOptions,
                        {
                          label: group[groupLabel],
                          key: gIndex
                        },
                        {
                          default: () => renderOptions(group[groupOptions], optionProps)
                        }
                      );
                    });
                  }
                }
              )
            ];
          }
          return [
            h(
              resolveComponent('el-select') as ComponentOptions,
              {
                ...attrs,
                ...props,
                ...ons
              },
              {
                default: () => renderOptions(options, optionProps)
              }
            )
          ];
        },
        exportMethod: createExportMethod(getSelectCellValue)
      }
    };
    Object.entries(renderers).forEach(([key, value]) => renderer.add(key, value));

    interceptor.add('event.clearFilter', handleClearEvent);
    interceptor.add('event.clearActived', handleClearEvent);
    interceptor.add('event.clearAreas', handleClearEvent);
  }
};

export default VXETablePluginElement;
