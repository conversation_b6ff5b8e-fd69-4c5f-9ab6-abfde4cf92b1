import { XYZ } from 'ol/source';
import { Tile } from 'ol/layer.js';

/**
 * 天地图密钥
 */
const key = '017dbcb2c2f1f366b95308d3390244a6';

export default function () {
  /**
   * 天地图影像图层
   */
  const tdtImgLayer = new Tile({
    visible: false,
    source: new XYZ({
      url: `//t{0-7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${key}`
    }),
    name: '天地图影像图层',
    id:"_baseMap_tdtImgLayer",
    _type:"img",
  });
  /**
   * 天地图影像标注图层
   */
  const tdtImgLayerText = new Tile({
    visible: false,
    source: new XYZ({
      url: `//t{0-7}.tianditu.gov.cn/DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=${key}`
    }),
    name: '天地图影像图层标注',
    id:"_baseMap_tdtImgLayerText",
    _type:"text"
  });
  /**
   * 天地图矢量图层
   */
  const tdtVecLayer = new Tile({
    visible: false,
    source: new XYZ({
      url: `//t{0-7}.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=${key}`
    }),
    name: '天地图矢量图层',
    id:"_baseMap_tdtVecLayer",
    _type:"img"
  });
  /**
   * 天地图矢量图层标注
   */
  const tdtVecLayerText = new Tile({
    visible: false,
    source: new XYZ({
      url: `//t{0-7}.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=${key}`
    }),
    name: '天地图矢量图层标注',
    id:"_baseMap_tdtVecLayerText",
    _type:"text"
  });
  return [tdtImgLayer, tdtImgLayerText, tdtVecLayer, tdtVecLayerText ];
}
