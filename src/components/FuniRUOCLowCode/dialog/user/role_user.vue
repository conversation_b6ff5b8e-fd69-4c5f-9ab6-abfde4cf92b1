<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-23 11:17:47
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-02 16:19:45
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCode/dialog/user/role_user.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="banner">
    <div class="roleList" v-loading="loadingRole">
      <el-checkbox-group v-model="roleSelectId" :validate-event="false">
        <template v-for="(item, index) in roleList" :key="item.id">
          <el-checkbox v-if="!item.disabled" :id="item.id" :label="item.id" :validate-event="false">
            <div class="role-item" @click.stop="setModel(item)">
              <span>{{ item.roleName }}</span>
            </div>
          </el-checkbox>
        </template>
      </el-checkbox-group>
    </div>
    <div class="userList" v-loading="loadingUser">
      <div class="userSearch">
        <el-form validate-status="success">
          <el-input
            placeholder="人员搜索"
            clearable
            v-model="keyword"
            @input="iptChange"
            :suffix-icon="Search"
          ></el-input>
        </el-form>
      </div>
      <div class="userListBox">
        <el-checkbox-group v-model="userSelectId" @change="checkedChange" :validate-event="false">
          <template v-for="(item, index) in userList" :key="item.id">
            <el-checkbox v-show="showFunc(item)" :id="item.id" :label="item.id" :validate-event="false">
              <div class="user-item">
                <span>{{ item.nickName }}</span>
              </div>
            </el-checkbox>
          </template>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, unref, watch, nextTick, computed } from 'vue';
import { getRoleList, getUserList } from './hooks/roleUser';
import { Search } from '@element-plus/icons-vue';
const props = defineProps({
  allList: {
    type: Array,
    default: () => []
  },
  valueList: {
    type: Array,
    default: () => []
  },
  propsConfig: Object
});
const emit = defineEmits(['setallList', 'setValue']);

const loadingRole = ref(false);
const loadingUser = ref(false);
const roleList = ref([]);
const userList = ref([]);
const userSelectId = ref([]);
const roleSelectId = ref([]);
const keyword = ref('');
const searchName = ref('');
let timer = null;

watch(
  () => props.valueList,
  newVal => {
    userSelectId.value = newVal.map(item => item.id);
  },
  {
    deep: true,
    immediate: true
  }
);

const setAllUser = list => {
  emit('setallList', list);
};
const checkedChange = async (e, a) => {
  await nextTick();
  if (mode.value !== 'multiple' && e && e.length) {
    userSelectId.value = [e.pop()];
  }
  emit('setValue', userSelectId.value);
};

const mode = computed(() => {
  return props?.propsConfig?.mode;
});

const setModel = async e => {
  await nextTick();
  userList.value = await getUserList({
    loadingUser,
    roleId: e.id,
    allList: props.allList,
    callback: setAllUser
  });
  roleSelectId.value = [e.id];
};

const iptChange = e => {
  clearTimeout(timer);
  timer = setTimeout(() => {
    clearTimeout(timer);
    searchName.value = keyword.value;
  }, 600);
};
const showFunc = item => {
  let key = searchName.value;
  if (key === '' || key === null || key === void 0) {
    return true;
  }
  let i = item.nickName.indexOf(searchName.value);
  let flag = i > -1;
  return flag;
};

onMounted(async () => {
  roleList.value = await getRoleList({ loadingRole, allowedRole: props.propsConfig.allowedRole });
  await nextTick();
  setModel(roleList.value[0]);
});
</script>

<style scoped>
@import url('./../../style/banner.css');
@import url('./../../style/checkbox.css');
@import url('./../../style/userList.css');
.roleList {
  border-right: 1px solid var(--el-border-color);
}

.roleList {
  width: 50%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}

.custom-tree-node {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 3px;
}

.role-item {
  box-sizing: border-box;
  padding: 0 8px;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
}
:deep(.el-checkbox.is-checked) .role-item {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-checkbox-text-color);
}

.roleList :deep(.el-checkbox__input) {
  display: none;
}
</style>
