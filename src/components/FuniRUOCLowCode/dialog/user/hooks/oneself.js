/*
 * @Author: co<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-23 17:43:07
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2025-01-15 15:31:58
 * @FilePath: /src/components/FuniRUOCLowCode/dialog/user/hooks/oneself.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { findCurrentUser } from '@/apis/app/bpaas';
export const getUserInfo = async ({ loading, allList = [], callback = () => {} }, allowedSelf = []) => {
  loading && (loading.value = true);
  let data = await findCurrentUser().finally(() => {
    loading && (loading.value = false);
  });
  let arr = [
    {
      id: data.id,
      nickName: data.username
    }
  ];
  let list = allList;
  arr.forEach(item => {
    let flag = allList.findIndex(ele => ele.id === item.id);
    if (flag < 0) {
      list.push(item);
    }
  });
  callback(list);
  return arr;
};
