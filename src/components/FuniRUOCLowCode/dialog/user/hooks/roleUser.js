/*
 * @Author: co<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-23 16:27:50
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2024-02-04 17:58:58
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniRUOCLowCode\dialog\user\hooks\roleUser.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const getRoleList = async params => {
  params.loadingRole.value = true;
  let { list } = await $http.post('/csccs/roleList/roleAllList', { flag: false }).finally(() => {
    params.loadingRole.value = false;
  });

  return list.map(item => {
    let index = params.allowedRole.findIndex(el => el === item.id);
    return {
      ...item,
      disabled: index < 0
    };
  });
};

export const getUserList = async params => {
  params.loadingUser.value = true;
  let { list: arr } = await $http
    .post('/csccs/roleList/getAccountById', {
      roleId: params.roleId,
      flag: false
    })
    .finally(() => {
      params.loadingUser.value = false;
    });
  arr = arr.map(item => {
    return {
      ...item,
      nickName: item.nickname,
      id: item.accountId
    };
  });
  let list = params.allList;
  arr.forEach(item => {
    let flag = params.allList.findIndex(ele => ele.id === item.id);
    if (flag < 0) {
      list.push(item);
    }
  });
  params.callback(list);

  return arr;
};
