<!--
 * @Author: co<PERSON>nh<PERSON> <EMAIL>
 * @Date: 2023-08-23 11:07:51
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-12-29 13:37:46
 * @FilePath: \funi-paas-cs-web-cli\src\components\FuniRUOCLowCode\dialog\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <funi-dialog v-model="showDialog"
    :title="title"
    append-to-body
    @confirm="handleConfirm">
    <div class="ruoc-body"
      :style="{
        '--valueBoxHeight': valueBoxHeight + 'px'
      }">
      <div class="ruoc-value_box"
        ref="ruocBox"
        v-if="propsConfig.mode === 'multiple'">
        <div v-for="(item, index) in valueList"
          :key="item.id"
          class="itemBlock">
          <span class="item-icon"> <funi-icon :icon="iconClass[item.type]" /> </span>
          <span>{{ item.name }}</span>
          <span class="delItem"
            @click="delItem(item)">
            <funi-icon icon="iconamoon:close-thin" />
          </span>
        </div>
      </div>
      <div class="operation"
        data-key="operation">
        <el-tabs v-model="activeNameVal">
          <el-tab-pane v-for="(item, index) in tabsList"
            :label="item.title"
            :name="item.name">
            <component :is="item.tag"
              :propsConfig="propsConfig"
              :valueList="valueList"
              :allList="allList"
              @setValue="setValue"
              @setallList="setallList" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </funi-dialog>
</template>
<script setup>
import { ref, computed, nextTick, watch } from 'vue';

import { iconClass, getTabs } from './../hooks/config.js';
const props = defineProps({
  value: {
    type: Array,
    default: []
  },
  propsConfig: {
    type: Object,
    default: {}
  }
});
const ruocBox = ref();
const valueBoxHeight = ref(0);
const valueList = ref([]);
const emit = defineEmits(['updateValue']);
const title = ref('');
const activeNameVal = ref('');
const showDialog = ref(false);
const allList = ref([]);
const keyName = computed(() => {
  return props.propsConfig.type === 'user' ? 'nickName' : props.propsConfig.type === 'org' ? 'orgName' : 'name';
});

const show = async params => {
  showDialog.value = true;
  title.value = params?.title;
  valueList.value = props.value;
  activeNameVal.value = tabsList.value[0].name;
  allList.value = valueList.value.map(item => {
    return {
      ...item,
      [keyName.value]: item.name
    };
  });
  await nextTick();
  valueBoxHeight.value = ruocBox.value ? ruocBox.value.offsetHeight : 0;
};

const setallList = list => {
  allList.value = list;
};

const delItem = e => {
  let index = valueList.value.findIndex(el => el.id === e.id);
  valueList.value.splice(index, 1);
};

const setValue = e => {
  valueList.value = e.map(item => {
    let data = allList.value.filter(el => el.id === item);
    return {
      type: props.propsConfig.type,
      id: data[0].id,
      name: data[0][keyName.value]
    };
  });
};
const handleConfirm = () => {
  emit('updateValue', valueList.value);
  showDialog.value = false;
};

const tabsList = computed(() => {
  return getTabs(props.propsConfig);
});
watch(
  () => tabsList,
  newVal => {
    if (newVal && newVal.value.length) activeNameVal.value = newVal.value[0].name;
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => props.value,
  newVal => {
    valueList.value = newVal;
  },
  {
    immediate: true,
    deep: true
  }
);
defineExpose({
  show
});
</script>
<style scoped>
@import url('./../style/itemBlock.css');
.ruoc-body {
  height: 50vh;
}

.ruoc-value_box {
  width: 100%;
  height: 60px;
  /* max-height: 150px; */
  border: 1px solid rgba(247, 247, 250, 1);
  padding: 5px;
  border-radius: 5px;
  display: flex;
  justify-content: flex-start;
  align-content: flex-start;
  flex-wrap: wrap;
  gap: 10px;
  overflow: hidden;
  overflow-y: auto;
}
.operation {
  height: calc(100% - var(--valueBoxHeight));
  overflow: hidden;
}
:deep(.el-tabs__content) {
  height: calc(100% - 55px);
}
:deep(.el-tab-pane),
:deep(.el-tabs) {
  height: 100%;
  overflow: hidden;
}
</style>
