/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-24 09:44:11
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-02 13:33:14
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCode/dialog/org/hooks/orgHooks.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
export const getOrgTree = async (params = {}) => {
    params.loadingOrg.value = true;
    let { list } = await $http.fetch('/csccs/orgList/orgTree', {}).finally(() => {
        params.loadingOrg.value = false;
    });
    const setTreeProperty = (tree) => {
        if (Array.isArray(tree)) {
            return tree.map(node => {
                let index = params.allowedOrg.findIndex(item => item === node.id)
                const newNode = { ...node, disabled: index < 0 };
                if (node.children) {
                    newNode.children = setTreeProperty(node.children);
                }
                return newNode;
            });
        } else if (typeof tree === 'object') {
            let index = params.allowedOrg.findIndex(item => item === tree[orgProps.id])
            const newNode = { ...tree, disabled: index < 0 };
            if (tree.children) {
                newNode.children = setTreeProperty(tree.children);
            }
            return newNode;
        }
    }
    return filterDisabledNodes(setTreeProperty(list))
}


// 过滤 disabled
function filterDisabledNodes(tree) {
    if (!Array.isArray(tree)) {
        return [];
    }
    const filteredTree = [];
    for (const node of tree) {
        if (node.disabled === false || !node.hasOwnProperty('disabled')) {
            // 如果节点的disabled属性为false或不存在，保留该节点
            filteredTree.push({
                ...node,
                children: filterDisabledNodes(node.children || [])
            });
        } else {
            // 如果节点的disabled属性为true，跳过该节点，但保留其子节点
            filteredTree.push(...filterDisabledNodes(node.children || []));
        }
    }

    return filteredTree;
}


export const getAllNode = org_list => {
    let list = [];
    const recursion = arr => {
        for (let i = 0; i < arr.length; i++) {
            list.push(arr[i]);
            if (arr[i].children && arr[i].children.length > 0) {
                recursion(arr[i].children);
            }
        }
    };
    recursion(org_list);
    return list;
};