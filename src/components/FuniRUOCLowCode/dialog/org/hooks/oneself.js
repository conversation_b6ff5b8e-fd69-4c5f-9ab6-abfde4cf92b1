/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-23 17:43:07
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-24 15:00:30
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCodeV2/dialog/org/hooks/oneself.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
export const getUserOrgInfo = async ({ loading, allList = [], callback = () => { } }) => {
    loading && (loading.value = true)
    let { unit } = await $http.post('/cscas/auth/getCurrentUser').finally(() => {
        loading && (loading.value = false)
    });
    let arr = []
    if (unit && unit.length) {
        unit.forEach(item => {
            arr.push({
                id: item.id,
                orgName: item.name,
            })
        })
    } else if (unit && Object.keys(unit).length) {
        arr = [{
            id: unit.id,
            orgName: unit.name,
        }]
    }

    let list = allList
    arr.forEach(item => {
        let flag = allList.findIndex(ele => ele.id === item.id);
        if (flag < 0) {
            list.push(item);
        }
    });
    callback(list)
    return arr
};