/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-06 20:27:10
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-20 15:42:30
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRegion/useRequest.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */

/**
 * @description 请求数据
 * @param { {} } params 请求配置数据
 * @param { {} } args 请求参数
 * @param { ()=>{} } callback 回调参数
 * **/
export const getRegionList = async (params, args = {}, config = {}, callback) => {
  let rn = params.returnName;
  let data = await $http[params.method](params.url, args, config);
  let list = rn ? data[rn] : data;
  callback &&
    callback(
      list.map(item => {
        return {
          ...item,
          code: item[params.defaultProps.code],
          name: item[params.defaultProps.name]
        };
      })
    );
};
