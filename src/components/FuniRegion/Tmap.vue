<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-07 16:14:40
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-20 16:02:40
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRegion/Tmap.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <funi-dialog title="点位标记" append-to-body size="large" v-model="dialogVisible" @confirm="onConfirm">
    <div class="infoAddress">
      <div>
        {{ formattedAddress }}
      </div>
      <div>
        {{ regionMap?.location ? regionMap?.location.join(',') : '' }}
      </div>
    </div>
    <div :id="id" class="tmap">
      <div class="search-input">
        <el-select
          v-model="addressValue"
          remote
          filterable
          reserve-keyword
          placeholder="请输入"
          :remote-method="querySearchAsync"
        >
          <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </div>
    <div :id="aid" style="display: none"></div>
  </funi-dialog>
</template>
<script setup lang="jsx">
import { ref, onMounted, watch, nextTick, computed, unref } from 'vue';
import {
  getCurrentPosition,
  setGeocoder,
  poiSearch,
  convertToGCJ02,
  getAddress,
  getParentCodes,
  padZeros
} from './amap_utils.js';
import { gcj02towgs84 } from './utils.js';

const id = 'tmap_' + (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
const aid = 'amap_' + (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
const state = ref('');
let centre = ref([]);
let province = ref('');
const autocomplete = ref();
const options = ref([]);
const addressValue = ref(void 0);
let map;
let zoom = 12;
let amap;
const dialogVisible = ref(false);
const regionMap = ref({});
const firstFlag = ref(false);
const emit = defineEmits(['setRegionMap']);

const formattedAddress = computed(() => {
  let str = '';
  let sort = ['province', 'city', 'district', 'community', 'street', 'addressFull'];
  for (let index in sort) {
    if (sort[index] !== 'addressFull' && regionMap.value[sort[index]]?.label) {
      str += regionMap.value[sort[index]]?.label ? regionMap.value[sort[index]]?.label : '';
    } else if (sort[index] === 'addressFull' && regionMap.value['addressFull']) {
      str += regionMap.value[sort[index]] ? regionMap.value[sort[index]] : '';
    }
  }
  return str;
});

const init = async region => {
  dialogVisible.value = true;
  await nextTick();
  regionMap.value = JSON.parse(JSON.stringify(region));
  let sort = ['province', 'city', 'district', 'community', 'street', 'addressFull'];
  state.value = '';
  for (let index in sort) {
    if (sort[index] !== 'addressFull' && region[sort[index]]?.label) {
      state.value += region[sort[index]]?.label ? region[sort[index]]?.label : '';
    } else if (sort[index] === 'addressFull' && region['addressFull']) {
      state.value += region[sort[index]] ? region[sort[index]] : '';
    }
    addressValue.value = void 0;
  }

  let cityName = regionMap.value.city?.label || regionMap.value.province?.label || '';
  amap = new AMap.Map(aid, { city: cityName });
  centre.value = amap.getCenter() ? [amap.getCenter().lng, amap.getCenter().lat] : [116.40769, 39.89945];
  amap.getCity(info => {
    province.value = info.province;
    setGeocoder(info.province);
  });
  let location = gcj02towgs84(centre.value[0], centre.value[1]);
  map = new T.Map(id);
  map.centerAndZoom(new T.LngLat(location[0], location[1]), zoom);
  if (state.value && (!regionMap.value?.location || !regionMap.value?.location?.length)) {
    querySearchAsync(state.value, true);
  } else {
    centre.value = regionMap.value.location;
    if (centre.value) {
      map.centerAndZoom(new T.LngLat(centre.value[0], centre.value[1]), zoom);
      setMarker([{ centre: centre.value }]);
      amapGetAddress();
    }
  }
};

/**
 * @description 搜索功能
 * **/
const querySearchAsync = (queryString, bool) => {
  if (queryString !== '' && queryString !== void 0 && queryString !== null) {
    poiSearch(queryString, province.value, results => {
      options.value = results || [];
      if (bool === true && results && results.length) {
        firstFlag.value = true;
        addressValue.value = results[0].id;
        centre.value = gcj02towgs84(results[0].location.lng, results[0].location.lat);
      }
      map.centerAndZoom(new T.LngLat(centre.value[0], centre.value[1]), zoom);
      setMarker([{ centre: centre.value }]);
    });
  }
};

/**
 * @description 天地图设置maker
 * **/

const setMarker = list => {
  var newMarker = map.getOverlays();
  for (var n = 0; n < newMarker.length; n++) {
    map.removeOverLay(newMarker[n]);
  }
  let markers = list.map(item => {
    return new T.Marker(new T.LngLat(item.centre[0], item.centre[1]));
  });
  markers.map(item => {
    map.addOverLay(item);
    item.enableDragging();
    item.addEventListener('mouseup', tdMouseup);
  });
};

/**
 * @description 天地图拖拽鼠标公开事件
 * **/
const tdMouseup = e => {
  centre.value = [e.lnglat.lng, e.lnglat.lat];
  amapGetAddress(true);
};

/**
 * @description 坐标逆向获取地址
 * @param {true | false} bool 是否拖拽
 * **/
const amapGetAddress = bool => {
  convertToGCJ02({ lng: centre.value[0], lat: centre.value[1] }, v => {
    getAddress({ lng: v.lng, lat: v.lat }, e => {
      if (!firstFlag.value) {
        const subRegionCode = e.towncode; // 给定的行政编码
        const parentCodes = getParentCodes(subRegionCode);
        let list = ['province', 'city', 'district'];
        for (let i = 0; i < parentCodes.length; i++) {
          const completeParentCode = padZeros(parentCodes[i], 12);

          regionMap.value[list[i]] = {
            label: e[list[i]],
            value: completeParentCode
          };
        }
        regionMap.value.community = { label: e.township, value: e.towncode };
        regionMap.value.street = { label: e.township, value: e.towncode };
        let str = e.township || e.district || e.city || '';
        regionMap.value.addressFull = e.formattedAddress.split(str)[1];
        regionMap.value.location = gcj02towgs84(e.lng, e.lat);
      }

      firstFlag.value = false;
      bool && (addressValue.value = void 0);
    });
  });
};

/**
 * @description 弹窗关闭
 * **/

const onConfirm = () => {
  dialogVisible.value = false;

  emit('setRegionMap', unref(regionMap));
};

/**
 * @description 监听
 * **/

watch(addressValue, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    let data = options.value.filter(item => item.id === newVal);
    centre.value = gcj02towgs84(data[0].location.lng, data[0].location.lat);
    map.centerAndZoom(new T.LngLat(centre.value[0], centre.value[1]), zoom);
    amapGetAddress();
    setMarker([{ centre: centre.value }]);
  }
});

const setCity = (cityName = '成都市', mapZoom) => {
  const districtSearch = new AMap.DistrictSearch({ level: 'city' });
  districtSearch.search(cityName, async function (status, result) {
    if (status === 'complete' && result.info === 'OK' && result.districtList.length) {
      const [city] = result.districtList;
      centre.value = gcj02towgs84(city.center.lng, city.center.lat);
    } else {
      centre.value = [116.40769, 39.89945];
    }

    regionMap.value = {};
    addressValue.value = '';
    dialogVisible.value = true;
    await nextTick();
    map = new T.Map(id);
    map.centerAndZoom(new T.LngLat(centre.value[0], centre.value[1]), mapZoom || zoom);
  });
};

defineExpose({
  init,
  setCity
});
</script>

<style scoped lang="scss">
.tmap {
  height: 60vh;
  width: 100%;
}
.infoAddress {
  display: flex;
  align-items: center;
  gap: 20px;
}

:deep(.search-input) {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 300px;
  z-index: 2000;

  .el-autocomplete {
    width: 100%;
    .el-input-group__append {
      background-color: var(--el-fill-color-blank);
    }
  }
}
</style>
