<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-06 15:58:16
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-20 15:39:42
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRegion/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div style="width: 100%">
    <div
      class="regionGroup"
      :style="{
        '--length': lvl
      }"
    >
      <div class="regionItem" v-for="(item, index) in regionList" :key="item.key">
        <el-select
          style="width: 100%"
          v-model="regionValue[item.key].value"
          @change="
            e => {
              changeSelect(item.key, index, e);
            }
          "
          :teleported="true"
          :placeholder="item.name"
          v-bind="regionProps[item.key] || {}"
          :validate-event="false"
        >
          <el-option
            v-for="(itemOption, indexOption) in item.list"
            :label="itemOption.name"
            :value="itemOption.code"
            :key="itemOption.code"
          />
        </el-select>
      </div>
      <component v-if="!!extension" v-model="other" :is="extraRender()" @change="otherChange" />
      <div class="addressFull" v-if="showAddressFull">
        <el-input v-model="addressFull" @change="changeInput" :validate-event="false" placeholder="详细地址">
          <template #append>
            <el-icon @click="openMap"><LocationInformation /></el-icon>
          </template>
        </el-input>
      </div>
    </div>
    <Tmap ref="tdMap" @setRegionMap="setRegionMap"></Tmap>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, watch, nextTick, unref, isVNode } from 'vue';
import { getRegionList } from './useRequest.js';
import Tmap from './Tmap.vue';
import { useFormItem } from 'element-plus';
const formTiem = useFormItem();
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {
        province: { value: void 0, label: void 0 },
        city: { value: void 0, label: void 0 },
        district: { value: void 0, label: void 0 },
        community: { value: void 0, label: void 0 },
        street: { value: void 0, label: void 0 },
        addressFull: void 0
      };
    }
  },

  lvl: {
    type: Number,
    default: 5
  },
  showAddressFull: {
    // 是否展示输入地址详情
    type: Boolean,
    default: true
  },
  addressFullWidth: {
    // 详情输入框的宽度
    type: String,
    default: '16%'
  },
  regionProps: {
    type: Object,
    default: {}
  },
  startFather: {
    /**
     * @description  开始的节点配置 默认省开始
     * @param {number} lvl 开始的级别：0：省，1：市，2:区，3：社区，4：街道
     * @param {string} code 父级的code
     * **/
    type: Object,
    default: {
      lvl: 0,
      code: void 0
    }
  },
  extension: Function | String
});
const emits = defineEmits(['update:modelValue']);
const defaultConfig = ref({
  getProvince: {
    url: '/csccs/district/findDistrictByLevel',
    method: 'fetch',
    returnName: void 0,
    request: {
      level: 0
    },
    defaultProps: {
      code: 'districtCode',
      name: 'districtName'
    }
  },
  getChild: {
    url: '/csccs/district/findDistrictByIdOrCode',
    method: 'post',
    returnName: void 0,
    defaultProps: {
      code: 'districtCode',
      name: 'districtName'
    }
  }
});

//
const provinceList = ref([]);
const cityList = ref([]);
const districtList = ref([]);
const communityList = ref([]);
const streetList = ref([]);
const regionValue = ref({});
const addressFull = ref(void 0);
const other = ref(void 0);
const excludeList = ref([]);
const controllerMap = {};
const tdMap = ref();

const regionList = computed(() => {
  let arr = [
    {
      key: 'province',
      name: '省',
      list: provinceList.value,
      nextArrayName: 'cityList',
      lvl: 0,
      controller: void 0
    },
    {
      key: 'city',
      name: '市',
      list: cityList.value,
      nextArrayName: 'districtList',
      lvl: 1,
      controller: void 0
    },
    {
      key: 'district',
      name: '区/县',
      list: districtList.value,
      nextArrayName: 'communityList',
      lvl: 2,
      controller: void 0
    },
    {
      key: 'community',
      name: '街道/镇',
      list: communityList.value,
      nextArrayName: 'streetList',
      lvl: 3,
      controller: void 0
    },
    {
      key: 'street',
      name: '社区/村',
      list: streetList.value,
      lvl: 4,
      controller: void 0
    }
  ];

  arr = arr.slice(props.startFather.lvl > 4 ? 4 : props.startFather.lvl);
  let lvl = Math.min(props.lvl, 5);
  let list = arr.slice(0, lvl);
  excludeList.value = arr.slice(lvl);
  list.forEach((item, index) => {
    if (!regionValue.value.hasOwnProperty(item.key)) {
      regionValue.value[item.key] = { label: void 0, value: void 0 };
    }
    if (index == list.length - 1) {
      item.nextArrayName = void 0;
    }
  });

  return list || [];
});

/**
 * @description 区域选择change事件
 * @param {string} key
 * @param {number} index
 * @param {string | number} value
 * @param {true|false} bool
 * **/
const changeSelect = async (key, index, value, bool = true, nan = void 0) => {
  if (!value) return false;
  if (bool) {
    if (key) {
      regionValue.value[key].value = value;
      regionValue.value[key].label = regionList.value[index].list.find(item => item.code === value)?.name;
    }
    regionValue.value.location = [];
    regionValue.value.addressFull = '';
    addressFull.value = '';
    regionValue.value.other = '';
    other.value = '';
    regionList.value.forEach((item, i) => {
      const { key } = item;
      if (i > index && regionValue.value[key] && bool) {
        regionValue.value[key].value = void 0;
        regionValue.value[key].label = void 0;
      }
    });

    emits('update:modelValue', regionValue.value);
    _validate();
  }

  let arrayName = nan ? nan : regionList.value[index].nextArrayName;
  if (arrayName) {
    setController(key);
    await getRegionList(
      defaultConfig.value.getChild,
      { districtCode: value },
      {
        signal: controllerMap[key]?.signal ?? void 0
      },
      e => {
        const emus = {
          provinceList,
          cityList,
          districtList,
          communityList,
          streetList
        };
        emus[arrayName].value = e;

        regionList.value.forEach((el, i) => {
          if (i > index && regionValue.value[el.key] && bool) {
            arrayName !== `${el.key}List` && (emus[`${el.key}List`].value = []);
            // arrayName !== `${el.key}List` && eval(`${el.key}List.value=[]`);
          }
        });
      }
    );
  }

  console.log('regionValue', regionValue.value);
};

/**
 * @description 详细地址输入
 * @param {string} e
 * **/
const changeInput = e => {
  regionValue.value['addressFull'] = e;
  emits('update:modelValue', regionValue.value);
  _validate();
};

/**
 * @description 其他组件操作
 * **/
const otherChange = e => {
  regionValue.value['other'] = e;
  emits('update:modelValue', regionValue.value);
  _validate();
};

const extraRender = () => {
  if ($utils.isString(props.extension)) return <span>{props.extension}</span>;
  if ($utils.isFunction(props.extension)) {
    const content = props.extension(regionValue.value);
    if (isVNode(content)) {
      return content;
    }
    return <span>{content || ''}</span>;
  }
  return '';
};

/**
 * @description 打开地图
 * **/
const openMap = async () => {
  tdMap.value.init(unref(regionValue));
};

const setRegionMap = v => {
  // regionValue.value = ;
  v = JSON.parse(JSON.stringify(v));
  let str = '';
  for (let i = 0; i < excludeList.value.length; i++) {
    if (v[excludeList.value[i].key].value) {
      str += v[excludeList.value[i].key].label ? v[excludeList.value[i].key].label : '';
      v[excludeList.value[i].key] = { value: void 0, label: void 0 };
    }
  }
  v.addressFull = str + v.addressFull;
  if (!props.showAddressFull) v.addressFull = '';
  emits('update:modelValue', v);
  _validate();
};

const setController = key => {
  try {
    controllerMap[key] && controllerMap[key].abort();
    controllerMap[key] = new AbortController();
  } finally {
  }
};

const setSelectList = value => {
  regionList.value.map(item => {
    const emus = {
      provinceList,
      cityList,
      districtList,
      communityList,
      streetList
    };
    let list = emus[`${item.key}List`];
    if (!list.value.length && value[item.key]?.value && value[item.key]?.label) {
      list.value = [
        {
          code: value[item.key]?.value,
          name: value[item.key]?.label
        }
      ];
    }
  });

  addressFull.value = '';
  other.value = '';
  for (let i = 0; i < excludeList.value.length; i++) {
    if (value[excludeList.value[i].key]?.label) addressFull.value += value[excludeList.value[i].key]?.label;
  }
  addressFull.value += value.addressFull ?? '';
  other.value = value.other;
  regionValue.value = value;
};

/**
 * @description watch modelValue
 * **/
watch(
  () => props.modelValue,
  async (newVal, oldVal) => {
    setSelectList(newVal);

    if (!props.startFather.lvl && !provinceList.value.length) {
      setController('province');
      getRegionList(
        defaultConfig.value.getProvince,
        defaultConfig.value.getProvince.request,
        {
          signal: controllerMap['province']?.signal ?? void 0
        },
        e => {
          provinceList.value = e;
        }
      );
    } else if (!regionList.value[0].list.length) {
      let { key } = regionList.value[0];
      changeSelect(void 0, 0, props.startFather.code, false, `${key}List`);
    }

    for (let i = 0; i < regionList.value.length; i++) {
      const newValue = newVal?.[regionList.value[i].key]?.value;
      const oldValue = oldVal?.[regionList.value[i].key]?.value;
      if (newValue && newValue != oldValue) {
        changeSelect(regionList.value[i].key, i, newVal[regionList.value[i].key]?.value, false);
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const _validate = () => {
  formTiem?.formItem?.validate().then(res => {
    console.log(res);
  });
};
</script>

<style scoped>
.regionGroup {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.regionItem {
  width: calc((100%) / var(--length));
}
.addressFull {
  width: var(--addressFullWidth);
  flex: 0 0 auto;
}
</style>
