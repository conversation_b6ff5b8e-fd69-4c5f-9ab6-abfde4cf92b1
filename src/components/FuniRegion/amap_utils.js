/*
 * @Author: co<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-07 17:40:29
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-06-25 18:37:00
 * @FilePath: /funi-bpaas-as-ui/src/components/FuniRegion/amap_utils.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
// 地址编码
const geocoder = city => new AMap.Geocoder({ city });
let city = '成都';
const setGeocoder = p => {
  city = p;
};

const getLocation = (address, callback = () => {}) => {
  geocoder(city).getLocation(address, (status, result) => {
    if (status === 'complete' && result.info === 'OK') {
      const [geocode] = result.geocodes || [];
      if (!!geocode) {
        callback({
          ...geocode.addressComponent,
          formattedAddress: geocode.formattedAddress,
          lng: geocode.location.lng,
          lat: geocode.location.lat
        });
      } else {
        callback();
      }
    }
  });
};

const getAddress = (location, callback = () => {}) => {
  geocoder(city).getAddress(new AMap.LngLat(location.lng, location.lat), (status, result) => {
    if (status === 'complete' && result.info === 'OK') {
      callback({
        ...result.regeocode.addressComponent,
        formattedAddress: result.regeocode.formattedAddress,
        lng: location.lng,
        lat: location.lat
      });
    }
  });
};

// POI

const poiSearch = (keyword, city, callback = () => {}) => {
  city = city || '北京';
  let placeSearch = new AMap.PlaceSearch({
    city: city, //城市
    pageSize: 10, //每页结果数,默认10
    pageIndex: 1 //请求页码，默认1
  });
  placeSearch.search(keyword, (status, result) => {
    if (status === 'complete') {
      callback(result.poiList.pois);
    } else {
      console.error('poisearch error - ', result);
      callback([]);
    }
  });
};

// 定位
const geolocation = () =>
  new AMap.Geolocation({
    enableHighAccuracy: true, //是否使用高精度定位，默认:true
    timeout: 10000 //超过10秒后停止定位，默认：5s
  });

const getCurrentPosition = () => {
  return new Promise((resolve, reject) => {
    geolocation().getCurrentPosition((status, result) => {
      if (status === 'complete' && result.info === 'SUCCESS') {
        resolve(result.position);
      } else {
        reject(result);
      }
    });
  });
};

const convertToGCJ02 = (location, callback) => {
  AMap.convertFrom([location.lng, location.lat], 'gps', function (status, result) {
    if (result.info === 'ok' && !!result.locations.length) {
      const [location] = result.locations;
      callback({ lng: location.lng, lat: location.lat });
    }
  });
};

/**
 * @description 街道镇的行政区域 反推 省市区行政编码
 * **/
function padZeros(code, totalLength) {
  while (code.length < totalLength) {
    code += '0';
  }
  return code;
}

function getParentCodes(subRegionCode) {
  const parentCodes = [];

  if (subRegionCode.length >= 6) {
    parentCodes.push(subRegionCode.slice(0, 2)); // 省级编码
    parentCodes.push(subRegionCode.slice(0, 4)); // 市级编码
    parentCodes.push(subRegionCode.slice(0, 6)); // 区级编码
  }

  return parentCodes;
}

export {
  getLocation,
  getAddress,
  poiSearch,
  getCurrentPosition,
  convertToGCJ02,
  setGeocoder,
  getParentCodes,
  padZeros
};
