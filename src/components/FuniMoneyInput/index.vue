<template>
  <div class="funiMoneyInput align-left">
    <el-input-number
      v-if="isEdit"
      :controls="false"
      :maxlength="20"
      :precision="precision"
      :value-on-clear="null"
      v-bind="$attrs"
      style="width: 100%"
      v-model="numberValue"
      @change="change"
    />
    <div v-else>{{ $utils.amountIntl(numberValue, showPrefix) }}</div>
    <div class="chinese-amount" style="font-size: 12px" v-if="props.moneyCapitalShow && numberValue">
      {{ $utils.moneyCapital(numberValue * unit) }}
    </div>
  </div>
</template>
<script setup>
import { computed, watch } from 'vue';
const props = defineProps({
  modelValue: {
    type: String,
    default: void 0
  },
  isEdit: {
    type: Boolean,
    default: true
  },
  moneyCapitalShow: {
    type: Boolean,
    default: true
  },
  precision: {
    type: Number,
    default: 2
  },
  showPrefix: {
    type: Boolean,
    default: true
  },
  unit: {
    type: Number,
    default: 1
  },
  defaultZero: {
    type: Boolean,
    default: false
  }
});
const emits = defineEmits(['update:modelValue', 'change']);

const numberValue = computed(() => {
  const value = props.modelValue;
  if ($utils.isNil(value)) {
    return props.defaultZero ? 0 : void 0;
  } else {
    return Number(value);
  }
});
const change = value => {
  emits('update:modelValue', value);
  emits('change', value);
};
</script>
<style scoped>
.funiMoneyInput.align-left :deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
