<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-09 14:27:50
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-02-09 15:43:29
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniImageView/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->

<template>
  <div>
    <el-image-viewer
      v-if="show"
      ref="imageView"
      v-bind="$attrs"
      @close="close"
    ></el-image-viewer>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
const show = ref(false);
const imageView = ref(null);
/**
 * @description elImageViewer props   https://element-plus.gitee.io/zh-CN/component/image.html#imageviewer-%E5%B1%9E%E6%80%A7
 * @param urlList:[]
 * @param zIndex:Number
 * @param initialIndex:Number
 * @param infinite:Boolean default:true
 * @param infinite:Boolean default:true
 * @param hideOnClickModal:Boolean default:false
 * @param teleported:Boolean default:false
 * @param closeOnPressEscape:Boolean default:true
 * @param zoomRate:Number default:1.2
 * **/
const showViewer = () => {
  show.value = true;
};
const close = () => {
  show.value = false;
};
defineExpose({
  ref: imageView,
  showViewer
});
</script>
