import Compressor from 'compressorjs';

const compressor = {
  isBlob(value) {
    if (typeof Blob === 'undefined') return false;
    return value instanceof Blob || Object.prototype.toString.call(value) === '[object Blob]';
  },
  detectImageType(arrayBuffer) {
    const bytes = new Uint8Array(arrayBuffer);
    const signatures = [
      // ----------------------------
      // 图片类型
      // ----------------------------
      { mime: 'image/png', pattern: [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a] },
      { mime: 'image/jpeg', pattern: [0xff, 0xd8] },
      { mime: 'image/gif', pattern: [0x47, 0x49, 0x46, 0x38] }, // GIF8
      { mime: 'image/bmp', pattern: [0x42, 0x4d] }, // BM
      {
        mime: 'image/webp',
        pattern: [0x52, 0x49, 0x46, 0x46],
        check: bytes => bytes[8] === 0x57 && bytes[9] === 0x45 && bytes[10] === 0x42 && bytes[11] === 0x50
      }, // RIFF...WEBP
      { mime: 'image/x-icon', pattern: [0x00, 0x00, 0x01, 0x00] }, // ICO
      { mime: 'image/tiff', pattern: [0x49, 0x49, 0x2a, 0x00] }, // TIFF小端序
      { mime: 'image/tiff', pattern: [0x4d, 0x4d, 0x00, 0x2a] }, // TIFF大端序
      { mime: 'image/vnd.adobe.photoshop', pattern: [0x38, 0x42, 0x50, 0x53] }, // 8BPS
      {
        mime: 'image/svg+xml',
        check: bytes => {
          const text = new TextDecoder().decode(bytes.slice(0, 1024));
          return /<svg\b/i.test(text);
        }
      },
      {
        mime: 'image/heic',
        check: bytes =>
          bytes[4] === 0x66 &&
          bytes[5] === 0x74 &&
          bytes[6] === 0x79 &&
          bytes[7] === 0x70 && // ftyp
          bytes[8] === 0x68 &&
          bytes[9] === 0x65 &&
          bytes[10] === 0x69 &&
          bytes[11] === 0x63
      } // heic
    ];

    // 遍历检测
    for (const entry of signatures) {
      const pattern = entry.pattern;
      if (pattern) {
        if (bytes.length < pattern.length) continue;
        const slice = bytes.subarray(0, pattern.length);
        if (slice.every((val, i) => val === pattern[i]) && (!entry.check || entry.check(bytes))) {
          return entry.mime;
        }
      } else if (entry.check && entry.check(bytes)) {
        return entry.mime;
      }
    }

    return ''; // 未知类型
  },

  async compressImage(file, options) {
    if (!compressor.isBlob(file)) {
      console.error('The file is not a Blob or File');
      return Promise.reject(new Error('The file is not a Blob or File'));
    }

    const fileType = file.type || compressor.detectImageType(await file.arrayBuffer());
    if (!fileType || !fileType.startsWith('image/')) {
      console.error('The file type is not supported');
      return Promise.reject(new Error('The file type is not supported'));
    }

    const targetSize = options?.targetSize || 1024 * 1024; // 1MB
    if (file.size <= targetSize) {
      return Promise.resolve(file);
    }

    const newFile = new File([file], file.name, {
      type: fileType,
      lastModified: file.lastModified
    });

    return new Promise((resolve, reject) => {
      new Compressor(newFile, {
        quality: targetSize / newFile.size,
        convertSize: targetSize,
        ...options,
        success(result) {
          resolve(result);
        },
        error(err) {
          reject(err);
        }
      });
    });
  }
};

export default compressor;
