export default async function () {
  const { MODE, VITE_FUNI_MODE } = import.meta.env;
  const key = new Date().getTime();
  const keyName = sessionStorage.getItem('appName') || '';
  const { origin, pathname } = window.location;
  let match = pathname.match(/[^/]+(?=\/?$)/);
  match = match[0] || null;
  const url = origin + pathname.replace(match, keyName);
  const flag = MODE === 'production' && VITE_FUNI_MODE === 'cli' && keyName;
  const appBootstrapList = [];
  if (flag) {
    try {
      const { appBootstrap } = await import(`${url}bootstrap.js?${key}`);
      if (appBootstrap && typeof appBootstrap == 'function') {
        appBootstrapList.push(appBootstrap);
      }
    } catch {}
  } else {
    const modules = import.meta.glob(['../apps/*/bootstrap.{js,jsx,ts,tsx}'], { eager: true });
    Object.values(modules).forEach(module => {
      if (module.appBootstrap && typeof module.appBootstrap === 'function') {
        appBootstrapList.push(module.appBootstrap);
      }
    });
  }

  return {
    install(app, options) {
      appBootstrapList.forEach(item => {
        if (item && typeof item === 'function') {
          item(app, options);
        }
      });
    }
  };
}
