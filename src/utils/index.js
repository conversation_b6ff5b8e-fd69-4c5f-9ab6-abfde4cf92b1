import FuniJS from '@funi-lib/utils';
import fodash from '@funi/fodash'
import { BaseApi } from '@/apis/base';
import env from './env.js';

/**
 * 1.遍历utils目录文件，自动mixin，不需要mixin，添加 '!./xxx.js'
 * 2.支持自动mixin到全局的文件必须 export default {}
 * @param {*} caller
 */
function mixinUtils(caller) {
  const modules = import.meta.glob(['./*.js', './file/index.js', '!./index.js', '!./env.js', '!./appBootstrap.js'], {
    eager: true,
    import: 'default'
  });
  for (const path in modules) {
    const module = modules[path];
    !!module && caller.mixin(module);
  }
}

export default {
  install(app, options) {
    // 挂载utils
    const utils = FuniJS;
    mixinUtils(utils);
    utils.mixin({ env ,fodash});
    window.$utils = utils;
    app.config.globalProperties.$utils = utils;

    // 挂载baseHttp
    const baseHttp = BaseApi.getInstance();
    window.$http = baseHttp;
    app.config.globalProperties.$http = baseHttp;

    // 挂载全局对象
    const funi = {
      auth: {
        user: {}, //当前登录用户信息
        roles: [], //用户角色
        orgs: [] //组织架构
      },
      app: {
        data: {}, //全局变量
        // dicts:[], //字典集合
        id: '',
        name: ''
      }, // 应用信息

      utils: utils
    };
    window.$funi = funi;
    app.config.globalProperties.$funi = funi;
  }
};
