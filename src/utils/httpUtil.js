import FuniJS from '@funi-lib/utils';

export default {
  getServerBaseApi() {
    const pathComponents = window.location.pathname
      .replace(/\/site\/.*/, '/siteapp/')
      .replace(/\/h5\/.*/, '/h5app/')
      .replace(/\/mir\/.*/, '/mirapp/')
      .split('/')
      .filter(i => !!i);

    const path =
      pathComponents.length > 1
        ? pathComponents.slice(0, -1).join('/')
        : pathComponents.filter(i => !i.includes('app'))[0];

    if (!FuniJS.isProduction()) return ['/api', path].filter(Boolean).join('/');

    if (!!window.getBaseURL) return window.getBaseURL();

    const origin = window.location.origin;
    if (window.location.href.indexOf('icbc.com.cn/') !== -1) {
      return `${origin}/paas-api`;
    }
    return [origin, path].filter(<PERSON><PERSON><PERSON>).join('/');
  },

  getRouteBaseApi() {
    const path = window.location.pathname
      .split('/')
      .filter(item => !!item && !/app$/.test(item))
      .join('/');

    if (!FuniJS.isProduction()) return `/api/${path}`;

    const origin = window.location.protocol + '//' + window.location.host;
    return `${origin}/${path}`;
  }
};
