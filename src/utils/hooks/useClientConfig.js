import { Base64 } from 'js-base64';
function findClientInfo(clientId) {
  return $http.fetch('cscas/saasClient/info', {
    sysClientId: clientId,
    isClientId: true
  });
}

function findAllWidget(widgetList) {
  let realWidgetList = widgetList.filter(wgt => !['container'].includes(wgt.category));
  if (!realWidgetList) {
    realWidgetList = [];
  }
  widgetList.forEach(wgt => {
    if (wgt.cols && wgt.cols.length > 0) {
      wgt.cols.forEach(col => {
        if (col.widgetList && col.widgetList.length > 0) {
          let childrenWidget = findAllWidget(col.widgetList);
          realWidgetList.push(...childrenWidget);
        }
      });
    } else if (wgt.type === 'table' && wgt.rows && wgt.rows.length > 0) {
      wgt.rows.forEach(row => {
        if (row.cols && row.cols.length > 0) {
          row.cols.forEach(col => {
            if (col.widgetList && col.widgetList.length > 0) {
              let childrenWidget = findAllWidget(col.widgetList);
              realWidgetList.push(...childrenWidget);
            }
          });
        }
      });
    } else if (wgt.type === 'tab' && wgt.tabs && wgt.tabs.length > 0) {
      wgt.tabs.forEach(tab => {
        let childrenWidget = findAllWidget(tab.widgetList);
        realWidgetList.push(...childrenWidget);
      });
    } else if (wgt.type === 'card' && wgt.widgetList && wgt.widgetList.length > 0) {
      let childrenWidget = findAllWidget(wgt.widgetList);
      realWidgetList.push(...childrenWidget);
    }
  });
  return realWidgetList;
}

export default async function (clientId) {
  clientId ??= sessionStorage.getItem('c_id');
  if (!clientId) {
    console.error('clientId 为空');
    return Promise.reject('clientId 为空');
  }
  const { sysClientVo } = await findClientInfo(clientId);
  sessionStorage.setItem(clientId, JSON.stringify(sysClientVo));

  let { elementJson, jsonValue } = await $http.post('csccs/sys/paramVersion/findMaxVersionByParamType', {
    clientId,
    paramType: 1,
    isClientId: true
  });

  let jsCode = Base64.decode(
    window.$utils.SM.sm4.decrypt(JSON.parse(elementJson)?.config, 'c8502bd294000ef49777ec31c54e6ebb')
  );
  jsonValue = JSON.parse(jsonValue);
  let config = findAllWidget(JSON.parse(jsCode).widgetList);
  config = (config || []).map(item => {
    return {
      label: item?.options?.label,
      key: item?.options?.name,
      value: jsonValue[item?.options?.name]
    };
  });
  window.$funi[`clientConfig_${clientId.replace(/-/g, '_')}`] = config;
}
