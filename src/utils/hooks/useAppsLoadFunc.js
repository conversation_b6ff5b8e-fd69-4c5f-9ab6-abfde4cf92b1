/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-09-07 20:33:59
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-07 20:51:00
 * @FilePath: /funi-cloud-web-gsbms/src/utils/hooks/useAppsLoadFunc.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
const modules = import.meta.glob(['../../apps/*/bootstrap.{js,jsx,ts,tsx}'], { eager: true });
const funcs = {};
const allFuncs = [];
Object.values(modules).forEach(module => {
  Object.assign(funcs, module.appLoadFunctions);
});
Object.values(funcs).forEach(item => {
  allFuncs.push(item);
});

export const appsLoadFunc = async () => {
  await Promise.all(allFuncs.map(item => item()));
  Promise.resolve();
};
