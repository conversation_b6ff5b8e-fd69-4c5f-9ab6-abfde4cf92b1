/*
 * @Author: tao.yang <EMAIL>
 * @Date: 2024-05-30 14:49:59
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-12-17 15:10:57
 * @FilePath: /src/utils/hooks/useExportTable.js
 * @Description:
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */

import { utils, writeFile } from 'xlsx';
import { getCurrentInstance, h, render } from 'vue';
import FuniVCurd from '@/components/FuniVCurd/index.vue';

export const useExportTable = props => {
  const instance = getCurrentInstance();

  const handleExport = (data, fileName, columns) => {
    const loading = instance.proxy.$loading({ fullscreen: true });
    // const columns = props.col¶umns;
    const rowKey = props.rowKey;

    // 分批次处理的大小
    const batchSize = 200;
    let currentIndex = 0;
    const virtualDOM = [];

    const processBatch = deadline => {
      while (currentIndex < data.length && deadline.timeRemaining() > 0) {
        const end = Math.min(currentIndex + batchSize, data.length);
        const batchData = data.slice(currentIndex, end);

        const vnode = h(FuniVCurd, {
          data: batchData,
          columns: columns,
          rowKey: rowKey
        });
        vnode.appContext = instance.appContext;

        // 创建一个虚拟的DOM片段
        const fragment = document.createDocumentFragment();
        render(vnode, fragment);
        const [table] = vnode.el.getElementsByTagName('table');
        const thead = table.getElementsByTagName('thead')[0];
        if (currentIndex !== 0 && !!thead) {
          table.removeChild(thead);
        }
        virtualDOM.push(table);
        currentIndex += batchSize;
      }

      if (currentIndex < data.length) {
        // 如果还有剩余的批次，继续使用 requestIdleCallback 处理
        requestIdleCallback(processBatch);
      } else {
        // 当所有批次处理完成后，执行导出操作
        setTimeout(() => {
          loading.close();
          exportToExcel(virtualDOM, fileName);
        }, 0);
      }
    };

    const exportToExcel = (virtualDOM, fileName) => {
      let worksheet;
      virtualDOM.forEach(node => {
        if (!worksheet) {
          worksheet = utils.table_to_sheet(node, { raw: true });
        } else {
          utils.sheet_add_dom(worksheet, node, { raw: true, origin: -1 });
        }
      });

      // 获取工作表中的范围
      const range = utils.decode_range(worksheet['!ref']);

      // 遍历工作表中的所有单元格
      for (let R = range.s.r; R <= range.e.r; ++R) {
        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cell_address = { c: C, r: R };
          const cell_ref = utils.encode_cell(cell_address);

          if (worksheet[cell_ref] && C === 8 && R > 1) {
            // 如果是第2列（索引为1），且不是标题行，则设置单元格类型为Number
            worksheet[cell_ref].t = 'n';
          }
        }
      }
      const workbook = utils.book_new();
      utils.book_append_sheet(workbook, worksheet, 'Sheet1');
      writeFile(workbook, fileName || 'export.xlsx');
    };

    // 开始处理批次
    requestIdleCallback(processBatch);
  };

  return { handleExport };
};
