/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2024-01-12 15:39:03
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-12 15:46:08
 * @FilePath: /funi-cloud-web-gsbms/src/utils/hooks/useMutationObserver.js
 * @Description: 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
function useMutationObserver(node, con = {}, callback = () => { }) {
    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            callback && callback()
        });
    });
    // 配置MutationObserver以监视子节点的变化
    const config = { childList: true, subtree: true, ...con };

    // 选择要观察变化的DOM元素
    const targetNode = node

    // 启动MutationObserver
    observer.observe(targetNode, config);
    // 初始检查一次
    callback && callback()
    return observer

}

export default useMutationObserver