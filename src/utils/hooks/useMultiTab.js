/*
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-03-14 16:24:19
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-12-20 14:25:28
 * @FilePath: /src/utils/hooks/useMultiTab.js
 * @Description: 多页签功能
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */

import { inject, unref } from 'vue';
import { useRoute } from 'vue-router';

export const useMultiTab = () => {
  const multiTab = inject('multiTab');
  const route = useRoute();

  /**
   * 关闭指定页面
   * @param {string} tabKey - 值为route.fullPath
   */
  function close(tabKey) {
    if (multiTab.value) {
      unref(multiTab).close(tabKey);
    }
  }

  /**
   * 关闭当前页面
   */
  function closeCurrentPage() {
    close(unref(route.fullPath));
  }

  return { close, closeCurrentPage };
};
