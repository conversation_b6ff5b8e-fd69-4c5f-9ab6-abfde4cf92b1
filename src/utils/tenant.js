import xss from 'xss';

export default {
  getTenantID() {
    return window.location.pathname.split('/')[1];
  },
  getLoginLocation() {
    const logoutURL = xss(sessionStorage.getItem('log_out') || '');
    if (!!logoutURL) return logoutURL;

    const { origin, pathname } = window.location;
    const path = pathname
      .split('/')
      .filter(i => !!i)
      .slice(0, -1)
      .concat('casapp')
      .join('/');
    return [origin, path, '#'].join('/');
  }
};
