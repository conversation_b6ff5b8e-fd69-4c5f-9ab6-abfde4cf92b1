// 导入 FuniJS.CryptoUtil
import FuniJS from '@funi-lib/utils';

// 检查属性是否为函数且不是 getter
function isFunctionProperty(obj, key) {
  if (['constructor', 'init'].includes(key)) return false;
  const descriptor = Object.getOwnPropertyDescriptor(obj, key);
  return descriptor && !descriptor.get && typeof obj[key] === 'function';
}

// 创建一个普通对象，包含所有过滤后的函数方法
const methods = {};

// 获取对象自身的函数方法
Object.getOwnPropertyNames(FuniJS.CryptoUtil).forEach(key => {
  if (isFunctionProperty(FuniJS.CryptoUtil, key)) {
    methods[key] = FuniJS.CryptoUtil[key].bind(FuniJS.CryptoUtil);
  }
});

// 获取原型链上的函数方法
const proto = Object.getPrototypeOf(FuniJS.CryptoUtil);
Object.getOwnPropertyNames(proto).forEach(key => {
  if (isFunctionProperty(proto, key) && !methods.hasOwnProperty(key)) {
    methods[key] = proto[key].bind(FuniJS.CryptoUtil);
  }
});

// 导出包含所有过滤后函数方法的普通对象
export default methods;
