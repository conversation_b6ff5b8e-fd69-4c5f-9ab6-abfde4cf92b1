const registers = new Map();
const innerKeys = ['USER_INFO', 'USER_CENTER', 'APP_MANAGE', 'ONLINE_DOC', 'LOGOUT', 'REC_RECORD', 'LOG'];

const register = {
  map: registers,
  get(key) {
    return registers.get(key) || null;
  },
  /**
   * Item
   * {
   *    label: String, // 显示的名称
   *    icon: String, // 图标url import icon from 'xxx';或者 new URL('./path/xxx', import.meta.url)
   *    component: VNode,
   *    action: (item, ref) => {}
   * }
   */
  add(key, item) {
    if (innerKeys.includes(key)) {
      console.warn('【Regist User Center Item】The key is already in use, please change the key name.');
      return;
    }
    if (key && !registers.has(key) && !!item) {
      registers.set(key, Object.assign({}, item, { key }));
    }
  },
  keys() {
    return [...registers.keys()];
  },
  values() {
    return [...registers.values()];
  },
  entries() {
    return registers.entries();
  }
};

export default {
  install(app, options) {
    app.config.globalProperties.$ucRegister = register;
  }
};
