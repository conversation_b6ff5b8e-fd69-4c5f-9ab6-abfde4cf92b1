/*
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-02-14 16:19:01
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2025-07-24 00:08:43
 * @FilePath: /src/apis/app/bpaas.js
 * @Description:
 */
import Vrouter from '@/router';

const router = Vrouter;
const apiUrl = {
  /** 查询当前用户信息 */
  findCurrentUser: '/cscas/auth/getCurrentUser',
  /** 根据系统编码查询系统 ?saasClientId=*** */
  clientInfo: '/cscas/saasClient/clientInfo',
  /** 根据saas服务主键id查询服务详情 ?sysServiceId=*** */
  serviceInfo: '/cscas/saasService/info',
  /** 根据系统编码查询系统 ?appNameInfo=*** */
  appNameInfo: '/cscas/saasClient/appNameInfo',
  /** 获取菜单、页面、权限数据 */
  getSystemMenus: '/cscas/userCenter/getSystemMenus',
  /** 查询平台配置 */
  findPlatformConfig: '/csccs/sys/param/getParam',
  /** 新增/修改Curd自定义配置 */
  newCurdCustomization: '/csccs/showCustomization/new',
  /** 查询Curd自定义配置 */
  findCurdCustomization: '/csccs/showCustomization/find',
  /** saas服务分类列表查询 */
  querySaasServiceClassifyList: '/cscas/saasServiceClassify/querySaasServiceClassifyList',
  /** 根据服务类型和页面类型查询服务信息 */
  findClientService: '/csuc/clientService/findClientService',
  /** 获取应用授权 */
  authorize: '/cscas/oauth/authorize',
  /**获取G端用户基本信息接口**/
  ddGetPhone: '/csccs/unifyAccountBind/queryUnifyAccount',
  /** 获取G端用户基本信息接口 **/
  dingTalkLogin: '/cscas/unifyAccountAuth/loginByDd',
  /** 查询全部管理主体信息，无数据权限控制 */
  orgTreeWithoutAuth: '/csccs/orgList/orgTree',
  /** 查询所有角色信息 */
  roles: '/csccs/roleList/roleList',
  /** 查询所有角色信息 - 无权限控制 */
  allRoles: '/csccs/roleList/roleAllList',
  /** 获取账号个性化配置 */
  govAccountConfig: '/csuc/govAccountConfig/detailMy',
  /** 查询账号配置 新接口**/
  unifyAccountConfigDetailMy: '/csccs/unifyAccountConfig/detailMy',
  /** 修改号配置 新接口 **/
  unifyAccountConfigUpdateMy: '/csccs/unifyAccountConfig/updateMy',
  /** 心跳 */
  heart: '/csccs/userActive/heart',
  /** 根据ticket获取token */
  getToken: '/cscas/oauth/getToken',
  /** 从cookie中获取token */
  getTokenFromCookie: '/cscas/oauth/getTokenFromCookie',
  /** 列表高级查询表单配置查询（根据页面编码） */
  findSQLFieldByPageCode: '/csccs/sqlField/findByPageCode',
  /** 列表表头排序配置查询（根据页面编码） */
  findSQLSortByPageCode: '/csccs/sqlSort/findByPageCode',
  /** 启动流程 */
  startBusiness: '/bpmn/businessManage/startBusiness',
  /** 执行流程 */
  executeBusiness: '/bpmn/businessManage/executeBusiness',
  /** 删除流程 */
  delBusiness: '/csccs/businessControl/delBusiness'
};

/** 查询当前用户信息 */
export function findCurrentUser() {
  return $http.post(apiUrl.findCurrentUser).then(res => {
    const userInfo = res || {};
    userInfo.userName = userInfo.username;
    return userInfo;
  });
}

/**
 * 根据系统编码查询系统
 * @param {string} sysCode 系统编码
 * @returns
 */
export function findSysByCode(appName) {
  return $http.fetch(apiUrl.appNameInfo, { appName }).then(res => {
    const clientInfo = (res || {}).sysClientVo || {};
    clientInfo.name = clientInfo.clientName;
    return clientInfo;
  });
}

/**
 * 根据系统id查询系统
 * @param {string} sysClientId 系统id
 * @returns
 */
export function fetchClientInfo(sysClientId) {
  return $http.fetch(apiUrl.clientInfo, { sysClientId }).then(res => {
    const clientInfo = (res || {}).sysClientVo || {};
    clientInfo.name = clientInfo.clientName;
    return clientInfo;
  });
}

/**
 * 根据saas服务主键id查询服务详情
 * @param {string} sysServiceId 系统id
 * @returns
 */
export function fetchServiceInfo(sysServiceId) {
  return $http.fetch(apiUrl.serviceInfo, { sysServiceId }).then(res => {
    const serviceInfo = (res || {}).sysServiceVo || {};
    serviceInfo.name = serviceInfo.serviceName;
    return serviceInfo;
  });
}

/**
 * 获取菜单、页面、权限数据
 * @param {string} sysCode 系统编码
 * @returns {object}
 */
export function getSystemMenus(saasClientId, serviceId) {
  return $http.post(apiUrl.getSystemMenus, { code: saasClientId, serviceId });
}

/** 查询平台配置 */
export function findPlatformConfig() {
  const urlParams = new URLSearchParams(window.location.hash.split('?')[1]);
  const platUnitId = urlParams.get('platUnitId');
  return $http.post(apiUrl.findPlatformConfig, { paramType: 0, platUnitId }).then(res => {
    return JSON.parse((res || {}).jsonValue || '{}');
  });
}

/** 新增/修改Curd自定义配置 */
export function newCurdCustomization(data) {
  return $http.post(apiUrl.newCurdCustomization, data);
}

/** 查询Curd自定义配置 */
export function findCurdCustomization(data) {
  return $http.post(apiUrl.findCurdCustomization, data);
}

export function querySaasServiceClassifyList() {
  return $http.post(apiUrl.querySaasServiceClassifyList);
}

export function findClientService() {
  return $http.post(apiUrl.findClientService, { serviceType: 'Gd', pageType: '1' });
}

export function authorize({ clientId, serviceId } = {}) {
  return $http.post(apiUrl.authorize, { clientId, serviceId });
}

export async function setDDLogin(dingTalkUserId) {
  try {
    let { phoneNumber } = await $http.post(apiUrl.ddGetPhone + '?bindType=2&bindId=' + dingTalkUserId);
    let tokenParams = {
      ddUserId: dingTalkUserId,
      phoneNumber: $utils.encryptedData(phoneNumber),
      userGroup: 'G'
    };
    await $http.post(apiUrl.dingTalkLogin, tokenParams).then(resToken => {
      document.cookie = 'fp_uk=' + resToken + ';';
      sessionStorage.setItem('token', resToken);
    });
    return Promise.resolve();
  } catch {
    return Promise.reject();
  }
}

/**
 * @description 工作台跳转 钉钉跳转设置cid sid
 * **/
function setCSId(s_id, c_id) {
  if (c_id || s_id) {
    sessionStorage.setItem('c_id', c_id);
    sessionStorage.setItem('s_id', s_id);
  }
}

export async function resetRouterParams(queryParams, skipConfig = {}) {
  let {
    businessConfigCode: bc,
    businessId: bi,
    extendParams_sysCode,
    extendParams_serviceId,
    extendParams_clientId
  } = queryParams;
  setCSId(extendParams_serviceId, extendParams_clientId);
  let keyName = window.location.pathname.split('/')[2] || void 0;
  if (keyName === 'cliapps') keyName = sessionStorage.getItem('appName');
  console.log('keyName', keyName);
  console.log('skipConfig', skipConfig);
  const currentPath = window.location.href.split('#')[1];
  const resolvedRoute = router.resolve(currentPath || '');
  let { path } = resolvedRoute;
  if (keyName && skipConfig[keyName] && extendParams_sysCode !== 'as') {
    try {
      let { id, clientId } = await $http.post(
        `${skipConfig[keyName]}?businessConfigCode=${bc}&businessId=${bi}&type=pc`,
        {}
      );
      sessionStorage.setItem('c_id', clientId);
      const { params } = await setRouterQuery(bc, id, skipConfig);
      router.push({
        path,
        query: params
      });
      return Promise.resolve();
    } catch (err) {
      return Promise.reject(err);
    }
  } else if (extendParams_sysCode == 'as') {
    return Promise.resolve();
  } else {
    return Promise.reject();
  }
}

async function setRouterQuery(bc, id, skipConfig) {
  let params = {};
  let data = void 0;
  try {
    const { api, method, requestIdName, routerQuery } = skipConfig[bc];
    data = await $http[method](api, {
      [requestIdName]: id
    });
    for (let key in routerQuery) {
      params[key] = new Function('data', `return ${routerQuery[key]}`)(data);
    }
  } catch {
    params = { id };
  }
  return {
    data,
    params
  };
}

export function fetchOrgTree() {
  return $http.fetch(apiUrl.orgTreeWithoutAuth).then(res => res?.list || []);
}

export function fetchAllRoles() {
  return $http.post(apiUrl.allRoles, { flag: false }).then(res => res?.list || []);
}

export function govAccountConfig() {
  return $http.post(apiUrl.govAccountConfig).then(res => res || {});
}

export function unifyAccountConfigDetailMy() {
  return $http.post(apiUrl.unifyAccountConfigDetailMy);
}
export function unifyAccountConfigUpdateMy(params) {
  return $http.post(apiUrl.unifyAccountConfigUpdateMy, params);
}

export function heart() {
  return $http.fetch(apiUrl.heart);
}

export function getToken(ticket) {
  return $http.post(apiUrl.getToken, { ticket });
}

export function getTokenFromCookie() {
  return $http.post(apiUrl.getTokenFromCookie);
}

export function findSQLFieldByPageCode({ pageCode, position } = { pageCode: '', position: '1' }) {
  return $http.post(apiUrl.findSQLFieldByPageCode, { pageCode, position });
}

export function findSQLSortByPageCode({ pageCode = '' } = { pageCode: '' }) {
  return $http.post(apiUrl.findSQLSortByPageCode, { pageCode });
}

// 启动工作流
export const startBusiness = params => {
  return $http.post(apiUrl.startBusiness, params);
};

// 执行工作流
export const executeBusiness = params => {
  return $http.post(apiUrl.executeBusiness, params);
};

// 工作流删除要件
export const delBusiness = params => {
  return $http.post(apiUrl.delBusiness, params);
};
