import { sm3 } from 'sm-crypto';
import FuniJS from '@funi-lib/utils';
import { minimatch } from 'minimatch';
import { ElMessageBox, ElNotification } from 'element-plus';

let abortController = new AbortController();

export class <PERSON><PERSON><PERSON> extends FuniJS.Http {
  constructor() {
    super({
      baseURL: window.$utils.getServerBaseApi(),
      timeout: 60000,
      openBaseURL: new URL(
        window.$utils.getServerBaseApi().replace(location.origin, ''),
        sessionStorage.getItem('isgateway') || location.origin
      )[import.meta.env.PROD ? 'href' : 'pathname'].replace(/\/[^\/]*$/, '/isgateway'),
      openAppKey: import.meta.env.VITE_OPEN_APP_KEY,
      openVersion: import.meta.env.VITE_OPEN_VERSION
    });
  }

  static getInstance() {
    if (!BaseApi._instance) {
      BaseApi._instance = new BaseApi();
    }
    return BaseApi._instance;
  }

  //////////////////////////////////////////////////////////////////////////////////////////

  async queryEncryptList() {
    if (!!this.encryptList) return Promise.resolve(this.encryptList);
    try {
      const res = await super.fetch('/gateway/resource/acl/queryEncryptList');
      this.encryptList = window.$utils.gatewayDecrypt(res) || [];
    } catch (error) {
      this.encryptList = [];
    }
  }

  isNeedEncrypt(url) {
    if (!this.encryptList || !this.encryptList.length || !url || typeof url !== 'string') return false;
    const path = new URL(url, location.origin).pathname;
    return this.encryptList.some(i => minimatch(path, i));
  }

  isgatewayMatched(url) {
    // 匹配/isgateway/或者isgateway/
    return /^\/?(isgateway)\//.test(url);
  }

  //axiox拦截处理begin

  //实现request拦截
  interceptorsRequest(config) {
    config.signal = config?.signal || abortController.signal;
    const token = sessionStorage.getItem('token');
    const requestId = $utils.guid();
    const configURL = new URL(config.url, location.origin);
    config.headers['Content-Type'] ||= 'application/json;charset=utf-8';

    if (config.isgateway) {
      config.baseURL = this._axios.defaults.openBaseURL;
      config.url = configURL.pathname.split('/isgateway')[1];

      const headers = {
        'X-Open-App-Key': this._axios.defaults.openAppKey,
        'X-Open-Version': this._axios.defaults.openVersion,
        'X-Open-Request-Id': requestId,
        'X-Open-Timestamp': Date.now()
      };

      const sortedHeaderString = Object.entries({ ...headers, Resource: config.url })
        .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
        .map(([key, value]) => `${key}=${value}`)
        .join('&');

      Object.assign(config.headers, {
        ...headers,
        'X-Open-Sign': $utils.isgatewaySign(sortedHeaderString)
      });
    } else {
      config.headers['X-FuniPaas-Request-Id'] = requestId;
      config.headers['X-FuniPaas-Authorization'] = token;
      if (!!token) {
        const digestStr = [configURL.pathname, requestId, token].join('$$');
        const signDigest = sm3(digestStr);
        config.headers['X-FuniPaas-Request-Hash'] = signDigest;
      }
    }

    config.transformRequest?.push(function (data, headers) {
      if (headers.has('X-Encrypted')) {
        headers.delete('X-Encrypted');
        try {
          return $utils.isString(data) ? JSON.parse(data) : data;
        } catch (error) {
          return data;
        }
      }
      return data;
    });

    return config;
  }

  //实现response拦截
  interceptorsResponse(response) {
    if (!response.source) {
      return { data: response };
    }
    if (response instanceof Blob) {
      return { data: response };
    } else if (![0, 200].includes(response.status) && response.success !== true) {
      return this.handleError(response);
    }
    if (!!response.dataEncrypt && $utils.isString(response.data)) {
      // 加密内容以string形式返回
      const decryptdData = $utils.decryptdData(response.data);
      response.data = decryptdData ? JSON.parse(decryptdData) || {} : response.data;
    }
    return response;
  }

  handleError(response) {
    const respect = super.handleError(response);
    respect.catch(err => {
      if (['100001', '100002', '100003', '100004', '990001', '990002'].includes(err.code)) {
        abortController.abort();
        /* TODO 退出登录 并重定向登录页 临时处理为前端重定向  */
        ElMessageBox.confirm(err.message, err.phrase, {
          confirmButtonText: '知道了',
          type: 'warning',
          showClose: false,
          showCancelButton: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        }).then(() => {
          abortController = new AbortController();
          sessionStorage.clear();
          window.location.replace($utils.getLoginLocation());
        });
      } else if ([404].includes(err.status)) {
        console.error(err);
      } else if ([502, 503].includes(err.status)) {
        let ops = {
          title: `Error(${err.status})`,
          message: err.message,
          type: 'error'
        };
        ElNotification(ops);
      } else {
        let ops = {
          title: err.phrase ? (err.code ? `${err.phrase}(${err.code})` : err.phrase) : err.message,
          type: 'error'
        };
        if (err.phrase) ops.message = err.message;
        ElNotification(ops);
      }
    });

    return respect;
  }

  async post(url, param, config = {}) {
    await this.queryEncryptList();

    const isgatewayMatched = this.isgatewayMatched(url);
    const isNeedEncrypt = this.isNeedEncrypt(url);

    if (isNeedEncrypt || isgatewayMatched) {
      const encryptMethod = isgatewayMatched ? $utils.isgatewayEncrypt : isNeedEncrypt ? $utils.gatewayEncrypt : p => p;
      const postData = $utils.isFormData(param) ? param : encryptMethod(param || {});
      const contentType = $utils.isFormData(param) ? 'multipart/form-data' : '';

      const headers = Object.assign({}, config.headers, { 'Content-Type': contentType, 'X-Encrypted': 1 });
      const postConfig = Object.assign({}, config, { isgateway: isgatewayMatched, headers });

      return super.post(url, postData, postConfig).then(data => {
        if (isgatewayMatched) {
          return $utils.isgatewayDecrypt(data.data);
        } else if (isNeedEncrypt) {
          return $utils.isString(data) ? $utils.gatewayDecrypt(data) : data;
        }
      });
    }

    return super.post(url, param, config);
  }

  async fetch(url, param, headers = {}, config = {}) {
    if (this.isgatewayMatched(url)) {
      return super.fetch(url, param, headers, { ...config, isgateway: true });
    }
    await this.queryEncryptList();
    if (this.isNeedEncrypt(url)) {
      const searchParams = new URLSearchParams(url.split('?')[1]);
      !!param && Object.entries(param).forEach(([key, value]) => searchParams.set(key, value));
      const paramString = searchParams.toString();
      return super
        .fetch(url.split('?')[0], !!paramString ? { data: $utils.gatewayEncrypt(paramString) } : {}, headers, config)
        .then(data => ($utils.isString(data) ? $utils.gatewayDecrypt(data) : data));
    }
    return super.fetch(url, param, headers, config);
  }

  downloadFile(url, param, config = {}) {
    if (this.isNeedEncrypt(url)) {
      const postData = $utils.isFormData(param) ? param : $utils.gatewayEncrypt(param || {});
      const contentType = $utils.isFormData(param) ? 'multipart/form-data' : 'text/plain';
      const postConfig = Object.assign({}, config, {
        headers: Object.assign({}, config.headers, { 'Content-Type': contentType })
      });
      return super.downloadFile(url, postData, postConfig);
    }
    return super.downloadFile(url, param, config);
  }

  /**
   * @description: 文件上传
   * @param {*} url      接口地址
   * @param {*} formData 文件数据
   * @param {*} config   自定义config
   * @return {*}
   */
  upload2(url, formData, configer = {}) {
    return new Promise((resolve, reject) => {
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        responseType: 'blob',
        ...configer
      };
      this._axios.post(url, formData, config).then(
        response => {
          const contentType = response && response.headers ? response.headers['content-type'] : '';
          if (contentType.toLowerCase().includes('application/octet-stream')) {
            this.downloadDataHandler(response)
              .then(() => {
                resolve(response);
              })
              .catch(err1 => {
                reject(err1);
              });
          } else if (response && response.data) {
            var reader = new FileReader();
            reader.onload = function () {
              var dataUrl = reader.result;
              var base64 = dataUrl.split(',')[1]; // 将 dataUrl 转换为 base64 编码的字符串
              var decodedData = atob(base64); // 解码 base64
              let realResponse = {};
              try {
                realResponse = JSON.parse(decodedData);
              } catch (ex) {
                console.log(ex);
              }
              resolve(realResponse);
            };
            reader.readAsDataURL(response?.data);
          } else {
            resolve(response);
          }
        },
        err => {
          reject(err);
        }
      );
    });
  }
  //axiox拦截处理end

  /////////////////////////////////////////////////////////////////////////////////

  /**
   * 创建 GET 方式的 Server-Sent Events (SSE) 连接
   * @param {string} url - SSE 端点 URL
   * @param {Object} param - 查询参数
   * @param {Object} headers - 自定义请求头
   * @param {Object} config - 配置选项
   * @returns {Promise<SSEConnection>} SSE 连接对象
   */
  async sseFetch(url, param = {}, headers = {}, config = {}) {
    return this._createSSEConnection('GET', url, param, headers, config);
  }

  /**
   * 创建 POST 方式的 Server-Sent Events (SSE) 连接
   * @param {string} url - SSE 端点 URL
   * @param {Object} param - 请求参数
   * @param {Object} headers - 自定义请求头
   * @param {Object} config - 配置选项
   * @returns {Promise<SSEConnection>} SSE 连接对象
   */
  async ssePost(url, param = {}, headers = {}, config = {}) {
    return this._createSSEConnection('POST', url, param, headers, config);
  }

  /**
   * 创建 SSE 连接的内部实现
   * @private
   * @param {string} method - HTTP 方法
   * @param {string} url - SSE 端点 URL
   * @param {Object} param - 请求参数
   * @param {Object} headers - 自定义请求头
   * @param {Object} config - 配置选项
   * @returns {Promise<SSEConnection>} SSE 连接对象
   */
  async _createSSEConnection(method, url, param, headers, config) {
    try {
      const isgatewayMatched = this.isgatewayMatched(url);
      // 确保加密列表已加载
      await this.queryEncryptList();

      // 准备基础配置，模拟标准 HTTP 请求配置
      const sseConfig = {
        method,
        url,
        headers: { Accept: 'text/event-stream', 'Content-Type': 'application/json', ...headers },
        isgateway: isgatewayMatched,
        ...config
      };

      // 处理请求参数和数据
      if (method === 'GET') {
        // GET 请求参数处理，参考现有 fetch 方法的逻辑
        if (this.isNeedEncrypt(url)) {
          const searchParams = new URLSearchParams(url.split('?')[1]);
          if (param && Object.keys(param).length > 0) {
            Object.entries(param).forEach(([key, value]) => searchParams.set(key, value));
          }
          const paramString = searchParams.toString();
          const query = paramString ? $utils.gatewayEncrypt(paramString) : '';
          sseConfig.url = `${url.split('?')[0]}?data=${query}`;
        } else {
          // 不需要加密的 GET 请求，正常处理查询参数
          if (param && Object.keys(param).length > 0) {
            const searchParams = new URLSearchParams();
            Object.entries(param).forEach(([key, value]) => searchParams.set(key, value));
            sseConfig.url = url.includes('?')
              ? `${url}&${searchParams.toString()}`
              : `${url}?${searchParams.toString()}`;
          }
        }
      } else {
        // POST 请求处理请求体，参考现有 post 方法的处理逻辑
        if (this.isNeedEncrypt(url)) {
          sseConfig.data = $utils.gatewayEncrypt(param || {});
        } else {
          sseConfig.data = param;
        }
      }

      // 使用现有的拦截器处理请求头和认证
      const processedConfig = this.interceptorsRequest(sseConfig);

      // 构建 fetch 选项
      const fetchOptions = {
        method: processedConfig.method,
        headers: processedConfig.headers
      };

      if (processedConfig.data) {
        fetchOptions.body =
          typeof processedConfig.data === 'string'
            ? processedConfig.data
            : isgatewayMatched
            ? $utils.isgatewayEncrypt(processedConfig.data)
            : JSON.stringify(processedConfig.data);
      }

      // 发起 fetch 请求
      const response = await fetch(
        [processedConfig.baseURL, processedConfig.url].filter(Boolean).join(''),
        fetchOptions
      );

      if (!response.ok) {
        throw new Error(`SSE 连接失败: ${response.status} ${response.statusText}`);
      }

      // 创建 SSE 连接对象
      return this._buildSSEConnection(response, url);
    } catch (error) {
      console.error('创建 SSE 连接失败:', error);
      throw error;
    }
  }

  /**
   * 构建 SSE 连接对象
   * @private
   * @param {Response} response - fetch 响应对象
   * @param {string} originalUrl - 原始请求 URL
   * @returns {SSEConnection} SSE 连接对象
   */
  _buildSSEConnection(response, originalUrl) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    // SSE 连接状态
    const CONNECTING = 0;
    const OPEN = 1;
    const CLOSED = 2;

    const sseConnection = {
      readyState: CONNECTING,
      url: response.url,
      _originalUrl: originalUrl,
      _needsDecryption: this.isNeedEncrypt(originalUrl),
      _eventListeners: {},
      _isClosed: false,

      // 事件监听器管理
      addEventListener(type, listener) {
        if (!this._eventListeners[type]) {
          this._eventListeners[type] = [];
        }
        this._eventListeners[type].push(listener);
      },

      removeEventListener(type, listener) {
        if (this._eventListeners[type]) {
          const index = this._eventListeners[type].indexOf(listener);
          if (index > -1) {
            this._eventListeners[type].splice(index, 1);
          }
        }
      },

      dispatchEvent(type, data) {
        const event = { type, data, target: this };

        // 调用对应的 on* 处理器
        if (this[`on${type}`]) {
          this[`on${type}`](event);
        }

        // 调用通过 addEventListener 注册的监听器
        if (this._eventListeners[type]) {
          this._eventListeners[type].forEach(listener => {
            try {
              listener(event);
            } catch (error) {
              console.error(`SSE 事件监听器错误 (${type}):`, error);
            }
          });
        }
      },

      close() {
        if (this._isClosed) return;

        this._isClosed = true;
        this.readyState = CLOSED;

        // 触发关闭事件
        this.dispatchEvent('close', { reason: 'manual' });
      }
    };

    // 开始读取流数据
    const readStream = async () => {
      try {
        // 连接建立成功
        sseConnection.readyState = OPEN;
        sseConnection.dispatchEvent('open', {});

        while (!sseConnection._isClosed) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          // 解码数据块
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // 处理 SSE 消息
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保留最后一个不完整的行

          for (const line of lines) {
            this._processSSELine(sseConnection, line);
          }
        }
      } catch (error) {
        if (!sseConnection._isClosed) {
          console.error('SSE 流读取错误:', error);
          sseConnection.dispatchEvent('error', {
            message: error.message,
            error: error
          });
        }
      } finally {
        // 清理资源
        try {
          reader.releaseLock();
        } catch (e) {
          // 忽略释放锁的错误
        }

        if (!sseConnection._isClosed) {
          sseConnection.close();
        }
      }
    };

    // 异步开始读取
    readStream();

    return sseConnection;
  }

  /**
   * 处理 SSE 消息行
   * @private
   * @param {Object} sseConnection - SSE 连接对象
   * @param {string} line - 消息行
   */
  _processSSELine(sseConnection, line) {
    const trimmedLine = line.trim();

    if (!trimmedLine || trimmedLine.startsWith(':')) {
      // 空行或注释行，忽略
      return;
    }

    if (trimmedLine.startsWith('data: ')) {
      // 数据行
      const data = trimmedLine.substring(6);

      try {
        // 根据 URL 和加密配置判断是否需要解密数据
        let processedData = data;

        // 如果该接口需要加密处理，则尝试解密数据
        if (sseConnection._needsDecryption) {
          try {
            processedData = $utils.gatewayDecrypt(data);
            if (typeof processedData === 'object') {
              processedData = JSON.stringify(processedData);
            }
          } catch (decryptError) {
            // 解密失败，使用原始数据
            console.warn('SSE 数据解密失败，使用原始数据:', decryptError);
            processedData = data;
          }
        }

        // 触发消息事件
        sseConnection.dispatchEvent('message', processedData);
      } catch (error) {
        console.error('处理 SSE 数据失败:', error);
        sseConnection.dispatchEvent('error', {
          message: '数据处理失败',
          error: error
        });
      }
    } else if (trimmedLine.startsWith('event: ')) {
      // 事件类型行（暂时不处理，可以扩展）
      const eventType = trimmedLine.substring(7);
      console.debug('SSE 事件类型:', eventType);
    } else if (trimmedLine.startsWith('id: ')) {
      // 事件 ID 行（暂时不处理，可以扩展）
      const eventId = trimmedLine.substring(4);
      console.debug('SSE 事件 ID:', eventId);
    } else if (trimmedLine.startsWith('retry: ')) {
      // 重试间隔行（暂时不处理，可以扩展）
      const retryTime = trimmedLine.substring(7);
      console.debug('SSE 重试间隔:', retryTime);
    }
  }
}
