/*
 * @Author: couti<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-09 14:06:14
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2024-02-28 09:44:16
 * @FilePath: /funi-paas-cs-web-cli/src/apis/base/paas.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import FuniJS from '@funi-lib/utils';
import { ElNotification, ElMessageBox } from 'element-plus';
import { sm3 } from 'sm-crypto';

let abortController = new AbortController();

export class BaseApi extends FuniJS.Http {
  constructor() {
    super({
      baseURL: window.$utils.getServerBaseApi(),
      timeout: 60000
    });
  }

  static getInstance() {
    if (!BaseApi._instance) {
      BaseApi._instance = new BaseApi();
    }
    return BaseApi._instance;
  }

  //////////////////////////////////////////////////////////////////////////////////////////

  //axiox拦截处理begin

  //实现request拦截
  interceptorsRequest(config) {
    config.signal = config.signal || abortController.signal;
    const requestId = $utils.guid();
    config.headers['X-FuniPaas-Request-Id'] = requestId;
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json;charset=utf-8';
    config.headers['X-FuniPaas-AprId'] = localStorage.getItem('aprId'); //录制功能使用id,用于网管辨认接口行为是否需要录制
    if (!!sessionStorage.getItem('token')) {
      const configURL = new URL(config.url, location.origin);
      const digestStr = [configURL.pathname, requestId, sessionStorage.getItem('token')].join('$$');
      const signDigest = sm3(digestStr);
      config.headers['X-FuniPaas-Request-Hash'] = signDigest;
    }
    return config;
  }

  //实现response拦截
  interceptorsResponse(response) {
    if (response instanceof Blob) {
      return { data: response };
    } else if (![0, 200].includes(response.status) && response.success !== true) {
      return this.handleError(response);
    }
    return response;
  }

  handleError(response) {
    const respect = super.handleError(response);
    respect.catch(err => {
      if (['100001', '100002', '100003', '100004', '990001', '990002'].includes(err.code)) {
        abortController.abort();
        /* TODO 退出登录 并重定向登录页 临时处理为前端重定向  */
        ElMessageBox.confirm(err.message, err.phrase, {
          confirmButtonText: '知道了',
          type: 'warning',
          showClose: false,
          showCancelButton: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        }).then(() => {
          abortController = new AbortController();
          const { protocol, host, pathname } = window.location;
          let loginUrl = `${protocol}//${host}/${pathname.split('/')[1]}/#/`;
          // if(FuniJS.isProduction())
          loginUrl = loginUrl.replace('/#/', '/casapp/#/');
          sessionStorage.removeItem('sysCode');
          sessionStorage.removeItem('token');
          window.location.href = loginUrl;
          // window.location.reload();
        });
      } else if ([404, 502, 503].includes(err.status)) {
        let ops = {
          title: `Error(${err.status})`,
          message: err.message,
          type: 'error'
        };
        ElNotification(ops);
      } else {
        let ops = {
          title: err.phrase ? (err.code ? `${err.phrase}(${err.code})` : err.phrase) : err.message,
          type: 'error'
        };
        if (err.phrase) ops.message = err.message;
        ElNotification(ops);
      }
    });
    return respect;
  }

  //axiox拦截处理end

  /////////////////////////////////////////////////////////////////////////////////
}
