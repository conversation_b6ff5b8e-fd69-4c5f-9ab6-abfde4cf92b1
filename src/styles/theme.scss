// * card 卡片样式
.card {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-fill-color-blank);
  // border: 1px solid var(--el-border-color-light);
  // border-radius: 4px;
  // box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.07);
}

/* 滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: rgba(144, 147, 153, 0.3);
}

/* 滑块hover */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144, 147, 153, 0.5);
}
/* 滚动条 */
