@use 'element-plus/theme-chalk/src/mixins/config';

// getCssVarName('button', 'text-color') => '--button-text-color'
@function getCssVarName($args...) {
  @return joinVarName($args);
}

// getCssVar('button', 'text-color') => var(--button-text-color)
@function getCssVar($args...) {
  @return var(#{joinVarName($args)});
}

// getCssVarWithDefault(('button', 'text-color'), red) => var(--button-text-color, red)
@function getCssVarWithDefault($args, $default) {
  @return var(#{joinVarName($args)}, #{$default});
}

// join var name
// joinVarName(('button', 'text-color')) => '--button-text-color'
@function joinVarName($list) {
  $name: '';
  @each $item in $list {
    @if $item != '' {
      @if $name == '' {
        $name: '--' + $item;
      } @else {
        $name: $name + '-' + $item;
      }
    }
  }
  @return $name;
}

// bem('block', 'element', 'modifier') => 'el-block__element--modifier'
@function bem($block, $element: '', $modifier: '') {
  $name: 'funi' + config.$common-separator + $block;

  @if $element != '' {
    $name: $name + config.$element-separator + $element;
  }

  @if $modifier != '' {
    $name: $name + config.$modifier-separator + $modifier;
  }

  // @debug $name;
  @return $name;
}
