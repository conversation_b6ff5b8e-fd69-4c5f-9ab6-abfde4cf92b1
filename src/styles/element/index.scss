@use './var' as funi;
@use 'sass:meta';

@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: funi.$colors,
  $text-color: funi.$text-color,
  $border-color: funi.$border-color,
  $fill-color: funi.$fill-color,
  $bg-color: funi.$bg-color,
  $mask-color: funi.$mask-color,
  $overlay-color: funi.$overlay-color,

  $font-family: funi.$font-family,
  $font-size: funi.$font-size,

  $box-shadow: funi.$box-shadow,
  $table: funi.$table
);

@use 'element-plus/theme-chalk/src/index.scss' as *;
@use 'element-plus/theme-chalk/src/mixins/var' as *;
@use 'element-plus/theme-chalk/src/mixins/mixins' as *;

:root {
  // Nav --el-nav-color-#{$type}
  @include set-component-css-var('nav-color', funi.$nav-color);
  @include set-css-var-value('font-weight-primary', 400);

  @include b(notification) {
    @include e(content) {
      p {
        word-break: break-all;
      }
    }
  }

  @include b(table) {
    thead th {
      font-weight: 500;
    }
    tbody td {
      font-weight: 400;
    }
  }
}
