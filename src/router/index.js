/*
 * @Author: co<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-25 23:45:15
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2025-07-18 10:38:53
 * @FilePath: /src/router/index.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { createRouter, createWebHashHistory } from 'vue-router';
import routers from './routes/index.js';
const constantRouterMap = [...routers];

const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRouterMap
});

export default router;
