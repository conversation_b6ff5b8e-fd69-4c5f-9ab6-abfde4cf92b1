/*
 * @Author: 古加文 <EMAIL>
 * @Date: 2023-11-08 13:50:30
 * @LastEditors: 古加文 <EMAIL>
 * @LastEditTime: 2023-11-08 14:08:46
 * @FilePath: \funi-paas-cs-web-cli\src\router\routes\common.js
 * @Description: 公共路由
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
export default {
  path: '/common',
  redirect: '/common/filePreview',
  name: 'Common',
  meta: {
    title: '公共页面'
  },
  children: [{
    path: '/common/filePreview',
    name: 'FilePreview',
    meta: {
      title: '文件预览'
    },
    component: () => import('@/components/FuniFilePreview/index.vue')
  }]
};
