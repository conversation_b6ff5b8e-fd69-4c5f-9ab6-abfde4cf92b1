/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-02-09 14:06:15
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-11 12:18:24
 * @FilePath: /funi-paas-cs-web-cli/src/router/routes/components.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
export default {
  path: '/components',
  redirect: '/components/curd',
  name: 'Components',
  meta: { title: '组件' },
  children: [
    {
      path: '/components/curd',
      name: 'CurdComponent',
      meta: { title: 'FuniCurd' },
      component: () => import('@/apps/components/curd.vue')
    },
    {
      path: '/components/form-demo',
      name: 'FormComponentDemo',
      meta: { title: 'FuniForm' },
      component: () => import('@/apps/components/form-demo.vue')
    },
    {
      path: '/components/list-page',
      name: 'FuniListPageDemo',
      meta: { title: 'FuniListPage' },
      component: () => import('@/apps/components/list-page.vue')
    },
    {
      path: '/components/icon',
      name: 'FuniIconDemo',
      meta: { title: '图标' },
      component: () => import('@/apps/components/icon.vue')
    },
    {
      path: '/components/table',
      name: 'ElTableDemo',
      meta: { title: 'Table' },
      component: () => import('@/apps/components/table.vue')
    },
    {
      path: '/components/codemirror',
      name: 'CodemirrorDemo',
      meta: { title: 'Codemirror' },
      component: () => import('@/apps/components/codemirror.vue')
    },
    {
      path: '/components/urc',
      name: 'urcDemo',
      meta: { title: 'urc' },
      component: () => import('@/apps/components/urc.vue')
    },
    {
      path: '/components/region',
      name: 'regionDemo',
      meta: { title: 'region' },
      component: () => import('@/apps/components/region.vue')
    }
  ]
};
