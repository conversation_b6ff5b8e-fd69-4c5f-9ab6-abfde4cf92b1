/*
 * @Author: 罗前 <EMAIL>
 * @Date: 2022-11-23 09:59:47
 * @LastEditors: fengyi <EMAIL>
 * @LastEditTime: 2023-09-13 11:07:34
 * @FilePath: \funi-paas-cs-web-cli\src\router\routes\demo.js
 * @Description: 仅用于处理demo路由
 * Copyright (c) 2022 by 罗前 <EMAIL>, All Rights Reserved.
 */

export default {
  path: '/demo',
  redirect: '/dashboard/analysis',
  name: 'Demo',
  meta: { title: 'Demo' },
  children: [
    {
      path: '/demo/detail',
      name: 'FuniDetailDemo',
      meta: { title: '详情组件' },
      children: [
        {
          path: '/demo/detail/funi-detail',
          component: () => import('@/apps/demo/FuniDetailDemo/index.vue'),
          name: 'FuniDetail',
          meta: { title: '详情组件' }
        }
      ]
    },
    {
      path: '/demo/cacheDemo',
      name: 'CacheDemo',
      meta: { title: '测试缓存' },
      children: [
        {
          path: '/demo/cacheDemo/index',
          component: () => import('@/apps/demo/CacheDemo/index.vue'),
          name: 'CacheDemoIndex',
          meta: { title: '测试缓存列表' }
        },
        {
          path: '/demo/cacheDemo/detail/:businessId',
          component: () => import('@/apps/demo/CacheDemo/detail.vue'),
          name: 'CacheDemoDetail',
          meta: { title: '测试缓存详情', hidden: true }
        }
      ]
    },
    {
      path: '/demo/FuniFormMobileDemo',
      name: 'FuniFormMobileDemo',
      meta: { title: '表单测试组件' },
      component: () => import('@/apps/demo/FuniFormMobileDemo/index.vue')
    },
    {
      path: '/demo/FuniDetailMobileDemo',
      name: 'FuniDetailMobileDemo',
      meta: { title: '详情测试组件' },
      component: () => import('@/apps/demo/FuniDetailMobileDemo/index.vue')
    },
    {
      path: '/demo/FuniFormMobileSlotDemo',
      name: 'FuniFormMobileSlotDemo',
      meta: { title: '表单组件插槽测试' },
      component: () => import('@/apps/demo/FuniFormMobileSlotDemo/index.vue')
    },
    {
      path: '/demo/FuniMoneyInput',
      name: 'FuniMoneyInput',
      meta: { title: '表单组件插槽测试' },
      component: () => import('@/apps/demo/FuniMoneyInput/index.vue')
    },
    {
      path: '/demo/FuniOl',
      name: 'FuniOl',
      meta: { title: '地图' },
      component: () => import('@/apps/demo/FuniOlDemo/index.vue')
    }
  ]
};
