# Funi框架HTML原型入口文件生成提示词

## 文件说明

本文件`docs/prompts/pm/start.md`旨在提供生成Funi管理系统HTML原型入口文件的提示词。该入口文件将作为整个原型系统的基础，包含核心布局、导航菜单和主题切换功能。

## 任务描述

请生成一个名为`docs/pm/framework/index.html`的HTML入口文件，作为Funi管理系统的PC端管理系统原型。该文件应基于`docs/pm/framework/base-template.html`模板，并集成以下核心功能：

1.  **整体布局**: 采用`base-template.html`中定义的Funi布局容器（`.funi-layout`）、头部区域（`.funi-header`）、侧边栏（`.funi-sidebar`）和内容区域（`.funi-main`）。
2.  **导航菜单**:
    - 在侧边栏的`<nav class="funi-menu">`中生成完整的导航菜单。
    - 菜单数据应通过执行`@docs/prompts/pm/core/generate-menu.md`来获取和构建。请确保生成的菜单符合`generate-menu.md`中定义的HTML结构要求（一级菜单、二级菜单、图标、激活状态等）。
    - 菜单应具备折叠/展开功能，并与`base-template.html`中引入的`funi-interactions.js`脚本兼容。
3.  **主题切换**:
    - 在头部区域集成主题切换按钮，并确保其功能与`base-template.html`中引入的`funi-theme-switcher.js`脚本兼容。
4.  **样式和脚本引用**: 确保`index.html`正确引用了`base-template.html`中声明的所有CSS文件和JavaScript文件。请注意路径的正确性，例如：
    - CSS: `assets/css/funi-framework.css`, `assets/css/funi-components.css`, `assets/css/funi-themes.css`
    - JS: `assets/js/funi-theme-switcher.js`, `assets/js/funi-interactions.js`

### 最终输出

请将生成的完整`docs/pm/framework/index.html`文件内容直接输出。
