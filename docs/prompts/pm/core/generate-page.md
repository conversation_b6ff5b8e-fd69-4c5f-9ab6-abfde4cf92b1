## 生成Funi管理系统页面提示词

### 任务描述

根据Funi管理系统的菜单结构，为指定的模块或菜单项生成对应的HTML页面。

**指令识别**: 生成页面的指令格式为 `生成页面-[模块名称或菜单名称]`。

- 如果提供的是**模块名称**（例如：“采购执行管理”），则生成该模块下所有子菜单对应的页面。
- 如果提供的是**菜单名称**（例如：“工作台”或“项目标段管理”），则只生成当前菜单对应的页面。

每个菜单项默认应包含以下三种页面类型：

1.  **列表页面 (List Page)**: 用于展示数据列表和提供查询、新增等操作。
    - **模板地址**: `docs/pm/framework/templates/list-page-template.html`
2.  **新增/编辑页面 (Add/Edit Page)**: 用于创建新数据或编辑现有数据。
3.  **详情/审核页面 (Detail/Review Page)**: 用于展示单条数据的详细信息和提供审核操作。

所有生成的页面文件都应放置在`docs/pm/framework/pages/`目录下，并根据其菜单路径和页面类型命名。

### 页面生成规则

1.  **需求文档解析**:
    - 必须首先解析`docs/PRD.md`文件，特别是其中的“系统功能菜单结构”部分，以获取完整的模块和菜单层级关系。
    - 同时，从`docs/PRD.md`的“第三部分：PC端管理系统功能规格”中提取每个菜单的详细功能描述（查询功能、列表功能、新建/编辑页面、详情页面等）。

2.  **菜单结构获取 (从 index.html)**:
    - 页面生成还需结合`docs/pm/framework/index.html`文件中的菜单结构。
    - 从`index.html`中解析出所有的菜单项及其对应的`href`属性（hash路由）和`span`标签中的文本（菜单名称）。
    - 对于模块（`funi-menu-group`），其名称通过`funi-menu-group-title`下的`span`标签获取。

3.  **模块/菜单匹配与类型判断**:
    - 根据用户提供的`[模块名称或菜单名称]`，首先在`docs/PRD.md`解析出的菜单结构中进行匹配，判断其是模块（一级菜单，如“采购执行管理”）还是具体菜单（二级或三级菜单，如“工作台”或“项目标段管理”）。
    - 如果是模块名称，则循环生成该模块下所有子菜单对应的页面。
    - 如果是菜单名称，则只生成当前菜单对应的页面。
    - **如果未找到匹配的模块名称或菜单名称，请直接结束任务并提示：“没有找到相关菜单或模块。”**

4.  **页面路径**:
    - 页面路径应基于菜单项的`path`字段（已在`docs/prompts/pm/core/generate-menu.md`中定义为hash路由）。
    - 例如，如果菜单项的path是`#/dashboard`，则其列表页面路径为`docs/pm/framework/pages/dashboard/list.html`。
    - 如果菜单项的path是`#/procurement-execution/project-bid-management`，则其列表页面路径为`docs/pm/framework/pages/procurement-execution/project-bid-management/list.html`。
    - **注意菜单名称和页面名称的对应以及hash名称的设计，确保它们逻辑一致且易于理解。**

5.  **页面命名**:
    - 列表页面: `{menu-path-segment}/list.html`
    - 新增/编辑页面: `{menu-path-segment}/add-edit.html`
    - 详情/审核页面: `{menu-path-segment}/detail-review.html`
    - 其中`{menu-path-segment}`是菜单项的路径（不包含`#`前缀），并转换为文件系统路径（例如，`/procurement-execution/project-bid-management` 转换为 `procurement-execution/project-bid-management`）。

6.  **页面内容生成**:
    - **必须完全使用原生的HTML、CSS和JavaScript，禁止使用任何前端框架（如Vue、React）或自定义组件（如`funi-`开头的组件）。**
    - 每个页面应包含基本的HTML结构，并引用`docs/pm/framework/base-template.html`中的CSS和JS文件。
    - 页面内容应包含一个简单的标题，指示当前页面类型和所属菜单。
    - 页面应为独立的HTML文件，不包含整体布局（如头部、侧边栏），因为它们将在主`index.html`的iframe中加载。
    - **页面内容的主体应直接包含在`<div id="app" class="container">`中，而不是额外的`funi-page-content`包裹。**
    - **根据`docs/PRD.md`中该菜单的详细描述，生成对应的代码结构，并使用原生HTML元素实现：**
      - **查询功能**: 对应页面上的搜索区域，使用`div`、`input`、`select`、`button`等原生HTML元素构建表单，并使用CSS进行布局和样式。
      - **列表功能**:
        - **视图**: 使用`div`和`button`或`a`标签模拟tab页签，通过JavaScript控制显示隐藏。
        - **列表字段**: 使用`table`、`thead`、`tbody`、`tr`、`th`、`td`等原生HTML元素构建表格。
        - **列表操作**: 使用`button`或`a`标签作为操作按钮，通过JavaScript实现交互逻辑。
      - **新建/编辑页面**: 使用`form`、`input`、`select`、`textarea`、`label`等原生HTML元素构建表单。
      - **详情页面**: 使用`div`、`p`、`span`等原生HTML元素展示详细信息，并使用CSS进行布局。
    - 示例页面内容（以“工作台”的列表页面为例，将更详细）：

    ```html
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>工作台 - 列表 - Funi管理系统</title>
        <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
        <link rel="stylesheet" href="../../assets/css/funi-components.css" />
        <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
        <link rel="stylesheet" href="../../assets/css/funi-list.css" />
        <!-- Added funi-list.css -->
      </head>
      <body>
        <div id="app" class="container">
          <!-- 根据PRD.md中“工作台”的“我的待办”查询功能生成搜索区域 -->
          <div class="search-area collapsed">
            <!-- Changed class and added 'collapsed' -->
            <form class="search-form">
              <div class="search-form-item">
                <label for="titleName">标题名称:</label>
                <input type="text" id="titleName" name="titleName" placeholder="请输入标题名称" />
              </div>
              <div class="search-form-item">
                <label for="createTimeStart">创建时间:</label>
                <div class="date-range-picker">
                  <input type="date" id="createTimeStart" name="createTimeStart" />
                  <span>~</span>
                  <input type="date" id="createTimeEnd" name="createTimeEnd" />
                </div>
              </div>
              <div class="search-form-item search-buttons-item">
                <!-- Added search-form-item and search-buttons-item -->
                <button type="button" class="button primary">查询</button>
                <button type="button" class="button">重置</button>
                <button type="button" class="button text" id="toggleCollapseButton">高级查询</button>
                <!-- Changed text to '高级查询' and added ID -->
              </div>
            </form>
          </div>
          <style>
            .search-area.collapsed .search-form {
              grid-template-columns: repeat(4, 1fr); /* 4 columns when collapsed (default state) */
            }
            .search-area.collapsed .search-form-item {
              display: none; /* Hide all items by default when collapsed */
              grid-column: span 1; /* Ensure they take 1 column if shown */
            }
            .search-area.collapsed .search-form-item:nth-child(1),
            .search-area.collapsed .search-form-item:nth-child(2),
            .search-area.collapsed .search-form-item:nth-child(3),
            .search-area.collapsed .search-form-item.search-buttons-item {
              display: flex; /* Show first three and the buttons item */
            }
            .search-form-item.search-buttons-item {
              grid-column: span 1; /* Align with other 3-column items */
              justify-content: flex-end; /* Align buttons to the right */
              margin-left: auto; /* Push to the right */
              margin-bottom: 15px; /* Keep consistent margin */
            }
          </style>
          <style>
            /* Theming for example page */
            .container {
                background-color: var(--funi-background-color-light);
            }
            .search-area {
                background-color: var(--funi-background-color-light);
            }
            .tabs {
                border-bottom: 1px solid var(--funi-border-color-light);
            }
            .tab-item {
                color: var(--funi-text-color-secondary);
            }
            .tab-item.active {
                color: var(--funi-primary-color);
                border-bottom: 2px solid var(--funi-primary-color);
            }
            .tab-item:hover {
                color: var(--funi-primary-color);
            }
            .search-form-item label {
                color: var(--funi-text-color-regular);
            }
            .search-form-item input,
            .search-form-item select {
                border: 1px solid var(--funi-border-color-base);
                color: var(--funi-text-color-regular);
            }
            .search-form-item input:focus,
            .search-form-item select:focus {
                border-color: var(--funi-primary-color);
            }
            .button {
                border: 1px solid var(--funi-border-color-base);
                background-color: var(--funi-background-color-light);
                color: var(--funi-text-color-regular);
            }
            .button.primary {
                background-color: var(--funi-primary-color);
                border-color: var(--funi-primary-color);
            }
            .button.text {
                color: var(--funi-primary-color);
            }
            .table-container {
                border: 1px solid var(--funi-border-color-lighter);
            }
            .data-table th,
            .data-table td {
                border-bottom: 1px solid var(--funi-border-color-lighter);
                color: var(--funi-text-color-regular);
            }
            .data-table th {
                background-color: var(--funi-background-color-base);
                color: var(--funi-text-color-secondary);
            }
            .data-table tbody tr:hover {
                background-color: var(--funi-background-color-base);
            }
            .pagination-container {
                border-top: 1px solid var(--funi-border-color-lighter);
                background-color: var(--funi-background-color-light);
            }
            .pagination-container span {
                color: var(--funi-text-color-regular);
            }
            .pagination-container select {
                border: 1px solid var(--funi-border-color-base);
            }
            .pagination-container .page-buttons button {
                border: 1px solid var(--funi-border-color-base);
                background-color: var(--funi-background-color-light);
                color: var(--funi-text-color-regular);
            }
            .pagination-container .page-buttons button.active {
                background-color: var(--funi-primary-color);
                border-color: var(--funi-primary-color);
            }
          </style>
          </style>

          <!-- 根据PRD.md中“工作台”的“我的待办”列表功能生成Tab页签和列表 -->
          <div class="tabs">
            <!-- Changed class to 'tabs' -->
            <div class="tab-item active" data-tab="myTodo">我的待办</div>
            <!-- Changed class to 'tab-item' -->
            <div class="tab-item" data-tab="notifications">通知公告</div>
            <!-- Changed class to 'tab-item' -->
          </div>
          <div class="table-container">
            <!-- Changed class to 'table-container' -->
            <table class="data-table">
              <!-- Changed class to 'data-table' -->
              <thead>
                <tr>
                  <th>标题名称</th>
                  <th>所属模块</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody id="tableBody">
                <tr>
                  <td>示例待办标题</td>
                  <td>示例模块</td>
                  <td>2023-01-01 10:00:00</td>
                  <td><button class="button text">去处理</button></td>
                  <!-- Changed class to 'button text' -->
                </tr>
              </tbody>
            </table>
          </div>
          <!-- Pagination component placeholder -->
          <div class="pagination-container">
            <span>总共 <span id="totalItems">0</span> 条</span>
            <select id="pageSizeSelect">
              <option value="10">10 条/页</option>
              <option value="20">20 条/页</option>
              <option value="50">50 条/页</option>
              <option value="100">100 条/页</option>
            </select>
            <div class="page-buttons">
              <button id="prevPageButton" disabled>上一页</button>
              <span id="currentPageSpan">1</span>
              <button id="nextPageButton">下一页</button>
            </div>
          </div>
        </div>
        <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
        <script src="../../assets/js/funi-theme-switcher.js"></script>
        <script src="../../assets/js/funi-interactions.js"></script>
        <script>
          document.addEventListener('DOMContentLoaded', () => {
            const tabs = document.querySelectorAll('.tab-item'); // Changed selector
            const searchArea = document.querySelector('.search-area'); // Added searchArea
            const toggleCollapseButton = document.getElementById('toggleCollapseButton'); // Added ID

            let isCollapsed = true; // Default to collapsed

            // Initial state for the search area
            const searchArea = document.querySelector('.search-area');
            if (isCollapsed) {
              searchArea.classList.add('collapsed');
              toggleCollapseButton.textContent = '高级查询';
            } else {
              searchArea.classList.remove('collapsed');
              toggleCollapseButton.textContent = '收起';
            }

            tabs.forEach(tab => {
              tab.addEventListener('click', () => {
                tabs.forEach(btn => btn.classList.remove('active')); // Changed class
                tab.classList.add('active'); // Changed class
                // Add logic to switch tab content if needed
              });
            });

            if (toggleCollapseButton && searchArea) {
              toggleCollapseButton.addEventListener('click', () => {
                isCollapsed = !isCollapsed;
                if (isCollapsed) {
                  searchArea.classList.add('collapsed');
                  toggleCollapseButton.textContent = '高级查询';
                } else {
                  searchArea.classList.remove('collapsed');
                  toggleCollapseButton.textContent = '收起';
                }
              });
            }
          });
        </script>
      </body>
    </html>

    - 注意CSS和JS文件的相对路径调整（相对于`docs/pm/framework/pages/{menu-path-segment}/`）。
    ```

### 最终输出

请将生成的完整`docs/prompts/pm/core/generate-page.md`文件内容直接输出。
