# FuniIcon 使用示例

## 基础示例

### 1. 基础图标显示
```vue
<template>
  <div class="example-container">
    <h3>基础图标显示</h3>
    <div class="icon-grid">
      <!-- ElementPlus 图标 -->
      <div class="icon-group">
        <h4>ElementPlus 图标</h4>
        <div class="icon-list">
          <FuniIcon name="Edit" size="20" />
          <FuniIcon name="Delete" size="20" color="#f56c6c" />
          <FuniIcon name="Search" size="20" color="#409eff" />
          <FuniIcon name="Plus" size="20" color="#67c23a" />
          <FuniIcon name="Setting" size="20" color="#e6a23c" />
        </div>
      </div>
      
      <!-- 不同尺寸 -->
      <div class="icon-group">
        <h4>不同尺寸</h4>
        <div class="icon-list">
          <FuniIcon name="Star" size="12" />
          <FuniIcon name="Star" size="16" />
          <FuniIcon name="Star" size="20" />
          <FuniIcon name="Star" size="24" />
          <FuniIcon name="Star" size="32" />
        </div>
      </div>
      
      <!-- 不同颜色 -->
      <div class="icon-group">
        <h4>不同颜色</h4>
        <div class="icon-list">
          <FuniIcon name="Heart" color="#f56c6c" size="24" />
          <FuniIcon name="Heart" color="#e6a23c" size="24" />
          <FuniIcon name="Heart" color="#67c23a" size="24" />
          <FuniIcon name="Heart" color="#409eff" size="24" />
          <FuniIcon name="Heart" color="#909399" size="24" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.example-container {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 20px;
}

.icon-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.icon-group h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}

.icon-list {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}
</style>
```

### 2. 动画效果图标
```vue
<template>
  <div class="example-container">
    <h3>动画效果图标</h3>
    <div class="animation-examples">
      <!-- 旋转动画 -->
      <div class="animation-group">
        <h4>旋转动画</h4>
        <div class="animation-list">
          <FuniIcon name="Loading" :spin="true" size="24" color="#409eff" />
          <FuniIcon name="Refresh" :spin="spinEnabled" size="24" color="#67c23a" />
          <el-button size="small" @click="toggleSpin">
            {{ spinEnabled ? '停止旋转' : '开始旋转' }}
          </el-button>
        </div>
      </div>
      
      <!-- 脉冲动画 -->
      <div class="animation-group">
        <h4>脉冲动画</h4>
        <div class="animation-list">
          <FuniIcon name="Bell" :pulse="true" size="24" color="#e6a23c" />
          <FuniIcon name="Message" :pulse="pulseEnabled" size="24" color="#f56c6c" />
          <el-button size="small" @click="togglePulse">
            {{ pulseEnabled ? '停止脉冲' : '开始脉冲' }}
          </el-button>
        </div>
      </div>
      
      <!-- 翻转效果 -->
      <div class="animation-group">
        <h4>翻转效果</h4>
        <div class="animation-list">
          <FuniIcon name="ArrowRight" flip="horizontal" size="24" />
          <FuniIcon name="ArrowDown" flip="vertical" size="24" />
          <FuniIcon name="Refresh" flip="both" size="24" />
        </div>
      </div>
      
      <!-- 旋转角度 -->
      <div class="animation-group">
        <h4>旋转角度</h4>
        <div class="animation-list">
          <FuniIcon name="ArrowRight" :rotate="0" size="24" />
          <FuniIcon name="ArrowRight" :rotate="90" size="24" />
          <FuniIcon name="ArrowRight" :rotate="180" size="24" />
          <FuniIcon name="ArrowRight" :rotate="270" size="24" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const spinEnabled = ref(false)
const pulseEnabled = ref(false)

const toggleSpin = () => {
  spinEnabled.value = !spinEnabled.value
}

const togglePulse = () => {
  pulseEnabled.value = !pulseEnabled.value
}
</script>

<style scoped>
.animation-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.animation-group h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}

.animation-list {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}
</style>
```

### 3. 带背景和边框的图标
```vue
<template>
  <div class="example-container">
    <h3>带背景和边框的图标</h3>
    <div class="styled-examples">
      <!-- 圆形背景 -->
      <div class="style-group">
        <h4>圆形背景</h4>
        <div class="style-list">
          <FuniIcon 
            name="User" 
            :circle="true"
            background="#409eff" 
            color="white" 
            size="20" 
            :padding="8"
          />
          <FuniIcon 
            name="Setting" 
            :circle="true"
            background="#67c23a" 
            color="white" 
            size="20" 
            :padding="8"
          />
          <FuniIcon 
            name="Warning" 
            :circle="true"
            background="#e6a23c" 
            color="white" 
            size="20" 
            :padding="8"
          />
          <FuniIcon 
            name="Close" 
            :circle="true"
            background="#f56c6c" 
            color="white" 
            size="20" 
            :padding="8"
          />
        </div>
      </div>
      
      <!-- 方形背景 -->
      <div class="style-group">
        <h4>方形背景</h4>
        <div class="style-list">
          <FuniIcon 
            name="Document" 
            :square="true"
            background="#f4f4f5" 
            color="#409eff" 
            size="20" 
            :padding="8"
            border-radius="4px"
          />
          <FuniIcon 
            name="Folder" 
            :square="true"
            background="#f4f4f5" 
            color="#67c23a" 
            size="20" 
            :padding="8"
            border-radius="4px"
          />
          <FuniIcon 
            name="Picture" 
            :square="true"
            background="#f4f4f5" 
            color="#e6a23c" 
            size="20" 
            :padding="8"
            border-radius="4px"
          />
        </div>
      </div>
      
      <!-- 边框样式 -->
      <div class="style-group">
        <h4>边框样式</h4>
        <div class="style-list">
          <FuniIcon 
            name="Edit" 
            :border="true"
            color="#409eff" 
            size="20" 
            :padding="8"
          />
          <FuniIcon 
            name="Delete" 
            :border="true"
            color="#f56c6c" 
            size="20" 
            :padding="8"
          />
          <FuniIcon 
            name="View" 
            :border="true"
            color="#67c23a" 
            size="20" 
            :padding="8"
          />
        </div>
      </div>
      
      <!-- 渐变背景 -->
      <div class="style-group">
        <h4>渐变背景</h4>
        <div class="style-list">
          <FuniIcon 
            name="Star" 
            :circle="true"
            background="linear-gradient(45deg, #409eff, #67c23a)" 
            color="white" 
            size="20" 
            :padding="8"
          />
          <FuniIcon 
            name="Heart" 
            :circle="true"
            background="linear-gradient(45deg, #f56c6c, #e6a23c)" 
            color="white" 
            size="20" 
            :padding="8"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.styled-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.style-group h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}

.style-list {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}
</style>
```

## 高级示例

### 4. 可点击图标
```vue
<template>
  <div class="example-container">
    <h3>可点击图标</h3>
    <div class="clickable-examples">
      <!-- 操作按钮 -->
      <div class="action-group">
        <h4>操作按钮</h4>
        <div class="action-list">
          <FuniIcon
            name="Edit"
            :clickable="true"
            cursor="pointer"
            size="20"
            color="#409eff"
            @click="handleEdit"
          />
          <FuniIcon
            name="Delete"
            :clickable="true"
            cursor="pointer"
            size="20"
            color="#f56c6c"
            @click="handleDelete"
          />
          <FuniIcon
            name="View"
            :clickable="true"
            cursor="pointer"
            size="20"
            color="#67c23a"
            @click="handleView"
          />
        </div>
      </div>

      <!-- 切换状态 -->
      <div class="action-group">
        <h4>切换状态</h4>
        <div class="action-list">
          <FuniIcon
            :name="favoriteIcon"
            :clickable="true"
            cursor="pointer"
            size="24"
            :color="isFavorite ? '#f56c6c' : '#c0c4cc'"
            @click="toggleFavorite"
          />
          <span>{{ isFavorite ? '已收藏' : '未收藏' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const isFavorite = ref(false)
const favoriteIcon = computed(() => isFavorite.value ? 'StarFilled' : 'Star')

const handleEdit = () => {
  ElMessage.success('编辑操作')
}

const handleDelete = () => {
  ElMessage.warning('删除操作')
}

const handleView = () => {
  ElMessage.info('查看操作')
}

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value
  ElMessage.success(isFavorite.value ? '已添加到收藏' : '已取消收藏')
}
</script>

<style scoped>
.clickable-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-group h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}

.action-list {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}
</style>
```

### 5. 带徽章的图标
```vue
<template>
  <div class="example-container">
    <h3>带徽章的图标</h3>
    <div class="badge-examples">
      <!-- 数字徽章 -->
      <div class="badge-group">
        <h4>数字徽章</h4>
        <div class="badge-list">
          <FuniIcon
            name="Message"
            :badge="messageCount"
            badge-type="danger"
            size="24"
            color="#409eff"
          />
          <FuniIcon
            name="Bell"
            badge="99+"
            badge-type="warning"
            size="24"
            color="#e6a23c"
          />
          <FuniIcon
            name="ShoppingCart"
            :badge="cartCount"
            :badge-max="99"
            badge-type="success"
            size="24"
            color="#67c23a"
          />
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="badge-group">
        <h4>控制操作</h4>
        <div class="badge-controls">
          <el-button size="small" @click="addMessage">新消息 (+1)</el-button>
          <el-button size="small" @click="clearMessages">清空消息</el-button>
          <el-button size="small" @click="addToCart">添加到购物车</el-button>
          <el-button size="small" @click="clearCart">清空购物车</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const messageCount = ref(5)
const cartCount = ref(3)

const addMessage = () => {
  messageCount.value++
}

const clearMessages = () => {
  messageCount.value = 0
}

const addToCart = () => {
  cartCount.value++
}

const clearCart = () => {
  cartCount.value = 0
}
</script>

<style scoped>
.badge-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.badge-group h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}

.badge-list {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.badge-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
</style>
```

## 注意事项

### 使用建议
1. **图标选择**：选择语义明确、风格一致的图标
2. **尺寸规范**：建立统一的图标尺寸体系
3. **颜色使用**：遵循设计系统的颜色规范
4. **动画效果**：适度使用，避免过于频繁

### 性能优化
1. **图标库管理**：按需加载图标库
2. **SVG优化**：使用优化过的SVG图标
3. **缓存策略**：合理使用图标缓存
4. **懒加载**：对于大量图标考虑懒加载

### 无障碍访问
1. **语义化**：为图标提供合适的语义
2. **键盘导航**：支持键盘操作
3. **屏幕阅读器**：提供适当的标签
4. **对比度**：确保足够的颜色对比度
