# FuniIcon 配置结构定义

## 基础配置结构

### FuniIconProps 接口定义

```typescript
interface FuniIconProps {
  // 基础配置
  name?: string
  type?: 'element' | 'svg' | 'font' | 'image'
  size?: string | number
  color?: string
  
  // 图标源配置
  prefix?: string
  src?: string
  svg?: string
  
  // 动画配置
  spin?: boolean
  pulse?: boolean
  flip?: 'horizontal' | 'vertical' | 'both'
  rotate?: number
  
  // 样式配置
  border?: boolean
  circle?: boolean
  square?: boolean
  background?: string
  padding?: string | number
  borderRadius?: string | number
  
  // 交互配置
  clickable?: boolean
  disabled?: boolean
  loading?: boolean
  cursor?: string
  
  // 徽章配置
  badge?: string | number
  badgeType?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  badgeMax?: number
  
  // 提示配置
  tooltip?: string
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right'
  
  // ElementPlus el-icon 透传属性
  // 继承所有 el-icon 的属性
}
```

### 图标类型枚举

```typescript
enum IconType {
  ELEMENT = 'element',    // ElementPlus 图标
  SVG = 'svg',           // 自定义 SVG 图标
  FONT = 'font',         // 字体图标
  IMAGE = 'image'        // 图片图标
}

interface IconTypeConfig {
  type: IconType
  description: string
  usage: string
  example: string
}

const ICON_TYPE_CONFIG: Record<IconType, IconTypeConfig> = {
  [IconType.ELEMENT]: {
    type: IconType.ELEMENT,
    description: 'ElementPlus 内置图标库',
    usage: '使用 ElementPlus 提供的图标组件',
    example: '<FuniIcon name="Edit" type="element" />'
  },
  [IconType.SVG]: {
    type: IconType.SVG,
    description: '自定义 SVG 图标',
    usage: '直接传入 SVG 字符串内容',
    example: '<FuniIcon type="svg" svg="<svg>...</svg>" />'
  },
  [IconType.FONT]: {
    type: IconType.FONT,
    description: '字体图标库',
    usage: '使用 Font Awesome、Iconfont 等字体图标',
    example: '<FuniIcon name="home" type="font" prefix="fa fa" />'
  },
  [IconType.IMAGE]: {
    type: IconType.IMAGE,
    description: '图片图标',
    usage: '使用图片文件作为图标',
    example: '<FuniIcon type="image" src="/icon.png" />'
  }
}
```

### 动画配置接口

```typescript
interface AnimationConfig {
  // 旋转动画
  spin?: boolean
  
  // 脉冲动画
  pulse?: boolean
  
  // 翻转配置
  flip?: FlipDirection
  
  // 旋转角度
  rotate?: number
  
  // 动画持续时间
  duration?: string
  
  // 动画延迟
  delay?: string
  
  // 动画缓动函数
  timingFunction?: string
}

type FlipDirection = 'horizontal' | 'vertical' | 'both'
```

### 样式配置接口

```typescript
interface StyleConfig {
  // 基础样式
  background?: string
  color?: string
  border?: boolean
  borderColor?: string
  borderWidth?: string | number
  borderStyle?: string
  
  // 形状配置
  circle?: boolean
  square?: boolean
  borderRadius?: string | number
  
  // 尺寸配置
  size?: string | number
  padding?: string | number
  margin?: string | number
  
  // 阴影配置
  shadow?: boolean
  shadowColor?: string
  shadowBlur?: number
  shadowOffset?: string
}
```

### 徽章配置接口

```typescript
interface BadgeConfig {
  // 徽章内容
  content?: string | number
  
  // 徽章类型
  type?: BadgeType
  
  // 最大值
  max?: number
  
  // 是否显示
  show?: boolean
  
  // 位置配置
  position?: BadgePosition
  
  // 自定义样式
  style?: BadgeStyle
}

type BadgeType = 'primary' | 'success' | 'warning' | 'danger' | 'info'

type BadgePosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'

interface BadgeStyle {
  backgroundColor?: string
  color?: string
  fontSize?: string | number
  borderRadius?: string | number
  padding?: string
  minWidth?: string | number
}
```

## 详细配置说明

### 基础配置项

#### name
- **类型**: `string`
- **说明**: 图标名称，根据type类型有不同含义
- **示例**:
  ```javascript
  // ElementPlus 图标
  name: 'Edit'
  name: 'Delete'
  name: 'Search'
  
  // 字体图标
  name: 'home'
  name: 'user'
  name: 'settings'
  ```

#### type
- **类型**: `'element' | 'svg' | 'font' | 'image'`
- **默认值**: `'element'`
- **说明**: 图标类型
- **示例**:
  ```javascript
  // ElementPlus 图标
  type: 'element'
  
  // 自定义 SVG
  type: 'svg'
  
  // 字体图标
  type: 'font'
  
  // 图片图标
  type: 'image'
  ```

#### size
- **类型**: `string | number`
- **默认值**: `'inherit'`
- **说明**: 图标大小
- **示例**:
  ```javascript
  // 数字形式（像素）
  size: 16
  size: 20
  size: 24
  
  // 字符串形式
  size: '16px'
  size: '1.2em'
  size: '100%'
  
  // 继承父元素
  size: 'inherit'
  ```

#### color
- **类型**: `string`
- **默认值**: `'inherit'`
- **说明**: 图标颜色
- **示例**:
  ```javascript
  // 十六进制
  color: '#409eff'
  color: '#f56c6c'
  
  // RGB/RGBA
  color: 'rgb(64, 158, 255)'
  color: 'rgba(64, 158, 255, 0.8)'
  
  // CSS 颜色名
  color: 'red'
  color: 'blue'
  
  // 继承父元素
  color: 'inherit'
  color: 'currentColor'
  ```

### 图标源配置项

#### prefix
- **类型**: `string`
- **默认值**: `'el-icon'`
- **说明**: 字体图标前缀
- **示例**:
  ```javascript
  // Font Awesome
  prefix: 'fa fa'
  prefix: 'fas fa'
  
  // Iconfont
  prefix: 'iconfont'
  
  // 自定义前缀
  prefix: 'my-icon'
  ```

#### src
- **类型**: `string`
- **说明**: 图片图标路径
- **示例**:
  ```javascript
  // 相对路径
  src: '/icons/home.png'
  src: './assets/icon.svg'
  
  // 绝对路径
  src: 'https://example.com/icon.png'
  
  // Base64
  src: 'data:image/svg+xml;base64,PHN2Zz4uLi48L3N2Zz4='
  ```

#### svg
- **类型**: `string`
- **说明**: SVG 图标内容
- **示例**:
  ```javascript
  svg: '<svg viewBox="0 0 24 24"><path d="M12 2l3.09..."/></svg>'
  ```

### 动画配置项

#### spin
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否旋转动画
- **示例**:
  ```javascript
  // 启用旋转
  spin: true
  
  // 禁用旋转
  spin: false
  ```

#### pulse
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否脉冲动画
- **示例**:
  ```javascript
  // 启用脉冲
  pulse: true
  
  // 禁用脉冲
  pulse: false
  ```

#### flip
- **类型**: `'horizontal' | 'vertical' | 'both'`
- **说明**: 翻转方向
- **示例**:
  ```javascript
  // 水平翻转
  flip: 'horizontal'
  
  // 垂直翻转
  flip: 'vertical'
  
  // 双向翻转
  flip: 'both'
  ```

#### rotate
- **类型**: `number`
- **默认值**: `0`
- **说明**: 旋转角度（度）
- **示例**:
  ```javascript
  // 顺时针旋转
  rotate: 90
  rotate: 180
  rotate: 270
  
  // 逆时针旋转
  rotate: -90
  rotate: -45
  ```

### 样式配置项

#### background
- **类型**: `string`
- **说明**: 背景颜色
- **示例**:
  ```javascript
  background: '#409eff'
  background: 'linear-gradient(45deg, #409eff, #67c23a)'
  background: 'rgba(64, 158, 255, 0.1)'
  ```

#### circle / square
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否为圆形/方形背景
- **示例**:
  ```javascript
  // 圆形背景
  circle: true
  
  // 方形背景
  square: true
  ```

#### padding
- **类型**: `string | number`
- **说明**: 内边距
- **示例**:
  ```javascript
  // 数字形式
  padding: 8
  padding: 12
  
  // 字符串形式
  padding: '8px'
  padding: '0.5em'
  padding: '8px 12px'
  ```

### 交互配置项

#### clickable
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否可点击
- **示例**:
  ```javascript
  // 可点击
  clickable: true
  
  // 不可点击
  clickable: false
  ```

#### disabled
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否禁用
- **示例**:
  ```javascript
  // 禁用状态
  disabled: true
  
  // 启用状态
  disabled: false
  ```

#### cursor
- **类型**: `string`
- **说明**: 鼠标指针样式
- **示例**:
  ```javascript
  cursor: 'pointer'
  cursor: 'help'
  cursor: 'not-allowed'
  cursor: 'default'
  ```

### 徽章配置项

#### badge
- **类型**: `string | number`
- **说明**: 徽章内容
- **示例**:
  ```javascript
  // 数字徽章
  badge: 5
  badge: 99
  
  // 文字徽章
  badge: 'NEW'
  badge: 'HOT'
  
  // 点状徽章
  badge: '•'
  ```

#### badgeType
- **类型**: `'primary' | 'success' | 'warning' | 'danger' | 'info'`
- **默认值**: `'danger'`
- **说明**: 徽章类型
- **示例**:
  ```javascript
  badgeType: 'danger'   // 红色
  badgeType: 'warning'  // 橙色
  badgeType: 'success'  // 绿色
  badgeType: 'primary'  // 蓝色
  badgeType: 'info'     // 灰色
  ```

#### badgeMax
- **类型**: `number`
- **默认值**: `99`
- **说明**: 徽章最大值
- **示例**:
  ```javascript
  badgeMax: 99   // 超过99显示99+
  badgeMax: 999  // 超过999显示999+
  ```

## 常用配置组合示例

### 基础图标
```javascript
const basicConfig = {
  name: 'Edit',
  type: 'element',
  size: 20,
  color: '#409eff'
}
```

### 动画图标
```javascript
const animatedConfig = {
  name: 'Loading',
  type: 'element',
  size: 24,
  color: '#409eff',
  spin: true
}
```

### 带背景的图标
```javascript
const backgroundConfig = {
  name: 'User',
  type: 'element',
  size: 20,
  color: 'white',
  circle: true,
  background: '#409eff',
  padding: 8
}
```

### 带徽章的图标
```javascript
const badgeConfig = {
  name: 'Message',
  type: 'element',
  size: 24,
  color: '#409eff',
  badge: 5,
  badgeType: 'danger'
}
```

### 可点击图标
```javascript
const clickableConfig = {
  name: 'Delete',
  type: 'element',
  size: 20,
  color: '#f56c6c',
  clickable: true,
  cursor: 'pointer'
}
```

### 自定义SVG图标
```javascript
const svgConfig = {
  type: 'svg',
  svg: '<svg viewBox="0 0 24 24"><path d="M12 2l3.09..."/></svg>',
  size: 24,
  color: '#67c23a'
}
```

### 字体图标
```javascript
const fontConfig = {
  name: 'home',
  type: 'font',
  prefix: 'fa fa',
  size: 20,
  color: '#409eff'
}
```

### 图片图标
```javascript
const imageConfig = {
  type: 'image',
  src: '/icons/custom-icon.png',
  size: 24
}
```

## 最佳实践建议

### 1. 图标选择
- 保持图标风格一致
- 选择语义明确的图标
- 考虑不同文化背景的理解
- 避免过于复杂的图标

### 2. 尺寸规范
- 建立统一的尺寸体系
- 考虑不同设备的显示效果
- 保持图标在不同尺寸下的清晰度
- 合理设置最小可点击区域

### 3. 颜色使用
- 遵循设计系统的颜色规范
- 考虑无障碍访问需求
- 提供足够的对比度
- 支持深色模式

### 4. 动画效果
- 适度使用动画效果
- 避免过于频繁的动画
- 考虑用户的偏好设置
- 提供禁用动画的选项

### 5. 性能优化
- 优先使用SVG图标
- 合理使用图标缓存
- 避免加载不必要的图标库
- 考虑图标的懒加载
