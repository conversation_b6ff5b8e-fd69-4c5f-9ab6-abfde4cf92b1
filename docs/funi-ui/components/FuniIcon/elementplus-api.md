# FuniIcon ElementPlus API 支持

## 基础组件说明

FuniIcon 基于 ElementPlus 的 `el-icon` 组件和 `@iconify/vue` 图标库封装，在保持原有功能的基础上，增加了多种图标类型支持、动画效果、徽章功能等业务功能。

### 核心组件构成
- **el-icon**: ElementPlus 基础图标组件
- **@iconify/vue**: Iconify 图标库支持
- **自定义图标**: SVG、字体图标、图片图标支持
- **徽章组件**: 图标徽章功能
- **动画系统**: 旋转、脉冲、翻转等动画效果

## 支持的 ElementPlus API

### el-icon 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| size | ✅ | 直接透传 | 图标大小 |
| color | ✅ | 直接透传 | 图标颜色 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| click | ✅ | 直接透传 | 点击事件 |
| mouseenter | ✅ | 直接透传 | 鼠标进入事件 |
| mouseleave | ✅ | 直接透传 | 鼠标离开事件 |
| mousedown | ✅ | 直接透传 | 鼠标按下事件 |
| mouseup | ✅ | 直接透传 | 鼠标抬起事件 |

#### Slots 透传支持
| ElementPlus Slot | 支持状态 | 透传方式 | 说明 |
|-----------------|----------|----------|------|
| default | ✅ | 扩展支持 | 自定义图标内容 |

#### Methods 透传支持
| ElementPlus Method | 支持状态 | 透传方式 | 说明 |
|-------------------|----------|----------|------|
| focus | ✅ | 内部处理 | 获得焦点 |
| blur | ✅ | 内部处理 | 失去焦点 |

## 透传方式说明

### 1. 直接透传
组件属性直接传递给 `el-icon` 组件，无需额外处理。

```vue
<template>
  <FuniIcon
    name="Edit"
    size="20"
    color="#409eff"
    @click="handleClick"
  />
</template>
```

### 2. 内部处理
组件内部处理后再传递给 ElementPlus 组件。

```vue
<template>
  <FuniIcon
    name="Loading"
    size="24"
    color="#409eff"
    :spin="true"
    :clickable="true"
    @click="handleClick"
  />
</template>

<script setup>
// spin、clickable 会被内部处理为样式和行为
// click 事件会被透传
const handleClick = (event) => {
  console.log('图标被点击:', event)
}
</script>
```

### 3. 扩展支持
在 ElementPlus 基础功能上增加新的功能。

```javascript
// 组件内部图标类型处理逻辑
const iconComponent = computed(() => {
  switch (props.type) {
    case 'element':
      return resolveComponent(props.name) // ElementPlus 图标
    case 'svg':
      return 'svg' // 自定义 SVG
    case 'font':
      return 'i' // 字体图标
    case 'image':
      return 'img' // 图片图标
    default:
      return Icon // Iconify 图标
  }
})
```

## 使用示例

### 基础透传示例
```vue
<template>
  <div class="example-container">
    <h3>ElementPlus API 透传示例</h3>
    
    <!-- 基础属性透传 -->
    <FuniIcon
      name="Edit"
      size="20"
      color="#409eff"
      @click="handleClick"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    />
    
    <!-- 尺寸和颜色配置 -->
    <FuniIcon
      name="Delete"
      :size="24"
      color="#f56c6c"
    />
    
    <!-- 事件处理 -->
    <FuniIcon
      name="Search"
      size="20"
      color="#67c23a"
      @click="handleSearch"
    />
  </div>
</template>

<script setup>
const handleClick = (event) => {
  console.log('图标点击:', event)
}

const handleMouseEnter = (event) => {
  console.log('鼠标进入:', event)
}

const handleMouseLeave = (event) => {
  console.log('鼠标离开:', event)
}

const handleSearch = () => {
  console.log('执行搜索')
}
</script>
```

### 扩展功能示例
```vue
<template>
  <div class="example-container">
    <h3>扩展功能示例</h3>
    
    <!-- 动画效果（扩展功能） -->
    <FuniIcon
      name="Loading"
      size="24"
      color="#409eff"
      :spin="true"
    />
    
    <!-- 徽章功能（扩展功能） -->
    <FuniIcon
      name="Message"
      size="24"
      color="#409eff"
      :badge="messageCount"
      badge-type="danger"
    />
    
    <!-- 背景样式（扩展功能） -->
    <FuniIcon
      name="User"
      size="20"
      color="white"
      :circle="true"
      background="#409eff"
      :padding="8"
    />
    
    <!-- 可点击状态（扩展功能） -->
    <FuniIcon
      name="Setting"
      size="20"
      color="#606266"
      :clickable="true"
      cursor="pointer"
      @click="handleSettings"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const messageCount = ref(5)

const handleSettings = () => {
  console.log('打开设置')
}
</script>
```

### 自定义插槽示例
```vue
<template>
  <div class="example-container">
    <h3>自定义插槽示例</h3>
    
    <!-- 自定义图标内容 -->
    <FuniIcon size="24" color="#409eff">
      <template #default>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      </template>
    </FuniIcon>
    
    <!-- 自定义徽章内容 -->
    <FuniIcon 
      name="Star" 
      size="24" 
      color="#e6a23c"
    >
      <template #badge>
        <span class="custom-badge">VIP</span>
      </template>
    </FuniIcon>
  </div>
</template>

<style scoped>
.custom-badge {
  background: linear-gradient(45deg, #f56c6c, #e6a23c);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
  position: absolute;
  top: -8px;
  right: -8px;
  font-weight: bold;
}
</style>
```

### 事件处理示例
```vue
<template>
  <div class="example-container">
    <h3>事件处理示例</h3>
    
    <div class="icon-with-events">
      <FuniIcon
        name="Heart"
        :size="32"
        :color="iconColor"
        :clickable="true"
        cursor="pointer"
        @click="handleIconClick"
        @mouseenter="handleIconEnter"
        @mouseleave="handleIconLeave"
        @mousedown="handleIconDown"
        @mouseup="handleIconUp"
      />
      
      <div class="event-logs">
        <h4>事件日志：</h4>
        <div class="log-list">
          <div v-for="log in eventLogs" :key="log.id" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-event">{{ log.event }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const iconColor = ref('#c0c4cc')
const eventLogs = ref([])

const addLog = (event) => {
  eventLogs.value.unshift({
    id: Date.now(),
    time: new Date().toLocaleTimeString(),
    event
  })
  
  // 保持日志数量
  if (eventLogs.value.length > 8) {
    eventLogs.value = eventLogs.value.slice(0, 8)
  }
}

const handleIconClick = () => {
  iconColor.value = iconColor.value === '#f56c6c' ? '#c0c4cc' : '#f56c6c'
  addLog('图标被点击')
}

const handleIconEnter = () => {
  addLog('鼠标进入图标')
}

const handleIconLeave = () => {
  addLog('鼠标离开图标')
}

const handleIconDown = () => {
  addLog('鼠标按下')
}

const handleIconUp = () => {
  addLog('鼠标抬起')
}
</script>

<style scoped>
.icon-with-events {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.event-logs {
  flex: 1;
  max-width: 300px;
}

.log-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  color: #909399;
  width: 80px;
}

.log-event {
  color: #409eff;
  flex: 1;
}
</style>
```

## 注意事项

### 1. API 兼容性
- 确保使用的 ElementPlus 版本支持相应的 API
- 建议使用 ElementPlus 2.0+ 版本以获得最佳兼容性
- Iconify 图标库需要单独安装和配置

### 2. 图标类型处理
- `type="element"` 使用 ElementPlus 内置图标
- `type="svg"` 使用自定义 SVG 内容
- `type="font"` 使用字体图标库
- `type="image"` 使用图片文件
- 默认使用 Iconify 图标库

### 3. 事件处理
- 所有 ElementPlus 原生事件都会直接透传
- 扩展的交互功能（如 clickable）会影响事件行为
- 建议使用组件提供的事件处理方式

### 4. 样式定制
- 可以通过 CSS 变量进行样式定制
- 扩展的样式属性（如 background、padding）不会透传给 el-icon
- 注意样式优先级，避免样式冲突

### 5. 性能考虑
- 大量图标时考虑按需加载
- SVG 图标比字体图标性能更好
- 合理使用图标缓存策略

## 版本兼容性

| FuniIcon 版本 | ElementPlus 版本 | Iconify 版本 | 兼容性 |
|--------------|-----------------|-------------|--------|
| 1.0.x | 2.0.x | 3.x | ✅ 完全兼容 |
| 1.0.x | 2.1.x | 3.x | ✅ 完全兼容 |
| 1.0.x | 2.2.x | 3.x | ✅ 完全兼容 |
| 1.0.x | 2.3.x | 3.x | ✅ 完全兼容 |
| 1.0.x | 1.x.x | 3.x | ⚠️ 部分兼容 |

## 迁移指南

### 从 el-icon 迁移
```vue
<!-- 原来的 el-icon -->
<el-icon :size="20" color="#409eff">
  <Edit />
</el-icon>

<!-- 迁移到 FuniIcon -->
<FuniIcon
  name="Edit"
  type="element"
  :size="20"
  color="#409eff"
/>
```

### 使用新增功能
```vue
<!-- 使用扩展的动画和样式功能 -->
<FuniIcon
  name="Loading"
  :size="24"
  color="#409eff"
  :spin="true"
  :circle="true"
  background="rgba(64, 158, 255, 0.1)"
  :padding="8"
/>

<!-- 使用徽章功能 -->
<FuniIcon
  name="Message"
  :size="24"
  color="#409eff"
  :badge="5"
  badge-type="danger"
/>
```

### 图标库配置
```javascript
// 配置 Iconify 图标库
import { addCollection } from '@iconify/vue'

// 添加自定义图标集合
addCollection({
  prefix: 'custom',
  icons: {
    'my-icon': {
      body: '<path d="..."/>',
      width: 24,
      height: 24
    }
  }
})
```
