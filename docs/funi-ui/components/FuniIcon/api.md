# FuniIcon API文档

## 组件概述

FuniIcon是基于ElementPlus的el-icon封装的图标组件，支持ElementPlus图标库、自定义SVG图标、图标字体等多种图标类型，提供了丰富的样式配置和交互功能。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| name | String | '' | - | 图标名称 | - |
| type | String | 'element' | - | 图标类型：'element'、'svg'、'font'、'image' | - |
| size | String/Number | 'inherit' | - | 图标大小 | el-icon.size |
| color | String | 'inherit' | - | 图标颜色 | el-icon.color |
| prefix | String | 'el-icon' | - | 图标前缀（字体图标） | - |
| src | String | '' | - | 图片路径（image类型） | - |
| svg | String | '' | - | SVG内容（svg类型） | - |
| spin | Boolean | false | - | 是否旋转 | - |
| pulse | Boolean | false | - | 是否脉冲动画 | - |
| flip | String | '' | - | 翻转方向：'horizontal'、'vertical'、'both' | - |
| rotate | Number | 0 | - | 旋转角度 | - |
| border | Boolean | false | - | 是否显示边框 | - |
| circle | Boolean | false | - | 是否圆形背景 | - |
| square | Boolean | false | - | 是否方形背景 | - |
| background | String | '' | - | 背景颜色 | - |
| borderColor | String | '' | - | 边框颜色 | - |
| borderWidth | String/Number | '1px' | - | 边框宽度 | - |
| borderRadius | String/Number | '4px' | - | 圆角大小 | - |
| padding | String/Number | '8px' | - | 内边距 | - |
| margin | String/Number | '0' | - | 外边距 | - |
| shadow | String | '' | - | 阴影效果 | - |
| cursor | String | 'default' | - | 鼠标样式 | - |
| clickable | Boolean | false | - | 是否可点击 | - |
| disabled | Boolean | false | - | 是否禁用 | - |
| loading | Boolean | false | - | 是否加载状态 | - |
| badge | String/Number | '' | - | 徽章内容 | - |
| badgeType | String | 'danger' | - | 徽章类型 | - |
| badgeMax | Number | 99 | - | 徽章最大值 | - |
| tooltip | String | '' | - | 提示文本 | - |
| tooltipPlacement | String | 'top' | - | 提示位置 | - |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| click | event: Event | 点击事件 | 点击图标时 |
| mouseenter | event: Event | 鼠标进入事件 | 鼠标进入图标时 |
| mouseleave | event: Event | 鼠标离开事件 | 鼠标离开图标时 |
| mousedown | event: Event | 鼠标按下事件 | 鼠标按下时 |
| mouseup | event: Event | 鼠标抬起事件 | 鼠标抬起时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| focus | - | void | 使图标获得焦点 |
| blur | - | void | 使图标失去焦点 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 自定义图标内容 |
| badge | - | 自定义徽章内容 |

## 图标类型说明

### ElementPlus图标
使用ElementPlus内置图标库：

```vue
<FuniIcon name="Edit" type="element" />
<FuniIcon name="Delete" type="element" />
<FuniIcon name="Search" type="element" />
```

### SVG图标
使用自定义SVG图标：

```vue
<FuniIcon 
  type="svg" 
  svg="<svg>...</svg>" 
/>
```

### 字体图标
使用字体图标库（如Font Awesome、Iconfont等）：

```vue
<FuniIcon 
  name="home" 
  type="font" 
  prefix="fa fa" 
/>
```

### 图片图标
使用图片作为图标：

```vue
<FuniIcon 
  type="image" 
  src="/path/to/icon.png" 
/>
```

## 使用示例

### 基础图标
```vue
<template>
  <div class="icon-examples">
    <div class="icon-group">
      <h4>ElementPlus图标</h4>
      <FuniIcon name="Edit" size="20" />
      <FuniIcon name="Delete" size="20" color="#f56c6c" />
      <FuniIcon name="Search" size="20" color="#409eff" />
      <FuniIcon name="Plus" size="20" color="#67c23a" />
    </div>
    
    <div class="icon-group">
      <h4>不同尺寸</h4>
      <FuniIcon name="Star" size="16" />
      <FuniIcon name="Star" size="20" />
      <FuniIcon name="Star" size="24" />
      <FuniIcon name="Star" size="32" />
    </div>
    
    <div class="icon-group">
      <h4>不同颜色</h4>
      <FuniIcon name="Heart" color="#f56c6c" />
      <FuniIcon name="Heart" color="#e6a23c" />
      <FuniIcon name="Heart" color="#67c23a" />
      <FuniIcon name="Heart" color="#409eff" />
    </div>
  </div>
</template>

<style scoped>
.icon-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.icon-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon-group h4 {
  width: 120px;
  margin: 0;
}
</style>
```

### 动画效果图标
```vue
<template>
  <div class="animated-icons">
    <div class="animation-group">
      <h4>旋转动画</h4>
      <FuniIcon name="Loading" spin size="24" />
      <FuniIcon name="Refresh" spin size="24" color="#409eff" />
    </div>
    
    <div class="animation-group">
      <h4>脉冲动画</h4>
      <FuniIcon name="Bell" pulse size="24" color="#e6a23c" />
      <FuniIcon name="Message" pulse size="24" color="#f56c6c" />
    </div>
    
    <div class="animation-group">
      <h4>翻转效果</h4>
      <FuniIcon name="ArrowRight" flip="horizontal" size="24" />
      <FuniIcon name="ArrowDown" flip="vertical" size="24" />
      <FuniIcon name="Refresh" flip="both" size="24" />
    </div>
    
    <div class="animation-group">
      <h4>旋转角度</h4>
      <FuniIcon name="ArrowRight" :rotate="45" size="24" />
      <FuniIcon name="ArrowRight" :rotate="90" size="24" />
      <FuniIcon name="ArrowRight" :rotate="135" size="24" />
    </div>
  </div>
</template>

<style scoped>
.animated-icons {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.animation-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.animation-group h4 {
  width: 120px;
  margin: 0;
}
</style>
```

### 带背景和边框的图标
```vue
<template>
  <div class="styled-icons">
    <div class="style-group">
      <h4>圆形背景</h4>
      <FuniIcon 
        name="User" 
        circle 
        background="#409eff" 
        color="white" 
        size="20" 
        padding="8px" 
      />
      <FuniIcon 
        name="Setting" 
        circle 
        background="#67c23a" 
        color="white" 
        size="20" 
        padding="8px" 
      />
      <FuniIcon 
        name="Warning" 
        circle 
        background="#e6a23c" 
        color="white" 
        size="20" 
        padding="8px" 
      />
    </div>
    
    <div class="style-group">
      <h4>方形背景</h4>
      <FuniIcon 
        name="Document" 
        square 
        background="#f4f4f5" 
        color="#409eff" 
        size="20" 
        padding="8px" 
        border-radius="4px" 
      />
      <FuniIcon 
        name="Folder" 
        square 
        background="#f4f4f5" 
        color="#67c23a" 
        size="20" 
        padding="8px" 
        border-radius="4px" 
      />
    </div>
    
    <div class="style-group">
      <h4>带边框</h4>
      <FuniIcon 
        name="Phone" 
        border 
        border-color="#409eff" 
        color="#409eff" 
        size="20" 
        padding="8px" 
        border-radius="50%" 
      />
      <FuniIcon 
        name="Message" 
        border 
        border-color="#67c23a" 
        color="#67c23a" 
        size="20" 
        padding="8px" 
        border-width="2px" 
      />
    </div>
    
    <div class="style-group">
      <h4>阴影效果</h4>
      <FuniIcon 
        name="Star" 
        background="#fff" 
        color="#e6a23c" 
        size="20" 
        padding="8px" 
        shadow="0 2px 8px rgba(0,0,0,0.1)" 
        border-radius="4px" 
      />
    </div>
  </div>
</template>

<style scoped>
.styled-icons {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.style-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.style-group h4 {
  width: 120px;
  margin: 0;
}
</style>
```

### 可点击图标
```vue
<template>
  <div class="clickable-icons">
    <div class="action-group">
      <h4>操作按钮</h4>
      <FuniIcon 
        name="Edit" 
        clickable 
        cursor="pointer" 
        size="20" 
        color="#409eff" 
        @click="handleEdit" 
      />
      <FuniIcon 
        name="Delete" 
        clickable 
        cursor="pointer" 
        size="20" 
        color="#f56c6c" 
        @click="handleDelete" 
      />
      <FuniIcon 
        name="View" 
        clickable 
        cursor="pointer" 
        size="20" 
        color="#67c23a" 
        @click="handleView" 
      />
    </div>
    
    <div class="action-group">
      <h4>禁用状态</h4>
      <FuniIcon 
        name="Edit" 
        clickable 
        disabled 
        size="20" 
        color="#c0c4cc" 
      />
      <FuniIcon 
        name="Delete" 
        clickable 
        disabled 
        size="20" 
        color="#c0c4cc" 
      />
    </div>
    
    <div class="action-group">
      <h4>加载状态</h4>
      <FuniIcon 
        name="Loading" 
        loading 
        spin 
        size="20" 
        color="#409eff" 
      />
      <el-button @click="toggleLoading">
        {{ isLoading ? '停止加载' : '开始加载' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const isLoading = ref(false)

const handleEdit = () => {
  ElMessage.success('编辑操作')
}

const handleDelete = () => {
  ElMessage.warning('删除操作')
}

const handleView = () => {
  ElMessage.info('查看操作')
}

const toggleLoading = () => {
  isLoading.value = !isLoading.value
}
</script>

<style scoped>
.clickable-icons {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-group h4 {
  width: 120px;
  margin: 0;
}
</style>
```

### 带徽章的图标
```vue
<template>
  <div class="badge-icons">
    <div class="badge-group">
      <h4>数字徽章</h4>
      <FuniIcon 
        name="Message" 
        badge="5" 
        badge-type="danger" 
        size="24" 
        color="#409eff" 
      />
      <FuniIcon 
        name="Bell" 
        badge="99+" 
        badge-type="warning" 
        size="24" 
        color="#e6a23c" 
      />
      <FuniIcon 
        name="ShoppingCart" 
        :badge="cartCount" 
        :badge-max="99" 
        badge-type="success" 
        size="24" 
        color="#67c23a" 
      />
    </div>
    
    <div class="badge-group">
      <h4>点状徽章</h4>
      <FuniIcon 
        name="User" 
        badge="•" 
        badge-type="danger" 
        size="24" 
        color="#409eff" 
      />
      <FuniIcon 
        name="Setting" 
        badge="•" 
        badge-type="success" 
        size="24" 
        color="#67c23a" 
      />
    </div>
    
    <div class="badge-group">
      <h4>自定义徽章</h4>
      <FuniIcon 
        name="Star" 
        size="24" 
        color="#e6a23c" 
      >
        <template #badge>
          <span class="custom-badge">NEW</span>
        </template>
      </FuniIcon>
    </div>
    
    <div class="badge-group">
      <el-button @click="addToCart">添加到购物车</el-button>
      <el-button @click="clearCart">清空购物车</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const cartCount = ref(3)

const addToCart = () => {
  cartCount.value++
}

const clearCart = () => {
  cartCount.value = 0
}
</script>

<style scoped>
.badge-icons {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.badge-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.badge-group h4 {
  width: 120px;
  margin: 0;
}

.custom-badge {
  background: #f56c6c;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
  position: absolute;
  top: -8px;
  right: -8px;
}
</style>
```

### 带提示的图标
```vue
<template>
  <div class="tooltip-icons">
    <div class="tooltip-group">
      <h4>基础提示</h4>
      <FuniIcon 
        name="QuestionFilled" 
        tooltip="这是帮助信息" 
        size="20" 
        color="#409eff" 
        cursor="help" 
      />
      <FuniIcon 
        name="InfoFilled" 
        tooltip="这是提示信息" 
        tooltip-placement="right" 
        size="20" 
        color="#e6a23c" 
        cursor="help" 
      />
      <FuniIcon 
        name="WarningFilled" 
        tooltip="这是警告信息" 
        tooltip-placement="bottom" 
        size="20" 
        color="#f56c6c" 
        cursor="help" 
      />
    </div>
  </div>
</template>

<style scoped>
.tooltip-icons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 40px;
}

.tooltip-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tooltip-group h4 {
  width: 120px;
  margin: 0;
}
</style>
```

### 自定义SVG图标
```vue
<template>
  <div class="svg-icons">
    <div class="svg-group">
      <h4>自定义SVG</h4>
      <FuniIcon 
        type="svg" 
        :svg="customSvg1" 
        size="24" 
        color="#409eff" 
      />
      <FuniIcon 
        type="svg" 
        :svg="customSvg2" 
        size="24" 
        color="#67c23a" 
      />
    </div>
    
    <div class="svg-group">
      <h4>图片图标</h4>
      <FuniIcon 
        type="image" 
        src="/path/to/icon1.png" 
        size="24" 
      />
      <FuniIcon 
        type="image" 
        src="/path/to/icon2.png" 
        size="24" 
      />
    </div>
  </div>
</template>

<script setup>
const customSvg1 = `
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
  </svg>
`

const customSvg2 = `
  <svg viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
  </svg>
`
</script>

<style scoped>
.svg-icons {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.svg-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.svg-group h4 {
  width: 120px;
  margin: 0;
}
</style>
```

## ElementPlus API支持

FuniIcon基于el-icon封装，支持所有el-icon的API：

```vue
<template>
  <FuniIcon
    :name="iconName"
    
    <!-- ElementPlus el-icon 所有属性 -->
    :size="iconSize"
    :color="iconColor"
    
    <!-- ElementPlus el-icon 所有事件 -->
    @click="handleClick"
  />
</template>
```

## 注意事项

### 1. 图标库管理
- 统一图标风格和尺寸
- 合理组织图标资源
- 避免重复的图标

### 2. 性能优化
- 使用SVG图标提高清晰度
- 合理使用图标缓存
- 避免过大的图标文件

### 3. 可访问性
- 提供合适的alt文本
- 支持键盘导航
- 考虑色盲用户的需求

### 4. 响应式设计
- 在不同设备上保持图标清晰
- 合理设置图标大小
- 考虑触摸设备的交互

## 常见问题

### Q: 如何添加自定义图标库？
A: 通过type="font"使用字体图标库，或type="svg"使用SVG图标

### Q: 如何实现图标的动画效果？
A: 使用spin、pulse、flip、rotate等属性，或通过CSS自定义动画

### Q: 如何处理图标的点击事件？
A: 设置clickable为true，监听click事件

### Q: 如何在图标上显示徽章？
A: 使用badge属性设置徽章内容，或使用badge插槽自定义徽章
