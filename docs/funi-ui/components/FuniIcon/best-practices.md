# FuniIcon 最佳实践

## 推荐用法和配置

### 1. 图标选择和使用最佳实践

#### 语义化图标选择
```vue
<template>
  <div class="semantic-examples">
    <!-- ✅ 推荐：语义明确的图标 -->
    <div class="action-buttons">
      <FuniIcon name="Edit" size="16" color="#409eff" tooltip="编辑" />
      <FuniIcon name="Delete" size="16" color="#f56c6c" tooltip="删除" />
      <FuniIcon name="View" size="16" color="#67c23a" tooltip="查看" />
      <FuniIcon name="Download" size="16" color="#e6a23c" tooltip="下载" />
    </div>
    
    <!-- ✅ 推荐：状态图标 -->
    <div class="status-icons">
      <FuniIcon name="SuccessFilled" size="16" color="#67c23a" />
      <span>成功状态</span>
      
      <FuniIcon name="WarningFilled" size="16" color="#e6a23c" />
      <span>警告状态</span>
      
      <FuniIcon name="CircleCloseFilled" size="16" color="#f56c6c" />
      <span>错误状态</span>
    </div>
    
    <!-- ❌ 不推荐：语义不明确的图标 -->
    <div class="bad-examples">
      <!-- 使用不相关的图标表示操作 -->
      <FuniIcon name="Star" size="16" tooltip="删除" />  <!-- 星星图标表示删除 -->
      <FuniIcon name="Heart" size="16" tooltip="设置" />  <!-- 心形图标表示设置 -->
    </div>
  </div>
</template>

<script setup>
// ✅ 推荐：建立图标映射表
const ICON_MAP = {
  actions: {
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    download: 'Download',
    upload: 'Upload',
    search: 'Search',
    filter: 'Filter',
    refresh: 'Refresh'
  },
  status: {
    success: 'SuccessFilled',
    warning: 'WarningFilled',
    error: 'CircleCloseFilled',
    info: 'InfoFilled',
    loading: 'Loading'
  },
  navigation: {
    home: 'House',
    back: 'ArrowLeft',
    forward: 'ArrowRight',
    up: 'ArrowUp',
    down: 'ArrowDown'
  }
}

// ✅ 推荐：使用函数获取图标
const getActionIcon = (action) => {
  return ICON_MAP.actions[action] || 'QuestionFilled'
}

const getStatusIcon = (status) => {
  return ICON_MAP.status[status] || 'InfoFilled'
}
</script>

<style scoped>
.semantic-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-buttons,
.status-icons,
.bad-examples {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.bad-examples {
  border-color: #f56c6c;
  background-color: #fef0f0;
}
</style>
```

#### 尺寸规范化
```vue
<template>
  <div class="size-examples">
    <!-- ✅ 推荐：建立尺寸体系 -->
    <div class="size-system">
      <h4>标准尺寸体系</h4>
      <div class="size-row">
        <FuniIcon name="Star" :size="ICON_SIZES.xs" />
        <span>xs (12px) - 表格内图标</span>
      </div>
      <div class="size-row">
        <FuniIcon name="Star" :size="ICON_SIZES.sm" />
        <span>sm (14px) - 按钮内图标</span>
      </div>
      <div class="size-row">
        <FuniIcon name="Star" :size="ICON_SIZES.md" />
        <span>md (16px) - 默认图标</span>
      </div>
      <div class="size-row">
        <FuniIcon name="Star" :size="ICON_SIZES.lg" />
        <span>lg (20px) - 导航图标</span>
      </div>
      <div class="size-row">
        <FuniIcon name="Star" :size="ICON_SIZES.xl" />
        <span>xl (24px) - 标题图标</span>
      </div>
    </div>
    
    <!-- ✅ 推荐：响应式尺寸 -->
    <div class="responsive-sizes">
      <h4>响应式尺寸</h4>
      <FuniIcon 
        name="Menu" 
        :size="responsiveIconSize" 
        color="#409eff"
      />
      <span>当前屏幕: {{ screenSize }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// ✅ 推荐：标准化尺寸常量
const ICON_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 20,
  xl: 24,
  xxl: 32
}

const screenWidth = ref(window.innerWidth)

// ✅ 推荐：响应式尺寸计算
const responsiveIconSize = computed(() => {
  if (screenWidth.value < 768) return ICON_SIZES.sm
  if (screenWidth.value < 1200) return ICON_SIZES.md
  return ICON_SIZES.lg
})

const screenSize = computed(() => {
  if (screenWidth.value < 768) return '小屏幕'
  if (screenWidth.value < 1200) return '中等屏幕'
  return '大屏幕'
})

const handleResize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.size-examples {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.size-row {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.responsive-sizes {
  display: flex;
  align-items: center;
  gap: 15px;
}
</style>
```

### 2. 颜色和主题最佳实践

#### 主题色彩系统
```vue
<template>
  <div class="color-examples">
    <!-- ✅ 推荐：使用主题色彩 -->
    <div class="theme-colors">
      <h4>主题色彩系统</h4>
      <div class="color-grid">
        <div class="color-item">
          <FuniIcon name="InfoFilled" :color="THEME_COLORS.primary" size="20" />
          <span>Primary</span>
        </div>
        <div class="color-item">
          <FuniIcon name="SuccessFilled" :color="THEME_COLORS.success" size="20" />
          <span>Success</span>
        </div>
        <div class="color-item">
          <FuniIcon name="WarningFilled" :color="THEME_COLORS.warning" size="20" />
          <span>Warning</span>
        </div>
        <div class="color-item">
          <FuniIcon name="CircleCloseFilled" :color="THEME_COLORS.danger" size="20" />
          <span>Danger</span>
        </div>
        <div class="color-item">
          <FuniIcon name="QuestionFilled" :color="THEME_COLORS.info" size="20" />
          <span>Info</span>
        </div>
      </div>
    </div>
    
    <!-- ✅ 推荐：语义化颜色使用 -->
    <div class="semantic-colors">
      <h4>语义化颜色</h4>
      <div class="semantic-grid">
        <div class="semantic-item">
          <FuniIcon name="Edit" :color="getSemanticColor('edit')" size="18" />
          <span>编辑操作</span>
        </div>
        <div class="semantic-item">
          <FuniIcon name="Delete" :color="getSemanticColor('delete')" size="18" />
          <span>删除操作</span>
        </div>
        <div class="semantic-item">
          <FuniIcon name="View" :color="getSemanticColor('view')" size="18" />
          <span>查看操作</span>
        </div>
        <div class="semantic-item">
          <FuniIcon name="Download" :color="getSemanticColor('download')" size="18" />
          <span>下载操作</span>
        </div>
      </div>
    </div>
    
    <!-- ✅ 推荐：深色模式支持 -->
    <div class="dark-mode-support">
      <h4>深色模式支持</h4>
      <div class="mode-toggle">
        <el-switch 
          v-model="isDarkMode" 
          @change="toggleDarkMode"
          active-text="深色模式"
          inactive-text="浅色模式"
        />
      </div>
      <div class="mode-icons" :class="{ 'dark-mode': isDarkMode }">
        <FuniIcon name="Sunny" :color="getThemeColor('text-primary')" size="20" />
        <FuniIcon name="Moon" :color="getThemeColor('text-secondary')" size="20" />
        <FuniIcon name="Star" :color="getThemeColor('text-regular')" size="20" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// ✅ 推荐：主题色彩常量
const THEME_COLORS = {
  primary: '#409eff',
  success: '#67c23a',
  warning: '#e6a23c',
  danger: '#f56c6c',
  info: '#909399'
}

// ✅ 推荐：语义化颜色映射
const SEMANTIC_COLOR_MAP = {
  edit: THEME_COLORS.primary,
  delete: THEME_COLORS.danger,
  view: THEME_COLORS.success,
  download: THEME_COLORS.warning,
  upload: THEME_COLORS.info
}

// ✅ 推荐：深色模式颜色
const DARK_MODE_COLORS = {
  'text-primary': '#e5eaf3',
  'text-secondary': '#a3a6ad',
  'text-regular': '#606266'
}

const LIGHT_MODE_COLORS = {
  'text-primary': '#303133',
  'text-secondary': '#606266',
  'text-regular': '#909399'
}

const isDarkMode = ref(false)

const getSemanticColor = (action) => {
  return SEMANTIC_COLOR_MAP[action] || THEME_COLORS.info
}

const getThemeColor = (colorKey) => {
  return isDarkMode.value 
    ? DARK_MODE_COLORS[colorKey] 
    : LIGHT_MODE_COLORS[colorKey]
}

const toggleDarkMode = () => {
  // 切换深色模式逻辑
  document.documentElement.classList.toggle('dark', isDarkMode.value)
}
</script>

<style scoped>
.color-examples {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.color-grid,
.semantic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.color-item,
.semantic-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  text-align: center;
}

.mode-toggle {
  margin-bottom: 15px;
}

.mode-icons {
  display: flex;
  gap: 20px;
  padding: 15px;
  border-radius: 6px;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.mode-icons.dark-mode {
  background-color: #2d2d2d;
}
</style>
```

### 3. 动画效果最佳实践

#### 合理使用动画
```vue
<template>
  <div class="animation-examples">
    <!-- ✅ 推荐：加载状态动画 -->
    <div class="loading-animations">
      <h4>加载状态动画</h4>
      <div class="loading-states">
        <div class="loading-item">
          <FuniIcon 
            name="Loading" 
            :spin="isLoading" 
            size="20" 
            color="#409eff"
          />
          <span>{{ isLoading ? '加载中...' : '已完成' }}</span>
          <el-button size="small" @click="toggleLoading">
            {{ isLoading ? '停止' : '开始' }}
          </el-button>
        </div>
        
        <div class="loading-item">
          <FuniIcon 
            name="Refresh" 
            :spin="isRefreshing" 
            size="20" 
            color="#67c23a"
          />
          <span>{{ isRefreshing ? '刷新中...' : '已刷新' }}</span>
          <el-button size="small" @click="handleRefresh">刷新</el-button>
        </div>
      </div>
    </div>
    
    <!-- ✅ 推荐：状态提示动画 -->
    <div class="status-animations">
      <h4>状态提示动画</h4>
      <div class="status-items">
        <div class="status-item">
          <FuniIcon 
            name="Bell" 
            :pulse="hasNotification" 
            size="20" 
            color="#e6a23c"
            :badge="notificationCount"
            badge-type="danger"
          />
          <span>通知提醒</span>
        </div>
        
        <div class="status-item">
          <FuniIcon 
            name="Message" 
            :pulse="hasNewMessage" 
            size="20" 
            color="#409eff"
            :badge="messageCount"
            badge-type="success"
          />
          <span>新消息</span>
        </div>
      </div>
      
      <div class="animation-controls">
        <el-button size="small" @click="addNotification">添加通知</el-button>
        <el-button size="small" @click="addMessage">新消息</el-button>
        <el-button size="small" @click="clearAll">清空所有</el-button>
      </div>
    </div>
    
    <!-- ❌ 不推荐：过度使用动画 -->
    <div class="bad-animations">
      <h4>❌ 避免过度使用动画</h4>
      <div class="overuse-examples">
        <!-- 所有图标都在旋转 -->
        <FuniIcon name="Edit" :spin="true" size="20" />
        <FuniIcon name="Delete" :spin="true" size="20" />
        <FuniIcon name="View" :spin="true" size="20" />
        <FuniIcon name="Setting" :spin="true" size="20" />
        <span>过多的动画会分散用户注意力</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const isLoading = ref(false)
const isRefreshing = ref(false)
const notificationCount = ref(0)
const messageCount = ref(0)

// ✅ 推荐：基于状态控制动画
const hasNotification = computed(() => notificationCount.value > 0)
const hasNewMessage = computed(() => messageCount.value > 0)

const toggleLoading = () => {
  isLoading.value = !isLoading.value
}

const handleRefresh = async () => {
  isRefreshing.value = true
  // 模拟刷新操作
  await new Promise(resolve => setTimeout(resolve, 2000))
  isRefreshing.value = false
}

const addNotification = () => {
  notificationCount.value++
}

const addMessage = () => {
  messageCount.value++
}

const clearAll = () => {
  notificationCount.value = 0
  messageCount.value = 0
}
</script>

<style scoped>
.animation-examples {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.loading-states,
.status-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.loading-item,
.status-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.animation-controls {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.bad-animations {
  padding: 15px;
  border: 2px solid #f56c6c;
  border-radius: 6px;
  background-color: #fef0f0;
}

.overuse-examples {
  display: flex;
  align-items: center;
  gap: 15px;
}
</style>
```

## 避免的用法和常见错误

### 1. 图标选择错误

```vue
<!-- ❌ 错误：语义不明确的图标 -->
<FuniIcon name="Star" tooltip="删除" />
<FuniIcon name="Heart" tooltip="设置" />

<!-- ✅ 正确：语义明确的图标 -->
<FuniIcon name="Delete" tooltip="删除" />
<FuniIcon name="Setting" tooltip="设置" />
```

### 2. 尺寸使用错误

```vue
<!-- ❌ 错误：尺寸不一致 -->
<div class="toolbar">
  <FuniIcon name="Edit" size="16" />
  <FuniIcon name="Delete" size="20" />
  <FuniIcon name="View" size="18" />
</div>

<!-- ✅ 正确：统一尺寸 -->
<div class="toolbar">
  <FuniIcon name="Edit" size="18" />
  <FuniIcon name="Delete" size="18" />
  <FuniIcon name="View" size="18" />
</div>
```

### 3. 动画使用错误

```vue
<!-- ❌ 错误：过度使用动画 -->
<div class="navigation">
  <FuniIcon name="Home" :spin="true" />
  <FuniIcon name="User" :pulse="true" />
  <FuniIcon name="Setting" :spin="true" />
</div>

<!-- ✅ 正确：有目的的动画 -->
<div class="navigation">
  <FuniIcon name="Home" />
  <FuniIcon name="User" />
  <FuniIcon name="Loading" :spin="isLoading" />
</div>
```

## 总结

### 核心原则
1. **语义化优先**：选择语义明确的图标
2. **一致性**：保持尺寸、颜色、风格的一致性
3. **可访问性**：支持键盘导航和屏幕阅读器
4. **性能考虑**：合理使用动画和图标库
5. **用户体验**：提供清晰的交互反馈

### 开发建议
1. 建立图标使用规范和映射表
2. 制定统一的尺寸和颜色体系
3. 适度使用动画效果
4. 确保可访问性支持
5. 考虑国际化和文化差异
6. 定期review图标使用的合理性
