# ElementPlus集成说明

## 概述

FuniUI组件库基于ElementPlus进行二次封装，所有组件都支持对应ElementPlus组件的原生API，通过v-bind透传机制实现无缝集成。

## 透传机制

### v-bind透传原理
FuniUI组件通过v-bind="$attrs"将所有未声明的属性透传给内部的ElementPlus组件：

```vue
<!-- FuniUI组件内部实现 -->
<template>
  <el-select v-bind="$attrs" v-on="$listeners">
    <!-- 组件内容 -->
  </el-select>
</template>

<script setup>
// 继承所有属性
defineOptions({
  inheritAttrs: false
})
</script>
```

### 使用方式
```vue
<template>
  <!-- 直接使用ElementPlus API -->
  <FuniSelect
    v-model="value"
    placeholder="请选择"
    clearable
    filterable
    multiple
    collapse-tags
    :loading="loading"
    @change="handleChange"
    @visible-change="handleVisibleChange"
  />
</template>
```

## 组件映射关系

### 表单控件映射

| FuniUI组件 | ElementPlus基础组件 | 支持的API | 特殊说明 |
|------------|-------------------|-----------|----------|
| FuniSelect | el-select | 完全支持 | 增加数据源配置 |
| FuniTreeSelect | el-tree-select | 完全支持 | 增加树形数据处理 |
| FuniAutocomplete | el-autocomplete | 完全支持 | 增加远程搜索 |
| FuniInputNumber | el-input-number | 完全支持 | 增加格式化功能 |
| FuniMoneyInput | el-input | 完全支持 | 增加金额格式化 |

### 展示组件映射

| FuniUI组件 | ElementPlus基础组件 | 支持的API | 特殊说明 |
|------------|-------------------|-----------|----------|
| FuniImage | el-image | 完全支持 | 增加图片处理功能 |
| FuniImageView | el-image-viewer | 完全支持 | 增加预览功能 |
| FuniDialog | el-dialog | 完全支持 | 增强功能和样式 |
| FuniIcon | el-icon | 完全支持 | 扩展图标库 |

### 数据组件映射

| FuniUI组件 | ElementPlus基础组件 | 支持的API | 特殊说明 |
|------------|-------------------|-----------|----------|
| FuniCurd | el-table + el-pagination | 完全支持 | 集成表格和分页 |
| FuniCurdV2 | el-table + el-pagination | 完全支持 | 增强版表格 |

### 布局组件映射

| FuniUI组件 | ElementPlus基础组件 | 支持的API | 特殊说明 |
|------------|-------------------|-----------|----------|
| FuniListPageV2 | el-tabs + el-table | 完全支持 | 集成标签页和表格 |
| FuniDetail | el-tabs | 部分支持 | 自定义步骤组件 |

## API透传示例

### FuniSelect透传示例
```vue
<template>
  <!-- 基础使用 -->
  <FuniSelect v-model="value" placeholder="请选择" />
  
  <!-- 透传ElementPlus API -->
  <FuniSelect
    v-model="value"
    placeholder="请选择选项"
    clearable
    filterable
    multiple
    collapse-tags
    collapse-tags-tooltip
    :max-collapse-tags="3"
    :loading="loading"
    :disabled="disabled"
    size="large"
    effect="dark"
    :popper-class="popperClass"
    :teleported="false"
    :persistent="false"
    :automatic-dropdown="true"
    :clear-icon="clearIcon"
    :fit-input-width="true"
    :suffix-icon="suffixIcon"
    :tag-type="tagType"
    :validate-event="false"
    :remote="true"
    :remote-method="remoteMethod"
    :reserve-keyword="true"
    :default-first-option="true"
    :popup-transition="popupTransition"
    :allow-create="true"
    :filter-method="filterMethod"
    :no-data-text="noDataText"
    :no-match-text="noMatchText"
    :loading-text="loadingText"
    @change="handleChange"
    @visible-change="handleVisibleChange"
    @remove-tag="handleRemoveTag"
    @clear="handleClear"
    @blur="handleBlur"
    @focus="handleFocus"
  >
    <!-- 透传插槽 -->
    <template #default>
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        :disabled="item.disabled"
      />
    </template>
    
    <template #prefix>
      <el-icon><Search /></el-icon>
    </template>
    
    <template #empty>
      <div>暂无数据</div>
    </template>
  </FuniSelect>
</template>

<script setup>
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'

const value = ref('')
const loading = ref(false)
const disabled = ref(false)
const options = ref([
  { label: '选项1', value: '1' },
  { label: '选项2', value: '2' },
  { label: '选项3', value: '3', disabled: true }
])

// 所有ElementPlus事件都可以正常使用
const handleChange = (value) => {
  console.log('值变化:', value)
}

const handleVisibleChange = (visible) => {
  console.log('下拉框显示状态:', visible)
}

const handleRemoveTag = (tag) => {
  console.log('移除标签:', tag)
}

const handleClear = () => {
  console.log('清空选择')
}

const handleBlur = (event) => {
  console.log('失去焦点:', event)
}

const handleFocus = (event) => {
  console.log('获得焦点:', event)
}

const remoteMethod = (query) => {
  console.log('远程搜索:', query)
  // 远程搜索逻辑
}

const filterMethod = (query) => {
  console.log('过滤方法:', query)
  // 自定义过滤逻辑
}
</script>
```

### FuniCurd透传示例
```vue
<template>
  <FuniCurd
    :columns="columns"
    :lodaData="loadData"
    
    <!-- 透传el-table所有API -->
    stripe
    border
    size="default"
    :height="400"
    :max-height="600"
    :fit="true"
    :show-header="true"
    :highlight-current-row="true"
    :current-row-key="currentRowKey"
    :row-class-name="rowClassName"
    :row-style="rowStyle"
    :cell-class-name="cellClassName"
    :cell-style="cellStyle"
    :header-row-class-name="headerRowClassName"
    :header-row-style="headerRowStyle"
    :header-cell-class-name="headerCellClassName"
    :header-cell-style="headerCellStyle"
    :row-key="rowKey"
    :empty-text="emptyText"
    :default-expand-all="false"
    :expand-row-keys="expandRowKeys"
    :default-sort="defaultSort"
    :tooltip-effect="tooltipEffect"
    :show-summary="true"
    :sum-text="sumText"
    :summary-method="summaryMethod"
    :span-method="spanMethod"
    :select-on-indeterminate="true"
    :indent="16"
    :lazy="false"
    :load="loadMethod"
    :tree-props="treeProps"
    :table-layout="tableLayout"
    :scrollbar-always-on="false"
    :flexible="false"
    
    <!-- 透传el-table所有事件 -->
    @select="handleSelect"
    @select-all="handleSelectAll"
    @selection-change="handleSelectionChange"
    @cell-mouse-enter="handleCellMouseEnter"
    @cell-mouse-leave="handleCellMouseLeave"
    @cell-click="handleCellClick"
    @cell-dblclick="handleCellDblclick"
    @cell-contextmenu="handleCellContextmenu"
    @row-click="handleRowClick"
    @row-contextmenu="handleRowContextmenu"
    @row-dblclick="handleRowDblclick"
    @header-click="handleHeaderClick"
    @header-contextmenu="handleHeaderContextmenu"
    @sort-change="handleSortChange"
    @filter-change="handleFilterChange"
    @current-change="handleCurrentChange"
    @header-dragend="handleHeaderDragend"
    @expand-change="handleExpandChange"
  >
    <!-- 透传所有插槽 -->
    <template #empty>
      <el-empty description="暂无数据" />
    </template>
    
    <template #append>
      <div class="table-append">表格底部内容</div>
    </template>
  </FuniCurd>
</template>

<script setup>
import { ref } from 'vue'

const columns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '名称', prop: 'name', minWidth: 120 },
  { label: '状态', prop: 'status', width: 100 }
])

const loadData = async (page, searchParams) => {
  // 数据加载逻辑
  return {
    list: [],
    total: 0
  }
}

// 所有ElementPlus表格事件都可以正常使用
const handleSelect = (selection, row) => {
  console.log('选择行:', selection, row)
}

const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

const handleRowClick = (row, column, event) => {
  console.log('行点击:', row, column, event)
}

const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', { column, prop, order })
}

// 其他事件处理...
</script>
```

## 样式定制

### CSS变量支持
FuniUI组件支持ElementPlus的所有CSS变量：

```css
:root {
  /* ElementPlus主题变量 */
  --el-color-primary: #409eff;
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
  --el-color-info: #909399;
  
  /* 字体变量 */
  --el-font-size-base: 14px;
  --el-font-size-small: 12px;
  --el-font-size-large: 16px;
  
  /* 间距变量 */
  --el-border-radius-base: 4px;
  --el-border-radius-small: 2px;
  --el-border-radius-round: 20px;
}
```

### 组件样式覆盖
```vue
<template>
  <FuniSelect
    class="custom-select"
    popper-class="custom-select-dropdown"
  />
</template>

<style scoped>
.custom-select {
  /* 自定义选择器样式 */
  --el-select-border-color-hover: #409eff;
}

:deep(.custom-select-dropdown) {
  /* 自定义下拉框样式 */
  --el-select-dropdown-border: 1px solid #409eff;
}
</style>
```

## 版本兼容性

### ElementPlus版本要求
- 最低版本：ElementPlus 2.0.0
- 推荐版本：ElementPlus 2.4.0+
- 测试版本：ElementPlus 2.4.4

### API兼容性说明
- 所有ElementPlus稳定API都完全支持
- 实验性API可能不支持或有限制
- 废弃API不建议使用

## 最佳实践

### 1. 属性透传
```vue
<!-- ✅ 推荐：直接使用ElementPlus属性 -->
<FuniSelect
  v-model="value"
  placeholder="请选择"
  clearable
  filterable
/>

<!-- ❌ 不推荐：重复封装已有属性 -->
<FuniSelect
  v-model="value"
  :placeholder-text="placeholderText"
  :is-clearable="isClearable"
/>
```

### 2. 事件处理
```vue
<!-- ✅ 推荐：直接使用ElementPlus事件 -->
<FuniSelect
  @change="handleChange"
  @visible-change="handleVisibleChange"
/>

<!-- ❌ 不推荐：重新定义事件名 -->
<FuniSelect
  @on-change="handleChange"
  @on-visible-change="handleVisibleChange"
/>
```

### 3. 插槽使用
```vue
<!-- ✅ 推荐：使用ElementPlus原生插槽 -->
<FuniSelect>
  <template #prefix>
    <el-icon><Search /></el-icon>
  </template>
  
  <template #empty>
    <div>暂无数据</div>
  </template>
</FuniSelect>
```

### 4. 样式定制
```vue
<!-- ✅ 推荐：使用CSS变量 -->
<FuniSelect
  style="--el-select-border-color-hover: #409eff"
/>

<!-- ✅ 推荐：使用class定制 -->
<FuniSelect class="custom-select" />
```

## 注意事项

### 1. 属性冲突
- FuniUI组件的自定义属性优先级高于透传属性
- 避免使用与FuniUI组件props同名的属性

### 2. 事件冲突
- 某些事件可能被FuniUI组件内部处理
- 建议查看组件文档确认事件支持情况

### 3. 插槽冲突
- FuniUI组件可能重新定义某些插槽
- 使用前请查看组件文档确认插槽支持

### 4. 样式隔离
- 使用scoped样式时注意深度选择器的使用
- 全局样式可能影响所有FuniUI组件
