# FuniTreeSelect API文档

## 组件概述

FuniTreeSelect是基于ElementPlus的el-tree-select封装的树形选择器组件，支持树形数据的选择、搜索、懒加载等功能，适用于层级数据选择场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| modelValue | Any | - | - | 绑定值 | el-tree-select.model-value |
| data | Array | [] | - | 树形数据 | el-tree-select.data |
| props | Object | defaultProps | - | 树节点属性配置 | el-tree-select.props |
| api | String/Function | - | - | 远程数据API | - |
| params | Object | {} | - | API请求参数 | - |
| immediate | Boolean | true | - | 是否立即加载数据 | - |
| transform | Function | - | - | 数据转换函数 | - |
| placeholder | String | '请选择' | - | 占位符文本 | el-tree-select.placeholder |
| clearable | Boolean | true | - | 是否可清空 | el-tree-select.clearable |
| filterable | Boolean | false | - | 是否可搜索 | el-tree-select.filterable |
| filterMethod | Function | - | - | 自定义搜索方法 | el-tree-select.filter-method |
| multiple | Boolean | false | - | 是否多选 | el-tree-select.multiple |
| multipleLimit | Number | 0 | - | 多选限制数量 | el-tree-select.multiple-limit |
| showCheckbox | Boolean | false | - | 是否显示复选框 | el-tree-select.show-checkbox |
| checkStrictly | Boolean | false | - | 是否严格的遵循父子不互相关联 | el-tree-select.check-strictly |
| checkOnClickNode | Boolean | false | - | 是否在点击节点时选中节点 | el-tree-select.check-on-click-node |
| expandOnClickNode | Boolean | true | - | 是否在点击节点时展开节点 | el-tree-select.expand-on-click-node |
| defaultExpandAll | Boolean | false | - | 是否默认展开所有节点 | el-tree-select.default-expand-all |
| defaultExpandedKeys | Array | [] | - | 默认展开的节点key数组 | el-tree-select.default-expanded-keys |
| lazy | Boolean | false | - | 是否懒加载子节点 | el-tree-select.lazy |
| load | Function | - | - | 加载子节点数据的函数 | el-tree-select.load |
| size | String | 'default' | - | 组件尺寸 | el-tree-select.size |
| disabled | Boolean | false | - | 是否禁用 | el-tree-select.disabled |
| valueKey | String | 'value' | - | 作为value唯一标识的键名 | el-tree-select.value-key |
| collapseTags | Boolean | false | - | 是否折叠标签 | el-tree-select.collapse-tags |
| collapseTagsTooltip | Boolean | false | - | 折叠标签提示 | el-tree-select.collapse-tags-tooltip |
| renderAfterExpand | Boolean | true | - | 是否在第一次展开某个树节点后才渲染其子节点 | el-tree-select.render-after-expand |
| accordion | Boolean | false | - | 是否每次只打开一个同级树节点展开 | el-tree-select.accordion |
| indent | Number | 18 | - | 相邻级节点间的水平缩进 | el-tree-select.indent |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: Any | 值更新事件 | 选择值变化时 |
| change | value: Any | 值变化事件 | 选择值变化时 |
| visible-change | visible: Boolean | 下拉框显示状态变化 | 下拉框显示/隐藏时 |
| remove-tag | value: Any | 移除标签事件 | 多选模式下移除标签时 |
| clear | - | 清空事件 | 点击清空按钮时 |
| blur | event: Event | 失去焦点事件 | 输入框失去焦点时 |
| focus | event: Event | 获得焦点事件 | 输入框获得焦点时 |
| node-click | (data: Object, node: Object, component: Object) | 节点点击事件 | 点击树节点时 |
| node-contextmenu | (event: Event, data: Object, node: Object, component: Object) | 节点右键事件 | 右键点击树节点时 |
| check-change | (data: Object, checked: Boolean, indeterminate: Boolean) | 节点选中状态变化事件 | 节点选中状态变化时 |
| check | (data: Object, checkedInfo: Object) | 节点被选中事件 | 节点被选中时 |
| current-change | (data: Object, node: Object) | 当前选中节点变化事件 | 当前选中节点变化时 |
| node-expand | (data: Object, node: Object, component: Object) | 节点展开事件 | 节点展开时 |
| node-collapse | (data: Object, node: Object, component: Object) | 节点收起事件 | 节点收起时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| focus | - | void | 使选择器获得焦点 |
| blur | - | void | 使选择器失去焦点 |
| refresh | - | Promise | 刷新树形数据 |
| getCheckedNodes | (leafOnly?: Boolean, includeHalfChecked?: Boolean) | Array | 获取选中的节点 |
| setCheckedNodes | (nodes: Array) | void | 设置选中的节点 |
| getCheckedKeys | (leafOnly?: Boolean) | Array | 获取选中节点的key数组 |
| setCheckedKeys | (keys: Array, leafOnly?: Boolean) | void | 设置选中节点的key数组 |
| setChecked | (data: Object/String/Number, checked: Boolean, deep: Boolean) | void | 设置节点选中状态 |
| getHalfCheckedNodes | - | Array | 获取半选中的节点 |
| getHalfCheckedKeys | - | Array | 获取半选中节点的key数组 |
| getCurrentKey | - | String/Number | 获取当前选中节点的key |
| getCurrentNode | - | Object | 获取当前选中的节点 |
| setCurrentKey | (key: String/Number) | void | 设置当前选中节点的key |
| setCurrentNode | (node: Object) | void | 设置当前选中的节点 |
| getNode | (data: Object/String/Number) | Object | 根据data或key获取节点 |
| remove | (data: Object/String/Number) | void | 删除节点 |
| append | (data: Object, parentNode: Object/String/Number) | void | 为节点追加子节点 |
| insertBefore | (data: Object, refNode: Object/String/Number) | void | 在指定节点前插入节点 |
| insertAfter | (data: Object, refNode: Object/String/Number) | void | 在指定节点后插入节点 |

## props配置结构

```typescript
interface TreeSelectProps {
  label?: string;                  // 节点标签字段名，默认'label'
  value?: string;                  // 节点值字段名，默认'value'
  children?: string;               // 子节点字段名，默认'children'
  disabled?: string;               // 节点禁用字段名，默认'disabled'
  isLeaf?: string;                 // 节点是否为叶子节点字段名，默认'isLeaf'
  class?: string;                  // 节点类名字段名
}

// 默认配置
const defaultProps = {
  label: 'label',
  value: 'value',
  children: 'children',
  disabled: 'disabled',
  isLeaf: 'isLeaf'
};
```

## 树形数据结构

```typescript
interface TreeNode {
  label: string;                   // 显示文本
  value: any;                      // 节点值
  children?: TreeNode[];           // 子节点
  disabled?: boolean;              // 是否禁用
  isLeaf?: boolean;                // 是否为叶子节点
  [key: string]: any;              // 其他自定义字段
}

type TreeData = TreeNode[];
```

## 使用示例

### 基础树形选择器
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="treeData"
    placeholder="请选择部门"
    clearable
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedValue = ref('')

const treeData = ref([
  {
    label: '技术部',
    value: 'tech',
    children: [
      { label: '前端组', value: 'frontend' },
      { label: '后端组', value: 'backend' },
      { label: '测试组', value: 'test' }
    ]
  },
  {
    label: '产品部',
    value: 'product',
    children: [
      { label: '产品组', value: 'product-team' },
      { label: '设计组', value: 'design' }
    ]
  },
  {
    label: '运营部',
    value: 'operation',
    children: [
      { label: '市场组', value: 'market' },
      { label: '客服组', value: 'service' }
    ]
  }
])

const handleChange = (value) => {
  console.log('选择值变化:', value)
}
</script>
```

### 多选树形选择器
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValues"
    :data="treeData"
    multiple
    show-checkbox
    collapse-tags
    collapse-tags-tooltip
    placeholder="请选择权限"
    @check="handleCheck"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedValues = ref([])

const treeData = ref([
  {
    label: '系统管理',
    value: 'system',
    children: [
      { label: '用户管理', value: 'user' },
      { label: '角色管理', value: 'role' },
      { label: '权限管理', value: 'permission' }
    ]
  },
  {
    label: '业务管理',
    value: 'business',
    children: [
      { label: '订单管理', value: 'order' },
      { label: '商品管理', value: 'product' },
      { label: '库存管理', value: 'inventory' }
    ]
  }
])

const handleCheck = (data, checkedInfo) => {
  console.log('选中节点:', data)
  console.log('选中信息:', checkedInfo)
}
</script>
```

### 远程数据树形选择器
```vue
<template>
  <FuniTreeSelect
    v-model="selectedDept"
    api="/api/departments"
    :props="{ label: 'name', value: 'id', children: 'children' }"
    placeholder="请选择部门"
    filterable
    :transform="transformData"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedDept = ref('')

const transformData = (data) => {
  // 数据转换，添加额外信息
  return data.map(item => ({
    ...item,
    label: `${item.name} (${item.code})`,
    value: item.id
  }))
}
</script>
```

### 懒加载树形选择器
```vue
<template>
  <FuniTreeSelect
    v-model="selectedNode"
    :data="lazyTreeData"
    lazy
    :load="loadNode"
    :props="{ isLeaf: 'leaf' }"
    placeholder="请选择节点"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedNode = ref('')

const lazyTreeData = ref([
  {
    label: '根节点1',
    value: 'root1',
    leaf: false
  },
  {
    label: '根节点2',
    value: 'root2',
    leaf: false
  }
])

const loadNode = async (node, resolve) => {
  if (node.level === 0) {
    return resolve(lazyTreeData.value)
  }
  
  if (node.level > 3) {
    return resolve([])
  }
  
  // 模拟异步加载
  setTimeout(() => {
    const data = Array.from({ length: 3 }, (_, index) => ({
      label: `${node.label}-子节点${index + 1}`,
      value: `${node.data.value}-child${index + 1}`,
      leaf: node.level >= 2
    }))
    
    resolve(data)
  }, 500)
}
</script>
```

### 可搜索树形选择器
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="searchableTreeData"
    filterable
    :filter-method="filterNode"
    placeholder="请输入关键字搜索"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedValue = ref('')

const searchableTreeData = ref([
  {
    label: '北京市',
    value: 'beijing',
    children: [
      { label: '朝阳区', value: 'chaoyang' },
      { label: '海淀区', value: 'haidian' },
      { label: '西城区', value: 'xicheng' }
    ]
  },
  {
    label: '上海市',
    value: 'shanghai',
    children: [
      { label: '浦东新区', value: 'pudong' },
      { label: '黄浦区', value: 'huangpu' },
      { label: '静安区', value: 'jingan' }
    ]
  }
])

const filterNode = (value, data) => {
  if (!value) return true
  return data.label.indexOf(value) !== -1
}
</script>
```

### 自定义节点内容
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="customTreeData"
    placeholder="请选择用户"
  >
    <template #default="{ node, data }">
      <div class="custom-tree-node">
        <el-avatar :src="data.avatar" size="small" />
        <span class="node-label">{{ data.label }}</span>
        <el-tag v-if="data.online" type="success" size="small">在线</el-tag>
      </div>
    </template>
  </FuniTreeSelect>
</template>

<script setup>
import { ref } from 'vue'

const selectedValue = ref('')

const customTreeData = ref([
  {
    label: '技术部',
    value: 'tech',
    children: [
      {
        label: '张三',
        value: 'zhangsan',
        avatar: 'https://example.com/avatar1.jpg',
        online: true
      },
      {
        label: '李四',
        value: 'lisi',
        avatar: 'https://example.com/avatar2.jpg',
        online: false
      }
    ]
  }
])
</script>

<style scoped>
.custom-tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-label {
  flex: 1;
}
</style>
```

## ElementPlus API支持

FuniTreeSelect基于el-tree-select封装，支持所有el-tree-select的API：

```vue
<template>
  <FuniTreeSelect
    v-model="value"
    :data="data"
    
    <!-- ElementPlus el-tree-select 所有属性 -->
    placeholder="请选择"
    clearable
    filterable
    multiple
    :multiple-limit="0"
    :show-checkbox="false"
    :check-strictly="false"
    :check-on-click-node="false"
    :expand-on-click-node="true"
    :default-expand-all="false"
    :default-expanded-keys="[]"
    :lazy="false"
    :load="loadMethod"
    size="default"
    :disabled="false"
    value-key="value"
    :collapse-tags="false"
    :collapse-tags-tooltip="false"
    :render-after-expand="true"
    :accordion="false"
    :indent="18"
    
    <!-- ElementPlus el-tree-select 所有事件 -->
    @change="handleChange"
    @visible-change="handleVisibleChange"
    @remove-tag="handleRemoveTag"
    @clear="handleClear"
    @blur="handleBlur"
    @focus="handleFocus"
    @node-click="handleNodeClick"
    @node-contextmenu="handleNodeContextmenu"
    @check-change="handleCheckChange"
    @check="handleCheck"
    @current-change="handleCurrentChange"
    @node-expand="handleNodeExpand"
    @node-collapse="handleNodeCollapse"
  />
</template>
```

## 注意事项

### 1. 数据结构
- 确保树形数据结构正确，包含必要的字段
- 使用props配置自定义字段映射
- 懒加载时正确设置isLeaf字段

### 2. 性能优化
- 大数据量时使用懒加载
- 合理设置renderAfterExpand避免一次性渲染大量节点
- 使用虚拟滚动处理超大数据集

### 3. 用户体验
- 提供清晰的搜索功能
- 合理设置默认展开状态
- 多选时使用折叠标签避免界面混乱

### 4. 数据处理
- 统一数据格式和字段命名
- 处理异步加载的错误情况
- 提供数据转换和格式化功能

## 常见问题

### Q: 如何实现父子节点联动选择？
A: 设置check-strictly为false（默认），父子节点会自动联动

### Q: 如何自定义节点显示内容？
A: 使用默认插槽自定义节点模板

### Q: 如何处理大量数据的性能问题？
A: 使用懒加载模式，按需加载子节点数据

### Q: 如何实现节点的动态禁用？
A: 在节点数据中设置disabled字段，或者通过computed动态计算
