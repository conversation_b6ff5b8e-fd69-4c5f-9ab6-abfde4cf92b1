# FuniTreeSelect 最佳实践

## 推荐用法

### 1. 标准树形选择器配置
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="treeData"
    placeholder="请选择选项"
    clearable
    filterable
    @change="handleChange"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

// 推荐：使用响应式数据管理选中值
const selectedValue = ref('')

// 推荐：将树形数据定义为响应式数据
const treeData = reactive([
  {
    label: '技术部',
    value: 'tech',
    children: [
      { label: '前端组', value: 'frontend' },
      { label: '后端组', value: 'backend' },
      { label: '测试组', value: 'test' }
    ]
  },
  {
    label: '产品部',
    value: 'product',
    children: [
      { label: '产品组', value: 'product-team' },
      { label: '设计组', value: 'design' }
    ]
  }
])

// 推荐：统一的变化处理
const handleChange = (value) => {
  console.log('选择值变化:', value)
  // 在这里处理业务逻辑
}
</script>
```

### 2. 多选树形选择器最佳实践
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValues"
    :data="treeData"
    multiple
    show-checkbox
    :check-strictly="checkStrictly"
    collapse-tags
    collapse-tags-tooltip
    :multiple-limit="maxSelection"
    placeholder="请选择权限"
    clearable
    @check="handleCheck"
    @check-change="handleCheckChange"
  />
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const selectedValues = ref([])
const maxSelection = 10

// 推荐：根据业务需求决定是否严格模式
const checkStrictly = computed(() => {
  // 权限选择通常使用严格模式，部门选择通常不使用
  return true
})

// 推荐：监听选择变化并进行业务验证
watch(selectedValues, (newValues, oldValues) => {
  // 数量限制检查
  if (newValues.length > maxSelection) {
    ElMessage.warning(`最多只能选择${maxSelection}个选项`)
    selectedValues.value = oldValues
    return
  }
  
  // 业务逻辑处理
  handleSelectionChange(newValues)
}, { deep: true })

const handleCheck = (data, checkedInfo) => {
  console.log('选中节点:', data)
  console.log('选中信息:', checkedInfo)
}

const handleCheckChange = (data, checked, indeterminate) => {
  console.log('节点选中状态变化:', { data, checked, indeterminate })
}

const handleSelectionChange = (values) => {
  console.log('选择项变化:', values)
  // 处理业务逻辑
}
</script>
```

### 3. 懒加载最佳实践
```vue
<template>
  <FuniTreeSelect
    v-model="selectedNode"
    :data="lazyTreeData"
    lazy
    :load="loadNode"
    :props="{ isLeaf: 'leaf' }"
    placeholder="请选择节点"
    clearable
    @node-click="handleNodeClick"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedNode = ref('')
const loadingNodes = new Set() // 记录正在加载的节点

const lazyTreeData = ref([
  {
    label: '根节点1',
    value: 'root1',
    leaf: false
  },
  {
    label: '根节点2',
    value: 'root2',
    leaf: false
  }
])

// 推荐：实现带缓存的懒加载
const nodeCache = new Map()

const loadNode = async (node, resolve) => {
  const nodeKey = node.data?.value || 'root'
  
  // 检查缓存
  if (nodeCache.has(nodeKey)) {
    return resolve(nodeCache.get(nodeKey))
  }
  
  // 防止重复加载
  if (loadingNodes.has(nodeKey)) {
    return
  }
  
  loadingNodes.add(nodeKey)
  
  try {
    // 模拟API请求
    const data = await loadNodeData(node)
    
    // 缓存结果
    nodeCache.set(nodeKey, data)
    
    resolve(data)
  } catch (error) {
    console.error('加载节点失败:', error)
    resolve([])
  } finally {
    loadingNodes.delete(nodeKey)
  }
}

const loadNodeData = async (node) => {
  if (node.level === 0) {
    return lazyTreeData.value
  }
  
  if (node.level > 3) {
    return []
  }
  
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 500))
  
  return Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, index) => ({
    label: `${node.label}-子节点${index + 1}`,
    value: `${node.data.value}-child${index + 1}`,
    leaf: node.level >= 2
  }))
}

const handleNodeClick = (data, node) => {
  console.log('点击节点:', data, node)
}
</script>
```

## 性能优化

### 1. 大数据量优化
```vue
<script setup>
import { ref, computed, shallowRef } from 'vue'

// 推荐：大量节点使用shallowRef减少响应式开销
const allTreeData = shallowRef([])
const searchKeyword = ref('')

// 推荐：使用computed进行客户端过滤
const filteredTreeData = computed(() => {
  if (!searchKeyword.value) {
    return allTreeData.value
  }
  
  return filterTreeData(allTreeData.value, searchKeyword.value)
})

// 推荐：实现高效的树形数据过滤
const filterTreeData = (data, keyword) => {
  const result = []
  
  const traverse = (nodes) => {
    return nodes.filter(node => {
      const matchesKeyword = node.label.toLowerCase().includes(keyword.toLowerCase())
      
      if (node.children) {
        const filteredChildren = traverse(node.children)
        if (filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren
          }
        }
      }
      
      return matchesKeyword
    })
  }
  
  return traverse(data)
}

// 推荐：使用虚拟滚动处理大量节点
const virtualScrollConfig = {
  itemHeight: 26,
  visibleCount: 20,
  buffer: 10
}
</script>
```

### 2. 内存管理
```vue
<script setup>
import { ref, onUnmounted } from 'vue'

const treeSelectRef = ref()
const timers = []
const caches = new Map()

// 推荐：清理资源
onUnmounted(() => {
  // 清理定时器
  timers.forEach(timer => clearTimeout(timer))
  
  // 清理缓存
  caches.clear()
  
  // 清理组件引用
  treeSelectRef.value = null
})

// 推荐：实现缓存过期机制
const setCacheWithExpire = (key, value, expireTime = 5 * 60 * 1000) => {
  caches.set(key, {
    value,
    expire: Date.now() + expireTime
  })
}

const getCacheValue = (key) => {
  const cached = caches.get(key)
  if (!cached) return null
  
  if (Date.now() > cached.expire) {
    caches.delete(key)
    return null
  }
  
  return cached.value
}
</script>
```

## 数据管理

### 1. 树形数据标准化
```vue
<script setup>
import { ref, computed } from 'vue'

const rawTreeData = ref([])

// 推荐：标准化树形数据格式
const normalizedTreeData = computed(() => {
  return normalizeTreeData(rawTreeData.value)
})

const normalizeTreeData = (data) => {
  const normalize = (nodes) => {
    return nodes.map(node => ({
      label: node.name || node.label || node.title,
      value: node.id || node.value || node.code,
      children: node.children ? normalize(node.children) : undefined,
      disabled: node.disabled || node.status === 0,
      isLeaf: node.isLeaf || !node.hasChildren,
      // 保留原始数据用于后续处理
      raw: node
    }))
  }
  
  return normalize(data)
}

// 推荐：提供获取原始数据的方法
const getOriginalData = (value) => {
  const findNode = (nodes, targetValue) => {
    for (const node of nodes) {
      if (node.value === targetValue) {
        return node.raw
      }
      if (node.children) {
        const found = findNode(node.children, targetValue)
        if (found) return found
      }
    }
    return null
  }
  
  return findNode(normalizedTreeData.value, value)
}
</script>
```

### 2. 树形数据操作工具
```vue
<script setup>
import { ref } from 'vue'

const treeData = ref([])

// 推荐：提供便捷的树形数据操作方法
const treeUtils = {
  // 扁平化树形数据
  flatten: (data) => {
    const result = []
    const traverse = (nodes, parent = null, level = 0) => {
      nodes.forEach(node => {
        result.push({
          ...node,
          parent: parent?.value || null,
          level,
          path: parent ? `${parent.path}/${node.value}` : node.value
        })
        if (node.children) {
          traverse(node.children, node, level + 1)
        }
      })
    }
    traverse(data)
    return result
  },
  
  // 查找节点
  findNode: (data, predicate) => {
    const find = (nodes) => {
      for (const node of nodes) {
        if (predicate(node)) {
          return node
        }
        if (node.children) {
          const found = find(node.children)
          if (found) return found
        }
      }
      return null
    }
    return find(data)
  },
  
  // 获取节点路径
  getNodePath: (data, targetValue) => {
    const findPath = (nodes, path = []) => {
      for (const node of nodes) {
        const currentPath = [...path, node]
        if (node.value === targetValue) {
          return currentPath
        }
        if (node.children) {
          const found = findPath(node.children, currentPath)
          if (found) return found
        }
      }
      return null
    }
    return findPath(data)
  },
  
  // 获取所有叶子节点
  getLeafNodes: (data) => {
    const leaves = []
    const traverse = (nodes) => {
      nodes.forEach(node => {
        if (!node.children || node.children.length === 0) {
          leaves.push(node)
        } else {
          traverse(node.children)
        }
      })
    }
    traverse(data)
    return leaves
  }
}
</script>
```

## 错误处理

### 1. 数据加载错误处理
```vue
<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const treeData = ref([])
const loading = ref(false)
const error = ref(null)

// 推荐：统一的错误处理函数
const handleError = (error, defaultMessage = '操作失败') => {
  console.error('树形选择器错误:', error)
  
  let message = defaultMessage
  if (error.response?.data?.message) {
    message = error.response.data.message
  } else if (error.message) {
    message = error.message
  }
  
  ElMessage.error(message)
  return message
}

// 推荐：带错误处理的数据加载
const loadTreeData = async () => {
  loading.value = true
  error.value = null
  
  try {
    const response = await api.getTreeData()
    treeData.value = response.data
  } catch (err) {
    error.value = handleError(err, '加载树形数据失败')
    treeData.value = [] // 确保有默认值
  } finally {
    loading.value = false
  }
}

// 推荐：懒加载错误处理
const loadNodeWithErrorHandling = async (node, resolve) => {
  try {
    const data = await loadNodeData(node)
    resolve(data)
  } catch (error) {
    handleError(error, '加载子节点失败')
    resolve([]) // 返回空数组避免无限加载
  }
}
</script>
```

### 2. 数据验证
```vue
<script setup>
import { ref, computed, watch } from 'vue'

const selectedValue = ref('')
const treeData = ref([])

// 推荐：验证选中值的有效性
const isValidSelection = computed(() => {
  if (!selectedValue.value) return true
  
  const findNode = (nodes, targetValue) => {
    for (const node of nodes) {
      if (node.value === targetValue) return true
      if (node.children && findNode(node.children, targetValue)) return true
    }
    return false
  }
  
  return findNode(treeData.value, selectedValue.value)
})

// 推荐：自动修复无效选择
watch([selectedValue, treeData], ([value, data]) => {
  if (value && data.length > 0 && !isValidSelection.value) {
    console.warn('检测到无效选择，自动清空:', value)
    selectedValue.value = ''
  }
})
</script>
```

## 可访问性

### 1. 无障碍支持
```vue
<template>
  <div class="tree-select-container">
    <label for="dept-tree-select" class="tree-select-label">
      部门选择
      <span class="required-mark" aria-label="必填">*</span>
    </label>
    <FuniTreeSelect
      id="dept-tree-select"
      v-model="selectedDept"
      :data="deptData"
      placeholder="请选择部门"
      clearable
      :aria-label="ariaLabel"
      :aria-describedby="ariaDescribedBy"
      @change="handleDeptChange"
    />
    <div id="dept-select-help" class="help-text">
      请从组织架构中选择一个部门
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const selectedDept = ref('')
const deptData = ref([])

// 推荐：提供适当的aria属性
const ariaLabel = computed(() => {
  return selectedDept.value ? `已选择部门: ${getSelectedDeptName()}` : '部门选择器'
})

const ariaDescribedBy = 'dept-select-help'

const getSelectedDeptName = () => {
  // 实现获取选中部门名称的逻辑
  return '技术部'
}
</script>

<style scoped>
.tree-select-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.required-mark {
  color: #f56c6c;
  margin-left: 4px;
}

.help-text {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}
</style>
```

## 测试建议

### 1. 单元测试
```javascript
// 推荐：测试树形选择器的基本功能
describe('FuniTreeSelect', () => {
  test('should emit change event when selection changes', async () => {
    const wrapper = mount(FuniTreeSelect, {
      props: {
        modelValue: '',
        data: [
          {
            label: '选项1',
            value: 'option1',
            children: [
              { label: '子选项1', value: 'child1' }
            ]
          }
        ]
      }
    })
    
    const treeSelect = wrapper.findComponent({ name: 'ElTreeSelect' })
    await treeSelect.vm.$emit('change', 'option1')
    
    expect(wrapper.emitted('change')).toBeTruthy()
    expect(wrapper.emitted('change')[0]).toEqual(['option1'])
  })
  
  test('should handle lazy loading', async () => {
    const mockLoad = jest.fn().mockImplementation((node, resolve) => {
      resolve([{ label: '动态节点', value: 'dynamic' }])
    })
    
    const wrapper = mount(FuniTreeSelect, {
      props: {
        data: [{ label: '根节点', value: 'root', isLeaf: false }],
        lazy: true,
        load: mockLoad
      }
    })
    
    // 触发懒加载
    const treeSelect = wrapper.findComponent({ name: 'ElTreeSelect' })
    await treeSelect.vm.$emit('node-expand', { value: 'root' })
    
    expect(mockLoad).toHaveBeenCalled()
  })
})
```

### 2. 集成测试
```javascript
// 推荐：测试与表单的集成
describe('FuniTreeSelect in Form', () => {
  test('should validate required tree selection', async () => {
    const wrapper = mount(FormComponent, {
      props: {
        schema: [
          {
            prop: 'department',
            component: 'FuniTreeSelect',
            props: { data: treeData },
            rules: [{ required: true, message: '请选择部门' }]
          }
        ]
      }
    })
    
    const form = wrapper.findComponent({ name: 'ElForm' })
    const result = await form.vm.validate()
    
    expect(result).toBe(false)
  })
})
```

## 常见问题

### 1. 树形数据结构问题
```vue
<script setup>
// ❌ 错误：数据结构不规范
const badTreeData = [
  { name: '节点1', id: 1 }, // 缺少value字段
  { title: '节点2', value: 2 } // 字段名不一致
]

// ✅ 正确：使用标准化的数据结构
const goodTreeData = [
  { label: '节点1', value: 1 },
  { label: '节点2', value: 2, children: [...] }
]

// ✅ 或者使用props配置映射字段
const customTreeData = [
  { name: '节点1', id: 1 },
  { name: '节点2', id: 2 }
]

const treeProps = {
  label: 'name',
  value: 'id'
}
</script>
```

### 2. 懒加载性能问题
```vue
<script setup>
// ❌ 错误：没有缓存的懒加载
const badLoadNode = async (node, resolve) => {
  // 每次都重新请求，没有缓存
  const data = await api.loadChildren(node.data.value)
  resolve(data)
}

// ✅ 正确：带缓存的懒加载
const cache = new Map()

const goodLoadNode = async (node, resolve) => {
  const key = node.data.value
  
  if (cache.has(key)) {
    return resolve(cache.get(key))
  }
  
  try {
    const data = await api.loadChildren(key)
    cache.set(key, data)
    resolve(data)
  } catch (error) {
    console.error('加载失败:', error)
    resolve([])
  }
}
</script>
```

### 3. 内存泄漏问题
```vue
<script setup>
import { onUnmounted } from 'vue'

const timers = []
const caches = new Map()

// ✅ 正确：清理资源
onUnmounted(() => {
  // 清理定时器
  timers.forEach(timer => clearTimeout(timer))
  
  // 清理缓存
  caches.clear()
  
  // 清理事件监听器
  window.removeEventListener('resize', handleResize)
})
</script>
```
