# FuniTreeSelect 配置架构

## 组件配置接口

### 基础配置类型
```typescript
interface FuniTreeSelectConfig {
  // 基础属性
  modelValue?: any;                    // 绑定值
  data?: TreeNode[];                   // 树形数据
  
  // ElementPlus el-tree-select 属性
  multiple?: boolean;                  // 是否多选
  disabled?: boolean;                  // 是否禁用
  valueKey?: string;                   // 作为value唯一标识的键名
  size?: ComponentSize;                // 输入框尺寸
  clearable?: boolean;                 // 是否可以清空选项
  collapseTags?: boolean;              // 多选时是否将选中值按文字的形式展示
  collapseTagsTooltip?: boolean;       // 当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签
  multipleLimit?: number;              // 多选时用户最多可以选择的项目数
  placeholder?: string;                // 占位符
  filterable?: boolean;                // 是否可搜索
  filterMethod?: Function;             // 自定义搜索方法
  
  // 树形特有属性
  props?: TreeSelectProps;             // 树节点属性配置
  showCheckbox?: boolean;              // 是否显示复选框
  checkStrictly?: boolean;             // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法
  checkOnClickNode?: boolean;          // 是否在点击节点的时候选中节点
  expandOnClickNode?: boolean;         // 是否在点击节点的时候展开或者收缩节点
  defaultExpandAll?: boolean;          // 是否默认展开所有节点
  defaultExpandedKeys?: Array;         // 默认展开的节点的key的数组
  lazy?: boolean;                      // 是否懒加载子节点
  load?: Function;                     // 加载子树数据的方法
  renderAfterExpand?: boolean;         // 是否在第一次展开某个树节点后才渲染其子节点
  accordion?: boolean;                 // 是否每次只打开一个同级树节点展开
  indent?: number;                     // 相邻级节点间的水平缩进，单位为像素
  
  // 事件处理
  onChange?: (value: any) => void;
  onVisibleChange?: (visible: boolean) => void;
  onRemoveTag?: (value: any) => void;
  onClear?: () => void;
  onBlur?: (event: Event) => void;
  onFocus?: (event: Event) => void;
  onNodeClick?: (data: any, node: any, component: any) => void;
  onNodeContextmenu?: (event: Event, data: any, node: any, component: any) => void;
  onCheckChange?: (data: any, checked: boolean, indeterminate: boolean) => void;
  onCheck?: (data: any, checkedInfo: any) => void;
  onCurrentChange?: (data: any, node: any) => void;
  onNodeExpand?: (data: any, node: any, component: any) => void;
  onNodeCollapse?: (data: any, node: any, component: any) => void;
}
```

### 树节点属性配置
```typescript
interface TreeSelectProps {
  label?: string;                      // 节点标签字段名，默认'label'
  value?: string;                      // 节点值字段名，默认'value'
  children?: string;                   // 子节点字段名，默认'children'
  disabled?: string;                   // 节点禁用字段名，默认'disabled'
  isLeaf?: string;                     // 节点是否为叶子节点字段名，默认'isLeaf'
  class?: string;                      // 节点类名字段名
}

// 默认配置
const defaultProps: TreeSelectProps = {
  label: 'label',
  value: 'value',
  children: 'children',
  disabled: 'disabled',
  isLeaf: 'isLeaf'
};
```

### 树节点数据类型
```typescript
interface TreeNode {
  label: string;                       // 显示文本
  value: any;                          // 节点值
  children?: TreeNode[];               // 子节点
  disabled?: boolean;                  // 是否禁用
  isLeaf?: boolean;                    // 是否为叶子节点
  class?: string;                      // 节点类名
  [key: string]: any;                  // 其他自定义字段
}

type TreeData = TreeNode[];
```

### 组件尺寸类型
```typescript
type ComponentSize = 'large' | 'default' | 'small';
```

### 懒加载函数类型
```typescript
type LoadFunction = (node: TreeNode, resolve: (data: TreeNode[]) => void) => void;
```

### 过滤函数类型
```typescript
type FilterMethod = (value: string, data: TreeNode, node: any) => boolean;
```

## 配置示例

### 基础树形选择器配置
```typescript
const basicTreeSelectConfig: FuniTreeSelectConfig = {
  modelValue: '',
  data: [
    {
      label: '技术部',
      value: 'tech',
      children: [
        { label: '前端组', value: 'frontend' },
        { label: '后端组', value: 'backend' },
        { label: '测试组', value: 'test' }
      ]
    },
    {
      label: '产品部',
      value: 'product',
      children: [
        { label: '产品组', value: 'product-team' },
        { label: '设计组', value: 'design' }
      ]
    }
  ],
  placeholder: '请选择部门',
  clearable: true,
  size: 'default'
};
```

### 多选树形选择器配置
```typescript
const multipleTreeSelectConfig: FuniTreeSelectConfig = {
  modelValue: [],
  data: [
    {
      label: '系统管理',
      value: 'system',
      children: [
        { label: '用户管理', value: 'user' },
        { label: '角色管理', value: 'role' },
        { label: '权限管理', value: 'permission' }
      ]
    },
    {
      label: '业务管理',
      value: 'business',
      children: [
        { label: '订单管理', value: 'order' },
        { label: '商品管理', value: 'product' },
        { label: '库存管理', value: 'inventory' }
      ]
    }
  ],
  multiple: true,
  showCheckbox: true,
  collapseTags: true,
  collapseTagsTooltip: true,
  multipleLimit: 5,
  placeholder: '请选择权限（最多5个）',
  clearable: true
};
```

### 懒加载配置
```typescript
const lazyLoadConfig: FuniTreeSelectConfig = {
  modelValue: '',
  data: [
    {
      label: '根节点1',
      value: 'root1',
      isLeaf: false
    },
    {
      label: '根节点2',
      value: 'root2',
      isLeaf: false
    }
  ],
  lazy: true,
  load: (node, resolve) => {
    if (node.level === 0) {
      return resolve([
        { label: '根节点1', value: 'root1', isLeaf: false },
        { label: '根节点2', value: 'root2', isLeaf: false }
      ]);
    }
    
    if (node.level > 3) {
      return resolve([]);
    }
    
    setTimeout(() => {
      const data = Array.from({ length: 3 }, (_, index) => ({
        label: `${node.label}-子节点${index + 1}`,
        value: `${node.data.value}-child${index + 1}`,
        isLeaf: node.level >= 2
      }));
      
      resolve(data);
    }, 500);
  },
  props: { isLeaf: 'isLeaf' },
  placeholder: '请选择节点',
  clearable: true
};
```

### 可搜索配置
```typescript
const filterableConfig: FuniTreeSelectConfig = {
  modelValue: '',
  data: [
    {
      label: '北京市',
      value: 'beijing',
      children: [
        { label: '朝阳区', value: 'chaoyang' },
        { label: '海淀区', value: 'haidian' },
        { label: '西城区', value: 'xicheng' }
      ]
    },
    {
      label: '上海市',
      value: 'shanghai',
      children: [
        { label: '浦东新区', value: 'pudong' },
        { label: '黄浦区', value: 'huangpu' },
        { label: '静安区', value: 'jingan' }
      ]
    }
  ],
  filterable: true,
  filterMethod: (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
  },
  placeholder: '请输入关键字搜索',
  clearable: true
};
```

### 严格模式配置
```typescript
const strictModeConfig: FuniTreeSelectConfig = {
  modelValue: [],
  data: [
    {
      label: '部门A',
      value: 'deptA',
      children: [
        { label: '组A1', value: 'groupA1' },
        { label: '组A2', value: 'groupA2' }
      ]
    },
    {
      label: '部门B',
      value: 'deptB',
      children: [
        { label: '组B1', value: 'groupB1' },
        { label: '组B2', value: 'groupB2' }
      ]
    }
  ],
  multiple: true,
  showCheckbox: true,
  checkStrictly: true, // 父子节点不关联
  placeholder: '请选择部门或组',
  clearable: true
};
```

## 表单集成配置

### 在FuniForm中使用
```typescript
interface FormTreeSelectSchema {
  prop: string;
  label: string;
  component: 'FuniTreeSelect';
  props: FuniTreeSelectConfig;
  rules?: FormRule[];
  span?: number;
  hidden?: boolean | ((formData: any) => boolean);
}

const formSchema: FormTreeSelectSchema = {
  prop: 'department',
  label: '部门',
  component: 'FuniTreeSelect',
  props: {
    data: departmentTreeData,
    placeholder: '请选择部门',
    clearable: true,
    filterable: true
  },
  rules: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  span: 12
};
```

### 在FuniSearch中使用
```typescript
interface SearchTreeSelectSchema {
  prop: string;
  label: string;
  component: 'FuniTreeSelect';
  props: FuniTreeSelectConfig;
  span?: number;
}

const searchSchema: SearchTreeSelectSchema = {
  prop: 'category',
  label: '分类',
  component: 'FuniTreeSelect',
  props: {
    data: categoryTreeData,
    multiple: true,
    collapseTags: true,
    placeholder: '请选择分类',
    clearable: true
  },
  span: 6
};
```

## 数据转换配置

### 数据格式化配置
```typescript
interface DataTransformConfig {
  // 节点数据转换
  nodeTransform?: (node: any) => TreeNode;
  
  // 树形数据扁平化
  flattenTransform?: (treeData: TreeNode[]) => any[];
  
  // 扁平数据转树形
  treeTransform?: (flatData: any[], parentKey?: string) => TreeNode[];
}

const transformConfig: DataTransformConfig = {
  nodeTransform: (node) => ({
    label: node.name || node.title,
    value: node.id || node.code,
    children: node.children || node.subItems,
    disabled: node.disabled || node.status === 0,
    isLeaf: node.isLeaf || !node.hasChildren
  }),
  
  flattenTransform: (treeData) => {
    const result = [];
    const traverse = (nodes, parent = null) => {
      nodes.forEach(node => {
        result.push({
          ...node,
          parent: parent?.value || null,
          level: parent ? parent.level + 1 : 0
        });
        if (node.children) {
          traverse(node.children, { ...node, level: parent ? parent.level + 1 : 0 });
        }
      });
    };
    traverse(treeData);
    return result;
  },
  
  treeTransform: (flatData, parentKey = 'parentId') => {
    const map = new Map();
    const roots = [];
    
    flatData.forEach(item => {
      map.set(item.id, { ...item, children: [] });
    });
    
    flatData.forEach(item => {
      const node = map.get(item.id);
      if (item[parentKey]) {
        const parent = map.get(item[parentKey]);
        if (parent) {
          parent.children.push(node);
        }
      } else {
        roots.push(node);
      }
    });
    
    return roots;
  }
};
```

## 验证规则配置

### 常用验证规则
```typescript
interface TreeSelectValidationRules {
  required?: boolean;
  message?: string;
  trigger?: string | string[];
  validator?: (rule: any, value: any, callback: Function) => void;
}

const validationRules: TreeSelectValidationRules[] = [
  // 必填验证
  { required: true, message: '请选择选项', trigger: 'change' },
  
  // 多选数量验证
  {
    validator: (rule, value, callback) => {
      if (Array.isArray(value) && value.length > 3) {
        callback(new Error('最多只能选择3个选项'));
      } else {
        callback();
      }
    },
    trigger: 'change'
  },
  
  // 叶子节点验证
  {
    validator: (rule, value, callback) => {
      if (value && !isLeafNode(value)) {
        callback(new Error('只能选择叶子节点'));
      } else {
        callback();
      }
    },
    trigger: 'change'
  }
];
```

## 性能优化配置

### 大数据量优化
```typescript
interface PerformanceConfig {
  // 虚拟滚动配置
  virtualScroll?: boolean;
  itemHeight?: number;
  visibleCount?: number;
  
  // 懒加载配置
  lazy?: boolean;
  renderAfterExpand?: boolean;
  
  // 缓存配置
  cache?: boolean;
  cacheKey?: string;
  cacheExpire?: number;
}

const performanceConfig: PerformanceConfig = {
  virtualScroll: true,
  itemHeight: 26,
  visibleCount: 10,
  lazy: true,
  renderAfterExpand: true,
  cache: true,
  cacheKey: 'tree-select-data',
  cacheExpire: 300000 // 5分钟
};
```
