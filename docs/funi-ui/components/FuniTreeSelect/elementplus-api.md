# FuniTreeSelect ElementPlus API支持

## 概述

FuniTreeSelect是基于ElementPlus的`el-tree-select`组件的直接透传封装，通过`v-bind="$attrs"`实现了对ElementPlus树形选择器API的完全透传支持。这意味着您可以直接使用所有ElementPlus树形选择器组件的原生属性、事件和方法。

## 支持的ElementPlus API

### el-tree-select 属性透传

FuniTreeSelect支持所有`el-tree-select`的原生属性：

```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="treeData"
    
    <!-- ElementPlus el-tree-select 原生属性 -->
    :multiple="false"
    :disabled="false"
    value-key="value"
    size="default"
    :clearable="true"
    :collapse-tags="false"
    :collapse-tags-tooltip="false"
    :multiple-limit="0"
    placeholder="请选择"
    :filterable="false"
    :filter-method="filterMethod"
    
    <!-- 树形特有属性 -->
    :props="treeProps"
    :show-checkbox="false"
    :check-strictly="false"
    :check-on-click-node="false"
    :expand-on-click-node="true"
    :default-expand-all="false"
    :default-expanded-keys="[]"
    :lazy="false"
    :load="loadMethod"
    :render-after-expand="true"
    :accordion="false"
    :indent="18"
    
    <!-- ElementPlus el-tree-select 原生事件 -->
    @change="handleChange"
    @visible-change="handleVisibleChange"
    @remove-tag="handleRemoveTag"
    @clear="handleClear"
    @blur="handleBlur"
    @focus="handleFocus"
    @node-click="handleNodeClick"
    @node-contextmenu="handleNodeContextmenu"
    @check-change="handleCheckChange"
    @check="handleCheck"
    @current-change="handleCurrentChange"
    @node-expand="handleNodeExpand"
    @node-collapse="handleNodeCollapse"
  />
</template>
```

### 属性详细说明

#### 基础选择器属性
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| multiple | Boolean | false | 是否多选 |
| disabled | Boolean | false | 是否禁用 |
| value-key | String | value | 作为value唯一标识的键名 |
| size | String | default | 输入框尺寸，可选值：large/default/small |
| clearable | Boolean | false | 是否可以清空选项 |
| collapse-tags | Boolean | false | 多选时是否将选中值按文字的形式展示 |
| collapse-tags-tooltip | Boolean | false | 当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签 |
| multiple-limit | Number | 0 | 多选时用户最多可以选择的项目数，为0则不限制 |
| placeholder | String | 请选择 | 占位符 |
| filterable | Boolean | false | 是否可搜索 |
| filter-method | Function | — | 自定义搜索方法 |

#### 树形特有属性
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | Array | [] | 展示数据 |
| props | Object | — | 配置选项，具体看下表 |
| show-checkbox | Boolean | false | 节点是否可被选择 |
| check-strictly | Boolean | false | 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法 |
| check-on-click-node | Boolean | false | 是否在点击节点的时候选中节点 |
| expand-on-click-node | Boolean | true | 是否在点击节点的时候展开或者收缩节点 |
| default-expand-all | Boolean | false | 是否默认展开所有节点 |
| default-expanded-keys | Array | — | 默认展开的节点的key的数组 |
| lazy | Boolean | false | 是否懒加载子节点，需与load方法结合使用 |
| load | Function | — | 加载子树数据的方法，仅当lazy属性为true时生效 |
| render-after-expand | Boolean | true | 是否在第一次展开某个树节点后才渲染其子节点 |
| accordion | Boolean | false | 是否每次只打开一个同级树节点展开 |
| indent | Number | 18 | 相邻级节点间的水平缩进，单位为像素 |

#### props配置项
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| label | String | label | 指定节点标签为节点对象的某个属性值 |
| children | String | children | 指定子树为节点对象的某个属性值 |
| disabled | String | disabled | 指定节点选择框是否禁用为节点对象的某个属性值 |
| isLeaf | String | isLeaf | 指定节点是否为叶子节点，仅在指定了lazy属性的情况下生效 |
| class | String | — | 自定义节点类名 |

### 事件透传

FuniTreeSelect支持所有`el-tree-select`的原生事件：

```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="treeData"
    @change="handleChange"
    @visible-change="handleVisibleChange"
    @remove-tag="handleRemoveTag"
    @clear="handleClear"
    @blur="handleBlur"
    @focus="handleFocus"
    @node-click="handleNodeClick"
    @node-contextmenu="handleNodeContextmenu"
    @check-change="handleCheckChange"
    @check="handleCheck"
    @current-change="handleCurrentChange"
    @node-expand="handleNodeExpand"
    @node-collapse="handleNodeCollapse"
  />
</template>

<script setup>
// 选择器事件
const handleChange = (value) => {
  console.log('值变化:', value)
}

const handleVisibleChange = (visible) => {
  console.log('下拉框显示状态:', visible)
}

const handleRemoveTag = (value) => {
  console.log('移除标签:', value)
}

const handleClear = () => {
  console.log('清空选择')
}

const handleBlur = (event) => {
  console.log('失去焦点:', event)
}

const handleFocus = (event) => {
  console.log('获得焦点:', event)
}

// 树形事件
const handleNodeClick = (data, node, component) => {
  console.log('节点点击:', { data, node, component })
}

const handleNodeContextmenu = (event, data, node, component) => {
  console.log('节点右键:', { event, data, node, component })
}

const handleCheckChange = (data, checked, indeterminate) => {
  console.log('节点选中状态变化:', { data, checked, indeterminate })
}

const handleCheck = (data, checkedInfo) => {
  console.log('节点被选中:', { data, checkedInfo })
}

const handleCurrentChange = (data, node) => {
  console.log('当前选中节点变化:', { data, node })
}

const handleNodeExpand = (data, node, component) => {
  console.log('节点展开:', { data, node, component })
}

const handleNodeCollapse = (data, node, component) => {
  console.log('节点收缩:', { data, node, component })
}
</script>
```

### 方法透传

通过模板引用可以访问所有ElementPlus树形选择器方法：

```vue
<template>
  <FuniTreeSelect
    ref="treeSelectRef"
    v-model="selectedValue"
    :data="treeData"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'

const treeSelectRef = ref()
const selectedValue = ref('')

onMounted(() => {
  // 访问ElementPlus原生方法
  const treeSelectInstance = treeSelectRef.value
  
  // 选择器方法
  treeSelectInstance.focus()  // 使选择器获得焦点
  treeSelectInstance.blur()   // 使选择器失去焦点
})

// 树形操作方法
const getCheckedNodes = (leafOnly = false, includeHalfChecked = false) => {
  return treeSelectRef.value?.getCheckedNodes(leafOnly, includeHalfChecked)
}

const setCheckedNodes = (nodes) => {
  treeSelectRef.value?.setCheckedNodes(nodes)
}

const getCheckedKeys = (leafOnly = false) => {
  return treeSelectRef.value?.getCheckedKeys(leafOnly)
}

const setCheckedKeys = (keys, leafOnly = false) => {
  treeSelectRef.value?.setCheckedKeys(keys, leafOnly)
}

const setChecked = (data, checked, deep = false) => {
  treeSelectRef.value?.setChecked(data, checked, deep)
}

const getHalfCheckedNodes = () => {
  return treeSelectRef.value?.getHalfCheckedNodes()
}

const getHalfCheckedKeys = () => {
  return treeSelectRef.value?.getHalfCheckedKeys()
}

const getCurrentKey = () => {
  return treeSelectRef.value?.getCurrentKey()
}

const getCurrentNode = () => {
  return treeSelectRef.value?.getCurrentNode()
}

const setCurrentKey = (key) => {
  treeSelectRef.value?.setCurrentKey(key)
}

const setCurrentNode = (node) => {
  treeSelectRef.value?.setCurrentNode(node)
}

const getNode = (data) => {
  return treeSelectRef.value?.getNode(data)
}

const remove = (data) => {
  treeSelectRef.value?.remove(data)
}

const append = (data, parentNode) => {
  treeSelectRef.value?.append(data, parentNode)
}

const insertBefore = (data, refNode) => {
  treeSelectRef.value?.insertBefore(data, refNode)
}

const insertAfter = (data, refNode) => {
  treeSelectRef.value?.insertAfter(data, refNode)
}
</script>
```

## 插槽透传

FuniTreeSelect支持所有`el-tree-select`的插槽：

```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="treeData"
  >
    <!-- 默认插槽：自定义树节点内容 -->
    <template #default="{ node, data }">
      <span class="custom-tree-node">
        <span class="node-label">{{ data.label }}</span>
        <span class="node-count" v-if="data.count">({{ data.count }})</span>
      </span>
    </template>
    
    <!-- 前缀插槽 -->
    <template #prefix>
      <el-icon><Search /></el-icon>
    </template>
    
    <!-- 空数据插槽 -->
    <template #empty>
      <div class="empty-data">
        <el-icon><DocumentDelete /></el-icon>
        <p>暂无数据</p>
      </div>
    </template>
  </FuniTreeSelect>
</template>

<style scoped>
.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.node-label {
  flex: 1;
}

.node-count {
  color: #909399;
  font-size: 12px;
}

.empty-data {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.empty-data .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
}
</style>
```

## 高级功能API

### 懒加载配置
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="lazyTreeData"
    lazy
    :load="loadNode"
    :props="{ isLeaf: 'leaf' }"
  />
</template>

<script setup>
const loadNode = (node, resolve) => {
  if (node.level === 0) {
    return resolve([
      { label: '根节点1', value: 'root1', leaf: false },
      { label: '根节点2', value: 'root2', leaf: false }
    ])
  }
  
  if (node.level > 3) {
    return resolve([])
  }
  
  setTimeout(() => {
    const data = Array.from({ length: 3 }, (_, index) => ({
      label: `${node.label}-子节点${index + 1}`,
      value: `${node.data.value}-child${index + 1}`,
      leaf: node.level >= 2
    }))
    
    resolve(data)
  }, 500)
}
</script>
```

### 自定义过滤方法
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="treeData"
    filterable
    :filter-method="customFilter"
  />
</template>

<script setup>
const customFilter = (value, data, node) => {
  if (!value) return true
  
  // 支持拼音搜索、模糊匹配等
  return data.label.toLowerCase().includes(value.toLowerCase()) ||
         data.pinyin?.toLowerCase().includes(value.toLowerCase()) ||
         data.code?.includes(value)
}
</script>
```

### 多选配置
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValues"
    :data="treeData"
    multiple
    show-checkbox
    :check-strictly="false"
    collapse-tags
    collapse-tags-tooltip
    :multiple-limit="10"
    @check="handleCheck"
    @check-change="handleCheckChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedValues = ref([])

const handleCheck = (data, checkedInfo) => {
  console.log('选中节点:', data)
  console.log('选中信息:', checkedInfo)
  // checkedInfo包含：checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys
}

const handleCheckChange = (data, checked, indeterminate) => {
  console.log('节点选中状态变化:', {
    node: data,
    checked,
    indeterminate
  })
}
</script>
```

## 样式定制

### CSS变量支持

FuniTreeSelect继承了ElementPlus的CSS变量系统：

```css
:root {
  /* 树形选择器相关变量 */
  --el-tree-select-border-color: #dcdfe6;
  --el-tree-select-border-color-hover: #c0c4cc;
  --el-tree-select-border-radius: 4px;
  --el-tree-select-font-size: 14px;
  --el-tree-select-height: 32px;
  
  /* 下拉框相关变量 */
  --el-tree-select-dropdown-bg: #fff;
  --el-tree-select-dropdown-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --el-tree-select-dropdown-max-height: 274px;
  
  /* 树节点相关变量 */
  --el-tree-node-hover-bg: #f5f7fa;
  --el-tree-node-content-height: 26px;
  --el-tree-expand-icon-color: #c0c4cc;
}
```

### 自定义样式类
```vue
<template>
  <FuniTreeSelect
    v-model="selectedValue"
    :data="treeData"
    popper-class="custom-tree-select-dropdown"
    class="custom-tree-select"
  />
</template>

<style>
.custom-tree-select {
  /* 自定义选择器样式 */
}

.custom-tree-select-dropdown {
  /* 自定义下拉框样式 */
}

.custom-tree-select-dropdown .el-tree-node__content {
  /* 自定义树节点样式 */
}
</style>
```

## 兼容性说明

FuniTreeSelect与ElementPlus版本兼容性：
- 支持ElementPlus 2.0+
- 建议使用ElementPlus 2.3.0及以上版本以获得最佳体验
- 所有ElementPlus树形选择器相关组件的API都得到完整支持

## 注意事项

### 1. 完全透传
- FuniTreeSelect是完全透传的封装，不添加任何自定义逻辑
- 所有ElementPlus的原生功能都可以直接使用
- 性能与ElementPlus原生组件完全一致

### 2. 插槽支持
- 支持所有ElementPlus树形选择器的插槽
- 插槽参数与ElementPlus完全一致
- 可以自定义节点内容、空状态等

### 3. 方法调用
- 通过模板引用可以直接访问ElementPlus的所有原生方法
- 方法签名与ElementPlus完全一致
- 支持所有树形操作和选择器操作

### 4. 事件处理
- 所有事件都是ElementPlus的原生事件
- 事件参数与ElementPlus完全一致
- 支持所有树形事件和选择器事件
