# FuniTreeSelect 使用示例

## 基础使用

### 简单树形选择器
```vue
<template>
  <div class="basic-tree-select-demo">
    <h3>基础树形选择器</h3>
    <FuniTreeSelect
      v-model="selectedValue"
      :data="treeData"
      placeholder="请选择部门"
      clearable
      @change="handleChange"
    />
    <p>选中值: {{ selectedValue }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedValue = ref('')

const treeData = ref([
  {
    label: '技术部',
    value: 'tech',
    children: [
      { label: '前端组', value: 'frontend' },
      { label: '后端组', value: 'backend' },
      { label: '测试组', value: 'test' },
      { label: '运维组', value: 'devops' }
    ]
  },
  {
    label: '产品部',
    value: 'product',
    children: [
      { label: '产品组', value: 'product-team' },
      { label: '设计组', value: 'design' },
      { label: '用研组', value: 'research' }
    ]
  },
  {
    label: '运营部',
    value: 'operation',
    children: [
      { label: '市场组', value: 'market' },
      { label: '客服组', value: 'service' },
      { label: '商务组', value: 'business' }
    ]
  }
])

const handleChange = (value) => {
  console.log('选择值变化:', value)
}
</script>

<style scoped>
.basic-tree-select-demo {
  padding: 20px;
}
</style>
```

### 多选树形选择器
```vue
<template>
  <div class="multiple-tree-select-demo">
    <h3>多选树形选择器</h3>
    <FuniTreeSelect
      v-model="selectedValues"
      :data="permissionData"
      multiple
      show-checkbox
      collapse-tags
      collapse-tags-tooltip
      :multiple-limit="5"
      placeholder="请选择权限（最多5个）"
      clearable
      @check="handleCheck"
      @remove-tag="handleRemoveTag"
    />
    <p>选中值: {{ selectedValues }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const selectedValues = ref([])

const permissionData = ref([
  {
    label: '系统管理',
    value: 'system',
    children: [
      { label: '用户管理', value: 'user' },
      { label: '角色管理', value: 'role' },
      { label: '权限管理', value: 'permission' },
      { label: '菜单管理', value: 'menu' }
    ]
  },
  {
    label: '业务管理',
    value: 'business',
    children: [
      { label: '订单管理', value: 'order' },
      { label: '商品管理', value: 'product' },
      { label: '库存管理', value: 'inventory' },
      { label: '财务管理', value: 'finance' }
    ]
  },
  {
    label: '内容管理',
    value: 'content',
    children: [
      { label: '文章管理', value: 'article' },
      { label: '媒体管理', value: 'media' },
      { label: '评论管理', value: 'comment' }
    ]
  }
])

const handleCheck = (data, checkedInfo) => {
  console.log('选中节点:', data)
  console.log('选中信息:', checkedInfo)
}

const handleRemoveTag = (value) => {
  ElMessage.info(`移除了权限: ${value}`)
}
</script>

<style scoped>
.multiple-tree-select-demo {
  padding: 20px;
}
</style>
```

## 高级功能

### 懒加载树形选择器
```vue
<template>
  <div class="lazy-load-demo">
    <h3>懒加载树形选择器</h3>
    <FuniTreeSelect
      v-model="selectedNode"
      :data="lazyTreeData"
      lazy
      :load="loadNode"
      :props="{ isLeaf: 'leaf' }"
      placeholder="请选择节点"
      clearable
      @node-click="handleNodeClick"
    />
    <p v-if="selectedNode">选中节点: {{ selectedNode }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedNode = ref('')

const lazyTreeData = ref([
  {
    label: '根节点1',
    value: 'root1',
    leaf: false
  },
  {
    label: '根节点2',
    value: 'root2',
    leaf: false
  },
  {
    label: '叶子节点',
    value: 'leaf1',
    leaf: true
  }
])

const loadNode = async (node, resolve) => {
  if (node.level === 0) {
    return resolve(lazyTreeData.value)
  }
  
  if (node.level > 3) {
    return resolve([])
  }
  
  // 模拟异步加载
  setTimeout(() => {
    const data = Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, index) => ({
      label: `${node.label}-子节点${index + 1}`,
      value: `${node.data.value}-child${index + 1}`,
      leaf: node.level >= 2 // 第3层设为叶子节点
    }))
    
    resolve(data)
  }, 500)
}

const handleNodeClick = (data, node) => {
  console.log('点击节点:', data, node)
}
</script>

<style scoped>
.lazy-load-demo {
  padding: 20px;
}
</style>
```

### 可搜索树形选择器
```vue
<template>
  <div class="filterable-demo">
    <h3>可搜索树形选择器</h3>
    <FuniTreeSelect
      v-model="selectedArea"
      :data="areaData"
      filterable
      :filter-method="filterNode"
      placeholder="请输入关键字搜索地区"
      clearable
      @change="handleAreaChange"
    />
    <p v-if="selectedArea">选中地区: {{ getSelectedAreaName() }}</p>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const selectedArea = ref('')

const areaData = ref([
  {
    label: '北京市',
    value: 'beijing',
    children: [
      { label: '朝阳区', value: 'chaoyang' },
      { label: '海淀区', value: 'haidian' },
      { label: '西城区', value: 'xicheng' },
      { label: '东城区', value: 'dongcheng' },
      { label: '丰台区', value: 'fengtai' }
    ]
  },
  {
    label: '上海市',
    value: 'shanghai',
    children: [
      { label: '浦东新区', value: 'pudong' },
      { label: '黄浦区', value: 'huangpu' },
      { label: '静安区', value: 'jingan' },
      { label: '徐汇区', value: 'xuhui' },
      { label: '长宁区', value: 'changning' }
    ]
  },
  {
    label: '广东省',
    value: 'guangdong',
    children: [
      {
        label: '广州市',
        value: 'guangzhou',
        children: [
          { label: '天河区', value: 'tianhe' },
          { label: '越秀区', value: 'yuexiu' }
        ]
      },
      {
        label: '深圳市',
        value: 'shenzhen',
        children: [
          { label: '南山区', value: 'nanshan' },
          { label: '福田区', value: 'futian' }
        ]
      }
    ]
  }
])

const filterNode = (value, data) => {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase())
}

const handleAreaChange = (value) => {
  console.log('选择地区:', value)
}

// 获取选中地区的名称
const getSelectedAreaName = () => {
  const findNode = (nodes, targetValue) => {
    for (const node of nodes) {
      if (node.value === targetValue) {
        return node.label
      }
      if (node.children) {
        const found = findNode(node.children, targetValue)
        if (found) return found
      }
    }
    return null
  }
  
  return findNode(areaData.value, selectedArea.value) || selectedArea.value
}
</script>

<style scoped>
.filterable-demo {
  padding: 20px;
}
</style>
```

### 严格模式选择器
```vue
<template>
  <div class="strict-mode-demo">
    <h3>严格模式（父子不关联）</h3>
    <FuniTreeSelect
      v-model="selectedDepts"
      :data="deptData"
      multiple
      show-checkbox
      check-strictly
      collapse-tags
      placeholder="请选择部门或组（父子不关联）"
      clearable
      @check-change="handleCheckChange"
    />
    <p>选中值: {{ selectedDepts }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedDepts = ref([])

const deptData = ref([
  {
    label: '技术中心',
    value: 'tech-center',
    children: [
      {
        label: '研发部',
        value: 'rd-dept',
        children: [
          { label: '前端组', value: 'frontend-group' },
          { label: '后端组', value: 'backend-group' },
          { label: '移动组', value: 'mobile-group' }
        ]
      },
      {
        label: '测试部',
        value: 'qa-dept',
        children: [
          { label: '功能测试组', value: 'func-test-group' },
          { label: '自动化测试组', value: 'auto-test-group' }
        ]
      }
    ]
  },
  {
    label: '产品中心',
    value: 'product-center',
    children: [
      {
        label: '产品部',
        value: 'product-dept',
        children: [
          { label: '产品策划组', value: 'product-plan-group' },
          { label: '产品运营组', value: 'product-ops-group' }
        ]
      },
      {
        label: '设计部',
        value: 'design-dept',
        children: [
          { label: 'UI设计组', value: 'ui-design-group' },
          { label: 'UX设计组', value: 'ux-design-group' }
        ]
      }
    ]
  }
])

const handleCheckChange = (data, checked, indeterminate) => {
  console.log('节点选中状态变化:', {
    node: data,
    checked,
    indeterminate
  })
}
</script>

<style scoped>
.strict-mode-demo {
  padding: 20px;
}
</style>
```

## 表单集成示例

### 在FuniForm中使用
```vue
<template>
  <div class="form-integration-demo">
    <h3>表单集成示例</h3>
    <FuniForm
      v-model="formData"
      :schema="formSchema"
      :col="2"
      @submit="handleSubmit"
      @reset="handleReset"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const formData = ref({
  name: '',
  department: '',
  permissions: [],
  region: '',
  category: ''
})

const formSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniTreeSelect',
    props: {
      data: [
        {
          label: '技术部',
          value: 'tech',
          children: [
            { label: '前端组', value: 'frontend' },
            { label: '后端组', value: 'backend' }
          ]
        },
        {
          label: '产品部',
          value: 'product',
          children: [
            { label: '产品组', value: 'product-team' },
            { label: '设计组', value: 'design' }
          ]
        }
      ],
      placeholder: '请选择部门',
      clearable: true
    },
    rules: [{ required: true, message: '请选择部门', trigger: 'change' }]
  },
  {
    prop: 'permissions',
    label: '权限',
    component: 'FuniTreeSelect',
    props: {
      data: [
        {
          label: '系统管理',
          value: 'system',
          children: [
            { label: '用户管理', value: 'user' },
            { label: '角色管理', value: 'role' }
          ]
        },
        {
          label: '业务管理',
          value: 'business',
          children: [
            { label: '订单管理', value: 'order' },
            { label: '商品管理', value: 'product' }
          ]
        }
      ],
      multiple: true,
      showCheckbox: true,
      collapseTags: true,
      placeholder: '请选择权限',
      clearable: true
    }
  },
  {
    prop: 'region',
    label: '地区',
    component: 'FuniTreeSelect',
    props: {
      data: [
        {
          label: '华北地区',
          value: 'north',
          children: [
            { label: '北京', value: 'beijing' },
            { label: '天津', value: 'tianjin' }
          ]
        },
        {
          label: '华东地区',
          value: 'east',
          children: [
            { label: '上海', value: 'shanghai' },
            { label: '江苏', value: 'jiangsu' }
          ]
        }
      ],
      filterable: true,
      placeholder: '请选择地区',
      clearable: true
    }
  },
  {
    prop: 'category',
    label: '分类',
    component: 'FuniTreeSelect',
    props: {
      data: [
        {
          label: '电子产品',
          value: 'electronics',
          children: [
            { label: '手机', value: 'phone' },
            { label: '电脑', value: 'computer' }
          ]
        },
        {
          label: '服装',
          value: 'clothing',
          children: [
            { label: '男装', value: 'mens' },
            { label: '女装', value: 'womens' }
          ]
        }
      ],
      checkStrictly: true,
      placeholder: '请选择分类',
      clearable: true
    }
  }
])

const handleSubmit = (data) => {
  console.log('提交表单:', data)
  ElMessage.success('提交成功')
}

const handleReset = () => {
  ElMessage.info('表单已重置')
}
</script>

<style scoped>
.form-integration-demo {
  padding: 20px;
  max-width: 800px;
}
</style>
```

## 业务场景示例

### 组织架构选择器
```vue
<template>
  <div class="org-structure-demo">
    <h3>组织架构选择器</h3>

    <div class="demo-section">
      <h4>单选模式 - 选择直属部门</h4>
      <FuniTreeSelect
        v-model="selectedDepartment"
        :data="orgData"
        placeholder="请选择部门"
        clearable
        filterable
        @change="handleDepartmentChange"
      />
    </div>

    <div class="demo-section">
      <h4>多选模式 - 选择管理范围</h4>
      <FuniTreeSelect
        v-model="managementScope"
        :data="orgData"
        multiple
        show-checkbox
        check-strictly
        collapse-tags
        collapse-tags-tooltip
        placeholder="请选择管理范围"
        clearable
      />
    </div>

    <div class="demo-section">
      <h4>层级限制 - 只能选择叶子节点</h4>
      <FuniTreeSelect
        v-model="leafOnlySelection"
        :data="orgData"
        :check-on-click-node="false"
        placeholder="请选择具体岗位"
        clearable
        @node-click="handleLeafNodeClick"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const selectedDepartment = ref('')
const managementScope = ref([])
const leafOnlySelection = ref('')

const orgData = ref([
  {
    label: '总经理办公室',
    value: 'ceo-office',
    children: [
      { label: '总经理', value: 'ceo', isLeaf: true },
      { label: '总经理助理', value: 'ceo-assistant', isLeaf: true }
    ]
  },
  {
    label: '技术中心',
    value: 'tech-center',
    children: [
      {
        label: '研发部',
        value: 'rd-dept',
        children: [
          { label: '前端工程师', value: 'frontend-engineer', isLeaf: true },
          { label: '后端工程师', value: 'backend-engineer', isLeaf: true },
          { label: '架构师', value: 'architect', isLeaf: true }
        ]
      },
      {
        label: '测试部',
        value: 'qa-dept',
        children: [
          { label: '测试工程师', value: 'qa-engineer', isLeaf: true },
          { label: '测试主管', value: 'qa-lead', isLeaf: true }
        ]
      }
    ]
  },
  {
    label: '产品中心',
    value: 'product-center',
    children: [
      {
        label: '产品部',
        value: 'product-dept',
        children: [
          { label: '产品经理', value: 'pm', isLeaf: true },
          { label: '产品助理', value: 'product-assistant', isLeaf: true }
        ]
      },
      {
        label: '设计部',
        value: 'design-dept',
        children: [
          { label: 'UI设计师', value: 'ui-designer', isLeaf: true },
          { label: 'UX设计师', value: 'ux-designer', isLeaf: true }
        ]
      }
    ]
  }
])

const handleDepartmentChange = (value) => {
  console.log('选择部门:', value)
}

const handleLeafNodeClick = (data, node) => {
  if (data.isLeaf) {
    leafOnlySelection.value = data.value
    ElMessage.success(`选择了岗位: ${data.label}`)
  } else {
    ElMessage.warning('请选择具体岗位')
  }
}
</script>

<style scoped>
.org-structure-demo {
  padding: 20px;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-section h4 {
  margin-bottom: 10px;
  color: #606266;
  font-size: 14px;
}
</style>
```
