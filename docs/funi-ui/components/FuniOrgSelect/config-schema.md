# FuniOrgSelect 配置结构定义

## 组件配置接口

### IFuniOrgSelectProps

```typescript
interface IFuniOrgSelectProps {
  /** 绑定值，单选时为字符串，多选时为数组 */
  modelValue?: string | string[]
  /** 选择模式 */
  mode?: 'radio' | 'checkbox'
  /** 搜索配置对象 */
  searchConfig?: ISearchConfig
  /** 搜索表单模型 */
  searchFormModel?: Record<string, any>
  /** 属性配置对象 */
  attribute?: IAttributeConfig
  /** 操作配置对象 */
  operate?: IOperateConfig
  /** 可选择的组织范围限制 */
  selectRange?: IOrgItem[]
}
```

### IFuniOrgSelectEmits

```typescript
interface IFuniOrgSelectEmits {
  /** v-model 更新事件 */
  'update:modelValue': (value: string | string[]) => void
}
```

## 配置对象结构

### ISearchConfig

```typescript
interface ISearchConfig {
  /** 搜索字段属性名 */
  prop: string
  /** 搜索类型 */
  type?: 'text' | 'select' | 'date'
  /** 搜索标签 */
  label?: string
  /** 搜索占位符 */
  placeholder?: string
}
```

### IAttributeConfig

```typescript
interface IAttributeConfig {
  /** 是否启用动态加载 */
  dynamic?: boolean
  /** 动态加载接口地址 */
  slotUrl?: string
  /** 接口请求参数 */
  params?: Record<string, any>
  /** 数据处理配置 */
  dataProcessor?: IDataProcessor
}
```

### IOperateConfig

```typescript
interface IOperateConfig {
  /** 操作类型 */
  type?: 'view' | 'edit' | 'select'
  /** 操作权限 */
  permissions?: string[]
  /** 操作回调 */
  callback?: (data: IOrgItem) => void
}
```

### IDataProcessor

```typescript
interface IDataProcessor {
  /** 数据转换函数 */
  transform?: (data: any[]) => IOrgItem[]
  /** 数据过滤函数 */
  filter?: (item: IOrgItem) => boolean
  /** 数据排序函数 */
  sort?: (a: IOrgItem, b: IOrgItem) => number
}
```

## 组织数据结构

### IOrgItem

```typescript
interface IOrgItem {
  /** 组织ID */
  id: string
  /** 组织名称 */
  orgName: string
  /** 组织简称 */
  orgShortName?: string
  /** 显示名称（自动生成） */
  orgShowName: string
  /** 组织层级 */
  level: number
  /** 父组织ID */
  parentId?: string
  /** 子组织列表 */
  children?: IOrgItem[]
  /** 是否禁用 */
  disabled?: boolean
  /** 组织类型 */
  orgType?: string
  /** 组织状态 */
  status?: 'active' | 'inactive'
  /** 扩展属性 */
  [key: string]: any
}
```

### IOrgTreeProps

```typescript
interface IOrgTreeProps {
  /** 节点值字段 */
  value: 'id'
  /** 节点显示字段 */
  label: 'orgShowName'
  /** 子节点字段 */
  children: 'children'
}
```

## 内部状态结构

### IComponentState

```typescript
interface IComponentState {
  /** 组织数据 */
  data: Ref<IOrgItem[]>
  /** 当前选中值 */
  value: Ref<string | string[]>
  /** 默认展开的节点 */
  defaultExpand: Ref<string[]>
  /** 选择数据（计算属性） */
  selectData: ComputedRef<IOrgItem[]>
}
```

## API 接口配置

### IGsbmsGlobalApi

```typescript
interface IGsbmsGlobalApi {
  /** 组织树接口 */
  orgListTree: string
  /** 特殊查询组织树接口 */
  orgListTree_: string
  /** 根据组织ID查询键值对 */
  findOrgKVByOrgIds: string
  /** 获取当前用户组织 */
  getOrgByUserId: string
  /** 获取子组织 */
  getCurrentSonOrg: string
  /** 获取用户组织树 */
  getUserOrgTree: string
}
```

### IApiResponse

```typescript
interface IApiResponse<T = any> {
  /** 响应状态 */
  success: boolean
  /** 响应消息 */
  message: string
  /** 响应数据 */
  list: T[]
  /** 总数 */
  total?: number
}
```

## 事件处理配置

### IEventHandlers

```typescript
interface IEventHandlers {
  /** 节点点击处理 */
  nodeClick: (data: IOrgItem) => void
  /** 过滤节点方法 */
  filterNodeMethod: (value: string, data: IOrgItem) => boolean
  /** 初始化处理 */
  init: () => Promise<void>
  /** 获取过滤组织 */
  getFilterOrg: () => Promise<void>
  /** 设置组织显示名称 */
  setOrgShowName: (list: IOrgItem[]) => void
}
```

## 监听器配置

### IWatcherConfig

```typescript
interface IWatcherConfig {
  /** 搜索表单模型监听器 */
  searchFormModelWatcher: {
    source: () => Props['searchFormModel']
    handler: () => void
    options: {
      deep: boolean
    }
  }
  /** modelValue 监听器 */
  modelValueWatcher: {
    source: () => Props['modelValue']
    handler: () => void
    options: {
      immediate: boolean
    }
  }
  /** 内部值监听器 */
  valueWatcher: {
    source: Ref<string | string[]>
    handler: () => void
  }
}
```

## 配置示例

### 基础配置

```typescript
const basicConfig: IFuniOrgSelectProps = {
  modelValue: '',
  mode: 'radio'
}
```

### 多选配置

```typescript
const multiSelectConfig: IFuniOrgSelectProps = {
  modelValue: [],
  mode: 'checkbox'
}
```

### 动态加载配置

```typescript
const dynamicConfig: IFuniOrgSelectProps = {
  modelValue: '',
  mode: 'radio',
  attribute: {
    dynamic: true,
    slotUrl: '/api/org/dynamic',
    params: {
      type: 'department',
      status: 'active'
    }
  },
  searchConfig: {
    prop: 'orgName',
    type: 'text',
    placeholder: '请输入组织名称'
  }
}
```

### 权限范围配置

```typescript
const rangeConfig: IFuniOrgSelectProps = {
  modelValue: '',
  mode: 'radio',
  selectRange: [
    {
      id: '001',
      orgName: '总公司',
      orgShowName: '总公司',
      level: 0,
      children: []
    },
    {
      id: '002',
      orgName: '分公司A',
      orgShowName: '分公司A',
      level: 1,
      children: []
    }
  ]
}
```

## 样式配置

### IStyleConfig

```typescript
interface IStyleConfig {
  /** 容器样式 */
  container: {
    width: '240px'
    position: 'relative'
  }
  /** 树选择器样式 */
  treeSelect: {
    width: '90%'
    clearable: boolean
    filterable: boolean
    checkStrictly: boolean
    highlightCurrent: boolean
    expandOnClickNode: boolean
  }
}
```

## 工具函数配置

### IUtilityFunctions

```typescript
interface IUtilityFunctions {
  /** 判断是否需要动态加载 */
  judgment: () => boolean
  /** 设置组织显示名称 */
  setOrgShowName: (list: IOrgItem[]) => void
  /** 过滤节点方法 */
  filterNodeMethod: (value: string, data: IOrgItem) => boolean
  /** 数据转换 */
  transformOrgData: (rawData: any[]) => IOrgItem[]
}
```

## 默认配置

```typescript
const defaultConfig: Required<IFuniOrgSelectProps> = {
  modelValue: '',
  mode: 'radio',
  searchConfig: {
    prop: 'orgName'
  },
  searchFormModel: {},
  attribute: {
    dynamic: false
  },
  operate: {},
  selectRange: []
}

const defaultTreeProps: IOrgTreeProps = {
  value: 'id',
  label: 'orgShowName',
  children: 'children'
}
```

## 扩展配置

### IExtendedConfig

```typescript
interface IExtendedConfig extends IFuniOrgSelectProps {
  /** 是否显示搜索框 */
  showSearch?: boolean
  /** 是否显示清空按钮 */
  showClear?: boolean
  /** 最大选择数量（多选模式） */
  maxSelection?: number
  /** 是否懒加载 */
  lazy?: boolean
  /** 懒加载函数 */
  load?: (node: any, resolve: Function) => void
  /** 自定义节点渲染 */
  renderContent?: (h: Function, data: IOrgItem) => VNode
}
```
