# FuniOrgSelect API 文档

## 组件概述

FuniOrgSelect 是一个组织机构选择器组件，基于 ElementPlus 的 el-tree-select 组件封装。支持单选和多选模式，提供组织架构树形选择功能，支持动态数据加载和权限范围控制。

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | String \| Array | - | 绑定值，单选时为字符串，多选时为数组 |
| mode | String | 'radio' | 选择模式，可选值：'radio'（单选）、'checkbox'（多选） |
| searchConfig | Object | - | 搜索配置对象 |
| searchFormModel | Object | - | 搜索表单模型 |
| attribute | Object | - | 属性配置对象 |
| operate | Object | - | 操作配置对象 |
| selectRange | Array | [] | 可选择的组织范围限制 |

### Props 详细说明

#### modelValue
- 组件的绑定值，支持 v-model 双向绑定
- 单选模式：字符串类型，表示选中组织的 ID
- 多选模式：数组类型，包含多个选中组织的 ID

#### mode
- 选择模式控制
- 'radio': 单选模式，只能选择一个组织
- 'checkbox': 多选模式，可以选择多个组织

#### searchConfig
- 搜索相关配置
- 包含搜索字段属性配置
- 用于动态搜索和过滤

#### searchFormModel
- 搜索表单的数据模型
- 当此值变化时会触发组织数据的重新加载
- 支持深度监听

#### attribute
- 组件属性配置对象
- `dynamic`: 是否启用动态加载
- `slotUrl`: 动态加载的接口地址
- `params`: 接口请求参数

#### selectRange
- 限制可选择的组织范围
- 数组格式，包含允许选择的组织对象
- 当设置此属性时，只有在范围内的组织可以被选择

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: string \| array) | 选择值改变时触发，用于 v-model |

### Events 详细说明

#### update:modelValue
- 用于 v-model 双向绑定
- 当用户选择或取消选择组织时触发
- 参数类型根据 mode 属性决定

## 内部配置

### 树形选择器配置

```javascript
const prop = {
  value: 'id',           // 节点值字段
  label: 'orgShowName',  // 节点显示字段
  children: 'children'   // 子节点字段
}
```

### 组织数据结构

```javascript
{
  id: string,              // 组织ID
  orgName: string,         // 组织名称
  orgShortName: string,    // 组织简称
  orgShowName: string,     // 显示名称（自动生成）
  level: number,           // 组织层级
  children: Array,         // 子组织
  disabled: boolean        // 是否禁用（基于selectRange控制）
}
```

## API 接口

### 主要接口

- `ccsOrgListTree()`: 获取组织架构树数据
- `findOrgKVByOrgIds()`: 根据组织ID获取组织键值对
- 动态接口：通过 `attribute.slotUrl` 配置的自定义接口

### 接口数据处理

1. **组织显示名称处理**：
   - 优先使用 `orgName`
   - 其次使用 `orgShortName`
   - 自动生成 `orgShowName` 字段

2. **权限范围控制**：
   - 根据 `selectRange` 设置组织的 `disabled` 状态
   - 不在范围内的组织将被禁用

3. **默认展开**：
   - 自动展开顶级组织（level === 0）

## 使用示例

### 基础单选用法

```vue
<template>
  <div>
    <FuniOrgSelect 
      v-model="selectedOrg"
      mode="radio"
      @update:modelValue="handleOrgChange"
    />
    <p>选中的组织：{{ selectedOrg }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniOrgSelect from '@/components/FuniOrgSelect/index.vue'

const selectedOrg = ref('')

const handleOrgChange = (value) => {
  console.log('选中的组织：', value)
}
</script>
```

### 多选模式

```vue
<template>
  <div>
    <FuniOrgSelect 
      v-model="selectedOrgs"
      mode="checkbox"
    />
    <p>选中的组织：{{ selectedOrgs }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniOrgSelect from '@/components/FuniOrgSelect/index.vue'

const selectedOrgs = ref([])
</script>
```

### 限制选择范围

```vue
<template>
  <div>
    <FuniOrgSelect 
      v-model="selectedOrg"
      :selectRange="allowedOrgs"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniOrgSelect from '@/components/FuniOrgSelect/index.vue'

const selectedOrg = ref('')
const allowedOrgs = ref([
  { id: '001', name: '总公司' },
  { id: '002', name: '分公司A' },
  { id: '003', name: '分公司B' }
])
</script>
```

### 动态加载配置

```vue
<template>
  <div>
    <FuniOrgSelect 
      v-model="selectedOrg"
      :attribute="dynamicConfig"
      :searchConfig="searchConfig"
      :searchFormModel="searchForm"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniOrgSelect from '@/components/FuniOrgSelect/index.vue'

const selectedOrg = ref('')
const searchForm = ref({})

const dynamicConfig = {
  dynamic: true,
  slotUrl: '/api/org/dynamic',
  params: {
    type: 'department'
  }
}

const searchConfig = {
  prop: 'orgName'
}
</script>
```

### 表单中使用

```vue
<template>
  <el-form :model="form" label-width="120px">
    <el-form-item label="所属组织：">
      <FuniOrgSelect 
        v-model="form.orgId"
        mode="radio"
      />
    </el-form-item>
    <el-form-item label="关联组织：">
      <FuniOrgSelect 
        v-model="form.relatedOrgs"
        mode="checkbox"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
import FuniOrgSelect from '@/components/FuniOrgSelect/index.vue'

const form = ref({
  orgId: '',
  relatedOrgs: []
})

const submitForm = () => {
  console.log('表单数据：', form.value)
}
</script>
```

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .checkBox | 组件根容器 |

### 样式特性

- 固定宽度：240px
- 树形选择器宽度：90%
- 支持清空和过滤功能
- 高亮当前选中项

## 特性功能

### 1. 树形结构展示
- 支持多层级组织架构
- 自动展开顶级组织
- 支持节点展开/收起

### 2. 搜索过滤
- 支持组织名称搜索
- 实时过滤匹配结果
- 支持自定义过滤方法

### 3. 权限控制
- 基于 selectRange 限制可选范围
- 不在范围内的组织自动禁用
- 支持动态权限更新

### 4. 数据加载
- 支持静态数据加载
- 支持动态接口数据加载
- 支持搜索条件变化时重新加载

### 5. 选择模式
- 单选模式：radio
- 多选模式：checkbox
- 严格选择：check-strictly

## 注意事项

1. **数据格式**：组织数据必须包含 id、orgName 等必要字段
2. **权限范围**：selectRange 为空时不限制选择范围
3. **动态加载**：需要正确配置 attribute.slotUrl 和相关参数
4. **搜索配置**：searchConfig.prop 应对应实际的搜索字段
5. **性能考虑**：大量组织数据时建议使用懒加载或分页
6. **兼容性**：依赖 ElementPlus 的 el-tree-select 组件
