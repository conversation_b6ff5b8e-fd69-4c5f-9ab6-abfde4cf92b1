# FuniImageView API 文档

## 组件概述

FuniImageView 是一个图片查看器组件，基于 ElementPlus 的 el-image-viewer 组件封装。提供了图片预览、缩放、旋转等功能，支持多图片浏览和键盘操作。

## Props

组件通过 `v-bind="$attrs"` 透传所有 ElementPlus ImageViewer 的属性。

### 主要属性（继承自 ElementPlus ImageViewer）

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| urlList | Array | [] | 图片地址列表 |
| zIndex | Number | 2000 | 图片查看器的层级 |
| initialIndex | Number | 0 | 初始显示的图片索引 |
| infinite | Boolean | true | 是否可以无限循环查看 |
| hideOnClickModal | Boolean | false | 是否可以通过点击遮罩层关闭 |
| teleported | Boolean | false | 是否将图片查看器插入到 body 下 |
| closeOnPressEscape | Boolean | true | 是否可以通过按下 ESC 关闭 |
| zoomRate | Number | 1.2 | 缩放比率 |

### Props 详细说明

#### urlList
- 图片地址数组，支持多张图片
- 必填属性，至少包含一个图片地址
- 支持相对路径和绝对路径

#### zIndex
- 控制图片查看器的层级
- 确保图片查看器显示在其他元素之上

#### initialIndex
- 设置初始显示的图片索引
- 从 0 开始计数
- 超出范围时会自动调整

#### infinite
- 控制是否可以无限循环查看
- true: 最后一张图片的下一张是第一张
- false: 到达边界时停止

#### hideOnClickModal
- 控制是否可以通过点击遮罩层关闭
- true: 点击图片外的区域关闭查看器
- false: 只能通过关闭按钮或 ESC 键关闭

#### teleported
- 控制是否将查看器插入到 body 下
- true: 插入到 body，避免层级问题
- false: 插入到当前组件位置

#### closeOnPressEscape
- 控制是否可以通过 ESC 键关闭
- 提供便捷的关闭方式

#### zoomRate
- 设置缩放比率
- 每次缩放的倍数
- 影响放大和缩小的步长

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| close | - | 图片查看器关闭时触发 |

### Events 详细说明

#### close
- 当图片查看器关闭时触发
- 可以通过多种方式触发：点击关闭按钮、按 ESC 键、点击遮罩层（如果启用）
- 用于执行清理操作或状态重置

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| showViewer | - | void | 显示图片查看器 |

### Methods 使用示例

```javascript
// 获取组件引用
const imageViewRef = ref()

// 显示图片查看器
imageViewRef.value.showViewer()
```

## Expose

组件通过 `defineExpose` 暴露以下属性和方法：

| 名称 | 类型 | 说明 |
|------|------|------|
| ref | Ref | ImageViewer 组件的引用 |
| showViewer | Function | 显示图片查看器的方法 |

## 使用示例

### 基础用法

```vue
<template>
  <div>
    <el-button @click="showImages">查看图片</el-button>
    <FuniImageView 
      ref="imageViewRef"
      :url-list="imageList"
      @close="handleClose"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniImageView from '@/components/FuniImageView/index.vue'

const imageViewRef = ref()
const imageList = ref([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg',
  'https://example.com/image3.jpg'
])

const showImages = () => {
  imageViewRef.value.showViewer()
}

const handleClose = () => {
  console.log('图片查看器已关闭')
}
</script>
```

### 单张图片预览

```vue
<template>
  <div>
    <img 
      :src="thumbnailUrl" 
      @click="previewImage"
      style="width: 100px; height: 100px; cursor: pointer;"
    />
    <FuniImageView 
      ref="imageViewRef"
      :url-list="[fullImageUrl]"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniImageView from '@/components/FuniImageView/index.vue'

const imageViewRef = ref()
const thumbnailUrl = ref('https://example.com/thumbnail.jpg')
const fullImageUrl = ref('https://example.com/full-image.jpg')

const previewImage = () => {
  imageViewRef.value.showViewer()
}
</script>
```

### 图片列表预览

```vue
<template>
  <div>
    <div class="image-grid">
      <div 
        v-for="(image, index) in imageList" 
        :key="index"
        @click="previewImages(index)"
        class="image-item"
      >
        <img :src="image" />
      </div>
    </div>
    
    <FuniImageView 
      ref="imageViewRef"
      :url-list="imageList"
      :initial-index="currentIndex"
      :infinite="true"
      :hide-on-click-modal="true"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniImageView from '@/components/FuniImageView/index.vue'

const imageViewRef = ref()
const currentIndex = ref(0)
const imageList = ref([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg',
  'https://example.com/image3.jpg',
  'https://example.com/image4.jpg'
])

const previewImages = (index) => {
  currentIndex.value = index
  imageViewRef.value.showViewer()
}
</script>

<style scoped>
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
}

.image-item {
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
}

.image-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}
</style>
```

### 高级配置

```vue
<template>
  <div>
    <el-button @click="showCustomViewer">自定义查看器</el-button>
    <FuniImageView 
      ref="imageViewRef"
      :url-list="imageList"
      :z-index="3000"
      :zoom-rate="1.5"
      :teleported="true"
      :close-on-press-escape="true"
      :hide-on-click-modal="false"
      @close="handleViewerClose"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniImageView from '@/components/FuniImageView/index.vue'

const imageViewRef = ref()
const imageList = ref([
  'https://example.com/high-res-1.jpg',
  'https://example.com/high-res-2.jpg'
])

const showCustomViewer = () => {
  imageViewRef.value.showViewer()
}

const handleViewerClose = () => {
  console.log('自定义查看器已关闭')
  // 执行清理操作
}
</script>
```

### 动态图片列表

```vue
<template>
  <div>
    <el-upload
      :auto-upload="false"
      :on-change="handleFileChange"
      multiple
      accept="image/*"
    >
      <el-button>选择图片</el-button>
    </el-upload>
    
    <el-button 
      @click="previewSelected" 
      :disabled="!imageUrls.length"
    >
      预览图片
    </el-button>
    
    <FuniImageView 
      ref="imageViewRef"
      :url-list="imageUrls"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniImageView from '@/components/FuniImageView/index.vue'

const imageViewRef = ref()
const imageUrls = ref([])

const handleFileChange = (file) => {
  const url = URL.createObjectURL(file.raw)
  imageUrls.value.push(url)
}

const previewSelected = () => {
  if (imageUrls.value.length > 0) {
    imageViewRef.value.showViewer()
  }
}
</script>
```

## 特性功能

### 1. 图片操作
- 缩放：支持鼠标滚轮和按钮缩放
- 旋转：支持顺时针和逆时针旋转
- 拖拽：支持拖拽移动图片位置
- 重置：一键重置图片到初始状态

### 2. 导航功能
- 上一张/下一张：支持按钮和键盘导航
- 无限循环：可配置是否循环浏览
- 索引指示：显示当前图片位置

### 3. 键盘快捷键
- ESC：关闭查看器
- 左右箭头：切换图片
- 空格：重置图片
- Ctrl + 滚轮：缩放

### 4. 响应式设计
- 自适应不同屏幕尺寸
- 移动端友好的触摸操作
- 高分辨率屏幕支持

## 样式定制

### 默认样式
- 全屏显示
- 黑色半透明背景
- 居中显示图片
- 底部工具栏

### 自定义样式

```vue
<style>
/* 自定义查看器背景 */
:deep(.el-image-viewer__mask) {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 自定义工具栏样式 */
:deep(.el-image-viewer__actions) {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

/* 自定义按钮样式 */
:deep(.el-image-viewer__actions__inner) {
  color: #fff;
}
</style>
```

## 注意事项

1. **图片地址**：确保 urlList 中的图片地址有效且可访问
2. **性能考虑**：大量高分辨率图片可能影响加载性能
3. **跨域问题**：注意图片资源的跨域访问权限
4. **移动端适配**：在移动设备上测试触摸操作
5. **内存管理**：及时清理不需要的图片资源
6. **层级管理**：合理设置 zIndex 避免被其他元素遮挡
7. **键盘事件**：确保键盘快捷键不与页面其他功能冲突
