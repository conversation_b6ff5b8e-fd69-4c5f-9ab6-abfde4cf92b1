# FuniImageView 配置结构定义

## 组件配置接口

### IFuniImageViewProps

```typescript
interface IFuniImageViewProps {
  /** 图片地址列表 */
  urlList?: string[]
  /** 图片查看器的层级 */
  zIndex?: number
  /** 初始显示的图片索引 */
  initialIndex?: number
  /** 是否可以无限循环查看 */
  infinite?: boolean
  /** 是否可以通过点击遮罩层关闭 */
  hideOnClickModal?: boolean
  /** 是否将图片查看器插入到 body 下 */
  teleported?: boolean
  /** 是否可以通过按下 ESC 关闭 */
  closeOnPressEscape?: boolean
  /** 缩放比率 */
  zoomRate?: number
}
```

### IFuniImageViewEmits

```typescript
interface IFuniImageViewEmits {
  /** 图片查看器关闭时触发 */
  close: () => void
}
```

### IFuniImageViewMethods

```typescript
interface IFuniImageViewMethods {
  /** 显示图片查看器 */
  showViewer: () => void
}
```

### IFuniImageViewExpose

```typescript
interface IFuniImageViewExpose {
  /** ImageViewer 组件的引用 */
  ref: Ref<any>
  /** 显示图片查看器的方法 */
  showViewer: () => void
}
```

## 内部状态结构

### IComponentState

```typescript
interface IComponentState {
  /** 是否显示图片查看器 */
  show: Ref<boolean>
  /** ImageViewer 组件引用 */
  imageView: Ref<any>
}
```

## ElementPlus ImageViewer 配置

### IImageViewerProps

```typescript
interface IImageViewerProps {
  /** 图片地址列表 */
  urlList: string[]
  /** 图片查看器的层级 */
  zIndex?: number
  /** 初始显示的图片索引 */
  initialIndex?: number
  /** 是否可以无限循环查看 */
  infinite?: boolean
  /** 是否可以通过点击遮罩层关闭 */
  hideOnClickModal?: boolean
  /** 是否将图片查看器插入到 body 下 */
  teleported?: boolean
  /** 是否可以通过按下 ESC 关闭 */
  closeOnPressEscape?: boolean
  /** 缩放比率 */
  zoomRate?: number
  /** 最小缩放比例 */
  minScale?: number
  /** 最大缩放比例 */
  maxScale?: number
}
```

## 图片数据结构

### IImageItem

```typescript
interface IImageItem {
  /** 图片地址 */
  url: string
  /** 图片标题 */
  title?: string
  /** 图片描述 */
  description?: string
  /** 图片尺寸 */
  size?: {
    width: number
    height: number
  }
  /** 图片格式 */
  format?: 'jpg' | 'png' | 'gif' | 'webp' | 'svg'
  /** 文件大小（字节） */
  fileSize?: number
}
```

### IImageList

```typescript
interface IImageList {
  /** 图片列表 */
  images: IImageItem[]
  /** 当前索引 */
  currentIndex: number
  /** 总数 */
  total: number
}
```

## 事件处理配置

### IEventHandlers

```typescript
interface IEventHandlers {
  /** 显示查看器处理 */
  showViewer: () => void
  /** 关闭查看器处理 */
  close: () => void
  /** 图片切换处理 */
  onImageChange?: (index: number) => void
  /** 缩放处理 */
  onZoom?: (scale: number) => void
  /** 旋转处理 */
  onRotate?: (angle: number) => void
}
```

## 操作配置

### IViewerActions

```typescript
interface IViewerActions {
  /** 缩放操作 */
  zoom: {
    /** 放大 */
    zoomIn: () => void
    /** 缩小 */
    zoomOut: () => void
    /** 重置缩放 */
    resetZoom: () => void
    /** 设置缩放比例 */
    setZoom: (scale: number) => void
  }
  /** 旋转操作 */
  rotate: {
    /** 顺时针旋转 */
    rotateRight: () => void
    /** 逆时针旋转 */
    rotateLeft: () => void
    /** 重置旋转 */
    resetRotate: () => void
  }
  /** 导航操作 */
  navigate: {
    /** 上一张 */
    prev: () => void
    /** 下一张 */
    next: () => void
    /** 跳转到指定图片 */
    goTo: (index: number) => void
  }
  /** 其他操作 */
  misc: {
    /** 重置所有 */
    reset: () => void
    /** 关闭查看器 */
    close: () => void
  }
}
```

## 键盘快捷键配置

### IKeyboardShortcuts

```typescript
interface IKeyboardShortcuts {
  /** ESC 键 - 关闭查看器 */
  escape: 'close'
  /** 左箭头 - 上一张 */
  arrowLeft: 'prev'
  /** 右箭头 - 下一张 */
  arrowRight: 'next'
  /** 空格键 - 重置 */
  space: 'reset'
  /** 加号 - 放大 */
  plus: 'zoomIn'
  /** 减号 - 缩小 */
  minus: 'zoomOut'
  /** R 键 - 顺时针旋转 */
  r: 'rotateRight'
  /** L 键 - 逆时针旋转 */
  l: 'rotateLeft'
}
```

## 样式配置

### IStyleConfig

```typescript
interface IStyleConfig {
  /** 遮罩层样式 */
  mask: {
    backgroundColor: string
    zIndex: number
  }
  /** 图片容器样式 */
  container: {
    display: 'flex'
    justifyContent: 'center'
    alignItems: 'center'
    width: '100%'
    height: '100%'
  }
  /** 工具栏样式 */
  toolbar: {
    position: 'fixed'
    bottom: string
    left: '50%'
    transform: 'translateX(-50%)'
    backgroundColor: string
    borderRadius: string
    padding: string
  }
  /** 按钮样式 */
  button: {
    color: string
    fontSize: string
    padding: string
    margin: string
  }
}
```

## 配置示例

### 基础配置

```typescript
const basicConfig: IFuniImageViewProps = {
  urlList: [
    'https://example.com/image1.jpg',
    'https://example.com/image2.jpg'
  ],
  initialIndex: 0,
  infinite: true
}
```

### 高级配置

```typescript
const advancedConfig: IFuniImageViewProps = {
  urlList: [
    'https://example.com/high-res-1.jpg',
    'https://example.com/high-res-2.jpg',
    'https://example.com/high-res-3.jpg'
  ],
  zIndex: 3000,
  initialIndex: 1,
  infinite: false,
  hideOnClickModal: true,
  teleported: true,
  closeOnPressEscape: true,
  zoomRate: 1.5
}
```

### 移动端配置

```typescript
const mobileConfig: IFuniImageViewProps = {
  urlList: ['https://example.com/mobile-image.jpg'],
  hideOnClickModal: true,
  teleported: true,
  zoomRate: 1.1,
  infinite: false
}
```

## 工具函数配置

### IUtilityFunctions

```typescript
interface IUtilityFunctions {
  /** 验证图片地址 */
  validateImageUrl: (url: string) => boolean
  /** 预加载图片 */
  preloadImages: (urls: string[]) => Promise<void>
  /** 获取图片信息 */
  getImageInfo: (url: string) => Promise<IImageItem>
  /** 格式化图片列表 */
  formatImageList: (urls: string[]) => IImageItem[]
  /** 检查图片是否加载完成 */
  isImageLoaded: (url: string) => boolean
}
```

## 性能优化配置

### IPerformanceConfig

```typescript
interface IPerformanceConfig {
  /** 预加载配置 */
  preload: {
    /** 是否启用预加载 */
    enabled: boolean
    /** 预加载数量 */
    count: number
    /** 预加载策略 */
    strategy: 'adjacent' | 'all' | 'lazy'
  }
  /** 缓存配置 */
  cache: {
    /** 是否启用缓存 */
    enabled: boolean
    /** 缓存大小限制 */
    maxSize: number
    /** 缓存时间 */
    ttl: number
  }
  /** 懒加载配置 */
  lazyLoad: {
    /** 是否启用懒加载 */
    enabled: boolean
    /** 加载阈值 */
    threshold: number
  }
}
```

## 错误处理配置

### IErrorHandling

```typescript
interface IErrorHandling {
  /** 图片加载失败处理 */
  onImageError: (url: string, error: Error) => void
  /** 显示错误占位图 */
  showErrorPlaceholder: boolean
  /** 错误占位图地址 */
  errorPlaceholder: string
  /** 重试配置 */
  retry: {
    /** 是否启用重试 */
    enabled: boolean
    /** 重试次数 */
    maxAttempts: number
    /** 重试间隔 */
    delay: number
  }
}
```

## 默认配置

```typescript
const defaultConfig: Required<IFuniImageViewProps> = {
  urlList: [],
  zIndex: 2000,
  initialIndex: 0,
  infinite: true,
  hideOnClickModal: false,
  teleported: false,
  closeOnPressEscape: true,
  zoomRate: 1.2
}

const defaultState: IComponentState = {
  show: ref(false),
  imageView: ref(null)
}

const defaultStyleConfig: IStyleConfig = {
  mask: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 2000
  },
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%'
  },
  toolbar: {
    position: 'fixed',
    bottom: '40px',
    left: '50%',
    transform: 'translateX(-50%)',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: '22px',
    padding: '0 22px'
  },
  button: {
    color: '#fff',
    fontSize: '24px',
    padding: '8px',
    margin: '0 4px'
  }
}
```
