# FuniInputNumber API 文档

## 组件概述

FuniInputNumber 是一个数字输入框组件，基于 ElementPlus 的 el-input 组件封装。提供了数字输入的验证、精度控制、范围限制等功能，并隐藏了浏览器默认的数字输入框箭头。

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Number | - | 绑定值 |
| max | Number | Number.POSITIVE_INFINITY | 允许的最大值 |
| min | Number | Number.NEGATIVE_INFINITY | 允许的最小值 |
| precision | Number | 0 | 数值精度，必须是非负整数 |
| placeholder | String | '请输入' | 输入框占位文本 |

### Props 详细说明

#### modelValue
- 组件的绑定值，必须是数字类型
- 支持 v-model 双向绑定
- 当传入非数字值时，组件会自动转换或设置为 null

#### max / min
- 设置数值的允许范围
- 当输入值超出范围时，会自动调整到边界值
- 默认不限制范围

#### precision
- 控制数值的小数位数
- 必须是非负整数
- 为 0 时表示整数，大于 0 时表示保留对应小数位数

#### placeholder
- 输入框的占位提示文本
- 当输入框为空时显示

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: number \| null) | 值改变时触发，用于 v-model |
| change | (newValue: number \| null, oldValue: number \| null) | 值改变且失去焦点时触发 |
| input | (value: number \| null) | 输入时触发 |

### Events 详细说明

#### update:modelValue
- 用于 v-model 双向绑定
- 在值发生变化时立即触发
- 参数为当前的数值或 null

#### change
- 在值改变且输入框失去焦点时触发
- 提供新值和旧值两个参数
- 适用于需要在用户完成输入后执行操作的场景

#### input
- 在用户输入过程中实时触发
- 参数为当前输入的数值
- 适用于需要实时响应用户输入的场景

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| focus | - | void | 使输入框获得焦点 |
| blur | - | void | 使输入框失去焦点 |

### Methods 使用示例

```javascript
// 获取组件引用
const inputNumberRef = ref()

// 使输入框获得焦点
inputNumberRef.value.focus()

// 使输入框失去焦点
inputNumberRef.value.blur()
```

## Slots

组件支持 ElementPlus el-input 的所有插槽，通过 v-bind 透传：

| 插槽名 | 说明 |
|--------|------|
| prefix | 输入框头部内容 |
| suffix | 输入框尾部内容 |
| prepend | 输入框前置内容 |
| append | 输入框后置内容 |

## 特性

### 1. 数值验证
- 自动验证输入值是否为有效数字
- 非数字输入会被转换为 null
- 空字符串会被转换为 null

### 2. 范围控制
- 支持设置最大值和最小值
- 超出范围的值会自动调整到边界值
- 实时验证输入范围

### 3. 精度控制
- 支持设置小数位数精度
- 自动四舍五入到指定精度
- 精度为 0 时只允许整数

### 4. 用户体验优化
- 禁用鼠标滚轮改变数值
- 隐藏浏览器默认的数字输入框箭头
- 支持清空按钮

### 5. 属性透传
- 继承 ElementPlus el-input 的所有属性
- 通过 v-bind="$attrs" 透传未定义的属性
- 支持所有 el-input 的样式和行为

## 使用示例

### 基础用法

```vue
<template>
  <div>
    <FuniInputNumber 
      v-model="value"
      placeholder="请输入数字"
    />
    <p>当前值：{{ value }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniInputNumber from '@/components/FuniInputNumber/index.vue'

const value = ref(0)
</script>
```

### 设置范围和精度

```vue
<template>
  <div>
    <FuniInputNumber 
      v-model="price"
      :min="0"
      :max="999999"
      :precision="2"
      placeholder="请输入价格"
    />
    <p>价格：{{ price }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniInputNumber from '@/components/FuniInputNumber/index.vue'

const price = ref(0)
</script>
```

### 监听事件

```vue
<template>
  <div>
    <FuniInputNumber 
      v-model="amount"
      @input="handleInput"
      @change="handleChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniInputNumber from '@/components/FuniInputNumber/index.vue'

const amount = ref(0)

const handleInput = (value) => {
  console.log('输入中：', value)
}

const handleChange = (newValue, oldValue) => {
  console.log('值改变：', newValue, '原值：', oldValue)
}
</script>
```

### 使用插槽

```vue
<template>
  <div>
    <FuniInputNumber v-model="value">
      <template #prefix>
        <el-icon><Money /></el-icon>
      </template>
      <template #suffix>
        <span>元</span>
      </template>
    </FuniInputNumber>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Money } from '@element-plus/icons-vue'
import FuniInputNumber from '@/components/FuniInputNumber/index.vue'

const value = ref(0)
</script>
```

### 方法调用

```vue
<template>
  <div>
    <FuniInputNumber 
      ref="inputRef"
      v-model="value"
    />
    <el-button @click="focusInput">聚焦</el-button>
    <el-button @click="blurInput">失焦</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniInputNumber from '@/components/FuniInputNumber/index.vue'

const inputRef = ref()
const value = ref(0)

const focusInput = () => {
  inputRef.value.focus()
}

const blurInput = () => {
  inputRef.value.blur()
}
</script>
```

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .funi-input-number | 组件根容器 |

### 样式特性

- 隐藏了浏览器默认的数字输入框箭头
- 支持 ElementPlus 主题定制
- 可通过 CSS 变量自定义样式

## 注意事项

1. **数据类型**：modelValue 必须是 Number 类型，传入其他类型会被转换
2. **精度验证**：precision 必须是非负整数，否则会抛出验证错误
3. **范围验证**：max 不能小于 min，否则会抛出错误
4. **空值处理**：空字符串和无效输入会被转换为 null
5. **滚轮禁用**：组件禁用了鼠标滚轮改变数值的行为
6. **浏览器兼容**：隐藏数字输入框箭头的样式可能在某些浏览器中表现不同
