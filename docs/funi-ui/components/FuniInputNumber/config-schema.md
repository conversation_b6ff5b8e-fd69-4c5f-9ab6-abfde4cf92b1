# FuniInputNumber 配置结构定义

## 组件配置接口

### IFuniInputNumberProps

```typescript
interface IFuniInputNumberProps {
  /** 绑定值 */
  modelValue?: number
  /** 允许的最大值 */
  max?: number
  /** 允许的最小值 */
  min?: number
  /** 数值精度，必须是非负整数 */
  precision?: number
  /** 输入框占位文本 */
  placeholder?: string
}
```

### IFuniInputNumberEmits

```typescript
interface IFuniInputNumberEmits {
  /** v-model 更新事件 */
  'update:modelValue': (value: number | null) => void
  /** 值改变事件 */
  change: (newValue: number | null, oldValue: number | null) => void
  /** 输入事件 */
  input: (value: number | null) => void
}
```

### IFuniInputNumberMethods

```typescript
interface IFuniInputNumberMethods {
  /** 使输入框获得焦点 */
  focus: () => void
  /** 使输入框失去焦点 */
  blur: () => void
}
```

## 内部状态结构

### IComponentState

```typescript
interface IComponentState {
  /** 当前值 */
  currentValue: number | null
  /** 用户输入值 */
  userInput: string | null
}
```

### IComputedProperties

```typescript
interface IComputedProperties {
  /** 显示值 */
  displayValue: string | number
}
```

## 验证器配置

### IPrecisionValidator

```typescript
interface IPrecisionValidator {
  /** 验证函数 */
  validator: (value: number) => boolean
  /** 验证规则：必须是非负整数 */
  rule: 'value >= 0 && value === parseInt(value, 10)'
}
```

### IValueValidator

```typescript
interface IValueValidator {
  /** 验证数值是否有效 */
  isValidNumber: (value: any) => boolean
  /** 验证数值是否在范围内 */
  isInRange: (value: number, min: number, max: number) => boolean
  /** 验证精度是否正确 */
  isValidPrecision: (precision: number) => boolean
}
```

## 数值处理配置

### IPrecisionHandler

```typescript
interface IPrecisionHandler {
  /** 精度处理函数 */
  toPrecision: (num: number, precision: number) => number
  /** 四舍五入规则 */
  roundingRule: 'standard' | 'banker'
  /** 特殊值处理 */
  specialCases: {
    /** 处理末尾为5的情况 */
    handleTrailingFive: boolean
  }
}
```

### IValueProcessor

```typescript
interface IValueProcessor {
  /** 验证并处理数值 */
  verifyValue: (value: any, shouldUpdate?: boolean) => number | null
  /** 设置当前值 */
  setCurrentValue: (value: any, emitChange?: boolean) => void
  /** 处理输入 */
  handleInput: (value: string) => void
  /** 处理输入变化 */
  handleInputChange: (value: string) => void
}
```

## 默认配置

### IDefaultConfig

```typescript
interface IDefaultConfig {
  /** 默认最大值 */
  defaultMax: number // Number.POSITIVE_INFINITY
  /** 默认最小值 */
  defaultMin: number // Number.NEGATIVE_INFINITY
  /** 默认精度 */
  defaultPrecision: number // 0
  /** 默认占位符 */
  defaultPlaceholder: string // '请输入'
  /** 默认清空功能 */
  defaultClearable: boolean // true
  /** 默认验证事件 */
  defaultValidateEvent: boolean // false
}
```

## 样式配置

### IStyleConfig

```typescript
interface IStyleConfig {
  /** 主要样式类 */
  mainClass: 'funi-input-number'
  /** 隐藏数字箭头 */
  hideSpinButtons: {
    webkit: '-webkit-appearance: none'
    moz: '-moz-appearance: textfield'
  }
  /** 输入框样式 */
  inputStyles: {
    lineHeight: '1'
    appearance: 'none'
  }
}
```

## 事件处理配置

### IEventHandlers

```typescript
interface IEventHandlers {
  /** 输入处理器 */
  inputHandler: (value: string) => void
  /** 变化处理器 */
  changeHandler: (value: string) => void
  /** 滚轮事件处理器 */
  wheelHandler: (event: WheelEvent) => void
  /** 焦点处理器 */
  focusHandler: () => void
  /** 失焦处理器 */
  blurHandler: () => void
}
```

## 配置示例

### 基础配置

```typescript
const basicConfig: IFuniInputNumberProps = {
  modelValue: 0,
  placeholder: '请输入数字'
}
```

### 范围限制配置

```typescript
const rangeConfig: IFuniInputNumberProps = {
  modelValue: 50,
  min: 0,
  max: 100,
  placeholder: '请输入0-100之间的数字'
}
```

### 精度控制配置

```typescript
const precisionConfig: IFuniInputNumberProps = {
  modelValue: 0,
  precision: 2,
  placeholder: '请输入金额'
}
```

### 完整配置示例

```typescript
const fullConfig: IFuniInputNumberProps = {
  modelValue: 0,
  min: 0,
  max: 999999.99,
  precision: 2,
  placeholder: '请输入价格（0-999999.99）'
}
```

## 验证规则配置

### IValidationRules

```typescript
interface IValidationRules {
  /** 数值验证规则 */
  numberValidation: {
    /** 是否允许空值 */
    allowNull: boolean
    /** 是否允许NaN */
    allowNaN: boolean
    /** 是否允许无穷大 */
    allowInfinity: boolean
  }
  /** 范围验证规则 */
  rangeValidation: {
    /** 是否自动调整超出范围的值 */
    autoAdjust: boolean
    /** 是否在超出范围时触发更新 */
    emitOnRangeAdjust: boolean
  }
  /** 精度验证规则 */
  precisionValidation: {
    /** 是否自动四舍五入 */
    autoRound: boolean
    /** 四舍五入方法 */
    roundingMethod: 'round' | 'floor' | 'ceil' | 'trunc'
  }
}
```

## 工具函数配置

### IUtilityFunctions

```typescript
interface IUtilityFunctions {
  /** 检查是否为空值 */
  isNil: (value: any) => boolean
  /** 检查是否为数字 */
  isNumber: (value: any) => boolean
  /** 检查是否为未定义 */
  isUndefined: (value: any) => boolean
  /** 深度克隆 */
  clone: (obj: any, deep?: boolean) => any
}
```

## 生命周期配置

### ILifecycleConfig

```typescript
interface ILifecycleConfig {
  /** 挂载时的处理 */
  onMounted: {
    /** 是否验证初始值 */
    validateInitialValue: boolean
    /** 是否转换非数字初始值 */
    convertInitialValue: boolean
  }
  /** 监听器配置 */
  watchers: {
    /** modelValue 监听器 */
    modelValueWatcher: {
      immediate: boolean
      deep: boolean
    }
  }
}
```

## 错误处理配置

### IErrorHandling

```typescript
interface IErrorHandling {
  /** 错误类型 */
  errorTypes: {
    INVALID_RANGE: 'min should not be greater than max'
    INVALID_PRECISION: 'precision must be a non-negative integer'
    INVALID_VALUE: 'value must be a number'
  }
  /** 错误处理策略 */
  errorStrategy: {
    /** 是否抛出错误 */
    throwError: boolean
    /** 是否控制台警告 */
    consoleWarn: boolean
    /** 是否静默处理 */
    silentHandle: boolean
  }
}
```

## 性能优化配置

### IPerformanceConfig

```typescript
interface IPerformanceConfig {
  /** 防抖配置 */
  debounce: {
    /** 是否启用防抖 */
    enabled: boolean
    /** 防抖延迟 */
    delay: number
  }
  /** 节流配置 */
  throttle: {
    /** 是否启用节流 */
    enabled: boolean
    /** 节流间隔 */
    interval: number
  }
  /** 缓存配置 */
  cache: {
    /** 是否缓存计算结果 */
    enabled: boolean
    /** 缓存大小 */
    maxSize: number
  }
}
```
