# FuniFilePreview API 文档

## 组件概述

FuniFilePreview 是一个文件预览组件，专门用于在线预览 Office 文档和 PDF 文件。组件基于 vue-office 库实现，支持 Word 文档（.docx）、Excel 表格（.xlsx）和 PDF 文件的在线预览功能。

## Props

该组件不接受直接的 props 参数，而是通过路由查询参数获取文件信息。

### 路由查询参数

| 参数 | 类型 | 说明 |
|------|------|------|
| src | String | 文件预览地址（需要 URL 编码） |
| type | String | 文件类型，支持：'docx', 'xlsx', 'pdf', 'PDF' |

### 参数详细说明

#### src
- 文件的预览地址
- 必须是经过 `encodeURIComponent` 编码的 URL
- 支持相对路径和绝对路径
- 文件必须可通过 HTTP/HTTPS 访问

#### type
- 文件类型标识
- 支持的类型：
  - `docx`: Word 文档
  - `xlsx`: Excel 表格
  - `pdf` 或 `PDF`: PDF 文档
- 不支持的文件类型会直接跳转到文件下载

## 功能特性

### 1. 多格式支持
- **Word 文档**: 使用 vue-office-docx 组件预览
- **Excel 表格**: 使用 vue-office-excel 组件预览
- **PDF 文件**: 使用 iframe 嵌入预览

### 2. 自动处理
- 自动从路由参数获取文件信息
- 自动解码 URL 编码的文件地址
- 不支持的文件类型自动跳转下载

### 3. 响应式设计
- 预览区域占满容器
- 自适应不同屏幕尺寸

## 使用示例

### 路由配置

```javascript
// router/index.js
{
  path: '/file-preview',
  name: 'FilePreview',
  component: () => import('@/components/FuniFilePreview/index.vue')
}
```

### 基础用法

```vue
<template>
  <div>
    <el-button @click="previewWord">预览 Word 文档</el-button>
    <el-button @click="previewExcel">预览 Excel 表格</el-button>
    <el-button @click="previewPdf">预览 PDF 文件</el-button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const previewWord = () => {
  const fileUrl = 'https://example.com/document.docx'
  const encodedUrl = encodeURIComponent(fileUrl)
  router.push({
    path: '/file-preview',
    query: {
      src: encodedUrl,
      type: 'docx'
    }
  })
}

const previewExcel = () => {
  const fileUrl = 'https://example.com/spreadsheet.xlsx'
  const encodedUrl = encodeURIComponent(fileUrl)
  router.push({
    path: '/file-preview',
    query: {
      src: encodedUrl,
      type: 'xlsx'
    }
  })
}

const previewPdf = () => {
  const fileUrl = 'https://example.com/document.pdf'
  const encodedUrl = encodeURIComponent(fileUrl)
  router.push({
    path: '/file-preview',
    query: {
      src: encodedUrl,
      type: 'pdf'
    }
  })
}
</script>
```

### 文件列表预览

```vue
<template>
  <div>
    <el-table :data="fileList">
      <el-table-column prop="name" label="文件名" />
      <el-table-column prop="type" label="类型" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button @click="previewFile(row)">预览</el-button>
          <el-button @click="downloadFile(row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const fileList = ref([
  {
    id: 1,
    name: '项目方案.docx',
    type: 'docx',
    url: 'https://example.com/files/proposal.docx'
  },
  {
    id: 2,
    name: '数据报表.xlsx',
    type: 'xlsx',
    url: 'https://example.com/files/report.xlsx'
  },
  {
    id: 3,
    name: '用户手册.pdf',
    type: 'pdf',
    url: 'https://example.com/files/manual.pdf'
  }
])

const previewFile = (file) => {
  if (['docx', 'xlsx', 'pdf'].includes(file.type.toLowerCase())) {
    const encodedUrl = encodeURIComponent(file.url)
    router.push({
      path: '/file-preview',
      query: {
        src: encodedUrl,
        type: file.type
      }
    })
  } else {
    // 不支持的文件类型直接下载
    downloadFile(file)
  }
}

const downloadFile = (file) => {
  window.open(file.url, '_blank')
}
</script>
```

### 新窗口预览

```vue
<template>
  <div>
    <el-button @click="previewInNewWindow">新窗口预览</el-button>
  </div>
</template>

<script setup>
const previewInNewWindow = () => {
  const fileUrl = 'https://example.com/document.docx'
  const encodedUrl = encodeURIComponent(fileUrl)
  const previewUrl = `/file-preview?src=${encodedUrl}&type=docx`
  
  window.open(previewUrl, '_blank', 'width=1200,height=800')
}
</script>
```

### 嵌入式预览

```vue
<template>
  <div class="preview-container">
    <div class="file-info">
      <h3>{{ currentFile.name }}</h3>
      <p>文件类型：{{ currentFile.type }}</p>
    </div>
    <div class="preview-area">
      <iframe 
        :src="previewUrl" 
        frameborder="0"
        style="width: 100%; height: 100%;"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const currentFile = ref({
  name: '示例文档.docx',
  type: 'docx',
  url: 'https://example.com/document.docx'
})

const previewUrl = computed(() => {
  const encodedUrl = encodeURIComponent(currentFile.value.url)
  return `/file-preview?src=${encodedUrl}&type=${currentFile.value.type}`
})
</script>

<style scoped>
.preview-container {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.file-info {
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.preview-area {
  flex: 1;
  min-height: 0;
}
</style>
```

### 动态文件预览

```vue
<template>
  <div>
    <el-upload
      :auto-upload="false"
      :on-change="handleFileChange"
      accept=".docx,.xlsx,.pdf"
    >
      <el-button>选择文件</el-button>
    </el-upload>
    
    <el-button 
      v-if="selectedFile"
      @click="previewSelectedFile"
    >
      预览选中文件
    </el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const selectedFile = ref(null)

const handleFileChange = (file) => {
  selectedFile.value = file
}

const previewSelectedFile = async () => {
  if (!selectedFile.value) return
  
  // 创建临时 URL
  const fileUrl = URL.createObjectURL(selectedFile.value.raw)
  const fileType = getFileType(selectedFile.value.name)
  
  if (['docx', 'xlsx', 'pdf'].includes(fileType)) {
    const encodedUrl = encodeURIComponent(fileUrl)
    router.push({
      path: '/file-preview',
      query: {
        src: encodedUrl,
        type: fileType
      }
    })
  }
}

const getFileType = (fileName) => {
  const extension = fileName.split('.').pop().toLowerCase()
  return extension
}
</script>
```

## 技术依赖

### 1. vue-office
- **@vue-office/docx**: Word 文档预览
- **@vue-office/excel**: Excel 表格预览
- 需要引入对应的 CSS 样式文件

### 2. 浏览器支持
- 现代浏览器支持
- 需要支持 iframe 嵌入（PDF 预览）

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .fileView | 预览容器（未使用） |

### 默认样式
- 预览区域占满容器（100% 宽高）
- PDF 使用 iframe 全屏显示
- Office 文档使用 vue-office 组件样式

### 自定义样式

```vue
<style>
/* 自定义 Word 文档样式 */
:deep(.vue-office-docx) {
  background-color: #f5f5f5;
}

/* 自定义 Excel 表格样式 */
:deep(.vue-office-excel) {
  border: 1px solid #ddd;
}

/* 自定义 PDF iframe 样式 */
iframe {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
```

## 注意事项

1. **文件编码**：src 参数必须使用 `encodeURIComponent` 进行 URL 编码
2. **文件访问**：确保文件 URL 可以通过 HTTP/HTTPS 访问
3. **跨域问题**：注意文件服务器的跨域配置
4. **文件大小**：大文件可能影响预览性能
5. **浏览器兼容**：某些浏览器可能不支持特定格式的预览
6. **安全性**：避免预览不可信的文件来源
7. **内存管理**：使用 `URL.createObjectURL` 时注意及时释放内存

## 错误处理

### 不支持的文件类型
- 组件会自动检测文件类型
- 不支持的类型会直接跳转到文件下载
- 支持的类型：docx, xlsx, pdf, PDF

### 文件加载失败
- vue-office 组件会显示加载错误信息
- PDF iframe 会显示浏览器默认错误页面
- 建议在上层组件中添加错误处理逻辑

## 扩展功能

### 添加更多文件类型支持

```vue
<template>
  <div>
    <vue-office-docx v-if="type === 'docx'" :src="src" />
    <vue-office-excel v-else-if="type === 'xlsx'" :src="src" />
    <vue-office-pdf v-else-if="type === 'pdf'" :src="src" />
    <iframe v-else-if="type === 'html'" :src="src" />
    <div v-else>
      不支持的文件类型：{{ type }}
    </div>
  </div>
</template>
```
