# FuniUI组件使用总览

## 核心业务组件

| 组件名 | 主要用途 | 适用场景 | 配置复杂度 | 强制使用 |
|--------|----------|----------|------------|----------|
| FuniListPageV2 | 列表页面 | 数据列表展示、搜索、操作 | 中等 | ✅ PC列表页 |
| FuniListPage | 列表页面(旧版) | 数据列表展示 | 中等 | - |
| FuniDetail | 详情页面 | 数据详情、编辑、审核、新建 | 高 | ✅ PC详情页 |
| FuniForm | 表单组件 | 数据录入、编辑 | 中等 | - |
| FuniFormV2 | 表单组件V2 | 增强版表单 | 中等 | - |
| FuniCurd | 数据表格 | 数据展示、操作 | 中等 | - |
| FuniCurdV2 | 数据表格V2 | 增强版数据表格 | 中等 | - |
| FuniCurdPro | 专业数据表格 | 高级数据表格 | 高 | - |
| FuniVCurd | 虚拟数据表格 | 大数据量表格 | 高 | - |
| FuniCurdColumn | 表格列组件 | 表格列定义 | 简单 | - |
| FuniSearch | 搜索组件 | 条件搜索 | 简单 | - |
| FuniSearchForm | 搜索表单 | 复杂搜索 | 中等 | - |
| FuniSearchFormV2 | 搜索表单V2 | 增强搜索 | 中等 | - |
| FuniSearchFormV3 | 搜索表单V3 | 最新搜索 | 中等 | - |

## 工作流组件

| 组件名 | 主要用途 | 使用条件 | 配置复杂度 | Mock数据需求 |
|--------|----------|----------|------------|-------------|
| FuniFileTable | 工作流附件管理 | 需要businessId | 低 | ✅ 附件配置 |
| FuniAuditButtomBtn | 工作流审核按钮 | 审核模式+businessId | 低 | ✅ 动态按钮 |
| FuniBusAuditDrawer | 审核意见抽屉 | 配合审核按钮 | 低 | ✅ 审核历史 |
| FuniProcessBottomBtn | 流程底部按钮 | 工作流场景 | 低 | ✅ 流程配置 |
| FuniBpmn | 流程图组件 | 流程设计/展示 | 高 | ✅ 流程数据 |
| FuniFileTable/upload.vue | 普通文件上传 | 非工作流场景 | 中等 | - |

## 表单控件组件

| 组件名 | 主要用途 | ElementPlus基础 | 配置复杂度 | 特殊功能 |
|--------|----------|----------------|------------|----------|
| FuniSelect | 下拉选择器 | el-select | 简单 | 数据源配置 |
| FuniTreeSelect | 树形选择器 | el-tree-select | 中等 | 树形数据 |
| FuniRUOC | 角色用户组织选择 | el-select | 高 | 多类型选择 |
| FuniRUOCLowCode | 低代码RUOC选择 | el-select | 高 | 低代码集成 |
| FuniOrgSelect | 组织选择器 | el-select | 中等 | 组织树选择 |
| FuniAutocomplete | 自动完成 | el-autocomplete | 简单 | 远程搜索 |
| FuniMoneyInput | 金额输入 | el-input | 简单 | 金额格式化 |
| FuniInputNumber | 数字输入 | el-input-number | 简单 | 数字验证 |
| FuniInputNumberRange | 数字范围 | el-input-number | 中等 | 范围选择 |
| FuniIconSelect | 图标选择器 | el-select | 中等 | 图标库选择 |
| FuniRegion | 地区选择器 | el-cascader | 中等 | 地区级联 |
| FuniSearchRegion | 搜索地区选择 | el-select | 中等 | 地区搜索 |

## 展示组件

| 组件名 | 主要用途 | ElementPlus基础 | 配置复杂度 | 特殊功能 |
|--------|----------|----------------|------------|----------|
| FuniChart | 图表组件 | - | 高 | ECharts集成 |
| FuniLineChart | 折线图 | - | 中等 | 折线图表 |
| FuniPieChart | 饼图 | - | 中等 | 饼图表 |
| FuniHistogramChart | 柱状图 | - | 中等 | 柱状图表 |
| FuniRadarChart | 雷达图 | - | 中等 | 雷达图表 |
| FuniScatterplotChart | 散点图 | - | 中等 | 散点图表 |
| FuniGantt | 甘特图 | - | 高 | 项目管理 |
| FuniImage | 图片组件 | el-image | 简单 | 图片处理 |
| FuniImageView | 图片预览 | el-image-viewer | 简单 | 预览功能 |
| FuniEditor | 富文本编辑器 | - | 高 | TinyMCE/Vditor |
| FuniCodemirror | 代码编辑器 | - | 高 | 代码高亮 |
| FuniPreview | 文件预览 | - | 中等 | 多格式预览 |
| FuniFilePreview | 文件预览器 | - | 中等 | 文件预览 |
| FuniIcon | 图标组件 | el-icon | 简单 | 图标库 |
| FuniSvg | SVG图标 | - | 简单 | SVG渲染 |
| FuniHighlightCode | 代码高亮 | - | 简单 | 代码展示 |
| FuniVideo | 视频播放器 | - | 中等 | 视频播放 |
| FuniReportView | 报表视图 | - | 高 | 报表展示 |
| FuniOlMap | 地图组件 | - | 高 | OpenLayers地图 |
| FuniCimMapDialog | CIM地图弹窗 | el-dialog | 高 | CIM地图集成 |

## 交互组件

| 组件名 | 主要用途 | ElementPlus基础 | 配置复杂度 | 特殊功能 |
|--------|----------|----------------|------------|----------|
| FuniDialog | 对话框 | el-dialog | 简单 | 增强功能 |
| FuniAuthButton | 权限按钮 | el-button | 简单 | 权限控制 |
| FuniActions | 操作按钮组 | el-button | 中等 | 批量操作 |
| FuniTeleport | 传送组件 | - | 简单 | DOM传送 |
| FuniHyperLink | 超链接组件 | el-link | 简单 | 链接跳转 |
| FuniShareAction | 分享操作 | el-button | 中等 | 分享功能 |

## 布局组件

| 组件名 | 主要用途 | ElementPlus基础 | 配置复杂度 | 特殊功能 |
|--------|----------|----------------|------------|----------|
| FuniWrap | 包装容器 | - | 简单 | 布局包装 |
| FuniGroupTitle | 分组标题 | - | 简单 | 标题分组 |
| FuniLabel | 标签组件 | - | 简单 | 标签显示 |
| FuniSiteHeader | 站点头部 | - | 中等 | 页面头部 |
| FuniSiteFooter | 站点底部 | - | 中等 | 页面底部 |

## 高级组件

| 组件名 | 主要用途 | ElementPlus基础 | 配置复杂度 | 特殊功能 |
|--------|----------|----------------|------------|----------|
| FuniFormEngine | 表单设计器 | - | 高 | 可视化表单设计 |
| FuniFormRender | 表单渲染器 | - | 高 | 动态表单渲染 |
| FuniEventEditor | 事件编辑器 | - | 高 | 事件流设计 |
| FuniPageRender | 页面渲染器 | - | 高 | 动态页面渲染 |
| FuniVariableSetter | 变量设置器 | - | 高 | 变量绑定设置 |

## 日志和记录组件

| 组件名 | 主要用途 | ElementPlus基础 | 配置复杂度 | 特殊功能 |
|--------|----------|----------------|------------|----------|
| FuniLog | 日志组件 | - | 中等 | 日志展示 |
| FuniOperationLog | 操作日志 | - | 中等 | 操作记录 |
| FuniWorkRecord | 工作记录 | - | 中等 | 工作日志 |

## 组件选择决策

### 页面级组件选择
```mermaid
graph TD
    A[页面类型] --> B{列表页?}
    B -->|是| C[FuniListPageV2]
    B -->|否| D{详情/新建/编辑?}
    D -->|是| E[FuniDetail]
    D -->|否| F[自定义组件]
```

### 文件组件选择
```mermaid
graph TD
    A[文件功能需求] --> B{工作流场景?}
    B -->|是| C{有businessId?}
    C -->|是| D[FuniFileTable]
    C -->|否| E[等待businessId生成]
    B -->|否| F[FuniFileTable/upload.vue]
```

### 表单组件选择
```mermaid
graph TD
    A[表单需求] --> B{复杂表单?}
    B -->|是| C[FuniForm]
    B -->|否| D{选择器类型?}
    D --> E[FuniSelect/FuniTreeSelect/FuniRUOC]
```

## 使用优先级

### 必须使用（强制）
1. **FuniListPageV2** - 所有PC端列表页
2. **FuniDetail** - 所有PC端详情/新建/编辑页

### 推荐使用
1. **FuniForm** - 复杂表单场景
2. **FuniCurd** - 数据表格展示
3. **FuniSearch** - 搜索功能

### 条件使用
1. **FuniFileTable** - 仅工作流场景（需businessId）
2. **FuniAuditButtomBtn** - 仅审核场景
3. **FuniBusAuditDrawer** - 配合审核按钮

## 配置复杂度说明

### 简单（配置项 < 10个）
- 基础属性配置
- 直接使用即可
- 示例：FuniSelect、FuniIcon

### 中等（配置项 10-30个）
- 需要理解业务配置
- 参考示例配置
- 示例：FuniListPageV2、FuniCurd

### 高（配置项 > 30个）
- 复杂业务逻辑
- 需要深入理解文档
- 示例：FuniDetail、FuniChart

## ElementPlus API透传

所有FuniUI组件都支持对应ElementPlus组件的API：

```vue
<template>
  <!-- 示例：FuniSelect透传el-select的所有API -->
  <FuniSelect
    v-model="value"
    placeholder="请选择"
    clearable
    filterable
    multiple
    collapse-tags
    :loading="loading"
    @change="handleChange"
    @visible-change="handleVisibleChange"
  />
</template>
```

## Mock数据需求

### 工作流组件Mock数据
- **FuniFileTable**：附件配置、文件列表
- **FuniAuditButtomBtn**：动态按钮配置
- **FuniBusAuditDrawer**：审核历史、用户信息

### 业务组件Mock数据
- **FuniListPageV2**：列表数据、分页信息
- **FuniDetail**：详情数据、步骤配置
- **FuniForm**：表单配置、验证规则

## 最佳实践

1. **优先使用强制组件**：确保界面一致性
2. **合理选择组件**：根据业务场景选择合适组件
3. **充分利用透传**：使用ElementPlus原生API
4. **完善Mock数据**：确保开发阶段功能完整
5. **遵循配置规范**：参考官方示例配置
