# FuniIconSelect 配置结构定义

## 组件配置接口

### IFuniIconSelectProps

```typescript
interface IFuniIconSelectProps {
  /** 选择模式 */
  mode?: 'basic' | 'advanced'
}
```

### IIconObject

```typescript
interface IIconObject {
  /** 图标键值，遵循 RemixIcon 格式 */
  key: string
  /** 图标大小，单位 px */
  size: string
  /** 图标颜色，支持 hex、rgb、rgba 等格式 */
  color: string
  /** 图标描述信息 */
  description: string
  /** 图标唯一标识 */
  id: string
}
```

### IIconSelectEmits

```typescript
interface IIconSelectEmits {
  /** 图标选择事件 */
  updateIcon: (value: string | IIconObject) => void
}
```

### IIconSelectMethods

```typescript
interface IIconSelectMethods {
  /** 显示图标选择器 */
  show: (icon?: string | IIconObject) => void
}
```

## 图标数据结构

### IIconsData

```typescript
interface IIconsData {
  [categoryName: string]: {
    [iconKey: string]: string // 图标描述
  }
}
```

### 示例数据结构

```json
{
  "编辑器": {
    "bold": "粗体",
    "italic": "斜体",
    "underline": "下划线"
  },
  "文件": {
    "folder": "文件夹",
    "file": "文件",
    "download": "下载"
  },
  "用户": {
    "user": "用户",
    "team": "团队",
    "group": "群组"
  }
}
```

## 内部状态结构

### IComponentState

```typescript
interface IComponentState {
  /** 弹窗显示状态 */
  dialogVisible: boolean
  /** 当前选中的图标对象 */
  iconObject: IIconObject
  /** 当前选择的类型代码 */
  typeCode: string
  /** 图标类型列表 */
  iconTypes: string[]
  /** 图标类型项目映射 */
  iconTypesItems: IIconsData
  /** 滚动块配置 */
  scrollBlock: Record<string, any>
  /** 虚拟引用 */
  virtualRef: HTMLElement | undefined
  /** Popover 引用 */
  popoverRef: any
}
```

## 搜索组件配置

### ISearchProps

```typescript
interface ISearchProps {
  /** 图标类型列表 */
  iconTypes: string[]
  /** 当前类型代码 */
  typeCode: string
  /** 滚动块配置 */
  scrollBlock: Record<string, any>
}
```

### ISearchEmits

```typescript
interface ISearchEmits {
  /** 更新类型事件 */
  updateType: (type: string) => void
  /** 更新图标名称事件 */
  updateIconName: (name: string) => void
}
```

### ISearchState

```typescript
interface ISearchState {
  /** 图标名称输入值 */
  iconName: string
  /** 选择框显示状态 */
  selectShow: boolean
  /** 防抖定时器 */
  timer: NodeJS.Timeout | undefined
}
```

## 计算属性结构

### IComputedIconsType

```typescript
interface IComputedIconsType {
  [categoryName: string]: IIconItem[]
}

interface IIconItem {
  /** 图标键值 */
  key: string
  /** 图标大小 */
  size: string
  /** 图标颜色 */
  color: string
  /** 图标描述 */
  description: string
  /** 图标ID */
  id: string
}
```

## 配置示例

### 基础模式配置

```vue
<FuniIconSelect 
  mode="basic"
  @updateIcon="handleBasicSelect"
/>
```

```javascript
// 基础模式返回值
const handleBasicSelect = (iconKey) => {
  console.log(iconKey) // 'ri:folder-settings-fill'
}
```

### 高级模式配置

```vue
<FuniIconSelect 
  mode="advanced"
  @updateIcon="handleAdvancedSelect"
/>
```

```javascript
// 高级模式返回值
const handleAdvancedSelect = (iconObject) => {
  console.log(iconObject)
  // {
  //   key: 'ri:folder-settings-fill',
  //   size: '16',
  //   color: '#606266',
  //   description: '文件夹设置',
  //   id: 'icon_folder-settings'
  // }
}
```

### 预设图标配置

```javascript
// 显示时预设图标
const showWithPreset = () => {
  iconSelectRef.value.show({
    key: 'ri:user-fill',
    size: '24',
    color: '#409eff',
    description: '用户',
    id: 'icon_user'
  })
}
```

## 样式配置结构

### ICSSVariables

```css
:root {
  /* 选中状态阴影 */
  --el-box-shadow-selected: 0px 0px 8px 1px #0080ff;
  /* 图标颜色变量 */
  --icon-color: #606266;
}
```

### 主要样式类配置

```typescript
interface IStyleClasses {
  /** 主容器 */
  'funi-icon-select': string
  /** 图标类型名称 */
  'icon-type-name': string
  /** 图标总数标签 */
  'icon-total': string
  /** 图标列表 */
  'icons-list': string
  /** 图标项 */
  'icons-item': string
  /** 图标项内容 */
  'icons-item__self': string
  /** 选中状态 */
  'icon-item__clicked': string
  /** 图标描述 */
  'icons-item__description': string
  /** 设置按钮 */
  'icon-set': string
  /** 颜色大小配置 */
  'icon-color-size': string
}
```

## 事件处理配置

### IEventHandlers

```typescript
interface IEventHandlers {
  /** 图标选择处理 */
  setSelectIcon: (iconItem: IIconItem) => void
  /** 设置点击处理 */
  clickSite: (iconItem: IIconItem) => Promise<void>
  /** 确认处理 */
  onConfirm: () => void
  /** 类型更新处理 */
  updateType: (type: string) => void
  /** 图标名称更新处理 */
  updateIconName: (name?: string) => Promise<void>
}
```

## 性能优化配置

### IPerformanceConfig

```typescript
interface IPerformanceConfig {
  /** 搜索防抖延迟 */
  searchDebounceDelay: 800
  /** 网格列数 */
  gridColumns: 12
  /** 滚动行为 */
  scrollBehavior: 'smooth'
  /** 滚动位置 */
  scrollBlock: 'center'
  /** 内联位置 */
  scrollInline: 'nearest'
}
```

## 默认配置

```typescript
const defaultConfig: IFuniIconSelectProps = {
  mode: 'basic'
}

const defaultIconObject: IIconObject = {
  key: '',
  size: '16',
  color: '#606266',
  description: '',
  id: ''
}
```
