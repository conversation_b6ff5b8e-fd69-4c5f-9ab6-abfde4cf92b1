# FuniIconSelect API 文档

## 组件概述

FuniIconSelect 是一个图标选择器组件，提供了丰富的图标库供用户选择。组件基于 FuniDialog 实现，支持图标搜索、分类浏览和高级配置功能。

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| mode | String | 'basic' | 选择模式，可选值：'basic'（基础模式）、'advanced'（高级模式） |

### mode 详细说明

- **basic**: 基础模式，只返回图标的 key 值
- **advanced**: 高级模式，返回包含图标完整配置的对象（key、size、color、description、id）

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| updateIcon | iconKey \| iconObject | 选择图标后触发，参数根据 mode 不同而不同 |

### updateIcon 事件参数

**基础模式 (mode='basic')**:
```javascript
// 返回图标 key 字符串
'ri:folder-settings-fill'
```

**高级模式 (mode='advanced')**:
```javascript
// 返回图标配置对象
{
  key: 'ri:folder-settings-fill',
  size: '16',
  color: '#606266',
  description: '文件夹设置',
  id: 'icon_folder-settings'
}
```

## Methods

| 方法名 | 参数 | 说明 |
|--------|------|------|
| show | iconKey \| iconObject | 显示图标选择器弹窗 |

### show 方法参数

```javascript
// 传入图标 key 字符串
iconSelectRef.value.show('ri:folder-settings-fill')

// 传入图标配置对象
iconSelectRef.value.show({
  key: 'ri:folder-settings-fill',
  size: '20',
  color: '#409eff',
  description: '文件夹设置',
  id: 'icon_folder-settings'
})

// 不传参数，显示空白选择器
iconSelectRef.value.show()
```

## 内部组件结构

### 主要子组件

1. **FuniDialog**: 弹窗容器
2. **Search**: 搜索组件，支持按类型和名称搜索
3. **FuniIcon**: 图标显示组件

### 图标数据结构

组件使用 `./assets/icons.json` 文件作为图标数据源，数据结构如下：

```javascript
{
  "类型名称": {
    "icon-key": "图标描述",
    // ...更多图标
  }
  // ...更多类型
}
```

## 功能特性

### 1. 图标分类浏览
- 支持按类型分类浏览图标
- 显示每个类型下的图标数量
- 支持"所有"类型查看全部图标

### 2. 图标搜索
- 支持按图标名称搜索
- 支持按类型筛选
- 实时搜索，防抖处理（800ms）

### 3. 图标选择
- 点击图标进行选择
- 选中状态高亮显示
- 自动滚动到选中图标位置

### 4. 高级配置（advanced 模式）
- 支持设置图标大小
- 支持设置图标颜色
- 通过 Popover 提供配置界面

### 5. 响应式设计
- 网格布局自适应
- 支持滚动浏览
- 交互动画效果

## 样式类名

| 类名 | 说明 |
|------|------|
| .funi-icon-select | 主容器 |
| .icon-type-name | 类型标题 |
| .icon-total | 图标数量标签 |
| .icons-list | 图标列表容器 |
| .icons-item | 单个图标项 |
| .icons-item__self | 图标内容区域 |
| .icon-item__clicked | 选中状态 |
| .icons-item__description | 图标描述 |
| .icon-set | 设置按钮 |
| .icon-color-size | 颜色大小配置区域 |

## 使用示例

### 基础用法

```vue
<template>
  <div>
    <el-button @click="openIconSelect">选择图标</el-button>
    <div v-if="selectedIcon">
      选中的图标：{{ selectedIcon }}
    </div>
    <FuniIconSelect 
      ref="iconSelectRef"
      mode="basic"
      @updateIcon="handleIconSelect"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniIconSelect from '@/components/FuniIconSelect/index.vue'

const iconSelectRef = ref()
const selectedIcon = ref('')

const openIconSelect = () => {
  iconSelectRef.value.show()
}

const handleIconSelect = (iconKey) => {
  selectedIcon.value = iconKey
}
</script>
```

### 高级用法

```vue
<template>
  <div>
    <el-button @click="openIconSelect">选择图标</el-button>
    <div v-if="selectedIcon.key">
      <FuniIcon 
        :icon="selectedIcon.key"
        :style="{
          fontSize: selectedIcon.size + 'px',
          color: selectedIcon.color
        }"
      />
      <span>{{ selectedIcon.description }}</span>
    </div>
    <FuniIconSelect 
      ref="iconSelectRef"
      mode="advanced"
      @updateIcon="handleIconSelect"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniIconSelect from '@/components/FuniIconSelect/index.vue'
import FuniIcon from '@/components/FuniIcon/index.vue'

const iconSelectRef = ref()
const selectedIcon = ref({})

const openIconSelect = () => {
  // 可以传入已选择的图标进行编辑
  iconSelectRef.value.show(selectedIcon.value)
}

const handleIconSelect = (iconObject) => {
  selectedIcon.value = iconObject
}
</script>
```

## 注意事项

1. 组件依赖 `./assets/icons.json` 文件，确保文件存在且格式正确
2. 高级模式下的颜色选择功能需要配合 CSS 变量使用
3. 图标 key 格式遵循 RemixIcon 规范：`ri:icon-name-fill` 或 `ri:icon-name`
4. 搜索功能区分中英文，优先显示中文描述
5. 组件内部使用了 IntersectionObserver 进行性能优化
