# FuniSelect API文档

## 组件概述

FuniSelect是基于ElementPlus的el-select封装的选择器组件，增加了数据源配置、远程搜索、选项格式化等功能，适用于各种选择场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| modelValue | Any | - | - | 绑定值 | el-select.model-value |
| options | Array | [] | - | 选项数据数组 | - |
| props | Object | defaultProps | - | 选项属性配置 | - |
| api | String/Function | - | - | 远程数据API | - |
| params | Object | {} | - | API请求参数 | - |
| immediate | Boolean | true | - | 是否立即加载数据 | - |
| transform | Function | - | - | 数据转换函数 | - |
| placeholder | String | '请选择' | - | 占位符文本 | el-select.placeholder |
| clearable | Boolean | true | - | 是否可清空 | el-select.clearable |
| filterable | Boolean | false | - | 是否可搜索 | el-select.filterable |
| remote | Boolean | false | - | 是否远程搜索 | el-select.remote |
| remoteMethod | Function | - | - | 远程搜索方法 | el-select.remote-method |
| loading | Boolean | false | - | 是否加载中 | el-select.loading |
| loadingText | String | '加载中...' | - | 加载中文本 | el-select.loading-text |
| noDataText | String | '暂无数据' | - | 无数据文本 | el-select.no-data-text |
| noMatchText | String | '无匹配数据' | - | 无匹配文本 | el-select.no-match-text |
| multiple | Boolean | false | - | 是否多选 | el-select.multiple |
| multipleLimit | Number | 0 | - | 多选限制数量 | el-select.multiple-limit |
| collapseTags | Boolean | false | - | 是否折叠标签 | el-select.collapse-tags |
| collapseTagsTooltip | Boolean | false | - | 折叠标签提示 | el-select.collapse-tags-tooltip |
| size | String | 'default' | - | 组件尺寸 | el-select.size |
| disabled | Boolean | false | - | 是否禁用 | el-select.disabled |
| valueKey | String | 'value' | - | 作为value唯一标识的键名 | el-select.value-key |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: Any | 值更新事件 | 选择值变化时 |
| change | value: Any | 值变化事件 | 选择值变化时 |
| visible-change | visible: Boolean | 下拉框显示状态变化 | 下拉框显示/隐藏时 |
| remove-tag | value: Any | 移除标签事件 | 多选模式下移除标签时 |
| clear | - | 清空事件 | 点击清空按钮时 |
| blur | event: Event | 失去焦点事件 | 输入框失去焦点时 |
| focus | event: Event | 获得焦点事件 | 输入框获得焦点时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| focus | - | void | 使选择器获得焦点 |
| blur | - | void | 使选择器失去焦点 |
| refresh | - | Promise | 刷新选项数据 |

## props配置结构

```typescript
interface SelectProps {
  label?: string;                  // 选项标签字段名，默认'label'
  value?: string;                  // 选项值字段名，默认'value'
  disabled?: string;               // 选项禁用字段名，默认'disabled'
  children?: string;               // 子选项字段名，默认'children'
}

// 默认配置
const defaultProps = {
  label: 'label',
  value: 'value',
  disabled: 'disabled',
  children: 'children'
};
```

## 选项数据结构

```typescript
interface OptionItem {
  label: string;                   // 显示文本
  value: any;                      // 选项值
  disabled?: boolean;              // 是否禁用
  children?: OptionItem[];         // 子选项（分组选择器使用）
  [key: string]: any;              // 其他自定义字段
}

type OptionsData = OptionItem[];
```

## API配置

### 字符串API
```javascript
// 直接指定API地址
const api = '/api/users';
```

### 函数API
```javascript
// 自定义API函数
const api = async (params) => {
  const response = await request.get('/api/users', { params });
  return response.data;
};
```

## 使用示例

### 基础选择器
```vue
<template>
  <FuniSelect
    v-model="selectedValue"
    :options="options"
    placeholder="请选择用户"
    clearable
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedValue = ref('')

const options = ref([
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu', disabled: true }
])

const handleChange = (value) => {
  console.log('选择值变化:', value)
}
</script>
```

### 远程数据选择器
```vue
<template>
  <FuniSelect
    v-model="selectedUser"
    api="/api/users"
    :params="{ status: 'active' }"
    :props="{ label: 'name', value: 'id' }"
    placeholder="请选择用户"
    clearable
    filterable
    :transform="transformData"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedUser = ref('')

const transformData = (data) => {
  // 数据转换，添加额外信息
  return data.map(item => ({
    ...item,
    label: `${item.name} (${item.email})`,
    value: item.id
  }))
}
</script>
```

### 远程搜索选择器
```vue
<template>
  <FuniSelect
    v-model="selectedUser"
    remote
    filterable
    :remote-method="remoteSearch"
    :loading="loading"
    placeholder="请输入用户名搜索"
    clearable
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedUser = ref('')
const loading = ref(false)
const options = ref([])

const remoteSearch = async (query) => {
  if (!query) {
    options.value = []
    return
  }
  
  loading.value = true
  try {
    const response = await userApi.search({ keyword: query })
    options.value = response.data.map(user => ({
      label: user.name,
      value: user.id
    }))
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

### 多选选择器
```vue
<template>
  <FuniSelect
    v-model="selectedUsers"
    :options="userOptions"
    multiple
    collapse-tags
    collapse-tags-tooltip
    :multiple-limit="5"
    placeholder="请选择用户（最多5个）"
    @remove-tag="handleRemoveTag"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedUsers = ref([])

const userOptions = ref([
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu' },
  { label: '赵六', value: 'zhaoliu' },
  { label: '孙七', value: 'sunqi' }
])

const handleRemoveTag = (value) => {
  console.log('移除标签:', value)
}
</script>
```

### 分组选择器
```vue
<template>
  <FuniSelect
    v-model="selectedCity"
    :options="cityOptions"
    placeholder="请选择城市"
    clearable
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedCity = ref('')

const cityOptions = ref([
  {
    label: '热门城市',
    options: [
      { label: '北京', value: 'beijing' },
      { label: '上海', value: 'shanghai' },
      { label: '广州', value: 'guangzhou' },
      { label: '深圳', value: 'shenzhen' }
    ]
  },
  {
    label: '其他城市',
    options: [
      { label: '杭州', value: 'hangzhou' },
      { label: '南京', value: 'nanjing' },
      { label: '苏州', value: 'suzhou' }
    ]
  }
])
</script>
```

### 自定义选项模板
```vue
<template>
  <FuniSelect
    v-model="selectedUser"
    :options="userOptions"
    placeholder="请选择用户"
    clearable
  >
    <template #default="{ option }">
      <div class="user-option">
        <el-avatar :src="option.avatar" size="small" />
        <div class="user-info">
          <div class="user-name">{{ option.name }}</div>
          <div class="user-email">{{ option.email }}</div>
        </div>
      </div>
    </template>
  </FuniSelect>
</template>

<script setup>
import { ref } from 'vue'

const selectedUser = ref('')

const userOptions = ref([
  {
    label: '张三',
    value: 'zhangsan',
    name: '张三',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar1.jpg'
  },
  {
    label: '李四',
    value: 'lisi',
    name: '李四',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar2.jpg'
  }
])
</script>

<style scoped>
.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
}

.user-email {
  font-size: 12px;
  color: #999;
}
</style>
```

## ElementPlus API支持

FuniSelect基于el-select封装，支持所有el-select的API：

```vue
<template>
  <FuniSelect
    v-model="value"
    :options="options"
    
    <!-- ElementPlus el-select 所有属性 -->
    placeholder="请选择"
    clearable
    filterable
    multiple
    collapse-tags
    collapse-tags-tooltip
    :multiple-limit="0"
    size="default"
    :disabled="false"
    value-key="value"
    :loading="false"
    loading-text="加载中..."
    no-data-text="暂无数据"
    no-match-text="无匹配数据"
    :popper-class="popperClass"
    :teleported="true"
    :persistent="true"
    :automatic-dropdown="false"
    :clear-icon="clearIcon"
    :fit-input-width="false"
    :suffix-icon="suffixIcon"
    :tag-type="tagType"
    :validate-event="true"
    :remote="false"
    :remote-method="remoteMethod"
    :reserve-keyword="false"
    :default-first-option="false"
    :popup-transition="popupTransition"
    :allow-create="false"
    :filter-method="filterMethod"
    
    <!-- ElementPlus el-select 所有事件 -->
    @change="handleChange"
    @visible-change="handleVisibleChange"
    @remove-tag="handleRemoveTag"
    @clear="handleClear"
    @blur="handleBlur"
    @focus="handleFocus"
  />
</template>
```

## 注意事项

### 1. 数据源配置
- options和api二选一使用
- api支持字符串地址和函数形式
- 使用transform函数可以对API返回数据进行转换

### 2. 选项属性映射
- 通过props配置选项的字段映射
- 默认使用label、value、disabled字段
- 支持自定义字段名映射

### 3. 远程搜索
- 设置remote=true启用远程搜索
- 必须提供remoteMethod函数
- 建议添加防抖处理避免频繁请求

### 4. 性能优化
- 大量选项时建议使用远程搜索
- 使用immediate=false可以延迟加载数据
- 合理使用缓存避免重复请求

## 常见问题

### Q: 如何实现级联选择？
A: 使用FuniCascader组件，或者监听change事件动态更新下级选项

### Q: 如何自定义选项显示格式？
A: 使用默认插槽自定义选项模板，或者通过transform函数处理数据

### Q: 如何处理大量数据的性能问题？
A: 使用远程搜索模式，按需加载数据，避免一次性加载大量选项

### Q: 如何实现选项的动态禁用？
A: 在选项数据中设置disabled字段，或者通过computed动态计算选项状态
