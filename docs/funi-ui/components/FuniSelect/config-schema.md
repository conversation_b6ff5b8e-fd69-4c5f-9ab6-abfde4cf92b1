# FuniSelect 配置架构

## 组件配置接口

### 基础配置类型
```typescript
interface FuniSelectConfig {
  // 基础属性
  modelValue?: any;                    // 绑定值
  options?: OptionItem[];              // 选项数据数组
  url?: string;                        // 请求地址
  typeCode?: string;                   // 字典码
  isLocal?: boolean;                   // 是否从本地缓存获取字典
  storageKey?: string;                 // 本地缓存的key
  action?: Function;                   // 自定义数据获取函数
  
  // ElementPlus el-select 属性
  multiple?: boolean;                  // 是否多选
  disabled?: boolean;                  // 是否禁用
  valueKey?: string;                   // 作为value唯一标识的键名
  size?: ComponentSize;                // 输入框尺寸
  clearable?: boolean;                 // 是否可以清空选项
  collapseTags?: boolean;              // 多选时是否将选中值按文字的形式展示
  collapseTagsTooltip?: boolean;       // 当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签
  multipleLimit?: number;              // 多选时用户最多可以选择的项目数
  placeholder?: string;                // 占位符
  filterable?: boolean;                // 是否可搜索
  allowCreate?: boolean;               // 是否允许用户创建新条目
  filterMethod?: Function;             // 自定义搜索方法
  remote?: boolean;                    // 是否为远程搜索
  remoteMethod?: Function;             // 远程搜索方法
  loading?: boolean;                   // 是否正在从远程获取数据
  loadingText?: string;                // 远程加载时显示的文字
  noMatchText?: string;                // 搜索条件无匹配时显示的文字
  noDataText?: string;                 // 选项为空时显示的文字
  popperClass?: string;                // Select下拉框的类名
  reserveKeyword?: boolean;            // 多选且可搜索时，是否在选中一个选项后保留当前的搜索关键词
  defaultFirstOption?: boolean;        // 在输入框按下回车，选择第一个匹配项
  teleported?: boolean;                // 是否将弹出框插入至body元素
  persistent?: boolean;                // 当下拉选择器未被激活并且persistent设置为false时，选择器会被删除
  automaticDropdown?: boolean;         // 对于不可搜索的Select，是否在输入框获得焦点后自动弹出选项菜单
  clearIcon?: string | Component;      // 自定义清空图标
  fitInputWidth?: boolean;             // 下拉框的宽度是否与输入框相同
  suffixIcon?: string | Component;     // 自定义后缀图标
  tagType?: TagType;                   // 标签类型
  validateEvent?: boolean;             // 输入时是否触发表单的校验
  
  // 事件处理
  onChange?: (value: any) => void;
  onVisibleChange?: (visible: boolean) => void;
  onRemoveTag?: (value: any) => void;
  onClear?: () => void;
  onBlur?: (event: Event) => void;
  onFocus?: (event: Event) => void;
}
```

### 选项数据类型
```typescript
interface OptionItem {
  label: string;                       // 显示文本
  value: any;                          // 选项值
  disabled?: boolean;                  // 是否禁用
  [key: string]: any;                  // 其他自定义字段
}

type OptionsData = OptionItem[];
```

### 字典数据类型
```typescript
interface DicResponse {
  name: string;                        // 字典项名称
  code: string;                        // 字典项编码
  [key: string]: any;                  // 其他字段
}

interface EnumSource {
  code: string;                        // 字典类型编码
  dicResponses: DicResponse[];         // 字典项列表
}
```

### 组件尺寸类型
```typescript
type ComponentSize = 'large' | 'default' | 'small';
```

### 标签类型
```typescript
type TagType = 'success' | 'info' | 'warning' | 'danger';
```

## 配置示例

### 基础选择器配置
```typescript
const basicSelectConfig: FuniSelectConfig = {
  modelValue: '',
  options: [
    { label: '选项1', value: 'option1' },
    { label: '选项2', value: 'option2' },
    { label: '选项3', value: 'option3', disabled: true }
  ],
  placeholder: '请选择',
  clearable: true,
  size: 'default'
};
```

### 字典数据选择器配置
```typescript
const dictSelectConfig: FuniSelectConfig = {
  modelValue: '',
  url: '/csops/dic/findDicByType',
  typeCode: 'USER_STATUS',
  isLocal: false,
  placeholder: '请选择用户状态',
  clearable: true
};
```

### 本地缓存字典配置
```typescript
const localDictConfig: FuniSelectConfig = {
  modelValue: '',
  typeCode: 'DEPARTMENT_TYPE',
  isLocal: true,
  storageKey: 'enumSource',
  placeholder: '请选择部门类型',
  clearable: true
};
```

### 自定义数据源配置
```typescript
const customActionConfig: FuniSelectConfig = {
  modelValue: '',
  action: async (query: string) => {
    const response = await userApi.search({ keyword: query });
    return response.data.map(user => ({
      label: `${user.name} (${user.email})`,
      value: user.id
    }));
  },
  placeholder: '请选择用户',
  clearable: true,
  filterable: true,
  remote: true
};
```

### 多选配置
```typescript
const multipleSelectConfig: FuniSelectConfig = {
  modelValue: [],
  options: [
    { label: '前端开发', value: 'frontend' },
    { label: '后端开发', value: 'backend' },
    { label: '测试工程师', value: 'tester' },
    { label: '产品经理', value: 'pm' }
  ],
  multiple: true,
  collapseTags: true,
  collapseTagsTooltip: true,
  multipleLimit: 3,
  placeholder: '请选择技能（最多3个）',
  clearable: true
};
```

### 远程搜索配置
```typescript
const remoteSearchConfig: FuniSelectConfig = {
  modelValue: '',
  remote: true,
  filterable: true,
  remoteMethod: async (query: string) => {
    if (!query) return [];
    
    const response = await api.searchUsers({ keyword: query });
    return response.data.map(user => ({
      label: user.name,
      value: user.id
    }));
  },
  loading: false,
  loadingText: '搜索中...',
  noMatchText: '无匹配数据',
  noDataText: '暂无数据',
  placeholder: '请输入关键词搜索',
  clearable: true,
  reserveKeyword: true
};
```

### 可创建新选项配置
```typescript
const creatableConfig: FuniSelectConfig = {
  modelValue: '',
  options: [
    { label: '标签1', value: 'tag1' },
    { label: '标签2', value: 'tag2' }
  ],
  filterable: true,
  allowCreate: true,
  defaultFirstOption: true,
  placeholder: '请选择或输入新标签',
  clearable: true
};
```

## 表单集成配置

### 在FuniForm中使用
```typescript
interface FormSelectSchema {
  prop: string;
  label: string;
  component: 'FuniSelect';
  props: FuniSelectConfig;
  rules?: FormRule[];
  span?: number;
  hidden?: boolean | ((formData: any) => boolean);
}

const formSchema: FormSelectSchema = {
  prop: 'department',
  label: '部门',
  component: 'FuniSelect',
  props: {
    url: '/api/departments',
    typeCode: 'DEPARTMENT',
    placeholder: '请选择部门',
    clearable: true
  },
  rules: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  span: 12
};
```

### 在FuniSearch中使用
```typescript
interface SearchSelectSchema {
  prop: string;
  label: string;
  component: 'FuniSelect';
  props: FuniSelectConfig;
  span?: number;
}

const searchSchema: SearchSelectSchema = {
  prop: 'status',
  label: '状态',
  component: 'FuniSelect',
  props: {
    options: [
      { label: '全部', value: '' },
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    placeholder: '请选择状态',
    clearable: true
  },
  span: 6
};
```

## 数据转换配置

### 响应数据转换
```typescript
interface DataTransformConfig {
  // 字典数据转换
  dictTransform?: (dicResponses: DicResponse[]) => OptionItem[];
  
  // API响应数据转换
  responseTransform?: (response: any) => OptionItem[];
  
  // 选项数据格式化
  optionFormatter?: (item: any) => OptionItem;
}

const transformConfig: DataTransformConfig = {
  dictTransform: (dicResponses) => {
    return dicResponses.map(item => ({
      label: item.name,
      value: item.code,
      disabled: item.status === 0
    }));
  },
  
  responseTransform: (response) => {
    return response.list.map(user => ({
      label: `${user.name} - ${user.department}`,
      value: user.id,
      disabled: !user.active
    }));
  },
  
  optionFormatter: (item) => ({
    label: item.displayName || item.name,
    value: item.id || item.code,
    disabled: item.disabled || false
  })
};
```

## 验证规则配置

### 常用验证规则
```typescript
interface SelectValidationRules {
  required?: boolean;
  message?: string;
  trigger?: string | string[];
  validator?: (rule: any, value: any, callback: Function) => void;
}

const validationRules: SelectValidationRules[] = [
  // 必填验证
  { required: true, message: '请选择选项', trigger: 'change' },
  
  // 多选数量验证
  {
    validator: (rule, value, callback) => {
      if (Array.isArray(value) && value.length > 5) {
        callback(new Error('最多只能选择5个选项'));
      } else {
        callback();
      }
    },
    trigger: 'change'
  },
  
  // 自定义验证
  {
    validator: (rule, value, callback) => {
      if (value && !['admin', 'user'].includes(value)) {
        callback(new Error('只能选择管理员或普通用户'));
      } else {
        callback();
      }
    },
    trigger: 'change'
  }
];
```

## 性能优化配置

### 大数据量优化
```typescript
interface PerformanceConfig {
  // 虚拟滚动配置
  virtualScroll?: boolean;
  itemHeight?: number;
  visibleCount?: number;
  
  // 懒加载配置
  lazy?: boolean;
  pageSize?: number;
  
  // 缓存配置
  cache?: boolean;
  cacheKey?: string;
  cacheExpire?: number;
}

const performanceConfig: PerformanceConfig = {
  virtualScroll: true,
  itemHeight: 34,
  visibleCount: 10,
  lazy: true,
  pageSize: 50,
  cache: true,
  cacheKey: 'select-options',
  cacheExpire: 300000 // 5分钟
};
```
