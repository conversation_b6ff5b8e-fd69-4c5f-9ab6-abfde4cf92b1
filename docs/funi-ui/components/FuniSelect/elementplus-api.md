# FuniSelect ElementPlus API支持

## 概述

FuniSelect基于ElementPlus的`el-select`组件封装，通过`v-bind="$attrs"`实现了对ElementPlus选择器API的完全透传支持。这意味着您可以直接使用所有ElementPlus选择器组件的原生属性、事件和方法。

## 支持的ElementPlus API

### el-select 属性透传

FuniSelect支持所有`el-select`的原生属性：

```vue
<template>
  <FuniSelect
    v-model="selectedValue"
    :options="options"
    
    <!-- ElementPlus el-select 原生属性 -->
    :multiple="false"
    :disabled="false"
    value-key="value"
    size="default"
    :clearable="true"
    :collapse-tags="false"
    :collapse-tags-tooltip="false"
    :multiple-limit="0"
    placeholder="请选择"
    :filterable="false"
    :allow-create="false"
    :filter-method="filterMethod"
    :remote="false"
    :remote-method="remoteMethod"
    :loading="false"
    loading-text="加载中"
    no-match-text="无匹配数据"
    no-data-text="无数据"
    :popper-class="popperClass"
    :reserve-keyword="false"
    :default-first-option="false"
    :teleported="true"
    :persistent="true"
    :automatic-dropdown="false"
    :clear-icon="clearIcon"
    :fit-input-width="false"
    :suffix-icon="suffixIcon"
    tag-type="info"
    :validate-event="true"
    :placement="placement"
    
    <!-- ElementPlus el-select 原生事件 -->
    @change="handleChange"
    @visible-change="handleVisibleChange"
    @remove-tag="handleRemoveTag"
    @clear="handleClear"
    @blur="handleBlur"
    @focus="handleFocus"
  />
</template>
```

### 属性详细说明

#### 基础属性
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| multiple | Boolean | false | 是否多选 |
| disabled | Boolean | false | 是否禁用 |
| value-key | String | value | 作为value唯一标识的键名 |
| size | String | default | 输入框尺寸，可选值：large/default/small |
| clearable | Boolean | false | 是否可以清空选项 |
| collapse-tags | Boolean | false | 多选时是否将选中值按文字的形式展示 |
| collapse-tags-tooltip | Boolean | false | 当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签 |
| multiple-limit | Number | 0 | 多选时用户最多可以选择的项目数，为0则不限制 |
| placeholder | String | 请选择 | 占位符 |
| filterable | Boolean | false | 是否可搜索 |
| allow-create | Boolean | false | 是否允许用户创建新条目 |
| filter-method | Function | — | 自定义搜索方法 |
| remote | Boolean | false | 是否为远程搜索 |
| remote-method | Function | — | 远程搜索方法 |
| loading | Boolean | false | 是否正在从远程获取数据 |
| loading-text | String | 加载中 | 远程加载时显示的文字 |
| no-match-text | String | 无匹配数据 | 搜索条件无匹配时显示的文字 |
| no-data-text | String | 无数据 | 选项为空时显示的文字 |
| popper-class | String | — | Select下拉框的类名 |
| reserve-keyword | Boolean | false | 多选且可搜索时，是否在选中一个选项后保留当前的搜索关键词 |
| default-first-option | Boolean | false | 在输入框按下回车，选择第一个匹配项 |
| teleported | Boolean | true | 是否将弹出框插入至body元素 |
| persistent | Boolean | true | 当下拉选择器未被激活并且persistent设置为false时，选择器会被删除 |
| automatic-dropdown | Boolean | false | 对于不可搜索的Select，是否在输入框获得焦点后自动弹出选项菜单 |
| clear-icon | String/Component | CircleClose | 自定义清空图标 |
| fit-input-width | Boolean | false | 下拉框的宽度是否与输入框相同 |
| suffix-icon | String/Component | ArrowDown | 自定义后缀图标 |
| tag-type | String | info | 标签类型，可选值：success/info/warning/danger |
| validate-event | Boolean | true | 输入时是否触发表单的校验 |
| placement | String | bottom-start | 下拉框出现的位置 |

### 事件透传

FuniSelect支持所有`el-select`的原生事件：

```vue
<template>
  <FuniSelect
    v-model="selectedValue"
    :options="options"
    @change="handleChange"
    @visible-change="handleVisibleChange"
    @remove-tag="handleRemoveTag"
    @clear="handleClear"
    @blur="handleBlur"
    @focus="handleFocus"
  />
</template>

<script setup>
// 事件处理函数
const handleChange = (value) => {
  console.log('值变化:', value)
}

const handleVisibleChange = (visible) => {
  console.log('下拉框显示状态:', visible)
}

const handleRemoveTag = (value) => {
  console.log('移除标签:', value)
}

const handleClear = () => {
  console.log('清空选择')
}

const handleBlur = (event) => {
  console.log('失去焦点:', event)
}

const handleFocus = (event) => {
  console.log('获得焦点:', event)
}
</script>
```

### 方法透传

通过模板引用可以访问所有ElementPlus选择器方法：

```vue
<template>
  <FuniSelect
    ref="selectRef"
    v-model="selectedValue"
    :options="options"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'

const selectRef = ref()
const selectedValue = ref('')

onMounted(() => {
  // 访问ElementPlus原生方法
  const selectInstance = selectRef.value
  
  // 使选择器获得焦点
  selectInstance.focus()
  
  // 使选择器失去焦点
  selectInstance.blur()
})

// 手动调用方法
const focusSelect = () => {
  selectRef.value?.focus()
}

const blurSelect = () => {
  selectRef.value?.blur()
}
</script>
```

## 选项组件API透传

### el-option 属性支持

在options数组中，每个选项对象支持所有`el-option`的属性：

```javascript
const options = [
  {
    label: '选项1',
    value: 'option1',
    disabled: false,        // el-option.disabled
    key: 'option1'          // el-option.key
  },
  {
    label: '选项2',
    value: 'option2',
    disabled: true
  }
]
```

### el-option-group 支持

FuniSelect支持选项分组，通过特殊的数据结构实现：

```javascript
const groupedOptions = [
  {
    label: '热门城市',
    options: [
      { label: '北京', value: 'beijing' },
      { label: '上海', value: 'shanghai' }
    ]
  },
  {
    label: '其他城市',
    options: [
      { label: '广州', value: 'guangzhou' },
      { label: '深圳', value: 'shenzhen' }
    ]
  }
]
```

## 高级功能API

### 远程搜索配置
```vue
<template>
  <FuniSelect
    v-model="selectedValue"
    :options="options"
    remote
    filterable
    :remote-method="remoteSearch"
    :loading="loading"
    loading-text="搜索中..."
    no-match-text="无匹配数据"
    no-data-text="请输入关键词搜索"
    reserve-keyword
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedValue = ref('')
const options = ref([])
const loading = ref(false)

const remoteSearch = async (query) => {
  if (!query) {
    options.value = []
    return
  }
  
  loading.value = true
  try {
    // 调用API搜索
    const response = await searchAPI(query)
    options.value = response.data
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

### 自定义过滤方法
```vue
<template>
  <FuniSelect
    v-model="selectedValue"
    :options="options"
    filterable
    :filter-method="customFilter"
  />
</template>

<script setup>
const customFilter = (value) => {
  // 自定义过滤逻辑
  return (option) => {
    return option.label.toLowerCase().includes(value.toLowerCase()) ||
           option.value.toString().includes(value)
  }
}
</script>
```

### 多选限制和标签处理
```vue
<template>
  <FuniSelect
    v-model="selectedValues"
    :options="options"
    multiple
    :multiple-limit="5"
    collapse-tags
    collapse-tags-tooltip
    tag-type="success"
    @remove-tag="handleRemoveTag"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedValues = ref([])

const handleRemoveTag = (value) => {
  console.log('移除标签:', value)
  // 可以在这里添加自定义逻辑
}
</script>
```

## 样式定制

### CSS变量支持

FuniSelect继承了ElementPlus的CSS变量系统：

```css
:root {
  /* 选择器相关变量 */
  --el-select-border-color-hover: #c0c4cc;
  --el-select-disabled-border: #e4e7ed;
  --el-select-font-size: 14px;
  --el-select-close-hover-color: #909399;
  --el-select-input-color: #666;
  --el-select-multiple-input-color: #666;
  --el-select-input-focus-border-color: #409eff;
  --el-select-input-font-size: 14px;
  
  /* 下拉框相关变量 */
  --el-select-dropdown-bg: #fff;
  --el-select-dropdown-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --el-select-dropdown-empty-color: #999;
  --el-select-dropdown-max-height: 274px;
  --el-select-dropdown-padding: 6px 0;
  --el-select-dropdown-empty-padding: 10px 0;
  
  /* 选项相关变量 */
  --el-select-option-color: #606266;
  --el-select-option-disabled-color: #c0c4cc;
  --el-select-option-height: 34px;
  --el-select-option-hover-bg: #f5f7fa;
  --el-select-option-selected-color: #409eff;
  --el-select-option-selected-bg: #f5f7fa;
  
  /* 分组相关变量 */
  --el-select-group-color: #909399;
  --el-select-group-height: 30px;
  --el-select-group-font-size: 12px;
}
```

### 自定义样式类
```vue
<template>
  <FuniSelect
    v-model="selectedValue"
    :options="options"
    popper-class="custom-select-dropdown"
    class="custom-select"
  />
</template>

<style>
.custom-select {
  /* 自定义选择器样式 */
}

.custom-select-dropdown {
  /* 自定义下拉框样式 */
}
</style>
```

## 兼容性说明

FuniSelect与ElementPlus版本兼容性：
- 支持ElementPlus 2.0+
- 建议使用ElementPlus 2.3.0及以上版本以获得最佳体验
- 所有ElementPlus选择器相关组件的API都得到完整支持

## 注意事项

### 1. 属性优先级
- FuniSelect的自定义属性（如url、typeCode、action等）优先级高于透传的ElementPlus属性
- 当存在冲突时，FuniSelect的属性会覆盖ElementPlus的同名属性

### 2. 事件处理
- 可以同时监听FuniSelect的自定义事件和ElementPlus的原生事件
- FuniSelect会在适当的时机触发ElementPlus的原生事件

### 3. 方法调用
- 通过模板引用可以直接访问ElementPlus的原生方法
- FuniSelect不会覆盖或修改ElementPlus的原生方法

### 4. 数据格式
- options数组中的每个选项对象必须包含label和value字段
- 支持通过disabled字段控制选项的禁用状态
- 支持选项分组，通过嵌套的options数组实现
