# FuniSelect 最佳实践

## 推荐用法

### 1. 标准选择器配置
```vue
<template>
  <FuniSelect
    v-model="selectedValue"
    :options="options"
    placeholder="请选择选项"
    clearable
    filterable
    @change="handleChange"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

// 推荐：使用响应式数据管理选中值
const selectedValue = ref('')

// 推荐：将选项定义为响应式数据
const options = reactive([
  { label: '选项一', value: 'option1' },
  { label: '选项二', value: 'option2' },
  { label: '选项三', value: 'option3' }
])

// 推荐：统一的变化处理
const handleChange = (value) => {
  console.log('选择值变化:', value)
  // 在这里处理业务逻辑
}
</script>
```

### 2. 字典数据最佳实践
```vue
<template>
  <!-- 推荐：使用字典码获取数据 -->
  <FuniSelect
    v-model="userStatus"
    url="/csops/dic/findDicByType"
    type-code="USER_STATUS"
    :is-local="useLocalCache"
    storage-key="enumSource"
    placeholder="请选择用户状态"
    clearable
    @change="handleStatusChange"
  />
</template>

<script setup>
import { ref, computed } from 'vue'

const userStatus = ref('')

// 推荐：根据环境决定是否使用本地缓存
const useLocalCache = computed(() => {
  return process.env.NODE_ENV === 'production'
})

const handleStatusChange = (value) => {
  console.log('用户状态变化:', value)
}
</script>
```

### 3. 远程搜索最佳实践
```vue
<template>
  <FuniSelect
    v-model="selectedUser"
    :options="userOptions"
    remote
    filterable
    :remote-method="debouncedSearch"
    :loading="loading"
    loading-text="搜索中..."
    no-match-text="无匹配数据"
    no-data-text="请输入关键词搜索"
    placeholder="请输入用户名搜索"
    clearable
    reserve-keyword
  />
</template>

<script setup>
import { ref } from 'vue'
import { debounce } from 'lodash-es'

const selectedUser = ref('')
const userOptions = ref([])
const loading = ref(false)

// 推荐：使用防抖处理搜索请求
const debouncedSearch = debounce(async (query) => {
  if (!query) {
    userOptions.value = []
    return
  }
  
  loading.value = true
  try {
    const response = await userApi.search({ keyword: query })
    userOptions.value = response.data.map(user => ({
      label: `${user.name} (${user.email})`,
      value: user.id
    }))
  } catch (error) {
    console.error('搜索失败:', error)
    userOptions.value = []
  } finally {
    loading.value = false
  }
}, 300) // 300ms防抖延迟

// 推荐：组件卸载时清理防抖函数
onUnmounted(() => {
  debouncedSearch.cancel()
})
</script>
```

## 性能优化

### 1. 大数据量优化
```vue
<script setup>
import { ref, computed, shallowRef } from 'vue'

// 推荐：大量选项使用shallowRef减少响应式开销
const allOptions = shallowRef([])
const searchKeyword = ref('')

// 推荐：使用computed进行客户端过滤
const filteredOptions = computed(() => {
  if (!searchKeyword.value) {
    return allOptions.value.slice(0, 100) // 限制显示数量
  }
  
  return allOptions.value
    .filter(option => 
      option.label.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
    .slice(0, 50) // 搜索结果也要限制数量
})

// 推荐：使用虚拟滚动处理大量选项
const virtualScrollConfig = {
  itemHeight: 34,
  visibleCount: 10,
  buffer: 5
}
</script>
```

### 2. 缓存策略
```vue
<script setup>
import { ref, onMounted } from 'vue'

const options = ref([])
const cacheKey = 'select-options-cache'
const cacheExpire = 5 * 60 * 1000 // 5分钟

// 推荐：实现选项数据缓存
const loadOptionsWithCache = async () => {
  // 检查缓存
  const cached = localStorage.getItem(cacheKey)
  if (cached) {
    try {
      const { data, timestamp } = JSON.parse(cached)
      if (Date.now() - timestamp < cacheExpire) {
        options.value = data
        return
      }
    } catch (error) {
      console.warn('缓存数据解析失败:', error)
    }
  }
  
  // 加载新数据
  try {
    const response = await api.getOptions()
    options.value = response.data
    
    // 更新缓存
    localStorage.setItem(cacheKey, JSON.stringify({
      data: response.data,
      timestamp: Date.now()
    }))
  } catch (error) {
    console.error('加载选项失败:', error)
  }
}

onMounted(() => {
  loadOptionsWithCache()
})
</script>
```

## 数据管理

### 1. 选项数据标准化
```vue
<script setup>
import { ref, computed } from 'vue'

const rawOptions = ref([])

// 推荐：标准化选项数据格式
const normalizedOptions = computed(() => {
  return rawOptions.value.map(item => ({
    label: item.name || item.label || item.title,
    value: item.id || item.value || item.code,
    disabled: item.disabled || item.status === 0,
    // 保留原始数据用于后续处理
    raw: item
  }))
})

// 推荐：提供获取原始数据的方法
const getOriginalData = (value) => {
  const option = normalizedOptions.value.find(opt => opt.value === value)
  return option?.raw
}
</script>
```

### 2. 多选数据处理
```vue
<script setup>
import { ref, watch } from 'vue'

const selectedValues = ref([])
const maxSelection = 5

// 推荐：监听多选变化并进行验证
watch(selectedValues, (newValues, oldValues) => {
  // 数量限制检查
  if (newValues.length > maxSelection) {
    ElMessage.warning(`最多只能选择${maxSelection}个选项`)
    selectedValues.value = oldValues
    return
  }
  
  // 业务逻辑处理
  handleSelectionChange(newValues)
}, { deep: true })

const handleSelectionChange = (values) => {
  console.log('选择项变化:', values)
  // 处理业务逻辑
}

// 推荐：提供便捷的操作方法
const addSelection = (value) => {
  if (!selectedValues.value.includes(value)) {
    selectedValues.value.push(value)
  }
}

const removeSelection = (value) => {
  const index = selectedValues.value.indexOf(value)
  if (index > -1) {
    selectedValues.value.splice(index, 1)
  }
}

const clearSelection = () => {
  selectedValues.value = []
}
</script>
```

## 错误处理

### 1. API错误处理
```vue
<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const options = ref([])
const loading = ref(false)
const error = ref(null)

// 推荐：统一的错误处理函数
const handleApiError = (error, defaultMessage = '操作失败') => {
  console.error('API错误:', error)
  
  let message = defaultMessage
  if (error.response?.data?.message) {
    message = error.response.data.message
  } else if (error.message) {
    message = error.message
  }
  
  ElMessage.error(message)
  return message
}

// 推荐：带错误处理的数据加载
const loadOptions = async () => {
  loading.value = true
  error.value = null
  
  try {
    const response = await api.getOptions()
    options.value = response.data
  } catch (err) {
    error.value = handleApiError(err, '加载选项失败')
    options.value = [] // 确保有默认值
  } finally {
    loading.value = false
  }
}

// 推荐：提供重试机制
const retryLoad = () => {
  if (!loading.value) {
    loadOptions()
  }
}
</script>
```

### 2. 数据验证
```vue
<script setup>
import { ref, computed } from 'vue'

const selectedValue = ref('')
const options = ref([])

// 推荐：验证选中值的有效性
const isValidSelection = computed(() => {
  if (!selectedValue.value) return true
  return options.value.some(option => option.value === selectedValue.value)
})

// 推荐：自动修复无效选择
watch([selectedValue, options], ([value, opts]) => {
  if (value && opts.length > 0 && !isValidSelection.value) {
    console.warn('检测到无效选择，自动清空:', value)
    selectedValue.value = ''
  }
})
</script>
```

## 可访问性

### 1. 无障碍支持
```vue
<template>
  <div class="select-container">
    <label for="user-select" class="select-label">
      用户选择
      <span class="required-mark" aria-label="必填">*</span>
    </label>
    <FuniSelect
      id="user-select"
      v-model="selectedUser"
      :options="userOptions"
      placeholder="请选择用户"
      clearable
      :aria-label="ariaLabel"
      :aria-describedby="ariaDescribedBy"
      @change="handleUserChange"
    />
    <div id="user-select-help" class="help-text">
      请从列表中选择一个用户
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const selectedUser = ref('')
const userOptions = ref([])

// 推荐：提供适当的aria属性
const ariaLabel = computed(() => {
  return selectedUser.value ? `已选择用户: ${getSelectedUserName()}` : '用户选择器'
})

const ariaDescribedBy = 'user-select-help'

const getSelectedUserName = () => {
  const user = userOptions.value.find(u => u.value === selectedUser.value)
  return user?.label || ''
}
</script>

<style scoped>
.select-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.required-mark {
  color: #f56c6c;
  margin-left: 4px;
}

.help-text {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}
</style>
```

## 测试建议

### 1. 单元测试
```javascript
// 推荐：测试选择器的基本功能
describe('FuniSelect', () => {
  test('should emit change event when selection changes', async () => {
    const wrapper = mount(FuniSelect, {
      props: {
        modelValue: '',
        options: [
          { label: '选项1', value: 'option1' },
          { label: '选项2', value: 'option2' }
        ]
      }
    })
    
    const select = wrapper.findComponent({ name: 'ElSelect' })
    await select.vm.$emit('change', 'option1')
    
    expect(wrapper.emitted('change')).toBeTruthy()
    expect(wrapper.emitted('change')[0]).toEqual(['option1'])
  })
  
  test('should load options from API', async () => {
    const mockApi = jest.fn().mockResolvedValue({
      data: [{ label: '测试', value: 'test' }]
    })
    
    const wrapper = mount(FuniSelect, {
      props: {
        action: mockApi
      }
    })
    
    await wrapper.vm.$nextTick()
    expect(mockApi).toHaveBeenCalled()
  })
})
```

### 2. 集成测试
```javascript
// 推荐：测试与表单的集成
describe('FuniSelect in Form', () => {
  test('should validate required selection', async () => {
    const wrapper = mount(FormComponent, {
      props: {
        schema: [
          {
            prop: 'user',
            component: 'FuniSelect',
            props: { options: userOptions },
            rules: [{ required: true, message: '请选择用户' }]
          }
        ]
      }
    })
    
    const form = wrapper.findComponent({ name: 'ElForm' })
    const result = await form.vm.validate()
    
    expect(result).toBe(false)
  })
})
```

## 常见问题

### 1. 选项数据不更新
```vue
<script setup>
// ❌ 错误：直接修改props中的options
const props = defineProps(['options'])
props.options.push(newOption) // 不要这样做

// ✅ 正确：使用本地响应式数据
const localOptions = ref([...props.options])
const addOption = (option) => {
  localOptions.value.push(option)
}
</script>
```

### 2. 远程搜索性能问题
```vue
<script setup>
// ❌ 错误：没有防抖的搜索
const remoteSearch = async (query) => {
  // 每次输入都会触发请求
  const response = await api.search(query)
  options.value = response.data
}

// ✅ 正确：使用防抖优化
import { debounce } from 'lodash-es'

const remoteSearch = debounce(async (query) => {
  if (!query) return
  
  loading.value = true
  try {
    const response = await api.search(query)
    options.value = response.data
  } finally {
    loading.value = false
  }
}, 300)
</script>
```

### 3. 内存泄漏问题
```vue
<script setup>
import { onUnmounted } from 'vue'

const timers = []
const abortController = new AbortController()

// ✅ 正确：清理资源
onUnmounted(() => {
  // 清理定时器
  timers.forEach(timer => clearTimeout(timer))
  
  // 取消未完成的请求
  abortController.abort()
  
  // 清理防抖函数
  if (debouncedSearch) {
    debouncedSearch.cancel()
  }
})
</script>
```
