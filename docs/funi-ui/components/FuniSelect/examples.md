# FuniSelect 使用示例

## 基础使用

### 简单选择器
```vue
<template>
  <div class="basic-select-demo">
    <h3>基础选择器</h3>
    <FuniSelect
      v-model="selectedValue"
      :options="basicOptions"
      placeholder="请选择选项"
      clearable
      @change="handleChange"
    />
    <p>选中值: {{ selectedValue }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedValue = ref('')

const basicOptions = ref([
  { label: '选项一', value: 'option1' },
  { label: '选项二', value: 'option2' },
  { label: '选项三', value: 'option3' },
  { label: '禁用选项', value: 'option4', disabled: true }
])

const handleChange = (value) => {
  console.log('选择值变化:', value)
}
</script>

<style scoped>
.basic-select-demo {
  padding: 20px;
}
</style>
```

### 多选选择器
```vue
<template>
  <div class="multiple-select-demo">
    <h3>多选选择器</h3>
    <FuniSelect
      v-model="selectedValues"
      :options="skillOptions"
      multiple
      collapse-tags
      collapse-tags-tooltip
      :multiple-limit="3"
      placeholder="请选择技能（最多3个）"
      clearable
      @change="handleMultipleChange"
      @remove-tag="handleRemoveTag"
    />
    <p>选中值: {{ selectedValues }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const selectedValues = ref([])

const skillOptions = ref([
  { label: 'JavaScript', value: 'js' },
  { label: 'TypeScript', value: 'ts' },
  { label: 'Vue.js', value: 'vue' },
  { label: 'React', value: 'react' },
  { label: 'Node.js', value: 'node' },
  { label: 'Python', value: 'python' },
  { label: 'Java', value: 'java' },
  { label: 'Go', value: 'go' }
])

const handleMultipleChange = (values) => {
  console.log('多选值变化:', values)
}

const handleRemoveTag = (value) => {
  ElMessage.info(`移除了标签: ${value}`)
}
</script>

<style scoped>
.multiple-select-demo {
  padding: 20px;
}
</style>
```

## 数据源配置

### 字典数据选择器
```vue
<template>
  <div class="dict-select-demo">
    <h3>字典数据选择器</h3>
    
    <div class="demo-item">
      <h4>远程字典数据</h4>
      <FuniSelect
        v-model="userStatus"
        url="/csops/dic/findDicByType"
        type-code="USER_STATUS"
        placeholder="请选择用户状态"
        clearable
      />
    </div>
    
    <div class="demo-item">
      <h4>本地缓存字典数据</h4>
      <FuniSelect
        v-model="departmentType"
        type-code="DEPARTMENT_TYPE"
        :is-local="true"
        storage-key="enumSource"
        placeholder="请选择部门类型"
        clearable
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const userStatus = ref('')
const departmentType = ref('')
</script>

<style scoped>
.dict-select-demo {
  padding: 20px;
}

.demo-item {
  margin-bottom: 20px;
}

.demo-item h4 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
```

### 自定义数据源
```vue
<template>
  <div class="custom-action-demo">
    <h3>自定义数据源</h3>
    <FuniSelect
      v-model="selectedUser"
      :action="loadUsers"
      placeholder="请选择用户"
      clearable
      filterable
      @change="handleUserChange"
    />
    <p v-if="selectedUser">选中用户: {{ selectedUser }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedUser = ref('')

// 模拟用户数据
const mockUsers = [
  { id: 1, name: '张三', email: '<EMAIL>', department: '技术部' },
  { id: 2, name: '李四', email: '<EMAIL>', department: '产品部' },
  { id: 3, name: '王五', email: '<EMAIL>', department: '运营部' },
  { id: 4, name: '赵六', email: '<EMAIL>', department: '设计部' }
]

const loadUsers = async (query = '') => {
  // 模拟API请求延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 根据查询条件过滤用户
  const filteredUsers = mockUsers.filter(user => 
    user.name.includes(query) || user.email.includes(query)
  )
  
  // 返回格式化的选项数据
  return filteredUsers.map(user => ({
    label: `${user.name} (${user.department})`,
    value: user.id
  }))
}

const handleUserChange = (value) => {
  const user = mockUsers.find(u => u.id === value)
  console.log('选中用户:', user)
}
</script>

<style scoped>
.custom-action-demo {
  padding: 20px;
}
</style>
```

## 高级功能

### 远程搜索选择器
```vue
<template>
  <div class="remote-search-demo">
    <h3>远程搜索选择器</h3>
    <FuniSelect
      v-model="selectedUser"
      :options="userOptions"
      remote
      filterable
      :remote-method="remoteSearch"
      :loading="loading"
      loading-text="搜索中..."
      no-match-text="无匹配数据"
      no-data-text="请输入关键词搜索"
      placeholder="请输入用户名或邮箱搜索"
      clearable
      reserve-keyword
      @change="handleRemoteChange"
    />
    <p v-if="selectedUser">选中用户ID: {{ selectedUser }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedUser = ref('')
const userOptions = ref([])
const loading = ref(false)

// 模拟远程用户数据
const remoteUsers = [
  { id: 1, name: '张三', email: '<EMAIL>' },
  { id: 2, name: '李四', email: '<EMAIL>' },
  { id: 3, name: '王五', email: '<EMAIL>' },
  { id: 4, name: '赵六', email: '<EMAIL>' },
  { id: 5, name: '钱七', email: '<EMAIL>' },
  { id: 6, name: '孙八', email: '<EMAIL>' }
]

const remoteSearch = async (query) => {
  if (!query) {
    userOptions.value = []
    return
  }
  
  loading.value = true
  
  try {
    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 根据查询条件过滤用户
    const filteredUsers = remoteUsers.filter(user => 
      user.name.includes(query) || user.email.includes(query)
    )
    
    // 更新选项数据
    userOptions.value = filteredUsers.map(user => ({
      label: `${user.name} (${user.email})`,
      value: user.id
    }))
  } catch (error) {
    console.error('搜索失败:', error)
    userOptions.value = []
  } finally {
    loading.value = false
  }
}

const handleRemoteChange = (value) => {
  const user = remoteUsers.find(u => u.id === value)
  console.log('选中用户:', user)
}
</script>

<style scoped>
.remote-search-demo {
  padding: 20px;
}
</style>
```

### 可创建新选项
```vue
<template>
  <div class="creatable-demo">
    <h3>可创建新选项</h3>
    <FuniSelect
      v-model="selectedTags"
      :options="tagOptions"
      multiple
      filterable
      allow-create
      default-first-option
      placeholder="请选择或输入新标签"
      clearable
      @change="handleTagChange"
    />
    <p>选中标签: {{ selectedTags }}</p>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const selectedTags = ref([])
const tagOptions = ref([
  { label: 'Vue.js', value: 'vue' },
  { label: 'React', value: 'react' },
  { label: 'Angular', value: 'angular' },
  { label: 'JavaScript', value: 'javascript' },
  { label: 'TypeScript', value: 'typescript' }
])

const handleTagChange = (values) => {
  console.log('标签变化:', values)
}

// 监听选中值变化，动态添加新创建的选项
watch(selectedTags, (newValues, oldValues) => {
  if (newValues && newValues.length > 0) {
    newValues.forEach(value => {
      // 检查是否是新创建的选项
      const exists = tagOptions.value.some(option => option.value === value)
      if (!exists) {
        // 添加新选项到选项列表
        tagOptions.value.push({
          label: value,
          value: value
        })
      }
    })
  }
}, { deep: true })
</script>

<style scoped>
.creatable-demo {
  padding: 20px;
}
</style>
```

## 表单集成示例

### 在FuniForm中使用
```vue
<template>
  <div class="form-integration-demo">
    <h3>表单集成示例</h3>
    <FuniForm
      v-model="formData"
      :schema="formSchema"
      :col="2"
      @submit="handleSubmit"
      @reset="handleReset"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const formData = ref({
  name: '',
  department: '',
  position: '',
  skills: [],
  status: 1
})

const formSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/csops/dic/findDicByType',
      typeCode: 'DEPARTMENT',
      placeholder: '请选择部门',
      clearable: true
    },
    rules: [{ required: true, message: '请选择部门', trigger: 'change' }]
  },
  {
    prop: 'position',
    label: '职位',
    component: 'FuniSelect',
    props: {
      options: [
        { label: '前端工程师', value: 'frontend' },
        { label: '后端工程师', value: 'backend' },
        { label: '测试工程师', value: 'tester' },
        { label: '产品经理', value: 'pm' },
        { label: '设计师', value: 'designer' }
      ],
      placeholder: '请选择职位',
      clearable: true
    },
    rules: [{ required: true, message: '请选择职位', trigger: 'change' }]
  },
  {
    prop: 'skills',
    label: '技能',
    component: 'FuniSelect',
    props: {
      options: [
        { label: 'JavaScript', value: 'js' },
        { label: 'TypeScript', value: 'ts' },
        { label: 'Vue.js', value: 'vue' },
        { label: 'React', value: 'react' },
        { label: 'Node.js', value: 'node' }
      ],
      multiple: true,
      collapseTags: true,
      multipleLimit: 5,
      placeholder: '请选择技能',
      clearable: true
    }
  },
  {
    prop: 'status',
    label: '状态',
    component: 'FuniSelect',
    props: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      placeholder: '请选择状态'
    },
    rules: [{ required: true, message: '请选择状态', trigger: 'change' }]
  }
])

const handleSubmit = (data) => {
  console.log('提交表单:', data)
  ElMessage.success('提交成功')
}

const handleReset = () => {
  ElMessage.info('表单已重置')
}
</script>

<style scoped>
.form-integration-demo {
  padding: 20px;
  max-width: 800px;
}
</style>
```

## 业务场景示例

### 用户管理选择器
```vue
<template>
  <div class="user-management-demo">
    <h3>用户管理场景</h3>

    <div class="demo-section">
      <h4>部门选择</h4>
      <FuniSelect
        v-model="selectedDepartment"
        :action="loadDepartments"
        placeholder="请选择部门"
        clearable
        @change="handleDepartmentChange"
      />
    </div>

    <div class="demo-section">
      <h4>用户选择（根据部门筛选）</h4>
      <FuniSelect
        v-model="selectedUser"
        :action="loadUsers"
        :disabled="!selectedDepartment"
        placeholder="请先选择部门"
        clearable
        filterable
      />
    </div>

    <div class="demo-section">
      <h4>角色分配</h4>
      <FuniSelect
        v-model="selectedRoles"
        :options="roleOptions"
        multiple
        collapse-tags
        placeholder="请选择角色"
        clearable
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const selectedDepartment = ref('')
const selectedUser = ref('')
const selectedRoles = ref([])

// 模拟部门数据
const departments = [
  { id: 1, name: '技术部' },
  { id: 2, name: '产品部' },
  { id: 3, name: '运营部' },
  { id: 4, name: '设计部' }
]

// 模拟用户数据
const users = {
  1: [
    { id: 101, name: '张三', position: '前端工程师' },
    { id: 102, name: '李四', position: '后端工程师' }
  ],
  2: [
    { id: 201, name: '王五', position: '产品经理' },
    { id: 202, name: '赵六', position: '产品助理' }
  ],
  3: [
    { id: 301, name: '钱七', position: '运营专员' },
    { id: 302, name: '孙八', position: '运营经理' }
  ],
  4: [
    { id: 401, name: '周九', position: 'UI设计师' },
    { id: 402, name: '吴十', position: 'UX设计师' }
  ]
}

const roleOptions = ref([
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'user' },
  { label: '访客', value: 'guest' },
  { label: '审核员', value: 'auditor' }
])

const loadDepartments = async () => {
  // 模拟API请求
  await new Promise(resolve => setTimeout(resolve, 200))

  return departments.map(dept => ({
    label: dept.name,
    value: dept.id
  }))
}

const loadUsers = async () => {
  if (!selectedDepartment.value) return []

  // 模拟API请求
  await new Promise(resolve => setTimeout(resolve, 300))

  const departmentUsers = users[selectedDepartment.value] || []
  return departmentUsers.map(user => ({
    label: `${user.name} - ${user.position}`,
    value: user.id
  }))
}

const handleDepartmentChange = (value) => {
  // 部门变化时清空用户选择
  selectedUser.value = ''
  console.log('选择部门:', value)
}

// 监听部门变化，重新加载用户列表
watch(selectedDepartment, () => {
  selectedUser.value = ''
})
</script>

<style scoped>
.user-management-demo {
  padding: 20px;
}

.demo-section {
  margin-bottom: 20px;
}

.demo-section h4 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
```

### 搜索筛选场景
```vue
<template>
  <div class="search-filter-demo">
    <h3>搜索筛选场景</h3>

    <div class="search-form">
      <div class="search-item">
        <label>状态:</label>
        <FuniSelect
          v-model="searchForm.status"
          :options="statusOptions"
          placeholder="全部状态"
          clearable
          style="width: 150px"
        />
      </div>

      <div class="search-item">
        <label>部门:</label>
        <FuniSelect
          v-model="searchForm.department"
          url="/csops/dic/findDicByType"
          type-code="DEPARTMENT"
          placeholder="全部部门"
          clearable
          style="width: 150px"
        />
      </div>

      <div class="search-item">
        <label>创建时间:</label>
        <FuniSelect
          v-model="searchForm.timeRange"
          :options="timeRangeOptions"
          placeholder="全部时间"
          clearable
          style="width: 150px"
        />
      </div>

      <div class="search-item">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </div>

    <div class="search-result">
      <p>搜索条件: {{ JSON.stringify(searchForm, null, 2) }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const searchForm = reactive({
  status: '',
  department: '',
  timeRange: ''
})

const statusOptions = ref([
  { label: '全部', value: '' },
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
  { label: '待审核', value: 2 }
])

const timeRangeOptions = ref([
  { label: '全部时间', value: '' },
  { label: '今天', value: 'today' },
  { label: '最近7天', value: 'week' },
  { label: '最近30天', value: 'month' },
  { label: '最近90天', value: 'quarter' }
])

const handleSearch = () => {
  console.log('执行搜索:', searchForm)
  ElMessage.success('搜索完成')
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  ElMessage.info('搜索条件已重置')
}
</script>

<style scoped>
.search-filter-demo {
  padding: 20px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.search-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.search-result {
  padding: 15px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.search-result p {
  margin: 0;
  font-family: monospace;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
}
</style>
```
