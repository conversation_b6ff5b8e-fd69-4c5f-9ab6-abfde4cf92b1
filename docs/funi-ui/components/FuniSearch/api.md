# FuniSearch API文档

## 组件概述

FuniSearch是基于ElementPlus表单组件封装的搜索组件，支持多种搜索控件、动态表单、条件组合等功能，适用于数据查询和过滤场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| modelValue | Object | {} | - | 搜索条件绑定值 |
| schema | Array | [] | ✅ | 搜索项配置数组 |
| inline | Boolean | true | - | 是否行内布局 |
| labelWidth | String | '100px' | - | 标签宽度 |
| labelPosition | String | 'right' | - | 标签位置 |
| size | String | 'default' | - | 组件尺寸 |
| disabled | Boolean | false | - | 是否禁用 |
| showReset | Boolean | true | - | 是否显示重置按钮 |
| showSearch | Boolean | true | - | 是否显示搜索按钮 |
| showExpand | Boolean | false | - | 是否显示展开/收起按钮 |
| defaultExpanded | Boolean | true | - | 默认是否展开 |
| expandCount | Number | 3 | - | 收起时显示的搜索项数量 |
| searchText | String | '搜索' | - | 搜索按钮文本 |
| resetText | String | '重置' | - | 重置按钮文本 |
| expandText | String | '展开' | - | 展开按钮文本 |
| collapseText | String | '收起' | - | 收起按钮文本 |
| searchLoading | Boolean | false | - | 搜索按钮加载状态 |
| autoSearch | Boolean | false | - | 是否自动搜索 |
| searchDelay | Number | 300 | - | 自动搜索延迟时间（毫秒） |
| validateOnSearch | Boolean | false | - | 搜索时是否验证表单 |
| preserveOnReset | Array | [] | - | 重置时保留的字段 |
| gutter | Number | 20 | - | 栅格间隔 |
| responsive | Boolean | true | - | 是否响应式布局 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: Object | 搜索条件更新事件 | 搜索条件变化时 |
| search | searchParams: Object | 搜索事件 | 点击搜索按钮或自动搜索时 |
| reset | - | 重置事件 | 点击重置按钮时 |
| change | (value: any, prop: string) | 搜索项变化事件 | 单个搜索项值变化时 |
| expand | expanded: Boolean | 展开状态变化事件 | 展开/收起状态变化时 |
| validate | (prop: string, isValid: boolean, message: string) | 验证事件 | 表单项验证时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| search | - | void | 执行搜索 |
| reset | (props?: string[]) | void | 重置搜索条件 |
| validate | (callback?: Function) | Promise\<boolean\> | 验证搜索表单 |
| validateField | (props: string \| string[], callback?: Function) | Promise\<boolean\> | 验证指定字段 |
| clearValidate | (props?: string \| string[]) | void | 清除验证信息 |
| getSearchParams | - | Object | 获取搜索参数 |
| setSearchParams | (params: Object) | void | 设置搜索参数 |
| expand | - | void | 展开搜索表单 |
| collapse | - | void | 收起搜索表单 |
| toggleExpand | - | void | 切换展开状态 |

## schema配置结构

```typescript
interface SearchSchemaItem {
  // 基础配置
  prop: string;                    // 字段名（必填）
  label: string;                   // 字段标签（必填）
  component: string;               // 组件类型（必填）
  
  // 组件配置
  props?: Record<string, any>;     // 组件属性
  options?: OptionItem[];          // 选项数据（select、radio、checkbox等）
  
  // 布局配置
  span?: number;                   // 栅格占据列数（1-24）
  offset?: number;                 // 栅格左侧间隔格数
  
  // 显示控制
  show?: boolean | ComputedRef<boolean>; // 是否显示
  
  // 验证规则
  rules?: FormRule[];              // 字段验证规则
  
  // 搜索配置
  searchable?: boolean;            // 是否参与搜索，默认true
  resetValue?: any;                // 重置时的默认值
  
  // 其他配置
  [key: string]: any;              // 扩展配置
}
```

## 支持的组件类型

### 输入类组件
- **el-input**: 输入框
- **el-textarea**: 文本域
- **el-input-number**: 数字输入框
- **el-autocomplete**: 自动完成输入框

### 选择类组件
- **el-select**: 选择器
- **el-cascader**: 级联选择器
- **el-tree-select**: 树形选择器
- **el-radio-group**: 单选框组
- **el-checkbox-group**: 多选框组

### 日期时间类组件
- **el-date-picker**: 日期选择器
- **el-time-picker**: 时间选择器
- **el-datetime-picker**: 日期时间选择器

### FuniUI组件
- **FuniSelect**: Funi选择器
- **FuniTreeSelect**: Funi树形选择器
- **FuniRUOC**: 角色用户组织选择器
- **FuniRegion**: 地区选择器

## 使用示例

### 基础搜索表单
```vue
<template>
  <FuniSearch
    v-model="searchParams"
    :schema="searchSchema"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const searchParams = ref({
  keyword: '',
  status: '',
  dateRange: []
})

const searchSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: {
      placeholder: '请输入用户名或邮箱',
      clearable: true
    },
    span: 8
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: {
      placeholder: '请选择状态',
      clearable: true
    },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    span: 6
  },
  {
    prop: 'dateRange',
    label: '创建时间',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 10
  }
])

const handleSearch = (params) => {
  console.log('搜索参数:', params)
  // 执行搜索逻辑
}

const handleReset = () => {
  console.log('重置搜索条件')
}
</script>
```

### 可展开收起的搜索表单
```vue
<template>
  <FuniSearch
    v-model="searchParams"
    :schema="expandableSchema"
    show-expand
    :expand-count="3"
    :default-expanded="false"
    @search="handleSearch"
    @expand="handleExpand"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const searchParams = ref({
  username: '',
  email: '',
  phone: '',
  department: '',
  role: '',
  status: '',
  createTimeStart: '',
  createTimeEnd: '',
  lastLoginStart: '',
  lastLoginEnd: ''
})

const expandableSchema = reactive([
  {
    prop: 'username',
    label: '用户名',
    component: 'el-input',
    props: { placeholder: '请输入用户名', clearable: true },
    span: 6
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱', clearable: true },
    span: 6
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号', clearable: true },
    span: 6
  },
  // 以下字段在收起时隐藏
  {
    prop: 'department',
    label: '部门',
    component: 'FuniTreeSelect',
    props: { 
      placeholder: '请选择部门',
      clearable: true,
      api: '/api/departments'
    },
    span: 6
  },
  {
    prop: 'role',
    label: '角色',
    component: 'FuniRUOC',
    props: { 
      type: 'role',
      placeholder: '请选择角色',
      clearable: true
    },
    span: 6
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    span: 6
  },
  {
    prop: 'createTimeStart',
    label: '创建时间从',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '开始日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 6
  },
  {
    prop: 'createTimeEnd',
    label: '创建时间至',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 6
  }
])

const handleSearch = (params) => {
  console.log('搜索参数:', params)
}

const handleExpand = (expanded) => {
  console.log('展开状态:', expanded)
}
</script>
```

### 自动搜索表单
```vue
<template>
  <FuniSearch
    v-model="searchParams"
    :schema="autoSearchSchema"
    auto-search
    :search-delay="500"
    :show-search="false"
    @search="handleAutoSearch"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const searchParams = ref({
  keyword: '',
  category: '',
  priceRange: []
})

const autoSearchSchema = reactive([
  {
    prop: 'keyword',
    label: '商品名称',
    component: 'el-input',
    props: {
      placeholder: '请输入商品名称',
      clearable: true,
      prefixIcon: 'Search'
    },
    span: 8
  },
  {
    prop: 'category',
    label: '商品分类',
    component: 'el-select',
    props: {
      placeholder: '请选择分类',
      clearable: true
    },
    options: [
      { label: '电子产品', value: 'electronics' },
      { label: '服装', value: 'clothing' },
      { label: '食品', value: 'food' },
      { label: '图书', value: 'books' }
    ],
    span: 6
  },
  {
    prop: 'priceRange',
    label: '价格区间',
    component: 'el-slider',
    props: {
      range: true,
      min: 0,
      max: 10000,
      step: 100,
      showStops: false,
      showTooltip: true,
      formatTooltip: (value) => `¥${value}`
    },
    span: 10
  }
])

const handleAutoSearch = (params) => {
  console.log('自动搜索:', params)
  // 执行搜索逻辑，通常是调用API
}
</script>
```

### 带验证的搜索表单
```vue
<template>
  <FuniSearch
    v-model="searchParams"
    :schema="validationSchema"
    validate-on-search
    @search="handleValidatedSearch"
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const searchParams = ref({
  startDate: '',
  endDate: '',
  minAmount: null,
  maxAmount: null
})

const validationSchema = reactive([
  {
    prop: 'startDate',
    label: '开始日期',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '请选择开始日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    rules: [
      { required: true, message: '请选择开始日期', trigger: 'change' }
    ],
    span: 6
  },
  {
    prop: 'endDate',
    label: '结束日期',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '请选择结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    rules: [
      { required: true, message: '请选择结束日期', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (value && searchParams.value.startDate && value < searchParams.value.startDate) {
            callback(new Error('结束日期不能早于开始日期'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
    span: 6
  },
  {
    prop: 'minAmount',
    label: '最小金额',
    component: 'el-input-number',
    props: {
      min: 0,
      precision: 2,
      placeholder: '请输入最小金额'
    },
    span: 6
  },
  {
    prop: 'maxAmount',
    label: '最大金额',
    component: 'el-input-number',
    props: {
      min: 0,
      precision: 2,
      placeholder: '请输入最大金额'
    },
    rules: [
      {
        validator: (rule, value, callback) => {
          if (value && searchParams.value.minAmount && value < searchParams.value.minAmount) {
            callback(new Error('最大金额不能小于最小金额'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    span: 6
  }
])

const handleValidatedSearch = (params) => {
  console.log('验证通过，执行搜索:', params)
}

const handleValidate = (prop, isValid, message) => {
  console.log('字段验证:', prop, isValid, message)
}
</script>
```

### 动态搜索表单
```vue
<template>
  <div>
    <el-radio-group v-model="searchType" @change="handleSearchTypeChange">
      <el-radio-button label="user">用户搜索</el-radio-button>
      <el-radio-button label="order">订单搜索</el-radio-button>
      <el-radio-button label="product">商品搜索</el-radio-button>
    </el-radio-group>
    
    <FuniSearch
      v-model="searchParams"
      :schema="dynamicSchema"
      @search="handleSearch"
      style="margin-top: 16px;"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const searchType = ref('user')
const searchParams = ref({})

const userSchema = [
  {
    prop: 'username',
    label: '用户名',
    component: 'el-input',
    props: { placeholder: '请输入用户名', clearable: true },
    span: 8
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱', clearable: true },
    span: 8
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    span: 8
  }
]

const orderSchema = [
  {
    prop: 'orderNo',
    label: '订单号',
    component: 'el-input',
    props: { placeholder: '请输入订单号', clearable: true },
    span: 8
  },
  {
    prop: 'status',
    label: '订单状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: [
      { label: '待付款', value: 'pending' },
      { label: '已付款', value: 'paid' },
      { label: '已发货', value: 'shipped' },
      { label: '已完成', value: 'completed' }
    ],
    span: 8
  },
  {
    prop: 'dateRange',
    label: '下单时间',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期'
    },
    span: 8
  }
]

const productSchema = [
  {
    prop: 'productName',
    label: '商品名称',
    component: 'el-input',
    props: { placeholder: '请输入商品名称', clearable: true },
    span: 8
  },
  {
    prop: 'category',
    label: '商品分类',
    component: 'el-select',
    props: { placeholder: '请选择分类', clearable: true },
    options: [
      { label: '电子产品', value: 'electronics' },
      { label: '服装', value: 'clothing' },
      { label: '食品', value: 'food' }
    ],
    span: 8
  },
  {
    prop: 'priceRange',
    label: '价格区间',
    component: 'el-input-number',
    props: { placeholder: '最低价格', min: 0 },
    span: 4
  },
  {
    prop: 'priceRangeMax',
    label: '至',
    component: 'el-input-number',
    props: { placeholder: '最高价格', min: 0 },
    span: 4
  }
]

const dynamicSchema = computed(() => {
  switch (searchType.value) {
    case 'user':
      return userSchema
    case 'order':
      return orderSchema
    case 'product':
      return productSchema
    default:
      return []
  }
})

const handleSearchTypeChange = () => {
  // 切换搜索类型时重置搜索参数
  searchParams.value = {}
}

const handleSearch = (params) => {
  console.log(`${searchType.value}搜索参数:`, params)
}
</script>
```

## 响应式布局

### 栅格系统
FuniSearch使用24栅格系统进行布局：

```javascript
// 不同屏幕尺寸的响应式配置
const responsiveSchema = [
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    span: 24,        // 默认占满一行
    xs: 24,          // 超小屏幕
    sm: 12,          // 小屏幕
    md: 8,           // 中等屏幕
    lg: 6,           // 大屏幕
    xl: 6            // 超大屏幕
  }
]
```

## 注意事项

### 1. 性能优化
- 使用防抖避免频繁搜索
- 合理设置自动搜索延迟时间
- 避免在schema中使用复杂的响应式对象

### 2. 用户体验
- 提供清晰的搜索提示
- 合理设置默认值
- 支持键盘快捷键操作

### 3. 数据处理
- 统一搜索参数格式
- 处理空值和特殊字符
- 提供参数转换功能

### 4. 表单验证
- 合理设置验证规则
- 提供友好的错误提示
- 支持异步验证

## 常见问题

### Q: 如何实现联动搜索？
A: 监听某个字段的change事件，动态更新其他字段的选项或显示状态

### Q: 如何自定义搜索按钮？
A: 使用插槽自定义按钮区域，或者通过CSS覆盖默认样式

### Q: 如何实现搜索历史？
A: 监听search事件，将搜索参数保存到localStorage或服务端

### Q: 如何处理复杂的搜索逻辑？
A: 在search事件中进行参数处理和转换，然后调用相应的API接口
