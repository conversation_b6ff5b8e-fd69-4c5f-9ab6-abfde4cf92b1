# FuniSearch ElementPlus API支持

## 基础组件

FuniSearch基于ElementPlus的以下组件进行封装：
- **el-form** - 搜索表单容器
- **el-form-item** - 搜索表单项
- **el-row/el-col** - 栅格布局系统
- **el-button** - 搜索和重置按钮
- **el-collapse** - 展开收起功能
- **el-card** - 搜索区域容器

## 支持的ElementPlus API

### el-form API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| model | Object | 表单数据对象 | 自动处理 |
| rules | Object | 表单验证规则 | v-bind透传 |
| inline | Boolean | 行内表单模式 | v-bind透传 |
| label-position | String | 标签位置 | v-bind透传 |
| label-width | String | 标签宽度 | v-bind透传 |
| label-suffix | String | 标签后缀 | v-bind透传 |
| hide-required-asterisk | Boolean | 隐藏必填星号 | v-bind透传 |
| show-message | Boolean | 显示校验信息 | v-bind透传 |
| inline-message | Boolean | 行内显示校验信息 | v-bind透传 |
| status-icon | Boolean | 显示校验状态图标 | v-bind透传 |
| validate-on-rule-change | Boolean | 规则改变时触发校验 | v-bind透传 |
| size | String | 组件尺寸 | v-bind透传 |
| disabled | Boolean | 是否禁用 | v-bind透传 |

### el-form Events透传

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| validate | prop, isValid, message | 表单项校验事件 | @validate |

### el-form Methods透传

| 方法名 | 参数 | 返回值 | 说明 | 透传方式 |
|--------|------|--------|------|---------|
| validate | callback | Promise | 验证整个表单 | ref调用 |
| validateField | props, callback | Promise | 验证指定字段 | ref调用 |
| resetFields | props | void | 重置表单字段 | ref调用 |
| clearValidate | props | void | 清除验证信息 | ref调用 |

### el-form-item API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| prop | String | 字段名 | 通过schema配置 |
| label | String | 标签文本 | 通过schema配置 |
| label-width | String | 标签宽度 | 通过schema配置 |
| required | Boolean | 是否必填 | 通过schema配置 |
| rules | Object/Array | 验证规则 | 通过schema配置 |
| error | String | 表单域验证错误信息 | 通过schema配置 |
| show-message | Boolean | 显示校验错误信息 | 通过schema配置 |
| inline-message | Boolean | 行内显示校验信息 | 通过schema配置 |
| size | String | 组件尺寸 | 通过schema配置 |

### el-row/el-col API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| gutter | Number | 栅格间隔 | 通过gutter属性 |
| type | String | 布局模式 | 通过layout配置 |
| justify | String | 水平排列方式 | 通过layout配置 |
| align | String | 垂直排列方式 | 通过layout配置 |
| tag | String | 自定义元素标签 | 通过layout配置 |
| span | Number | 栅格占据列数 | 通过schema配置 |
| offset | Number | 栅格左侧间隔格数 | 通过schema配置 |
| push | Number | 栅格向右移动格数 | 通过schema配置 |
| pull | Number | 栅格向左移动格数 | 通过schema配置 |
| xs | Number/Object | 超小屏幕配置 | 通过schema配置 |
| sm | Number/Object | 小屏幕配置 | 通过schema配置 |
| md | Number/Object | 中等屏幕配置 | 通过schema配置 |
| lg | Number/Object | 大屏幕配置 | 通过schema配置 |
| xl | Number/Object | 超大屏幕配置 | 通过schema配置 |

### el-button API透传（搜索/重置按钮）

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| type | String | 按钮类型 | 通过searchButtonProps/resetButtonProps |
| size | String | 按钮尺寸 | 通过searchButtonProps/resetButtonProps |
| plain | Boolean | 朴素按钮 | 通过searchButtonProps/resetButtonProps |
| round | Boolean | 圆角按钮 | 通过searchButtonProps/resetButtonProps |
| circle | Boolean | 圆形按钮 | 通过searchButtonProps/resetButtonProps |
| loading | Boolean | 加载状态 | 通过searchButtonProps/resetButtonProps |
| disabled | Boolean | 禁用状态 | 通过searchButtonProps/resetButtonProps |
| icon | String | 图标 | 通过searchButtonProps/resetButtonProps |
| autofocus | Boolean | 自动聚焦 | 通过searchButtonProps/resetButtonProps |
| native-type | String | 原生type属性 | 通过searchButtonProps/resetButtonProps |

### el-collapse API透传（展开收起功能）

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| model-value | String/Array | 当前激活的面板 | 通过expandConfig配置 |
| accordion | Boolean | 手风琴模式 | 通过expandConfig配置 |

### el-collapse Events透传

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| change | activeNames | 当前激活面板变化 | @expand-change |

## 使用方式

### 基础搜索使用
```vue
<template>
  <FuniSearch
    v-model="searchData"
    :schema="searchSchema"
    
    <!-- ElementPlus el-form 属性透传 -->
    label-width="80px"
    label-position="right"
    :inline="true"
    
    <!-- 按钮配置 -->
    :search-button-props="{ type: 'primary', icon: 'Search' }"
    :reset-button-props="{ icon: 'Refresh' }"
    
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const searchData = ref({
  keyword: '',
  status: '',
  dateRange: []
})

const searchSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { 
      placeholder: '请输入关键字',
      clearable: true 
    }
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { 
      placeholder: '请选择状态',
      clearable: true 
    },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    }
  }
])

const handleSearch = (searchParams) => {
  console.log('搜索参数:', searchParams)
}

const handleReset = () => {
  console.log('重置搜索')
}
</script>
```

### 展开收起功能使用
```vue
<template>
  <FuniSearch
    v-model="searchData"
    :schema="expandSchema"
    
    <!-- 展开收起配置 -->
    :expand-config="expandConfig"
    
    <!-- ElementPlus el-collapse 事件透传 -->
    @expand-change="handleExpandChange"
  />
</template>

<script setup>
const expandSchema = reactive([
  // 基础搜索字段（始终显示）
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    expand: false // 不在展开区域
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    options: statusOptions,
    expand: false
  },
  
  // 高级搜索字段（展开时显示）
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id'
    },
    expand: true // 在展开区域
  },
  {
    prop: 'createUser',
    label: '创建人',
    component: 'FuniSelect',
    props: {
      url: '/api/users',
      labelKey: 'name',
      valueKey: 'id'
    },
    expand: true
  },
  {
    prop: 'tags',
    label: '标签',
    component: 'el-select',
    props: { multiple: true },
    options: tagOptions,
    expand: true
  }
])

const expandConfig = reactive({
  enabled: true,
  defaultExpanded: false,
  expandText: '展开',
  collapseText: '收起',
  showCount: true // 显示展开字段数量
})

const handleExpandChange = (expanded) => {
  console.log('展开状态变化:', expanded)
}
</script>
```

### 响应式布局使用
```vue
<template>
  <FuniSearch
    v-model="searchData"
    :schema="responsiveSchema"
    
    <!-- ElementPlus el-row 属性透传 -->
    :gutter="20"
    justify="start"
    align="top"
  />
</template>

<script setup>
const responsiveSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    // ElementPlus el-col 响应式配置
    span: 24,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    xl: 6
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    span: 24,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    xl: 6
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    component: 'el-date-picker',
    props: { type: 'daterange' },
    span: 24,
    xs: 24,
    sm: 24,
    md: 8,
    lg: 12,
    xl: 12
  }
])
</script>
```

### 搜索表单验证
```vue
<template>
  <FuniSearch
    ref="searchRef"
    v-model="searchData"
    :schema="validationSchema"
    :rules="searchRules"
    
    <!-- ElementPlus 验证相关属性 -->
    :validate-on-rule-change="true"
    :show-message="true"
    :inline-message="false"
    
    @search="handleValidatedSearch"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const searchRef = ref()

const searchData = ref({
  startDate: '',
  endDate: '',
  amount: '',
  email: ''
})

const validationSchema = reactive([
  {
    prop: 'startDate',
    label: '开始日期',
    component: 'el-date-picker',
    props: { type: 'date' }
  },
  {
    prop: 'endDate',
    label: '结束日期',
    component: 'el-date-picker',
    props: { type: 'date' }
  },
  {
    prop: 'amount',
    label: '金额范围',
    component: 'el-input-number',
    props: { min: 0 }
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input'
  }
])

// ElementPlus 验证规则
const searchRules = reactive({
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value && searchData.value.startDate && value <= searchData.value.startDate) {
          callback(new Error('结束日期必须大于开始日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  amount: [
    { type: 'number', min: 0, message: '金额不能小于0', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
})

const handleValidatedSearch = async (searchParams) => {
  try {
    // 调用ElementPlus el-form验证方法
    const valid = await searchRef.value.validate()
    if (valid) {
      console.log('搜索参数验证通过:', searchParams)
      // 执行搜索逻辑
    }
  } catch (error) {
    console.log('搜索参数验证失败:', error)
  }
}
</script>
```

### 搜索方法调用
```vue
<template>
  <FuniSearch
    ref="searchRef"
    v-model="searchData"
    :schema="schema"
  />
  
  <div class="search-actions">
    <el-button @click="validateSearch">验证搜索条件</el-button>
    <el-button @click="resetSearch">重置搜索</el-button>
    <el-button @click="clearValidation">清除验证</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const searchRef = ref()

// 调用ElementPlus el-form方法
const validateSearch = async () => {
  try {
    const valid = await searchRef.value.validate()
    console.log('搜索条件验证结果:', valid)
  } catch (error) {
    console.log('搜索条件验证失败:', error)
  }
}

const resetSearch = () => {
  searchRef.value.resetFields()
}

const clearValidation = () => {
  searchRef.value.clearValidate()
}
</script>
```

## 注意事项

### 1. API透传机制
- 所有ElementPlus el-form的属性都通过v-bind自动透传
- 搜索项相关API通过schema配置传递
- 布局相关API通过gutter属性或直接透传
- 按钮相关API通过searchButtonProps和resetButtonProps传递
- 展开收起功能通过expandConfig配置传递

### 2. 方法调用
- 所有ElementPlus el-form的方法都可以通过组件ref调用
- 方法调用方式与原生el-form完全一致
- 支持Promise和callback两种调用方式

### 3. 事件处理
- 所有ElementPlus相关事件都可以正常监听
- 事件参数与原生ElementPlus组件一致
- 组件内部处理不会影响外部事件监听

### 4. 搜索字段配置
- 完全支持ElementPlus表单组件的所有配置
- 通过schema数组统一管理搜索字段
- 支持动态字段配置和条件显示

### 5. 展开收起功能
- 基于ElementPlus el-collapse组件实现
- 支持自定义展开文本和图标
- 可以配置默认展开状态

### 6. 响应式支持
- 完全支持ElementPlus的响应式栅格系统
- 可以为每个搜索字段单独配置响应式参数
- 支持断点配置和自适应布局

### 7. 兼容性
- 完全兼容ElementPlus el-form的所有功能
- 新增功能不会影响原有ElementPlus API的使用
- 可以无缝迁移现有的搜索表单代码

### 8. 性能考虑
- 合理设置搜索字段数量，避免过多字段影响用户体验
- 使用防抖处理频繁的搜索操作
- 避免在schema中使用复杂的响应式对象
