# FuniSearch 配置结构

## 基础配置结构

```typescript
interface FuniSearchConfig {
  // 数据绑定
  modelValue: Record<string, any>;  // 搜索条件绑定值（必填）
  schema: SearchSchemaItem[];       // 搜索项配置数组（必填）
  
  // 布局配置
  inline?: boolean;                 // 是否行内布局，默认true
  labelWidth?: string;              // 标签宽度，默认'100px'
  labelPosition?: 'left' | 'right' | 'top'; // 标签位置，默认'right'
  size?: 'large' | 'default' | 'small'; // 组件尺寸，默认'default'
  gutter?: number;                  // 栅格间隔，默认20
  
  // 功能配置
  showReset?: boolean;              // 是否显示重置按钮，默认true
  showSearch?: boolean;             // 是否显示搜索按钮，默认true
  showExpand?: boolean;             // 是否显示展开/收起按钮，默认false
  defaultExpanded?: boolean;        // 默认是否展开，默认true
  expandCount?: number;             // 收起时显示的搜索项数量，默认3
  
  // 按钮文本配置
  searchText?: string;              // 搜索按钮文本，默认'搜索'
  resetText?: string;               // 重置按钮文本，默认'重置'
  expandText?: string;              // 展开按钮文本，默认'展开'
  collapseText?: string;            // 收起按钮文本，默认'收起'
  
  // 行为配置
  searchLoading?: boolean;          // 搜索按钮加载状态，默认false
  autoSearch?: boolean;             // 是否自动搜索，默认false
  searchDelay?: number;             // 自动搜索延迟时间（毫秒），默认300
  validateOnSearch?: boolean;       // 搜索时是否验证表单，默认false
  preserveOnReset?: string[];       // 重置时保留的字段，默认[]
  
  // 状态配置
  disabled?: boolean;               // 是否禁用，默认false
  
  // 响应式配置
  responsive?: boolean;             // 是否响应式布局，默认true
}
```

## 搜索项配置结构

```typescript
interface SearchSchemaItem {
  // 基础配置
  prop: string;                     // 字段名（必填）
  label: string;                    // 字段标签（必填）
  component: ComponentType;         // 组件类型（必填）
  
  // 组件配置
  props?: Record<string, any>;      // 组件属性
  options?: OptionItem[];           // 选项数据（select、radio、checkbox等）
  
  // 布局配置
  span?: number;                    // 栅格占据列数（1-24），默认6
  offset?: number;                  // 栅格左侧间隔格数，默认0
  xs?: number | ResponsiveConfig;   // 超小屏幕配置
  sm?: number | ResponsiveConfig;   // 小屏幕配置
  md?: number | ResponsiveConfig;   // 中等屏幕配置
  lg?: number | ResponsiveConfig;   // 大屏幕配置
  xl?: number | ResponsiveConfig;   // 超大屏幕配置
  
  // 显示控制
  show?: boolean | ComputedRef<boolean> | ShowCondition; // 是否显示
  
  // 验证规则
  rules?: FormRule[];               // 字段验证规则
  
  // 搜索配置
  searchable?: boolean;             // 是否参与搜索，默认true
  resetValue?: any;                 // 重置时的默认值
  
  // 联动配置
  dependencies?: string[];          // 依赖的字段
  watch?: WatchConfig;              // 监听配置
  
  // 样式配置
  class?: string;                   // 自定义类名
  style?: object;                   // 自定义样式
  
  // 其他配置
  [key: string]: any;               // 扩展配置
}

type ComponentType = 
  // 输入类组件
  | 'el-input'
  | 'el-textarea'
  | 'el-input-number'
  | 'el-autocomplete'
  // 选择类组件
  | 'el-select'
  | 'el-cascader'
  | 'el-tree-select'
  | 'el-radio-group'
  | 'el-checkbox-group'
  // 日期时间类组件
  | 'el-date-picker'
  | 'el-time-picker'
  | 'el-datetime-picker'
  // 其他组件
  | 'el-switch'
  | 'el-slider'
  | 'el-rate'
  | 'el-color-picker'
  // FuniUI组件
  | 'FuniSelect'
  | 'FuniTreeSelect'
  | 'FuniRUOC'
  | 'FuniRegion'
  | 'FuniMoneyInput'
  | 'FuniIconSelect';

interface OptionItem {
  label: string;                    // 显示文本
  value: any;                       // 选项值
  disabled?: boolean;               // 是否禁用
  children?: OptionItem[];          // 子选项（级联选择器）
  [key: string]: any;               // 其他属性
}

interface ResponsiveConfig {
  span?: number;                    // 栅格占据列数
  offset?: number;                  // 栅格左侧间隔格数
}

type ShowCondition = (formData: Record<string, any>) => boolean;

interface WatchConfig {
  handler: (newVal: any, oldVal: any, formData: Record<string, any>) => void;
  immediate?: boolean;              // 是否立即执行
  deep?: boolean;                   // 是否深度监听
}

interface FormRule {
  required?: boolean;               // 是否必填
  message?: string;                 // 错误信息
  trigger?: 'blur' | 'change';      // 触发方式
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email';
  min?: number;                     // 最小长度/值
  max?: number;                     // 最大长度/值
  pattern?: RegExp;                 // 正则表达式
  validator?: (rule: any, value: any, callback: Function) => void; // 自定义验证函数
}
```

## 常用组件配置

### 输入类组件配置

#### el-input 配置
```typescript
interface InputConfig extends SearchSchemaItem {
  component: 'el-input';
  props?: {
    type?: 'text' | 'textarea' | 'password' | 'number';
    placeholder?: string;
    clearable?: boolean;
    showPassword?: boolean;
    disabled?: boolean;
    readonly?: boolean;
    maxlength?: number;
    minlength?: number;
    showWordLimit?: boolean;
    prefixIcon?: string;
    suffixIcon?: string;
    rows?: number;                  // textarea时的行数
    autosize?: boolean | { minRows?: number; maxRows?: number };
    resize?: 'none' | 'both' | 'horizontal' | 'vertical';
  };
}
```

#### el-input-number 配置
```typescript
interface InputNumberConfig extends SearchSchemaItem {
  component: 'el-input-number';
  props?: {
    min?: number;
    max?: number;
    step?: number;
    stepStrictly?: boolean;
    precision?: number;
    size?: 'large' | 'default' | 'small';
    disabled?: boolean;
    controls?: boolean;
    controlsPosition?: 'right' | '';
    placeholder?: string;
  };
}
```

### 选择类组件配置

#### el-select 配置
```typescript
interface SelectConfig extends SearchSchemaItem {
  component: 'el-select';
  options: OptionItem[];            // 必填
  props?: {
    multiple?: boolean;
    disabled?: boolean;
    valueKey?: string;
    size?: 'large' | 'default' | 'small';
    clearable?: boolean;
    collapseTags?: boolean;
    collapseTagsTooltip?: boolean;
    multipleLimit?: number;
    placeholder?: string;
    filterable?: boolean;
    allowCreate?: boolean;
    filterMethod?: Function;
    remote?: boolean;
    remoteMethod?: Function;
    loading?: boolean;
    loadingText?: string;
    noMatchText?: string;
    noDataText?: string;
    popperClass?: string;
    reserveKeyword?: boolean;
    defaultFirstOption?: boolean;
    teleported?: boolean;
    persistent?: boolean;
    automaticDropdown?: boolean;
    clearIcon?: string;
    fitInputWidth?: boolean;
    suffixIcon?: string;
    tagType?: 'success' | 'info' | 'warning' | 'danger';
    validateEvent?: boolean;
  };
}
```

#### el-cascader 配置
```typescript
interface CascaderConfig extends SearchSchemaItem {
  component: 'el-cascader';
  options: OptionItem[];            // 必填，树形结构
  props?: {
    expandTrigger?: 'click' | 'hover';
    multiple?: boolean;
    checkStrictly?: boolean;
    emitPath?: boolean;
    lazy?: boolean;
    lazyLoad?: Function;
    value?: string;
    label?: string;
    children?: string;
    disabled?: string;
    leaf?: string;
    separator?: string;
    showAllLevels?: boolean;
    collapseTags?: boolean;
    collapseTagsTooltip?: boolean;
    debounce?: number;
    beforeFilter?: Function;
    popperClass?: string;
    teleported?: boolean;
    tagType?: 'success' | 'info' | 'warning' | 'danger';
    filterable?: boolean;
    placeholder?: string;
    disabled?: boolean;
    clearable?: boolean;
    size?: 'large' | 'default' | 'small';
  };
}
```

### 日期时间类组件配置

#### el-date-picker 配置
```typescript
interface DatePickerConfig extends SearchSchemaItem {
  component: 'el-date-picker';
  props?: {
    type?: 'year' | 'month' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange';
    readonly?: boolean;
    disabled?: boolean;
    editable?: boolean;
    clearable?: boolean;
    size?: 'large' | 'default' | 'small';
    placeholder?: string;
    startPlaceholder?: string;
    endPlaceholder?: string;
    format?: string;
    valueFormat?: string;
    popperClass?: string;
    rangeSeparator?: string;
    defaultValue?: Date | [Date, Date];
    defaultTime?: Date | [Date, Date];
    disabledDate?: Function;
    shortcuts?: Array<{ text: string; value: Date | Function }>;
    cellClassName?: Function;
    teleported?: boolean;
    unlinkPanels?: boolean;
  };
}
```

### FuniUI组件配置

#### FuniRUOC 配置
```typescript
interface FuniRUOCConfig extends SearchSchemaItem {
  component: 'FuniRUOC';
  props?: {
    type: 'role' | 'user' | 'org' | 'company'; // 必填
    multiple?: boolean;
    placeholder?: string;
    clearable?: boolean;
    filterable?: boolean;
    remote?: boolean;
    remoteMethod?: Function;
    api?: string | object;
    params?: object;
    props?: object;
    transform?: Function;
    size?: 'large' | 'default' | 'small';
    disabled?: boolean;
  };
}
```

#### FuniTreeSelect 配置
```typescript
interface FuniTreeSelectConfig extends SearchSchemaItem {
  component: 'FuniTreeSelect';
  props?: {
    data?: any[];
    api?: string | Function;
    props?: {
      label?: string;
      value?: string;
      children?: string;
      disabled?: string;
      isLeaf?: string;
    };
    multiple?: boolean;
    showCheckbox?: boolean;
    checkStrictly?: boolean;
    defaultExpandAll?: boolean;
    lazy?: boolean;
    load?: Function;
    filterable?: boolean;
    placeholder?: string;
    clearable?: boolean;
    size?: 'large' | 'default' | 'small';
    disabled?: boolean;
  };
}
```

## 响应式布局配置

```typescript
interface ResponsiveLayoutConfig {
  // 栅格系统（24栅格）
  span?: number;                    // 默认占据列数
  
  // 响应式断点
  xs?: number | ResponsiveConfig;   // <768px
  sm?: number | ResponsiveConfig;   // ≥768px
  md?: number | ResponsiveConfig;   // ≥992px
  lg?: number | ResponsiveConfig;   // ≥1200px
  xl?: number | ResponsiveConfig;   // ≥1920px
  
  // 间隔配置
  gutter?: number;                  // 栅格间隔
  offset?: number;                  // 栅格偏移
}

// 常用响应式配置示例
const responsiveConfig = {
  span: 24,        // 默认占满一行
  xs: 24,          // 超小屏幕占满一行
  sm: 12,          // 小屏幕占半行
  md: 8,           // 中等屏幕占1/3行
  lg: 6,           // 大屏幕占1/4行
  xl: 6            // 超大屏幕占1/4行
};
```

## 常用配置组合

### 基础搜索表单配置
```typescript
const basicSearchConfig: FuniSearchConfig = {
  modelValue: {},
  schema: [
    {
      prop: 'keyword',
      label: '关键字',
      component: 'el-input',
      props: {
        placeholder: '请输入关键字',
        clearable: true
      },
      span: 8
    },
    {
      prop: 'status',
      label: '状态',
      component: 'el-select',
      props: {
        placeholder: '请选择状态',
        clearable: true
      },
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      span: 6
    },
    {
      prop: 'dateRange',
      label: '日期范围',
      component: 'el-date-picker',
      props: {
        type: 'daterange',
        rangeSeparator: '至',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      },
      span: 10
    }
  ],
  inline: true,
  showReset: true,
  showSearch: true
};
```

### 可展开收起的搜索表单配置
```typescript
const expandableSearchConfig: FuniSearchConfig = {
  modelValue: {},
  schema: [
    // 基础搜索项（始终显示）
    {
      prop: 'name',
      label: '名称',
      component: 'el-input',
      props: { placeholder: '请输入名称', clearable: true },
      span: 6
    },
    {
      prop: 'type',
      label: '类型',
      component: 'el-select',
      props: { placeholder: '请选择类型', clearable: true },
      options: [
        { label: '类型A', value: 'A' },
        { label: '类型B', value: 'B' }
      ],
      span: 6
    },
    {
      prop: 'status',
      label: '状态',
      component: 'el-select',
      props: { placeholder: '请选择状态', clearable: true },
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      span: 6
    },
    // 高级搜索项（可折叠）
    {
      prop: 'category',
      label: '分类',
      component: 'el-cascader',
      props: { placeholder: '请选择分类', clearable: true },
      options: [
        {
          label: '分类1',
          value: '1',
          children: [
            { label: '子分类1-1', value: '1-1' },
            { label: '子分类1-2', value: '1-2' }
          ]
        }
      ],
      span: 6
    },
    {
      prop: 'createUser',
      label: '创建人',
      component: 'FuniRUOC',
      props: {
        type: 'user',
        placeholder: '请选择创建人',
        clearable: true
      },
      span: 6
    },
    {
      prop: 'createTime',
      label: '创建时间',
      component: 'el-date-picker',
      props: {
        type: 'datetimerange',
        rangeSeparator: '至',
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间'
      },
      span: 12
    }
  ],
  showExpand: true,
  expandCount: 3,
  defaultExpanded: false
};
```

### 带验证的搜索表单配置
```typescript
const validatedSearchConfig: FuniSearchConfig = {
  modelValue: {},
  schema: [
    {
      prop: 'startDate',
      label: '开始日期',
      component: 'el-date-picker',
      props: {
        type: 'date',
        placeholder: '请选择开始日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      },
      rules: [
        { required: true, message: '请选择开始日期', trigger: 'change' }
      ],
      span: 6
    },
    {
      prop: 'endDate',
      label: '结束日期',
      component: 'el-date-picker',
      props: {
        type: 'date',
        placeholder: '请选择结束日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      },
      rules: [
        { required: true, message: '请选择结束日期', trigger: 'change' },
        {
          validator: (rule, value, callback) => {
            // 自定义验证逻辑
            callback();
          },
          trigger: 'change'
        }
      ],
      span: 6
    }
  ],
  validateOnSearch: true
};
```

### 联动搜索表单配置
```typescript
const cascadeSearchConfig: FuniSearchConfig = {
  modelValue: {},
  schema: [
    {
      prop: 'province',
      label: '省份',
      component: 'el-select',
      props: { placeholder: '请选择省份', clearable: true },
      options: [
        { label: '北京市', value: 'beijing' },
        { label: '上海市', value: 'shanghai' },
        { label: '广东省', value: 'guangdong' }
      ],
      span: 6
    },
    {
      prop: 'city',
      label: '城市',
      component: 'el-select',
      props: { placeholder: '请选择城市', clearable: true },
      options: [], // 动态选项
      dependencies: ['province'],
      watch: {
        handler: (newVal, oldVal, formData) => {
          // 根据省份更新城市选项
          updateCityOptions(formData.province);
        },
        immediate: true
      },
      span: 6
    }
  ]
};
```

## 最佳实践建议

### 1. 布局设计
- 合理使用栅格系统进行布局
- 重要搜索项放在前面
- 使用展开收起功能处理复杂搜索
- 考虑移动端的响应式适配

### 2. 用户体验
- 提供清晰的占位符文本
- 合理设置默认值
- 支持快速清空和重置
- 提供搜索历史功能

### 3. 性能优化
- 使用防抖避免频繁搜索
- 合理设置自动搜索延迟
- 避免在schema中使用复杂的响应式对象
- 大量选项时使用远程搜索

### 4. 数据处理
- 统一搜索参数格式
- 处理空值和特殊字符
- 提供参数转换功能
- 支持复杂查询条件

## 常见问题

### Q: 如何实现搜索项的联动？
A: 使用dependencies和watch配置，监听依赖字段的变化来更新当前字段的选项

### Q: 如何自定义搜索按钮？
A: 使用插槽自定义按钮区域，或者通过CSS覆盖默认样式

### Q: 如何实现搜索历史功能？
A: 监听search事件，将搜索参数保存到localStorage或服务端

### Q: 如何处理复杂的搜索逻辑？
A: 在search事件中进行参数处理和转换，然后调用相应的API接口
