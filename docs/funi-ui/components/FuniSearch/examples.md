# FuniSearch 使用示例

## 基础示例

### 简单搜索表单
```vue
<template>
  <div>
    <FuniSearch
      v-model="searchParams"
      :schema="basicSchema"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <div class="search-result">
      <p>搜索参数：{{ JSON.stringify(searchParams) }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const searchParams = ref({
  keyword: '',
  status: '',
  type: ''
})

const basicSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: {
      placeholder: '请输入关键字',
      clearable: true
    },
    span: 8
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: {
      placeholder: '请选择状态',
      clearable: true
    },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    span: 6
  },
  {
    prop: 'type',
    label: '类型',
    component: 'el-select',
    props: {
      placeholder: '请选择类型',
      clearable: true
    },
    options: [
      { label: '类型A', value: 'A' },
      { label: '类型B', value: 'B' },
      { label: '类型C', value: 'C' }
    ],
    span: 6
  }
])

const handleSearch = (params) => {
  console.log('搜索参数:', params)
  // 执行搜索逻辑
}

const handleReset = () => {
  console.log('重置搜索条件')
}
</script>

<style scoped>
.search-result {
  margin-top: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
```

### 日期范围搜索
```vue
<template>
  <FuniSearch
    v-model="dateSearchParams"
    :schema="dateSchema"
    @search="handleDateSearch"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const dateSearchParams = ref({
  name: '',
  dateRange: [],
  createTime: ''
})

const dateSchema = reactive([
  {
    prop: 'name',
    label: '名称',
    component: 'el-input',
    props: {
      placeholder: '请输入名称',
      clearable: true
    },
    span: 6
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 10
  },
  {
    prop: 'createTime',
    label: '创建时间',
    component: 'el-date-picker',
    props: {
      type: 'datetime',
      placeholder: '请选择创建时间',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
    span: 8
  }
])

const handleDateSearch = (params) => {
  console.log('日期搜索参数:', params)
}
</script>
```

## 高级功能示例

### 可展开收起的搜索表单
```vue
<template>
  <FuniSearch
    v-model="expandSearchParams"
    :schema="expandSchema"
    show-expand
    :expand-count="3"
    :default-expanded="false"
    @search="handleExpandSearch"
    @expand="handleExpand"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const expandSearchParams = ref({
  username: '',
  email: '',
  phone: '',
  department: '',
  role: '',
  status: '',
  createTimeStart: '',
  createTimeEnd: '',
  lastLoginStart: '',
  lastLoginEnd: ''
})

const expandSchema = reactive([
  // 基础搜索项（始终显示）
  {
    prop: 'username',
    label: '用户名',
    component: 'el-input',
    props: { placeholder: '请输入用户名', clearable: true },
    span: 6
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱', clearable: true },
    span: 6
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号', clearable: true },
    span: 6
  },
  // 高级搜索项（可折叠）
  {
    prop: 'department',
    label: '部门',
    component: 'FuniTreeSelect',
    props: { 
      placeholder: '请选择部门',
      clearable: true,
      api: '/api/departments'
    },
    span: 6
  },
  {
    prop: 'role',
    label: '角色',
    component: 'FuniRUOC',
    props: { 
      type: 'role',
      placeholder: '请选择角色',
      clearable: true
    },
    span: 6
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    span: 6
  },
  {
    prop: 'createTimeStart',
    label: '创建时间从',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '开始日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 6
  },
  {
    prop: 'createTimeEnd',
    label: '创建时间至',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 6
  }
])

const handleExpandSearch = (params) => {
  console.log('展开搜索参数:', params)
}

const handleExpand = (expanded) => {
  console.log('展开状态:', expanded)
}
</script>
```

### 自动搜索表单
```vue
<template>
  <FuniSearch
    v-model="autoSearchParams"
    :schema="autoSearchSchema"
    auto-search
    :search-delay="500"
    :show-search="false"
    @search="handleAutoSearch"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const autoSearchParams = ref({
  keyword: '',
  category: '',
  priceRange: []
})

const autoSearchSchema = reactive([
  {
    prop: 'keyword',
    label: '商品名称',
    component: 'el-input',
    props: {
      placeholder: '请输入商品名称',
      clearable: true,
      prefixIcon: 'Search'
    },
    span: 8
  },
  {
    prop: 'category',
    label: '商品分类',
    component: 'el-select',
    props: {
      placeholder: '请选择分类',
      clearable: true
    },
    options: [
      { label: '电子产品', value: 'electronics' },
      { label: '服装', value: 'clothing' },
      { label: '食品', value: 'food' },
      { label: '图书', value: 'books' }
    ],
    span: 6
  },
  {
    prop: 'priceRange',
    label: '价格区间',
    component: 'el-slider',
    props: {
      range: true,
      min: 0,
      max: 10000,
      step: 100,
      showStops: false,
      showTooltip: true,
      formatTooltip: (value) => `¥${value}`
    },
    span: 10
  }
])

const handleAutoSearch = (params) => {
  console.log('自动搜索:', params)
  // 执行搜索逻辑，通常是调用API
}
</script>
```

### 带验证的搜索表单
```vue
<template>
  <FuniSearch
    v-model="validatedSearchParams"
    :schema="validatedSchema"
    validate-on-search
    @search="handleValidatedSearch"
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const validatedSearchParams = ref({
  startDate: '',
  endDate: '',
  minAmount: null,
  maxAmount: null
})

const validatedSchema = reactive([
  {
    prop: 'startDate',
    label: '开始日期',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '请选择开始日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    rules: [
      { required: true, message: '请选择开始日期', trigger: 'change' }
    ],
    span: 6
  },
  {
    prop: 'endDate',
    label: '结束日期',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '请选择结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    rules: [
      { required: true, message: '请选择结束日期', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (value && validatedSearchParams.value.startDate && value < validatedSearchParams.value.startDate) {
            callback(new Error('结束日期不能早于开始日期'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
    span: 6
  },
  {
    prop: 'minAmount',
    label: '最小金额',
    component: 'el-input-number',
    props: {
      min: 0,
      precision: 2,
      placeholder: '请输入最小金额'
    },
    span: 6
  },
  {
    prop: 'maxAmount',
    label: '最大金额',
    component: 'el-input-number',
    props: {
      min: 0,
      precision: 2,
      placeholder: '请输入最大金额'
    },
    rules: [
      {
        validator: (rule, value, callback) => {
          if (value && validatedSearchParams.value.minAmount && value < validatedSearchParams.value.minAmount) {
            callback(new Error('最大金额不能小于最小金额'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    span: 6
  }
])

const handleValidatedSearch = (params) => {
  console.log('验证通过，执行搜索:', params)
}

const handleValidate = (prop, isValid, message) => {
  console.log('字段验证:', prop, isValid, message)
}
</script>
```

## 联动搜索示例

### 省市区联动搜索
```vue
<template>
  <FuniSearch
    v-model="cascadeSearchParams"
    :schema="cascadeSchema"
    @search="handleCascadeSearch"
  />
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

const cascadeSearchParams = ref({
  province: '',
  city: '',
  district: ''
})

const cascadeSchema = reactive([
  {
    prop: 'province',
    label: '省份',
    component: 'el-select',
    props: { placeholder: '请选择省份', clearable: true },
    options: [
      { label: '北京市', value: 'beijing' },
      { label: '上海市', value: 'shanghai' },
      { label: '广东省', value: 'guangdong' },
      { label: '江苏省', value: 'jiangsu' }
    ],
    span: 6
  },
  {
    prop: 'city',
    label: '城市',
    component: 'el-select',
    props: { placeholder: '请选择城市', clearable: true },
    options: [],
    span: 6
  },
  {
    prop: 'district',
    label: '区县',
    component: 'el-select',
    props: { placeholder: '请选择区县', clearable: true },
    options: [],
    span: 6
  }
])

// 城市数据映射
const cityMap = {
  beijing: [
    { label: '东城区', value: 'dongcheng' },
    { label: '西城区', value: 'xicheng' },
    { label: '朝阳区', value: 'chaoyang' },
    { label: '海淀区', value: 'haidian' }
  ],
  shanghai: [
    { label: '黄浦区', value: 'huangpu' },
    { label: '徐汇区', value: 'xuhui' },
    { label: '长宁区', value: 'changning' },
    { label: '静安区', value: 'jingan' }
  ],
  guangdong: [
    { label: '广州市', value: 'guangzhou' },
    { label: '深圳市', value: 'shenzhen' },
    { label: '珠海市', value: 'zhuhai' },
    { label: '佛山市', value: 'foshan' }
  ]
}

// 区县数据映射
const districtMap = {
  dongcheng: [
    { label: '东华门街道', value: 'donghuamen' },
    { label: '景山街道', value: 'jingshan' }
  ],
  chaoyang: [
    { label: '建国门街道', value: 'jianguomen' },
    { label: '朝外街道', value: 'chaowai' }
  ]
  // ... 更多区县数据
}

// 监听省份变化，更新城市选项
watch(() => cascadeSearchParams.value.province, (newProvince) => {
  // 清空城市和区县
  cascadeSearchParams.value.city = ''
  cascadeSearchParams.value.district = ''
  
  // 更新城市选项
  const citySchema = cascadeSchema.find(item => item.prop === 'city')
  if (citySchema) {
    citySchema.options = cityMap[newProvince] || []
  }
  
  // 清空区县选项
  const districtSchema = cascadeSchema.find(item => item.prop === 'district')
  if (districtSchema) {
    districtSchema.options = []
  }
})

// 监听城市变化，更新区县选项
watch(() => cascadeSearchParams.value.city, (newCity) => {
  // 清空区县
  cascadeSearchParams.value.district = ''
  
  // 更新区县选项
  const districtSchema = cascadeSchema.find(item => item.prop === 'district')
  if (districtSchema) {
    districtSchema.options = districtMap[newCity] || []
  }
})

const handleCascadeSearch = (params) => {
  console.log('联动搜索参数:', params)
}
</script>
```

### 动态搜索表单
```vue
<template>
  <div>
    <el-radio-group v-model="searchType" @change="handleSearchTypeChange">
      <el-radio-button label="user">用户搜索</el-radio-button>
      <el-radio-button label="order">订单搜索</el-radio-button>
      <el-radio-button label="product">商品搜索</el-radio-button>
    </el-radio-group>
    
    <FuniSearch
      v-model="dynamicSearchParams"
      :schema="dynamicSchema"
      @search="handleDynamicSearch"
      style="margin-top: 16px;"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const searchType = ref('user')
const dynamicSearchParams = ref({})

const userSchema = [
  {
    prop: 'username',
    label: '用户名',
    component: 'el-input',
    props: { placeholder: '请输入用户名', clearable: true },
    span: 8
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱', clearable: true },
    span: 8
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    span: 8
  }
]

const orderSchema = [
  {
    prop: 'orderNo',
    label: '订单号',
    component: 'el-input',
    props: { placeholder: '请输入订单号', clearable: true },
    span: 8
  },
  {
    prop: 'status',
    label: '订单状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: [
      { label: '待付款', value: 'pending' },
      { label: '已付款', value: 'paid' },
      { label: '已发货', value: 'shipped' },
      { label: '已完成', value: 'completed' }
    ],
    span: 8
  },
  {
    prop: 'dateRange',
    label: '下单时间',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期'
    },
    span: 8
  }
]

const productSchema = [
  {
    prop: 'productName',
    label: '商品名称',
    component: 'el-input',
    props: { placeholder: '请输入商品名称', clearable: true },
    span: 8
  },
  {
    prop: 'category',
    label: '商品分类',
    component: 'el-select',
    props: { placeholder: '请选择分类', clearable: true },
    options: [
      { label: '电子产品', value: 'electronics' },
      { label: '服装', value: 'clothing' },
      { label: '食品', value: 'food' }
    ],
    span: 8
  },
  {
    prop: 'priceRange',
    label: '价格区间',
    component: 'el-input-number',
    props: { placeholder: '最低价格', min: 0 },
    span: 4
  },
  {
    prop: 'priceRangeMax',
    label: '至',
    component: 'el-input-number',
    props: { placeholder: '最高价格', min: 0 },
    span: 4
  }
]

const dynamicSchema = computed(() => {
  switch (searchType.value) {
    case 'user':
      return userSchema
    case 'order':
      return orderSchema
    case 'product':
      return productSchema
    default:
      return []
  }
})

const handleSearchTypeChange = () => {
  // 切换搜索类型时重置搜索参数
  dynamicSearchParams.value = {}
}

const handleDynamicSearch = (params) => {
  console.log(`${searchType.value}搜索参数:`, params)
}
</script>
```

## 响应式布局示例

### 移动端适配搜索表单
```vue
<template>
  <FuniSearch
    v-model="responsiveSearchParams"
    :schema="responsiveSchema"
    responsive
    @search="handleResponsiveSearch"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const responsiveSearchParams = ref({
  keyword: '',
  category: '',
  status: '',
  dateRange: []
})

const responsiveSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { placeholder: '请输入关键字', clearable: true },
    span: 24,        // 默认占满一行
    xs: 24,          // 超小屏幕占满一行
    sm: 12,          // 小屏幕占半行
    md: 8,           // 中等屏幕占1/3行
    lg: 6,           // 大屏幕占1/4行
    xl: 6            // 超大屏幕占1/4行
  },
  {
    prop: 'category',
    label: '分类',
    component: 'el-select',
    props: { placeholder: '请选择分类', clearable: true },
    options: [
      { label: '分类A', value: 'A' },
      { label: '分类B', value: 'B' }
    ],
    span: 24,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    xl: 6
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    span: 24,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    xl: 6
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期'
    },
    span: 24,
    xs: 24,
    sm: 24,
    md: 24,
    lg: 12,
    xl: 12
  }
])

const handleResponsiveSearch = (params) => {
  console.log('响应式搜索参数:', params)
}
</script>
```

这些示例展示了FuniSearch组件的各种使用场景，从基础的搜索表单到复杂的联动搜索，都有相应的实现方式。
