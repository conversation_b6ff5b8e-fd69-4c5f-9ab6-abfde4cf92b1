# FuniSearch 最佳实践

## 推荐用法

### 1. 标准搜索表单配置
```vue
<template>
  <FuniSearch
    v-model="searchForm"
    :schema="searchSchema"
    :loading="loading"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const loading = ref(false)

// 推荐：使用响应式数据管理搜索表单
const searchForm = ref({
  keyword: '',
  status: '',
  dateRange: [],
  department: '',
  createUser: ''
})

// 推荐：合理的搜索字段配置（3-6个基础字段）
const searchSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { 
      placeholder: '请输入用户名或邮箱',
      clearable: true 
    },
    span: 6
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { 
      placeholder: '请选择状态',
      clearable: true 
    },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    span: 4
  },
  {
    prop: 'dateRange',
    label: '创建时间',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 8
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门',
      clearable: true
    },
    span: 6
  }
])

// 推荐：统一的搜索处理函数
const handleSearch = (searchParams) => {
  // 清理搜索参数
  const cleanParams = Object.keys(searchParams).reduce((acc, key) => {
    const value = searchParams[key]
    if (value !== '' && value !== null && value !== undefined) {
      // 处理日期范围
      if (key === 'dateRange' && Array.isArray(value) && value.length === 2) {
        acc.startDate = value[0]
        acc.endDate = value[1]
      } else {
        acc[key] = value
      }
    }
    return acc
  }, {})
  
  console.log('搜索参数:', cleanParams)
  loadData(cleanParams)
}

const handleReset = () => {
  console.log('重置搜索条件')
  loadData({})
}

// 推荐：使用防抖优化搜索
import { debounce } from 'lodash-es'

const debouncedSearch = debounce((params) => {
  handleSearch(params)
}, 300)
</script>
```

### 2. 展开收起搜索配置
```vue
<template>
  <FuniSearch
    v-model="expandSearchForm"
    :schema="expandSearchSchema"
    :expand-config="expandConfig"
    @search="handleExpandSearch"
    @expand-change="handleExpandChange"
  />
</template>

<script setup>
// 推荐：展开收起的合理配置
const expandSearchForm = ref({
  // 基础搜索字段
  keyword: '',
  status: '',
  
  // 高级搜索字段
  department: '',
  role: '',
  createUser: '',
  tags: [],
  amount: '',
  priority: ''
})

const expandSearchSchema = reactive([
  // 基础搜索字段（始终显示，3-4个）
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { placeholder: '请输入关键字', clearable: true },
    expand: false,
    span: 6
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: statusOptions,
    expand: false,
    span: 4
  },
  
  // 高级搜索字段（展开时显示，4-8个）
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门',
      clearable: true
    },
    expand: true,
    span: 6
  },
  {
    prop: 'role',
    label: '角色',
    component: 'FuniRUOC',
    props: { type: 'role', multiple: true },
    expand: true,
    span: 6
  },
  {
    prop: 'createUser',
    label: '创建人',
    component: 'FuniSelect',
    props: {
      url: '/api/users',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择创建人',
      clearable: true
    },
    expand: true,
    span: 6
  },
  {
    prop: 'tags',
    label: '标签',
    component: 'el-select',
    props: { 
      multiple: true,
      placeholder: '请选择标签',
      clearable: true 
    },
    options: tagOptions,
    expand: true,
    span: 6
  }
])

// 推荐：展开收起配置
const expandConfig = reactive({
  enabled: true,
  defaultExpanded: false,
  expandText: '展开',
  collapseText: '收起',
  showCount: true,
  expandIcon: 'ArrowDown',
  collapseIcon: 'ArrowUp'
})

const handleExpandChange = (expanded) => {
  console.log('展开状态变化:', expanded)
}
</script>
```

### 3. 响应式搜索布局
```vue
<script setup>
// 推荐：响应式布局配置
const responsiveSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    // 推荐：合理的响应式配置
    span: 24,
    xs: 24,  // 超小屏：全宽
    sm: 12,  // 小屏：半宽
    md: 8,   // 中屏：1/3宽
    lg: 6,   // 大屏：1/4宽
    xl: 6    // 超大屏：1/4宽
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    span: 24,
    xs: 24,
    sm: 12,
    md: 6,
    lg: 4,
    xl: 4
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    component: 'el-date-picker',
    props: { type: 'daterange' },
    // 推荐：日期范围组件占用更多空间
    span: 24,
    xs: 24,
    sm: 24,
    md: 10,
    lg: 8,
    xl: 8
  }
])
</script>
```

## 避免的用法

### 1. 不推荐的配置方式
```vue
<!-- ❌ 避免：过多的搜索字段 -->
<FuniSearch
  :schema="[...15个搜索字段]" // 应该使用展开收起功能
/>

<!-- ❌ 避免：在模板中写复杂配置 -->
<FuniSearch
  :schema="[
    { prop: 'name', label: '姓名', component: 'el-input', props: { placeholder: '请输入' } },
    { prop: 'email', label: '邮箱', component: 'el-input', rules: [{ type: 'email' }] }
  ]"
/>

<!-- ❌ 避免：在模板中写内联函数 -->
<FuniSearch
  @search="(params) => loadData(params)"
  @reset="() => resetData()"
/>
```

### 2. 不推荐的数据处理
```vue
<script setup>
// ❌ 避免：不处理空值参数
const handleSearch = (searchParams) => {
  // 直接传递所有参数，包括空值
  api.search(searchParams) // 会传递很多空字符串和null
}

// ❌ 避免：在schema中使用复杂的响应式对象
const schema = reactive([
  {
    prop: 'department',
    label: '部门',
    component: 'el-select',
    options: computed(() => { // 不要在schema中使用computed
      return departments.value.map(d => ({ label: d.name, value: d.id }))
    })
  }
])

// ❌ 避免：频繁的搜索请求
const handleSearch = (params) => {
  api.search(params) // 没有防抖，每次输入都会触发请求
}
</script>
```

### 3. 常见错误和解决方案

#### 错误1：搜索字段过多影响用户体验
```vue
<script setup>
// ❌ 错误：所有字段都在基础搜索区域
const schema = [
  { prop: 'field1', label: '字段1', component: 'el-input' },
  { prop: 'field2', label: '字段2', component: 'el-select' },
  // ... 10个以上字段
]

// ✅ 正确：使用展开收起功能
const schema = [
  // 基础字段（3-4个）
  { prop: 'keyword', label: '关键字', component: 'el-input', expand: false },
  { prop: 'status', label: '状态', component: 'el-select', expand: false },
  
  // 高级字段（展开时显示）
  { prop: 'department', label: '部门', component: 'el-select', expand: true },
  { prop: 'createUser', label: '创建人', component: 'el-select', expand: true }
]
</script>
```

#### 错误2：日期范围参数处理不当
```vue
<script setup>
// ❌ 错误：不处理日期范围参数
const handleSearch = (params) => {
  api.search(params) // dateRange: ['2023-01-01', '2023-12-31']
}

// ✅ 正确：转换日期范围参数
const handleSearch = (params) => {
  const cleanParams = { ...params }
  
  // 处理日期范围
  if (cleanParams.dateRange && Array.isArray(cleanParams.dateRange)) {
    cleanParams.startDate = cleanParams.dateRange[0]
    cleanParams.endDate = cleanParams.dateRange[1]
    delete cleanParams.dateRange
  }
  
  api.search(cleanParams)
}
</script>
```

#### 错误3：搜索状态管理不当
```vue
<script setup>
// ❌ 错误：没有loading状态
const handleSearch = async (params) => {
  const result = await api.search(params) // 用户不知道正在搜索
  updateData(result)
}

// ✅ 正确：管理loading状态
const loading = ref(false)

const handleSearch = async (params) => {
  loading.value = true
  try {
    const result = await api.search(params)
    updateData(result)
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}
</script>
```

## 性能优化建议

### 1. 搜索防抖优化
```vue
<script setup>
import { debounce } from 'lodash-es'

// 推荐：使用防抖优化搜索
const debouncedSearch = debounce(async (params) => {
  await loadData(params)
}, 300)

// 推荐：区分立即搜索和防抖搜索
const handleSearch = (params, immediate = false) => {
  if (immediate) {
    loadData(params) // 点击搜索按钮时立即执行
  } else {
    debouncedSearch(params) // 输入时防抖执行
  }
}
</script>
```

### 2. 选项数据缓存
```vue
<script setup>
// 推荐：缓存常用选项数据
const optionsCache = new Map()

const loadOptions = async (url) => {
  if (optionsCache.has(url)) {
    return optionsCache.get(url)
  }
  
  const options = await api.getOptions(url)
  optionsCache.set(url, options)
  return options
}

// 推荐：预加载常用选项
onMounted(async () => {
  await Promise.all([
    loadOptions('/api/departments'),
    loadOptions('/api/roles'),
    loadOptions('/api/status')
  ])
})
</script>
```

### 3. 搜索结果缓存
```vue
<script setup>
// 推荐：缓存搜索结果
const searchCache = new Map()

const loadData = async (params) => {
  const cacheKey = JSON.stringify(params)
  
  if (searchCache.has(cacheKey)) {
    return searchCache.get(cacheKey)
  }
  
  const result = await api.search(params)
  searchCache.set(cacheKey, result)
  
  // 限制缓存大小
  if (searchCache.size > 50) {
    const firstKey = searchCache.keys().next().value
    searchCache.delete(firstKey)
  }
  
  return result
}
</script>
```

## 业务场景最佳实践

### 1. 用户管理搜索
```vue
<template>
  <FuniSearch
    v-model="userSearchForm"
    :schema="userSearchSchema"
    :expand-config="userExpandConfig"
    @search="handleUserSearch"
  />
</template>

<script setup>
// 用户管理搜索的标准配置
const userSearchForm = ref({
  keyword: '',
  status: '',
  department: '',
  role: '',
  dateRange: []
})

const userSearchSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { placeholder: '请输入用户名或邮箱', clearable: true },
    expand: false,
    span: 6
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    expand: false,
    span: 4
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门',
      clearable: true
    },
    expand: true,
    span: 6
  },
  {
    prop: 'role',
    label: '角色',
    component: 'FuniRUOC',
    props: { type: 'role', multiple: true },
    expand: true,
    span: 6
  },
  {
    prop: 'dateRange',
    label: '注册时间',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期'
    },
    expand: true,
    span: 8
  }
])

const userExpandConfig = reactive({
  enabled: true,
  defaultExpanded: false,
  expandText: '高级搜索',
  collapseText: '收起'
})
</script>
```

### 2. 订单管理搜索
```vue
<template>
  <FuniSearch
    v-model="orderSearchForm"
    :schema="orderSearchSchema"
    @search="handleOrderSearch"
  />
</template>

<script setup>
// 订单管理搜索的标准配置
const orderSearchForm = ref({
  orderNo: '',
  customerName: '',
  status: '',
  payStatus: '',
  amountRange: [],
  dateRange: []
})

const orderSearchSchema = reactive([
  {
    prop: 'orderNo',
    label: '订单号',
    component: 'el-input',
    props: { placeholder: '请输入订单号', clearable: true },
    span: 6
  },
  {
    prop: 'customerName',
    label: '客户名称',
    component: 'el-input',
    props: { placeholder: '请输入客户名称', clearable: true },
    span: 6
  },
  {
    prop: 'status',
    label: '订单状态',
    component: 'el-select',
    props: { placeholder: '请选择订单状态', clearable: true },
    options: orderStatusOptions,
    span: 4
  },
  {
    prop: 'payStatus',
    label: '支付状态',
    component: 'el-select',
    props: { placeholder: '请选择支付状态', clearable: true },
    options: payStatusOptions,
    span: 4
  },
  {
    prop: 'amountRange',
    label: '金额范围',
    component: 'FuniMoneyInput',
    props: { type: 'range', placeholder: ['最小金额', '最大金额'] },
    span: 8
  },
  {
    prop: 'dateRange',
    label: '下单时间',
    component: 'el-date-picker',
    props: {
      type: 'datetimerange',
      rangeSeparator: '至',
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间'
    },
    span: 10
  }
])
</script>
```

### 3. 日志查询搜索
```vue
<template>
  <FuniSearch
    v-model="logSearchForm"
    :schema="logSearchSchema"
    :expand-config="logExpandConfig"
    @search="handleLogSearch"
  />
</template>

<script setup>
// 日志查询搜索的标准配置
const logSearchForm = ref({
  keyword: '',
  level: '',
  module: '',
  user: '',
  ip: '',
  dateRange: []
})

const logSearchSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { placeholder: '请输入日志内容关键字', clearable: true },
    expand: false,
    span: 8
  },
  {
    prop: 'level',
    label: '日志级别',
    component: 'el-select',
    props: { placeholder: '请选择日志级别', clearable: true },
    options: [
      { label: 'ERROR', value: 'error' },
      { label: 'WARN', value: 'warn' },
      { label: 'INFO', value: 'info' },
      { label: 'DEBUG', value: 'debug' }
    ],
    expand: false,
    span: 4
  },
  {
    prop: 'dateRange',
    label: '时间范围',
    component: 'el-date-picker',
    props: {
      type: 'datetimerange',
      rangeSeparator: '至',
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间',
      shortcuts: [
        { text: '最近1小时', value: () => [new Date(Date.now() - 3600 * 1000), new Date()] },
        { text: '最近24小时', value: () => [new Date(Date.now() - 24 * 3600 * 1000), new Date()] },
        { text: '最近7天', value: () => [new Date(Date.now() - 7 * 24 * 3600 * 1000), new Date()] }
      ]
    },
    expand: false,
    span: 12
  },
  {
    prop: 'module',
    label: '模块',
    component: 'el-select',
    props: { placeholder: '请选择模块', clearable: true },
    options: moduleOptions,
    expand: true,
    span: 6
  },
  {
    prop: 'user',
    label: '操作用户',
    component: 'FuniSelect',
    props: {
      url: '/api/users',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择操作用户',
      clearable: true
    },
    expand: true,
    span: 6
  },
  {
    prop: 'ip',
    label: 'IP地址',
    component: 'el-input',
    props: { placeholder: '请输入IP地址', clearable: true },
    expand: true,
    span: 6
  }
])
</script>
```

## 总结

### 关键原则
1. **用户体验优先**：合理控制搜索字段数量，使用展开收起功能
2. **性能优化**：使用防抖、缓存等技术优化搜索性能
3. **参数处理**：正确处理搜索参数，过滤空值，转换特殊参数
4. **响应式设计**：合理配置响应式布局，适配不同屏幕尺寸
5. **状态管理**：正确管理loading状态和错误处理

### 开发建议
1. 基础搜索字段控制在3-4个
2. 高级搜索字段使用展开收起功能
3. 使用防抖优化搜索性能
4. 缓存常用的选项数据
5. 正确处理日期范围等特殊参数
