# FuniPreview API 文档

## 组件概述

FuniPreview 是一个文件预览组件，支持图片和 Office 文档的预览功能。对于图片文件，提供 Canvas 渲染和 OCR 文字识别功能；对于 Office 文档，集成 OnlyOffice 文档编辑器进行预览。支持关键字搜索和高亮显示。

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| type | String | 'png' | 文件类型/扩展名 |
| url | String | - | 文件预览地址 |
| title | String | - | 文件标题 |

### Props 详细说明

#### type
- 文件类型或扩展名，用于判断预览方式
- 支持的图片格式：png, jpg, jpeg, gif, webp, svg（不区分大小写）
- 支持的文档格式：doc, docx, xls, xlsx, ppt, pptx, pdf 等 OnlyOffice 支持的格式
- 根据类型自动选择预览模式

#### url
- 文件的预览地址
- 图片文件：直接的图片 URL
- 文档文件：OnlyOffice 可访问的文档 URL
- 支持相对路径和绝对路径

#### title
- 文件标题，主要用于文档预览时的标题显示
- 在 OnlyOffice 编辑器中显示

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| outputImageOcrData | (ocrData: Array) | 图片 OCR 识别完成后触发 |

### Events 详细说明

#### outputImageOcrData
- 当图片加载完成并进行 OCR 识别后触发
- 参数为 OCR 识别结果数组
- 包含文字位置坐标和识别内容
- 仅在预览图片文件时触发

## Methods

| 方法名 | 参数 | 说明 |
|--------|------|------|
| highLightKeyword | (keyword: string, isLike?: boolean, color?: string) | 高亮显示关键字 |
| pdfKeywordSearch | (keyword: string) | PDF 文档关键字搜索 |

### Methods 详细说明

#### highLightKeyword
- 在预览内容中高亮显示指定关键字
- `keyword`: 要高亮的关键字
- `isLike`: 是否模糊匹配（图片 OCR 暂不支持）
- `color`: 高亮颜色，默认为 '#3386998a'
- 图片模式：在 Canvas 上绘制高亮区域
- 文档模式：调用 OnlyOffice 搜索功能

#### pdfKeywordSearch
- 在 PDF 或文档中搜索关键字
- 通过 OnlyOffice 编辑器的搜索功能实现
- 支持连续搜索下一个匹配项

## 功能特性

### 1. 图片预览
- 基于 Canvas 渲染
- 自适应容器大小
- 支持跨域图片加载
- 自动进行 OCR 文字识别

### 2. 文档预览
- 集成 OnlyOffice DocumentEditor
- 支持多种文档格式
- 只读模式预览
- 中文界面

### 3. 关键字搜索
- 图片：基于 OCR 结果的文字匹配
- 文档：OnlyOffice 内置搜索功能
- 支持高亮显示匹配结果

### 4. OCR 功能
- 自动识别图片中的文字
- 返回文字位置坐标
- 支持关键字定位和高亮

## 使用示例

### 图片预览

```vue
<template>
  <div style="height: 500px;">
    <FuniPreview 
      type="jpg"
      :url="imageUrl"
      title="示例图片"
      @outputImageOcrData="handleOcrData"
    />
    <el-button @click="searchInImage">搜索文字</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniPreview from '@/components/FuniPreview/index.vue'

const previewRef = ref()
const imageUrl = ref('https://example.com/document-image.jpg')
const ocrData = ref([])

const handleOcrData = (data) => {
  ocrData.value = data
  console.log('OCR 识别结果：', data)
}

const searchInImage = () => {
  previewRef.value.highLightKeyword('关键字', false, '#ff0000')
}
</script>
```

### 文档预览

```vue
<template>
  <div style="height: 600px;">
    <FuniPreview 
      type="pdf"
      :url="documentUrl"
      title="示例文档"
    />
    <div>
      <el-input 
        v-model="searchKeyword" 
        placeholder="输入搜索关键字"
        @keyup.enter="searchInDocument"
      />
      <el-button @click="searchInDocument">搜索</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniPreview from '@/components/FuniPreview/index.vue'

const previewRef = ref()
const documentUrl = ref('https://example.com/document.pdf')
const searchKeyword = ref('')

const searchInDocument = () => {
  if (searchKeyword.value) {
    previewRef.value.highLightKeyword(searchKeyword.value)
  }
}
</script>
```

### 动态文件预览

```vue
<template>
  <div>
    <el-select v-model="selectedFile" @change="changeFile">
      <el-option 
        v-for="file in fileList" 
        :key="file.id"
        :label="file.name"
        :value="file"
      />
    </el-select>
    
    <div style="height: 500px; margin-top: 20px;">
      <FuniPreview 
        v-if="selectedFile"
        ref="previewRef"
        :type="selectedFile.type"
        :url="selectedFile.url"
        :title="selectedFile.name"
        @outputImageOcrData="handleOcrData"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniPreview from '@/components/FuniPreview/index.vue'

const previewRef = ref()
const selectedFile = ref(null)

const fileList = ref([
  {
    id: 1,
    name: '合同扫描件.jpg',
    type: 'jpg',
    url: 'https://example.com/contract.jpg'
  },
  {
    id: 2,
    name: '项目方案.pdf',
    type: 'pdf',
    url: 'https://example.com/proposal.pdf'
  },
  {
    id: 3,
    name: '数据报表.xlsx',
    type: 'xlsx',
    url: 'https://example.com/report.xlsx'
  }
])

const changeFile = (file) => {
  console.log('切换文件：', file)
}

const handleOcrData = (data) => {
  console.log('OCR 数据：', data)
}
</script>
```

### 搜索功能集成

```vue
<template>
  <div>
    <div class="search-bar">
      <el-input 
        v-model="searchText"
        placeholder="输入搜索内容"
        @keyup.enter="performSearch"
      >
        <template #append>
          <el-button @click="performSearch">搜索</el-button>
        </template>
      </el-input>
      <el-button @click="nextMatch">下一个</el-button>
      <el-button @click="clearHighlight">清除高亮</el-button>
    </div>
    
    <div style="height: 500px;">
      <FuniPreview 
        ref="previewRef"
        :type="fileType"
        :url="fileUrl"
        :title="fileName"
        @outputImageOcrData="handleOcrData"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniPreview from '@/components/FuniPreview/index.vue'

const previewRef = ref()
const searchText = ref('')
const fileType = ref('pdf')
const fileUrl = ref('https://example.com/document.pdf')
const fileName = ref('示例文档')

const performSearch = () => {
  if (searchText.value && previewRef.value) {
    previewRef.value.highLightKeyword(searchText.value)
  }
}

const nextMatch = () => {
  if (searchText.value && previewRef.value) {
    previewRef.value.pdfKeywordSearch(searchText.value)
  }
}

const clearHighlight = () => {
  // 重新初始化预览以清除高亮
  previewRef.value.highLightKeyword('')
}

const handleOcrData = (data) => {
  console.log('OCR 识别完成，可进行文字搜索')
}
</script>

<style scoped>
.search-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  align-items: center;
}
</style>
```

## 技术依赖

### 1. OnlyOffice DocumentEditor
- 用于文档预览功能
- 需要配置 documentServerUrl
- 支持多种文档格式

### 2. Canvas API
- 用于图片渲染和高亮绘制
- 支持跨域图片处理

### 3. OCR 服务
- 接口地址：`/cssw/imageOcr/extractText`
- 用于图片文字识别
- 返回文字位置和内容

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .funi_preview_box | 预览容器 |

### 默认样式
- 宽度：100%
- 高度：自适应父容器
- 支持垂直滚动

## 注意事项

1. **环境配置**：需要正确配置 OnlyOffice 服务器地址
2. **跨域处理**：图片需要支持跨域访问
3. **文件格式**：确保文件格式与 type 参数匹配
4. **OCR 服务**：需要后端提供 OCR 识别接口
5. **性能考虑**：大文件预览可能影响性能
6. **浏览器兼容**：Canvas 和 OnlyOffice 的浏览器兼容性要求
7. **安全性**：文件 URL 需要包含必要的认证信息
