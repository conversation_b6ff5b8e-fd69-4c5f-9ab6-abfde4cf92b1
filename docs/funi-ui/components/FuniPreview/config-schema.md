# FuniPreview 配置结构定义

## 组件配置接口

### IFuniPreviewProps

```typescript
interface IFuniPreviewProps {
  /** 文件类型/扩展名 */
  type?: string
  /** 文件预览地址 */
  url?: string
  /** 文件标题 */
  title?: string
}
```

### IFuniPreviewEmits

```typescript
interface IFuniPreviewEmits {
  /** 图片 OCR 识别完成后触发 */
  outputImageOcrData: (ocrData: IOcrResult[]) => void
}
```

### IFuniPreviewMethods

```typescript
interface IFuniPreviewMethods {
  /** 高亮显示关键字 */
  highLightKeyword: (keyword: string, isLike?: boolean, color?: string) => void
  /** PDF 文档关键字搜索 */
  pdfKeywordSearch: (keyword: string) => void
}
```

## 内部状态结构

### IComponentState

```typescript
interface IComponentState {
  /** 预览容器引用 */
  funiPreviewBox: Ref<HTMLElement>
  /** OnlyOffice 配置 */
  config: Ref<IOnlyOfficeConfig>
  /** iframe 引用 */
  iframeRef: Ref<any>
  /** Canvas 引用 */
  canvasRef: Ref<HTMLCanvasElement>
  /** Canvas 上下文 */
  ctx: CanvasRenderingContext2D | null
  /** 图片对象 */
  image: HTMLImageElement | null
  /** OCR 识别结果 */
  extractTextRes: IOcrResult[] | null
  /** 当前搜索关键字 */
  currentSearchKeyword: string | null
}
```

## OnlyOffice 配置结构

### IOnlyOfficeConfig

```typescript
interface IOnlyOfficeConfig {
  /** 文档配置 */
  document: IDocumentConfig
  /** 编辑器配置 */
  editorConfig: IEditorConfig
  /** 文档服务器地址 */
  documentServerUrl?: string
}
```

### IDocumentConfig

```typescript
interface IDocumentConfig {
  /** 文件类型 */
  fileType: string
  /** 文档标题 */
  title: string
  /** 文档 URL */
  url: string
  /** 文档权限 */
  permissions?: IDocumentPermissions
}
```

### IEditorConfig

```typescript
interface IEditorConfig {
  /** 编辑器模式 */
  mode: 'view' | 'edit' | 'review'
  /** 界面语言 */
  lang: string
  /** 用户配置 */
  user?: IUserConfig
  /** 自定义配置 */
  customization?: ICustomizationConfig
}
```

### IDocumentPermissions

```typescript
interface IDocumentPermissions {
  /** 是否可编辑 */
  edit?: boolean
  /** 是否可下载 */
  download?: boolean
  /** 是否可打印 */
  print?: boolean
  /** 是否可复制 */
  copy?: boolean
}
```

### IUserConfig

```typescript
interface IUserConfig {
  /** 用户 ID */
  id: string
  /** 用户名 */
  name: string
  /** 用户组 */
  group?: string
}
```

### ICustomizationConfig

```typescript
interface ICustomizationConfig {
  /** 是否显示工具栏 */
  toolbar?: boolean
  /** 是否显示状态栏 */
  statusBar?: boolean
  /** 是否显示标尺 */
  ruler?: boolean
  /** 自定义 Logo */
  logo?: ILogoConfig
}
```

## OCR 数据结构

### IOcrResult

```typescript
interface IOcrResult {
  /** 文字区域坐标 */
  0: number[][]
  /** 识别结果 */
  1: [string, number]
}
```

### IOcrCoordinate

```typescript
interface IOcrCoordinate {
  /** X 坐标 */
  x: number
  /** Y 坐标 */
  y: number
}
```

### IOcrTextInfo

```typescript
interface IOcrTextInfo {
  /** 识别的文字内容 */
  text: string
  /** 置信度 */
  confidence: number
  /** 文字区域边界框 */
  boundingBox: IOcrCoordinate[]
}
```

### IOcrApiRequest

```typescript
interface IOcrApiRequest {
  /** Base64 编码的图片数据 */
  base64Image: string
}
```

### IOcrApiResponse

```typescript
interface IOcrApiResponse {
  /** 识别结果 */
  data: IOcrResult[]
  /** 响应状态 */
  success: boolean
  /** 错误信息 */
  message?: string
}
```

## 文件类型配置

### IFileTypeConfig

```typescript
interface IFileTypeConfig {
  /** 支持的图片格式 */
  imageTypes: string[]
  /** 支持的文档格式 */
  documentTypes: string[]
  /** 文件类型判断函数 */
  isImage: (type: string) => boolean
  /** 文件类型判断函数 */
  isDocument: (type: string) => boolean
}
```

### 预设文件类型

```typescript
const fileTypeConfig: IFileTypeConfig = {
  imageTypes: ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'],
  documentTypes: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'],
  isImage: (type: string) => 
    ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'].includes(type.toLowerCase()),
  isDocument: (type: string) => 
    ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'].includes(type.toLowerCase())
}
```

## Canvas 配置结构

### ICanvasConfig

```typescript
interface ICanvasConfig {
  /** 默认宽度 */
  defaultWidth: number
  /** 默认高度 */
  defaultHeight: number
  /** 是否支持跨域 */
  crossOrigin: boolean
  /** 高亮颜色 */
  highlightColor: string
  /** 绘制配置 */
  drawConfig: IDrawConfig
}
```

### IDrawConfig

```typescript
interface IDrawConfig {
  /** 填充样式 */
  fillStyle: string
  /** 线条宽度 */
  lineWidth: number
  /** 线条样式 */
  strokeStyle: string
  /** 全局透明度 */
  globalAlpha: number
}
```

## 搜索配置结构

### ISearchConfig

```typescript
interface ISearchConfig {
  /** 是否区分大小写 */
  caseSensitive: boolean
  /** 是否全词匹配 */
  wholeWord: boolean
  /** 是否支持正则表达式 */
  regex: boolean
  /** 高亮颜色 */
  highlightColor: string
  /** 最大搜索结果数 */
  maxResults: number
}
```

### ISearchResult

```typescript
interface ISearchResult {
  /** 匹配的文本 */
  text: string
  /** 匹配位置 */
  position: {
    start: number
    end: number
  }
  /** 页码（文档模式） */
  page?: number
  /** 坐标（图片模式） */
  coordinates?: IOcrCoordinate[]
}
```

## 事件处理配置

### IEventHandlers

```typescript
interface IEventHandlers {
  /** 初始化处理 */
  init: () => Promise<void>
  /** 绘制图片 */
  drawImage: () => void
  /** 高亮关键字 */
  highLightKeyword: (keyword: string, isLike?: boolean, color?: string) => void
  /** OCR 文字提取 */
  extractText: (base64Image: string) => Promise<IOcrResult[]>
  /** PDF 关键字搜索 */
  pdfKeywordSearch: (keyword: string) => void
  /** 图片加载完成处理 */
  onImageLoad: () => Promise<void>
}
```

## API 接口配置

### IApiEndpoints

```typescript
interface IApiEndpoints {
  /** OCR 识别接口 */
  ocrExtractText: '/cssw/imageOcr/extractText'
  /** 文档服务器地址 */
  documentServer: string
}
```

## 配置示例

### 基础图片预览配置

```typescript
const imagePreviewConfig: IFuniPreviewProps = {
  type: 'jpg',
  url: 'https://example.com/image.jpg',
  title: '示例图片'
}
```

### 文档预览配置

```typescript
const documentPreviewConfig: IFuniPreviewProps = {
  type: 'pdf',
  url: 'https://example.com/document.pdf',
  title: '示例文档'
}
```

### OnlyOffice 完整配置

```typescript
const onlyOfficeConfig: IOnlyOfficeConfig = {
  document: {
    fileType: 'pdf',
    title: '示例文档',
    url: 'https://example.com/document.pdf?token=xxx',
    permissions: {
      edit: false,
      download: false,
      print: true,
      copy: false
    }
  },
  editorConfig: {
    mode: 'view',
    lang: 'zh-CN',
    user: {
      id: 'user123',
      name: '张三'
    },
    customization: {
      toolbar: false,
      statusBar: false,
      ruler: false
    }
  }
}
```

## 默认配置

```typescript
const defaultConfig: Required<IFuniPreviewProps> = {
  type: 'png',
  url: '',
  title: ''
}

const defaultCanvasConfig: ICanvasConfig = {
  defaultWidth: 500,
  defaultHeight: 300,
  crossOrigin: true,
  highlightColor: '#3386998a',
  drawConfig: {
    fillStyle: '#3386998a',
    lineWidth: 2,
    strokeStyle: '#338699',
    globalAlpha: 0.5
  }
}

const defaultSearchConfig: ISearchConfig = {
  caseSensitive: false,
  wholeWord: false,
  regex: false,
  highlightColor: '#3386998a',
  maxResults: 100
}
```

## 环境配置

### IEnvironmentConfig

```typescript
interface IEnvironmentConfig {
  /** 文档服务器地址 */
  documentServerUrl: string
  /** 是否为生产环境 */
  isProduction: boolean
  /** API 基础地址 */
  apiBaseUrl: string
  /** 认证 Token */
  authToken: string
}
```
