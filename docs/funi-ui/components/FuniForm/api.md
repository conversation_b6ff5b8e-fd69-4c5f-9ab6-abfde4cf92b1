# FuniForm API文档

## 组件概述

FuniForm是基于ElementPlus的el-form封装的表单组件，支持通过schema配置快速生成表单，适用于数据录入、编辑等场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| schema | Array | [] | ✅ | 表单项配置数组 | - |
| modelValue | Object | {} | - | 表单数据对象 | el-form.model |
| rules | Object | {} | - | 表单验证规则 | el-form.rules |
| labelWidth | String | '100px' | - | 标签宽度 | el-form.label-width |
| labelPosition | String | 'right' | - | 标签位置 | el-form.label-position |
| inline | Boolean | false | - | 是否行内表单 | el-form.inline |
| size | String | 'default' | - | 表单尺寸 | el-form.size |
| disabled | Boolean | false | - | 是否禁用 | el-form.disabled |
| readonly | Boolean | false | - | 是否只读 | - |
| showMessage | Boolean | true | - | 是否显示校验错误信息 | el-form.show-message |
| inlineMessage | Boolean | false | - | 是否以行内形式展示校验信息 | el-form.inline-message |
| statusIcon | Boolean | false | - | 是否在输入框中显示校验结果反馈图标 | el-form.status-icon |
| validateOnRuleChange | Boolean | true | - | 是否在rules属性改变后立即触发一次验证 | el-form.validate-on-rule-change |
| hideRequiredAsterisk | Boolean | false | - | 是否隐藏必填字段标签旁的红色星号 | el-form.hide-required-asterisk |
| scrollToError | Boolean | false | - | 当校验失败时，滚动到第一个错误表单项 | el-form.scroll-to-error |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: Object | 表单数据更新事件 | 表单项值变化时 |
| validate | (prop: string, isValid: boolean, message: string) | 表单项校验事件 | 表单项校验后 |
| change | (value: any, prop: string) | 表单项变化事件 | 表单项值变化时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| validate | (callback?: Function) | Promise\<boolean\> | 对整个表单的内容进行验证 |
| validateField | (props: string \| string[], callback?: Function) | Promise\<boolean\> | 验证具体的某个字段 |
| resetFields | (props?: string \| string[]) | void | 重置该表单项，将其值重置为初始值，并移除校验结果 |
| scrollToField | (prop: string) | void | 滚动到指定的字段 |
| clearValidate | (props?: string \| string[]) | void | 清理指定字段的表单验证信息 |

## schema配置结构

```typescript
interface FormSchemaItem {
  // 基础配置
  prop: string;                    // 字段名，必填
  label: string;                   // 字段标签，必填
  component: string;               // 组件类型，必填
  
  // 组件配置
  props?: Record<string, any>;     // 组件属性
  slots?: Record<string, any>;     // 组件插槽
  
  // 布局配置
  span?: number;                   // 栅格占据的列数（24栅格系统）
  offset?: number;                 // 栅格左侧的间隔格数
  
  // 显示控制
  show?: boolean | ComputedRef<boolean>; // 是否显示
  
  // 验证规则
  rules?: FormRule[];              // 字段验证规则
  
  // 表单项配置
  labelWidth?: string;             // 标签宽度
  required?: boolean;              // 是否必填
  error?: string;                  // 表单域验证错误时的提示文本
  showMessage?: boolean;           // 是否显示校验错误信息
  inlineMessage?: boolean;         // 是否以行内形式展示校验信息
  size?: 'large' | 'default' | 'small'; // 表单项尺寸
  
  // 其他配置
  [key: string]: any;              // 其他自定义配置
}
```

## 支持的组件类型

### 输入类组件
- **el-input**: 输入框
- **el-textarea**: 文本域
- **el-input-number**: 数字输入框
- **el-autocomplete**: 自动完成输入框

### 选择类组件
- **el-select**: 选择器
- **el-cascader**: 级联选择器
- **el-tree-select**: 树形选择器
- **el-radio-group**: 单选框组
- **el-checkbox-group**: 多选框组

### 日期时间类组件
- **el-date-picker**: 日期选择器
- **el-time-picker**: 时间选择器
- **el-datetime-picker**: 日期时间选择器

### 其他组件
- **el-switch**: 开关
- **el-slider**: 滑块
- **el-rate**: 评分
- **el-color-picker**: 颜色选择器
- **el-upload**: 文件上传

### FuniUI组件
- **FuniSelect**: Funi选择器
- **FuniTreeSelect**: Funi树形选择器
- **FuniRUOC**: 角色用户组织选择器
- **FuniMoneyInput**: 金额输入框
- **FuniEditor**: 富文本编辑器

## 使用示例

### 基础表单
```vue
<template>
  <FuniForm
    v-model="formData"
    :schema="formSchema"
    :rules="formRules"
    label-width="120px"
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = ref({
  username: '',
  email: '',
  age: null,
  gender: '',
  hobbies: [],
  birthday: null,
  isActive: true
})

const formSchema = reactive([
  {
    prop: 'username',
    label: '用户名',
    component: 'el-input',
    props: {
      placeholder: '请输入用户名',
      clearable: true
    },
    span: 12,
    rules: [
      { required: true, message: '用户名不能为空', trigger: 'blur' },
      { min: 3, max: 20, message: '长度在3到20个字符', trigger: 'blur' }
    ]
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: {
      type: 'email',
      placeholder: '请输入邮箱地址'
    },
    span: 12,
    rules: [
      { required: true, message: '邮箱不能为空', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
  },
  {
    prop: 'age',
    label: '年龄',
    component: 'el-input-number',
    props: {
      min: 1,
      max: 120,
      placeholder: '请输入年龄'
    },
    span: 12
  },
  {
    prop: 'gender',
    label: '性别',
    component: 'el-radio-group',
    props: {
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ]
    },
    span: 12
  }
])

const handleValidate = (prop, isValid, message) => {
  console.log('字段验证:', prop, isValid, message)
}
</script>
```

## ElementPlus API支持

FuniForm基于el-form封装，支持所有el-form的API：

```vue
<template>
  <FuniForm
    v-model="formData"
    :schema="schema"
    
    <!-- ElementPlus el-form 所有属性 -->
    :rules="rules"
    label-width="120px"
    label-position="right"
    :inline="false"
    size="default"
    :disabled="false"
    :show-message="true"
    :inline-message="false"
    :status-icon="false"
    :validate-on-rule-change="true"
    :hide-required-asterisk="false"
    :scroll-to-error="false"
    
    <!-- ElementPlus el-form 所有事件 -->
    @validate="handleValidate"
  />
</template>
```

## 注意事项

### 1. 数据绑定
- 使用v-model进行双向数据绑定
- formData对象的属性名必须与schema中的prop对应

### 2. 验证规则
- 可以在schema的rules中定义字段级验证规则
- 也可以通过组件的rules属性定义全局验证规则

### 3. 组件属性透传
- schema中的props会透传给对应的表单组件
- 支持所有ElementPlus组件的原生属性

### 4. 响应式布局
- 使用24栅格系统进行布局
- 通过span和offset控制字段布局

### 5. 动态表单
- 支持通过show属性动态显示/隐藏字段
- 支持运行时修改schema配置

## 常见问题

### Q: 如何自定义表单组件？
A: 在schema的component中指定组件名，通过props传递组件属性

### Q: 如何实现字段联动？
A: 使用computed属性或watch监听其他字段变化，动态修改schema

### Q: 如何处理复杂的验证逻辑？
A: 使用自定义验证器，在rules中定义validator函数

### Q: 如何实现表单的重置？
A: 调用组件的resetFields方法，或直接重置formData对象
