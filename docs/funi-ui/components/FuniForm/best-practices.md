# FuniForm 最佳实践

## 推荐用法

### 1. 标准表单配置
```vue
<template>
  <FuniForm
    v-model="formData"
    :schema="schema"
    :loading="loading"
    label-width="120px"
    @submit="handleSubmit"
    @reset="handleReset"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

// 推荐：使用响应式数据管理表单
const formData = ref({
  name: '',
  email: '',
  phone: '',
  department: '',
  status: 1
})

// 推荐：统一的schema配置
const schema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { 
      placeholder: '请输入姓名',
      clearable: true 
    },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { 
      placeholder: '请输入邮箱',
      clearable: true 
    },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { 
      placeholder: '请输入手机号',
      clearable: true 
    },
    rules: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门'
    },
    rules: [{ required: true, message: '请选择部门', trigger: 'change' }],
    span: 12
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-switch',
    props: {
      activeText: '启用',
      inactiveText: '禁用'
    },
    span: 12
  }
])

const loading = ref(false)

// 推荐：统一的提交处理
const handleSubmit = async (data) => {
  loading.value = true
  try {
    await api.saveUser(data)
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  ElMessage.info('表单已重置')
}
</script>
```

### 2. 搜索表单最佳配置
```vue
<template>
  <FuniForm
    v-model="searchForm"
    :schema="searchSchema"
    inline
    :show-submit="true"
    :show-reset="true"
    submit-text="搜索"
    reset-text="重置"
    :submit-button-props="{ type: 'primary', icon: 'Search' }"
    :reset-button-props="{ icon: 'Refresh' }"
    @submit="handleSearch"
    @reset="handleSearchReset"
  />
</template>

<script setup>
// 推荐：搜索表单的标准配置
const searchForm = ref({
  keyword: '',
  status: '',
  dateRange: [],
  department: ''
})

const searchSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { 
      placeholder: '请输入用户名或邮箱',
      clearable: true 
    }
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { 
      placeholder: '请选择状态',
      clearable: true 
    },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  },
  {
    prop: 'dateRange',
    label: '创建时间',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门',
      clearable: true
    }
  }
])

// 推荐：搜索处理函数
const handleSearch = (searchParams) => {
  // 过滤空值
  const cleanParams = Object.keys(searchParams).reduce((acc, key) => {
    const value = searchParams[key]
    if (value !== '' && value !== null && value !== undefined) {
      // 处理日期范围
      if (key === 'dateRange' && Array.isArray(value) && value.length === 2) {
        acc.startDate = value[0]
        acc.endDate = value[1]
      } else {
        acc[key] = value
      }
    }
    return acc
  }, {})
  
  console.log('搜索参数:', cleanParams)
  // 执行搜索逻辑
}

const handleSearchReset = () => {
  console.log('重置搜索条件')
}
</script>
```

### 3. 动态表单最佳实践
```vue
<script setup>
// 推荐：合理的动态表单设计
const formData = ref({
  userType: 'individual',
  name: '',
  companyName: '',
  contactPerson: '',
  email: '',
  phone: '',
  hasAddress: false,
  address: ''
})

// 推荐：使用函数控制字段显示
const schema = reactive([
  {
    prop: 'userType',
    label: '用户类型',
    component: 'el-radio-group',
    options: [
      { label: '个人用户', value: 'individual' },
      { label: '企业用户', value: 'company' }
    ],
    span: 24
  },
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    show: () => formData.value.userType === 'individual',
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'companyName',
    label: '公司名称',
    component: 'el-input',
    show: () => formData.value.userType === 'company',
    rules: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'address',
    label: '详细地址',
    component: 'el-input',
    props: { type: 'textarea', rows: 3 },
    show: () => formData.value.hasAddress,
    rules: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
    span: 24
  }
])

// 推荐：监听关键字段变化，清空相关数据
watch(() => formData.value.userType, (newType, oldType) => {
  if (newType !== oldType) {
    if (newType === 'individual') {
      formData.value.companyName = ''
      formData.value.contactPerson = ''
    } else {
      formData.value.name = ''
    }
  }
})

watch(() => formData.value.hasAddress, (hasAddress) => {
  if (!hasAddress) {
    formData.value.address = ''
  }
})
</script>
```

## 避免的用法

### 1. 不推荐的配置方式
```vue
<!-- ❌ 避免：直接在模板中写复杂配置 -->
<FuniForm
  :schema="[
    { prop: 'name', label: '姓名', component: 'el-input', rules: [{ required: true }] },
    { prop: 'email', label: '邮箱', component: 'el-input', rules: [{ type: 'email' }] }
  ]"
/>

<!-- ❌ 避免：在模板中写内联函数 -->
<FuniForm
  @submit="(data) => api.save(data)"
  @reset="() => console.log('reset')"
/>

<!-- ❌ 避免：过度复杂的单个表单 -->
<FuniForm
  :schema="[...30个字段的配置]" // 应该考虑分组或分页
/>
```

### 2. 不推荐的数据处理
```vue
<script setup>
// ❌ 避免：在schema中使用复杂的响应式对象
const schema = reactive([
  {
    prop: 'department',
    label: '部门',
    component: 'el-select',
    options: computed(() => { // 不要在schema中使用computed
      return departments.value.map(d => ({ label: d.name, value: d.id }))
    })
  }
])

// ❌ 避免：直接修改props传入的数据
const handleSubmit = (data) => {
  data.id = Date.now() // 不要直接修改传入的数据
  api.save(data)
}

// ❌ 避免：在表单组件内部处理业务逻辑
const handleSubmit = async (data) => {
  await api.save(data)
  router.push('/list') // 应该通过事件通知父组件
  store.commit('updateUser', data) // 不应该在表单组件中操作store
}
</script>
```

### 3. 常见错误和解决方案

#### 错误1：表单验证不生效
```vue
<script setup>
// ❌ 错误：rules配置不正确
const schema = [
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    rules: { required: true } // 应该是数组
  }
]

// ✅ 正确：使用数组配置rules
const schema = [
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    rules: [{ required: true, message: '请输入邮箱', trigger: 'blur' }]
  }
]
</script>
```

#### 错误2：动态字段控制不当
```vue
<script setup>
// ❌ 错误：使用字符串或布尔值控制显示
const schema = [
  {
    prop: 'advancedField',
    label: '高级选项',
    component: 'el-input',
    show: 'showAdvanced' // 错误的方式
  }
]

// ✅ 正确：使用函数控制显示
const schema = [
  {
    prop: 'advancedField',
    label: '高级选项',
    component: 'el-input',
    show: () => formData.value.showAdvanced // 正确的方式
  }
]
</script>
```

#### 错误3：选项数据处理不当
```vue
<script setup>
// ❌ 错误：静态选项数据
const schema = [
  {
    prop: 'department',
    label: '部门',
    component: 'el-select',
    options: [] // 空数组，没有数据
  }
]

// ✅ 正确：动态加载选项数据
const departmentOptions = ref([])

const loadDepartments = async () => {
  try {
    departmentOptions.value = await api.getDepartments()
  } catch (error) {
    console.error('加载部门数据失败:', error)
  }
}

const schema = reactive([
  {
    prop: 'department',
    label: '部门',
    component: 'el-select',
    options: departmentOptions,
    rules: [{ required: true, message: '请选择部门', trigger: 'change' }]
  }
])

onMounted(() => {
  loadDepartments()
})
</script>
```

## 性能优化建议

### 1. 表单字段优化
```vue
<script setup>
// 推荐：合理设置表单字段数量
const schema = reactive([
  // 基础字段（必填）
  { prop: 'name', label: '姓名', component: 'el-input', span: 12 },
  { prop: 'email', label: '邮箱', component: 'el-input', span: 12 },
  
  // 可选字段（条件显示）
  {
    prop: 'advancedField',
    label: '高级选项',
    component: 'el-input',
    show: () => formData.value.showAdvanced,
    span: 24
  }
])

// 推荐：使用防抖处理搜索
import { debounce } from 'lodash-es'

const debouncedSearch = debounce((keyword) => {
  // 执行搜索逻辑
}, 300)
</script>
```

### 2. 数据加载优化
```vue
<script setup>
// 推荐：缓存常用数据
const departmentOptions = ref([])
const roleOptions = ref([])

const loadOptions = async () => {
  try {
    const [departments, roles] = await Promise.all([
      api.getDepartments(),
      api.getRoles()
    ])
    departmentOptions.value = departments
    roleOptions.value = roles
  } catch (error) {
    console.error('加载选项数据失败:', error)
  }
}

// 推荐：只在需要时加载数据
onMounted(() => {
  loadOptions()
})
</script>
```

### 3. 组件复用优化
```vue
<script setup>
// 推荐：提取公共schema配置
const createBaseUserSchema = () => [
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ]
  }
]

// 推荐：根据场景扩展schema
const getUserSchema = (mode = 'create') => {
  const baseSchema = createBaseUserSchema()
  
  if (mode === 'edit') {
    baseSchema.unshift({
      prop: 'id',
      label: 'ID',
      component: 'el-input',
      props: { readonly: true }
    })
  }
  
  return baseSchema
}
</script>
```

## 业务场景最佳实践

### 1. 登录表单
```vue
<template>
  <div class="login-form">
    <FuniForm
      v-model="loginForm"
      :schema="loginSchema"
      :loading="loading"
      label-width="0"
      @submit="handleLogin"
    />
  </div>
</template>

<script setup>
// 登录表单的标准配置
const loginForm = ref({
  username: '',
  password: '',
  remember: false
})

const loginSchema = reactive([
  {
    prop: 'username',
    label: '',
    component: 'el-input',
    props: {
      placeholder: '请输入用户名',
      prefixIcon: 'User',
      clearable: true
    },
    rules: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    span: 24
  },
  {
    prop: 'password',
    label: '',
    component: 'el-input',
    props: {
      type: 'password',
      placeholder: '请输入密码',
      prefixIcon: 'Lock',
      showPassword: true
    },
    rules: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    span: 24
  },
  {
    prop: 'remember',
    label: '',
    component: 'el-checkbox',
    props: { label: '记住密码' },
    span: 24
  }
])
</script>
```

### 2. 设置表单
```vue
<template>
  <FuniForm
    v-model="settingsForm"
    :schema="settingsSchema"
    label-width="120px"
    @submit="handleSaveSettings"
  />
</template>

<script setup>
// 设置表单的标准配置
const settingsSchema = reactive([
  {
    prop: 'siteName',
    label: '网站名称',
    component: 'el-input',
    rules: [{ required: true, message: '请输入网站名称', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'cacheEnabled',
    label: '启用缓存',
    component: 'el-switch',
    span: 12
  },
  {
    prop: 'cacheTime',
    label: '缓存时间',
    component: 'el-input-number',
    props: { min: 60, max: 86400, step: 60 },
    show: () => settingsForm.value.cacheEnabled,
    span: 12
  }
])
</script>
```

### 3. 筛选表单
```vue
<template>
  <FuniForm
    v-model="filterForm"
    :schema="filterSchema"
    inline
    :show-submit="true"
    :show-reset="true"
    submit-text="筛选"
    reset-text="清空"
    @submit="handleFilter"
    @reset="handleClearFilter"
  />
</template>

<script setup>
// 筛选表单的标准配置
const filterSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { placeholder: '请输入关键字', clearable: true }
  },
  {
    prop: 'category',
    label: '分类',
    component: 'el-select',
    props: { placeholder: '请选择分类', clearable: true },
    options: categoryOptions
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: statusOptions
  }
])
</script>
```

## 总结

### 关键原则
1. **配置驱动**：通过schema配置而非硬编码实现表单
2. **数据验证**：合理使用验证规则确保数据质量
3. **用户体验**：提供清晰的反馈和合理的交互
4. **性能优先**：使用条件显示、防抖等优化性能
5. **可维护性**：保持配置的清晰和一致性

### 开发建议
1. 优先使用响应式配置对象
2. 合理设计表单结构和字段
3. 统一处理数据提交和错误处理
4. 遵循ElementPlus的API规范
5. 保持代码的可维护性和一致性
