# FuniForm 使用示例

## 基础使用

### 简单表单
```vue
<template>
  <FuniForm
    v-model="formData"
    :schema="basicSchema"
    @submit="handleSubmit"
    @reset="handleReset"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const formData = ref({
  name: '',
  email: '',
  phone: '',
  gender: '',
  birthday: '',
  address: ''
})

const basicSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { 
      placeholder: '请输入姓名',
      clearable: true 
    },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { 
      placeholder: '请输入邮箱',
      clearable: true 
    },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { 
      placeholder: '请输入手机号',
      clearable: true 
    },
    rules: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'gender',
    label: '性别',
    component: 'el-radio-group',
    options: [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' }
    ],
    span: 12
  },
  {
    prop: 'birthday',
    label: '生日',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '请选择生日',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 12
  },
  {
    prop: 'address',
    label: '地址',
    component: 'el-input',
    props: {
      type: 'textarea',
      placeholder: '请输入详细地址',
      rows: 3
    },
    span: 24
  }
])

const handleSubmit = async (data) => {
  try {
    console.log('提交数据:', data)
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('提交成功')
  } catch (error) {
    ElMessage.error('提交失败')
  }
}

const handleReset = () => {
  console.log('重置表单')
  ElMessage.info('表单已重置')
}
</script>
```

### 行内表单
```vue
<template>
  <FuniForm
    v-model="searchForm"
    :schema="searchSchema"
    inline
    :show-submit="true"
    :show-reset="true"
    submit-text="搜索"
    reset-text="重置"
    @submit="handleSearch"
    @reset="handleSearchReset"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const searchForm = ref({
  keyword: '',
  status: '',
  dateRange: []
})

const searchSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { 
      placeholder: '请输入关键字',
      clearable: true 
    }
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { 
      placeholder: '请选择状态',
      clearable: true 
    },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    }
  }
])

const handleSearch = (data) => {
  console.log('搜索条件:', data)
  // 处理搜索逻辑
}

const handleSearchReset = () => {
  console.log('重置搜索条件')
}
</script>
```

## 高级配置

### 复杂表单验证
```vue
<template>
  <FuniForm
    v-model="userForm"
    :schema="userSchema"
    :rules="formRules"
    label-width="120px"
    @submit="handleUserSubmit"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const userForm = ref({
  username: '',
  password: '',
  confirmPassword: '',
  email: '',
  phone: '',
  department: '',
  roles: [],
  agreement: false
})

const userSchema = reactive([
  {
    prop: 'username',
    label: '用户名',
    component: 'el-input',
    props: { placeholder: '请输入用户名' },
    span: 12
  },
  {
    prop: 'password',
    label: '密码',
    component: 'el-input',
    props: { 
      type: 'password',
      placeholder: '请输入密码',
      showPassword: true 
    },
    span: 12
  },
  {
    prop: 'confirmPassword',
    label: '确认密码',
    component: 'el-input',
    props: { 
      type: 'password',
      placeholder: '请再次输入密码',
      showPassword: true 
    },
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号' },
    span: 12
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门'
    },
    span: 12
  },
  {
    prop: 'roles',
    label: '角色',
    component: 'el-checkbox-group',
    options: [
      { label: '管理员', value: 'admin' },
      { label: '编辑者', value: 'editor' },
      { label: '查看者', value: 'viewer' }
    ],
    span: 24
  },
  {
    prop: 'agreement',
    label: '用户协议',
    component: 'el-checkbox',
    props: { label: '我已阅读并同意用户协议' },
    span: 24
  }
])

// 自定义验证规则
const validatePassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== userForm.value.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const formRules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' }
  ],
  password: [
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  roles: [
    { type: 'array', required: true, message: '请至少选择一个角色', trigger: 'change' }
  ],
  agreement: [
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请同意用户协议'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
})

const handleUserSubmit = async (data) => {
  console.log('用户表单提交:', data)
}
</script>
```

### 动态表单
```vue
<template>
  <FuniForm
    v-model="dynamicForm"
    :schema="dynamicSchema"
    @submit="handleDynamicSubmit"
  />
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

const dynamicForm = ref({
  type: 'personal',
  name: '',
  companyName: '',
  contactPerson: '',
  email: '',
  phone: '',
  hasAddress: false,
  address: ''
})

// 动态schema，根据表单数据变化
const dynamicSchema = reactive([
  {
    prop: 'type',
    label: '类型',
    component: 'el-radio-group',
    options: [
      { label: '个人', value: 'personal' },
      { label: '企业', value: 'company' }
    ],
    span: 24
  },
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    show: () => dynamicForm.value.type === 'personal',
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'companyName',
    label: '公司名称',
    component: 'el-input',
    props: { placeholder: '请输入公司名称' },
    show: () => dynamicForm.value.type === 'company',
    rules: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'contactPerson',
    label: '联系人',
    component: 'el-input',
    props: { placeholder: '请输入联系人' },
    show: () => dynamicForm.value.type === 'company',
    rules: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'phone',
    label: '联系电话',
    component: 'el-input',
    props: { placeholder: '请输入联系电话' },
    rules: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'hasAddress',
    label: '填写地址',
    component: 'el-switch',
    props: { activeText: '是', inactiveText: '否' },
    span: 12
  },
  {
    prop: 'address',
    label: '详细地址',
    component: 'el-input',
    props: { 
      type: 'textarea',
      placeholder: '请输入详细地址',
      rows: 3 
    },
    show: () => dynamicForm.value.hasAddress,
    rules: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
    span: 24
  }
])

// 监听类型变化，清空相关字段
watch(() => dynamicForm.value.type, (newType, oldType) => {
  if (newType !== oldType) {
    if (newType === 'personal') {
      dynamicForm.value.companyName = ''
      dynamicForm.value.contactPerson = ''
    } else {
      dynamicForm.value.name = ''
    }
  }
})

// 监听地址开关，清空地址字段
watch(() => dynamicForm.value.hasAddress, (hasAddress) => {
  if (!hasAddress) {
    dynamicForm.value.address = ''
  }
})

const handleDynamicSubmit = async (data) => {
  console.log('动态表单提交:', data)
}
</script>
```

## 业务场景示例

### 用户注册表单
```vue
<template>
  <div class="register-form">
    <h2>用户注册</h2>
    <FuniForm
      v-model="registerForm"
      :schema="registerSchema"
      :loading="loading"
      label-width="100px"
      @submit="handleRegister"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const registerForm = ref({
  username: '',
  password: '',
  confirmPassword: '',
  email: '',
  phone: '',
  verifyCode: '',
  agreement: false
})

const registerSchema = reactive([
  {
    prop: 'username',
    label: '用户名',
    component: 'el-input',
    props: { 
      placeholder: '请输入用户名',
      clearable: true 
    },
    rules: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
    ],
    span: 24
  },
  {
    prop: 'password',
    label: '密码',
    component: 'el-input',
    props: { 
      type: 'password',
      placeholder: '请输入密码',
      showPassword: true 
    },
    rules: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' }
    ],
    span: 24
  },
  {
    prop: 'confirmPassword',
    label: '确认密码',
    component: 'el-input',
    props: { 
      type: 'password',
      placeholder: '请再次输入密码',
      showPassword: true 
    },
    rules: [
      { required: true, message: '请再次输入密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== registerForm.value.password) {
            callback(new Error('两次输入密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    span: 24
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { 
      placeholder: '请输入邮箱',
      clearable: true 
    },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    span: 24
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { 
      placeholder: '请输入手机号',
      clearable: true 
    },
    rules: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    span: 18
  },
  {
    prop: 'verifyCode',
    label: '验证码',
    component: 'el-input',
    props: { 
      placeholder: '请输入验证码',
      maxlength: 6 
    },
    rules: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { len: 6, message: '验证码为6位数字', trigger: 'blur' }
    ],
    span: 12,
    suffix: () => h('el-button', {
      type: 'text',
      onClick: sendVerifyCode
    }, '发送验证码')
  },
  {
    prop: 'agreement',
    label: '',
    component: 'el-checkbox',
    props: { 
      label: '我已阅读并同意《用户协议》和《隐私政策》' 
    },
    rules: [
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback(new Error('请同意用户协议'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
    span: 24
  }
])

const sendVerifyCode = async () => {
  if (!registerForm.value.phone) {
    ElMessage.warning('请先输入手机号')
    return
  }
  
  try {
    // 发送验证码逻辑
    await api.sendVerifyCode(registerForm.value.phone)
    ElMessage.success('验证码已发送')
  } catch (error) {
    ElMessage.error('验证码发送失败')
  }
}

const handleRegister = async (data) => {
  loading.value = true
  try {
    await api.register(data)
    ElMessage.success('注册成功')
    // 跳转到登录页面
    router.push('/login')
  } catch (error) {
    ElMessage.error('注册失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-form {
  max-width: 400px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.register-form h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #303133;
}
</style>
```

### 设置页面表单
```vue
<template>
  <div class="settings-page">
    <h2>系统设置</h2>
    <FuniForm
      v-model="settingsForm"
      :schema="settingsSchema"
      label-width="150px"
      @submit="handleSaveSettings"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

const settingsForm = ref({
  siteName: '',
  siteUrl: '',
  logo: '',
  favicon: '',
  description: '',
  keywords: '',
  emailHost: '',
  emailPort: 587,
  emailUser: '',
  emailPassword: '',
  emailSsl: true,
  cacheEnabled: true,
  cacheTime: 3600,
  debugMode: false,
  maintenanceMode: false
})

const settingsSchema = reactive([
  // 基本设置
  {
    prop: 'siteName',
    label: '网站名称',
    component: 'el-input',
    props: { placeholder: '请输入网站名称' },
    rules: [{ required: true, message: '请输入网站名称', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'siteUrl',
    label: '网站地址',
    component: 'el-input',
    props: { placeholder: '请输入网站地址' },
    rules: [
      { required: true, message: '请输入网站地址', trigger: 'blur' },
      { type: 'url', message: '请输入正确的网址格式', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'logo',
    label: '网站Logo',
    component: 'el-upload',
    props: {
      action: '/api/upload',
      listType: 'picture-card',
      limit: 1
    },
    span: 12
  },
  {
    prop: 'favicon',
    label: '网站图标',
    component: 'el-upload',
    props: {
      action: '/api/upload',
      listType: 'picture-card',
      limit: 1
    },
    span: 12
  },
  {
    prop: 'description',
    label: '网站描述',
    component: 'el-input',
    props: {
      type: 'textarea',
      placeholder: '请输入网站描述',
      rows: 3
    },
    span: 24
  },
  {
    prop: 'keywords',
    label: '关键词',
    component: 'el-input',
    props: {
      placeholder: '请输入关键词，多个关键词用逗号分隔'
    },
    span: 24
  },
  
  // 邮件设置
  {
    prop: 'emailHost',
    label: '邮件服务器',
    component: 'el-input',
    props: { placeholder: '请输入邮件服务器地址' },
    span: 12
  },
  {
    prop: 'emailPort',
    label: '端口号',
    component: 'el-input-number',
    props: { min: 1, max: 65535 },
    span: 12
  },
  {
    prop: 'emailUser',
    label: '邮箱账号',
    component: 'el-input',
    props: { placeholder: '请输入邮箱账号' },
    span: 12
  },
  {
    prop: 'emailPassword',
    label: '邮箱密码',
    component: 'el-input',
    props: { 
      type: 'password',
      placeholder: '请输入邮箱密码',
      showPassword: true 
    },
    span: 12
  },
  {
    prop: 'emailSsl',
    label: '启用SSL',
    component: 'el-switch',
    props: { activeText: '是', inactiveText: '否' },
    span: 12
  },
  
  // 系统设置
  {
    prop: 'cacheEnabled',
    label: '启用缓存',
    component: 'el-switch',
    props: { activeText: '是', inactiveText: '否' },
    span: 12
  },
  {
    prop: 'cacheTime',
    label: '缓存时间',
    component: 'el-input-number',
    props: { 
      min: 60,
      max: 86400,
      step: 60,
      placeholder: '秒'
    },
    show: () => settingsForm.value.cacheEnabled,
    span: 12
  },
  {
    prop: 'debugMode',
    label: '调试模式',
    component: 'el-switch',
    props: { activeText: '开启', inactiveText: '关闭' },
    span: 12
  },
  {
    prop: 'maintenanceMode',
    label: '维护模式',
    component: 'el-switch',
    props: { activeText: '开启', inactiveText: '关闭' },
    span: 12
  }
])

onMounted(async () => {
  // 加载设置数据
  try {
    const settings = await api.getSettings()
    Object.assign(settingsForm.value, settings)
  } catch (error) {
    console.error('加载设置失败:', error)
  }
})

const handleSaveSettings = async (data) => {
  try {
    await api.saveSettings(data)
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('设置保存失败')
  }
}
</script>

<style scoped>
.settings-page {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.settings-page h2 {
  margin-bottom: 20px;
  color: #303133;
}
</style>
```

## 注意事项

### 1. 数据绑定
- 使用v-model进行双向数据绑定
- 确保formData的结构与schema中的prop对应
- 注意数据类型的一致性

### 2. 表单验证
- 在schema中配置rules或使用独立的rules对象
- 支持ElementPlus的所有验证规则
- 可以使用自定义验证函数

### 3. 组件配置
- 通过props传递组件属性
- 通过options配置选择器选项
- 支持所有ElementPlus表单组件

### 4. 动态表单
- 使用show函数控制字段显示
- 监听数据变化清空相关字段
- 合理使用计算属性优化性能

### 5. 性能优化
- 避免在schema中使用复杂的响应式对象
- 合理使用条件显示减少渲染
- 大表单时考虑分组或分页
