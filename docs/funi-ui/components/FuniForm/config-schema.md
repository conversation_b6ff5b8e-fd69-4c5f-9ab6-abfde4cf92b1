# FuniForm 配置结构

## 基础配置结构

```typescript
interface FuniFormConfig {
  // 表单数据
  modelValue: Record<string, any>;
  
  // 表单配置
  schema: FormSchemaItem[];
  rules?: Record<string, FormRule[]>;
  
  // 布局配置
  labelWidth?: string;
  labelPosition?: 'left' | 'right' | 'top';
  inline?: boolean;
  size?: 'large' | 'default' | 'small';
  
  // 状态配置
  disabled?: boolean;
  readonly?: boolean;
  
  // 验证配置
  showMessage?: boolean;
  inlineMessage?: boolean;
  statusIcon?: boolean;
  validateOnRuleChange?: boolean;
  hideRequiredAsterisk?: boolean;
  scrollToError?: boolean;
}
```

## 表单项配置结构

```typescript
interface FormSchemaItem {
  // 基础信息
  prop: string;                    // 字段名（必填）
  label: string;                   // 字段标签（必填）
  component: string;               // 组件类型（必填）
  
  // 组件配置
  props?: ComponentProps;          // 组件属性
  slots?: ComponentSlots;          // 组件插槽
  
  // 布局配置
  span?: number;                   // 栅格占据列数（1-24）
  offset?: number;                 // 栅格左侧间隔格数
  
  // 表单项配置
  labelWidth?: string;             // 标签宽度
  required?: boolean;              // 是否必填
  error?: string;                  // 错误提示文本
  showMessage?: boolean;           // 是否显示校验错误信息
  inlineMessage?: boolean;         // 是否行内显示校验信息
  size?: 'large' | 'default' | 'small'; // 表单项尺寸
  
  // 验证规则
  rules?: FormRule[];              // 字段验证规则
  
  // 显示控制
  show?: boolean | ComputedRef<boolean>; // 是否显示
  
  // 其他配置
  [key: string]: any;              // 扩展配置
}
```

## 组件属性配置

### 输入类组件属性
```typescript
// el-input 属性
interface InputProps {
  type?: 'text' | 'textarea' | 'password' | 'number' | 'email' | 'url' | 'tel';
  placeholder?: string;
  clearable?: boolean;
  showPassword?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  maxlength?: number;
  minlength?: number;
  showWordLimit?: boolean;
  prefixIcon?: string;
  suffixIcon?: string;
  rows?: number;                   // textarea专用
  autosize?: boolean | { minRows?: number; maxRows?: number }; // textarea专用
  resize?: 'none' | 'both' | 'horizontal' | 'vertical'; // textarea专用
}

// el-input-number 属性
interface InputNumberProps {
  min?: number;
  max?: number;
  step?: number;
  stepStrictly?: boolean;
  precision?: number;
  size?: 'large' | 'default' | 'small';
  disabled?: boolean;
  controls?: boolean;
  controlsPosition?: 'right';
  placeholder?: string;
}
```

### 选择类组件属性
```typescript
// el-select 属性
interface SelectProps {
  multiple?: boolean;
  disabled?: boolean;
  valueKey?: string;
  size?: 'large' | 'default' | 'small';
  clearable?: boolean;
  collapseTags?: boolean;
  collapseTagsTooltip?: boolean;
  multipleLimit?: number;
  placeholder?: string;
  filterable?: boolean;
  allowCreate?: boolean;
  filterMethod?: Function;
  remote?: boolean;
  remoteMethod?: Function;
  loading?: boolean;
  loadingText?: string;
  noMatchText?: string;
  noDataText?: string;
  popperClass?: string;
  reserveKeyword?: boolean;
  defaultFirstOption?: boolean;
  teleported?: boolean;
  persistent?: boolean;
  automaticDropdown?: boolean;
  clearIcon?: string;
  fitInputWidth?: boolean;
  suffixIcon?: string;
  tagType?: 'success' | 'info' | 'warning' | 'danger';
  validateEvent?: boolean;
  options?: OptionItem[];          // 选项数据
}

// el-cascader 属性
interface CascaderProps {
  options?: CascaderOption[];
  props?: CascaderProps;
  size?: 'large' | 'default' | 'small';
  placeholder?: string;
  disabled?: boolean;
  clearable?: boolean;
  showAllLevels?: boolean;
  collapseTags?: boolean;
  collapseTagsTooltip?: boolean;
  separator?: string;
  filterable?: boolean;
  filterMethod?: Function;
  debounce?: number;
  beforeFilter?: Function;
  popperClass?: string;
  teleported?: boolean;
  tagType?: 'success' | 'info' | 'warning' | 'danger';
  validateEvent?: boolean;
}
```

### 日期时间组件属性
```typescript
// el-date-picker 属性
interface DatePickerProps {
  type?: 'year' | 'month' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange';
  format?: string;
  valueFormat?: string;
  readonly?: boolean;
  disabled?: boolean;
  size?: 'large' | 'default' | 'small';
  editable?: boolean;
  clearable?: boolean;
  placeholder?: string;
  startPlaceholder?: string;
  endPlaceholder?: string;
  rangeSeparator?: string;
  defaultValue?: Date | Date[];
  defaultTime?: Date | Date[];
  disabledDate?: Function;
  shortcuts?: Array<{ text: string; value: Date | Function }>;
  cellClassName?: Function;
  teleported?: boolean;
  popperClass?: string;
  unlinkPanels?: boolean;
  validateEvent?: boolean;
}
```

## 验证规则配置

```typescript
interface FormRule {
  // 基础验证
  required?: boolean;              // 是否必填
  message?: string;                // 错误提示信息
  trigger?: 'blur' | 'change' | string[]; // 触发方式
  
  // 类型验证
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email';
  
  // 长度验证
  min?: number;                    // 最小长度
  max?: number;                    // 最大长度
  len?: number;                    // 精确长度
  
  // 模式验证
  pattern?: RegExp;                // 正则表达式
  
  // 自定义验证
  validator?: (rule: any, value: any, callback: Function) => void;
  
  // 异步验证
  asyncValidator?: (rule: any, value: any, callback: Function) => Promise<void>;
  
  // 其他配置
  whitespace?: boolean;            // 是否将空格视为错误
  fields?: Record<string, FormRule>; // 对象或数组类型的字段验证规则
  defaultField?: FormRule;         // 数组类型的默认验证规则
  transform?: (value: any) => any; // 验证前转换值
  [key: string]: any;              // 其他自定义配置
}
```

## 常用配置组合

### 用户信息表单
```typescript
const userFormSchema: FormSchemaItem[] = [
  {
    prop: 'username',
    label: '用户名',
    component: 'el-input',
    props: {
      placeholder: '请输入用户名',
      clearable: true,
      maxlength: 20,
      showWordLimit: true
    },
    span: 12,
    rules: [
      { required: true, message: '用户名不能为空', trigger: 'blur' },
      { min: 3, max: 20, message: '长度在3到20个字符', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
    ]
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: {
      type: 'email',
      placeholder: '请输入邮箱地址',
      clearable: true
    },
    span: 12,
    rules: [
      { required: true, message: '邮箱不能为空', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: {
      placeholder: '请输入手机号',
      clearable: true,
      maxlength: 11
    },
    span: 12,
    rules: [
      { required: true, message: '手机号不能为空', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ]
  },
  {
    prop: 'age',
    label: '年龄',
    component: 'el-input-number',
    props: {
      min: 1,
      max: 120,
      placeholder: '请输入年龄'
    },
    span: 12,
    rules: [
      { required: true, message: '年龄不能为空', trigger: 'blur' },
      { type: 'number', min: 1, max: 120, message: '年龄必须在1-120之间', trigger: 'blur' }
    ]
  }
];
```

### 地址信息表单
```typescript
const addressFormSchema: FormSchemaItem[] = [
  {
    prop: 'region',
    label: '所在地区',
    component: 'el-cascader',
    props: {
      options: regionOptions,
      placeholder: '请选择省市区',
      clearable: true,
      showAllLevels: false,
      props: {
        value: 'code',
        label: 'name',
        children: 'children'
      }
    },
    span: 12,
    rules: [
      { required: true, message: '请选择所在地区', trigger: 'change' }
    ]
  },
  {
    prop: 'address',
    label: '详细地址',
    component: 'el-input',
    props: {
      type: 'textarea',
      placeholder: '请输入详细地址',
      rows: 3,
      maxlength: 200,
      showWordLimit: true
    },
    span: 24,
    rules: [
      { required: true, message: '详细地址不能为空', trigger: 'blur' },
      { min: 5, max: 200, message: '地址长度在5到200个字符', trigger: 'blur' }
    ]
  }
];
```

### 时间范围表单
```typescript
const timeRangeFormSchema: FormSchemaItem[] = [
  {
    prop: 'startDate',
    label: '开始日期',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '请选择开始日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      clearable: true
    },
    span: 12,
    rules: [
      { required: true, message: '请选择开始日期', trigger: 'change' }
    ]
  },
  {
    prop: 'endDate',
    label: '结束日期',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '请选择结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      clearable: true
    },
    span: 12,
    rules: [
      { required: true, message: '请选择结束日期', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          const startDate = formData.startDate;
          if (startDate && value && new Date(value) < new Date(startDate)) {
            callback(new Error('结束日期不能早于开始日期'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ]
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      clearable: true
    },
    span: 24,
    rules: [
      { required: true, message: '请选择日期范围', trigger: 'change' }
    ]
  }
];
```

## 动态表单配置

### 条件显示配置
```typescript
const conditionalFormSchema = computed(() => [
  {
    prop: 'userType',
    label: '用户类型',
    component: 'el-radio-group',
    props: {
      options: [
        { label: '个人用户', value: 'personal' },
        { label: '企业用户', value: 'enterprise' }
      ]
    },
    span: 24
  },
  {
    prop: 'personalInfo',
    label: '个人信息',
    component: 'el-input',
    props: {
      placeholder: '请输入个人信息'
    },
    span: 24,
    show: computed(() => formData.value.userType === 'personal')
  },
  {
    prop: 'companyName',
    label: '公司名称',
    component: 'el-input',
    props: {
      placeholder: '请输入公司名称'
    },
    span: 12,
    show: computed(() => formData.value.userType === 'enterprise')
  },
  {
    prop: 'businessLicense',
    label: '营业执照',
    component: 'el-input',
    props: {
      placeholder: '请输入营业执照号'
    },
    span: 12,
    show: computed(() => formData.value.userType === 'enterprise')
  }
]);
```

## 最佳实践建议

### 1. 表单布局
- 使用24栅格系统进行响应式布局
- 相关字段放在同一行，提高表单紧凑性
- 重要字段放在前面，次要字段放在后面

### 2. 验证规则
- 必填字段使用required规则
- 格式验证使用pattern或type规则
- 复杂验证使用validator自定义函数
- 异步验证使用asyncValidator

### 3. 用户体验
- 提供清晰的placeholder提示
- 使用clearable属性方便用户清空输入
- 长文本使用textarea组件
- 数字输入使用input-number组件

### 4. 性能优化
- 大表单使用v-show而不是v-if控制显示
- 复杂计算使用computed缓存
- 避免在schema中使用复杂的响应式对象
