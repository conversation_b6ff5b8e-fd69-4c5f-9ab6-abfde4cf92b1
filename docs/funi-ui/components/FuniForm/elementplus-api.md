# FuniForm ElementPlus API支持

## 基础组件

FuniForm基于ElementPlus的以下组件进行封装：
- **el-form** - 主要表单容器
- **el-form-item** - 表单项容器
- **el-row/el-col** - 栅格布局系统
- **el-button** - 提交和重置按钮

## 支持的ElementPlus API

### el-form API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| model | Object | 表单数据对象 | 自动处理 |
| rules | Object | 表单验证规则 | v-bind透传 |
| inline | Boolean | 行内表单模式 | v-bind透传 |
| label-position | String | 标签位置 | v-bind透传 |
| label-width | String | 标签宽度 | v-bind透传 |
| label-suffix | String | 标签后缀 | v-bind透传 |
| hide-required-asterisk | Boolean | 隐藏必填星号 | v-bind透传 |
| show-message | Boolean | 显示校验信息 | v-bind透传 |
| inline-message | Boolean | 行内显示校验信息 | v-bind透传 |
| status-icon | Boolean | 显示校验状态图标 | v-bind透传 |
| validate-on-rule-change | Boolean | 规则改变时触发校验 | v-bind透传 |
| size | String | 组件尺寸 | v-bind透传 |
| disabled | Boolean | 是否禁用 | v-bind透传 |
| scroll-to-error | Boolean | 校验失败时滚动到错误字段 | v-bind透传 |
| scroll-into-view-options | Object/Boolean | 滚动行为配置 | v-bind透传 |

### el-form Events透传

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| validate | prop, isValid, message | 表单项校验事件 | @validate |

### el-form Methods透传

| 方法名 | 参数 | 返回值 | 说明 | 透传方式 |
|--------|------|--------|------|---------|
| validate | callback | Promise | 验证整个表单 | ref调用 |
| validateField | props, callback | Promise | 验证指定字段 | ref调用 |
| resetFields | props | void | 重置表单字段 | ref调用 |
| scrollToField | prop | void | 滚动到指定字段 | ref调用 |
| clearValidate | props | void | 清除验证信息 | ref调用 |

### el-form-item API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| prop | String | 字段名 | 通过schema配置 |
| label | String | 标签文本 | 通过schema配置 |
| label-width | String | 标签宽度 | 通过schema配置 |
| required | Boolean | 是否必填 | 通过schema配置 |
| rules | Object/Array | 验证规则 | 通过schema配置 |
| error | String | 表单域验证错误信息 | 通过schema配置 |
| show-message | Boolean | 显示校验错误信息 | 通过schema配置 |
| inline-message | Boolean | 行内显示校验信息 | 通过schema配置 |
| size | String | 组件尺寸 | 通过schema配置 |

### el-row/el-col API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| gutter | Number | 栅格间隔 | 通过gutter属性 |
| type | String | 布局模式 | 通过layout配置 |
| justify | String | 水平排列方式 | 通过layout配置 |
| align | String | 垂直排列方式 | 通过layout配置 |
| tag | String | 自定义元素标签 | 通过layout配置 |
| span | Number | 栅格占据列数 | 通过schema配置 |
| offset | Number | 栅格左侧间隔格数 | 通过schema配置 |
| push | Number | 栅格向右移动格数 | 通过schema配置 |
| pull | Number | 栅格向左移动格数 | 通过schema配置 |
| xs | Number/Object | 超小屏幕配置 | 通过schema配置 |
| sm | Number/Object | 小屏幕配置 | 通过schema配置 |
| md | Number/Object | 中等屏幕配置 | 通过schema配置 |
| lg | Number/Object | 大屏幕配置 | 通过schema配置 |
| xl | Number/Object | 超大屏幕配置 | 通过schema配置 |

### el-button API透传（提交/重置按钮）

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| type | String | 按钮类型 | 通过submitButtonProps/resetButtonProps |
| size | String | 按钮尺寸 | 通过submitButtonProps/resetButtonProps |
| plain | Boolean | 朴素按钮 | 通过submitButtonProps/resetButtonProps |
| round | Boolean | 圆角按钮 | 通过submitButtonProps/resetButtonProps |
| circle | Boolean | 圆形按钮 | 通过submitButtonProps/resetButtonProps |
| loading | Boolean | 加载状态 | 通过submitButtonProps/resetButtonProps |
| disabled | Boolean | 禁用状态 | 通过submitButtonProps/resetButtonProps |
| icon | String | 图标 | 通过submitButtonProps/resetButtonProps |
| autofocus | Boolean | 自动聚焦 | 通过submitButtonProps/resetButtonProps |
| native-type | String | 原生type属性 | 通过submitButtonProps/resetButtonProps |

## 使用方式

### 基础表单使用
```vue
<template>
  <FuniForm
    v-model="formData"
    :schema="schema"
    
    <!-- ElementPlus el-form 属性透传 -->
    :rules="rules"
    label-width="120px"
    label-position="right"
    :show-message="true"
    :status-icon="true"
    :inline="false"
    
    <!-- ElementPlus el-form 事件透传 -->
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = ref({
  name: '',
  email: '',
  phone: ''
})

const rules = reactive({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
})

const schema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号' },
    span: 24
  }
])

const handleValidate = (prop, isValid, message) => {
  console.log('字段验证:', { prop, isValid, message })
}
</script>
```

### 行内表单使用
```vue
<template>
  <FuniForm
    v-model="searchData"
    :schema="searchSchema"
    
    <!-- ElementPlus el-form 行内模式 -->
    inline
    label-width="80px"
    
    <!-- 按钮配置 -->
    :show-submit="true"
    :show-reset="true"
    submit-text="搜索"
    reset-text="重置"
    :submit-button-props="{ type: 'primary', icon: 'Search' }"
    :reset-button-props="{ type: 'default', icon: 'Refresh' }"
  />
</template>

<script setup>
const searchData = ref({
  keyword: '',
  status: '',
  dateRange: []
})

const searchSchema = reactive([
  {
    prop: 'keyword',
    label: '关键字',
    component: 'el-input',
    props: { placeholder: '请输入关键字', clearable: true }
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: { placeholder: '请选择状态', clearable: true },
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  },
  {
    prop: 'dateRange',
    label: '日期',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期'
    }
  }
])
</script>
```

### 响应式布局使用
```vue
<template>
  <FuniForm
    v-model="formData"
    :schema="schema"
    
    <!-- ElementPlus el-row 属性透传 -->
    :gutter="20"
    justify="start"
    align="top"
  />
</template>

<script setup>
const schema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    // ElementPlus el-col 配置
    span: 24,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    xl: 6
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    span: 24,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    xl: 6,
    offset: 0,
    push: 0,
    pull: 0
  }
])
</script>
```

### 表单方法调用
```vue
<template>
  <FuniForm
    ref="formRef"
    v-model="formData"
    :schema="schema"
  />
  
  <div class="form-actions">
    <el-button @click="validateForm">验证表单</el-button>
    <el-button @click="resetForm">重置表单</el-button>
    <el-button @click="validateField">验证单个字段</el-button>
    <el-button @click="clearValidate">清除验证</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const formRef = ref()

// 调用ElementPlus el-form方法
const validateForm = async () => {
  try {
    const valid = await formRef.value.validate()
    console.log('表单验证结果:', valid)
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const resetForm = () => {
  formRef.value.resetFields()
}

const validateField = async () => {
  try {
    const valid = await formRef.value.validateField(['name', 'email'])
    console.log('字段验证结果:', valid)
  } catch (error) {
    console.log('字段验证失败:', error)
  }
}

const clearValidate = () => {
  formRef.value.clearValidate(['name', 'email'])
}
</script>
```

### 自定义验证规则
```vue
<template>
  <FuniForm
    v-model="formData"
    :schema="schema"
    :rules="customRules"
    
    <!-- ElementPlus 验证相关属性 -->
    :validate-on-rule-change="true"
    :show-message="true"
    :inline-message="false"
    :status-icon="true"
    :scroll-to-error="true"
  />
</template>

<script setup>
// 自定义验证函数
const validateAge = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入年龄'))
  } else if (!Number.isInteger(value)) {
    callback(new Error('年龄必须为数字'))
  } else if (value < 18 || value > 65) {
    callback(new Error('年龄必须在18-65之间'))
  } else {
    callback()
  }
}

const validatePhone = (rule, value, callback) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  if (value === '') {
    callback(new Error('请输入手机号'))
  } else if (!phoneRegex.test(value)) {
    callback(new Error('请输入正确的手机号格式'))
  } else {
    callback()
  }
}

// ElementPlus 验证规则配置
const customRules = reactive({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度在2到10个字符', trigger: 'blur' }
  ],
  age: [
    { validator: validateAge, trigger: 'blur' }
  ],
  phone: [
    { validator: validatePhone, trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
})

const schema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    span: 12
  },
  {
    prop: 'age',
    label: '年龄',
    component: 'el-input-number',
    props: { min: 1, max: 100 },
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    span: 12
  }
])
</script>
```

## 注意事项

### 1. API透传机制
- 所有ElementPlus el-form的属性都通过v-bind自动透传
- 表单项相关API通过schema配置传递
- 布局相关API通过gutter属性或直接透传
- 按钮相关API通过submitButtonProps和resetButtonProps传递

### 2. 方法调用
- 所有ElementPlus el-form的方法都可以通过组件ref调用
- 方法调用方式与原生el-form完全一致
- 支持Promise和callback两种调用方式

### 3. 事件处理
- 所有ElementPlus相关事件都可以正常监听
- 事件参数与原生ElementPlus组件一致
- 组件内部处理不会影响外部事件监听

### 4. 验证规则
- 支持在schema中配置rules或使用独立的rules对象
- 完全兼容ElementPlus的验证规则语法
- 支持自定义验证函数和异步验证

### 5. 样式定制
- 可以通过ElementPlus的样式相关属性定制外观
- 支持通过CSS变量进行主题定制
- 可以通过深度选择器进一步定制样式

### 6. 响应式支持
- 完全支持ElementPlus的响应式栅格系统
- 可以为每个表单项单独配置响应式参数
- 支持断点配置和自适应布局

### 7. 兼容性
- 完全兼容ElementPlus el-form的所有功能
- 新增功能不会影响原有ElementPlus API的使用
- 可以无缝迁移现有的el-form代码

### 8. 性能考虑
- 大表单时建议合理设置栅格布局
- 避免在schema中使用复杂的响应式对象
- 使用条件显示优化渲染性能
- 合理使用表单验证的触发时机
