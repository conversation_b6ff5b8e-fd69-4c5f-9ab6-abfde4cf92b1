# FuniListPage 最佳实践

## 使用场景

### 适用场景

1. **多标签页列表**: 需要在同一页面展示多个相关的数据列表
2. **传统列表页**: 不需要FuniListPageV2的高级功能，使用简单的列表展示
3. **兼容性需求**: 需要兼容旧版本的项目
4. **自定义标签页**: 需要混合表格和自定义内容的标签页

### 不适用场景

1. **新项目**: 建议使用FuniListPageV2，功能更完善
2. **单一列表**: 只有一个列表时，直接使用FuniCurd更合适
3. **复杂搜索**: 需要高级搜索功能时，建议使用FuniListPageV2
4. **大数据量**: 需要虚拟滚动等性能优化时，建议使用FuniCurdV2

## 配置最佳实践

### 1. 标签页配置优化

```javascript
// ✅ 推荐：清晰的标签页结构
const cardTabConfig = [
  {
    label: '活跃用户',
    key: 'active-users',
    api: '/api/users',
    requestParams: { status: 'active' },
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80, sortable: true },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '最后登录', prop: 'lastLogin', width: 180 }
      ]
    }
  },
  {
    label: '禁用用户',
    key: 'disabled-users',
    api: '/api/users',
    requestParams: { status: 'disabled' },
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80 },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '禁用时间', prop: 'disabledAt', width: 180 }
      ]
    }
  }
]

// ❌ 不推荐：配置过于复杂
const complexConfig = [
  {
    label: '复杂标签页',
    key: 'complex',
    api: '/api/complex',
    curdOption: {
      columns: [
        // 避免过多列配置
        ...Array.from({ length: 20 }, (_, i) => ({
          label: `列${i}`,
          prop: `field${i}`,
          width: 100
        }))
      ]
    }
  }
]
```

### 2. 搜索参数处理

```javascript
// ✅ 推荐：合理的搜索参数转换
const searchParamsHandler = {
  fixSearchParams: (searchParams) => {
    const processed = { ...searchParams }
    
    // 日期范围处理
    if (searchParams.dateRange && searchParams.dateRange.length === 2) {
      processed.startDate = searchParams.dateRange[0]
      processed.endDate = searchParams.dateRange[1]
      delete processed.dateRange
    }
    
    // 空值过滤
    Object.keys(processed).forEach(key => {
      if (processed[key] === '' || processed[key] == null) {
        delete processed[key]
      }
    })
    
    return processed
  }
}

// ❌ 不推荐：复杂的参数处理逻辑
const complexHandler = {
  fixSearchParams: (searchParams) => {
    // 避免在这里进行复杂的业务逻辑处理
    return heavyProcessing(searchParams)
  }
}
```

### 3. 数据回调优化

```javascript
// ✅ 推荐：简洁的数据处理
const dataCallback = ({ response, params }) => {
  return {
    list: response.data.map(item => ({
      ...item,
      // 简单的数据预处理
      displayName: `${item.firstName} ${item.lastName}`,
      statusText: getStatusText(item.status)
    })),
    total: response.total,
    pageNum: response.pageNum,
    pageSize: response.pageSize
  }
}

// ❌ 不推荐：复杂的数据处理
const complexCallback = ({ response, params }) => {
  // 避免在回调中进行复杂计算
  return {
    list: response.data.map(item => ({
      ...item,
      complexField: heavyComputation(item)
    })),
    total: response.total
  }
}
```

## 性能优化最佳实践

### 1. 列配置优化

```javascript
// ✅ 推荐：合理的列配置
const optimizedColumns = [
  { label: 'ID', prop: 'id', width: 80 },
  { label: '用户名', prop: 'username', minWidth: 120, showOverflowTooltip: true },
  { label: '状态', prop: 'status', width: 100, formatter: formatStatus },
  { label: '创建时间', prop: 'createTime', width: 180, sortable: true }
]

// ❌ 不推荐：过多的列
const tooManyColumns = [
  // 避免超过10列，影响用户体验
  ...Array.from({ length: 15 }, (_, i) => ({
    label: `列${i}`,
    prop: `field${i}`
  }))
]
```

### 2. 按钮配置优化

```javascript
// ✅ 推荐：合理的按钮配置
const optimizedButtons = [
  {
    label: '新增',
    key: 'add',
    type: 'primary',
    position: 'left',
    auth: 'user:add'
  },
  {
    label: '导出',
    key: 'export',
    position: 'right'
  }
]

// ❌ 不推荐：过多的按钮
const tooManyButtons = [
  // 避免超过5个按钮，影响界面美观
  ...Array.from({ length: 10 }, (_, i) => ({
    label: `按钮${i}`,
    key: `btn${i}`
  }))
]
```

### 3. 事件处理优化

```javascript
// ✅ 推荐：高效的事件处理
const handleHeadBtnClick = (button, tab) => {
  switch (button.key) {
    case 'add':
      handleAdd(tab)
      break
    case 'export':
      handleExport(tab)
      break
    default:
      console.warn('未知按钮:', button.key)
  }
}

// ✅ 推荐：使用防抖处理频繁事件
import { debounce } from 'lodash-es'

const handleSearch = debounce((searchParams) => {
  // 搜索逻辑
}, 300)
```

## 数据处理最佳实践

### 1. API接口设计

```javascript
// ✅ 推荐：标准的接口返回格式
const standardResponse = {
  data: [
    { id: 1, name: '用户1', status: 'active' }
  ],
  total: 100,
  pageNum: 1,
  pageSize: 20
}

// ✅ 推荐：统一的错误处理
const handleRequestError = (error, tab) => {
  console.error(`${tab.label}数据加载失败:`, error)
  // 统一的错误提示
  ElMessage.error(`${tab.label}数据加载失败，请重试`)
}
```

### 2. 状态管理

```javascript
// ✅ 推荐：使用响应式数据
import { reactive, ref } from 'vue'

const listState = reactive({
  loading: false,
  selectedRows: [],
  currentTab: 'users'
})

// ✅ 推荐：合理的状态更新
const updateState = (key, value) => {
  listState[key] = value
}
```

### 3. 缓存策略

```javascript
// ✅ 推荐：简单的数据缓存
const dataCache = new Map()

const getCachedData = (cacheKey) => {
  return dataCache.get(cacheKey)
}

const setCachedData = (cacheKey, data) => {
  // 限制缓存大小
  if (dataCache.size > 10) {
    const firstKey = dataCache.keys().next().value
    dataCache.delete(firstKey)
  }
  dataCache.set(cacheKey, data)
}
```

## 用户体验最佳实践

### 1. 加载状态

```javascript
// ✅ 推荐：明确的加载状态
const handleBeforeRequest = (tab) => {
  listState.loading = true
  // 可以显示骨架屏或loading动画
}

const handleAfterRequest = (list, tab) => {
  listState.loading = false
  // 数据加载完成后的处理
}
```

### 2. 错误处理

```javascript
// ✅ 推荐：友好的错误提示
const handleRequestError = (error, tab) => {
  listState.loading = false
  
  // 根据错误类型显示不同提示
  if (error.code === 'NETWORK_ERROR') {
    ElMessage.error('网络连接失败，请检查网络设置')
  } else if (error.code === 'PERMISSION_DENIED') {
    ElMessage.error('权限不足，请联系管理员')
  } else {
    ElMessage.error('数据加载失败，请重试')
  }
}
```

### 3. 交互反馈

```javascript
// ✅ 推荐：及时的操作反馈
const handleHeadBtnClick = async (button, tab) => {
  try {
    switch (button.key) {
      case 'add':
        await handleAdd(tab)
        ElMessage.success('添加成功')
        // 刷新列表
        reloadTable(tab.key)
        break
      case 'export':
        ElMessage.info('正在导出，请稍候...')
        await handleExport(tab)
        ElMessage.success('导出完成')
        break
    }
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}
```

## 权限控制最佳实践

### 1. 按钮权限

```javascript
// ✅ 推荐：基于权限的按钮配置
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const getButtonsWithPermission = () => {
  const buttons = []
  
  if (userStore.hasPermission('user:add')) {
    buttons.push({
      label: '新增',
      key: 'add',
      type: 'primary',
      auth: 'user:add'
    })
  }
  
  if (userStore.hasPermission('user:export')) {
    buttons.push({
      label: '导出',
      key: 'export',
      position: 'right'
    })
  }
  
  return buttons
}
```

### 2. 操作权限

```javascript
// ✅ 推荐：动态的操作权限控制
const getActionsWithPermission = () => [
  {
    name: '编辑',
    key: 'edit',
    props: { type: 'primary', size: 'small' },
    auth: 'user:edit',
    show: (row) => {
      return userStore.hasPermission('user:edit') && 
             row.status !== 'deleted' &&
             row.id !== userStore.currentUser.id
    }
  }
]
```

## 常见问题和解决方案

### 1. 标签页切换性能问题

```javascript
// ✅ 解决：使用reloadOnActive控制数据加载
const cardTab = [
  {
    label: '用户列表',
    key: 'users',
    api: '/api/users',
    curdOption: {
      // 配置项
    }
  }
]

// 在组件中设置
<FuniListPage 
  :cardTab="cardTab" 
  :reloadOnActive="false"  // 避免频繁切换时重复加载
/>
```

### 2. 内存泄漏问题

```javascript
// ✅ 解决：组件销毁时清理资源
import { onUnmounted } from 'vue'

onUnmounted(() => {
  // 清理定时器
  clearInterval(timer)
  // 清理事件监听
  window.removeEventListener('resize', handleResize)
  // 清理缓存
  dataCache.clear()
})
```

### 3. 数据同步问题

```javascript
// ✅ 解决：使用事件总线或状态管理
import { useEventBus } from '@/composables/useEventBus'

const eventBus = useEventBus()

// 监听数据变化
eventBus.on('user:updated', (userData) => {
  // 更新列表数据
  reloadTable('users')
})

// 在操作完成后发送事件
const handleEdit = async (row) => {
  await updateUser(row)
  eventBus.emit('user:updated', row)
}
```
