# FuniListPage ElementPlus API 支持

## 基础组件说明

FuniListPage 是基于 ElementPlus 的多个组件进行二次封装的列表页组件。它主要集成了以下 ElementPlus 组件：

- **el-tabs** - 标签页容器
- **el-tab-pane** - 标签页面板
- **FuniCurd** - 数据表格（内部使用 el-table）
- **el-button** - 操作按钮
- **FuniTeleport** - 内容传送

### 架构设计

```vue
<!-- FuniListPage 内部结构 -->
<funi-teleport to=".layout-content__wrap" :disabled="!teleported">
  <el-tabs v-model="activeTab" class="funi_list_page">
    <el-tab-pane v-for="item in cardTab" :key="item.key" :label="item.label" :name="item.key">
      <funi-curd v-bind="item.curdOption" :loadData="getData" />
    </el-tab-pane>
  </el-tabs>
</funi-teleport>
```

## 支持的 ElementPlus el-tabs API

### 基础属性透传

| 属性名 | 类型 | 说明 | 透传方式 |
|--------|------|------|---------|
| model-value | String | 当前激活标签页 | 通过active属性 |
| type | String | 标签页类型 | 固定为默认类型 |
| closable | Boolean | 是否可关闭 | 暂不支持 |
| addable | Boolean | 是否可增加 | 暂不支持 |
| editable | Boolean | 是否可编辑 | 暂不支持 |
| tab-position | String | 标签页位置 | 固定为top |
| stretch | Boolean | 标签是否拉伸 | 暂不支持 |

### 标签页事件

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| tab-click | TabsPaneContext, Event | 标签页点击事件 | 内部处理 |
| tab-change | name | 标签页切换事件 | 内部处理 |
| tab-remove | name | 标签页移除事件 | 暂不支持 |
| tab-add | - | 标签页新增事件 | 暂不支持 |
| edit | targetName, action | 标签页编辑事件 | 暂不支持 |

## 支持的 FuniCurd API

FuniListPage 内部使用 FuniCurd 组件，支持其所有 API 配置：

### 表格属性透传

| 属性名 | 类型 | 说明 | 透传方式 |
|--------|------|------|---------|
| columns | Array | 列配置 | curdOption.columns |
| actions | Array | 行操作配置 | curdOption.actions |
| selection | Boolean | 是否显示选择列 | curdOption.selection |
| pagination | Object | 分页配置 | curdOption.pagination |
| loadData | Function | 数据加载函数 | 自动生成 |
| searchConfig | Object | 搜索配置 | 通过searchConfig |

### 表格事件透传

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| selection-change | selection | 选择变化 | curdOption.on |
| row-click | row, column, event | 行点击 | curdOption.on |
| cell-click | row, column, cell, event | 单元格点击 | curdOption.on |
| sort-change | { column, prop, order } | 排序变化 | curdOption.on |
| filter-change | filters | 筛选变化 | curdOption.on |

## 支持的 ElementPlus el-button API

### 头部按钮配置

通过 `curdOption.btns` 配置头部按钮，支持 ElementPlus el-button 的所有属性：

| 属性名 | 类型 | 说明 | 透传方式 |
|--------|------|------|---------|
| type | String | 按钮类型 | 直接透传 |
| size | String | 按钮尺寸 | 直接透传 |
| plain | Boolean | 是否朴素按钮 | 直接透传 |
| round | Boolean | 是否圆角按钮 | 直接透传 |
| circle | Boolean | 是否圆形按钮 | 直接透传 |
| loading | Boolean | 是否加载中 | 直接透传 |
| disabled | Boolean | 是否禁用 | 直接透传 |
| icon | String/Component | 图标 | 直接透传 |
| autofocus | Boolean | 是否自动聚焦 | 直接透传 |
| native-type | String | 原生type属性 | 直接透传 |

### 行操作按钮配置

通过 `curdOption.actions` 配置行操作按钮：

```javascript
const actions = [
  {
    name: '编辑',
    key: 'edit',
    props: {
      type: 'primary',    // ElementPlus el-button type
      size: 'small',      // ElementPlus el-button size
      icon: 'Edit',       // ElementPlus el-button icon
      disabled: false     // ElementPlus el-button disabled
    }
  }
]
```

## 使用方式

### 基础表格使用

```vue
<template>
  <FuniListPage
    :cardTab="cardTabConfig"
    :active="activeTab"
    :isShowSearch="true"
    
    <!-- 事件监听 -->
    @headBtnClick="handleHeadBtnClick"
    @beforeRequest="handleBeforeRequest"
    @afterRequest="handleAfterRequest"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const activeTab = ref('users')

const cardTabConfig = reactive([
  {
    label: '用户列表',
    key: 'users',
    api: '/api/users',
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80, sortable: true },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '邮箱', prop: 'email', minWidth: 180 }
      ],
      
      // ElementPlus el-table 属性透传
      stripe: true,
      border: true,
      size: 'default',
      'row-key': 'id',
      'show-summary': true,
      
      // ElementPlus el-table 事件透传
      on: {
        'selection-change': handleSelectionChange,
        'sort-change': handleSortChange,
        'row-click': handleRowClick
      },
      
      // 分页配置（ElementPlus el-pagination）
      pagination: {
        background: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        pageSizes: [10, 20, 50, 100],
        small: false
      }
    }
  }
])

// 事件处理
const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', prop, order)
}

const handleRowClick = (row, column, event) => {
  console.log('行点击:', row)
}

const handleHeadBtnClick = (button, tab) => {
  console.log('按钮点击:', button, tab)
}

const handleBeforeRequest = (tab) => {
  console.log('请求开始:', tab)
}

const handleAfterRequest = (list, tab) => {
  console.log('请求完成:', list, tab)
}
</script>
```

### 高级配置示例

```vue
<template>
  <FuniListPage :cardTab="advancedConfig" />
</template>

<script setup>
import { reactive } from 'vue'

const advancedConfig = reactive([
  {
    label: '高级表格',
    key: 'advanced',
    api: '/api/advanced',
    curdOption: {
      columns: [
        { type: 'selection', width: 55 },
        { type: 'index', label: '序号', width: 80 },
        { label: '名称', prop: 'name', minWidth: 120, sortable: 'custom' },
        { label: '状态', prop: 'status', width: 100 }
      ],
      
      // ElementPlus el-table 高级配置
      'tree-props': { children: 'children', hasChildren: 'hasChildren' },
      lazy: true,
      load: loadTreeNode,
      'row-class-name': getRowClassName,
      'cell-style': getCellStyle,
      'header-cell-style': getHeaderCellStyle,
      'span-method': getSpanMethod,
      'summary-method': getSummaries,
      'default-sort': { prop: 'name', order: 'ascending' },
      
      // 头部按钮（ElementPlus el-button）
      btns: [
        {
          label: '新增',
          key: 'add',
          type: 'primary',        // el-button type
          size: 'default',        // el-button size
          icon: 'Plus',           // el-button icon
          plain: false,           // el-button plain
          round: false,           // el-button round
          loading: false,         // el-button loading
          disabled: false,        // el-button disabled
          position: 'left'
        },
        {
          label: '导出',
          key: 'export',
          type: 'success',
          size: 'default',
          icon: 'Download',
          position: 'right'
        }
      ],
      
      // 行操作按钮
      actions: [
        {
          name: '编辑',
          key: 'edit',
          props: {
            type: 'primary',      // el-button type
            size: 'small',        // el-button size
            icon: 'Edit',         // el-button icon
            circle: false,        // el-button circle
            plain: true           // el-button plain
          }
        },
        {
          name: '删除',
          key: 'delete',
          props: {
            type: 'danger',
            size: 'small',
            icon: 'Delete',
            loading: false,
            disabled: false
          }
        }
      ],
      
      // 分页配置（ElementPlus el-pagination）
      pagination: {
        small: false,
        background: true,
        'page-size': 20,
        layout: 'total, sizes, prev, pager, next, jumper',
        'page-sizes': [10, 20, 50, 100],
        'popper-class': 'custom-pagination-popper',
        'prev-text': '上一页',
        'next-text': '下一页',
        disabled: false,
        'hide-on-single-page': false
      }
    }
  }
])

// 样式函数
const getRowClassName = ({ row, rowIndex }) => {
  if (row.status === 'disabled') {
    return 'disabled-row'
  }
  return ''
}

const getCellStyle = ({ row, column, rowIndex, columnIndex }) => {
  if (column.property === 'status') {
    return { color: row.status === 'active' ? '#67c23a' : '#f56c6c' }
  }
  return {}
}

const getHeaderCellStyle = ({ row, column, rowIndex, columnIndex }) => {
  return { backgroundColor: '#f5f7fa' }
}

// 合并行或列
const getSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    if (rowIndex % 2 === 0) {
      return {
        rowspan: 2,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
}

// 合计行
const getSummaries = (param) => {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map(item => Number(item[column.property]))
    if (!values.every(value => isNaN(value))) {
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
    } else {
      sums[index] = 'N/A'
    }
  })
  return sums
}

// 懒加载
const loadTreeNode = (tree, treeNode, resolve) => {
  setTimeout(() => {
    resolve([
      {
        id: tree.id * 10 + 1,
        name: '子节点1',
        status: 'active'
      }
    ])
  }, 1000)
}
</script>

<style scoped>
:deep(.disabled-row) {
  background-color: #f5f7fa;
  color: #c0c4cc;
}
</style>
```

## 注意事项

### 1. API透传机制

- **el-tabs**: 通过组件内部逻辑处理标签页切换
- **FuniCurd**: 通过 `v-bind="item.curdOption"` 透传所有配置
- **el-button**: 通过 btns 和 actions 配置透传按钮属性
- **el-pagination**: 通过 pagination 配置对象透传

### 2. 事件处理

- 标签页事件由组件内部处理，外部通过 active 属性控制
- 表格事件通过 `curdOption.on` 配置监听
- 按钮事件通过 `@headBtnClick` 统一处理

### 3. 数据处理

- 数据加载由组件内部的 getData 函数处理
- 支持通过 fixSearchParams 转换搜索参数
- 支持通过 dataCallback 处理响应数据

### 4. 样式定制

- 可以通过 ElementPlus 的样式相关属性定制外观
- 支持行、列、单元格级别的样式定制
- 可以通过 CSS 深度选择器进一步定制样式

### 5. 兼容性

- 完全兼容 ElementPlus el-table 的所有功能
- 完全兼容 ElementPlus el-tabs 的基础功能
- 完全兼容 ElementPlus el-button 的所有属性
- 完全兼容 ElementPlus el-pagination 的所有配置
