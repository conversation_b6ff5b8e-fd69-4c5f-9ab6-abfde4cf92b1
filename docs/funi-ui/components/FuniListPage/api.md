# FuniListPage API文档

## 组件概述

FuniListPage是CLI框架中的列表页组件（旧版），基于ElementPlus的el-tabs和FuniCurd封装。它提供了多标签页的列表展示功能，支持搜索、分页、导出等完整的列表页功能。

**注意**: 这是旧版列表页组件，新项目建议使用FuniListPageV2。

## Props

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| cardTab | Array | `[]` | 标签页配置数组 |
| isShowSearch | Boolean | `true` | 是否显示搜索功能 |
| active | String | - | 默认激活的标签页key |
| showTab | Boolean | - | 是否显示标签页头部 |
| teleported | Boolean | `true` | 是否传送到.layout-content__wrap容器 |
| reloadOnActive | Boolean | `false` | 切换标签页时是否重新加载数据 |

### 详细说明

#### cardTab
- **类型**: `Array`
- **默认值**: `[]`
- **说明**: 标签页配置数组，每个元素包含一个标签页的完整配置
- **配置项**:
  - `label`: 标签页标题
  - `key`: 标签页唯一标识
  - `api`: 数据接口地址
  - `curdOption`: FuniCurd组件配置
  - `requestParams`: 请求参数
  - `fixSearchParams`: 搜索参数处理函数
  - `slot`: 自定义插槽名称
  - `showSearch`: 是否显示搜索（默认true）

#### isShowSearch
- **类型**: `Boolean`
- **默认值**: `true`
- **说明**: 是否显示搜索功能，控制全局搜索显示

#### active
- **类型**: `String`
- **说明**: 默认激活的标签页key，不传则使用第一个标签页

#### showTab
- **类型**: `Boolean`
- **说明**: 是否显示标签页头部，不传则根据标签页数量自动判断（单个标签页自动隐藏）

#### teleported
- **类型**: `Boolean`
- **默认值**: `true`
- **说明**: 是否使用FuniTeleport传送到.layout-content__wrap容器

#### reloadOnActive
- **类型**: `Boolean`
- **默认值**: `false`
- **说明**: 切换标签页时是否重新加载数据

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| headBtnClick | button, tab | 头部按钮点击事件 |
| beforeRequest | tab | 请求前事件 |
| afterRequest | list, tab | 请求后事件 |
| requestError | error, tab | 请求错误事件 |

### 事件详细说明

#### headBtnClick
- **参数**: `(button, tab)`
- **说明**: 头部按钮点击时触发
- **示例**:
```javascript
const handleHeadBtnClick = (button, tab) => {
  console.log('按钮点击:', button.key, tab.key)
}
```

#### beforeRequest
- **参数**: `(tab)`
- **说明**: 数据请求前触发，可用于设置loading状态
- **示例**:
```javascript
const handleBeforeRequest = (tab) => {
  console.log('开始请求:', tab.key)
}
```

#### afterRequest
- **参数**: `(list, tab)`
- **说明**: 数据请求成功后触发，可用于数据后处理
- **示例**:
```javascript
const handleAfterRequest = (list, tab) => {
  console.log('请求完成:', list.length, tab.key)
}
```

#### requestError
- **参数**: `(error, tab)`
- **说明**: 数据请求失败时触发
- **示例**:
```javascript
const handleRequestError = (error, tab) => {
  console.error('请求失败:', error, tab.key)
}
```

## Slots

### 基础插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| empty | 空数据时的内容 | - |
| append | 插入至表格最后一行之后的内容 | - |

### 动态插槽

FuniListPage支持通过cardTab配置中的slot属性定义自定义标签页内容：

```javascript
const cardTab = [
  {
    label: '自定义内容',
    key: 'custom',
    slot: 'custom-content'
  }
]
```

对应的模板使用：
```vue
<template>
  <FuniListPage :cardTab="cardTab">
    <template #custom-content>
      <div>自定义标签页内容</div>
    </template>
  </FuniListPage>
</template>
```

### 列插槽

通过columns配置中的slots属性定义列插槽：

```javascript
const columns = [
  {
    prop: 'status',
    label: '状态',
    slots: {
      default: 'status-slot'
    }
  }
]
```

对应的模板使用：
```vue
<template>
  <FuniListPage :cardTab="cardTab">
    <template #status-slot="{ row, column, $index }">
      <el-tag :type="getStatusType(row.status)">
        {{ row.status }}
      </el-tag>
    </template>
  </FuniListPage>
</template>
```

## Methods

FuniListPage通过ref暴露以下方法：

### 数据操作方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| reload | tabKey? | 重新加载数据，不传tabKey则重载当前标签页 |
| search | searchParams, tabKey? | 执行搜索，不传tabKey则搜索当前标签页 |
| getTableData | tabKey? | 获取表格数据，不传tabKey则获取当前标签页 |
| getSelectedRows | tabKey? | 获取选中行数据 |
| clearSelection | tabKey? | 清空选择 |

### 使用示例

```vue
<template>
  <FuniListPage ref="listPageRef" :cardTab="cardTab" />
</template>

<script setup>
import { ref } from 'vue'

const listPageRef = ref()

// 重新加载数据
const reload = () => {
  listPageRef.value.reload()
}

// 执行搜索
const search = (params) => {
  listPageRef.value.search(params)
}

// 获取选中行
const getSelected = () => {
  const selected = listPageRef.value.getSelectedRows()
  console.log('选中行:', selected)
}
</script>
```

## cardTab配置详解

### 基础配置

```javascript
const cardTab = [
  {
    label: '用户列表',           // 标签页标题
    key: 'users',               // 唯一标识
    api: '/api/users',          // 数据接口
    requestParams: {            // 固定请求参数
      status: 'active'
    },
    curdOption: {               // FuniCurd配置
      columns: [...],           // 列配置
      actions: [...],           // 操作按钮配置
      btns: [...],             // 头部按钮配置
      selection: true,          // 是否显示选择列
      pagination: {...}         // 分页配置
    }
  }
]
```

### 高级配置

```javascript
const cardTab = [
  {
    label: '高级列表',
    key: 'advanced',
    api: '/api/advanced',
    // 搜索参数处理函数
    fixSearchParams: (searchParams) => {
      return {
        ...searchParams,
        // 自定义参数转换
        dateRange: searchParams.startDate && searchParams.endDate 
          ? [searchParams.startDate, searchParams.endDate]
          : undefined
      }
    },
    curdOption: {
      columns: [...],
      // 数据回调处理
      dataCallback: ({ response, params }) => {
        return {
          list: response.data.map(item => ({
            ...item,
            // 数据预处理
            displayName: `${item.firstName} ${item.lastName}`
          })),
          total: response.total,
          pageNum: response.pageNum,
          pageSize: response.pageSize
        }
      },
      // 头部按钮配置
      btns: [
        {
          label: '新增',
          key: 'add',
          type: 'primary',
          auth: 'user:add',
          position: 'left'
        },
        {
          label: '导出',
          key: 'export',
          position: 'right'
        }
      ],
      // 行操作按钮
      actions: [
        {
          name: '编辑',
          key: 'edit',
          props: { type: 'primary', size: 'small' }
        }
      ]
    }
  }
]
```

## 导出功能

FuniListPage内置了导出功能，通过ExportSettingModal组件实现：

### 导出配置

```javascript
const handleExport = async (exportConfig) => {
  const { fileName, fields, fileType } = exportConfig
  
  // 执行导出逻辑
  await exportData({
    fileName,
    fields,
    fileType,
    data: currentTableData
  })
}
```

### 导出按钮配置

```javascript
const cardTab = [
  {
    label: '用户列表',
    key: 'users',
    api: '/api/users',
    curdOption: {
      btns: [
        {
          label: '导出',
          key: 'export',
          position: 'right',
          onClick: () => {
            // 触发导出弹窗
            showExportModal()
          }
        }
      ]
    }
  }
]
```

## 与FuniCurd的关系

FuniListPage内部使用FuniCurd组件，支持FuniCurd的所有配置：

```vue
<funi-curd
  v-bind="item.curdOption"
  :loading="loading[index]"
  :loadData="getData"
  :isShowSearch="isShowSearch"
  :searchConfig="getSearchConfig()"
  @beforeRequest="beforeRequest(item)"
  @afterRequest="list => afterRequest(list, item)"
  @requestError="error => requestError(error, item)"
/>
```

## 注意事项

1. **版本说明**: 这是旧版列表页组件，新项目建议使用FuniListPageV2
2. **数据接口**: 接口返回格式需要符合CLI框架的标准格式
3. **权限控制**: 按钮权限通过auth属性配置，需要配合权限系统使用
4. **性能优化**: 大数据量时建议使用分页和虚拟滚动
5. **样式定制**: 可以通过CSS深度选择器定制样式
