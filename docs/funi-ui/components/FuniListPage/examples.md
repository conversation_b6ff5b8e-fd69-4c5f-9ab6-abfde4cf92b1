# FuniListPage 使用示例

## 基础示例

### 单标签页列表

```vue
<template>
  <FuniListPage 
    :cardTab="cardTabConfig" 
    @headBtnClick="handleHeadBtnClick"
    @beforeRequest="handleBeforeRequest"
    @afterRequest="handleAfterRequest"
  />
</template>

<script setup>
import { reactive } from 'vue'

const cardTabConfig = reactive([
  {
    label: '用户列表',
    key: 'users',
    api: '/api/users',
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80 },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '邮箱', prop: 'email', minWidth: 180 },
        { label: '状态', prop: 'status', width: 100 },
        { label: '创建时间', prop: 'createTime', width: 180 }
      ],
      actions: [
        {
          name: '编辑',
          key: 'edit',
          props: { type: 'primary', size: 'small' },
          on: { click: handleEdit }
        },
        {
          name: '删除',
          key: 'delete',
          props: { type: 'danger', size: 'small' },
          on: { click: handleDelete }
        }
      ],
      btns: [
        {
          label: '新增用户',
          key: 'add',
          type: 'primary',
          position: 'left'
        }
      ],
      selection: true,
      pagination: {
        pageSize: 20,
        pageSizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
        background: true
      }
    }
  }
])

// 事件处理
const handleHeadBtnClick = (button, tab) => {
  console.log('头部按钮点击:', button.key, tab.key)
  if (button.key === 'add') {
    // 新增用户逻辑
    showAddUserDialog()
  }
}

const handleEdit = (row, index) => {
  console.log('编辑用户:', row)
}

const handleDelete = (row, index) => {
  console.log('删除用户:', row)
}

const handleBeforeRequest = (tab) => {
  console.log('开始请求:', tab.key)
}

const handleAfterRequest = (list, tab) => {
  console.log('请求完成:', list.length, tab.key)
}
</script>
```

### 多标签页列表

```vue
<template>
  <FuniListPage 
    :cardTab="multiTabConfig" 
    :active="activeTab"
    :showTab="true"
    @headBtnClick="handleHeadBtnClick"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const activeTab = ref('active-users')

const multiTabConfig = reactive([
  {
    label: '活跃用户',
    key: 'active-users',
    api: '/api/users',
    requestParams: { status: 'active' },
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80 },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '邮箱', prop: 'email', minWidth: 180 },
        { label: '最后登录', prop: 'lastLogin', width: 180 }
      ],
      btns: [
        {
          label: '激活用户',
          key: 'activate',
          type: 'success',
          position: 'left'
        }
      ]
    }
  },
  {
    label: '禁用用户',
    key: 'disabled-users',
    api: '/api/users',
    requestParams: { status: 'disabled' },
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80 },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '邮箱', prop: 'email', minWidth: 180 },
        { label: '禁用时间', prop: 'disabledAt', width: 180 }
      ],
      btns: [
        {
          label: '启用用户',
          key: 'enable',
          type: 'warning',
          position: 'left'
        }
      ]
    }
  },
  {
    label: '待审核用户',
    key: 'pending-users',
    api: '/api/users',
    requestParams: { status: 'pending' },
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80 },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '申请时间', prop: 'applyTime', width: 180 }
      ],
      actions: [
        {
          name: '审核',
          key: 'audit',
          props: { type: 'primary', size: 'small' }
        }
      ]
    }
  }
])

const handleHeadBtnClick = (button, tab) => {
  console.log('按钮点击:', button.key, '标签页:', tab.key)
  
  switch (button.key) {
    case 'activate':
      handleActivateUsers()
      break
    case 'enable':
      handleEnableUsers()
      break
  }
}
</script>
```

## 高级示例

### 带插槽的列表页

```vue
<template>
  <FuniListPage :cardTab="cardTabConfig">
    <!-- 状态列插槽 -->
    <template #status-slot="{ row }">
      <el-tag :type="getStatusType(row.status)">
        {{ getStatusText(row.status) }}
      </el-tag>
    </template>
    
    <!-- 头像列插槽 -->
    <template #avatar-slot="{ row }">
      <el-avatar :src="row.avatar" :size="32">
        {{ row.name.charAt(0) }}
      </el-avatar>
    </template>
    
    <!-- 操作列插槽 -->
    <template #actions-slot="{ row, $index }">
      <el-button 
        size="small" 
        type="primary" 
        @click="handleEdit(row)"
        v-auth="'user:edit'"
      >
        编辑
      </el-button>
      <el-button 
        size="small" 
        type="danger" 
        @click="handleDelete(row, $index)"
        v-auth="'user:delete'"
      >
        删除
      </el-button>
      <el-dropdown @command="handleMoreAction">
        <el-button size="small">
          更多<el-icon><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :command="{action: 'reset', row}">重置密码</el-dropdown-item>
            <el-dropdown-item :command="{action: 'lock', row}">锁定账户</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>
    
    <!-- 自定义标签页内容 -->
    <template #custom-content>
      <div class="custom-tab-content">
        <h3>自定义内容</h3>
        <p>这里可以放置任何自定义内容</p>
      </div>
    </template>
  </FuniListPage>
</template>

<script setup>
import { reactive } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'

const cardTabConfig = reactive([
  {
    label: '用户管理',
    key: 'users',
    api: '/api/users',
    curdOption: {
      columns: [
        { type: 'selection', width: 55 },
        { type: 'index', label: '序号', width: 80 },
        {
          label: '头像',
          prop: 'avatar',
          width: 80,
          slots: { default: 'avatar-slot' }
        },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '邮箱', prop: 'email', minWidth: 180 },
        {
          label: '状态',
          prop: 'status',
          width: 100,
          slots: { default: 'status-slot' }
        },
        {
          label: '操作',
          width: 200,
          fixed: 'right',
          slots: { default: 'actions-slot' }
        }
      ]
    }
  },
  {
    label: '自定义',
    key: 'custom',
    slot: 'custom-content'
  }
])

// 状态映射
const getStatusType = (status) => {
  const typeMap = {
    active: 'success',
    disabled: 'danger',
    pending: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    active: '激活',
    disabled: '禁用',
    pending: '待审核'
  }
  return textMap[status] || '未知'
}

// 事件处理
const handleEdit = (row) => {
  console.log('编辑用户:', row)
}

const handleDelete = (row, index) => {
  console.log('删除用户:', row, index)
}

const handleMoreAction = ({ action, row }) => {
  console.log('更多操作:', action, row)
  switch (action) {
    case 'reset':
      handleResetPassword(row)
      break
    case 'lock':
      handleLockAccount(row)
      break
  }
}
</script>

<style scoped>
.custom-tab-content {
  padding: 20px;
  text-align: center;
}
</style>
```

### 数据处理和搜索参数转换

```vue
<template>
  <FuniListPage 
    :cardTab="advancedConfig"
    @beforeRequest="handleBeforeRequest"
    @afterRequest="handleAfterRequest"
    @requestError="handleRequestError"
  />
</template>

<script setup>
import { reactive } from 'vue'

const advancedConfig = reactive([
  {
    label: '高级用户列表',
    key: 'advanced-users',
    api: '/api/users/advanced',
    requestParams: {
      includeDeleted: false,
      expand: 'profile,roles'
    },
    // 搜索参数转换
    fixSearchParams: (searchParams) => {
      const processed = { ...searchParams }
      
      // 日期范围处理
      if (searchParams.dateRange && searchParams.dateRange.length === 2) {
        processed.startDate = searchParams.dateRange[0]
        processed.endDate = searchParams.dateRange[1]
        delete processed.dateRange
      }
      
      // 状态数组处理
      if (searchParams.statusList && Array.isArray(searchParams.statusList)) {
        processed.status = searchParams.statusList.join(',')
        delete processed.statusList
      }
      
      // 关键词处理
      if (searchParams.keyword) {
        processed.q = searchParams.keyword.trim()
        delete processed.keyword
      }
      
      return processed
    },
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80, sortable: true },
        { label: '用户名', prop: 'username', minWidth: 120, sortable: true },
        { label: '邮箱', prop: 'email', minWidth: 180 },
        { label: '角色', prop: 'roleName', width: 120 },
        { label: '部门', prop: 'departmentName', width: 120 },
        { label: '创建时间', prop: 'createTime', width: 180, sortable: true }
      ],
      // 数据回调处理
      dataCallback: ({ response, params }) => {
        console.log('原始响应:', response)
        console.log('请求参数:', params)
        
        return {
          list: response.data.map(item => ({
            ...item,
            // 数据预处理
            displayName: `${item.firstName} ${item.lastName}`,
            roleName: item.roles?.map(r => r.name).join(', ') || '无角色',
            departmentName: item.department?.name || '未分配'
          })),
          total: response.total,
          pageNum: response.pageNum,
          pageSize: response.pageSize
        }
      },
      btns: [
        {
          label: '新增用户',
          key: 'add',
          type: 'primary',
          position: 'left',
          auth: 'user:add'
        },
        {
          label: '批量导入',
          key: 'import',
          position: 'left',
          auth: 'user:import'
        },
        {
          label: '导出',
          key: 'export',
          position: 'right'
        }
      ]
    }
  }
])

const handleBeforeRequest = (tab) => {
  console.log('请求开始:', tab.key)
  // 可以在这里设置loading状态
}

const handleAfterRequest = (list, tab) => {
  console.log('请求完成:', {
    tab: tab.key,
    count: list.length,
    data: list
  })
  // 可以在这里进行数据后处理
}

const handleRequestError = (error, tab) => {
  console.error('请求失败:', {
    tab: tab.key,
    error
  })
  // 错误处理逻辑
}
</script>
```

### 导出功能示例

```vue
<template>
  <FuniListPage 
    ref="listPageRef"
    :cardTab="exportConfig"
    @headBtnClick="handleHeadBtnClick"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const listPageRef = ref()

const exportConfig = reactive([
  {
    label: '用户列表',
    key: 'users',
    api: '/api/users',
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80 },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '邮箱', prop: 'email', minWidth: 180 },
        { label: '手机号', prop: 'phone', width: 130 },
        { label: '状态', prop: 'status', width: 100 },
        { label: '创建时间', prop: 'createTime', width: 180 }
      ],
      btns: [
        {
          label: '导出',
          key: 'export',
          position: 'right'
        }
      ]
    }
  }
])

const handleHeadBtnClick = (button, tab) => {
  if (button.key === 'export') {
    handleExport(tab)
  }
}

const handleExport = async (tab) => {
  try {
    // 获取当前表格数据
    const tableData = listPageRef.value.getTableData(tab.key)
    
    // 导出配置
    const exportConfig = {
      fileName: `${tab.label}_${new Date().toISOString().split('T')[0]}`,
      fields: tab.curdOption.columns.filter(col => col.prop).map(col => ({
        prop: col.prop,
        label: col.label
      })),
      fileType: 'xlsx'
    }
    
    // 执行导出
    await exportToFile(tableData, exportConfig)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 导出函数（示例）
const exportToFile = async (data, config) => {
  // 这里实现具体的导出逻辑
  console.log('导出数据:', data)
  console.log('导出配置:', config)
}
</script>
```

### 权限控制示例

```vue
<template>
  <FuniListPage :cardTab="permissionConfig" />
</template>

<script setup>
import { reactive, computed } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 根据用户权限动态配置
const permissionConfig = computed(() => [
  {
    label: '用户管理',
    key: 'users',
    api: '/api/users',
    curdOption: {
      columns: [
        { label: 'ID', prop: 'id', width: 80 },
        { label: '用户名', prop: 'username', minWidth: 120 },
        { label: '邮箱', prop: 'email', minWidth: 180 },
        { label: '状态', prop: 'status', width: 100 }
      ],
      actions: [
        {
          name: '编辑',
          key: 'edit',
          props: { type: 'primary', size: 'small' },
          auth: 'user:edit',
          show: (row) => userStore.hasPermission('user:edit') && row.status !== 'deleted'
        },
        {
          name: '删除',
          key: 'delete',
          props: { type: 'danger', size: 'small' },
          auth: 'user:delete',
          show: (row) => userStore.hasPermission('user:delete') && row.id !== userStore.currentUser.id
        }
      ],
      btns: [
        ...(userStore.hasPermission('user:add') ? [{
          label: '新增用户',
          key: 'add',
          type: 'primary',
          position: 'left',
          auth: 'user:add'
        }] : []),
        ...(userStore.hasPermission('user:export') ? [{
          label: '导出',
          key: 'export',
          position: 'right'
        }] : [])
      ]
    }
  }
])
</script>
```
