# FuniListPage 配置结构定义

## 组件配置接口

### IFuniListPageProps

```typescript
interface IFuniListPageProps {
  /** 标签页配置数组 */
  cardTab?: ICardTabItem[]
  /** 是否显示搜索功能 */
  isShowSearch?: boolean
  /** 默认激活的标签页key */
  active?: string
  /** 是否显示标签页头部 */
  showTab?: boolean
  /** 是否传送到.layout-content__wrap容器 */
  teleported?: boolean
  /** 切换标签页时是否重新加载数据 */
  reloadOnActive?: boolean
}
```

## 标签页配置接口

### ICardTabItem

```typescript
interface ICardTabItem {
  /** 标签页标题 */
  label: string
  /** 标签页唯一标识 */
  key: string
  /** 数据接口地址 */
  api?: string
  /** 固定请求参数 */
  requestParams?: Record<string, any>
  /** 搜索参数处理函数 */
  fixSearchParams?: (searchParams: Record<string, any>) => Record<string, any>
  /** 自定义插槽名称 */
  slot?: string
  /** 是否显示搜索 */
  showSearch?: boolean
  /** FuniCurd组件配置 */
  curdOption: ICurdOption
}
```

### ICurdOption

```typescript
interface ICurdOption {
  /** 列配置数组 */
  columns: IColumnConfig[]
  /** 行操作按钮配置 */
  actions?: IActionConfig[]
  /** 头部按钮配置 */
  btns?: IButtonConfig[]
  /** 是否显示选择列 */
  selection?: boolean
  /** 分页配置 */
  pagination?: IPaginationConfig
  /** 数据回调处理函数 */
  dataCallback?: (params: IDataCallbackParams) => IDataResponse
  /** 表格头部渲染函数 */
  header?: () => VNode | string
  /** 分页额外内容渲染函数 */
  paginationExtra?: (params: any) => VNode | string
  /** 事件监听器 */
  on?: Record<string, Function>
  /** 其他FuniCurd支持的属性 */
  [key: string]: any
}
```

### IColumnConfig

```typescript
interface IColumnConfig {
  /** 列绑定的字段名 */
  prop?: string
  /** 列标题 */
  label: string
  /** 列宽度 */
  width?: number | string
  /** 最小列宽度 */
  minWidth?: number | string
  /** 是否可排序 */
  sortable?: boolean | 'custom'
  /** 格式化函数 */
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  /** 插槽配置 */
  slots?: {
    default?: string
    header?: string
  }
  /** 是否隐藏 */
  hidden?: boolean
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 是否固定列 */
  fixed?: boolean | 'left' | 'right'
  /** 列类型 */
  type?: 'selection' | 'index' | 'expand'
  /** 子列配置（多级表头） */
  children?: IColumnConfig[]
}
```

### IActionConfig

```typescript
interface IActionConfig {
  /** 按钮名称 */
  name: string
  /** 按钮唯一标识 */
  key: string
  /** 按钮属性 */
  props?: {
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
    size?: 'large' | 'default' | 'small'
    disabled?: boolean
    loading?: boolean
    icon?: string
  }
  /** 事件监听器 */
  on?: {
    click?: (row: any, index: number) => void
  }
  /** 权限标识 */
  auth?: string
  /** 显示条件函数 */
  show?: (row: any, index: number) => boolean
}
```

### IButtonConfig

```typescript
interface IButtonConfig {
  /** 按钮标题 */
  label: string
  /** 按钮唯一标识 */
  key: string
  /** 按钮类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  /** 按钮位置 */
  position?: 'left' | 'right'
  /** 权限标识 */
  auth?: string
  /** 自定义组件 */
  component?: () => VNode
  /** 点击事件 */
  onClick?: () => void
  /** 其他ElementPlus Button属性 */
  [key: string]: any
}
```

### IPaginationConfig

```typescript
interface IPaginationConfig {
  /** 每页显示条目个数 */
  pageSize?: number
  /** 当前页数 */
  currentPage?: number
  /** 总条目数 */
  total?: number
  /** 每页显示个数选择器的选项设置 */
  pageSizes?: number[]
  /** 组件布局 */
  layout?: string
  /** 是否为分页按钮添加背景色 */
  background?: boolean
  /** 小型分页 */
  small?: boolean
  /** 只有一页时是否隐藏 */
  hideOnSinglePage?: boolean
}
```

### IDataCallbackParams

```typescript
interface IDataCallbackParams {
  /** 接口响应数据 */
  response: any
  /** 请求参数 */
  params: Record<string, any>
}
```

### IDataResponse

```typescript
interface IDataResponse {
  /** 数据列表 */
  list: any[]
  /** 总数 */
  total: number
  /** 当前页码 */
  pageNum?: number
  /** 每页大小 */
  pageSize?: number
}
```

## 配置示例

### 基础配置

```typescript
const basicConfig: IFuniListPageProps = {
  cardTab: [
    {
      label: '用户列表',
      key: 'users',
      api: '/api/users',
      curdOption: {
        columns: [
          { label: 'ID', prop: 'id', width: 80 },
          { label: '用户名', prop: 'username', minWidth: 120 },
          { label: '邮箱', prop: 'email', minWidth: 180 },
          { label: '状态', prop: 'status', width: 100 }
        ],
        selection: true,
        pagination: {
          pageSize: 20,
          pageSizes: [10, 20, 50, 100],
          layout: 'total, sizes, prev, pager, next, jumper',
          background: true
        }
      }
    }
  ],
  isShowSearch: true,
  teleported: true
}
```

### 高级配置

```typescript
const advancedConfig: IFuniListPageProps = {
  cardTab: [
    {
      label: '高级用户列表',
      key: 'advanced-users',
      api: '/api/users/advanced',
      requestParams: {
        includeDeleted: false
      },
      fixSearchParams: (searchParams) => {
        return {
          ...searchParams,
          // 日期范围处理
          startDate: searchParams.dateRange?.[0],
          endDate: searchParams.dateRange?.[1],
          // 移除原始日期范围字段
          dateRange: undefined
        }
      },
      curdOption: {
        columns: [
          { type: 'selection', width: 55, fixed: 'left' },
          { type: 'index', label: '序号', width: 80 },
          { label: '用户名', prop: 'username', minWidth: 120, sortable: true },
          { label: '邮箱', prop: 'email', minWidth: 180 },
          {
            label: '状态',
            prop: 'status',
            width: 100,
            slots: { default: 'status-slot' }
          },
          {
            label: '操作',
            width: 200,
            fixed: 'right',
            slots: { default: 'actions-slot' }
          }
        ],
        actions: [
          {
            name: '编辑',
            key: 'edit',
            props: { type: 'primary', size: 'small' },
            auth: 'user:edit'
          },
          {
            name: '删除',
            key: 'delete',
            props: { type: 'danger', size: 'small' },
            auth: 'user:delete',
            show: (row) => row.status !== 'deleted'
          }
        ],
        btns: [
          {
            label: '新增用户',
            key: 'add',
            type: 'primary',
            position: 'left',
            auth: 'user:add'
          },
          {
            label: '批量导入',
            key: 'import',
            position: 'left',
            auth: 'user:import'
          },
          {
            label: '导出',
            key: 'export',
            position: 'right'
          }
        ],
        dataCallback: ({ response, params }) => {
          return {
            list: response.data.map(item => ({
              ...item,
              displayName: `${item.firstName} ${item.lastName}`,
              statusText: getStatusText(item.status)
            })),
            total: response.total,
            pageNum: response.pageNum,
            pageSize: response.pageSize
          }
        },
        header: () => h('div', { class: 'table-header' }, '用户管理'),
        on: {
          'selection-change': (selection) => {
            console.log('选择变化:', selection)
          }
        }
      }
    }
  ],
  active: 'advanced-users',
  isShowSearch: true,
  teleported: true,
  reloadOnActive: false
}
```

### 多标签页配置

```typescript
const multiTabConfig: IFuniListPageProps = {
  cardTab: [
    {
      label: '活跃用户',
      key: 'active-users',
      api: '/api/users',
      requestParams: { status: 'active' },
      curdOption: {
        columns: [
          { label: 'ID', prop: 'id', width: 80 },
          { label: '用户名', prop: 'username', minWidth: 120 },
          { label: '最后登录', prop: 'lastLogin', width: 180 }
        ]
      }
    },
    {
      label: '禁用用户',
      key: 'disabled-users',
      api: '/api/users',
      requestParams: { status: 'disabled' },
      curdOption: {
        columns: [
          { label: 'ID', prop: 'id', width: 80 },
          { label: '用户名', prop: 'username', minWidth: 120 },
          { label: '禁用时间', prop: 'disabledAt', width: 180 }
        ]
      }
    },
    {
      label: '自定义内容',
      key: 'custom',
      slot: 'custom-content'
    }
  ],
  showTab: true
}
```

### 导出配置

```typescript
interface IExportConfig {
  /** 文件名 */
  fileName: string
  /** 导出字段 */
  fields: IExportField[]
  /** 文件类型 */
  fileType: 'xlsx' | 'csv'
}

interface IExportField {
  /** 字段名 */
  prop: string
  /** 字段标题 */
  label: string
  /** 父级字段ID */
  parentId?: string
}
```

## 事件配置

### IEventHandlers

```typescript
interface IEventHandlers {
  /** 头部按钮点击事件 */
  onHeadBtnClick?: (button: IButtonConfig, tab: ICardTabItem) => void
  /** 请求前事件 */
  onBeforeRequest?: (tab: ICardTabItem) => void
  /** 请求后事件 */
  onAfterRequest?: (list: any[], tab: ICardTabItem) => void
  /** 请求错误事件 */
  onRequestError?: (error: any, tab: ICardTabItem) => void
}
```

## 默认配置

```typescript
const defaultConfig: Required<IFuniListPageProps> = {
  cardTab: [],
  isShowSearch: true,
  active: '',
  showTab: undefined,
  teleported: true,
  reloadOnActive: false
}

const defaultCurdOption: Partial<ICurdOption> = {
  selection: false,
  pagination: {
    pageSize: 20,
    pageSizes: [10, 20, 50, 100],
    layout: 'total, sizes, prev, pager, next, jumper',
    background: true
  }
}
```

## 配置验证

### 必需配置检查

```typescript
function validateConfig(config: IFuniListPageProps): boolean {
  if (!Array.isArray(config.cardTab) || config.cardTab.length === 0) {
    console.warn('FuniListPage: cardTab must be a non-empty array')
    return false
  }
  
  for (const tab of config.cardTab) {
    if (!tab.label || !tab.key) {
      console.warn('FuniListPage: each tab must have label and key')
      return false
    }
    
    if (!tab.slot && !tab.curdOption) {
      console.warn('FuniListPage: each tab must have either slot or curdOption')
      return false
    }
  }
  
  return true
}
```
