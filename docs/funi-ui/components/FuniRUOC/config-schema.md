# FuniRUOC 配置结构定义

## 基础配置结构

### FuniRUOCProps 接口定义

```typescript
interface FuniRUOCProps {
  // 基础配置
  modelValue?: any
  type: 'role' | 'user' | 'org' | 'company'
  mode?: {
    user?: 'single' | 'multiple'
    role?: 'single' | 'multiple'
    org?: 'single' | 'multiple'
  }
  
  // 显示控制
  edit?: boolean
  hide?: {
    role?: boolean
    user?: boolean
    org?: boolean
  }
  activeName?: string
  tabPaneHeight?: string
  
  // 扩展配置
  extendTabs?: ExtendTab[]
  
  // 子组件配置
  roleProps?: RoleComponentProps
  userProps?: UserComponentProps
  orgProps?: OrgComponentProps
}
```

### ExtendTab 接口定义

```typescript
interface ExtendTab {
  name: string
  label: string
}
```

### RoleComponentProps 接口定义

```typescript
interface RoleComponentProps {
  request?: {
    api: string
    method: 'get' | 'post' | 'put' | 'delete'
    param?: Record<string, any>
  }
  defaultProps?: {
    id: string
    name: string
  }
  searchName?: string
  mode?: 'single' | 'multiple'
}
```

### UserComponentProps 接口定义

```typescript
interface UserComponentProps {
  orgRequest?: {
    api: string
    method: 'get' | 'post' | 'put' | 'delete' | 'fetch'
    param?: Record<string, any>
  }
  userRequest?: {
    api: string
    method: 'get' | 'post' | 'put' | 'delete'
    param?: Record<string, any>
  }
  orgDefaultProps?: {
    id: string
    name: string
    children: string
  }
  userDefaultProps?: {
    id: string
    name: string
  }
  mode?: 'single' | 'multiple'
  orgPowerMethod?: Function
  rightRatio?: string
}
```

### OrgComponentProps 接口定义

```typescript
interface OrgComponentProps {
  request?: {
    api: string
    method: 'get' | 'post' | 'put' | 'delete' | 'fetch'
    param?: Record<string, any>
  }
  defaultProps?: {
    id: string
    name: string
    children: string
  }
  searchName?: string
  mode?: 'single' | 'multiple'
  orgPowerMethod?: Function
}
```

## 详细配置说明

### 基础配置项

#### modelValue
- **类型**: `any`
- **默认值**: `[]`
- **说明**: 绑定值，支持单选和多选模式
- **示例**:
  ```javascript
  // 单选模式
  const selectedValue = ref('')
  
  // 多选模式
  const selectedValues = ref([])
  ```

#### type
- **类型**: `'role' | 'user' | 'org' | 'company'`
- **必填**: ✅
- **说明**: 选择器类型，决定显示的数据类型
- **示例**:
  ```javascript
  // 角色选择
  type: 'role'
  
  // 用户选择
  type: 'user'
  
  // 组织选择
  type: 'org'
  
  // 公司选择
  type: 'company'
  ```

#### mode
- **类型**: `Object`
- **默认值**: `{ user: 'multiple', role: 'multiple', org: 'multiple' }`
- **说明**: 各类型的选择模式配置
- **示例**:
  ```javascript
  // 混合模式配置
  mode: {
    user: 'single',    // 用户单选
    role: 'multiple',  // 角色多选
    org: 'single'      // 组织单选
  }
  ```

### 显示控制配置

#### edit
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否允许编辑选择
- **示例**:
  ```javascript
  // 只读模式
  edit: false
  
  // 编辑模式
  edit: true
  ```

#### hide
- **类型**: `Object`
- **默认值**: `{ role: false, user: false, org: false }`
- **说明**: 控制各选项卡的显示隐藏
- **示例**:
  ```javascript
  // 隐藏角色选项卡
  hide: {
    role: true,
    user: false,
    org: false
  }
  ```

#### activeName
- **类型**: `string`
- **默认值**: `'role'`
- **说明**: 默认激活的选项卡
- **示例**:
  ```javascript
  // 默认显示用户选项卡
  activeName: 'user'
  ```

#### tabPaneHeight
- **类型**: `string`
- **默认值**: `'500px'`
- **说明**: 选项卡面板高度
- **示例**:
  ```javascript
  // 自定义高度
  tabPaneHeight: '400px'
  ```

### 扩展配置

#### extendTabs
- **类型**: `ExtendTab[]`
- **默认值**: `[]`
- **说明**: 扩展选项卡配置
- **示例**:
  ```javascript
  extendTabs: [
    {
      name: 'custom',
      label: '自定义选择'
    },
    {
      name: 'external',
      label: '外部数据'
    }
  ]
  ```

## 常用配置组合示例

### 基础用户选择器
```javascript
const config = {
  type: 'user',
  mode: {
    user: 'single'
  },
  hide: {
    role: true,
    org: true
  },
  activeName: 'user',
  tabPaneHeight: '300px'
}
```

### 多类型选择器
```javascript
const config = {
  type: 'user',
  mode: {
    user: 'multiple',
    role: 'multiple',
    org: 'single'
  },
  hide: {
    role: false,
    user: false,
    org: false
  },
  activeName: 'role',
  tabPaneHeight: '500px'
}
```

### 带扩展选项卡的选择器
```javascript
const config = {
  type: 'user',
  extendTabs: [
    {
      name: 'department',
      label: '部门选择'
    },
    {
      name: 'position',
      label: '职位选择'
    }
  ],
  activeName: 'department'
}
```

### 自定义API配置
```javascript
const config = {
  type: 'user',
  userProps: {
    orgRequest: {
      api: '/api/custom/org-tree',
      method: 'post',
      param: {
        includeDisabled: false,
        level: 3
      }
    },
    userRequest: {
      api: '/api/custom/user-list',
      method: 'post',
      param: {
        status: 'active'
      }
    },
    orgDefaultProps: {
      id: 'orgId',
      name: 'orgName',
      children: 'subOrgs'
    },
    userDefaultProps: {
      id: 'userId',
      name: 'userName'
    }
  }
}
```

### 权限控制配置
```javascript
const config = {
  type: 'user',
  edit: hasEditPermission,
  userProps: {
    orgPowerMethod: (orgData) => {
      // 自定义权限过滤逻辑
      return orgData.filter(org => 
        userPermissions.includes(org.id)
      )
    }
  }
}
```

## 最佳实践建议

### 1. 类型选择建议
- 根据业务场景选择合适的type
- 单一类型选择时隐藏其他选项卡
- 多类型选择时合理设置默认激活选项卡

### 2. 模式配置建议
- 根据业务需求设置单选或多选模式
- 考虑用户体验，避免过于复杂的选择逻辑
- 提供清晰的选择状态反馈

### 3. 性能优化建议
- 大数据量时使用分页加载
- 合理设置组件高度避免页面布局问题
- 使用权限过滤减少不必要的数据加载

### 4. 扩展性建议
- 使用extendTabs扩展自定义选择类型
- 通过插槽机制实现复杂的自定义界面
- 保持配置结构的一致性和可维护性
