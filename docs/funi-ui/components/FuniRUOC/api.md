# FuniRUOC API文档

## 组件概述

FuniRUOC（Role User Organization Company）是角色用户组织选择器组件，基于ElementPlus的el-select封装，支持角色、用户、组织、公司等多种类型的数据选择，适用于权限管理、人员选择等场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| modelValue | Any | - | - | 绑定值 |
| type | String | 'user' | ✅ | 选择类型：'role'、'user'、'org'、'company' |
| multiple | Boolean | false | - | 是否多选 |
| placeholder | String | - | - | 占位符文本，默认根据type自动生成 |
| clearable | Boolean | true | - | 是否可清空 |
| filterable | Boolean | true | - | 是否可搜索 |
| remote | Boolean | true | - | 是否远程搜索 |
| remoteMethod | Function | - | - | 自定义远程搜索方法 |
| api | String/Object | - | - | API配置 |
| params | Object | {} | - | 额外的请求参数 |
| props | Object | - | - | 字段映射配置 |
| transform | Function | - | - | 数据转换函数 |
| size | String | 'default' | - | 组件尺寸 |
| disabled | Boolean | false | - | 是否禁用 |
| loading | Boolean | false | - | 是否加载中 |
| loadingText | String | '加载中...' | - | 加载中文本 |
| noDataText | String | '暂无数据' | - | 无数据文本 |
| noMatchText | String | '无匹配数据' | - | 无匹配文本 |
| multipleLimit | Number | 0 | - | 多选限制数量 |
| collapseTags | Boolean | false | - | 是否折叠标签 |
| collapseTagsTooltip | Boolean | false | - | 折叠标签提示 |
| reserveKeyword | Boolean | false | - | 是否保留关键字 |
| defaultFirstOption | Boolean | false | - | 是否默认选择第一个选项 |
| popperClass | String | '' | - | 下拉框的类名 |
| teleported | Boolean | true | - | 是否将下拉菜单teleport至body |
| persistent | Boolean | true | - | 当下拉选择器未被激活并且persistent设置为false时，选择器会被删除 |
| automaticDropdown | Boolean | false | - | 对于不可搜索的Select，是否在输入框获得焦点后自动弹出选项菜单 |
| clearIcon | String | 'CircleClose' | - | 清空图标 |
| fitInputWidth | Boolean | false | - | 下拉框的宽度是否与输入框相同 |
| suffixIcon | String | 'ArrowDown' | - | 自定义后缀图标 |
| tagType | String | 'info' | - | 标签类型 |
| validateEvent | Boolean | true | - | 是否触发表单验证 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: Any | 值更新事件 | 选择值变化时 |
| change | value: Any | 值变化事件 | 选择值变化时 |
| visible-change | visible: Boolean | 下拉框显示状态变化 | 下拉框显示/隐藏时 |
| remove-tag | value: Any | 移除标签事件 | 多选模式下移除标签时 |
| clear | - | 清空事件 | 点击清空按钮时 |
| blur | event: Event | 失去焦点事件 | 输入框失去焦点时 |
| focus | event: Event | 获得焦点事件 | 输入框获得焦点时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| focus | - | void | 使选择器获得焦点 |
| blur | - | void | 使选择器失去焦点 |
| refresh | - | Promise | 刷新选项数据 |

## type类型说明

### 支持的类型
- **'role'**: 角色选择
- **'user'**: 用户选择
- **'org'**: 组织选择
- **'company'**: 公司选择

### 默认配置
```typescript
const typeConfig = {
  role: {
    placeholder: '请选择角色',
    api: '/api/roles',
    props: { label: 'roleName', value: 'roleId' }
  },
  user: {
    placeholder: '请选择用户',
    api: '/api/users',
    props: { label: 'userName', value: 'userId' }
  },
  org: {
    placeholder: '请选择组织',
    api: '/api/organizations',
    props: { label: 'orgName', value: 'orgId' }
  },
  company: {
    placeholder: '请选择公司',
    api: '/api/companies',
    props: { label: 'companyName', value: 'companyId' }
  }
};
```

## API配置结构

```typescript
interface APIConfig {
  // 字符串形式
  url?: string;                    // API地址
  
  // 对象形式
  list?: string;                   // 列表查询API
  search?: string;                 // 搜索API
  detail?: string;                 // 详情API
  
  // 函数形式
  method?: (params: any) => Promise<any>; // 自定义API方法
}
```

## 数据结构

### 角色数据结构
```typescript
interface RoleData {
  roleId: string;                  // 角色ID
  roleName: string;                // 角色名称
  roleCode?: string;               // 角色编码
  description?: string;            // 角色描述
  status?: number;                 // 状态
  createTime?: string;             // 创建时间
  [key: string]: any;              // 其他字段
}
```

### 用户数据结构
```typescript
interface UserData {
  userId: string;                  // 用户ID
  userName: string;                // 用户名称
  userCode?: string;               // 用户编码
  email?: string;                  // 邮箱
  phone?: string;                  // 手机号
  avatar?: string;                 // 头像
  orgId?: string;                  // 所属组织ID
  orgName?: string;                // 所属组织名称
  status?: number;                 // 状态
  [key: string]: any;              // 其他字段
}
```

### 组织数据结构
```typescript
interface OrgData {
  orgId: string;                   // 组织ID
  orgName: string;                 // 组织名称
  orgCode?: string;                // 组织编码
  parentId?: string;               // 父级组织ID
  level?: number;                  // 组织层级
  sort?: number;                   // 排序
  status?: number;                 // 状态
  [key: string]: any;              // 其他字段
}
```

### 公司数据结构
```typescript
interface CompanyData {
  companyId: string;               // 公司ID
  companyName: string;             // 公司名称
  companyCode?: string;            // 公司编码
  address?: string;                // 地址
  phone?: string;                  // 电话
  email?: string;                  // 邮箱
  status?: number;                 // 状态
  [key: string]: any;              // 其他字段
}
```

## 使用示例

### 基础用户选择
```vue
<template>
  <FuniRUOC
    v-model="selectedUser"
    type="user"
    placeholder="请选择用户"
    @change="handleUserChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedUser = ref('')

const handleUserChange = (value) => {
  console.log('选择的用户:', value)
}
</script>
```

### 多选角色选择
```vue
<template>
  <FuniRUOC
    v-model="selectedRoles"
    type="role"
    multiple
    collapse-tags
    collapse-tags-tooltip
    :multiple-limit="5"
    placeholder="请选择角色（最多5个）"
    @change="handleRoleChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedRoles = ref([])

const handleRoleChange = (values) => {
  console.log('选择的角色:', values)
}
</script>
```

### 自定义API配置
```vue
<template>
  <FuniRUOC
    v-model="selectedOrg"
    type="org"
    :api="orgApi"
    :params="orgParams"
    :props="orgProps"
    :transform="transformOrgData"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedOrg = ref('')

const orgApi = {
  list: '/api/organizations/list',
  search: '/api/organizations/search'
}

const orgParams = {
  status: 1,
  level: 2
}

const orgProps = {
  label: 'name',
  value: 'id'
}

const transformOrgData = (data) => {
  return data.map(item => ({
    ...item,
    label: `${item.name} (${item.code})`,
    value: item.id
  }))
}
</script>
```

### 自定义远程搜索
```vue
<template>
  <FuniRUOC
    v-model="selectedUser"
    type="user"
    :remote-method="customSearch"
    :loading="searchLoading"
    placeholder="请输入用户名搜索"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedUser = ref('')
const searchLoading = ref(false)

const customSearch = async (query) => {
  if (!query) return
  
  searchLoading.value = true
  try {
    const response = await userApi.search({
      keyword: query,
      pageSize: 20
    })
    
    // 返回搜索结果
    return response.data.list.map(user => ({
      label: `${user.userName} (${user.email})`,
      value: user.userId,
      ...user
    }))
  } catch (error) {
    console.error('搜索失败:', error)
    return []
  } finally {
    searchLoading.value = false
  }
}
</script>
```

### 带权限控制的选择器
```vue
<template>
  <FuniRUOC
    v-model="selectedUser"
    type="user"
    :params="userParams"
    :disabled="!hasPermission"
    placeholder="请选择用户"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const selectedUser = ref('')

const hasPermission = computed(() => {
  return userStore.hasPermission('user:select')
})

const userParams = computed(() => ({
  orgId: userStore.currentUser.orgId,
  status: 1
}))
</script>
```

### 自定义选项模板
```vue
<template>
  <FuniRUOC
    v-model="selectedUser"
    type="user"
    placeholder="请选择用户"
  >
    <template #default="{ option }">
      <div class="user-option">
        <el-avatar :src="option.avatar" size="small" />
        <div class="user-info">
          <div class="user-name">{{ option.userName }}</div>
          <div class="user-org">{{ option.orgName }}</div>
        </div>
        <el-tag v-if="option.online" type="success" size="small">在线</el-tag>
      </div>
    </template>
  </FuniRUOC>
</template>

<script setup>
import { ref } from 'vue'

const selectedUser = ref('')
</script>

<style scoped>
.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  font-size: 14px;
}

.user-org {
  font-size: 12px;
  color: #999;
}
</style>
```

### 组织树形选择
```vue
<template>
  <FuniRUOC
    v-model="selectedOrg"
    type="org"
    :api="orgTreeApi"
    :transform="transformOrgTree"
    placeholder="请选择组织"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedOrg = ref('')

const orgTreeApi = '/api/organizations/tree'

const transformOrgTree = (data) => {
  // 将树形数据转换为平铺数据
  const flattenTree = (nodes, level = 0) => {
    let result = []
    nodes.forEach(node => {
      result.push({
        label: '　'.repeat(level) + node.orgName,
        value: node.orgId,
        level,
        ...node
      })
      if (node.children && node.children.length > 0) {
        result = result.concat(flattenTree(node.children, level + 1))
      }
    })
    return result
  }
  
  return flattenTree(data)
}
</script>
```

## 高级用法

### 联动选择
```vue
<template>
  <div class="cascader-select">
    <FuniRUOC
      v-model="selectedCompany"
      type="company"
      placeholder="请选择公司"
      @change="handleCompanyChange"
    />
    
    <FuniRUOC
      v-model="selectedOrg"
      type="org"
      :params="orgParams"
      :disabled="!selectedCompany"
      placeholder="请选择组织"
      @change="handleOrgChange"
    />
    
    <FuniRUOC
      v-model="selectedUser"
      type="user"
      :params="userParams"
      :disabled="!selectedOrg"
      placeholder="请选择用户"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const selectedCompany = ref('')
const selectedOrg = ref('')
const selectedUser = ref('')

const orgParams = computed(() => ({
  companyId: selectedCompany.value
}))

const userParams = computed(() => ({
  orgId: selectedOrg.value
}))

const handleCompanyChange = (value) => {
  selectedOrg.value = ''
  selectedUser.value = ''
}

const handleOrgChange = (value) => {
  selectedUser.value = ''
}
</script>

<style scoped>
.cascader-select {
  display: flex;
  gap: 16px;
}
</style>
```

### 批量操作
```vue
<template>
  <div>
    <div class="batch-actions">
      <el-button @click="selectAll">全选</el-button>
      <el-button @click="clearAll">清空</el-button>
      <el-button @click="selectByRole">按角色选择</el-button>
    </div>
    
    <FuniRUOC
      v-model="selectedUsers"
      type="user"
      multiple
      collapse-tags
      placeholder="请选择用户"
    />
    
    <div class="selected-info">
      已选择 {{ selectedUsers.length }} 个用户
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedUsers = ref([])

const selectAll = async () => {
  const response = await userApi.getAll()
  selectedUsers.value = response.data.map(user => user.userId)
}

const clearAll = () => {
  selectedUsers.value = []
}

const selectByRole = async () => {
  const roleId = await selectRole() // 假设有选择角色的方法
  if (roleId) {
    const response = await userApi.getByRole(roleId)
    selectedUsers.value = response.data.map(user => user.userId)
  }
}
</script>

<style scoped>
.batch-actions {
  margin-bottom: 16px;
}

.selected-info {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}
</style>
```

## 注意事项

### 1. 类型配置
- 确保type属性正确设置
- 不同类型对应不同的API和数据结构
- 可以通过props自定义字段映射

### 2. 权限控制
- 根据用户权限控制组件的可用性
- 可以通过params传递权限相关参数
- 支持动态禁用和启用

### 3. 性能优化
- 大数据量时使用远程搜索
- 合理设置搜索防抖
- 使用虚拟滚动处理超大数据集

### 4. 数据处理
- 统一数据格式和字段命名
- 提供数据转换和格式化功能
- 处理异步加载的错误情况

## 常见问题

### Q: 如何自定义API接口？
A: 通过api属性配置自定义接口地址，支持字符串、对象、函数等多种形式

### Q: 如何实现级联选择？
A: 监听上级选择器的change事件，动态更新下级选择器的params参数

### Q: 如何处理大量数据的性能问题？
A: 使用远程搜索模式，按需加载数据，避免一次性加载大量选项

### Q: 如何自定义选项显示格式？
A: 使用默认插槽自定义选项模板，或者通过transform函数处理数据格式
