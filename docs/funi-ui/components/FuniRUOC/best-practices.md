# FuniRUOC 最佳实践

## ⚠️ 强制使用场景

### 必须使用FuniRUOC的场景
1. **角色选择**：任何涉及角色选择的表单项必须使用 `type="role"`
2. **用户选择**：任何涉及用户选择的表单项必须使用 `type="user"`
3. **部门/组织选择**：推荐使用 `type="org"`，避免使用el-select

### 禁止使用el-select的场景
```javascript
// ❌ 禁止：使用el-select进行角色选择
{
  component: 'el-select',
  options: roleOptions // 禁止这种方式
}

// ✅ 正确：使用FuniRUOC进行角色选择
{
  component: 'FuniRUOC',
  props: {
    type: 'role',
    multiple: true
  }
}
```

## 推荐用法和配置

### 1. 基础配置最佳实践

#### 选择合适的类型和模式
```vue
<template>
  <!-- ✅ 推荐：明确指定类型和模式 -->
  <FuniRUOC
    v-model="selectedUsers"
    type="user"
    :mode="{ user: 'multiple' }"
    :hide="{ role: true, org: true }"
    activeName="user"
    placeholder="请选择用户"
  />
  
  <!-- ❌ 不推荐：使用默认配置可能不符合业务需求 -->
  <FuniRUOC v-model="selectedUsers" />
</template>
```

#### 合理设置组件尺寸
```vue
<template>
  <!-- ✅ 推荐：根据容器大小设置合适的高度 -->
  <FuniRUOC
    v-model="selectedItems"
    type="user"
    tabPaneHeight="400px"
    class="user-selector"
  />
</template>

<style scoped>
.user-selector {
  width: 100%;
  max-width: 800px;
}
</style>
```

### 2. 数据处理最佳实践

#### API 配置规范
```javascript
// ✅ 推荐：统一的API配置格式
const apiConfig = {
  roleProps: {
    request: {
      api: '/api/roles/list',
      method: 'post',
      param: {
        status: 'active',
        pageSize: 100
      }
    },
    defaultProps: {
      id: 'roleId',
      name: 'roleName'
    },
    searchName: 'keyword'
  },
  userProps: {
    orgRequest: {
      api: '/api/organizations/tree',
      method: 'get',
      param: {}
    },
    userRequest: {
      api: '/api/users/list',
      method: 'post',
      param: {
        status: 'active'
      }
    },
    orgDefaultProps: {
      id: 'orgId',
      name: 'orgName',
      children: 'children'
    },
    userDefaultProps: {
      id: 'userId',
      name: 'userName'
    }
  }
}
```

#### 数据格式标准化
```javascript
// ✅ 推荐：标准化的数据处理
const normalizeUserData = (rawData) => {
  return rawData.map(user => ({
    id: user.userId || user.id,
    name: user.userName || user.name || user.nickName,
    type: 'user',
    avatar: user.avatar,
    orgName: user.orgName,
    email: user.email,
    phone: user.phone
  }))
}

// ✅ 推荐：统一的选择结果格式
const handleSelectionChange = (selectedItems) => {
  const formattedResult = {
    roles: selectedItems.filter(item => item.type === 'role'),
    users: selectedItems.filter(item => item.type === 'user'),
    orgs: selectedItems.filter(item => item.type === 'org'),
    total: selectedItems.length
  }
  
  console.log('选择结果:', formattedResult)
}
```

### 3. 性能优化最佳实践

#### 大数据量处理
```vue
<template>
  <!-- ✅ 推荐：使用分页和搜索优化大数据量 -->
  <FuniRUOC
    v-model="selectedUsers"
    type="user"
    :userProps="{
      userRequest: {
        api: '/api/users/search',
        method: 'post',
        param: {
          pageSize: 50,
          searchDelay: 300
        }
      }
    }"
    @search="handleUserSearch"
  />
</template>

<script setup>
import { ref, debounce } from 'vue'

// ✅ 推荐：使用防抖优化搜索性能
const handleUserSearch = debounce((keyword) => {
  if (keyword.length < 2) return
  
  // 执行搜索逻辑
  searchUsers(keyword)
}, 300)

const searchUsers = async (keyword) => {
  try {
    const response = await userApi.search({
      keyword,
      pageSize: 50
    })
    // 处理搜索结果
  } catch (error) {
    console.error('搜索失败:', error)
  }
}
</script>
```

#### 内存管理优化
```javascript
// ✅ 推荐：及时清理不需要的数据
const cleanupComponent = () => {
  // 清理大型数据对象
  selectedUsers.value = []
  cachedOrgData.value = null
  
  // 取消未完成的请求
  if (pendingRequest.value) {
    pendingRequest.value.abort()
    pendingRequest.value = null
  }
}

// 在组件卸载时清理
onUnmounted(() => {
  cleanupComponent()
})
```

### 4. 用户体验最佳实践

#### 加载状态处理
```vue
<template>
  <div class="selector-container">
    <!-- ✅ 推荐：提供加载状态反馈 -->
    <el-skeleton v-if="loading" :rows="5" animated />
    
    <FuniRUOC
      v-else
      v-model="selectedItems"
      type="user"
      :loading="apiLoading"
      @update:modelValue="handleChange"
    />
    
    <!-- ✅ 推荐：显示选择状态 -->
    <div class="selection-summary" v-if="selectedItems.length">
      已选择 {{ selectedItems.length }} 项
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const loading = ref(true)
const apiLoading = ref(false)
const selectedItems = ref([])

// ✅ 推荐：提供选择状态反馈
const selectionSummary = computed(() => {
  const summary = {
    roles: selectedItems.value.filter(item => item.type === 'role').length,
    users: selectedItems.value.filter(item => item.type === 'user').length,
    orgs: selectedItems.value.filter(item => item.type === 'org').length
  }
  
  return `角色: ${summary.roles}, 用户: ${summary.users}, 组织: ${summary.orgs}`
})
</script>
```

#### 错误处理和用户提示
```vue
<template>
  <div class="selector-wrapper">
    <FuniRUOC
      v-model="selectedItems"
      type="user"
      @error="handleError"
      @success="handleSuccess"
    />
    
    <!-- ✅ 推荐：友好的错误提示 -->
    <el-alert
      v-if="errorMessage"
      :title="errorMessage"
      type="error"
      :closable="true"
      @close="clearError"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const errorMessage = ref('')

// ✅ 推荐：统一的错误处理
const handleError = (error) => {
  console.error('组件错误:', error)
  
  const errorMessages = {
    'NETWORK_ERROR': '网络连接失败，请检查网络设置',
    'API_ERROR': '数据加载失败，请稍后重试',
    'PERMISSION_ERROR': '权限不足，无法访问该数据',
    'VALIDATION_ERROR': '数据验证失败，请检查输入'
  }
  
  errorMessage.value = errorMessages[error.code] || '操作失败，请稍后重试'
  
  // 自动清除错误提示
  setTimeout(() => {
    clearError()
  }, 5000)
}

const handleSuccess = (message) => {
  ElMessage.success(message || '操作成功')
}

const clearError = () => {
  errorMessage.value = ''
}
</script>
```

### 5. 业务场景最佳实践

#### 权限管理场景
```vue
<template>
  <div class="permission-manager">
    <!-- ✅ 推荐：权限控制的选择器配置 -->
    <FuniRUOC
      v-model="permissionAssignees"
      type="user"
      :mode="permissionMode"
      :userProps="userPropsWithPermission"
      :disabled="!hasManagePermission"
      @update:modelValue="validatePermissionAssignment"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserPermissions } from '@/composables/usePermissions'

const { hasPermission } = useUserPermissions()

// ✅ 推荐：基于权限的配置
const hasManagePermission = computed(() => 
  hasPermission('user.manage') || hasPermission('admin')
)

const permissionMode = computed(() => ({
  role: hasPermission('role.assign') ? 'multiple' : 'single',
  user: hasPermission('user.batch') ? 'multiple' : 'single',
  org: hasPermission('org.manage') ? 'multiple' : 'single'
}))

// ✅ 推荐：权限过滤的数据配置
const userPropsWithPermission = computed(() => ({
  orgPowerMethod: (orgData) => {
    return orgData.filter(org => 
      hasPermission(`org.${org.id}.view`)
    )
  },
  userRequest: {
    api: '/api/users/list',
    method: 'post',
    param: {
      accessLevel: getCurrentUserLevel()
    }
  }
}))

const validatePermissionAssignment = (assignees) => {
  // ✅ 推荐：验证权限分配的合法性
  const invalidAssignees = assignees.filter(assignee => 
    !canAssignToTarget(assignee)
  )
  
  if (invalidAssignees.length > 0) {
    ElMessage.warning('部分选择对象权限不足，已自动移除')
    // 移除无权限的对象
    permissionAssignees.value = assignees.filter(assignee => 
      canAssignToTarget(assignee)
    )
  }
}
</script>
```

#### 工作流审批场景
```vue
<template>
  <div class="workflow-approver">
    <!-- ✅ 推荐：工作流场景的配置 -->
    <FuniRUOC
      v-model="approvers"
      type="user"
      :mode="approverMode"
      :userProps="approverUserProps"
      :extendTabs="workflowTabs"
      @update:modelValue="validateApproverSelection"
    >
      <!-- 自定义审批规则选项卡 -->
      <template #rules>
        <div class="approval-rules">
          <el-form :model="approvalRules" label-width="120px">
            <el-form-item label="审批类型">
              <el-radio-group v-model="approvalRules.type">
                <el-radio label="sequential">顺序审批</el-radio>
                <el-radio label="parallel">并行审批</el-radio>
                <el-radio label="any">任意一人审批</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="超时处理">
              <el-select v-model="approvalRules.timeoutAction">
                <el-option label="自动通过" value="auto-approve" />
                <el-option label="自动拒绝" value="auto-reject" />
                <el-option label="转交上级" value="escalate" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </FuniRUOC>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const approvers = ref([])
const approvalRules = ref({
  type: 'sequential',
  timeoutAction: 'escalate'
})

// ✅ 推荐：工作流特定的配置
const approverMode = computed(() => ({
  user: approvalRules.value.type === 'any' ? 'single' : 'multiple',
  role: 'multiple',
  org: 'single'
}))

const workflowTabs = [
  { name: 'rules', label: '审批规则' }
]

// ✅ 推荐：审批人选择验证
const validateApproverSelection = (selectedApprovers) => {
  const validation = {
    hasApprovers: selectedApprovers.length > 0,
    hasValidLevel: selectedApprovers.every(approver => 
      approver.level >= getRequiredApprovalLevel()
    ),
    noDuplicates: new Set(selectedApprovers.map(a => a.id)).size === selectedApprovers.length
  }
  
  if (!validation.hasApprovers) {
    ElMessage.warning('请至少选择一个审批人')
    return false
  }
  
  if (!validation.hasValidLevel) {
    ElMessage.warning('部分审批人权限级别不足')
    return false
  }
  
  if (!validation.noDuplicates) {
    ElMessage.warning('审批人不能重复')
    return false
  }
  
  return true
}
</script>
```

## 避免的用法和常见错误

### 1. 配置错误

```vue
<!-- ❌ 错误：不一致的数据格式 -->
<FuniRUOC
  v-model="selectedItems"
  type="user"
  :userProps="{
    orgDefaultProps: {
      id: 'id',
      name: 'name'  // 应该与实际API返回字段一致
    }
  }"
/>

<!-- ✅ 正确：确保字段映射正确 -->
<FuniRUOC
  v-model="selectedItems"
  type="user"
  :userProps="{
    orgDefaultProps: {
      id: 'orgId',
      name: 'orgName',  // 与API返回字段一致
      children: 'subOrganizations'
    }
  }"
/>
```

### 2. 性能问题

```javascript
// ❌ 错误：没有防抖的搜索
const handleSearch = (keyword) => {
  // 每次输入都会触发请求
  searchAPI(keyword)
}

// ✅ 正确：使用防抖优化
const handleSearch = debounce((keyword) => {
  if (keyword.length >= 2) {
    searchAPI(keyword)
  }
}, 300)
```

### 3. 内存泄漏

```javascript
// ❌ 错误：没有清理定时器和请求
const component = {
  setup() {
    const timer = setInterval(() => {
      // 定时任务
    }, 1000)
    
    // 组件销毁时没有清理
  }
}

// ✅ 正确：及时清理资源
const component = {
  setup() {
    const timer = setInterval(() => {
      // 定时任务
    }, 1000)
    
    onUnmounted(() => {
      clearInterval(timer)
    })
  }
}
```

## 总结

### 核心原则
1. **明确配置**：始终明确指定组件的类型、模式和关键配置
2. **数据标准化**：统一数据格式和字段映射
3. **性能优先**：合理使用防抖、分页和缓存
4. **用户体验**：提供清晰的状态反馈和错误处理
5. **权限控制**：在需要的场景下实现适当的权限验证

### 开发建议
1. 在开发前仔细阅读API文档和配置说明
2. 使用TypeScript提高代码质量和开发效率
3. 编写单元测试确保组件功能正确
4. 定期review代码，优化性能和用户体验
5. 保持组件配置的一致性和可维护性
