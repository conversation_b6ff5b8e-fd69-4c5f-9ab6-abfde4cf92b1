# FuniRUOC 使用示例

## 基础示例

### 1. 基础角色选择器
```vue
<template>
  <div class="example-container">
    <h3>基础角色选择器</h3>
    <FuniRUOC
      v-model="selectedRoles"
      type="role"
      :mode="{ role: 'multiple' }"
      :hide="{ user: true, org: true }"
      activeName="role"
      tabPaneHeight="300px"
    />
    
    <div class="result">
      <h4>选择结果：</h4>
      <pre>{{ JSON.stringify(selectedRoles, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedRoles = ref([])
</script>

<style scoped>
.example-container {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 20px;
}

.result {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
```

### 2. 基础用户选择器
```vue
<template>
  <div class="example-container">
    <h3>基础用户选择器</h3>
    <FuniRUOC
      v-model="selectedUsers"
      type="user"
      :mode="{ user: 'single' }"
      :hide="{ role: true, org: true }"
      activeName="user"
      tabPaneHeight="400px"
      :userProps="userConfig"
    />
    
    <div class="result">
      <h4>选择的用户：</h4>
      <div v-if="selectedUsers.length">
        <div v-for="user in selectedUsers" :key="user.id" class="user-item">
          <span>{{ user.name }} ({{ user.id }})</span>
        </div>
      </div>
      <div v-else>暂无选择</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedUsers = ref([])

const userConfig = {
  orgRequest: {
    api: '/csccs/orgList/orgTree',
    method: 'fetch',
    param: {}
  },
  userRequest: {
    api: 'csccs/govAccountOrg/findAccountList',
    method: 'post',
    param: {}
  },
  orgDefaultProps: {
    id: 'id',
    name: 'name',
    children: 'children'
  },
  userDefaultProps: {
    id: 'id',
    name: 'nickName'
  },
  mode: 'single'
}
</script>

<style scoped>
.user-item {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}
</style>
```

### 3. 基础组织选择器
```vue
<template>
  <div class="example-container">
    <h3>基础组织选择器</h3>
    <FuniRUOC
      v-model="selectedOrgs"
      type="org"
      :mode="{ org: 'multiple' }"
      :hide="{ role: true, user: true }"
      activeName="org"
      tabPaneHeight="350px"
      :orgProps="orgConfig"
    />
    
    <div class="result">
      <h4>选择的组织：</h4>
      <el-tag 
        v-for="org in selectedOrgs" 
        :key="org.id" 
        type="info" 
        class="org-tag"
      >
        {{ org.name }}
      </el-tag>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedOrgs = ref([])

const orgConfig = {
  request: {
    api: '/csccs/orgList/orgTree',
    method: 'fetch',
    param: {}
  },
  defaultProps: {
    id: 'id',
    name: 'orgName',
    children: 'children'
  },
  searchName: 'orgName',
  mode: 'multiple'
}
</script>

<style scoped>
.org-tag {
  margin: 2px;
}
</style>
```

## 高级示例

### 4. 多类型综合选择器
```vue
<template>
  <div class="example-container">
    <h3>多类型综合选择器</h3>
    <FuniRUOC
      v-model="selectedItems"
      type="user"
      :mode="selectionMode"
      :hide="hideConfig"
      :activeName="activeTab"
      tabPaneHeight="500px"
      :roleProps="roleConfig"
      :userProps="userConfig"
      :orgProps="orgConfig"
      @update:modelValue="handleSelectionChange"
    />
    
    <div class="controls">
      <h4>控制面板：</h4>
      <el-row :gutter="20">
        <el-col :span="8">
          <label>默认选项卡：</label>
          <el-select v-model="activeTab" placeholder="选择默认选项卡">
            <el-option label="角色" value="role" />
            <el-option label="用户" value="user" />
            <el-option label="组织" value="org" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <label>选择模式：</label>
          <el-checkbox-group v-model="modeSettings">
            <el-checkbox label="role-multiple">角色多选</el-checkbox>
            <el-checkbox label="user-multiple">用户多选</el-checkbox>
            <el-checkbox label="org-multiple">组织多选</el-checkbox>
          </el-checkbox-group>
        </el-col>
        <el-col :span="8">
          <label>隐藏选项卡：</label>
          <el-checkbox-group v-model="hiddenTabs">
            <el-checkbox label="role">隐藏角色</el-checkbox>
            <el-checkbox label="user">隐藏用户</el-checkbox>
            <el-checkbox label="org">隐藏组织</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
    </div>
    
    <div class="result">
      <h4>选择结果统计：</h4>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="总数">{{ selectedItems.length }}</el-descriptions-item>
        <el-descriptions-item label="角色数">{{ roleCount }}</el-descriptions-item>
        <el-descriptions-item label="用户数">{{ userCount }}</el-descriptions-item>
        <el-descriptions-item label="组织数">{{ orgCount }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const selectedItems = ref([])
const activeTab = ref('role')
const modeSettings = ref(['role-multiple', 'user-multiple'])
const hiddenTabs = ref([])

// 计算选择模式
const selectionMode = computed(() => ({
  role: modeSettings.value.includes('role-multiple') ? 'multiple' : 'single',
  user: modeSettings.value.includes('user-multiple') ? 'multiple' : 'single',
  org: modeSettings.value.includes('org-multiple') ? 'multiple' : 'single'
}))

// 计算隐藏配置
const hideConfig = computed(() => ({
  role: hiddenTabs.value.includes('role'),
  user: hiddenTabs.value.includes('user'),
  org: hiddenTabs.value.includes('org')
}))

// 统计各类型数量
const roleCount = computed(() => 
  selectedItems.value.filter(item => item.type === 'role').length
)
const userCount = computed(() => 
  selectedItems.value.filter(item => item.type === 'user').length
)
const orgCount = computed(() => 
  selectedItems.value.filter(item => item.type === 'org').length
)

// 配置对象
const roleConfig = {
  request: {
    api: '/csccs/roleList/roleList',
    method: 'post',
    param: { flag: false }
  },
  defaultProps: {
    id: 'id',
    name: 'name'
  },
  searchName: 'keyword'
}

const userConfig = {
  orgRequest: {
    api: '/csccs/orgList/orgTree',
    method: 'fetch',
    param: {}
  },
  userRequest: {
    api: 'csccs/govAccountOrg/findAccountList',
    method: 'post',
    param: {}
  }
}

const orgConfig = {
  request: {
    api: '/csccs/orgList/orgTree',
    method: 'fetch',
    param: {}
  },
  defaultProps: {
    id: 'id',
    name: 'orgName',
    children: 'children'
  },
  searchName: 'orgName'
}

const handleSelectionChange = (value) => {
  console.log('选择变化:', value)
}
</script>
</template>
```

### 5. 带扩展选项卡的选择器
```vue
<template>
  <div class="example-container">
    <h3>带扩展选项卡的选择器</h3>
    <FuniRUOC
      v-model="selectedItems"
      type="user"
      :extendTabs="customTabs"
      activeName="department"
      tabPaneHeight="400px"
    >
      <!-- 部门选择扩展 -->
      <template #department>
        <div class="custom-tab-content">
          <h4>部门选择</h4>
          <el-tree
            :data="departmentData"
            :props="{ label: 'name', children: 'children' }"
            show-checkbox
            node-key="id"
            @check="handleDepartmentCheck"
          />
        </div>
      </template>
      
      <!-- 职位选择扩展 -->
      <template #position>
        <div class="custom-tab-content">
          <h4>职位选择</h4>
          <el-checkbox-group v-model="selectedPositions" @change="handlePositionChange">
            <el-checkbox 
              v-for="position in positionData" 
              :key="position.id" 
              :label="position.id"
            >
              {{ position.name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </template>
    </FuniRUOC>
    
    <div class="result">
      <h4>扩展选择结果：</h4>
      <div>部门：{{ selectedDepartments.map(d => d.name).join(', ') }}</div>
      <div>职位：{{ selectedPositions.join(', ') }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedItems = ref([])
const selectedDepartments = ref([])
const selectedPositions = ref([])

const customTabs = [
  { name: 'department', label: '部门选择' },
  { name: 'position', label: '职位选择' }
]

const departmentData = ref([
  {
    id: 1,
    name: '技术部',
    children: [
      { id: 11, name: '前端组' },
      { id: 12, name: '后端组' }
    ]
  },
  {
    id: 2,
    name: '产品部',
    children: [
      { id: 21, name: '产品组' },
      { id: 22, name: '设计组' }
    ]
  }
])

const positionData = ref([
  { id: 'dev', name: '开发工程师' },
  { id: 'pm', name: '产品经理' },
  { id: 'designer', name: '设计师' },
  { id: 'tester', name: '测试工程师' }
])

const handleDepartmentCheck = (data, checkedInfo) => {
  selectedDepartments.value = checkedInfo.checkedNodes
}

const handlePositionChange = (values) => {
  console.log('职位选择变化:', values)
}
</script>

<style scoped>
.custom-tab-content {
  padding: 20px;
}
</style>
```

## 业务场景示例

### 6. 权限管理场景
```vue
<template>
  <div class="example-container">
    <h3>权限管理 - 角色用户分配</h3>

    <!-- 权限配置表单 -->
    <el-form :model="permissionForm" label-width="120px">
      <el-form-item label="权限名称">
        <el-input v-model="permissionForm.name" placeholder="请输入权限名称" />
      </el-form-item>

      <el-form-item label="分配对象">
        <FuniRUOC
          v-model="permissionForm.assignees"
          type="user"
          :mode="{ role: 'multiple', user: 'multiple', org: 'single' }"
          tabPaneHeight="350px"
          @update:modelValue="handleAssigneeChange"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="savePermission">保存权限配置</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 分配结果预览 -->
    <div class="assignment-preview">
      <h4>分配预览：</h4>
      <el-table :data="assignmentPreview" border>
        <el-table-column prop="type" label="类型" width="80">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)">{{ getTypeName(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="id" label="ID" width="120" />
        <el-table-column label="操作" width="100">
          <template #default="{ row, $index }">
            <el-button
              type="danger"
              size="small"
              @click="removeAssignee($index)"
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const permissionForm = ref({
  name: '',
  assignees: []
})

const assignmentPreview = computed(() => {
  return permissionForm.value.assignees.map(item => ({
    ...item,
    typeName: getTypeName(item.type)
  }))
})

const getTypeName = (type) => {
  const typeMap = {
    role: '角色',
    user: '用户',
    org: '组织'
  }
  return typeMap[type] || type
}

const getTypeColor = (type) => {
  const colorMap = {
    role: 'warning',
    user: 'success',
    org: 'info'
  }
  return colorMap[type] || 'default'
}

const handleAssigneeChange = (assignees) => {
  console.log('分配对象变化:', assignees)
}

const removeAssignee = (index) => {
  permissionForm.value.assignees.splice(index, 1)
}

const savePermission = () => {
  if (!permissionForm.value.name) {
    ElMessage.warning('请输入权限名称')
    return
  }

  if (permissionForm.value.assignees.length === 0) {
    ElMessage.warning('请选择分配对象')
    return
  }

  // 模拟保存
  ElMessage.success('权限配置保存成功')
  console.log('保存权限配置:', permissionForm.value)
}

const resetForm = () => {
  permissionForm.value = {
    name: '',
    assignees: []
  }
}
</script>

<style scoped>
.assignment-preview {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
</style>
```

### 7. 工作流审批场景
```vue
<template>
  <div class="example-container">
    <h3>工作流审批 - 审批人选择</h3>

    <el-form :model="workflowForm" label-width="120px">
      <el-form-item label="流程名称">
        <el-input v-model="workflowForm.name" placeholder="请输入流程名称" />
      </el-form-item>

      <el-form-item label="审批节点">
        <div v-for="(node, index) in workflowForm.nodes" :key="index" class="approval-node">
          <div class="node-header">
            <span>节点 {{ index + 1 }}: {{ node.name }}</span>
            <el-button
              type="danger"
              size="small"
              @click="removeNode(index)"
              v-if="workflowForm.nodes.length > 1"
            >
              删除节点
            </el-button>
          </div>

          <div class="node-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-input
                  v-model="node.name"
                  placeholder="节点名称"
                  size="small"
                />
              </el-col>
              <el-col :span="8">
                <el-select
                  v-model="node.type"
                  placeholder="审批类型"
                  size="small"
                >
                  <el-option label="单人审批" value="single" />
                  <el-option label="多人会签" value="multiple" />
                  <el-option label="多人或签" value="any" />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-button
                  type="primary"
                  size="small"
                  @click="openApproverSelector(index)"
                >
                  选择审批人
                </el-button>
              </el-col>
            </el-row>

            <div class="approvers-list" v-if="node.approvers.length">
              <h5>已选审批人：</h5>
              <el-tag
                v-for="approver in node.approvers"
                :key="approver.id"
                :type="getTypeColor(approver.type)"
                closable
                @close="removeApprover(index, approver.id)"
                class="approver-tag"
              >
                {{ approver.name }} ({{ getTypeName(approver.type) }})
              </el-tag>
            </div>
          </div>
        </div>

        <el-button type="dashed" @click="addNode" class="add-node-btn">
          + 添加审批节点
        </el-button>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="saveWorkflow">保存流程</el-button>
        <el-button @click="previewWorkflow">预览流程</el-button>
      </el-form-item>
    </el-form>

    <!-- 审批人选择弹窗 -->
    <el-dialog
      v-model="selectorVisible"
      title="选择审批人"
      width="800px"
      @close="closeSelectorDialog"
    >
      <FuniRUOC
        v-model="tempApprovers"
        type="user"
        :mode="{ role: 'multiple', user: 'multiple', org: 'multiple' }"
        tabPaneHeight="400px"
      />

      <template #footer>
        <el-button @click="closeSelectorDialog">取消</el-button>
        <el-button type="primary" @click="confirmApprovers">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const workflowForm = ref({
  name: '',
  nodes: [
    {
      name: '初审',
      type: 'single',
      approvers: []
    }
  ]
})

const selectorVisible = ref(false)
const currentNodeIndex = ref(-1)
const tempApprovers = ref([])

const getTypeName = (type) => {
  const typeMap = {
    role: '角色',
    user: '用户',
    org: '组织'
  }
  return typeMap[type] || type
}

const getTypeColor = (type) => {
  const colorMap = {
    role: 'warning',
    user: 'success',
    org: 'info'
  }
  return colorMap[type] || 'default'
}

const addNode = () => {
  workflowForm.value.nodes.push({
    name: `节点${workflowForm.value.nodes.length + 1}`,
    type: 'single',
    approvers: []
  })
}

const removeNode = (index) => {
  workflowForm.value.nodes.splice(index, 1)
}

const openApproverSelector = (index) => {
  currentNodeIndex.value = index
  tempApprovers.value = [...workflowForm.value.nodes[index].approvers]
  selectorVisible.value = true
}

const closeSelectorDialog = () => {
  selectorVisible.value = false
  currentNodeIndex.value = -1
  tempApprovers.value = []
}

const confirmApprovers = () => {
  if (currentNodeIndex.value >= 0) {
    workflowForm.value.nodes[currentNodeIndex.value].approvers = [...tempApprovers.value]
  }
  closeSelectorDialog()
}

const removeApprover = (nodeIndex, approverId) => {
  const node = workflowForm.value.nodes[nodeIndex]
  const index = node.approvers.findIndex(a => a.id === approverId)
  if (index >= 0) {
    node.approvers.splice(index, 1)
  }
}

const saveWorkflow = () => {
  if (!workflowForm.value.name) {
    ElMessage.warning('请输入流程名称')
    return
  }

  const hasEmptyNode = workflowForm.value.nodes.some(node =>
    !node.name || node.approvers.length === 0
  )

  if (hasEmptyNode) {
    ElMessage.warning('请完善所有审批节点信息')
    return
  }

  ElMessage.success('工作流保存成功')
  console.log('保存工作流:', workflowForm.value)
}

const previewWorkflow = () => {
  console.log('预览工作流:', workflowForm.value)
}
</script>

<style scoped>
.approval-node {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: bold;
}

.node-content {
  margin-top: 10px;
}

.approvers-list {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.approver-tag {
  margin: 2px;
}

.add-node-btn {
  width: 100%;
  margin-top: 10px;
}
</style>
```

## 注意事项

### 使用建议
1. **合理配置选择模式**：根据业务需求选择单选或多选模式
2. **优化用户体验**：设置合适的组件高度和默认选项卡
3. **处理数据格式**：确保API返回的数据格式符合组件要求
4. **权限控制**：在需要的场景下实现权限过滤逻辑

### 性能优化
1. **大数据量处理**：使用分页加载和搜索功能
2. **避免频繁请求**：合理设置搜索防抖时间
3. **内存管理**：及时清理不需要的数据引用

### 错误处理
1. **API异常**：处理网络请求失败的情况
2. **数据验证**：验证选择结果的有效性
3. **用户反馈**：提供清晰的错误提示信息
