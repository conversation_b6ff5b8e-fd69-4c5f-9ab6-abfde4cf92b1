# FuniRUOC ElementPlus API 支持

## 基础组件说明

FuniRUOC 是一个复合组件，内部使用了多个 ElementPlus 组件来构建角色用户组织选择器。主要包含以下 ElementPlus 组件：

### 核心组件构成
- **el-tabs**: 选项卡容器，用于切换不同类型的选择器
- **el-tab-pane**: 选项卡面板，承载具体的选择内容
- **el-tree**: 组织树形结构显示
- **el-checkbox-group**: 多选模式下的选择组
- **el-checkbox**: 单个选择项
- **el-input**: 搜索输入框
- **el-icon**: 图标显示

## 支持的 ElementPlus API

### el-tabs 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| type | ✅ | 直接透传 | 选项卡类型 |
| closable | ✅ | 直接透传 | 是否可关闭 |
| addable | ✅ | 直接透传 | 是否可增加 |
| editable | ✅ | 直接透传 | 是否可编辑 |
| tab-position | ✅ | 直接透传 | 选项卡位置 |
| stretch | ✅ | 直接透传 | 标签是否拉伸 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| tab-click | ✅ | 直接透传 | 选项卡点击事件 |
| tab-change | ✅ | 直接透传 | 选项卡切换事件 |
| tab-remove | ✅ | 直接透传 | 选项卡移除事件 |
| tab-add | ✅ | 直接透传 | 选项卡添加事件 |

### el-tree 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| data | ✅ | 内部处理 | 树形数据 |
| props | ✅ | 配置透传 | 树节点配置 |
| node-key | ✅ | 直接透传 | 节点唯一标识 |
| expand-on-click-node | ✅ | 直接透传 | 点击节点展开 |
| check-on-click-node | ✅ | 直接透传 | 点击节点选中 |
| highlight-current | ✅ | 直接透传 | 高亮当前节点 |
| default-expand-all | ✅ | 直接透传 | 默认展开所有节点 |
| filter-node-method | ✅ | 直接透传 | 节点过滤方法 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| node-click | ✅ | 直接透传 | 节点点击事件 |
| node-contextmenu | ✅ | 直接透传 | 节点右键事件 |
| check-change | ✅ | 直接透传 | 节点选中状态变化 |
| check | ✅ | 直接透传 | 节点选中事件 |
| current-change | ✅ | 直接透传 | 当前节点变化 |
| node-expand | ✅ | 直接透传 | 节点展开事件 |
| node-collapse | ✅ | 直接透传 | 节点收起事件 |

### el-checkbox-group 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| model-value | ✅ | 内部处理 | 绑定值 |
| size | ✅ | 直接透传 | 尺寸 |
| disabled | ✅ | 直接透传 | 是否禁用 |
| min | ✅ | 直接透传 | 最小选中数量 |
| max | ✅ | 直接透传 | 最大选中数量 |
| text-color | ✅ | 直接透传 | 文字颜色 |
| fill | ✅ | 直接透传 | 填充颜色 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| change | ✅ | 内部处理 | 值变化事件 |

### el-checkbox 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| model-value | ✅ | 内部处理 | 绑定值 |
| label | ✅ | 内部处理 | 选项标签 |
| true-label | ✅ | 直接透传 | 选中时的值 |
| false-label | ✅ | 直接透传 | 未选中时的值 |
| disabled | ✅ | 直接透传 | 是否禁用 |
| border | ✅ | 直接透传 | 是否显示边框 |
| size | ✅ | 直接透传 | 尺寸 |
| name | ✅ | 直接透传 | 原生name属性 |
| checked | ✅ | 直接透传 | 是否选中 |
| indeterminate | ✅ | 直接透传 | 半选状态 |

### el-input 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| model-value | ✅ | 内部处理 | 绑定值 |
| placeholder | ✅ | 直接透传 | 占位符 |
| clearable | ✅ | 直接透传 | 是否可清空 |
| disabled | ✅ | 直接透传 | 是否禁用 |
| size | ✅ | 直接透传 | 尺寸 |
| prefix-icon | ✅ | 直接透传 | 前缀图标 |
| suffix-icon | ✅ | 直接透传 | 后缀图标 |
| validate-event | ✅ | 直接透传 | 是否触发表单验证 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| input | ✅ | 内部处理 | 输入事件 |
| change | ✅ | 内部处理 | 值变化事件 |
| focus | ✅ | 直接透传 | 获得焦点事件 |
| blur | ✅ | 直接透传 | 失去焦点事件 |
| clear | ✅ | 直接透传 | 清空事件 |

## 透传方式说明

### 1. 直接透传
组件属性直接传递给对应的 ElementPlus 组件，无需额外处理。

```vue
<template>
  <FuniRUOC
    v-model="selected"
    type="user"
    :tab-position="'left'"
    :stretch="true"
  />
</template>
```

### 2. 配置透传
通过配置对象传递给子组件的特定属性。

```vue
<template>
  <FuniRUOC
    v-model="selected"
    type="user"
    :userProps="{
      orgDefaultProps: {
        id: 'orgId',
        name: 'orgName',
        children: 'subOrgs'
      }
    }"
  />
</template>
```

### 3. 内部处理
组件内部处理后再传递给 ElementPlus 组件。

```vue
<template>
  <FuniRUOC
    v-model="selected"
    type="user"
    @update:modelValue="handleChange"
  />
</template>
```

## 使用示例

### 基础透传示例
```vue
<template>
  <div class="example-container">
    <h3>ElementPlus API 透传示例</h3>
    
    <!-- 选项卡配置透传 -->
    <FuniRUOC
      v-model="selectedItems"
      type="user"
      tab-position="left"
      :stretch="true"
      @tab-click="handleTabClick"
      @tab-change="handleTabChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedItems = ref([])

const handleTabClick = (tab, event) => {
  console.log('选项卡点击:', tab.props.name)
}

const handleTabChange = (name) => {
  console.log('选项卡切换:', name)
}
</script>
```

### 树形组件配置透传
```vue
<template>
  <div class="example-container">
    <h3>树形组件配置透传</h3>
    
    <FuniRUOC
      v-model="selectedItems"
      type="org"
      :orgProps="{
        request: {
          api: '/api/org/tree',
          method: 'get'
        },
        defaultProps: {
          id: 'id',
          name: 'name',
          children: 'children'
        }
      }"
      :default-expand-all="true"
      :highlight-current="true"
      @node-click="handleNodeClick"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedItems = ref([])

const handleNodeClick = (data, node, component) => {
  console.log('节点点击:', data)
}
</script>
```

### 复合配置透传
```vue
<template>
  <div class="example-container">
    <h3>复合配置透传</h3>
    
    <FuniRUOC
      v-model="selectedItems"
      type="user"
      tab-position="top"
      :userProps="{
        mode: 'multiple',
        orgDefaultProps: {
          id: 'id',
          name: 'name',
          children: 'children'
        },
        userDefaultProps: {
          id: 'userId',
          name: 'userName'
        }
      }"
      :roleProps="{
        mode: 'single',
        defaultProps: {
          id: 'roleId',
          name: 'roleName'
        }
      }"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedItems = ref([])
</script>
```

## 注意事项

### 1. API 兼容性
- 确保使用的 ElementPlus 版本支持相应的 API
- 某些新增的 API 可能在旧版本中不可用
- 建议使用 ElementPlus 2.0+ 版本

### 2. 事件处理
- 部分事件会被组件内部处理，可能不会直接透传
- 建议使用组件提供的自定义事件而非直接监听 ElementPlus 事件
- 复杂的事件处理逻辑建议在组件外部实现

### 3. 样式定制
- 可以通过 CSS 变量或类名覆盖默认样式
- 注意样式优先级，避免样式冲突
- 建议使用组件提供的样式配置选项

### 4. 性能考虑
- 大数据量时注意树形组件的性能
- 合理使用虚拟滚动和懒加载
- 避免不必要的属性透传和事件监听

## 版本兼容性

| FuniRUOC 版本 | ElementPlus 版本 | 兼容性 |
|--------------|-----------------|--------|
| 1.0.x | 2.0.x | ✅ 完全兼容 |
| 1.0.x | 2.1.x | ✅ 完全兼容 |
| 1.0.x | 2.2.x | ✅ 完全兼容 |
| 1.0.x | 1.x.x | ⚠️ 部分兼容 |
