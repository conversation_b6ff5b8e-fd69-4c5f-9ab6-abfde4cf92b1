# FuniFormV2 ElementPlus API支持

## 概述

FuniFormV2基于ElementPlus的`el-form`组件封装，通过`v-bind="$attrs"`实现了对ElementPlus表单API的完全透传支持。这意味着您可以直接使用所有ElementPlus表单组件的原生属性、事件和方法。

## 支持的ElementPlus API

### el-form 属性透传

FuniFormV2支持所有`el-form`的原生属性：

```vue
<template>
  <FuniFormV2
    v-model="formData"
    :schema="schema"
    
    <!-- ElementPlus el-form 原生属性 -->
    :rules="rules"
    label-width="120px"
    label-position="right"
    label-suffix=":"
    :inline="false"
    :hide-required-asterisk="false"
    :show-message="true"
    :inline-message="false"
    :status-icon="false"
    :validate-on-rule-change="true"
    size="default"
    :disabled="false"
    :scroll-to-error="false"
    :scroll-into-view-options="scrollOptions"
    require-asterisk-position="left"
    
    <!-- ElementPlus el-form 原生事件 -->
    @validate="handleValidate"
  />
</template>
```

### 属性详细说明

#### 基础属性
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| rules | Object | — | 表单验证规则 |
| inline | Boolean | false | 行内表单模式 |
| label-position | String | right | 表单域标签的位置，可选值：right/left/top |
| label-width | String/Number | — | 表单域标签的宽度 |
| label-suffix | String | — | 表单域标签的后缀 |
| hide-required-asterisk | Boolean | false | 是否隐藏必填字段的标签旁的红色星号 |
| require-asterisk-position | String | left | 星号的位置，可选值：left/right |
| show-message | Boolean | true | 是否显示校验错误信息 |
| inline-message | Boolean | false | 是否以行内形式展示校验信息 |
| status-icon | Boolean | false | 是否在输入框中显示校验结果反馈图标 |
| validate-on-rule-change | Boolean | true | 是否在 rules 属性改变后立即触发一次验证 |
| size | String | — | 用于控制该表单内组件的尺寸，可选值：large/default/small |
| disabled | Boolean | false | 是否禁用该表单内的所有组件 |
| scroll-to-error | Boolean | false | 当校验失败时，是否滚动到第一个错误表单项 |
| scroll-into-view-options | Object/Boolean | — | 当校验失败时，滚动到第一个错误表单项的配置项 |

### 事件透传

FuniFormV2支持所有`el-form`的原生事件：

```vue
<template>
  <FuniFormV2
    v-model="formData"
    :schema="schema"
    @validate="handleValidate"
  />
</template>

<script setup>
// 验证事件处理
const handleValidate = (prop, isValid, message) => {
  console.log('字段验证:', {
    prop,      // 验证的字段名
    isValid,   // 是否验证通过
    message    // 验证消息
  })
}
</script>
```

### 方法透传

通过`@get-form`事件获取的表单实例包含所有ElementPlus表单方法：

```vue
<template>
  <FuniFormV2
    v-model="formData"
    :schema="schema"
    @get-form="handleGetForm"
  />
</template>

<script setup>
let formRef = null

const handleGetForm = (form) => {
  formRef = form
  // form实例包含以下方法：
  // - validate()
  // - validateField()
  // - resetFields()
  // - scrollToField()
  // - clearValidate()
}

// 使用ElementPlus原生方法
const validateForm = async () => {
  try {
    // 使用ElementPlus原生validate方法
    const valid = await formRef.elFormRef.validate()
    console.log('验证结果:', valid)
  } catch (error) {
    console.log('验证失败:', error)
  }
}

const validateSpecificField = async () => {
  try {
    // 验证特定字段
    await formRef.elFormRef.validateField(['name', 'email'])
    console.log('字段验证通过')
  } catch (error) {
    console.log('字段验证失败:', error)
  }
}

const resetForm = () => {
  // 重置表单
  formRef.elFormRef.resetFields()
}

const clearValidation = () => {
  // 清除验证信息
  formRef.elFormRef.clearValidate()
}

const scrollToField = (prop) => {
  // 滚动到指定字段
  formRef.elFormRef.scrollToField(prop)
}
</script>
```

## 表单项组件API透传

### schema中的组件属性透传

在schema配置中，`props`对象会直接透传给对应的ElementPlus组件：

```javascript
const schema = [
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: {
      // 所有el-input的原生属性都支持
      placeholder: '请输入姓名',
      clearable: true,
      'show-password': false,
      'show-word-limit': true,
      maxlength: 50,
      minlength: 2,
      size: 'default',
      disabled: false,
      readonly: false,
      type: 'text',
      resize: 'none',
      autosize: false,
      autocomplete: 'off',
      name: 'name',
      form: '',
      label: '姓名',
      tabindex: 0,
      'validate-event': true,
      'input-style': {},
      // ... 更多el-input属性
    },
    // 事件也可以透传
    on: {
      blur: (event) => console.log('失去焦点', event),
      focus: (event) => console.log('获得焦点', event),
      change: (value) => console.log('值改变', value),
      input: (value) => console.log('输入', value),
      clear: () => console.log('清空'),
    }
  }
]
```

### 常用ElementPlus组件属性示例

#### el-input 属性透传
```javascript
{
  prop: 'description',
  label: '描述',
  component: 'el-input',
  props: {
    type: 'textarea',
    rows: 4,
    placeholder: '请输入描述',
    maxlength: 500,
    'show-word-limit': true,
    resize: 'vertical',
    autosize: { minRows: 2, maxRows: 6 }
  }
}
```

#### el-select 属性透传
```javascript
{
  prop: 'category',
  label: '分类',
  component: 'el-select',
  props: {
    multiple: true,
    'collapse-tags': true,
    'collapse-tags-tooltip': true,
    filterable: true,
    'allow-create': false,
    'default-first-option': false,
    'reserve-keyword': true,
    'no-data-text': '无数据',
    'no-match-text': '无匹配数据',
    'loading-text': '加载中',
    'max-collapse-tags': 3,
    'tag-type': 'info',
    options: [
      { label: '选项1', value: 'option1' },
      { label: '选项2', value: 'option2' }
    ]
  }
}
```

#### el-date-picker 属性透传
```javascript
{
  prop: 'dateRange',
  label: '日期范围',
  component: 'el-date-picker',
  props: {
    type: 'daterange',
    'range-separator': '至',
    'start-placeholder': '开始日期',
    'end-placeholder': '结束日期',
    format: 'YYYY-MM-DD',
    'value-format': 'YYYY-MM-DD',
    'default-time': ['00:00:00', '23:59:59'],
    'unlink-panels': true,
    'shortcuts': [
      {
        text: '最近一周',
        value: () => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          return [start, end]
        }
      }
    ]
  }
}
```

## 验证规则透传

FuniFormV2完全支持ElementPlus的验证规则系统：

```javascript
const schema = [
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    // 支持所有ElementPlus验证规则
    rules: [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] },
      { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          // 自定义验证器
          if (value && !value.includes('@')) {
            callback(new Error('邮箱格式不正确'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }
]
```

## 注意事项

### 1. 属性优先级
- FuniFormV2的自定义属性优先级高于透传的ElementPlus属性
- 当存在冲突时，FuniFormV2的属性会覆盖ElementPlus的同名属性

### 2. 事件处理
- 可以同时监听FuniFormV2的自定义事件和ElementPlus的原生事件
- 建议优先使用FuniFormV2提供的增强事件

### 3. 方法调用
- 通过`@get-form`获取的表单实例提供了增强的方法
- 如需使用ElementPlus原生方法，可通过`formRef.elFormRef`访问

### 4. 样式定制
- 可以通过ElementPlus的CSS变量定制表单样式
- FuniFormV2的自定义样式不会影响ElementPlus的原生样式

## 兼容性说明

FuniFormV2与ElementPlus版本兼容性：
- 支持ElementPlus 2.0+
- 建议使用ElementPlus 2.3.0及以上版本以获得最佳体验
- 所有ElementPlus表单相关组件的API都得到完整支持
