# FuniFormV2 使用示例

## 基础使用

### 简单增强表单
```vue
<template>
  <FuniFormV2
    v-model="formData"
    :schema="basicSchema"
    :col="2"
    border
    @get-form="handleGetForm"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = ref({
  name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  status: 1
})

const basicSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { 
      placeholder: '请输入姓名',
      clearable: true
    },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { 
      placeholder: '请输入邮箱',
      clearable: true
    },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ]
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { 
      placeholder: '请输入手机号',
      clearable: true
    },
    rules: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ]
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门'
    },
    rules: [{ required: true, message: '请选择部门', trigger: 'change' }]
  },
  {
    prop: 'position',
    label: '职位',
    component: 'el-input',
    props: { 
      placeholder: '请输入职位',
      clearable: true
    }
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-switch',
    props: {
      activeText: '启用',
      inactiveText: '禁用'
    }
  }
])

let formRef = null

const handleGetForm = (form) => {
  formRef = form
  console.log('获取到表单实例:', form)
}

// 表单验证
const validateForm = async () => {
  if (!formRef) return
  
  const result = await formRef.validate()
  if (result.isValid) {
    console.log('验证通过:', result.values)
  } else {
    console.log('验证失败:', result.error)
  }
}

// 重置表单
const resetForm = () => {
  if (!formRef) return
  formRef.resetFields()
}
</script>
```

### 网格布局表单
```vue
<template>
  <div class="grid-form-demo">
    <h3>3列网格布局</h3>
    <FuniFormV2
      v-model="gridFormData"
      :schema="gridSchema"
      :col="3"
      border
      expand
      @get-form="handleGetGridForm"
    />
    
    <div class="form-actions">
      <el-button @click="resetGridForm">重置</el-button>
      <el-button type="primary" @click="validateGridForm">验证</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const gridFormData = ref({
  name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  salary: '',
  startDate: '',
  endDate: '',
  address: '',
  remark: ''
})

const gridSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    rules: [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }]
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号' }
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      options: [
        { label: '技术部', value: 'tech' },
        { label: '产品部', value: 'product' },
        { label: '运营部', value: 'operation' }
      ],
      placeholder: '请选择部门'
    }
  },
  {
    prop: 'position',
    label: '职位',
    component: 'el-input',
    props: { placeholder: '请输入职位' }
  },
  {
    prop: 'salary',
    label: '薪资',
    component: 'FuniMoneyInput',
    props: { placeholder: '请输入薪资' }
  },
  {
    prop: 'startDate',
    label: '入职日期',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '请选择入职日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    prop: 'endDate',
    label: '离职日期',
    component: 'el-date-picker',
    props: {
      type: 'date',
      placeholder: '请选择离职日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    prop: 'address',
    label: '地址',
    component: 'el-input',
    props: {
      type: 'textarea',
      placeholder: '请输入地址',
      rows: 2
    }
  },
  {
    prop: 'remark',
    label: '备注',
    component: 'el-input',
    props: {
      type: 'textarea',
      placeholder: '请输入备注',
      rows: 3
    }
  }
])

let gridFormRef = null

const handleGetGridForm = (form) => {
  gridFormRef = form
}

const validateGridForm = async () => {
  if (!gridFormRef) return
  
  const result = await gridFormRef.validate()
  if (result.isValid) {
    ElMessage.success('验证通过')
    console.log('表单数据:', result.values)
  } else {
    ElMessage.error('验证失败')
  }
}

const resetGridForm = () => {
  if (!gridFormRef) return
  gridFormRef.resetFields()
  ElMessage.info('表单已重置')
}
</script>

<style scoped>
.grid-form-demo {
  padding: 20px;
}

.form-actions {
  margin-top: 20px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
}
</style>
```

## 高级功能

### 条件显示表单
```vue
<template>
  <div class="conditional-form-demo">
    <h3>条件显示表单</h3>
    <FuniFormV2
      v-model="conditionalFormData"
      :schema="conditionalSchema"
      :col="2"
      border
      @get-form="handleGetConditionalForm"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'

const conditionalFormData = ref({
  userType: '',
  name: '',
  email: '',
  phone: '',
  companyName: '',
  position: '',
  studentId: '',
  school: '',
  grade: ''
})

const conditionalSchema = computed(() => [
  {
    prop: 'userType',
    label: '用户类型',
    component: 'el-select',
    props: {
      placeholder: '请选择用户类型',
      options: [
        { label: '企业用户', value: 'company' },
        { label: '学生用户', value: 'student' },
        { label: '个人用户', value: 'personal' }
      ]
    },
    rules: [{ required: true, message: '请选择用户类型', trigger: 'change' }]
  },
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    rules: [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }]
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号' }
  },
  // 企业用户专用字段
  {
    prop: 'companyName',
    label: '公司名称',
    component: 'el-input',
    props: { placeholder: '请输入公司名称' },
    hidden: ({ formModel }) => formModel.userType !== 'company',
    rules: [{ required: true, message: '请输入公司名称', trigger: 'blur' }]
  },
  {
    prop: 'position',
    label: '职位',
    component: 'el-input',
    props: { placeholder: '请输入职位' },
    hidden: ({ formModel }) => formModel.userType !== 'company'
  },
  // 学生用户专用字段
  {
    prop: 'studentId',
    label: '学号',
    component: 'el-input',
    props: { placeholder: '请输入学号' },
    hidden: ({ formModel }) => formModel.userType !== 'student',
    rules: [{ required: true, message: '请输入学号', trigger: 'blur' }]
  },
  {
    prop: 'school',
    label: '学校',
    component: 'el-input',
    props: { placeholder: '请输入学校名称' },
    hidden: ({ formModel }) => formModel.userType !== 'student',
    rules: [{ required: true, message: '请输入学校名称', trigger: 'blur' }]
  },
  {
    prop: 'grade',
    label: '年级',
    component: 'el-select',
    props: {
      placeholder: '请选择年级',
      options: [
        { label: '大一', value: '1' },
        { label: '大二', value: '2' },
        { label: '大三', value: '3' },
        { label: '大四', value: '4' }
      ]
    },
    hidden: ({ formModel }) => formModel.userType !== 'student'
  }
])

let conditionalFormRef = null

const handleGetConditionalForm = (form) => {
  conditionalFormRef = form
}
</script>

<style scoped>
.conditional-form-demo {
  padding: 20px;
}
</style>
```

### 内联表单
```vue
<template>
  <div class="inline-form-demo">
    <h3>内联表单</h3>
    <FuniFormV2
      v-model="inlineFormData"
      :schema="inlineSchema"
      inline
      @get-form="handleGetInlineForm"
    />

    <div class="form-actions">
      <el-button @click="searchData">搜索</el-button>
      <el-button @click="resetInlineForm">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const inlineFormData = ref({
  keyword: '',
  status: '',
  dateRange: [],
  department: ''
})

const inlineSchema = reactive([
  {
    prop: 'keyword',
    label: '关键词',
    component: 'el-input',
    props: {
      placeholder: '请输入关键词',
      clearable: true,
      style: { width: '200px' }
    }
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-select',
    props: {
      placeholder: '请选择状态',
      clearable: true,
      style: { width: '150px' },
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    component: 'el-date-picker',
    props: {
      type: 'daterange',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '240px' }
    }
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门',
      clearable: true,
      style: { width: '150px' }
    }
  }
])

let inlineFormRef = null

const handleGetInlineForm = (form) => {
  inlineFormRef = form
}

const searchData = () => {
  console.log('搜索条件:', inlineFormData.value)
  ElMessage.success('搜索完成')
}

const resetInlineForm = () => {
  if (!inlineFormRef) return
  inlineFormRef.resetFields()
  ElMessage.info('搜索条件已重置')
}
</script>

<style scoped>
.inline-form-demo {
  padding: 20px;
}

.form-actions {
  margin-top: 15px;
}

.form-actions .el-button {
  margin-right: 10px;
}
</style>
```

## 业务场景示例

### 用户信息编辑表单
```vue
<template>
  <div class="user-edit-form">
    <h3>用户信息编辑</h3>
    <FuniFormV2
      v-model="userFormData"
      :schema="userSchema"
      :col="2"
      border
      @get-form="handleGetUserForm"
    />

    <div class="form-actions">
      <el-button @click="cancelEdit">取消</el-button>
      <el-button type="primary" @click="saveUser" :loading="saving">保存</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const saving = ref(false)

const userFormData = ref({
  id: '',
  name: '',
  email: '',
  phone: '',
  avatar: '',
  department: '',
  position: '',
  status: 1,
  roles: [],
  permissions: [],
  remark: ''
})

const userSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: {
      placeholder: '请输入姓名',
      clearable: true
    },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: {
      placeholder: '请输入邮箱',
      clearable: true
    },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ]
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: {
      placeholder: '请输入手机号',
      clearable: true
    },
    rules: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ]
  },
  {
    prop: 'avatar',
    label: '头像',
    component: 'FuniFileTable',
    props: {
      fileType: 'image',
      limit: 1,
      listType: 'picture-card'
    }
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门'
    },
    rules: [{ required: true, message: '请选择部门', trigger: 'change' }]
  },
  {
    prop: 'position',
    label: '职位',
    component: 'el-input',
    props: {
      placeholder: '请输入职位',
      clearable: true
    }
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-switch',
    props: {
      activeText: '启用',
      inactiveText: '禁用',
      activeValue: 1,
      inactiveValue: 0
    }
  },
  {
    prop: 'roles',
    label: '角色',
    component: 'el-select',
    props: {
      multiple: true,
      placeholder: '请选择角色',
      options: [
        { label: '管理员', value: 'admin' },
        { label: '普通用户', value: 'user' },
        { label: '访客', value: 'guest' }
      ]
    }
  },
  {
    prop: 'permissions',
    label: '权限',
    component: 'el-checkbox-group',
    props: {
      options: [
        { label: '用户管理', value: 'user:manage' },
        { label: '角色管理', value: 'role:manage' },
        { label: '系统设置', value: 'system:setting' }
      ]
    }
  },
  {
    prop: 'remark',
    label: '备注',
    component: 'el-input',
    props: {
      type: 'textarea',
      placeholder: '请输入备注信息',
      rows: 3
    }
  }
])

let userFormRef = null

const handleGetUserForm = (form) => {
  userFormRef = form
}

// 模拟加载用户数据
onMounted(() => {
  loadUserData()
})

const loadUserData = () => {
  // 模拟从API加载数据
  setTimeout(() => {
    userFormData.value = {
      id: '1',
      name: '张三',
      email: '<EMAIL>',
      phone: '13800138000',
      avatar: '',
      department: 'tech',
      position: '前端工程师',
      status: 1,
      roles: ['user'],
      permissions: ['user:manage'],
      remark: '这是一个测试用户'
    }
  }, 500)
}

const saveUser = async () => {
  if (!userFormRef) return

  const result = await userFormRef.validate()
  if (!result.isValid) {
    ElMessage.error('请检查表单数据')
    return
  }

  saving.value = true
  try {
    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('保存成功')
    console.log('保存的用户数据:', result.values)
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const cancelEdit = () => {
  ElMessage.info('已取消编辑')
  // 可以在这里处理取消逻辑，比如返回列表页
}
</script>

<style scoped>
.user-edit-form {
  padding: 20px;
  max-width: 800px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 100px;
}
</style>
```
