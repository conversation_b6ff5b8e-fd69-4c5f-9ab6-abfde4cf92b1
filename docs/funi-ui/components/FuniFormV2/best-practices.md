# FuniFormV2 最佳实践

## 推荐用法

### 1. 标准表单配置
```vue
<template>
  <FuniFormV2
    v-model="formData"
    :schema="schema"
    :col="2"
    border
    @get-form="handleGetForm"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

// 推荐：使用响应式数据管理表单
const formData = ref({
  name: '',
  email: '',
  phone: '',
  department: '',
  status: 1
})

// 推荐：将schema定义为响应式数据
const schema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { 
      placeholder: '请输入姓名',
      clearable: true
    },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { 
      placeholder: '请输入邮箱',
      clearable: true
    },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ]
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { 
      placeholder: '请输入手机号',
      clearable: true
    },
    rules: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ]
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门'
    },
    rules: [{ required: true, message: '请选择部门', trigger: 'change' }]
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-switch',
    props: {
      activeText: '启用',
      inactiveText: '禁用'
    }
  }
])

let formRef = null

const handleGetForm = (form) => {
  formRef = form
}

// 推荐：统一的表单验证处理
const validateForm = async () => {
  if (!formRef) return false
  
  const result = await formRef.validate()
  if (result.isValid) {
    console.log('验证通过:', result.values)
    return true
  } else {
    console.log('验证失败:', result.error)
    return false
  }
}

// 推荐：统一的表单重置处理
const resetForm = () => {
  if (!formRef) return
  formRef.resetFields()
}
</script>
```

### 2. 网格布局最佳实践
```vue
<template>
  <!-- 推荐：根据屏幕尺寸动态调整列数 -->
  <FuniFormV2
    v-model="formData"
    :schema="schema"
    :col="responsiveCol"
    border
    expand
    @get-form="handleGetForm"
  />
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const windowWidth = ref(window.innerWidth)

// 推荐：响应式列数配置
const responsiveCol = computed(() => {
  if (windowWidth.value < 768) return 1      // 移动端：1列
  if (windowWidth.value < 1200) return 2     // 平板：2列
  if (windowWidth.value < 1600) return 3     // 桌面：3列
  return 4                                   // 大屏：4列
})

const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
```

### 3. 条件显示最佳实践
```vue
<script setup>
import { ref, computed } from 'vue'

const formData = ref({
  userType: '',
  name: '',
  companyName: '',
  studentId: ''
})

// 推荐：使用computed定义动态schema
const schema = computed(() => [
  {
    prop: 'userType',
    label: '用户类型',
    component: 'el-select',
    props: {
      placeholder: '请选择用户类型',
      options: [
        { label: '企业用户', value: 'company' },
        { label: '学生用户', value: 'student' }
      ]
    },
    rules: [{ required: true, message: '请选择用户类型', trigger: 'change' }]
  },
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  // 推荐：使用函数形式的hidden属性
  {
    prop: 'companyName',
    label: '公司名称',
    component: 'el-input',
    props: { placeholder: '请输入公司名称' },
    hidden: ({ formModel }) => formModel.userType !== 'company',
    rules: [{ required: true, message: '请输入公司名称', trigger: 'blur' }]
  },
  {
    prop: 'studentId',
    label: '学号',
    component: 'el-input',
    props: { placeholder: '请输入学号' },
    hidden: ({ formModel }) => formModel.userType !== 'student',
    rules: [{ required: true, message: '请输入学号', trigger: 'blur' }]
  }
])
</script>
```

## 性能优化

### 1. 大表单优化
```vue
<script setup>
import { ref, reactive, shallowRef } from 'vue'

// 推荐：大表单使用shallowRef减少响应式开销
const formData = shallowRef({
  // 大量字段...
})

// 推荐：分组管理大表单
const basicSchema = reactive([
  // 基础信息字段
])

const detailSchema = reactive([
  // 详细信息字段
])

const schema = computed(() => [
  ...basicSchema,
  ...detailSchema
])

// 推荐：使用防抖处理频繁的数据变化
import { debounce } from 'lodash-es'

const debouncedValidate = debounce(async () => {
  if (formRef.value) {
    await formRef.value.validate()
  }
}, 300)
</script>
```

### 2. 内存管理
```vue
<script setup>
import { ref, onUnmounted } from 'vue'

let formRef = null
const timers = []

const handleGetForm = (form) => {
  formRef = form
}

// 推荐：清理定时器和事件监听器
onUnmounted(() => {
  timers.forEach(timer => clearTimeout(timer))
  formRef = null
})
</script>
```

## 数据管理

### 1. 表单数据初始化
```vue
<script setup>
import { ref, onMounted } from 'vue'

const formData = ref({})

// 推荐：明确定义初始数据结构
const initFormData = () => {
  formData.value = {
    name: '',
    email: '',
    phone: '',
    department: '',
    status: 1,
    roles: [],
    permissions: [],
    remark: ''
  }
}

// 推荐：组件挂载时初始化数据
onMounted(() => {
  initFormData()
})

// 推荐：提供重置到初始状态的方法
const resetToInitial = () => {
  initFormData()
  if (formRef) {
    formRef.clearValidate()
  }
}
</script>
```

### 2. 数据验证策略
```vue
<script setup>
// 推荐：定义验证规则常量
const VALIDATION_RULES = {
  required: { required: true, trigger: 'blur' },
  email: { type: 'email', trigger: ['blur', 'change'] },
  phone: { pattern: /^1[3-9]\d{9}$/, trigger: 'blur' },
  minLength: (min) => ({ min, trigger: 'blur' }),
  maxLength: (max) => ({ max, trigger: 'blur' })
}

// 推荐：使用工厂函数创建验证规则
const createValidationRules = (field, options = {}) => {
  const rules = []
  
  if (options.required) {
    rules.push({
      ...VALIDATION_RULES.required,
      message: options.requiredMessage || `请输入${field}`
    })
  }
  
  if (options.email) {
    rules.push({
      ...VALIDATION_RULES.email,
      message: options.emailMessage || '请输入正确的邮箱格式'
    })
  }
  
  if (options.phone) {
    rules.push({
      ...VALIDATION_RULES.phone,
      message: options.phoneMessage || '请输入正确的手机号'
    })
  }
  
  return rules
}

const schema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: createValidationRules('姓名', { required: true })
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    rules: createValidationRules('邮箱', { required: true, email: true })
  }
])
</script>
```

## 错误处理

### 1. 表单验证错误处理
```vue
<script setup>
import { ElMessage } from 'element-plus'

// 推荐：统一的错误处理函数
const handleValidationError = (error) => {
  if (error && error.length > 0) {
    const firstError = error[0]
    ElMessage.error(firstError.message || '表单验证失败')
    
    // 推荐：滚动到第一个错误字段
    if (formRef && firstError.field) {
      formRef.scrollToField(firstError.field)
    }
  }
}

const submitForm = async () => {
  if (!formRef) return
  
  try {
    const result = await formRef.validate()
    if (result.isValid) {
      // 处理提交逻辑
      await submitData(result.values)
      ElMessage.success('提交成功')
    } else {
      handleValidationError(result.error)
    }
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('提交失败，请重试')
  }
}
</script>
```

### 2. 异步操作错误处理
```vue
<script setup>
const loading = ref(false)

// 推荐：使用loading状态管理异步操作
const submitData = async (data) => {
  loading.value = true
  try {
    const response = await api.submitForm(data)
    return response
  } catch (error) {
    console.error('API调用失败:', error)
    throw error
  } finally {
    loading.value = false
  }
}
</script>
```

## 可访问性

### 1. 无障碍支持
```vue
<template>
  <FuniFormV2
    v-model="formData"
    :schema="accessibleSchema"
    :col="2"
    border
    @get-form="handleGetForm"
  />
</template>

<script setup>
// 推荐：为表单字段添加适当的aria属性
const accessibleSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { 
      placeholder: '请输入姓名',
      'aria-label': '姓名输入框',
      'aria-describedby': 'name-help'
    },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  }
])
</script>
```

## 测试建议

### 1. 单元测试
```javascript
// 推荐：测试表单验证逻辑
describe('FuniFormV2 Validation', () => {
  test('should validate required fields', async () => {
    const wrapper = mount(FuniFormV2, {
      props: {
        modelValue: { name: '' },
        schema: [
          {
            prop: 'name',
            label: '姓名',
            component: 'el-input',
            rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
          }
        ]
      }
    })
    
    const form = wrapper.vm
    const result = await form.validate()
    
    expect(result.isValid).toBe(false)
    expect(result.error).toContain('请输入姓名')
  })
})
```

### 2. 集成测试
```javascript
// 推荐：测试表单提交流程
describe('Form Submission', () => {
  test('should submit form with valid data', async () => {
    const mockSubmit = jest.fn()
    const wrapper = mount(FormComponent, {
      props: { onSubmit: mockSubmit }
    })
    
    // 填写表单
    await wrapper.find('input[name="name"]').setValue('张三')
    await wrapper.find('input[name="email"]').setValue('<EMAIL>')
    
    // 提交表单
    await wrapper.find('button[type="submit"]').trigger('click')
    
    expect(mockSubmit).toHaveBeenCalledWith({
      name: '张三',
      email: '<EMAIL>'
    })
  })
})
```

## 常见问题

### 1. 表单数据不更新
```vue
<script setup>
// ❌ 错误：直接修改props
const props = defineProps(['modelValue'])
props.modelValue.name = 'new value' // 不要这样做

// ✅ 正确：使用emit更新数据
const emit = defineEmits(['update:modelValue'])
const updateFormData = (newData) => {
  emit('update:modelValue', { ...props.modelValue, ...newData })
}
</script>
```

### 2. 验证规则不生效
```javascript
// ❌ 错误：验证规则定义不正确
const schema = [
  {
    prop: 'name',
    rules: { required: true } // 缺少message和trigger
  }
]

// ✅ 正确：完整的验证规则定义
const schema = [
  {
    prop: 'name',
    rules: [
      { required: true, message: '请输入姓名', trigger: 'blur' }
    ]
  }
]
```

### 3. 性能问题
```vue
<script setup>
// ❌ 错误：在模板中使用复杂计算
// <FuniFormV2 :schema="expensiveComputation()" />

// ✅ 正确：使用computed缓存计算结果
const schema = computed(() => expensiveComputation())
</script>
```
