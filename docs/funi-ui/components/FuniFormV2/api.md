# FuniFormV2 API文档

## 组件概述

FuniFormV2是FuniForm的增强版本，基于ElementPlus的el-form封装，提供了更强大的表单功能，包括动态表单、条件显示、数据联动、表单验证、布局控制等高级特性，适用于复杂的表单场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | 与FuniForm差异 |
|--------|------|--------|------|------|----------------|
| modelValue | Object | {} | ✅ | 表单数据对象 | 同FuniForm |
| schema | Array | [] | ✅ | 表单项配置数组 | 增强配置结构 |
| labelWidth | String | '100px' | - | 标签宽度 | 同FuniForm |
| labelPosition | String | 'right' | - | 标签位置 | 同FuniForm |
| size | String | 'default' | - | 组件尺寸 | 同FuniForm |
| disabled | Boolean | false | - | 是否禁用 | 同FuniForm |
| readonly | Boolean | false | - | 是否只读 | 同FuniForm |
| inline | Boolean | false | - | 是否行内表单 | 同FuniForm |
| rules | Object | {} | - | 表单验证规则 | 同FuniForm |
| validateOnRuleChange | Boolean | true | - | 是否在rules改变后立即触发一次验证 | 同FuniForm |
| hideRequiredAsterisk | Boolean | false | - | 是否隐藏必填字段的标签旁的红色星号 | 同FuniForm |
| showMessage | Boolean | true | - | 是否显示校验错误信息 | 同FuniForm |
| inlineMessage | Boolean | false | - | 是否以行内形式展示校验信息 | 同FuniForm |
| statusIcon | Boolean | false | - | 是否在输入框中显示校验结果反馈图标 | 同FuniForm |
| scrollToError | Boolean | false | - | 当校验失败时，滚动到第一个错误表单项 | 同FuniForm |
| scrollIntoViewOptions | Object/Boolean | - | - | 滚动到错误字段时的行为 | 同FuniForm |
| gutter | Number | 20 | - | 栅格间隔 | 🆕 新增 |
| responsive | Boolean | true | - | 是否响应式布局 | 🆕 新增 |
| dynamicForm | Boolean | false | - | 是否启用动态表单 | 🆕 新增 |
| conditionalDisplay | Boolean | false | - | 是否启用条件显示 | 🆕 新增 |
| dataLinkage | Boolean | false | - | 是否启用数据联动 | 🆕 新增 |
| autoSave | Boolean | false | - | 是否自动保存 | 🆕 新增 |
| autoSaveDelay | Number | 1000 | - | 自动保存延迟时间（毫秒） | 🆕 新增 |
| formMode | String | 'create' | - | 表单模式：'create'、'edit'、'view' | 🆕 新增 |
| stepForm | Boolean | false | - | 是否分步表单 | 🆕 新增 |
| stepConfig | Object | {} | - | 分步表单配置 | 🆕 新增 |
| groupForm | Boolean | false | - | 是否分组表单 | 🆕 新增 |
| groupConfig | Object | {} | - | 分组表单配置 | 🆕 新增 |
| submitOnEnter | Boolean | false | - | 是否在按下回车键时提交表单 | 🆕 新增 |
| resetOnSubmit | Boolean | false | - | 提交成功后是否重置表单 | 🆕 新增 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: Object | 表单数据更新事件 | 表单数据变化时 |
| validate | (prop: string, isValid: boolean, message: string) | 表单项验证事件 | 表单项验证时 |
| submit | formData: Object | 表单提交事件 | 表单提交时 |
| reset | - | 表单重置事件 | 表单重置时 |
| change | (value: any, prop: string, formData: Object) | 表单项变化事件 | 表单项值变化时 |
| focus | (prop: string, event: Event) | 表单项获得焦点事件 | 表单项获得焦点时 |
| blur | (prop: string, event: Event) | 表单项失去焦点事件 | 表单项失去焦点时 |
| step-change | (currentStep: number, direction: string) | 🆕 步骤变化事件 | 分步表单步骤变化时 |
| group-change | (activeGroup: string) | 🆕 分组变化事件 | 分组表单激活分组变化时 |
| auto-save | formData: Object | 🆕 自动保存事件 | 自动保存触发时 |
| dynamic-change | (action: string, item: any, index: number) | 🆕 动态表单变化事件 | 动态表单项变化时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| validate | (callback?: Function) | Promise\<boolean\> | 验证整个表单 |
| validateField | (props: string \| string[], callback?: Function) | Promise\<boolean\> | 验证指定字段 |
| resetFields | (props?: string \| string[]) | void | 重置表单字段 |
| scrollToField | (prop: string) | void | 滚动到指定字段 |
| clearValidate | (props?: string \| string[]) | void | 清除验证信息 |
| submit | - | Promise | 提交表单 |
| getFormData | - | Object | 获取表单数据 |
| setFormData | (data: Object) | void | 设置表单数据 |
| getFieldValue | (prop: string) | any | 获取指定字段值 |
| setFieldValue | (prop: string, value: any) | void | 设置指定字段值 |
| nextStep | - | void | 🆕 下一步（分步表单） |
| prevStep | - | void | 🆕 上一步（分步表单） |
| goToStep | (step: number) | void | 🆕 跳转到指定步骤 |
| addFormItem | (item: FormItemConfig, index?: number) | void | 🆕 添加表单项（动态表单） |
| removeFormItem | (index: number) | void | 🆕 移除表单项（动态表单） |
| updateFormItem | (index: number, item: FormItemConfig) | void | 🆕 更新表单项（动态表单） |
| toggleGroup | (groupKey: string) | void | 🆕 切换分组显示状态 |
| saveForm | - | Promise | 🆕 保存表单（自动保存） |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 自定义表单内容 |
| header | - | 表单头部内容 |
| footer | - | 表单底部内容 |
| [prop] | { value, onChange, formData } | 自定义表单项内容 |
| [prop]-label | { label, required } | 自定义表单项标签 |
| step-header | { currentStep, steps } | 🆕 分步表单头部 |
| step-footer | { currentStep, steps, nextStep, prevStep } | 🆕 分步表单底部 |
| group-header | { group, isActive } | 🆕 分组表单头部 |
| group-footer | { group, isActive } | 🆕 分组表单底部 |

## 增强功能配置

### 动态表单配置
```typescript
interface DynamicFormConfig {
  enabled: boolean;                // 是否启用动态表单
  addable: boolean;                // 是否可添加表单项
  removable: boolean;              // 是否可移除表单项
  sortable: boolean;               // 是否可排序表单项
  maxItems?: number;               // 最大表单项数量
  minItems?: number;               // 最小表单项数量
  addText?: string;                // 添加按钮文本
  removeText?: string;             // 移除按钮文本
  template?: FormItemConfig;       // 新增表单项模板
}
```

### 条件显示配置
```typescript
interface ConditionalDisplayConfig {
  enabled: boolean;                // 是否启用条件显示
  rules: ConditionalRule[];        // 条件规则
}

interface ConditionalRule {
  target: string;                  // 目标字段
  condition: ConditionFunction;    // 条件函数
  action: 'show' | 'hide' | 'enable' | 'disable' | 'require'; // 动作
}

type ConditionFunction = (formData: Object) => boolean;
```

### 数据联动配置
```typescript
interface DataLinkageConfig {
  enabled: boolean;                // 是否启用数据联动
  rules: LinkageRule[];            // 联动规则
}

interface LinkageRule {
  source: string;                  // 源字段
  target: string | string[];       // 目标字段
  handler: LinkageHandler;         // 联动处理函数
  immediate?: boolean;             // 是否立即执行
}

type LinkageHandler = (sourceValue: any, formData: Object) => any | Promise<any>;
```

### 分步表单配置
```typescript
interface StepFormConfig {
  enabled: boolean;                // 是否启用分步表单
  steps: StepConfig[];             // 步骤配置
  showStepNumber?: boolean;        // 是否显示步骤编号
  allowSkip?: boolean;             // 是否允许跳过步骤
  validateOnNext?: boolean;        // 下一步时是否验证
}

interface StepConfig {
  key: string;                     // 步骤键
  title: string;                   // 步骤标题
  description?: string;            // 步骤描述
  icon?: string;                   // 步骤图标
  fields: string[];                // 包含的字段
  optional?: boolean;              // 是否可选步骤
  validator?: Function;            // 自定义验证函数
}
```

### 分组表单配置
```typescript
interface GroupFormConfig {
  enabled: boolean;                // 是否启用分组表单
  groups: GroupConfig[];           // 分组配置
  collapsible?: boolean;           // 是否可折叠
  accordion?: boolean;             // 是否手风琴模式
}

interface GroupConfig {
  key: string;                     // 分组键
  title: string;                   // 分组标题
  description?: string;            // 分组描述
  icon?: string;                   // 分组图标
  fields: string[];                // 包含的字段
  collapsed?: boolean;             // 是否默认折叠
  required?: boolean;              // 是否必填分组
}
```

## 表单项配置结构V2

```typescript
interface FormItemConfigV2 extends FormItemConfig {
  // V2增强功能
  dependencies?: string[];         // 依赖字段
  watch?: WatchConfig;             // 监听配置
  conditional?: ConditionalConfig; // 条件配置
  linkage?: LinkageConfig;         // 联动配置
  
  // 布局增强
  responsive?: ResponsiveConfig;   // 响应式配置
  group?: string;                  // 所属分组
  step?: number;                   // 所属步骤
  
  // 显示增强
  tooltip?: string | TooltipConfig; // 提示信息
  helpText?: string;               // 帮助文本
  prefix?: string;                 // 前缀文本
  suffix?: string;                 // 后缀文本
  
  // 验证增强
  asyncValidator?: AsyncValidator; // 异步验证器
  validateTrigger?: string[];      // 验证触发方式
  
  // 数据处理
  transformer?: DataTransformer;   // 数据转换器
  formatter?: DataFormatter;       // 数据格式化器
  
  // 权限控制
  permission?: string | string[];  // 权限标识
  
  // 其他增强
  placeholder?: string | PlaceholderFunction; // 动态占位符
  options?: OptionItem[] | OptionsFunction;   // 动态选项
  [key: string]: any;              // 扩展配置
}

interface WatchConfig {
  handler: WatchHandler;           // 监听处理函数
  immediate?: boolean;             // 是否立即执行
  deep?: boolean;                  // 是否深度监听
}

type WatchHandler = (newVal: any, oldVal: any, formData: Object) => void;

interface ConditionalConfig {
  show?: ConditionFunction;        // 显示条件
  enable?: ConditionFunction;      // 启用条件
  require?: ConditionFunction;     // 必填条件
}

interface LinkageConfig {
  target: string | string[];       // 联动目标
  handler: LinkageHandler;         // 联动处理函数
}

interface ResponsiveConfig {
  xs?: number | ResponsiveItem;    // 超小屏幕
  sm?: number | ResponsiveItem;    // 小屏幕
  md?: number | ResponsiveItem;    // 中等屏幕
  lg?: number | ResponsiveItem;    // 大屏幕
  xl?: number | ResponsiveItem;    // 超大屏幕
}

interface ResponsiveItem {
  span?: number;                   // 栅格占比
  offset?: number;                 // 栅格偏移
  push?: number;                   // 栅格向右移动
  pull?: number;                   // 栅格向左移动
}

interface TooltipConfig {
  content: string;                 // 提示内容
  placement?: string;              // 提示位置
  trigger?: string;                // 触发方式
}

type AsyncValidator = (rule: any, value: any, callback: Function) => Promise<void>;
type DataTransformer = (value: any, formData: Object) => any;
type DataFormatter = (value: any, formData: Object) => string;
type PlaceholderFunction = (formData: Object) => string;
type OptionsFunction = (formData: Object) => Promise<OptionItem[]>;
```

## 使用示例

### 基础增强表单
```vue
<template>
  <FuniFormV2
    v-model="formData"
    :schema="formSchema"
    :gutter="20"
    responsive
    @submit="handleSubmit"
    @change="handleChange"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = ref({
  name: '',
  email: '',
  phone: '',
  address: ''
})

const formSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号' },
    rules: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'address',
    label: '地址',
    component: 'el-input',
    props: { 
      type: 'textarea',
      placeholder: '请输入地址',
      rows: 3
    },
    span: 24
  }
])

const handleSubmit = (data) => {
  console.log('提交表单:', data)
}

const handleChange = (value, prop, formData) => {
  console.log('字段变化:', prop, value)
}
</script>
```

### 动态表单示例
```vue
<template>
  <FuniFormV2
    v-model="dynamicFormData"
    :schema="dynamicSchema"
    dynamic-form
    @dynamic-change="handleDynamicChange"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const dynamicFormData = ref({
  contacts: [
    { name: '', phone: '', email: '' }
  ]
})

const dynamicSchema = reactive([
  {
    prop: 'contacts',
    label: '联系人信息',
    component: 'dynamic-list',
    dynamic: {
      enabled: true,
      addable: true,
      removable: true,
      maxItems: 5,
      minItems: 1,
      template: {
        name: { 
          label: '姓名', 
          component: 'el-input',
          props: { placeholder: '请输入姓名' },
          rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
        },
        phone: { 
          label: '电话', 
          component: 'el-input',
          props: { placeholder: '请输入电话' }
        },
        email: { 
          label: '邮箱', 
          component: 'el-input',
          props: { placeholder: '请输入邮箱' }
        }
      }
    },
    span: 24
  }
])

const handleDynamicChange = (action, item, index) => {
  console.log('动态表单变化:', action, item, index)
}
</script>
```

### 分步表单示例
```vue
<template>
  <FuniFormV2
    v-model="stepFormData"
    :schema="stepSchema"
    step-form
    :step-config="stepConfig"
    @step-change="handleStepChange"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const stepFormData = ref({
  // 基本信息
  name: '',
  email: '',
  phone: '',
  // 详细信息
  address: '',
  company: '',
  position: '',
  // 其他信息
  remark: '',
  tags: []
})

const stepConfig = reactive({
  enabled: true,
  steps: [
    {
      key: 'basic',
      title: '基本信息',
      description: '填写基本个人信息',
      fields: ['name', 'email', 'phone']
    },
    {
      key: 'detail',
      title: '详细信息',
      description: '填写详细信息',
      fields: ['address', 'company', 'position']
    },
    {
      key: 'other',
      title: '其他信息',
      description: '填写其他补充信息',
      fields: ['remark', 'tags'],
      optional: true
    }
  ],
  showStepNumber: true,
  validateOnNext: true
})

const stepSchema = reactive([
  // 基本信息字段
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    step: 0,
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    step: 0,
    rules: [{ required: true, type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }]
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    step: 0,
    rules: [{ required: true, pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }]
  },
  // 详细信息字段
  {
    prop: 'address',
    label: '地址',
    component: 'el-input',
    step: 1,
    props: { type: 'textarea', rows: 3 }
  },
  {
    prop: 'company',
    label: '公司',
    component: 'el-input',
    step: 1
  },
  {
    prop: 'position',
    label: '职位',
    component: 'el-input',
    step: 1
  },
  // 其他信息字段
  {
    prop: 'remark',
    label: '备注',
    component: 'el-input',
    step: 2,
    props: { type: 'textarea', rows: 4 }
  },
  {
    prop: 'tags',
    label: '标签',
    component: 'el-select',
    step: 2,
    props: { multiple: true },
    options: [
      { label: 'VIP客户', value: 'vip' },
      { label: '重要客户', value: 'important' },
      { label: '普通客户', value: 'normal' }
    ]
  }
])

const handleStepChange = (currentStep, direction) => {
  console.log('步骤变化:', currentStep, direction)
}
</script>
```

## ElementPlus API支持

FuniFormV2基于el-form封装，支持所有el-form的API：

```vue
<template>
  <FuniFormV2
    v-model="formData"
    :schema="schema"
    
    <!-- ElementPlus el-form 所有属性 -->
    :rules="rules"
    :inline="false"
    :label-position="labelPosition"
    :label-width="labelWidth"
    :label-suffix="labelSuffix"
    :hide-required-asterisk="false"
    :show-message="true"
    :inline-message="false"
    :status-icon="false"
    :validate-on-rule-change="true"
    :size="size"
    :disabled="disabled"
    :scroll-to-error="false"
    :scroll-into-view-options="scrollOptions"
    
    <!-- ElementPlus el-form 所有事件 -->
    @validate="handleValidate"
  />
</template>
```

## 注意事项

### 1. 性能优化
- 大表单时合理使用分步或分组
- 避免在schema中使用复杂的响应式对象
- 合理使用条件显示减少不必要的渲染
- 使用防抖处理频繁的数据联动

### 2. 用户体验
- 提供清晰的表单结构和步骤指引
- 合理设置表单验证和错误提示
- 支持表单数据的自动保存
- 提供表单填写进度提示

### 3. 数据处理
- 统一表单数据格式和字段命名
- 合理处理异步数据加载和验证
- 提供数据转换和格式化功能
- 处理表单提交的异常情况

### 4. 可维护性
- 合理组织表单配置结构
- 使用组件化思维拆分复杂表单
- 提供清晰的文档和示例
- 支持表单配置的动态加载

## 常见问题

### Q: 如何实现表单项的动态显示和隐藏？
A: 使用conditional配置或在schema中设置show函数

### Q: 如何实现表单数据的联动？
A: 使用linkage配置或watch配置监听字段变化

### Q: 如何实现分步表单的数据验证？
A: 在stepConfig中设置validateOnNext为true，并为每个步骤配置相应的验证规则

### Q: 如何处理复杂的表单布局？
A: 使用响应式配置、分组表单或自定义插槽来实现复杂布局
