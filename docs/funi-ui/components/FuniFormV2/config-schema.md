# FuniFormV2 配置结构

## 基础配置结构

```typescript
interface FuniFormV2Config {
  // 数据绑定
  modelValue: Record<string, any>;  // 表单数据对象（必填）
  schema: FormItemConfigV2[];       // 表单项配置数组（必填）
  
  // 基础配置
  labelWidth?: string;              // 标签宽度，默认'100px'
  labelPosition?: 'left' | 'right' | 'top'; // 标签位置，默认'right'
  size?: 'large' | 'default' | 'small'; // 组件尺寸，默认'default'
  disabled?: boolean;               // 是否禁用，默认false
  readonly?: boolean;               // 是否只读，默认false
  
  // 布局配置
  inline?: boolean;                 // 是否行内表单，默认false
  gutter?: number;                  // 栅格间隔，默认20
  responsive?: boolean;             // 是否响应式布局，默认true
  
  // 验证配置
  rules?: Record<string, FormRule[]>; // 表单验证规则
  validateOnRuleChange?: boolean;   // 是否在rules改变后立即触发验证，默认true
  hideRequiredAsterisk?: boolean;   // 是否隐藏必填字段标签旁的红色星号，默认false
  showMessage?: boolean;            // 是否显示校验错误信息，默认true
  inlineMessage?: boolean;          // 是否以行内形式展示校验信息，默认false
  statusIcon?: boolean;             // 是否在输入框中显示校验结果反馈图标，默认false
  scrollToError?: boolean;          // 当校验失败时，滚动到第一个错误表单项，默认false
  scrollIntoViewOptions?: object | boolean; // 滚动到错误字段时的行为
  
  // V2增强功能
  dynamicForm?: DynamicFormConfig;  // 动态表单配置
  conditionalDisplay?: ConditionalDisplayConfig; // 条件显示配置
  dataLinkage?: DataLinkageConfig;  // 数据联动配置
  autoSave?: AutoSaveConfig;        // 自动保存配置
  stepForm?: StepFormConfig;        // 分步表单配置
  groupForm?: GroupFormConfig;      // 分组表单配置
  
  // 行为配置
  formMode?: 'create' | 'edit' | 'view'; // 表单模式，默认'create'
  submitOnEnter?: boolean;          // 是否在按下回车键时提交表单，默认false
  resetOnSubmit?: boolean;          // 提交成功后是否重置表单，默认false
}
```

## 表单项配置结构V2

```typescript
interface FormItemConfigV2 {
  // 基础配置
  prop: string;                     // 字段名（必填）
  label: string;                    // 字段标签（必填）
  component: ComponentType;         // 组件类型（必填）
  
  // 组件配置
  props?: Record<string, any>;      // 组件属性
  options?: OptionItem[];           // 选项数据（select、radio、checkbox等）
  
  // 布局配置
  span?: number;                    // 栅格占据列数（1-24），默认24
  offset?: number;                  // 栅格左侧间隔格数，默认0
  push?: number;                    // 栅格向右移动格数，默认0
  pull?: number;                    // 栅格向左移动格数，默认0
  
  // 响应式配置
  xs?: number | ResponsiveConfig;   // 超小屏幕配置
  sm?: number | ResponsiveConfig;   // 小屏幕配置
  md?: number | ResponsiveConfig;   // 中等屏幕配置
  lg?: number | ResponsiveConfig;   // 大屏幕配置
  xl?: number | ResponsiveConfig;   // 超大屏幕配置
  
  // 验证规则
  rules?: FormRule[];               // 字段验证规则
  required?: boolean;               // 是否必填
  
  // V2增强功能
  dependencies?: string[];          // 依赖字段
  watch?: WatchConfig;              // 监听配置
  conditional?: ConditionalConfig;  // 条件配置
  linkage?: LinkageConfig;          // 联动配置
  
  // 显示控制
  show?: boolean | ShowCondition;   // 是否显示
  disabled?: boolean | DisabledCondition; // 是否禁用
  readonly?: boolean | ReadonlyCondition; // 是否只读
  
  // 分组和步骤
  group?: string;                   // 所属分组
  step?: number;                    // 所属步骤
  
  // 样式配置
  class?: string;                   // 自定义类名
  style?: object;                   // 自定义样式
  labelWidth?: string;              // 标签宽度
  
  // 提示信息
  tooltip?: string | TooltipConfig; // 提示信息
  helpText?: string;                // 帮助文本
  placeholder?: string | PlaceholderFunction; // 占位符
  
  // 数据处理
  defaultValue?: any;               // 默认值
  transformer?: DataTransformer;    // 数据转换器
  formatter?: DataFormatter;        // 数据格式化器
  parser?: DataParser;              // 数据解析器
  
  // 权限控制
  permission?: string | string[];   // 权限标识
  
  // 其他配置
  [key: string]: any;               // 扩展配置
}

type ComponentType = 
  // 输入类组件
  | 'el-input'
  | 'el-textarea'
  | 'el-input-number'
  | 'el-autocomplete'
  // 选择类组件
  | 'el-select'
  | 'el-cascader'
  | 'el-tree-select'
  | 'el-radio-group'
  | 'el-checkbox-group'
  // 日期时间类组件
  | 'el-date-picker'
  | 'el-time-picker'
  | 'el-datetime-picker'
  // 其他组件
  | 'el-switch'
  | 'el-slider'
  | 'el-rate'
  | 'el-color-picker'
  | 'el-upload'
  // FuniUI组件
  | 'FuniSelect'
  | 'FuniTreeSelect'
  | 'FuniRUOC'
  | 'FuniRegion'
  | 'FuniMoneyInput'
  | 'FuniIconSelect'
  | 'FuniFileTable';
```

## 动态表单配置

```typescript
interface DynamicFormConfig {
  enabled: boolean;                 // 是否启用动态表单
  addable?: boolean;                // 是否可添加表单项，默认true
  removable?: boolean;              // 是否可移除表单项，默认true
  sortable?: boolean;               // 是否可排序表单项，默认false
  maxItems?: number;                // 最大表单项数量
  minItems?: number;                // 最小表单项数量，默认1
  addText?: string;                 // 添加按钮文本，默认'添加'
  removeText?: string;              // 移除按钮文本，默认'删除'
  template?: FormItemConfigV2;      // 新增表单项模板
  itemWrapper?: string;             // 表单项包装器组件
  addButtonPosition?: 'top' | 'bottom'; // 添加按钮位置，默认'bottom'
}
```

## 条件显示配置

```typescript
interface ConditionalDisplayConfig {
  enabled: boolean;                 // 是否启用条件显示
  rules: ConditionalRule[];         // 条件规则
}

interface ConditionalRule {
  target: string | string[];        // 目标字段
  condition: ConditionFunction;     // 条件函数
  action: ConditionalAction;        // 动作类型
  value?: any;                      // 动作值
}

type ConditionalAction = 
  | 'show'                          // 显示
  | 'hide'                          // 隐藏
  | 'enable'                        // 启用
  | 'disable'                       // 禁用
  | 'require'                       // 必填
  | 'optional'                      // 可选
  | 'setValue'                      // 设置值
  | 'clearValue';                   // 清空值

type ConditionFunction = (formData: Record<string, any>) => boolean;
```

## 数据联动配置

```typescript
interface DataLinkageConfig {
  enabled: boolean;                 // 是否启用数据联动
  rules: LinkageRule[];             // 联动规则
}

interface LinkageRule {
  source: string | string[];        // 源字段
  target: string | string[];        // 目标字段
  handler: LinkageHandler;          // 联动处理函数
  immediate?: boolean;              // 是否立即执行，默认false
  debounce?: number;                // 防抖延迟时间（毫秒），默认300
}

type LinkageHandler = (
  sourceValue: any, 
  formData: Record<string, any>, 
  context: LinkageContext
) => any | Promise<any>;

interface LinkageContext {
  setFieldValue: (field: string, value: any) => void;
  getFieldValue: (field: string) => any;
  setFieldOptions: (field: string, options: OptionItem[]) => void;
  setFieldProps: (field: string, props: Record<string, any>) => void;
}
```

## 自动保存配置

```typescript
interface AutoSaveConfig {
  enabled: boolean;                 // 是否启用自动保存
  delay?: number;                   // 保存延迟时间（毫秒），默认1000
  fields?: string[];                // 监听的字段，默认所有字段
  saveHandler?: SaveHandler;        // 保存处理函数
  storageKey?: string;              // 本地存储键名
  showIndicator?: boolean;          // 是否显示保存指示器，默认true
  indicatorText?: string;           // 保存指示器文本，默认'自动保存中...'
}

type SaveHandler = (formData: Record<string, any>) => Promise<void>;
```

## 分步表单配置

```typescript
interface StepFormConfig {
  enabled: boolean;                 // 是否启用分步表单
  steps: StepConfig[];              // 步骤配置
  currentStep?: number;             // 当前步骤，默认0
  showStepNumber?: boolean;         // 是否显示步骤编号，默认true
  showStepTitle?: boolean;          // 是否显示步骤标题，默认true
  allowSkip?: boolean;              // 是否允许跳过步骤，默认false
  validateOnNext?: boolean;         // 下一步时是否验证，默认true
  submitOnLastStep?: boolean;       // 最后一步是否自动提交，默认false
  navigation?: StepNavigationConfig; // 导航配置
}

interface StepConfig {
  key: string;                      // 步骤键
  title: string;                    // 步骤标题
  description?: string;             // 步骤描述
  icon?: string;                    // 步骤图标
  fields: string[];                 // 包含的字段
  optional?: boolean;               // 是否可选步骤，默认false
  validator?: StepValidator;        // 自定义验证函数
  beforeEnter?: StepHook;           // 进入步骤前钩子
  afterLeave?: StepHook;            // 离开步骤后钩子
}

interface StepNavigationConfig {
  showPrevButton?: boolean;         // 是否显示上一步按钮，默认true
  showNextButton?: boolean;         // 是否显示下一步按钮，默认true
  prevButtonText?: string;          // 上一步按钮文本，默认'上一步'
  nextButtonText?: string;          // 下一步按钮文本，默认'下一步'
  submitButtonText?: string;        // 提交按钮文本，默认'提交'
  position?: 'top' | 'bottom' | 'both'; // 导航位置，默认'bottom'
}

type StepValidator = (formData: Record<string, any>, fields: string[]) => Promise<boolean>;
type StepHook = (stepKey: string, formData: Record<string, any>) => void | Promise<void>;
```

## 分组表单配置

```typescript
interface GroupFormConfig {
  enabled: boolean;                 // 是否启用分组表单
  groups: GroupConfig[];            // 分组配置
  layout?: 'tabs' | 'collapse' | 'card'; // 布局方式，默认'tabs'
  activeGroup?: string;             // 当前激活分组
  collapsible?: boolean;            // 是否可折叠（collapse布局），默认true
  accordion?: boolean;              // 是否手风琴模式（collapse布局），默认false
  tabPosition?: 'top' | 'right' | 'bottom' | 'left'; // 标签页位置（tabs布局），默认'top'
}

interface GroupConfig {
  key: string;                      // 分组键
  title: string;                    // 分组标题
  description?: string;             // 分组描述
  icon?: string;                    // 分组图标
  fields: string[];                 // 包含的字段
  collapsed?: boolean;              // 是否默认折叠，默认false
  disabled?: boolean;               // 是否禁用，默认false
  required?: boolean;               // 是否必填分组，默认false
  validator?: GroupValidator;       // 分组验证函数
  permission?: string | string[];   // 权限标识
}

type GroupValidator = (formData: Record<string, any>, fields: string[]) => Promise<boolean>;
```

## 常用配置组合

### 基础增强表单配置
```typescript
const basicFormV2Config: FuniFormV2Config = {
  modelValue: {},
  schema: [
    {
      prop: 'name',
      label: '姓名',
      component: 'el-input',
      props: { placeholder: '请输入姓名' },
      rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      span: 12
    },
    {
      prop: 'email',
      label: '邮箱',
      component: 'el-input',
      props: { placeholder: '请输入邮箱' },
      rules: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      span: 12
    }
  ],
  labelWidth: '100px',
  gutter: 20,
  responsive: true
};
```

### 动态表单配置
```typescript
const dynamicFormConfig: FuniFormV2Config = {
  modelValue: { contacts: [{}] },
  schema: [
    {
      prop: 'contacts',
      label: '联系人',
      component: 'dynamic-list',
      span: 24
    }
  ],
  dynamicForm: {
    enabled: true,
    addable: true,
    removable: true,
    maxItems: 5,
    minItems: 1,
    template: {
      name: { label: '姓名', component: 'el-input', required: true },
      phone: { label: '电话', component: 'el-input' }
    }
  }
};
```

### 分步表单配置
```typescript
const stepFormConfig: FuniFormV2Config = {
  modelValue: {},
  schema: [
    { prop: 'name', label: '姓名', component: 'el-input', step: 0 },
    { prop: 'email', label: '邮箱', component: 'el-input', step: 0 },
    { prop: 'address', label: '地址', component: 'el-input', step: 1 },
    { prop: 'company', label: '公司', component: 'el-input', step: 1 }
  ],
  stepForm: {
    enabled: true,
    steps: [
      { key: 'basic', title: '基本信息', fields: ['name', 'email'] },
      { key: 'detail', title: '详细信息', fields: ['address', 'company'] }
    ],
    validateOnNext: true
  }
};
```

### 联动表单配置
```typescript
const linkageFormConfig: FuniFormV2Config = {
  modelValue: {},
  schema: [
    {
      prop: 'province',
      label: '省份',
      component: 'el-select',
      options: provinceOptions
    },
    {
      prop: 'city',
      label: '城市',
      component: 'el-select',
      options: [],
      dependencies: ['province']
    }
  ],
  dataLinkage: {
    enabled: true,
    rules: [
      {
        source: 'province',
        target: 'city',
        handler: async (provinceCode, formData, context) => {
          const cities = await getCitiesByProvince(provinceCode);
          context.setFieldOptions('city', cities);
          context.setFieldValue('city', '');
        }
      }
    ]
  }
};
```

## 最佳实践建议

### 1. 性能优化
- 合理使用防抖处理联动和自动保存
- 避免在schema中使用复杂的响应式对象
- 大表单时使用分步或分组减少渲染压力
- 合理设置条件显示减少不必要的组件渲染

### 2. 用户体验
- 提供清晰的表单结构和步骤指引
- 合理设置表单验证和错误提示
- 支持表单数据的自动保存
- 提供表单填写进度提示

### 3. 数据处理
- 统一表单数据格式和字段命名
- 合理处理异步数据加载和验证
- 提供数据转换和格式化功能
- 处理表单提交的异常情况

### 4. 可维护性
- 合理组织表单配置结构
- 使用组件化思维拆分复杂表单
- 提供清晰的文档和示例
- 支持表单配置的动态加载
