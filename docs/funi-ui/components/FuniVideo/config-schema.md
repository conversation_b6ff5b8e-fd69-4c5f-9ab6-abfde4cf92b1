# FuniVideo 配置结构定义

## 组件配置接口

### IFuniVideoProps

```typescript
interface IFuniVideoProps {
  /** 视频播放器高度 */
  height?: number
  /** 视频容器样式配置 */
  funiVideoStyle?: Record<string, string>
  /** 摄像头列表样式配置 */
  treeStyle?: Record<string, string>
  /** 左侧容器内边距 */
  leftPadding?: string
  /** 左侧容器宽度 */
  leftWidth?: number | string
  /** 右侧容器宽度（flex 值） */
  rightWidth?: number
  /** 视频适配方式 */
  'object-fit'?: 'contain' | 'cover' | 'fill' | 'scale-down'
  /** 视频地址 */
  url?: string
  /** 视频类型 */
  type?: 'mpegts' | 'm2ts' | 'flv' | 'm3u8' | 'dh'
  /** 大华摄像头配置 */
  dhConfig?: IDhConfig
  /** 是否显示云台控制 */
  isShowControl?: boolean
  /** 是否自动播放 */
  isAuto?: boolean
  /** 视频列表数据 */
  dataTree?: ICameraItem[]
  /** 视频列表中是否包含视频地址 */
  dataTreeVideoUrl?: string
  /** 是否显示父级节点 */
  isShowTreeParent?: boolean
  /** 云台操作速度 */
  speed?: number
  /** 内置 API 参数 */
  treeApiParams?: Record<string, any>
  /** 自定义视频列表 API */
  customTreeApi?: IApiConfig
  /** 视频获取 API */
  getVideoApi?: IApiConfig
  /** 云台控制变焦 API */
  operateCamera?: IApiConfig
  /** 云台控制方向 API */
  operateDirectApi?: IApiConfig
  /** 树组件字段映射 */
  defaultTreeProps?: ITreeProps
}
```

### IFuniVideoMethods

```typescript
interface IFuniVideoMethods {
  /** 初始化播放器 */
  init: () => void
  /** 播放视频 */
  play: (url?: string, channelId?: string) => void
  /** 暂停播放 */
  pause: () => void
  /** 销毁播放器 */
  destroy: () => void
  /** 关闭当前视频窗口（大华） */
  closeVideo: () => void
}
```

## 配置对象结构

### IDhConfig

```typescript
interface IDhConfig {
  /** 唯一 ID，不能重复 */
  videoId: string
  /** 初始化创建窗口个数 */
  division: number
  /** 窗口类型 */
  windowType: number
  /** 创建成功回调 */
  createSuccess?: (versionInfo: any) => void
  /** 点击窗口回调 */
  clickWindow?: (snum: number, info: any) => void
  /** 创建错误回调 */
  createError?: (err: any) => void
}
```

### IApiConfig

```typescript
interface IApiConfig {
  /** API 地址 */
  api: string
  /** 请求方法 */
  method: 'get' | 'post' | 'put' | 'delete'
  /** 请求参数 */
  params: Record<string, any>
}
```

### ITreeProps

```typescript
interface ITreeProps {
  /** 子节点字段 */
  children: string
  /** 标签字段 */
  label: string
  /** ID 字段 */
  id: string
  /** 是否父节点字段 */
  isParent: string
  /** 父 ID 字段 */
  pId: string
  /** 通道 ID 字段 */
  channelId: string
}
```

### ICameraItem

```typescript
interface ICameraItem {
  /** 摄像头 ID */
  id: string
  /** 摄像头名称 */
  name: string
  /** 是否为父节点 */
  isParent: boolean
  /** 父节点 ID */
  pId?: string
  /** 子节点列表 */
  children?: ICameraItem[]
  /** 通道 ID */
  channelId?: string
  /** 视频地址（可选） */
  videoUrl?: string
}
```

## 内部状态结构

### IComponentState

```typescript
interface IComponentState {
  /** 播放器实例 */
  player: any
  /** 视频类型 */
  videoType: string
  /** 视频 DOM 引用 */
  videoRef: Ref<HTMLVideoElement>
  /** 摄像头数据 */
  _dataTree: Ref<ICameraItem[]>
  /** 当前通道 ID */
  channelId: string
  /** 大华视窗编号 */
  snum: number
  /** DOM ID */
  domId: string
}
```

## 播放器配置

### IMpegtsConfig

```typescript
interface IMpegtsConfig {
  /** 视频类型 */
  type: 'mpegts' | 'm2ts' | 'flv'
  /** 是否为直播流 */
  isLive: boolean
  /** 视频地址 */
  url: string
  /** 其他配置 */
  [key: string]: any
}
```

### IHlsConfig

```typescript
interface IHlsConfig {
  /** 是否启用调试 */
  debug?: boolean
  /** 启用工作线程 */
  enableWorker?: boolean
  /** 低延迟模式 */
  lowLatencyMode?: boolean
  /** 其他配置 */
  [key: string]: any
}
```

## 样式配置结构

### IStyleConfig

```typescript
interface IStyleConfig {
  /** 视频容器样式 */
  funiVideoStyle: {
    '--funi_video_background_color': string
  }
  /** 树形组件样式 */
  treeStyle: {
    '--el-tree-text-color': string
    '--el-tree-node-hover-bg-color': string
  }
  /** 左侧容器样式 */
  leftStyle: {
    width?: string
    flex?: number
    padding: string
  }
  /** 右侧容器样式 */
  rightStyle: {
    width?: string
    flex?: number
  }
}
```

## API 接口配置

### IDefaultApiConfig

```typescript
interface IDefaultApiConfig {
  /** 摄像头列表 API */
  treeApi: {
    url: '/aplcm/findVideos/resourcesTree'
    method: 'post'
    params: {
      rType: 302
      nodeType: 'resource'
      isSearchSubResources: boolean
    }
  }
  /** 视频获取 API */
  videoApi: {
    url: '/aplcm/findVideos/startVideo'
    method: 'post'
    params: {
      channelId: string
      dataType: number
      streamType: number
    }
  }
  /** 云台控制 API */
  cameraApi: {
    url: '/aplcm/findVideos/operateCamera'
    method: 'post'
  }
  /** 方向控制 API */
  directApi: {
    url: '/aplcm/findVideos/operateDirect'
    method: 'post'
  }
}
```

## 云台控制配置

### IControlParams

```typescript
interface IControlParams {
  /** 操作类型 */
  type: 'operateCamera' | 'operateDirect'
  /** 通道 ID */
  channelId: string
  /** 操作命令 */
  command?: string
  /** 操作速度 */
  speed?: number
  /** 其他参数 */
  [key: string]: any
}
```

### IControlCommands

```typescript
interface IControlCommands {
  /** 方向控制 */
  direction: {
    up: 'UP'
    down: 'DOWN'
    left: 'LEFT'
    right: 'RIGHT'
    stop: 'STOP'
  }
  /** 变焦控制 */
  zoom: {
    in: 'ZOOM_IN'
    out: 'ZOOM_OUT'
    stop: 'ZOOM_STOP'
  }
  /** 焦点控制 */
  focus: {
    near: 'FOCUS_NEAR'
    far: 'FOCUS_FAR'
    stop: 'FOCUS_STOP'
  }
}
```

## 事件处理配置

### IEventHandlers

```typescript
interface IEventHandlers {
  /** 初始化处理 */
  init: () => void
  /** 树节点点击处理 */
  treeNodeClick: (item: ICameraItem, node: any) => void
  /** 云台控制输出处理 */
  controlOutput: (params: IControlParams) => void
  /** 播放处理 */
  play: (url?: string, channelId?: string) => void
  /** 暂停处理 */
  pause: () => void
  /** 销毁处理 */
  destroy: () => void
}
```

## 配置示例

### 基础配置

```typescript
const basicConfig: IFuniVideoProps = {
  height: 500,
  url: 'https://example.com/live.flv',
  type: 'flv',
  isAuto: true,
  isShowControl: false
}
```

### 大华摄像头配置

```typescript
const dhConfig: IFuniVideoProps = {
  type: 'dh',
  dhConfig: {
    videoId: 'video-player-1',
    division: 1,
    windowType: 0,
    createSuccess: (versionInfo) => {
      console.log('初始化成功', versionInfo)
    },
    clickWindow: (snum, info) => {
      console.log('窗口点击', snum, info)
    },
    createError: (err) => {
      console.error('初始化失败', err)
    }
  },
  isShowControl: true,
  speed: 5
}
```

### 自定义 API 配置

```typescript
const customApiConfig: IFuniVideoProps = {
  customTreeApi: {
    api: '/api/cameras/list',
    method: 'get',
    params: { type: 'surveillance' }
  },
  getVideoApi: {
    api: '/api/video/start',
    method: 'post',
    params: {
      channelId: '',
      dataType: 1,
      streamType: 1
    }
  },
  operateCamera: {
    api: '/api/camera/zoom',
    method: 'post',
    params: {}
  },
  operateDirectApi: {
    api: '/api/camera/direction',
    method: 'post',
    params: {}
  }
}
```

## 默认配置

```typescript
const defaultConfig: Required<IFuniVideoProps> = {
  height: 500,
  funiVideoStyle: {
    '--funi_video_background_color': 'rgba(3,25,49,.89)'
  },
  treeStyle: {
    '--el-tree-text-color': '#fff',
    '--el-tree-node-hover-bg-color': '#bae7ff'
  },
  leftPadding: '10px 20px',
  leftWidth: '280px',
  rightWidth: 1,
  'object-fit': 'fill',
  url: '',
  type: '',
  dhConfig: undefined,
  isShowControl: true,
  isAuto: true,
  dataTree: [],
  dataTreeVideoUrl: '',
  isShowTreeParent: false,
  speed: 3,
  treeApiParams: {},
  customTreeApi: {
    api: '/aplcm/findVideos/resourcesTree',
    method: 'post',
    params: {}
  },
  getVideoApi: {
    api: '/aplcm/findVideos/startVideo',
    method: 'post',
    params: {
      channelId: '',
      dataType: 1,
      streamType: 1
    }
  },
  operateCamera: {
    api: '/aplcm/findVideos/operateCamera',
    method: 'post',
    params: {}
  },
  operateDirectApi: {
    api: '/aplcm/findVideos/operateDirect',
    method: 'post',
    params: {}
  },
  defaultTreeProps: {
    children: 'children',
    label: 'name',
    id: 'id',
    isParent: 'isParent',
    pId: 'pId',
    channelId: 'id'
  }
}
```
