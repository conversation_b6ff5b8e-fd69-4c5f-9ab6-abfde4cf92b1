# FuniVideo API 文档

## 组件概述

FuniVideo 是一个视频播放组件，支持多种视频格式和协议的播放，包括 FLV、HLS、RTSP 等。组件集成了摄像头列表管理和云台控制功能，特别适用于监控视频播放场景。支持大华摄像头的专业控制功能。

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| height | Number | 500 | 视频播放器高度 |
| funiVideoStyle | Object | 见下方 | 视频容器样式配置 |
| treeStyle | Object | 见下方 | 摄像头列表样式配置 |
| leftPadding | String | '10px 20px' | 左侧容器内边距 |
| leftWidth | Number \| String | '280px' | 左侧容器宽度 |
| rightWidth | Number | 1 | 右侧容器宽度（flex 值） |
| object-fit | String | 'fill' | 视频适配方式 |
| url | String | '' | 视频地址 |
| type | String | '' | 视频类型 |
| dhConfig | Object | - | 大华摄像头配置 |
| isShowControl | Boolean | true | 是否显示云台控制 |
| isAuto | Boolean | true | 是否自动播放 |
| dataTree | Array | [] | 视频列表数据 |
| dataTreeVideoUrl | String | '' | 视频列表中是否包含视频地址 |
| isShowTreeParent | Boolean | false | 是否显示父级节点 |
| speed | Number | 3 | 云台操作速度 |
| treeApiParams | Object | {} | 内置 API 参数 |
| customTreeApi | Object | 见下方 | 自定义视频列表 API |
| getVideoApi | Object | 见下方 | 视频获取 API |
| operateCamera | Object | 见下方 | 云台控制变焦 API |
| operateDirectApi | Object | 见下方 | 云台控制方向 API |
| defaultTreeProps | Object | 见下方 | 树组件字段映射 |

### Props 详细说明

#### 样式配置

```javascript
// funiVideoStyle 默认值
{
  '--funi_video_background_color': 'rgba(3,25,49,.89)'
}

// treeStyle 默认值
{
  '--el-tree-text-color': '#fff',
  '--el-tree-node-hover-bg-color': '#bae7ff'
}
```

#### 视频配置

- **object-fit**: 视频适配方式，可选值：'contain', 'cover', 'fill', 'scale-down'
- **type**: 支持的视频类型：'mpegts', 'm2ts', 'flv', 'm3u8', 'dh'（大华）
- **url**: 视频流地址，支持 RTSP、HTTP 等协议

#### API 配置

```javascript
// customTreeApi 默认值
{
  api: '/aplcm/findVideos/resourcesTree',
  method: 'post',
  params: {}
}

// getVideoApi 默认值
{
  api: '/aplcm/findVideos/startVideo',
  method: 'post',
  params: {
    channelId: '',
    dataType: 1,
    streamType: 1 // 1=主码流，2=辅码流，3=辅码流
  }
}

// operateCamera 默认值
{
  api: '/aplcm/findVideos/operateCamera',
  method: 'post',
  params: {}
}

// operateDirectApi 默认值
{
  api: '/aplcm/findVideos/operateDirect',
  method: 'post',
  params: {}
}

// defaultTreeProps 默认值
{
  children: 'children',
  label: 'name',
  id: 'id',
  isParent: 'isParent',
  pId: 'pId',
  channelId: 'id'
}
```

## Methods

| 方法名 | 参数 | 说明 |
|--------|------|------|
| init | - | 初始化播放器 |
| play | (url?, channelId?) | 播放视频 |
| pause | - | 暂停播放 |
| destroy | - | 销毁播放器 |
| closeVideo | - | 关闭当前视频窗口（大华） |

### Methods 使用示例

```javascript
// 获取组件引用
const videoRef = ref()

// 初始化
videoRef.value.init()

// 播放
videoRef.value.play()

// 暂停
videoRef.value.pause()

// 销毁
videoRef.value.destroy()
```

## 功能特性

### 1. 多格式支持
- **FLV/MPEGTS**: 使用 mpegts.js 播放
- **HLS**: 使用 hls.js 播放
- **大华摄像头**: 使用专用 VideoPlayer

### 2. 摄像头管理
- 树形结构显示摄像头列表
- 支持点击切换摄像头
- 自动播放第一个可用摄像头

### 3. 云台控制
- 方向控制（上下左右）
- 变焦控制（放大缩小）
- 速度调节
- 专为大华摄像头优化

### 4. 响应式布局
- 左右分栏布局
- 可配置宽度比例
- 自适应容器大小

## 使用示例

### 基础视频播放

```vue
<template>
  <div style="height: 600px;">
    <FuniVideo 
      :url="videoUrl"
      type="flv"
      :height="500"
      :isShowControl="false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniVideo from '@/components/FuniVideo/index.vue'

const videoUrl = ref('https://example.com/live.flv')
</script>
```

### 大华摄像头监控

```vue
<template>
  <div style="height: 800px;">
    <FuniVideo 
      ref="videoRef"
      type="dh"
      :dhConfig="dhConfig"
      :dataTree="cameraList"
      :isShowControl="true"
      :speed="5"
      @treeNodeClick="handleCameraSelect"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniVideo from '@/components/FuniVideo/index.vue'

const videoRef = ref()

const dhConfig = {
  videoId: 'video-player-1',
  division: 1,
  windowType: 0,
  createSuccess: (versionInfo) => {
    console.log('大华播放器初始化成功', versionInfo)
  },
  clickWindow: (snum, info) => {
    console.log('窗口点击', snum, info)
  },
  createError: (err) => {
    console.error('大华播放器初始化失败', err)
  }
}

const cameraList = ref([
  {
    id: '001',
    name: '前门摄像头',
    isParent: false,
    pId: null
  },
  {
    id: '002',
    name: '后门摄像头',
    isParent: false,
    pId: null
  }
])

const handleCameraSelect = (camera) => {
  console.log('选择摄像头：', camera)
}
</script>
```

### 自定义 API 配置

```vue
<template>
  <div style="height: 600px;">
    <FuniVideo 
      type="dh"
      :customTreeApi="customApi"
      :getVideoApi="videoApi"
      :operateCamera="cameraApi"
      :operateDirectApi="directApi"
    />
  </div>
</template>

<script setup>
import FuniVideo from '@/components/FuniVideo/index.vue'

const customApi = {
  api: '/api/cameras/list',
  method: 'get',
  params: {
    type: 'surveillance'
  }
}

const videoApi = {
  api: '/api/video/start',
  method: 'post',
  params: {
    channelId: '',
    dataType: 1,
    streamType: 1
  }
}

const cameraApi = {
  api: '/api/camera/zoom',
  method: 'post',
  params: {}
}

const directApi = {
  api: '/api/camera/direction',
  method: 'post',
  params: {}
}
</script>
```

### HLS 直播流

```vue
<template>
  <div style="height: 500px;">
    <FuniVideo 
      :url="hlsUrl"
      type="m3u8"
      :isShowControl="false"
      :isAuto="true"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniVideo from '@/components/FuniVideo/index.vue'

const hlsUrl = ref('https://example.com/live/stream.m3u8')
</script>
```

### 多窗口监控

```vue
<template>
  <div class="multi-video">
    <FuniVideo 
      v-for="(camera, index) in cameras"
      :key="camera.id"
      :ref="el => videoRefs[index] = el"
      type="dh"
      :height="300"
      :dataTree="[camera]"
      :isShowControl="false"
      class="video-item"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniVideo from '@/components/FuniVideo/index.vue'

const videoRefs = ref([])
const cameras = ref([
  { id: '001', name: '摄像头1' },
  { id: '002', name: '摄像头2' },
  { id: '003', name: '摄像头3' },
  { id: '004', name: '摄像头4' }
])
</script>

<style scoped>
.multi-video {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  height: 600px;
}

.video-item {
  border: 1px solid #ddd;
}
</style>
```

## 技术依赖

### 1. 视频播放库
- **mpegts.js**: FLV/MPEGTS 格式支持
- **hls.js**: HLS 格式支持
- **VideoPlayer**: 大华摄像头专用播放器

### 2. UI 组件
- **ElementPlus**: 树形组件和消息提示
- **Control**: 云台控制组件

## 样式定制

### CSS 变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| --funi_video_background_color | rgba(3,25,49,.89) | 视频容器背景色 |
| --el-tree-text-color | #fff | 树形组件文字颜色 |
| --el-tree-node-hover-bg-color | #bae7ff | 树形组件悬停背景色 |

### 自定义样式

```vue
<style>
/* 自定义视频容器样式 */
.funi-video {
  border-radius: 8px;
  overflow: hidden;
}

/* 自定义左侧面板样式 */
.funi-video .left {
  background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
}

/* 自定义视频播放区域 */
.funi-video video {
  border-radius: 4px;
}
</style>
```

## 注意事项

1. **浏览器兼容性**：确保浏览器支持 MSE（Media Source Extensions）
2. **视频格式**：不同格式需要对应的解码库支持
3. **大华摄像头**：需要安装大华 WebSDK 插件
4. **网络协议**：RTSP 流可能需要转换为 WebRTC 或 HLS
5. **性能考虑**：多路视频同时播放会消耗大量资源
6. **安全性**：确保视频流地址的安全性和访问权限
7. **跨域问题**：注意视频资源的跨域配置
