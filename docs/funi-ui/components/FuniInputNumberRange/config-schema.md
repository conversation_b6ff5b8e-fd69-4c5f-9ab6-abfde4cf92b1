# FuniInputNumberRange 配置结构定义

## 组件配置接口

### IFuniInputNumberRangeProps

```typescript
interface IFuniInputNumberRangeProps {
  /** 绑定值，数组格式 [min, max] */
  modelValue?: [number | null, number | null] | string | []
}
```

### IFuniInputNumberRangeEmits

```typescript
interface IFuniInputNumberRangeEmits {
  /** v-model 更新事件 */
  'update:modelValue': (value: [number | null, number | null]) => void
}
```

## 内部状态结构

### IComponentState

```typescript
interface IComponentState {
  /** 最小值 */
  minValue: Ref<number | null>
  /** 最大值 */
  maxValue: Ref<number | null>
}
```

### IRangeValue

```typescript
interface IRangeValue {
  /** 最小值 */
  min: number | null
  /** 最大值 */
  max: number | null
}

// 或者使用元组类型
type RangeValueTuple = [number | null, number | null]
```

## 数据格式定义

### IInputFormats

```typescript
interface IInputFormats {
  /** 完整范围 */
  fullRange: [number, number]
  /** 只有最小值 */
  minOnly: [number, null]
  /** 只有最大值 */
  maxOnly: [null, number]
  /** 空范围 */
  empty: [null, null] | []
}
```

### IOutputFormats

```typescript
interface IOutputFormats {
  /** 标准输出格式 */
  standard: [number | null, number | null]
  /** 对象格式（扩展用） */
  object: {
    min: number | null
    max: number | null
  }
  /** 字符串格式（扩展用） */
  string: string // "min,max" 或 "min-max"
}
```

## 样式配置结构

### IStyleConfig

```typescript
interface IStyleConfig {
  /** 主容器样式 */
  container: {
    display: 'flex'
    flexWrap: 'nowrap'
    width: '100%'
  }
  /** 分隔符样式 */
  divider: {
    padding: '0 10px'
    width: '32px'
    flexShrink: 0
    boxSizing: 'border-box'
    display: 'flex'
    justifyContent: 'center'
  }
  /** 输入框样式 */
  inputNumber: {
    flex: 1
    width: 'auto'
    hideButtons: true
    padding: '1px 11px'
  }
}
```

### IStyleClasses

```typescript
interface IStyleClasses {
  /** 主容器类名 */
  container: 'funi-input-number-range'
  /** 分隔符类名 */
  divider: 'divider'
  /** 输入框类名（继承） */
  inputNumber: 'el-input-number'
}
```

## 监听器配置

### IWatcherConfig

```typescript
interface IWatcherConfig {
  /** modelValue 监听器 */
  modelValueWatcher: {
    /** 监听的属性 */
    source: () => Props['modelValue']
    /** 处理函数 */
    handler: () => void
    /** 配置选项 */
    options: {
      immediate?: boolean
      deep?: boolean
    }
  }
  /** 内部值监听器 */
  internalValueWatcher: {
    /** 监听的属性 */
    source: () => [Ref<number | null>, Ref<number | null>]
    /** 处理函数 */
    handler: ([min, max]: [number | null, number | null]) => void
    /** 配置选项 */
    options: {
      immediate?: boolean
      deep?: boolean
    }
  }
}
```

## 事件处理配置

### IEventHandlers

```typescript
interface IEventHandlers {
  /** 范围值更新处理器 */
  updateRangeValue: (min: number | null, max: number | null) => void
  /** 最小值变化处理器 */
  handleMinValueChange: (value: number | null) => void
  /** 最大值变化处理器 */
  handleMaxValueChange: (value: number | null) => void
  /** 范围验证处理器 */
  validateRange: (min: number | null, max: number | null) => boolean
}
```

## 验证规则配置

### IValidationRules

```typescript
interface IValidationRules {
  /** 范围验证 */
  rangeValidation: {
    /** 最小值不能大于最大值 */
    minNotGreaterThanMax: boolean
    /** 是否自动调整无效范围 */
    autoAdjustInvalidRange: boolean
    /** 调整策略 */
    adjustmentStrategy: 'swap' | 'reset' | 'ignore'
  }
  /** 数值验证 */
  valueValidation: {
    /** 是否允许负数 */
    allowNegative: boolean
    /** 是否允许小数 */
    allowDecimal: boolean
    /** 最大精度 */
    maxPrecision: number
  }
}
```

## 配置示例

### 基础配置

```typescript
const basicConfig: IFuniInputNumberRangeProps = {
  modelValue: [0, 100]
}
```

### 价格范围配置

```typescript
const priceRangeConfig: IFuniInputNumberRangeProps = {
  modelValue: [null, null] // 允许用户自由输入
}
```

### 年龄范围配置

```typescript
const ageRangeConfig: IFuniInputNumberRangeProps = {
  modelValue: [18, 65] // 预设合理的年龄范围
}
```

## 扩展配置接口

### IExtendedProps

```typescript
interface IExtendedProps extends IFuniInputNumberRangeProps {
  /** 分隔符文本 */
  separator?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 占位符 */
  placeholder?: [string, string]
  /** 最小值限制 */
  min?: number
  /** 最大值限制 */
  max?: number
  /** 精度控制 */
  precision?: number
  /** 是否显示单位 */
  unit?: string
  /** 单位位置 */
  unitPosition?: 'before' | 'after'
}
```

### IExtendedEmits

```typescript
interface IExtendedEmits extends IFuniInputNumberRangeEmits {
  /** 最小值变化事件 */
  'min-change': (value: number | null) => void
  /** 最大值变化事件 */
  'max-change': (value: number | null) => void
  /** 范围验证失败事件 */
  'validation-error': (error: string) => void
  /** 范围重置事件 */
  'range-reset': () => void
}
```

## 工具函数配置

### IUtilityFunctions

```typescript
interface IUtilityFunctions {
  /** 解析范围值 */
  parseRangeValue: (value: any) => [number | null, number | null]
  /** 格式化范围值 */
  formatRangeValue: (min: number | null, max: number | null) => string
  /** 验证范围有效性 */
  validateRangeValue: (min: number | null, max: number | null) => boolean
  /** 交换最小最大值 */
  swapMinMax: (min: number | null, max: number | null) => [number | null, number | null]
  /** 重置范围值 */
  resetRangeValue: () => [null, null]
}
```

## 主题配置

### IThemeConfig

```typescript
interface IThemeConfig {
  /** 分隔符样式 */
  separator: {
    color: string
    fontSize: string
    fontWeight: string | number
  }
  /** 输入框间距 */
  spacing: {
    between: string // 输入框之间的间距
    padding: string // 内边距
  }
  /** 边框样式 */
  border: {
    color: string
    width: string
    radius: string
  }
  /** 焦点样式 */
  focus: {
    borderColor: string
    boxShadow: string
  }
}
```

## 响应式配置

### IResponsiveConfig

```typescript
interface IResponsiveConfig {
  /** 断点配置 */
  breakpoints: {
    mobile: string // '768px'
    tablet: string // '1024px'
    desktop: string // '1200px'
  }
  /** 响应式行为 */
  behavior: {
    /** 移动端是否垂直排列 */
    stackOnMobile: boolean
    /** 最小宽度 */
    minWidth: string
    /** 最大宽度 */
    maxWidth: string
  }
}
```

## 性能优化配置

### IPerformanceConfig

```typescript
interface IPerformanceConfig {
  /** 防抖配置 */
  debounce: {
    enabled: boolean
    delay: number // 毫秒
  }
  /** 懒加载配置 */
  lazy: {
    enabled: boolean
    threshold: number
  }
  /** 缓存配置 */
  cache: {
    enabled: boolean
    maxSize: number
  }
}
```

## 默认配置

```typescript
const defaultConfig: Required<IFuniInputNumberRangeProps> = {
  modelValue: []
}

const defaultStyleConfig: IStyleConfig = {
  container: {
    display: 'flex',
    flexWrap: 'nowrap',
    width: '100%'
  },
  divider: {
    padding: '0 10px',
    width: '32px',
    flexShrink: 0,
    boxSizing: 'border-box',
    display: 'flex',
    justifyContent: 'center'
  },
  inputNumber: {
    flex: 1,
    width: 'auto',
    hideButtons: true,
    padding: '1px 11px'
  }
}
```
