# FuniInputNumberRange API 文档

## 组件概述

FuniInputNumberRange 是一个数字范围输入组件，由两个 FuniInputNumber 组件组成，用于输入数值范围（最小值到最大值）。组件提供了简洁的范围输入界面，支持双向绑定数组格式的数据。

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Array \| String | [] | 绑定值，数组格式 [min, max] |

### Props 详细说明

#### modelValue
- 组件的绑定值，支持数组和字符串类型
- 数组格式：`[minValue, maxValue]`
- 支持 v-model 双向绑定
- 当传入空数组时，两个输入框都为空
- 数组第一个元素对应最小值输入框，第二个元素对应最大值输入框

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: [number \| null, number \| null]) | 值改变时触发，用于 v-model |

### Events 详细说明

#### update:modelValue
- 用于 v-model 双向绑定
- 当任一输入框的值发生变化时触发
- 参数为包含两个元素的数组：`[minValue, maxValue]`
- 每个元素可能是数字或 null（当输入框为空时）

## 组件结构

### 子组件
- **FuniInputNumber**: 数字输入框组件，用于最小值和最大值输入
- **分隔符**: 显示 "~" 符号，用于分隔两个输入框

### 布局特性
- 使用 Flexbox 布局
- 两个输入框等宽，自动适应容器宽度
- 分隔符固定宽度，不会收缩
- 支持响应式布局

## 样式特性

### CSS 类名

| 类名 | 说明 |
|------|------|
| .funi-input-number-range | 组件根容器 |
| .divider | 分隔符容器 |

### 样式定制

- 隐藏了 FuniInputNumber 的增减按钮
- 调整了输入框的内边距
- 分隔符居中显示
- 支持自定义样式覆盖

## 使用示例

### 基础用法

```vue
<template>
  <div>
    <FuniInputNumberRange 
      v-model="range"
      @update:modelValue="handleRangeChange"
    />
    <p>选择的范围：{{ range[0] }} ~ {{ range[1] }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniInputNumberRange from '@/components/FuniInputNumberRange/index.vue'

const range = ref([0, 100])

const handleRangeChange = (newRange) => {
  console.log('范围改变：', newRange)
}
</script>
```

### 价格范围输入

```vue
<template>
  <div>
    <label>价格范围：</label>
    <FuniInputNumberRange v-model="priceRange" />
    <p v-if="priceRange[0] || priceRange[1]">
      价格筛选：
      <span v-if="priceRange[0]">{{ priceRange[0] }}元</span>
      <span v-if="priceRange[0] && priceRange[1]"> ~ </span>
      <span v-if="priceRange[1]">{{ priceRange[1] }}元</span>
    </p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniInputNumberRange from '@/components/FuniInputNumberRange/index.vue'

const priceRange = ref([null, null])
</script>
```

### 年龄范围选择

```vue
<template>
  <div>
    <label>年龄范围：</label>
    <FuniInputNumberRange v-model="ageRange" />
    <el-button @click="search" type="primary">搜索</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniInputNumberRange from '@/components/FuniInputNumberRange/index.vue'

const ageRange = ref([18, 65])

const search = () => {
  const [minAge, maxAge] = ageRange.value
  console.log(`搜索年龄范围：${minAge} - ${maxAge}`)
  // 执行搜索逻辑
}
</script>
```

### 表单中使用

```vue
<template>
  <el-form :model="form" label-width="120px">
    <el-form-item label="价格范围：">
      <FuniInputNumberRange v-model="form.priceRange" />
    </el-form-item>
    <el-form-item label="数量范围：">
      <FuniInputNumberRange v-model="form.quantityRange" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
import FuniInputNumberRange from '@/components/FuniInputNumberRange/index.vue'

const form = ref({
  priceRange: [0, 1000],
  quantityRange: [1, 100]
})

const submitForm = () => {
  console.log('表单数据：', form.value)
}
</script>
```

### 动态设置范围

```vue
<template>
  <div>
    <FuniInputNumberRange v-model="range" />
    <div style="margin-top: 10px;">
      <el-button @click="setRange(0, 100)">0-100</el-button>
      <el-button @click="setRange(100, 1000)">100-1000</el-button>
      <el-button @click="clearRange">清空</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniInputNumberRange from '@/components/FuniInputNumberRange/index.vue'

const range = ref([null, null])

const setRange = (min, max) => {
  range.value = [min, max]
}

const clearRange = () => {
  range.value = [null, null]
}
</script>
```

## 数据格式

### 输入格式

```javascript
// 完整范围
[10, 100]

// 只有最小值
[10, null]

// 只有最大值
[null, 100]

// 空范围
[null, null]
// 或
[]
```

### 输出格式

```javascript
// 组件始终输出数组格式
[minValue, maxValue]

// 示例
[0, 100]        // 范围 0 到 100
[50, null]      // 最小值 50，无最大值限制
[null, 200]     // 无最小值限制，最大值 200
[null, null]    // 无限制
```

## 注意事项

1. **数据类型**：modelValue 支持数组和字符串类型，但推荐使用数组
2. **数组长度**：输入数组应包含两个元素，分别对应最小值和最大值
3. **空值处理**：空输入框对应的值为 null
4. **数值验证**：继承 FuniInputNumber 的所有验证特性
5. **样式继承**：可以通过 CSS 自定义样式，支持深度选择器
6. **响应式**：组件支持响应式布局，在不同屏幕尺寸下自适应

## 扩展功能

### 自定义分隔符

```vue
<template>
  <div class="custom-range">
    <FuniInputNumber v-model="minValue" />
    <span class="custom-divider">至</span>
    <FuniInputNumber v-model="maxValue" />
  </div>
</template>

<style scoped>
.custom-range {
  display: flex;
  align-items: center;
  gap: 10px;
}

.custom-divider {
  color: #666;
  font-weight: bold;
}
</style>
```

### 添加单位显示

```vue
<template>
  <div class="range-with-unit">
    <FuniInputNumberRange v-model="range" />
    <span class="unit">元</span>
  </div>
</template>

<style scoped>
.range-with-unit {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unit {
  color: #666;
  font-size: 14px;
}
</style>
```
