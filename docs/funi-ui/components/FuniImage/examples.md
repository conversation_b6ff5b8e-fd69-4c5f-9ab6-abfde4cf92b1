# FuniImage 使用示例

## 基础示例

### 1. 基础图片显示
```vue
<template>
  <div class="example-container">
    <h3>基础图片显示</h3>
    <div class="image-grid">
      <!-- 基础用法 -->
      <div class="image-item">
        <h4>基础用法</h4>
        <FuniImage
          src="https://picsum.photos/200/150?random=1"
          alt="基础图片"
          :width="200"
          :height="150"
        />
      </div>
      
      <!-- 不同适应方式 -->
      <div class="image-item">
        <h4>填充模式 (fill)</h4>
        <FuniImage
          src="https://picsum.photos/300/200?random=2"
          alt="填充模式"
          :width="200"
          :height="150"
          fit="fill"
        />
      </div>
      
      <div class="image-item">
        <h4>包含模式 (contain)</h4>
        <FuniImage
          src="https://picsum.photos/300/200?random=3"
          alt="包含模式"
          :width="200"
          :height="150"
          fit="contain"
        />
      </div>
      
      <div class="image-item">
        <h4>覆盖模式 (cover)</h4>
        <FuniImage
          src="https://picsum.photos/300/200?random=4"
          alt="覆盖模式"
          :width="200"
          :height="150"
          fit="cover"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.example-container {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 20px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.image-item {
  text-align: center;
}

.image-item h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}
</style>
```

### 2. 圆角和阴影效果
```vue
<template>
  <div class="example-container">
    <h3>圆角和阴影效果</h3>
    <div class="style-grid">
      <!-- 圆角效果 -->
      <div class="style-item">
        <h4>小圆角</h4>
        <FuniImage
          src="https://picsum.photos/150/150?random=5"
          alt="小圆角"
          :width="150"
          :height="150"
          :radius="8"
          fit="cover"
        />
      </div>
      
      <div class="style-item">
        <h4>大圆角</h4>
        <FuniImage
          src="https://picsum.photos/150/150?random=6"
          alt="大圆角"
          :width="150"
          :height="150"
          :radius="20"
          fit="cover"
        />
      </div>
      
      <div class="style-item">
        <h4>圆形</h4>
        <FuniImage
          src="https://picsum.photos/150/150?random=7"
          alt="圆形"
          :width="150"
          :height="150"
          radius="50%"
          fit="cover"
        />
      </div>
      
      <!-- 阴影效果 -->
      <div class="style-item">
        <h4>始终阴影</h4>
        <FuniImage
          src="https://picsum.photos/150/150?random=8"
          alt="始终阴影"
          :width="150"
          :height="150"
          :radius="8"
          shadow="always"
          fit="cover"
        />
      </div>
      
      <div class="style-item">
        <h4>悬停阴影</h4>
        <FuniImage
          src="https://picsum.photos/150/150?random=9"
          alt="悬停阴影"
          :width="150"
          :height="150"
          :radius="8"
          shadow="hover"
          fit="cover"
        />
      </div>
      
      <div class="style-item">
        <h4>边框样式</h4>
        <FuniImage
          src="https://picsum.photos/150/150?random=10"
          alt="边框样式"
          :width="150"
          :height="150"
          :radius="8"
          :border="true"
          fit="cover"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.style-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.style-item {
  text-align: center;
}

.style-item h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 14px;
}
</style>
```

### 3. 懒加载示例
```vue
<template>
  <div class="example-container">
    <h3>懒加载示例</h3>
    <div class="lazy-container" ref="scrollContainer">
      <div class="lazy-tip">
        <p>向下滚动查看懒加载效果</p>
        <p>图片将在进入视口时加载</p>
      </div>
      
      <div 
        v-for="index in 20" 
        :key="index" 
        class="lazy-item"
      >
        <h4>图片 {{ index }}</h4>
        <FuniImage
          :src="`https://picsum.photos/300/200?random=${index + 20}`"
          :alt="`懒加载图片 ${index}`"
          :width="300"
          :height="200"
          :lazy="true"
          loading="lazy"
          :scroll-container="scrollContainer"
          :placeholder-image="placeholderImage"
          :error-image="errorImage"
          fit="cover"
          :radius="8"
          @load="handleImageLoad(index)"
          @error="handleImageError(index)"
        />
        <div class="load-status">
          <span v-if="loadedImages.includes(index)" class="loaded">✅ 已加载</span>
          <span v-else-if="errorImages.includes(index)" class="error">❌ 加载失败</span>
          <span v-else class="loading">⏳ 等待加载</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const scrollContainer = ref()
const loadedImages = ref([])
const errorImages = ref([])

const placeholderImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veS4rS4uLjwvdGV4dD48L3N2Zz4='

const errorImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmVmMmYyIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2Y1NmM2YyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veWksei0pTwvdGV4dD48L3N2Zz4='

const handleImageLoad = (index) => {
  if (!loadedImages.value.includes(index)) {
    loadedImages.value.push(index)
  }
}

const handleImageError = (index) => {
  if (!errorImages.value.includes(index)) {
    errorImages.value.push(index)
  }
}
</script>

<style scoped>
.lazy-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
}

.lazy-tip {
  text-align: center;
  padding: 20px;
  background-color: #f0f9ff;
  border-radius: 6px;
  margin-bottom: 20px;
  color: #1e40af;
}

.lazy-item {
  margin-bottom: 30px;
  text-align: center;
}

.lazy-item h4 {
  margin-bottom: 10px;
  color: #333;
}

.load-status {
  margin-top: 8px;
  font-size: 12px;
}

.loaded {
  color: #67c23a;
}

.error {
  color: #f56c6c;
}

.loading {
  color: #909399;
}
</style>
```

## 高级示例

### 4. 图片预览画廊
```vue
<template>
  <div class="example-container">
    <h3>图片预览画廊</h3>
    <div class="gallery-grid">
      <FuniImage
        v-for="(image, index) in galleryImages"
        :key="index"
        :src="image.thumb"
        :alt="image.alt"
        :width="150"
        :height="150"
        :radius="8"
        shadow="hover"
        fit="cover"
        :preview-src-list="galleryImages.map(img => img.full)"
        :initial-index="index"
        :z-index="3000"
        class="gallery-item"
      />
    </div>

    <div class="gallery-info">
      <p>点击任意图片可以预览大图</p>
      <p>支持键盘方向键切换图片</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const galleryImages = ref([
  {
    thumb: 'https://picsum.photos/150/150?random=30',
    full: 'https://picsum.photos/800/600?random=30',
    alt: '风景图片1'
  },
  {
    thumb: 'https://picsum.photos/150/150?random=31',
    full: 'https://picsum.photos/800/600?random=31',
    alt: '风景图片2'
  },
  {
    thumb: 'https://picsum.photos/150/150?random=32',
    full: 'https://picsum.photos/800/600?random=32',
    alt: '风景图片3'
  },
  {
    thumb: 'https://picsum.photos/150/150?random=33',
    full: 'https://picsum.photos/800/600?random=33',
    alt: '风景图片4'
  },
  {
    thumb: 'https://picsum.photos/150/150?random=34',
    full: 'https://picsum.photos/800/600?random=34',
    alt: '风景图片5'
  },
  {
    thumb: 'https://picsum.photos/150/150?random=35',
    full: 'https://picsum.photos/800/600?random=35',
    alt: '风景图片6'
  }
])
</script>

<style scoped>
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.gallery-item {
  cursor: pointer;
  transition: transform 0.2s;
}

.gallery-item:hover {
  transform: scale(1.05);
}

.gallery-info {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.gallery-info p {
  margin: 5px 0;
}
</style>
```

### 5. 带水印的图片
```vue
<template>
  <div class="example-container">
    <h3>带水印的图片</h3>
    <div class="watermark-examples">
      <!-- 文字水印 -->
      <div class="watermark-item">
        <h4>文字水印</h4>
        <FuniImage
          src="https://picsum.photos/300/200?random=40"
          alt="文字水印"
          :width="300"
          :height="200"
          :radius="8"
          :watermark="textWatermark"
          fit="cover"
        />
      </div>

      <!-- 图片水印 -->
      <div class="watermark-item">
        <h4>图片水印</h4>
        <FuniImage
          src="https://picsum.photos/300/200?random=41"
          alt="图片水印"
          :width="300"
          :height="200"
          :radius="8"
          :watermark="imageWatermark"
          fit="cover"
        />
      </div>

      <!-- 重复水印 -->
      <div class="watermark-item">
        <h4>重复水印</h4>
        <FuniImage
          src="https://picsum.photos/300/200?random=42"
          alt="重复水印"
          :width="300"
          :height="200"
          :radius="8"
          :watermark="repeatWatermark"
          fit="cover"
        />
      </div>
    </div>

    <div class="watermark-controls">
      <h4>水印配置</h4>
      <el-form :model="customWatermark" label-width="100px" size="small">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="水印文字">
              <el-input v-model="customWatermark.text" placeholder="请输入水印文字" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="字体大小">
              <el-input-number v-model="customWatermark.fontSize" :min="12" :max="48" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="透明度">
              <el-slider v-model="customWatermark.opacity" :min="0" :max="1" :step="0.1" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="位置">
              <el-select v-model="customWatermark.position" placeholder="选择位置">
                <el-option label="左上角" value="top-left" />
                <el-option label="上中" value="top-center" />
                <el-option label="右上角" value="top-right" />
                <el-option label="左中" value="center-left" />
                <el-option label="居中" value="center" />
                <el-option label="右中" value="center-right" />
                <el-option label="左下角" value="bottom-left" />
                <el-option label="下中" value="bottom-center" />
                <el-option label="右下角" value="bottom-right" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="旋转角度">
              <el-input-number v-model="customWatermark.rotation" :min="-180" :max="180" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重复模式">
              <el-switch v-model="customWatermark.repeat" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="custom-preview">
        <h4>预览效果</h4>
        <FuniImage
          src="https://picsum.photos/400/300?random=43"
          alt="自定义水印预览"
          :width="400"
          :height="300"
          :radius="8"
          :watermark="customWatermark"
          fit="cover"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 预设水印配置
const textWatermark = {
  text: '版权所有',
  fontSize: 16,
  fontColor: '#ffffff',
  position: 'bottom-right',
  offsetX: -20,
  offsetY: -20,
  opacity: 0.8,
  rotation: 0
}

const imageWatermark = {
  image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIGZpbGw9IiMzYjgyZjYiIHJ4PSI0Ii8+PHRleHQgeD0iMzAiIHk9IjI0IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5MT0dPPC90ZXh0Pjwvc3ZnPg==',
  imageWidth: 60,
  imageHeight: 40,
  position: 'top-right',
  offsetX: -10,
  offsetY: 10,
  opacity: 0.9
}

const repeatWatermark = {
  text: 'CONFIDENTIAL',
  fontSize: 20,
  fontColor: 'rgba(255, 255, 255, 0.15)',
  rotation: -30,
  repeat: true,
  spacing: 150
}

// 自定义水印配置
const customWatermark = reactive({
  text: '自定义水印',
  fontSize: 18,
  fontColor: '#ffffff',
  position: 'center',
  opacity: 0.6,
  rotation: -15,
  repeat: false,
  offsetX: 0,
  offsetY: 0
})
</script>

<style scoped>
.watermark-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.watermark-item {
  text-align: center;
}

.watermark-item h4 {
  margin-bottom: 10px;
  color: #333;
}

.watermark-controls {
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.custom-preview {
  margin-top: 20px;
  text-align: center;
}

.custom-preview h4 {
  margin-bottom: 15px;
  color: #333;
}
</style>
```

## 业务场景示例

### 6. 用户头像展示
```vue
<template>
  <div class="example-container">
    <h3>用户头像展示</h3>
    <div class="avatar-examples">
      <!-- 不同尺寸头像 -->
      <div class="avatar-sizes">
        <h4>不同尺寸</h4>
        <div class="size-list">
          <div class="avatar-item">
            <FuniImage
              :src="userAvatar"
              alt="小头像"
              :width="32"
              :height="32"
              radius="50%"
              fit="cover"
              :error-image="defaultAvatar"
            />
            <span>32px</span>
          </div>

          <div class="avatar-item">
            <FuniImage
              :src="userAvatar"
              alt="中头像"
              :width="48"
              :height="48"
              radius="50%"
              fit="cover"
              :error-image="defaultAvatar"
            />
            <span>48px</span>
          </div>

          <div class="avatar-item">
            <FuniImage
              :src="userAvatar"
              alt="大头像"
              :width="64"
              :height="64"
              radius="50%"
              fit="cover"
              :error-image="defaultAvatar"
            />
            <span>64px</span>
          </div>

          <div class="avatar-item">
            <FuniImage
              :src="userAvatar"
              alt="超大头像"
              :width="96"
              :height="96"
              radius="50%"
              fit="cover"
              :error-image="defaultAvatar"
              shadow="hover"
            />
            <span>96px</span>
          </div>
        </div>
      </div>

      <!-- 头像状态 -->
      <div class="avatar-status">
        <h4>头像状态</h4>
        <div class="status-list">
          <div class="status-item">
            <div class="avatar-wrapper online">
              <FuniImage
                :src="userAvatar"
                alt="在线用户"
                :width="60"
                :height="60"
                radius="50%"
                fit="cover"
                :error-image="defaultAvatar"
              />
              <span class="status-dot"></span>
            </div>
            <span>在线</span>
          </div>

          <div class="status-item">
            <div class="avatar-wrapper offline">
              <FuniImage
                :src="userAvatar"
                alt="离线用户"
                :width="60"
                :height="60"
                radius="50%"
                fit="cover"
                :error-image="defaultAvatar"
              />
              <span class="status-dot"></span>
            </div>
            <span>离线</span>
          </div>

          <div class="status-item">
            <div class="avatar-wrapper busy">
              <FuniImage
                :src="userAvatar"
                alt="忙碌用户"
                :width="60"
                :height="60"
                radius="50%"
                fit="cover"
                :error-image="defaultAvatar"
              />
              <span class="status-dot"></span>
            </div>
            <span>忙碌</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const userAvatar = ref('https://picsum.photos/200/200?random=50')
const defaultAvatar = ref('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjxjaXJjbGUgY3g9IjEwMCIgY3k9IjgwIiByPSIzMCIgZmlsbD0iIzk5OSIvPjxwYXRoIGQ9Ik00MCAyMDBjMC00MCAzMC04MCA2MC04MHM2MCA0MCA2MCA4MCIgZmlsbD0iIzk5OSIvPjwvc3ZnPg==')
</script>

<style scoped>
.avatar-examples {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.size-list {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.avatar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.avatar-item span {
  font-size: 12px;
  color: #666;
}

.status-list {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.avatar-wrapper {
  position: relative;
}

.status-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
}

.avatar-wrapper.online .status-dot {
  background-color: #67c23a;
}

.avatar-wrapper.offline .status-dot {
  background-color: #909399;
}

.avatar-wrapper.busy .status-dot {
  background-color: #f56c6c;
}

.status-item span {
  font-size: 12px;
  color: #666;
}
</style>
```

## 注意事项

### 使用建议
1. **图片优化**：使用合适尺寸的图片，避免加载过大的图片
2. **懒加载**：在图片较多的页面使用懒加载提升性能
3. **错误处理**：提供合适的占位图和错误图片
4. **无障碍访问**：为图片提供有意义的alt文本

### 性能优化
1. **图片格式**：优先使用WebP等现代格式
2. **尺寸适配**：根据显示尺寸提供对应的图片
3. **缓存策略**：合理设置图片缓存
4. **预加载**：对重要图片进行预加载

### 用户体验
1. **加载状态**：显示加载进度或占位符
2. **预览功能**：为重要图片提供预览功能
3. **响应式**：确保图片在不同设备上正常显示
4. **交互反馈**：提供适当的悬停和点击效果
