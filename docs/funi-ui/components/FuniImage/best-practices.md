# FuniImage 最佳实践

## 推荐用法和配置

### 1. 图片尺寸和适应方式最佳实践

#### 根据使用场景选择合适的fit模式
```vue
<template>
  <!-- ✅ 推荐：头像使用cover模式保持圆形 -->
  <FuniImage
    :src="userAvatar"
    alt="用户头像"
    :width="64"
    :height="64"
    radius="50%"
    fit="cover"
    :error-image="defaultAvatar"
  />
  
  <!-- ✅ 推荐：产品图片使用contain模式完整显示 -->
  <FuniImage
    :src="productImage"
    alt="产品图片"
    :width="300"
    :height="300"
    fit="contain"
    :radius="8"
    shadow="hover"
  />
  
  <!-- ✅ 推荐：横幅图片使用cover模式填充容器 -->
  <FuniImage
    :src="bannerImage"
    alt="横幅图片"
    width="100%"
    :height="200"
    fit="cover"
    :radius="12"
  />
  
  <!-- ❌ 不推荐：头像使用fill模式会变形 -->
  <FuniImage
    :src="userAvatar"
    alt="用户头像"
    :width="64"
    :height="64"
    radius="50%"
    fit="fill"
  />
</template>

<script setup>
import { ref } from 'vue'

const userAvatar = ref('https://example.com/avatar.jpg')
const productImage = ref('https://example.com/product.jpg')
const bannerImage = ref('https://example.com/banner.jpg')
const defaultAvatar = ref('/default-avatar.png')
</script>
```

#### 响应式尺寸配置
```vue
<template>
  <!-- ✅ 推荐：响应式图片尺寸 -->
  <div class="responsive-image-container">
    <FuniImage
      :src="responsiveImage"
      alt="响应式图片"
      :width="imageWidth"
      :height="imageHeight"
      fit="cover"
      :radius="imageRadius"
      :lazy="true"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const responsiveImage = ref('https://example.com/image.jpg')
const screenWidth = ref(window.innerWidth)

// ✅ 推荐：根据屏幕尺寸动态调整图片大小
const imageWidth = computed(() => {
  if (screenWidth.value < 768) return '100%'
  if (screenWidth.value < 1200) return '400px'
  return '600px'
})

const imageHeight = computed(() => {
  if (screenWidth.value < 768) return '200px'
  if (screenWidth.value < 1200) return '300px'
  return '400px'
})

const imageRadius = computed(() => {
  return screenWidth.value < 768 ? 4 : 8
})

const handleResize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.responsive-image-container {
  width: 100%;
  display: flex;
  justify-content: center;
}
</style>
```

### 2. 性能优化最佳实践

#### 懒加载配置
```vue
<template>
  <div class="performance-examples">
    <!-- ✅ 推荐：图片列表使用懒加载 -->
    <div class="image-list" ref="scrollContainer">
      <FuniImage
        v-for="(image, index) in imageList"
        :key="index"
        :src="image.src"
        :alt="image.alt"
        :width="200"
        :height="150"
        fit="cover"
        :radius="8"
        :lazy="true"
        loading="lazy"
        :scroll-container="scrollContainer"
        :placeholder-image="placeholderImage"
        :error-image="errorImage"
        @load="handleImageLoad(index)"
        @error="handleImageError(index)"
      />
    </div>
    
    <!-- ✅ 推荐：首屏重要图片立即加载 -->
    <FuniImage
      :src="heroImage"
      alt="首屏图片"
      width="100%"
      :height="400"
      fit="cover"
      :lazy="false"
      loading="eager"
      :radius="0"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const scrollContainer = ref()
const imageList = ref(Array.from({ length: 50 }, (_, i) => ({
  src: `https://picsum.photos/200/150?random=${i}`,
  alt: `图片 ${i + 1}`
})))

const heroImage = ref('https://picsum.photos/1200/400?random=hero')

// ✅ 推荐：使用轻量级占位图
const placeholderImage = ref('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veS4rS4uLjwvdGV4dD48L3N2Zz4=')

const errorImage = ref('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmVmMmYyIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2Y1NmM2YyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=')

const handleImageLoad = (index) => {
  console.log(`图片 ${index + 1} 加载成功`)
}

const handleImageError = (index) => {
  console.log(`图片 ${index + 1} 加载失败`)
}
</script>

<style scoped>
.image-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  max-height: 400px;
  overflow-y: auto;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}
</style>
```

#### 图片预加载策略
```javascript
// ✅ 推荐：关键图片预加载
const preloadImages = (imageUrls) => {
  return Promise.all(
    imageUrls.map(url => {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.onload = () => resolve(url)
        img.onerror = () => reject(url)
        img.src = url
      })
    })
  )
}

// 使用示例
const criticalImages = [
  'https://example.com/hero.jpg',
  'https://example.com/logo.png',
  'https://example.com/featured.jpg'
]

// 在组件挂载时预加载关键图片
onMounted(async () => {
  try {
    await preloadImages(criticalImages)
    console.log('关键图片预加载完成')
  } catch (error) {
    console.error('图片预加载失败:', error)
  }
})

// ✅ 推荐：渐进式图片加载
const useProgressiveImage = (lowQualitySrc, highQualitySrc) => {
  const currentSrc = ref(lowQualitySrc)
  const isLoaded = ref(false)
  
  const loadHighQuality = () => {
    const img = new Image()
    img.onload = () => {
      currentSrc.value = highQualitySrc
      isLoaded.value = true
    }
    img.src = highQualitySrc
  }
  
  onMounted(() => {
    loadHighQuality()
  })
  
  return { currentSrc, isLoaded }
}
```

### 3. 用户体验最佳实践

#### 加载状态和错误处理
```vue
<template>
  <div class="ux-examples">
    <!-- ✅ 推荐：提供清晰的加载状态 -->
    <FuniImage
      :src="imageWithStatus"
      alt="带状态的图片"
      :width="300"
      :height="200"
      fit="cover"
      :radius="8"
      @load="handleLoad"
      @error="handleError"
    >
      <template #placeholder>
        <div class="loading-placeholder">
          <el-icon class="loading-icon">
            <Loading />
          </el-icon>
          <p>图片加载中...</p>
          <el-progress 
            v-if="loadingProgress > 0"
            :percentage="loadingProgress" 
            :stroke-width="4"
            :show-text="false"
          />
        </div>
      </template>
      
      <template #error>
        <div class="error-placeholder">
          <el-icon class="error-icon">
            <Picture />
          </el-icon>
          <p>图片加载失败</p>
          <el-button size="small" @click="retryLoad">重试</el-button>
        </div>
      </template>
    </FuniImage>
    
    <!-- ✅ 推荐：图片画廊带预览 -->
    <div class="gallery-section">
      <h4>图片画廊</h4>
      <div class="gallery-grid">
        <FuniImage
          v-for="(image, index) in galleryImages"
          :key="index"
          :src="image.thumb"
          :alt="image.alt"
          :width="150"
          :height="150"
          fit="cover"
          :radius="8"
          shadow="hover"
          :preview-src-list="galleryImages.map(img => img.full)"
          :initial-index="index"
          :z-index="3000"
          class="gallery-item"
          @show="handlePreviewShow"
          @close="handlePreviewClose"
        />
      </div>
      
      <div class="gallery-info">
        <p>{{ galleryImages.length }} 张图片</p>
        <p v-if="currentPreviewIndex >= 0">
          当前预览：第 {{ currentPreviewIndex + 1 }} 张
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Loading, Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const imageWithStatus = ref('https://picsum.photos/300/200?random=status')
const loadingProgress = ref(0)
const currentPreviewIndex = ref(-1)

const galleryImages = ref([
  {
    thumb: 'https://picsum.photos/150/150?random=g1',
    full: 'https://picsum.photos/800/600?random=g1',
    alt: '画廊图片1'
  },
  {
    thumb: 'https://picsum.photos/150/150?random=g2',
    full: 'https://picsum.photos/800/600?random=g2',
    alt: '画廊图片2'
  },
  {
    thumb: 'https://picsum.photos/150/150?random=g3',
    full: 'https://picsum.photos/800/600?random=g3',
    alt: '画廊图片3'
  }
])

// ✅ 推荐：模拟加载进度
const simulateLoadingProgress = () => {
  loadingProgress.value = 0
  const timer = setInterval(() => {
    loadingProgress.value += 10
    if (loadingProgress.value >= 100) {
      clearInterval(timer)
    }
  }, 100)
}

const handleLoad = () => {
  loadingProgress.value = 100
  ElMessage.success('图片加载成功')
}

const handleError = () => {
  loadingProgress.value = 0
  ElMessage.error('图片加载失败')
}

const retryLoad = () => {
  // 重新加载图片
  const timestamp = Date.now()
  imageWithStatus.value = `https://picsum.photos/300/200?random=status&t=${timestamp}`
  simulateLoadingProgress()
}

const handlePreviewShow = () => {
  console.log('预览器打开')
}

const handlePreviewClose = () => {
  currentPreviewIndex.value = -1
  console.log('预览器关闭')
}

// 组件挂载时开始模拟加载
onMounted(() => {
  simulateLoadingProgress()
})
</script>

<style scoped>
.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  padding: 20px;
}

.loading-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #fef0f0;
  color: #f56c6c;
  padding: 20px;
}

.error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.gallery-section {
  margin-top: 30px;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin: 15px 0;
}

.gallery-item {
  cursor: pointer;
  transition: transform 0.2s;
}

.gallery-item:hover {
  transform: scale(1.05);
}

.gallery-info {
  text-align: center;
  color: #666;
  font-size: 14px;
}
</style>
```

## 避免的用法和常见错误

### 1. 性能问题

```vue
<!-- ❌ 错误：大量图片不使用懒加载 -->
<div v-for="image in 1000Images" :key="image.id">
  <FuniImage
    :src="image.src"
    :lazy="false"
    loading="eager"
  />
</div>

<!-- ✅ 正确：大量图片使用懒加载 -->
<div v-for="image in 1000Images" :key="image.id">
  <FuniImage
    :src="image.src"
    :lazy="true"
    loading="lazy"
    :scroll-container="scrollContainer"
  />
</div>
```

### 2. 用户体验问题

```vue
<!-- ❌ 错误：没有提供有意义的alt文本 -->
<FuniImage
  src="product.jpg"
  alt="图片"
/>

<!-- ✅ 正确：提供描述性的alt文本 -->
<FuniImage
  src="product.jpg"
  alt="红色Nike运动鞋，型号Air Max 270"
/>
```

### 3. 样式配置错误

```vue
<!-- ❌ 错误：头像使用不合适的fit模式 -->
<FuniImage
  :src="userAvatar"
  :width="64"
  :height="64"
  radius="50%"
  fit="contain"
/>

<!-- ✅ 正确：头像使用cover模式 -->
<FuniImage
  :src="userAvatar"
  :width="64"
  :height="64"
  radius="50%"
  fit="cover"
/>
```

## 总结

### 核心原则
1. **性能优先**：合理使用懒加载和图片优化
2. **用户体验**：提供清晰的加载状态和错误处理
3. **无障碍访问**：确保所有用户都能访问图片内容
4. **响应式设计**：适配不同屏幕尺寸和设备
5. **内容保护**：合理使用水印保护版权

### 开发建议
1. 根据使用场景选择合适的图片适应模式
2. 为所有图片提供有意义的alt文本
3. 在图片较多的页面使用懒加载
4. 提供合适的占位图和错误处理
5. 考虑图片的版权保护需求
6. 定期优化图片资源和加载策略
