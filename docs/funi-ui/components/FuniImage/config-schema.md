# FuniImage 配置结构定义

## 基础配置结构

### FuniImageProps 接口定义

```typescript
interface FuniImageProps {
  // 基础配置
  src: string
  alt?: string
  fit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'
  
  // 尺寸配置
  width?: string | number
  height?: string | number
  radius?: string | number
  
  // 加载配置
  loading?: 'eager' | 'lazy'
  lazy?: boolean
  scrollContainer?: string | HTMLElement
  
  // 预览配置
  previewSrcList?: string[]
  initialIndex?: number
  zIndex?: number
  hideOnClickModal?: boolean
  closeOnPressEscape?: boolean
  previewTeleported?: boolean
  
  // 样式配置
  border?: boolean
  shadow?: 'always' | 'hover' | 'never'
  
  // 占位和错误处理
  placeholderImage?: string
  loadingImage?: string
  errorImage?: string
  
  // 水印配置
  watermark?: WatermarkConfig
  
  // 工具栏配置
  showToolbar?: boolean
  toolbarConfig?: ToolbarConfig
  zoomable?: boolean
  rotatable?: boolean
  downloadable?: boolean
  copyable?: boolean
  
  // ElementPlus el-image 透传属性
  referrerPolicy?: string
  crossorigin?: string
}
```

### 水印配置接口

```typescript
interface WatermarkConfig {
  // 文字水印
  text?: string
  fontSize?: number
  fontColor?: string
  fontFamily?: string
  fontWeight?: string | number
  
  // 图片水印
  image?: string
  imageWidth?: number
  imageHeight?: number
  
  // 位置配置
  position?: 'top-left' | 'top-center' | 'top-right' | 
            'center-left' | 'center' | 'center-right' |
            'bottom-left' | 'bottom-center' | 'bottom-right'
  offsetX?: number
  offsetY?: number
  
  // 样式配置
  opacity?: number
  rotation?: number
  
  // 重复模式
  repeat?: boolean
  spacing?: number
}
```

### 工具栏配置接口

```typescript
interface ToolbarConfig {
  // 功能开关
  zoom?: boolean
  rotate?: boolean
  download?: boolean
  copy?: boolean
  fullscreen?: boolean
  close?: boolean
  
  // 样式配置
  position?: 'top' | 'bottom'
  background?: string
  color?: string
  size?: 'small' | 'default' | 'large'
  
  // 自定义按钮
  customButtons?: CustomButton[]
}

interface CustomButton {
  icon: string | Component
  text?: string
  action: () => void
  disabled?: boolean
}
```

## 详细配置说明

### 基础配置项

#### src
- **类型**: `string`
- **必填**: ✅
- **说明**: 图片源地址
- **示例**:
  ```javascript
  src: 'https://example.com/image.jpg'
  src: '/static/images/photo.png'
  src: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...'
  ```

#### alt
- **类型**: `string`
- **默认值**: `''`
- **说明**: 图片描述，用于无障碍访问
- **示例**:
  ```javascript
  alt: '产品展示图片'
  alt: '用户头像'
  ```

#### fit
- **类型**: `'fill' | 'contain' | 'cover' | 'none' | 'scale-down'`
- **默认值**: `'cover'`
- **说明**: 图片适应容器的方式
- **示例**:
  ```javascript
  // 填充整个容器，可能变形
  fit: 'fill'
  
  // 保持比例，完整显示
  fit: 'contain'
  
  // 保持比例，填充容器
  fit: 'cover'
  
  // 原始尺寸
  fit: 'none'
  
  // 缩小到合适尺寸
  fit: 'scale-down'
  ```

### 尺寸配置项

#### width / height
- **类型**: `string | number`
- **说明**: 图片容器的宽度和高度
- **示例**:
  ```javascript
  // 数字形式（像素）
  width: 200
  height: 150
  
  // 字符串形式
  width: '200px'
  height: '150px'
  
  // 百分比
  width: '100%'
  height: 'auto'
  
  // CSS单位
  width: '20rem'
  height: '50vh'
  ```

#### radius
- **类型**: `string | number`
- **说明**: 图片圆角半径
- **示例**:
  ```javascript
  // 数字形式
  radius: 8
  
  // 字符串形式
  radius: '8px'
  radius: '50%'  // 圆形
  radius: '12px 12px 0 0'  // 上圆角
  ```

### 加载配置项

#### loading
- **类型**: `'eager' | 'lazy'`
- **默认值**: `'eager'`
- **说明**: 图片加载策略
- **示例**:
  ```javascript
  // 立即加载
  loading: 'eager'
  
  // 懒加载
  loading: 'lazy'
  ```

#### lazy
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否启用懒加载
- **示例**:
  ```javascript
  // 启用懒加载
  lazy: true
  
  // 禁用懒加载
  lazy: false
  ```

#### scrollContainer
- **类型**: `string | HTMLElement`
- **说明**: 懒加载的滚动容器
- **示例**:
  ```javascript
  // CSS选择器
  scrollContainer: '.scroll-container'
  
  // DOM元素
  scrollContainer: document.querySelector('.container')
  
  // ref引用
  scrollContainer: containerRef.value
  ```

### 预览配置项

#### previewSrcList
- **类型**: `string[]`
- **说明**: 预览图片列表
- **示例**:
  ```javascript
  previewSrcList: [
    'https://example.com/image1.jpg',
    'https://example.com/image2.jpg',
    'https://example.com/image3.jpg'
  ]
  ```

#### initialIndex
- **类型**: `number`
- **默认值**: `0`
- **说明**: 预览时的初始图片索引
- **示例**:
  ```javascript
  initialIndex: 0  // 第一张
  initialIndex: 2  // 第三张
  ```

#### zIndex
- **类型**: `number`
- **默认值**: `2000`
- **说明**: 预览层级
- **示例**:
  ```javascript
  zIndex: 3000
  ```

### 样式配置项

#### border
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否显示边框
- **示例**:
  ```javascript
  border: true
  ```

#### shadow
- **类型**: `'always' | 'hover' | 'never'`
- **默认值**: `'never'`
- **说明**: 阴影显示时机
- **示例**:
  ```javascript
  shadow: 'always'  // 始终显示
  shadow: 'hover'   // 悬停时显示
  shadow: 'never'   // 从不显示
  ```

### 水印配置项

#### 文字水印配置
```javascript
const textWatermark = {
  text: '版权所有',
  fontSize: 16,
  fontColor: '#ffffff',
  fontFamily: 'Arial',
  fontWeight: 'bold',
  position: 'bottom-right',
  offsetX: -20,
  offsetY: -20,
  opacity: 0.6,
  rotation: -45
}
```

#### 图片水印配置
```javascript
const imageWatermark = {
  image: '/logo.png',
  imageWidth: 100,
  imageHeight: 50,
  position: 'top-right',
  offsetX: -10,
  offsetY: 10,
  opacity: 0.8
}
```

#### 重复水印配置
```javascript
const repeatWatermark = {
  text: 'CONFIDENTIAL',
  fontSize: 24,
  fontColor: 'rgba(255, 255, 255, 0.1)',
  rotation: -30,
  repeat: true,
  spacing: 200
}
```

## 常用配置组合示例

### 基础图片显示
```javascript
const basicConfig = {
  src: 'https://example.com/image.jpg',
  alt: '示例图片',
  width: 200,
  height: 150,
  fit: 'cover',
  radius: 8
}
```

### 懒加载图片列表
```javascript
const lazyConfig = {
  src: 'https://example.com/image.jpg',
  alt: '懒加载图片',
  lazy: true,
  loading: 'lazy',
  scrollContainer: '.image-container',
  placeholderImage: '/placeholder.jpg',
  errorImage: '/error.jpg'
}
```

### 图片预览画廊
```javascript
const galleryConfig = {
  src: 'https://example.com/thumb.jpg',
  alt: '画廊图片',
  width: 150,
  height: 150,
  fit: 'cover',
  radius: 8,
  shadow: 'hover',
  border: true,
  previewSrcList: [
    'https://example.com/image1.jpg',
    'https://example.com/image2.jpg',
    'https://example.com/image3.jpg'
  ],
  initialIndex: 0,
  zIndex: 3000
}
```

### 带水印的图片
```javascript
const watermarkConfig = {
  src: 'https://example.com/image.jpg',
  alt: '带水印图片',
  width: 400,
  height: 300,
  watermark: {
    text: '版权所有',
    position: 'bottom-right',
    fontSize: 14,
    fontColor: '#ffffff',
    opacity: 0.7,
    offsetX: -15,
    offsetY: -15
  }
}
```

### 带工具栏的图片
```javascript
const toolbarConfig = {
  src: 'https://example.com/image.jpg',
  alt: '可操作图片',
  width: 500,
  height: 400,
  showToolbar: true,
  zoomable: true,
  rotatable: true,
  downloadable: true,
  copyable: true,
  toolbarConfig: {
    position: 'bottom',
    background: 'rgba(0, 0, 0, 0.7)',
    color: '#ffffff',
    zoom: true,
    rotate: true,
    download: true,
    copy: true
  }
}
```

### 响应式图片
```javascript
const responsiveConfig = {
  src: 'https://example.com/image.jpg',
  alt: '响应式图片',
  width: '100%',
  height: 'auto',
  fit: 'contain',
  lazy: true,
  previewSrcList: ['https://example.com/image.jpg']
}
```

## 最佳实践建议

### 1. 性能优化
- 大量图片时使用懒加载
- 提供合适尺寸的缩略图
- 使用WebP等现代图片格式
- 设置合理的预加载策略

### 2. 用户体验
- 提供有意义的alt文本
- 设置加载和错误状态
- 合理使用预览功能
- 支持键盘操作

### 3. 样式设计
- 保持图片比例协调
- 使用一致的圆角和阴影
- 考虑不同屏幕尺寸的适配
- 提供视觉反馈

### 4. 安全考虑
- 验证图片来源
- 处理跨域问题
- 防止XSS攻击
- 合理设置referrer策略

### 5. 水印使用
- 选择合适的水印位置
- 控制水印透明度
- 考虑图片内容的对比度
- 避免影响图片主要内容
