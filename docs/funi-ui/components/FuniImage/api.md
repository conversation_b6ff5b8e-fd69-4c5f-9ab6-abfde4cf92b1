# FuniImage API文档

## 组件概述

FuniImage是基于ElementPlus的el-image封装的图片组件，增加了懒加载、水印、缩放、旋转、裁剪等功能，支持多种图片格式和显示模式，适用于各种图片展示场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| src | String | '' | ✅ | 图片源地址 | el-image.src |
| fit | String | 'cover' | - | 图片适应容器的方式 | el-image.fit |
| hideOnClickModal | Boolean | false | - | 点击遮罩层是否隐藏预览 | el-image.hide-on-click-modal |
| loading | String | 'eager' | - | 图片加载方式 | el-image.loading |
| lazy | Boolean | false | - | 是否开启懒加载 | el-image.lazy |
| scrollContainer | String/HTMLElement | - | - | 懒加载的滚动容器 | el-image.scroll-container |
| alt | String | '' | - | 图片描述 | el-image.alt |
| referrerPolicy | String | '' | - | 原生referrerPolicy | el-image.referrer-policy |
| crossorigin | String | '' | - | 原生crossorigin | el-image.crossorigin |
| previewSrcList | Array | [] | - | 预览图片列表 | el-image.preview-src-list |
| zIndex | Number | 2000 | - | 预览时的z-index | el-image.z-index |
| initialIndex | Number | 0 | - | 预览的初始图片索引 | el-image.initial-index |
| closeOnPressEscape | Boolean | true | - | 是否可通过ESC关闭预览 | el-image.close-on-press-escape |
| previewTeleported | Boolean | false | - | 预览是否插入至body | el-image.preview-teleported |
| width | String/Number | 'auto' | - | 图片宽度 | - |
| height | String/Number | 'auto' | - | 图片高度 | - |
| radius | String/Number | '0' | - | 圆角大小 | - |
| shadow | String | 'never' | - | 阴影效果 | - |
| border | Boolean | false | - | 是否显示边框 | - |
| watermark | String/Object | '' | - | 水印配置 | - |
| placeholder | String | '' | - | 占位图片 | - |
| errorImage | String | '' | - | 错误时显示的图片 | - |
| loadingImage | String | '' | - | 加载中显示的图片 | - |
| showToolbar | Boolean | false | - | 预览时是否显示工具栏 | - |
| toolbarConfig | Object | {} | - | 工具栏配置 | - |
| zoomable | Boolean | false | - | 是否可缩放 | - |
| rotatable | Boolean | false | - | 是否可旋转 | - |
| downloadable | Boolean | false | - | 是否可下载 | - |
| copyable | Boolean | false | - | 是否可复制链接 | - |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| load | event: Event | 图片加载成功事件 | 图片加载完成时 |
| error | event: Event | 图片加载失败事件 | 图片加载失败时 |
| switch | index: Number | 预览图片切换事件 | 预览时切换图片 |
| close | - | 预览关闭事件 | 关闭预览时 |
| show | - | 预览显示事件 | 显示预览时 |
| click | event: Event | 图片点击事件 | 点击图片时 |
| contextmenu | event: Event | 右键菜单事件 | 右键点击图片时 |
| zoom | scale: Number | 缩放事件 | 缩放图片时 |
| rotate | angle: Number | 旋转事件 | 旋转图片时 |
| download | src: String | 下载事件 | 点击下载按钮时 |
| copy | src: String | 复制事件 | 点击复制按钮时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| clickHandler | - | void | 手动触发点击事件 |
| showViewer | - | void | 显示图片预览 |
| closeViewer | - | void | 关闭图片预览 |
| zoomIn | - | void | 放大图片 |
| zoomOut | - | void | 缩小图片 |
| rotateLeft | - | void | 向左旋转 |
| rotateRight | - | void | 向右旋转 |
| reset | - | void | 重置图片状态 |
| download | filename?: String | void | 下载图片 |
| copyUrl | - | Promise | 复制图片链接 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| placeholder | - | 自定义占位内容 |
| error | - | 自定义错误内容 |
| loading | - | 自定义加载内容 |
| toolbar | { zoomIn, zoomOut, rotateLeft, rotateRight, download, copy } | 自定义工具栏 |

## 水印配置

```typescript
interface WatermarkConfig {
  text?: string;                   // 水印文字
  image?: string;                  // 水印图片
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'; // 位置
  opacity?: number;                // 透明度 0-1
  fontSize?: number;               // 字体大小
  fontColor?: string;              // 字体颜色
  fontFamily?: string;             // 字体族
  rotate?: number;                 // 旋转角度
  offsetX?: number;                // X轴偏移
  offsetY?: number;                // Y轴偏移
}
```

## 工具栏配置

```typescript
interface ToolbarConfig {
  zoom?: boolean;                  // 是否显示缩放按钮
  rotate?: boolean;                // 是否显示旋转按钮
  download?: boolean;              // 是否显示下载按钮
  copy?: boolean;                  // 是否显示复制按钮
  fullscreen?: boolean;            // 是否显示全屏按钮
  close?: boolean;                 // 是否显示关闭按钮
  position?: 'top' | 'bottom';     // 工具栏位置
  background?: string;             // 工具栏背景色
}
```

## 使用示例

### 基础图片显示
```vue
<template>
  <div class="image-gallery">
    <FuniImage
      src="https://example.com/image1.jpg"
      alt="示例图片"
      width="200px"
      height="150px"
      fit="cover"
      @load="handleImageLoad"
      @error="handleImageError"
    />
  </div>
</template>

<script setup>
const handleImageLoad = (event) => {
  console.log('图片加载成功:', event)
}

const handleImageError = (event) => {
  console.error('图片加载失败:', event)
}
</script>
```

### 图片预览功能
```vue
<template>
  <div class="preview-gallery">
    <div class="image-grid">
      <FuniImage
        v-for="(image, index) in imageList"
        :key="index"
        :src="image.thumbnail"
        :preview-src-list="previewList"
        :initial-index="index"
        width="150px"
        height="150px"
        fit="cover"
        radius="8px"
        shadow="hover"
        @click="handleImageClick(index)"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const imageList = ref([
  {
    thumbnail: 'https://example.com/thumb1.jpg',
    original: 'https://example.com/image1.jpg'
  },
  {
    thumbnail: 'https://example.com/thumb2.jpg',
    original: 'https://example.com/image2.jpg'
  },
  {
    thumbnail: 'https://example.com/thumb3.jpg',
    original: 'https://example.com/image3.jpg'
  }
])

const previewList = computed(() => {
  return imageList.value.map(item => item.original)
})

const handleImageClick = (index) => {
  console.log('点击图片:', index)
}
</script>

<style scoped>
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}
</style>
```

### 带水印的图片
```vue
<template>
  <div class="watermark-examples">
    <div class="example-item">
      <h4>文字水印</h4>
      <FuniImage
        src="https://example.com/image.jpg"
        width="300px"
        height="200px"
        :watermark="textWatermark"
      />
    </div>
    
    <div class="example-item">
      <h4>图片水印</h4>
      <FuniImage
        src="https://example.com/image.jpg"
        width="300px"
        height="200px"
        :watermark="imageWatermark"
      />
    </div>
    
    <div class="example-item">
      <h4>旋转水印</h4>
      <FuniImage
        src="https://example.com/image.jpg"
        width="300px"
        height="200px"
        :watermark="rotatedWatermark"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const textWatermark = ref({
  text: '版权所有',
  position: 'bottom-right',
  opacity: 0.6,
  fontSize: 16,
  fontColor: '#ffffff',
  offsetX: -20,
  offsetY: -20
})

const imageWatermark = ref({
  image: 'https://example.com/logo.png',
  position: 'top-left',
  opacity: 0.8,
  offsetX: 20,
  offsetY: 20
})

const rotatedWatermark = ref({
  text: 'CONFIDENTIAL',
  position: 'center',
  opacity: 0.3,
  fontSize: 24,
  fontColor: '#ff0000',
  rotate: -45
})
</script>

<style scoped>
.watermark-examples {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.example-item {
  text-align: center;
}

.example-item h4 {
  margin-bottom: 10px;
}
</style>
```

### 懒加载图片列表
```vue
<template>
  <div class="lazy-image-list" ref="scrollContainer">
    <div
      v-for="(image, index) in lazyImageList"
      :key="index"
      class="lazy-image-item"
    >
      <FuniImage
        :src="image.src"
        :alt="image.alt"
        lazy
        :scroll-container="scrollContainer"
        width="100%"
        height="200px"
        fit="cover"
        :placeholder="placeholderImage"
        :loading-image="loadingImage"
        :error-image="errorImage"
        @load="handleLazyLoad(index)"
      />
      <div class="image-info">
        <h4>{{ image.title }}</h4>
        <p>{{ image.description }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const scrollContainer = ref()
const placeholderImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0cHgiIGZpbGw9IiM5OTkiPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+'
const loadingImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0cHgiIGZpbGw9IiM2NjYiPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+'
const errorImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjE0cHgiIGZpbGw9IiNmNTY1NjUiPkVycm9yPC90ZXh0Pjwvc3ZnPg=='

const lazyImageList = ref([])

onMounted(() => {
  // 生成大量图片数据用于演示懒加载
  for (let i = 1; i <= 50; i++) {
    lazyImageList.value.push({
      src: `https://picsum.photos/400/300?random=${i}`,
      alt: `图片 ${i}`,
      title: `图片标题 ${i}`,
      description: `这是第 ${i} 张图片的描述信息`
    })
  }
})

const handleLazyLoad = (index) => {
  console.log(`图片 ${index + 1} 加载完成`)
}
</script>

<style scoped>
.lazy-image-list {
  height: 600px;
  overflow-y: auto;
  padding: 20px;
}

.lazy-image-item {
  margin-bottom: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
}

.image-info {
  padding: 16px;
}

.image-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.image-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}
</style>
```

### 带工具栏的图片预览
```vue
<template>
  <div>
    <FuniImage
      src="https://example.com/large-image.jpg"
      width="400px"
      height="300px"
      show-toolbar
      :toolbar-config="toolbarConfig"
      zoomable
      rotatable
      downloadable
      copyable
      @zoom="handleZoom"
      @rotate="handleRotate"
      @download="handleDownload"
      @copy="handleCopy"
    >
      <template #toolbar="{ zoomIn, zoomOut, rotateLeft, rotateRight, download, copy }">
        <div class="custom-toolbar">
          <el-button-group>
            <el-button size="small" @click="zoomOut">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button size="small" @click="zoomIn">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>
          
          <el-button-group>
            <el-button size="small" @click="rotateLeft">
              <el-icon><RefreshLeft /></el-icon>
            </el-button>
            <el-button size="small" @click="rotateRight">
              <el-icon><RefreshRight /></el-icon>
            </el-button>
          </el-button-group>
          
          <el-button-group>
            <el-button size="small" @click="download">
              <el-icon><Download /></el-icon>
            </el-button>
            <el-button size="small" @click="copy">
              <el-icon><CopyDocument /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </template>
    </FuniImage>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ZoomIn, ZoomOut, RefreshLeft, RefreshRight, Download, CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const toolbarConfig = ref({
  zoom: true,
  rotate: true,
  download: true,
  copy: true,
  position: 'bottom',
  background: 'rgba(0, 0, 0, 0.7)'
})

const handleZoom = (scale) => {
  console.log('缩放比例:', scale)
}

const handleRotate = (angle) => {
  console.log('旋转角度:', angle)
}

const handleDownload = (src) => {
  console.log('下载图片:', src)
  ElMessage.success('开始下载图片')
}

const handleCopy = (src) => {
  console.log('复制链接:', src)
  ElMessage.success('图片链接已复制到剪贴板')
}
</script>

<style scoped>
.custom-toolbar {
  display: flex;
  gap: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
}
</style>
```

### 响应式图片网格
```vue
<template>
  <div class="responsive-gallery">
    <div class="gallery-grid">
      <FuniImage
        v-for="(image, index) in galleryImages"
        :key="index"
        :src="image.src"
        :alt="image.alt"
        :preview-src-list="galleryPreviewList"
        :initial-index="index"
        class="gallery-item"
        fit="cover"
        radius="12px"
        shadow="hover"
        border
        @click="handleGalleryClick(index)"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const galleryImages = ref([
  { src: 'https://picsum.photos/400/300?random=1', alt: '风景1' },
  { src: 'https://picsum.photos/400/300?random=2', alt: '风景2' },
  { src: 'https://picsum.photos/400/300?random=3', alt: '风景3' },
  { src: 'https://picsum.photos/400/300?random=4', alt: '风景4' },
  { src: 'https://picsum.photos/400/300?random=5', alt: '风景5' },
  { src: 'https://picsum.photos/400/300?random=6', alt: '风景6' }
])

const galleryPreviewList = computed(() => {
  return galleryImages.value.map(item => item.src)
})

const handleGalleryClick = (index) => {
  console.log('查看图片:', index)
}
</script>

<style scoped>
.responsive-gallery {
  padding: 20px;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.gallery-item {
  width: 100%;
  height: 200px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: scale(1.05);
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .gallery-item {
    height: 150px;
  }
}
</style>
```

## ElementPlus API支持

FuniImage基于el-image封装，支持所有el-image的API：

```vue
<template>
  <FuniImage
    :src="imageSrc"
    
    <!-- ElementPlus el-image 所有属性 -->
    fit="cover"
    :hide-on-click-modal="false"
    loading="eager"
    :lazy="false"
    :scroll-container="scrollContainer"
    alt="图片描述"
    referrer-policy=""
    crossorigin=""
    :preview-src-list="previewList"
    :z-index="2000"
    :initial-index="0"
    :close-on-press-escape="true"
    :preview-teleported="false"
    
    <!-- ElementPlus el-image 所有事件 -->
    @load="handleLoad"
    @error="handleError"
    @switch="handleSwitch"
    @close="handleClose"
    @show="handleShow"
  />
</template>
```

## 注意事项

### 1. 图片优化
- 使用合适的图片格式和大小
- 提供不同分辨率的图片
- 合理使用懒加载

### 2. 用户体验
- 提供加载状态反馈
- 处理加载失败的情况
- 支持键盘操作

### 3. 性能考虑
- 大量图片时使用虚拟滚动
- 合理设置预加载策略
- 避免同时加载过多图片

### 4. 安全性
- 验证图片来源
- 处理跨域问题
- 防止XSS攻击

## 常见问题

### Q: 如何实现图片的懒加载？
A: 设置lazy属性为true，并指定scroll-container

### Q: 如何自定义图片加载失败时的显示？
A: 使用error插槽或设置error-image属性

### Q: 如何实现图片水印功能？
A: 通过watermark属性配置水印文字或图片

### Q: 如何处理大量图片的性能问题？
A: 使用懒加载、虚拟滚动，合理设置图片尺寸和质量
