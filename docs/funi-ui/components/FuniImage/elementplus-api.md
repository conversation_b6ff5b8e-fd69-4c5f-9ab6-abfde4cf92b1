# FuniImage ElementPlus API 支持

## 基础组件说明

FuniImage 基于 ElementPlus 的 `el-image` 组件封装，在保持原有功能的基础上，增加了水印、工具栏、样式增强等业务功能。

### 核心组件构成
- **el-image**: 基础图片组件，提供核心的图片显示和预览功能
- **水印层**: 自定义水印渲染
- **工具栏**: 图片操作工具栏
- **样式增强**: 圆角、阴影、边框等样式功能

## 支持的 ElementPlus API

### el-image 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| src | ✅ | 直接透传 | 图片源地址 |
| fit | ✅ | 直接透传 | 图片适应容器的方式 |
| alt | ✅ | 直接透传 | 图片描述 |
| referrer-policy | ✅ | 直接透传 | referrer策略 |
| crossorigin | ✅ | 直接透传 | 跨域设置 |
| loading | ✅ | 直接透传 | 加载策略 |
| lazy | ✅ | 直接透传 | 是否懒加载 |
| scroll-container | ✅ | 直接透传 | 懒加载滚动容器 |
| preview-src-list | ✅ | 直接透传 | 预览图片列表 |
| initial-index | ✅ | 直接透传 | 预览初始索引 |
| z-index | ✅ | 直接透传 | 预览层级 |
| hide-on-click-modal | ✅ | 直接透传 | 点击遮罩是否关闭预览 |
| close-on-press-escape | ✅ | 直接透传 | 按ESC是否关闭预览 |
| preview-teleported | ✅ | 直接透传 | 预览是否插入到body |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| load | ✅ | 直接透传 | 图片加载成功事件 |
| error | ✅ | 直接透传 | 图片加载失败事件 |
| switch | ✅ | 直接透传 | 预览切换事件 |
| close | ✅ | 直接透传 | 预览关闭事件 |
| show | ✅ | 直接透传 | 预览显示事件 |

#### Slots 透传支持
| ElementPlus Slot | 支持状态 | 透传方式 | 说明 |
|-----------------|----------|----------|------|
| placeholder | ✅ | 直接透传 | 加载中占位内容 |
| error | ✅ | 直接透传 | 加载失败内容 |
| viewer | ✅ | 直接透传 | 预览器自定义内容 |

#### Methods 透传支持
| ElementPlus Method | 支持状态 | 透传方式 | 说明 |
|-------------------|----------|----------|------|
| clickHandler | ✅ | 内部处理 | 点击处理 |
| closeViewer | ✅ | 内部处理 | 关闭预览 |

## 透传方式说明

### 1. 直接透传
组件属性直接传递给 `el-image` 组件，无需额外处理。

```vue
<template>
  <FuniImage
    src="https://example.com/image.jpg"
    alt="示例图片"
    fit="cover"
    :lazy="true"
    loading="lazy"
    :preview-src-list="previewList"
  />
</template>
```

### 2. 内部处理
组件内部处理后再传递给 ElementPlus 组件。

```vue
<template>
  <FuniImage
    src="https://example.com/image.jpg"
    :width="200"
    :height="150"
    :radius="8"
    shadow="hover"
    @load="handleLoad"
    @error="handleError"
  />
</template>

<script setup>
// width、height、radius、shadow 会被内部处理为样式
// load、error 事件会被透传
const handleLoad = (event) => {
  console.log('图片加载成功:', event)
}

const handleError = (event) => {
  console.log('图片加载失败:', event)
}
</script>
```

### 3. 样式处理
尺寸、圆角、阴影等样式属性会被转换为CSS样式。

```javascript
// 组件内部样式处理逻辑
const imageStyle = computed(() => {
  const style = {}
  
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }
  
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  }
  
  if (props.radius) {
    style.borderRadius = typeof props.radius === 'number' ? `${props.radius}px` : props.radius
  }
  
  return style
})
```

## 使用示例

### 基础透传示例
```vue
<template>
  <div class="example-container">
    <h3>ElementPlus API 透传示例</h3>
    
    <!-- 基础属性透传 -->
    <FuniImage
      src="https://picsum.photos/300/200?random=1"
      alt="基础图片"
      fit="cover"
      :lazy="false"
      loading="eager"
      @load="handleLoad"
      @error="handleError"
    />
    
    <!-- 懒加载配置 -->
    <FuniImage
      src="https://picsum.photos/300/200?random=2"
      alt="懒加载图片"
      fit="contain"
      :lazy="true"
      loading="lazy"
      :scroll-container="scrollContainer"
    />
    
    <!-- 预览配置 -->
    <FuniImage
      src="https://picsum.photos/200/200?random=3"
      alt="预览图片"
      :width="200"
      :height="200"
      fit="cover"
      :preview-src-list="previewList"
      :initial-index="0"
      :z-index="3000"
      :hide-on-click-modal="false"
      :close-on-press-escape="true"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const scrollContainer = ref('.scroll-container')
const previewList = ref([
  'https://picsum.photos/800/600?random=3',
  'https://picsum.photos/800/600?random=4',
  'https://picsum.photos/800/600?random=5'
])

const handleLoad = (event) => {
  console.log('图片加载成功:', event)
}

const handleError = (event) => {
  console.log('图片加载失败:', event)
}
</script>
```

### 自定义插槽示例
```vue
<template>
  <div class="example-container">
    <h3>自定义插槽示例</h3>
    
    <FuniImage
      src="https://picsum.photos/300/200?random=6"
      alt="自定义插槽"
      :width="300"
      :height="200"
      fit="cover"
    >
      <!-- 自定义加载占位符 -->
      <template #placeholder>
        <div class="custom-placeholder">
          <el-icon class="loading-icon">
            <Loading />
          </el-icon>
          <p>图片加载中...</p>
        </div>
      </template>
      
      <!-- 自定义错误内容 -->
      <template #error>
        <div class="custom-error">
          <el-icon class="error-icon">
            <Picture />
          </el-icon>
          <p>图片加载失败</p>
          <el-button size="small" @click="retryLoad">重试</el-button>
        </div>
      </template>
    </FuniImage>
  </div>
</template>

<script setup>
import { Loading, Picture } from '@element-plus/icons-vue'

const retryLoad = () => {
  // 重试加载逻辑
  console.log('重试加载图片')
}
</script>

<style scoped>
.custom-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
}

.loading-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.custom-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #fef0f0;
  color: #f56c6c;
}

.error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
```

### 事件处理示例
```vue
<template>
  <div class="example-container">
    <h3>事件处理示例</h3>
    
    <div class="image-with-events">
      <FuniImage
        :src="currentImageSrc"
        alt="事件处理示例"
        :width="400"
        :height="300"
        fit="cover"
        :preview-src-list="eventPreviewList"
        @load="handleImageLoad"
        @error="handleImageError"
        @switch="handlePreviewSwitch"
        @show="handlePreviewShow"
        @close="handlePreviewClose"
      />
      
      <div class="event-controls">
        <el-button @click="changeImage">切换图片</el-button>
        <el-button @click="triggerError">触发错误</el-button>
        <el-button @click="clearLogs">清空日志</el-button>
      </div>
      
      <div class="event-logs">
        <h4>事件日志：</h4>
        <div class="log-list">
          <div v-for="log in eventLogs" :key="log.id" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-event">{{ log.event }}</span>
            <span class="log-data">{{ log.data }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const currentImageSrc = ref('https://picsum.photos/400/300?random=10')
const eventLogs = ref([])
const imageIndex = ref(10)

const eventPreviewList = ref([
  'https://picsum.photos/800/600?random=10',
  'https://picsum.photos/800/600?random=11',
  'https://picsum.photos/800/600?random=12'
])

const addLog = (event, data = '') => {
  eventLogs.value.unshift({
    id: Date.now(),
    time: new Date().toLocaleTimeString(),
    event,
    data
  })
  
  // 保持日志数量
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10)
  }
}

const handleImageLoad = (event) => {
  addLog('图片加载成功', `尺寸: ${event.target.naturalWidth}x${event.target.naturalHeight}`)
}

const handleImageError = (event) => {
  addLog('图片加载失败', event.target.src)
}

const handlePreviewSwitch = (index) => {
  addLog('预览切换', `切换到第 ${index + 1} 张图片`)
}

const handlePreviewShow = () => {
  addLog('预览显示', '预览器已打开')
}

const handlePreviewClose = () => {
  addLog('预览关闭', '预览器已关闭')
}

const changeImage = () => {
  imageIndex.value++
  currentImageSrc.value = `https://picsum.photos/400/300?random=${imageIndex.value}`
  eventPreviewList.value = [
    `https://picsum.photos/800/600?random=${imageIndex.value}`,
    `https://picsum.photos/800/600?random=${imageIndex.value + 1}`,
    `https://picsum.photos/800/600?random=${imageIndex.value + 2}`
  ]
}

const triggerError = () => {
  currentImageSrc.value = 'https://invalid-url.com/nonexistent.jpg'
}

const clearLogs = () => {
  eventLogs.value = []
}
</script>

<style scoped>
.image-with-events {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.event-controls {
  display: flex;
  gap: 10px;
}

.event-logs {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.log-list {
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  color: #909399;
  width: 80px;
}

.log-event {
  color: #409eff;
  width: 100px;
  font-weight: bold;
}

.log-data {
  color: #67c23a;
  flex: 1;
}
</style>
```

## 注意事项

### 1. API 兼容性
- 确保使用的 ElementPlus 版本支持相应的 API
- 建议使用 ElementPlus 2.0+ 版本以获得最佳兼容性
- 某些新增的 API 可能在旧版本中不可用

### 2. 样式处理
- 组件内部会处理尺寸、圆角、阴影等样式属性
- 这些样式属性不会直接透传给 `el-image`
- 可以通过CSS变量或类名进行样式定制

### 3. 事件处理
- 所有 ElementPlus 原生事件都会直接透传
- 建议使用组件提供的事件处理方式
- 复杂的事件处理逻辑建议在组件外部实现

### 4. 插槽使用
- 支持 ElementPlus 的所有原生插槽
- 可以自定义加载状态和错误状态的显示
- 注意插槽内容的样式和布局

### 5. 性能考虑
- 懒加载功能直接使用 ElementPlus 的实现
- 大量图片时建议启用懒加载
- 合理设置预览图片列表的大小

## 版本兼容性

| FuniImage 版本 | ElementPlus 版本 | 兼容性 |
|---------------|-----------------|--------|
| 1.0.x | 2.0.x | ✅ 完全兼容 |
| 1.0.x | 2.1.x | ✅ 完全兼容 |
| 1.0.x | 2.2.x | ✅ 完全兼容 |
| 1.0.x | 2.3.x | ✅ 完全兼容 |
| 1.0.x | 1.x.x | ⚠️ 部分兼容 |

## 迁移指南

### 从 el-image 迁移
```vue
<!-- 原来的 el-image -->
<el-image
  src="https://example.com/image.jpg"
  fit="cover"
  :lazy="true"
  :preview-src-list="previewList"
  style="width: 200px; height: 150px; border-radius: 8px;"
/>

<!-- 迁移到 FuniImage -->
<FuniImage
  src="https://example.com/image.jpg"
  fit="cover"
  :lazy="true"
  :preview-src-list="previewList"
  :width="200"
  :height="150"
  :radius="8"
/>
```

### 新增功能使用
```vue
<!-- 使用新增的样式和功能 -->
<FuniImage
  src="https://example.com/image.jpg"
  :width="300"
  :height="200"
  :radius="12"
  shadow="hover"
  :border="true"
  :watermark="{ text: '版权所有', position: 'bottom-right' }"
  :show-toolbar="true"
  :zoomable="true"
  :downloadable="true"
/>
```
