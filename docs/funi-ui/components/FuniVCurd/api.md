# FuniVCurd API文档

## 组件概述

FuniVCurd是一个基于ElementPlus el-table封装的虚拟数据表格组件，专门用于处理大数据量的表格展示。它通过虚拟滚动技术优化性能，结合FuniCurdColumn组件实现灵活的列配置和渲染。

## Props

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | Array | `[]` | 表格数据源 |
| rowKey | String \| Function | `'id'` | 行数据的Key，用于优化Table的渲染 |
| columns | Array | `[]` | 表格列配置数组 |

### 详细说明

#### data
- **类型**: `Array`
- **默认值**: `[]`
- **说明**: 表格的数据源，每个元素代表一行数据
- **示例**:
```javascript
const data = [
  { id: 1, name: '张三', age: 25, status: 'active' },
  { id: 2, name: '李四', age: 30, status: 'inactive' }
]
```

#### rowKey
- **类型**: `String | Function`
- **默认值**: `'id'`
- **说明**: 行数据的唯一标识，用于优化表格渲染性能
- **示例**:
```javascript
// 字符串形式
rowKey: 'id'

// 函数形式
rowKey: (row) => row.id
```

#### columns
- **类型**: `Array`
- **默认值**: `[]`
- **说明**: 表格列配置数组，定义表格的列结构和渲染方式
- **配置项**:
  - `prop`: 列绑定的字段名
  - `label`: 列标题
  - `width`: 列宽度
  - `minWidth`: 最小列宽度
  - `maxWidth`: 最大列宽度
  - `hidden`: 是否隐藏列
  - `showOverflowTooltip`: 是否显示溢出提示
  - `slots`: 插槽配置
  - `render`: 自定义渲染函数

## Events

FuniVCurd通过v-bind="$attrs"透传所有ElementPlus el-table的事件。

### 常用事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| row-click | row, column, event | 行点击事件 |
| row-dblclick | row, column, event | 行双击事件 |
| cell-click | row, column, cell, event | 单元格点击事件 |
| selection-change | selection | 选择项变化事件 |
| sort-change | { column, prop, order } | 排序变化事件 |

## Slots

### 基础插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| empty | 空数据时的内容 | - |
| append | 插入至表格最后一行之后的内容 | - |

### 动态插槽

FuniVCurd支持通过columns配置中的slots属性定义动态插槽：

```javascript
const columns = [
  {
    prop: 'status',
    label: '状态',
    slots: {
      default: 'status-slot',
      header: 'status-header-slot'
    }
  }
]
```

对应的模板使用：
```vue
<template>
  <FuniVCurd :columns="columns" :data="data">
    <!-- 自定义单元格内容 -->
    <template #status-slot="{ row, column, $index }">
      <el-tag :type="getStatusType(row.status)">
        {{ row.status }}
      </el-tag>
    </template>
    
    <!-- 自定义表头内容 -->
    <template #status-header-slot="{ column, $index }">
      <i class="el-icon-info"></i>
      {{ column.label }}
    </template>
  </FuniVCurd>
</template>
```

## Methods

FuniVCurd通过ref暴露内部el-table的所有方法：

### 常用方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| clearSelection | - | 清空选择 |
| toggleRowSelection | row, selected | 切换行选择状态 |
| toggleAllSelection | - | 切换全选状态 |
| setCurrentRow | row | 设置当前行 |
| clearSort | - | 清空排序 |
| clearFilter | columnKey | 清空过滤器 |
| doLayout | - | 重新布局表格 |
| sort | prop, order | 手动排序 |

### 使用示例

```vue
<template>
  <FuniVCurd ref="vcurdRef" :columns="columns" :data="data" />
</template>

<script setup>
import { ref } from 'vue'

const vcurdRef = ref()

// 清空选择
const clearSelection = () => {
  vcurdRef.value.clearSelection()
}

// 设置当前行
const setCurrentRow = (row) => {
  vcurdRef.value.setCurrentRow(row)
}
</script>
```

## 列配置详解

### 基础列配置

```javascript
const basicColumn = {
  prop: 'name',           // 字段名
  label: '姓名',          // 列标题
  width: 120,             // 固定宽度
  minWidth: 100,          // 最小宽度
  maxWidth: 200,          // 最大宽度
  align: 'center',        // 对齐方式
  headerAlign: 'center',  // 表头对齐方式
  sortable: true,         // 是否可排序
  resizable: true,        // 是否可调整列宽
  showOverflowTooltip: true // 是否显示溢出提示
}
```

### 隐藏列配置

```javascript
const hiddenColumn = {
  prop: 'secret',
  label: '隐藏列',
  hidden: true  // 隐藏列，宽度会被设置为极小值
}
```

### 自定义渲染列

```javascript
const renderColumn = {
  prop: 'status',
  label: '状态',
  render: ({ row, index }) => {
    return h('el-tag', {
      type: row.status === 'active' ? 'success' : 'danger'
    }, row.status)
  }
}
```

### 插槽列配置

```javascript
const slotColumn = {
  prop: 'actions',
  label: '操作',
  slots: {
    default: 'actions-slot'
  }
}
```

## 性能优化

### 虚拟滚动

FuniVCurd通过设置极小的隐藏列宽度和优化的渲染机制来提升大数据量场景下的性能：

```javascript
// 隐藏列的宽度优化
column.width = column.hidden ? 0.00000001 : column.width
```

### 样式优化

```css
.funi-curd-column__hidden {
  display: none !important;
}
```

## 与其他组件的关系

### FuniCurdColumn

FuniVCurd内部使用FuniCurdColumn组件来渲染每一列：

```vue
<FuniCurdColumn 
  v-for="column in computedColumns" 
  :key="column.prop" 
  :column="column"
>
  <!-- 插槽透传 -->
</FuniCurdColumn>
```

### useRender Hook

使用FuniCurdV2的useRender hook来处理内容渲染：

```javascript
import { useRender } from '@/components/FuniCurdV2/hooks/useRender.jsx'

const { contentRender, defaultRenderCell } = useRender()
```

## 注意事项

1. **数据量**: 适用于大数据量场景，小数据量建议使用FuniCurd或FuniCurdV2
2. **列配置**: 必须提供完整的columns配置，不支持自动列生成
3. **性能**: 隐藏列通过极小宽度实现，避免频繁的DOM操作
4. **兼容性**: 基于ElementPlus el-table，继承其所有特性和限制
