# FuniVCurd 最佳实践

## 使用场景

### 适用场景

1. **大数据量表格**: 数据量超过1000条的表格展示
2. **虚拟滚动需求**: 需要优化渲染性能的场景
3. **简化表格**: 不需要复杂功能，只需基础展示和操作
4. **内存敏感**: 对内存使用有严格要求的应用

### 不适用场景

1. **小数据量**: 少于100条数据，建议使用FuniCurd或FuniCurdV2
2. **复杂功能**: 需要搜索、分页、导出等功能，建议使用FuniCurdV2
3. **实时数据**: 需要频繁更新数据的场景

## 性能优化最佳实践

### 1. 合理配置rowKey

```vue
<template>
  <!-- ✅ 推荐：使用稳定的唯一标识 -->
  <FuniVCurd :row-key="'id'" :data="data" :columns="columns" />
  
  <!-- ✅ 推荐：函数式rowKey，适用于复杂场景 -->
  <FuniVCurd :row-key="getRowKey" :data="data" :columns="columns" />
  
  <!-- ❌ 不推荐：使用索引作为key -->
  <FuniVCurd :row-key="(row, index) => index" :data="data" :columns="columns" />
</template>

<script setup>
// 推荐的rowKey函数
const getRowKey = (row) => {
  return row.id || row.uuid || `${row.name}_${row.timestamp}`
}
</script>
```

### 2. 优化列配置

```javascript
// ✅ 推荐：简化列配置
const optimizedColumns = [
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'name', label: '姓名', width: 120, showOverflowTooltip: true },
  { prop: 'status', label: '状态', width: 100 }
]

// ❌ 不推荐：过于复杂的render函数
const complexColumns = [
  {
    prop: 'data',
    label: '复杂数据',
    render: ({ row }) => {
      // 避免在render中进行复杂计算
      return heavyComputation(row.data)
    }
  }
]

// ✅ 推荐：预处理数据
const processedData = computed(() => {
  return rawData.value.map(item => ({
    ...item,
    processedField: heavyComputation(item.data)
  }))
})
```

### 3. 内存管理

```vue
<template>
  <FuniVCurd
    :data="visibleData"
    :columns="columns"
    :row-key="rowKey"
    height="500px"
  />
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'

// ✅ 推荐：限制内存中的数据量
const pageSize = 1000
const currentPage = ref(0)

const visibleData = computed(() => {
  const start = currentPage.value * pageSize
  const end = start + pageSize
  return allData.value.slice(start, end)
})

// ✅ 推荐：组件销毁时清理数据
onUnmounted(() => {
  allData.value = []
})
</script>
```

## 列配置最佳实践

### 1. 隐藏列的正确使用

```javascript
// ✅ 推荐：动态控制列显示
const columns = computed(() => {
  return baseColumns.map(column => ({
    ...column,
    hidden: !visibleColumns.value.includes(column.prop)
  }))
})

// ✅ 推荐：条件性隐藏列
const conditionalColumns = computed(() => {
  return [
    { prop: 'id', label: 'ID', width: 80 },
    { prop: 'name', label: '姓名', width: 120 },
    // 根据权限隐藏敏感列
    {
      prop: 'salary',
      label: '薪资',
      width: 100,
      hidden: !hasPermission('view_salary')
    }
  ]
})
```

### 2. 插槽的高效使用

```vue
<template>
  <FuniVCurd :data="data" :columns="columns">
    <!-- ✅ 推荐：简单的插槽内容 -->
    <template #status-slot="{ row }">
      <el-tag :type="statusTypeMap[row.status]">
        {{ statusTextMap[row.status] }}
      </el-tag>
    </template>
    
    <!-- ❌ 不推荐：复杂的插槽逻辑 -->
    <template #complex-slot="{ row }">
      <div v-if="complexCondition(row)">
        <!-- 避免在插槽中进行复杂计算 -->
      </div>
    </template>
  </FuniVCurd>
</template>

<script setup>
// ✅ 推荐：预定义映射关系
const statusTypeMap = {
  active: 'success',
  inactive: 'danger',
  pending: 'warning'
}

const statusTextMap = {
  active: '激活',
  inactive: '禁用',
  pending: '待审核'
}
</script>
```

### 3. 自定义渲染优化

```javascript
// ✅ 推荐：简单的render函数
const simpleRenderColumn = {
  prop: 'amount',
  label: '金额',
  render: ({ row }) => {
    return h('span', {
      style: { color: row.amount > 0 ? '#67c23a' : '#f56c6c' }
    }, `¥${row.amount}`)
  }
}

// ❌ 不推荐：复杂的render函数
const complexRenderColumn = {
  prop: 'data',
  label: '复杂数据',
  render: ({ row }) => {
    // 避免在render中进行API调用或复杂计算
    return h('div', [
      h('span', heavyComputation(row)),
      h('button', { onClick: () => apiCall(row.id) }, '操作')
    ])
  }
}
```

## 数据处理最佳实践

### 1. 数据预处理

```javascript
// ✅ 推荐：在数据加载时预处理
const processData = (rawData) => {
  return rawData.map(item => ({
    ...item,
    // 预计算显示用的字段
    displayName: `${item.firstName} ${item.lastName}`,
    statusText: getStatusText(item.status),
    formattedDate: formatDate(item.createTime)
  }))
}

// ✅ 推荐：使用computed进行响应式处理
const processedData = computed(() => {
  return rawData.value.map(processData)
})
```

### 2. 大数据量处理

```vue
<template>
  <div class="large-data-container">
    <div class="data-info">
      总数据量: {{ totalCount }} | 当前显示: {{ visibleData.length }}
    </div>
    
    <FuniVCurd
      :data="visibleData"
      :columns="columns"
      :row-key="rowKey"
      height="600px"
      @scroll="handleScroll"
    />
    
    <div class="load-more" v-if="hasMore">
      <el-button @click="loadMore" :loading="loading">加载更多</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const pageSize = 500
const currentPage = ref(0)
const allData = ref([])
const loading = ref(false)

// 虚拟分页
const visibleData = computed(() => {
  const start = 0
  const end = (currentPage.value + 1) * pageSize
  return allData.value.slice(start, end)
})

const hasMore = computed(() => {
  return visibleData.value.length < totalCount.value
})

// 加载更多数据
const loadMore = async () => {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  try {
    const newData = await fetchData(currentPage.value + 1, pageSize)
    allData.value.push(...newData)
    currentPage.value++
  } finally {
    loading.value = false
  }
}

// 滚动加载
const handleScroll = (event) => {
  const { scrollTop, scrollHeight, clientHeight } = event.target
  if (scrollTop + clientHeight >= scrollHeight - 100) {
    loadMore()
  }
}
</script>
```

## 事件处理最佳实践

### 1. 高效的事件处理

```vue
<template>
  <FuniVCurd
    :data="data"
    :columns="columns"
    @row-click="handleRowClick"
    @selection-change="handleSelectionChange"
  />
</template>

<script setup>
// ✅ 推荐：使用防抖处理频繁事件
import { debounce } from 'lodash-es'

const handleRowClick = debounce((row, column, event) => {
  console.log('行点击:', row)
}, 300)

// ✅ 推荐：批量处理选择变化
const handleSelectionChange = (selection) => {
  // 批量更新选择状态
  selectedIds.value = selection.map(row => row.id)
}
</script>
```

### 2. 内存泄漏防护

```vue
<script setup>
import { ref, onUnmounted } from 'vue'

const timers = []
const eventListeners = []

// ✅ 推荐：清理定时器和事件监听器
onUnmounted(() => {
  timers.forEach(timer => clearTimeout(timer))
  eventListeners.forEach(({ element, event, handler }) => {
    element.removeEventListener(event, handler)
  })
})
</script>
```

## 样式和主题最佳实践

### 1. 响应式设计

```vue
<template>
  <FuniVCurd
    :data="data"
    :columns="responsiveColumns"
    :row-key="rowKey"
    class="responsive-table"
  />
</template>

<script setup>
import { computed } from 'vue'

// ✅ 推荐：响应式列配置
const responsiveColumns = computed(() => {
  const isMobile = window.innerWidth < 768
  
  if (isMobile) {
    return [
      { prop: 'name', label: '姓名', width: 120 },
      { prop: 'status', label: '状态', width: 80 }
    ]
  }
  
  return [
    { prop: 'id', label: 'ID', width: 80 },
    { prop: 'name', label: '姓名', width: 120 },
    { prop: 'email', label: '邮箱', width: 200 },
    { prop: 'status', label: '状态', width: 100 }
  ]
})
</script>

<style scoped>
.responsive-table {
  width: 100%;
}

@media (max-width: 768px) {
  .responsive-table :deep(.el-table__cell) {
    padding: 8px 4px;
  }
}
</style>
```

### 2. 主题定制

```css
/* ✅ 推荐：使用CSS变量进行主题定制 */
.funi-vcurd-theme {
  --el-table-border-color: #e4e7ed;
  --el-table-bg-color: #ffffff;
  --el-table-tr-bg-color: #fafafa;
  --el-table-header-bg-color: #f5f7fa;
}

/* 隐藏列样式 */
.funi-curd-column__hidden {
  display: none !important;
}

/* 性能优化：减少重绘 */
.funi-vcurd-optimized {
  contain: layout style paint;
}
```

## 错误处理和调试

### 1. 错误边界

```vue
<template>
  <div class="vcurd-wrapper">
    <FuniVCurd
      v-if="!hasError"
      :data="data"
      :columns="columns"
      :row-key="rowKey"
      @error="handleError"
    />
    
    <div v-else class="error-fallback">
      <p>表格加载失败，请刷新重试</p>
      <el-button @click="retry">重试</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const hasError = ref(false)

const handleError = (error) => {
  console.error('FuniVCurd Error:', error)
  hasError.value = true
}

const retry = () => {
  hasError.value = false
  // 重新加载数据
}
</script>
```

### 2. 性能监控

```javascript
// ✅ 推荐：性能监控
const performanceMonitor = {
  startTime: 0,
  
  start() {
    this.startTime = performance.now()
  },
  
  end(operation) {
    const duration = performance.now() - this.startTime
    if (duration > 100) {
      console.warn(`FuniVCurd ${operation} took ${duration}ms`)
    }
  }
}

// 在数据更新时监控性能
watch(data, () => {
  performanceMonitor.start()
  nextTick(() => {
    performanceMonitor.end('data update')
  })
})
```

## 常见问题和解决方案

### 1. 数据更新不及时

```javascript
// ❌ 问题：直接修改数组元素
data.value[0].name = 'new name'

// ✅ 解决：使用响应式更新
data.value = data.value.map(item => 
  item.id === targetId ? { ...item, name: 'new name' } : item
)
```

### 2. 内存占用过高

```javascript
// ✅ 解决：实现数据分页和清理
const MAX_CACHE_SIZE = 5000

const addData = (newData) => {
  data.value.push(...newData)
  
  // 限制缓存大小
  if (data.value.length > MAX_CACHE_SIZE) {
    data.value = data.value.slice(-MAX_CACHE_SIZE)
  }
}
```

### 3. 列宽度问题

```javascript
// ✅ 解决：合理设置列宽
const columns = [
  { prop: 'id', label: 'ID', width: 80 },           // 固定宽度
  { prop: 'name', label: '姓名', minWidth: 120 },    // 最小宽度
  { prop: 'desc', label: '描述' },                   // 自适应宽度
  { prop: 'actions', label: '操作', width: 150, fixed: 'right' } // 固定列
]
```
