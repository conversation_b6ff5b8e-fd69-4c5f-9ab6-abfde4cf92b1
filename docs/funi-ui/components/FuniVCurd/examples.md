# FuniVCurd 使用示例

## 基础示例

### 简单虚拟表格

```vue
<template>
  <FuniVCurd
    :data="tableData"
    :columns="columns"
    :row-key="rowKey"
  />
</template>

<script setup>
import { ref } from 'vue'

const rowKey = 'id'

const columns = ref([
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'age', label: '年龄', width: 80 },
  { prop: 'address', label: '地址', minWidth: 200 }
])

const tableData = ref([
  { id: 1, name: '张三', age: 25, address: '北京市朝阳区' },
  { id: 2, name: '李四', age: 30, address: '上海市浦东新区' },
  { id: 3, name: '王五', age: 28, address: '广州市天河区' }
])
</script>
```

### 大数据量虚拟表格

```vue
<template>
  <div class="large-data-demo">
    <div class="demo-header">
      <span>数据量：{{ tableData.length }} 条</span>
      <el-button @click="generateLargeData">生成10000条数据</el-button>
    </div>
    
    <FuniVCurd
      :data="tableData"
      :columns="columns"
      :row-key="rowKey"
      height="500px"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const rowKey = 'id'

const columns = ref([
  { prop: 'id', label: 'ID', width: 80, fixed: 'left' },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'department', label: '部门', width: 150 },
  { prop: 'position', label: '职位', width: 120 },
  { prop: 'salary', label: '薪资', width: 100, align: 'right' },
  { prop: 'email', label: '邮箱', minWidth: 200 },
  { prop: 'phone', label: '电话', width: 130 },
  { prop: 'address', label: '地址', minWidth: 250 }
])

const tableData = ref([])

// 生成大量数据
const generateLargeData = () => {
  const departments = ['技术部', '产品部', '运营部', '市场部', '人事部']
  const positions = ['工程师', '经理', '专员', '总监', '助理']
  
  const data = []
  for (let i = 1; i <= 10000; i++) {
    data.push({
      id: i,
      name: `用户${i}`,
      department: departments[Math.floor(Math.random() * departments.length)],
      position: positions[Math.floor(Math.random() * positions.length)],
      salary: Math.floor(Math.random() * 50000) + 5000,
      email: `user${i}@example.com`,
      phone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      address: `地址${i}`
    })
  }
  tableData.value = data
}

// 初始化数据
generateLargeData()
</script>

<style scoped>
.large-data-demo {
  padding: 20px;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
```

## 高级示例

### 带插槽的虚拟表格

```vue
<template>
  <FuniVCurd
    :data="tableData"
    :columns="columns"
    :row-key="rowKey"
    @row-click="handleRowClick"
    @selection-change="handleSelectionChange"
  >
    <!-- 状态列插槽 -->
    <template #status-slot="{ row }">
      <el-tag :type="getStatusType(row.status)">
        {{ getStatusText(row.status) }}
      </el-tag>
    </template>
    
    <!-- 头像列插槽 -->
    <template #avatar-slot="{ row }">
      <el-avatar :src="row.avatar" :size="32">
        {{ row.name.charAt(0) }}
      </el-avatar>
    </template>
    
    <!-- 操作列插槽 -->
    <template #actions-slot="{ row, $index }">
      <el-button size="small" type="primary" @click="handleEdit(row)">
        编辑
      </el-button>
      <el-button size="small" type="danger" @click="handleDelete(row, $index)">
        删除
      </el-button>
    </template>
    
    <!-- 空数据插槽 -->
    <template #empty>
      <div class="custom-empty">
        <el-icon size="48"><Document /></el-icon>
        <p>暂无数据</p>
      </div>
    </template>
  </FuniVCurd>
</template>

<script setup>
import { ref } from 'vue'
import { Document } from '@element-plus/icons-vue'

const rowKey = 'id'

const columns = ref([
  { type: 'selection', width: 55, fixed: 'left' },
  { type: 'index', label: '序号', width: 80, fixed: 'left' },
  {
    prop: 'avatar',
    label: '头像',
    width: 80,
    slots: { default: 'avatar-slot' }
  },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'email', label: '邮箱', width: 200 },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slots: { default: 'status-slot' }
  },
  {
    label: '操作',
    width: 150,
    fixed: 'right',
    slots: { default: 'actions-slot' }
  }
])

const tableData = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    status: 'active',
    avatar: 'https://example.com/avatar1.jpg'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    status: 'inactive',
    avatar: 'https://example.com/avatar2.jpg'
  }
])

// 状态类型映射
const getStatusType = (status) => {
  const typeMap = {
    active: 'success',
    inactive: 'danger',
    pending: 'warning'
  }
  return typeMap[status] || 'info'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    active: '激活',
    inactive: '禁用',
    pending: '待审核'
  }
  return textMap[status] || '未知'
}

// 事件处理
const handleRowClick = (row, column, event) => {
  console.log('行点击:', row)
}

const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

const handleEdit = (row) => {
  console.log('编辑:', row)
}

const handleDelete = (row, index) => {
  console.log('删除:', row, index)
}
</script>

<style scoped>
.custom-empty {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.custom-empty p {
  margin-top: 16px;
  font-size: 14px;
}
</style>
```

### 自定义渲染虚拟表格

```vue
<template>
  <FuniVCurd
    :data="tableData"
    :columns="columns"
    :row-key="rowKey"
    stripe
    border
  />
</template>

<script setup>
import { ref, h } from 'vue'

const rowKey = 'id'

const columns = ref([
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'name', label: '姓名', width: 120 },
  {
    prop: 'progress',
    label: '进度',
    width: 200,
    render: ({ row }) => {
      return h('el-progress', {
        percentage: row.progress,
        strokeWidth: 8,
        textInside: true,
        status: row.progress === 100 ? 'success' : undefined
      })
    }
  },
  {
    prop: 'rating',
    label: '评分',
    width: 150,
    render: ({ row }) => {
      return h('el-rate', {
        modelValue: row.rating,
        disabled: true,
        showText: true,
        textColor: '#ff9900'
      })
    }
  },
  {
    prop: 'tags',
    label: '标签',
    width: 200,
    render: ({ row }) => {
      return row.tags.map(tag => 
        h('el-tag', {
          key: tag,
          size: 'small',
          style: { marginRight: '5px' }
        }, tag)
      )
    }
  },
  {
    prop: 'amount',
    label: '金额',
    width: 120,
    align: 'right',
    render: ({ row }) => {
      const color = row.amount > 0 ? '#67c23a' : '#f56c6c'
      return h('span', {
        style: { color, fontWeight: 'bold' }
      }, `¥${row.amount.toLocaleString()}`)
    }
  }
])

const tableData = ref([
  {
    id: 1,
    name: '项目A',
    progress: 75,
    rating: 4,
    tags: ['重要', '紧急'],
    amount: 12500
  },
  {
    id: 2,
    name: '项目B',
    progress: 100,
    rating: 5,
    tags: ['已完成'],
    amount: -3200
  },
  {
    id: 3,
    name: '项目C',
    progress: 30,
    rating: 3,
    tags: ['进行中', '延期'],
    amount: 8900
  }
])
</script>
```

### 隐藏列功能示例

```vue
<template>
  <div class="hidden-column-demo">
    <div class="demo-controls">
      <el-checkbox-group v-model="visibleColumns" @change="updateColumns">
        <el-checkbox label="id">ID</el-checkbox>
        <el-checkbox label="name">姓名</el-checkbox>
        <el-checkbox label="email">邮箱</el-checkbox>
        <el-checkbox label="phone">电话</el-checkbox>
        <el-checkbox label="address">地址</el-checkbox>
      </el-checkbox-group>
    </div>
    
    <FuniVCurd
      :data="tableData"
      :columns="computedColumns"
      :row-key="rowKey"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const rowKey = 'id'
const visibleColumns = ref(['id', 'name', 'email', 'phone'])

const baseColumns = [
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'email', label: '邮箱', width: 200 },
  { prop: 'phone', label: '电话', width: 130 },
  { prop: 'address', label: '地址', minWidth: 200 }
]

const computedColumns = computed(() => {
  return baseColumns.map(column => ({
    ...column,
    hidden: !visibleColumns.value.includes(column.prop)
  }))
})

const tableData = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    address: '北京市朝阳区'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    address: '上海市浦东新区'
  }
])

const updateColumns = () => {
  // 列显示/隐藏变化时的处理逻辑
  console.log('可见列:', visibleColumns.value)
}
</script>

<style scoped>
.hidden-column-demo {
  padding: 20px;
}

.demo-controls {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
```

## 性能优化示例

### 函数式rowKey

```vue
<template>
  <FuniVCurd
    :data="tableData"
    :columns="columns"
    :row-key="getRowKey"
  />
</template>

<script setup>
import { ref } from 'vue'

const columns = ref([
  { prop: 'uuid', label: 'UUID', width: 200 },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'timestamp', label: '时间戳', width: 150 }
])

const tableData = ref([
  {
    uuid: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    name: '张三',
    timestamp: Date.now()
  }
])

// 使用函数式rowKey提升性能
const getRowKey = (row) => {
  return row.uuid || row.id || JSON.stringify(row)
}
</script>
```

### 优化大数据渲染

```vue
<template>
  <FuniVCurd
    :data="tableData"
    :columns="optimizedColumns"
    :row-key="rowKey"
    height="600px"
  />
</template>

<script setup>
import { ref, computed } from 'vue'

const rowKey = 'id'

// 优化列配置，减少不必要的计算
const optimizedColumns = computed(() => [
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'name', label: '姓名', width: 120, showOverflowTooltip: true },
  { prop: 'status', label: '状态', width: 100 },
  // 对于大数据量，避免复杂的render函数
  { prop: 'simpleField', label: '简单字段', width: 150 }
])

// 大数据集
const tableData = ref(
  Array.from({ length: 50000 }, (_, index) => ({
    id: index + 1,
    name: `用户${index + 1}`,
    status: index % 2 === 0 ? 'active' : 'inactive',
    simpleField: `数据${index + 1}`
  }))
)
</script>
```
