# FuniVCurd 配置结构定义

## 组件配置接口

### IFuniVCurdProps

```typescript
interface IFuniVCurdProps {
  /** 表格数据源 */
  data?: Array<Record<string, any>>
  /** 行数据的Key，用于优化Table的渲染 */
  rowKey?: string | ((row: Record<string, any>) => string)
  /** 表格列配置数组 */
  columns?: IVCurdColumn[]
}
```

## 列配置接口

### IVCurdColumn

```typescript
interface IVCurdColumn {
  /** 列绑定的字段名 */
  prop?: string
  /** 列标题 */
  label?: string
  /** 列宽度 */
  width?: number | string
  /** 最小列宽度 */
  minWidth?: number | string
  /** 最大列宽度 */
  maxWidth?: number | string
  /** 是否隐藏列 */
  hidden?: boolean
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 表头对齐方式 */
  headerAlign?: 'left' | 'center' | 'right'
  /** 是否可排序 */
  sortable?: boolean | 'custom'
  /** 是否可调整列宽 */
  resizable?: boolean
  /** 是否显示溢出提示 */
  showOverflowTooltip?: boolean
  /** 列的className */
  className?: string
  /** 表头的className */
  labelClassName?: string
  /** 列的样式 */
  style?: Record<string, any>
  /** 插槽配置 */
  slots?: IVCurdColumnSlots
  /** 自定义渲染函数 */
  render?: (params: IRenderParams) => VNode | string
  /** 子列配置（多级表头） */
  children?: IVCurdColumn[]
  /** 列类型 */
  type?: 'selection' | 'index' | 'expand'
  /** 格式化函数 */
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  /** 是否固定列 */
  fixed?: boolean | 'left' | 'right'
}
```

### IVCurdColumnSlots

```typescript
interface IVCurdColumnSlots {
  /** 自定义表头插槽名 */
  header?: string
  /** 自定义内容插槽名 */
  default?: string
}
```

### IRenderParams

```typescript
interface IRenderParams {
  /** 当前行数据 */
  row: Record<string, any>
  /** 行索引 */
  index: number
  /** 列配置 */
  column?: IVCurdColumn
}
```

## 配置示例

### 基础配置

```typescript
const basicConfig: IFuniVCurdProps = {
  data: [
    { id: 1, name: '张三', age: 25, status: 'active' },
    { id: 2, name: '李四', age: 30, status: 'inactive' }
  ],
  rowKey: 'id',
  columns: [
    { prop: 'id', label: 'ID', width: 80 },
    { prop: 'name', label: '姓名', width: 120 },
    { prop: 'age', label: '年龄', width: 80 },
    { prop: 'status', label: '状态', width: 100 }
  ]
}
```

### 高级配置

```typescript
const advancedConfig: IFuniVCurdProps = {
  data: [],
  rowKey: (row) => row.id,
  columns: [
    {
      type: 'selection',
      width: 55,
      fixed: 'left'
    },
    {
      type: 'index',
      label: '序号',
      width: 80,
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '姓名',
      width: 120,
      showOverflowTooltip: true,
      sortable: true
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      slots: {
        default: 'status-slot'
      }
    },
    {
      prop: 'avatar',
      label: '头像',
      width: 80,
      render: ({ row }) => {
        return h('el-avatar', {
          src: row.avatar,
          size: 'small'
        })
      }
    },
    {
      label: '操作',
      width: 200,
      fixed: 'right',
      slots: {
        default: 'actions-slot'
      }
    }
  ]
}
```

### 隐藏列配置

```typescript
const hiddenColumnConfig: IVCurdColumn = {
  prop: 'secretData',
  label: '隐藏数据',
  hidden: true,  // 隐藏列
  width: 0       // 宽度会被自动设置为极小值
}
```

### 多级表头配置

```typescript
const multiLevelConfig: IVCurdColumn[] = [
  {
    label: '基本信息',
    children: [
      { prop: 'name', label: '姓名', width: 120 },
      { prop: 'age', label: '年龄', width: 80 }
    ]
  },
  {
    label: '联系方式',
    children: [
      { prop: 'phone', label: '电话', width: 120 },
      { prop: 'email', label: '邮箱', width: 180 }
    ]
  }
]
```

### 自定义渲染配置

```typescript
const renderConfig: IVCurdColumn = {
  prop: 'status',
  label: '状态',
  width: 100,
  render: ({ row, index }) => {
    const statusMap = {
      active: { type: 'success', text: '激活' },
      inactive: { type: 'danger', text: '禁用' }
    }
    const config = statusMap[row.status] || { type: 'info', text: '未知' }
    
    return h('el-tag', {
      type: config.type
    }, config.text)
  }
}
```

## 样式配置

### 列样式配置

```typescript
const styledColumn: IVCurdColumn = {
  prop: 'amount',
  label: '金额',
  width: 120,
  align: 'right',
  headerAlign: 'center',
  className: 'amount-column',
  labelClassName: 'amount-header',
  style: {
    color: '#f56c6c',
    fontWeight: 'bold'
  }
}
```

### CSS类名定义

```css
/* 隐藏列样式 */
.funi-curd-column__hidden {
  display: none !important;
}

/* 自定义列样式 */
.amount-column {
  color: #f56c6c;
  font-weight: bold;
}

.amount-header {
  background-color: #f5f7fa;
}
```

## 事件配置

### 事件处理器类型

```typescript
interface IVCurdEvents {
  /** 行点击事件 */
  onRowClick?: (row: any, column: any, event: Event) => void
  /** 行双击事件 */
  onRowDblclick?: (row: any, column: any, event: Event) => void
  /** 单元格点击事件 */
  onCellClick?: (row: any, column: any, cell: any, event: Event) => void
  /** 选择项变化事件 */
  onSelectionChange?: (selection: any[]) => void
  /** 排序变化事件 */
  onSortChange?: (params: { column: any, prop: string, order: string }) => void
}
```

## 性能配置

### 虚拟滚动优化

```typescript
interface IVirtualScrollConfig {
  /** 是否启用虚拟滚动 */
  enabled: boolean
  /** 每行高度 */
  itemHeight: number
  /** 缓冲区大小 */
  bufferSize: number
  /** 阈值，超过此数量启用虚拟滚动 */
  threshold: number
}

const virtualConfig: IVirtualScrollConfig = {
  enabled: true,
  itemHeight: 50,
  bufferSize: 10,
  threshold: 100
}
```

## 默认配置

```typescript
const defaultConfig: Required<IFuniVCurdProps> = {
  data: [],
  rowKey: 'id',
  columns: []
}

const defaultColumnConfig: Partial<IVCurdColumn> = {
  showOverflowTooltip: true,
  align: 'left',
  headerAlign: 'left',
  resizable: true,
  sortable: false,
  hidden: false
}
```

## 配置验证

### 必需配置检查

```typescript
function validateConfig(config: IFuniVCurdProps): boolean {
  // 检查必需的配置项
  if (!Array.isArray(config.data)) {
    console.warn('FuniVCurd: data must be an array')
    return false
  }
  
  if (!Array.isArray(config.columns)) {
    console.warn('FuniVCurd: columns must be an array')
    return false
  }
  
  return true
}
```

### 列配置验证

```typescript
function validateColumn(column: IVCurdColumn): boolean {
  if (column.prop && typeof column.prop !== 'string') {
    console.warn('FuniVCurd: column.prop must be a string')
    return false
  }
  
  if (column.width && typeof column.width !== 'number' && typeof column.width !== 'string') {
    console.warn('FuniVCurd: column.width must be a number or string')
    return false
  }
  
  return true
}
```
