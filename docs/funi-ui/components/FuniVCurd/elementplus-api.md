# FuniVCurd ElementPlus API 支持

## 基础组件说明

FuniVCurd 是基于 ElementPlus 的 `el-table` 组件进行二次封装的虚拟数据表格组件。它通过 `v-bind="tableAttrs"` 的方式透传核心表格属性，并结合 FuniCurdColumn 组件实现灵活的列渲染。

### 架构设计

```vue
<!-- FuniVCurd 内部结构 -->
<el-table ref="curdRef" v-bind="tableAttrs">
  <FuniCurdColumn 
    v-for="column in computedColumns" 
    :key="column.prop" 
    :column="column"
  >
    <!-- 插槽透传 -->
  </FuniCurdColumn>
</el-table>
```

## 支持的 ElementPlus el-table API

### 基础属性透传

| 属性名 | 类型 | 说明 | 透传方式 |
|--------|------|------|---------|
| data | Array | 表格数据 | 直接透传 |
| row-key | String/Function | 行数据的Key | 直接透传 |
| height | String/Number | 表格高度 | v-bind透传 |
| max-height | String/Number | 表格最大高度 | v-bind透传 |
| stripe | Boolean | 是否为斑马纹表格 | v-bind透传 |
| border | Boolean | 是否带有纵向边框 | v-bind透传 |
| size | String | 表格尺寸 | v-bind透传 |
| fit | Boolean | 列的宽度是否自撑开 | v-bind透传 |
| show-header | Boolean | 是否显示表头 | v-bind透传 |
| highlight-current-row | Boolean | 是否要高亮当前行 | v-bind透传 |
| current-row-key | String/Number | 当前行的key | v-bind透传 |
| row-class-name | String/Function | 行的className | v-bind透传 |
| row-style | Object/Function | 行的style | v-bind透传 |
| cell-class-name | String/Function | 单元格的className | v-bind透传 |
| cell-style | Object/Function | 单元格的style | v-bind透传 |
| header-row-class-name | String/Function | 表头行的className | v-bind透传 |
| header-row-style | Object/Function | 表头行的style | v-bind透传 |
| header-cell-class-name | String/Function | 表头单元格的className | v-bind透传 |
| header-cell-style | Object/Function | 表头单元格的style | v-bind透传 |

### 排序相关属性

| 属性名 | 类型 | 说明 | 透传方式 |
|--------|------|------|---------|
| default-sort | Object | 默认排序列和排序顺序 | v-bind透传 |
| sort-method | Function | 对数据进行排序的时候使用的方法 | v-bind透传 |
| sort-by | String/Array/Function | 指定数据按照哪个属性进行排序 | v-bind透传 |

### 选择相关属性

| 属性名 | 类型 | 说明 | 透传方式 |
|--------|------|------|---------|
| row-selection | Object | 行选择配置 | v-bind透传 |
| select-on-indeterminate | Boolean | 在多选表格中，当仅有部分行被选中时，点击表头的多选框时的行为 | v-bind透传 |

### 展开行相关属性

| 属性名 | 类型 | 说明 | 透传方式 |
|--------|------|------|---------|
| expand-row-keys | Array | 可以通过该属性设置Table目前的展开行 | v-bind透传 |
| default-expand-all | Boolean | 是否默认展开所有行 | v-bind透传 |

### 树形数据相关属性

| 属性名 | 类型 | 说明 | 透传方式 |
|--------|------|------|---------|
| tree-props | Object | 渲染嵌套数据的配置选项 | v-bind透传 |
| lazy | Boolean | 是否懒加载子节点数据 | v-bind透传 |
| load | Function | 加载子节点数据的函数 | v-bind透传 |
| indent | Number | 相邻级节点间的水平缩进 | v-bind透传 |

## 支持的 ElementPlus el-table 事件

### 基础事件透传

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| select | selection, row | 当用户手动勾选数据行的Checkbox时触发 | @select |
| select-all | selection | 当用户手动勾选全选Checkbox时触发 | @select-all |
| selection-change | selection | 当选择项发生变化时会触发该事件 | @selection-change |
| cell-mouse-enter | row, column, cell, event | 当单元格hover进入时会触发该事件 | @cell-mouse-enter |
| cell-mouse-leave | row, column, cell, event | 当单元格hover退出时会触发该事件 | @cell-mouse-leave |
| cell-click | row, column, cell, event | 当某个单元格被点击时会触发该事件 | @cell-click |
| cell-dblclick | row, column, cell, event | 当某个单元格被双击击时会触发该事件 | @cell-dblclick |
| row-click | row, column, event | 当某一行被点击时会触发该事件 | @row-click |
| row-contextmenu | row, column, event | 当某一行被鼠标右键点击时会触发该事件 | @row-contextmenu |
| row-dblclick | row, column, event | 当某一行被双击时会触发该事件 | @row-dblclick |
| header-click | column, event | 当某一列的表头被点击时会触发该事件 | @header-click |
| header-contextmenu | column, event | 当某一列的表头被鼠标右键点击时触发该事件 | @header-contextmenu |
| sort-change | { column, prop, order } | 当表格的排序条件发生变化的时候会触发该事件 | @sort-change |
| filter-change | filters | 当表格的筛选条件发生变化的时候会触发该事件 | @filter-change |
| current-change | currentRow, oldCurrentRow | 当表格的当前行发生变化的时候会触发该事件 | @current-change |
| header-dragend | newWidth, oldWidth, column, event | 当拖动表头改变了列的宽度的时候会触发该事件 | @header-dragend |
| expand-change | row, expandedRows | 当用户对某一行展开或者关闭的时候会触发该事件 | @expand-change |

## 支持的 ElementPlus el-table 方法

FuniVCurd 通过 ref 暴露内部 el-table 的所有方法：

### 选择相关方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| clearSelection | - | 用于多选表格，清空用户的选择 |
| getSelectionRows | - | 返回当前选中的行 |
| toggleRowSelection | row, selected | 用于多选表格，切换某一行的选中状态 |
| toggleAllSelection | - | 用于多选表格，切换全选和全不选 |
| setCurrentRow | row | 用于单选表格，设定某一行为选中行 |
| clearCurrentRow | - | 用于单选表格，清空选中行 |

### 排序相关方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| clearSort | - | 用于清空排序条件 |
| sort | prop, order | 手动对Table进行排序 |

### 筛选相关方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| clearFilter | columnKeys | 不传入参数时用于清空所有过滤条件 |

### 布局相关方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| doLayout | - | 对Table进行重新布局 |
| scrollTo | options | 滚动到一组特定坐标 |
| setScrollTop | top | 设置垂直滚动位置 |
| setScrollLeft | left | 设置水平滚动位置 |

## 使用方式

### 基础表格使用

```vue
<template>
  <FuniVCurd
    :data="tableData"
    :columns="columns"
    :row-key="rowKey"
    
    <!-- ElementPlus el-table 属性透传 -->
    stripe
    border
    height="400"
    :default-sort="{ prop: 'date', order: 'descending' }"
    :row-class-name="tableRowClassName"
    
    <!-- ElementPlus el-table 事件透传 -->
    @cell-click="handleCellClick"
    @header-click="handleHeaderClick"
    @sort-change="handleSortChange"
    @selection-change="handleSelectionChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const rowKey = 'id'

const columns = ref([
  { type: 'selection', width: 55 },
  { prop: 'date', label: '日期', width: 180, sortable: true },
  { prop: 'name', label: '姓名', width: 180 },
  { prop: 'address', label: '地址' }
])

const tableData = ref([
  {
    id: 1,
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }
])

// 行样式
const tableRowClassName = ({ row, rowIndex }) => {
  if (rowIndex === 1) {
    return 'warning-row'
  } else if (rowIndex === 3) {
    return 'success-row'
  }
  return ''
}

// 事件处理
const handleCellClick = (row, column, cell, event) => {
  console.log('单元格点击:', row, column)
}

const handleHeaderClick = (column, event) => {
  console.log('表头点击:', column)
}

const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', prop, order)
}

const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}
</script>
```

### 方法调用示例

```vue
<template>
  <div>
    <div class="button-group">
      <el-button @click="clearSelection">清空选择</el-button>
      <el-button @click="toggleFirstRowSelection">切换第一行选择</el-button>
      <el-button @click="clearSort">清空排序</el-button>
      <el-button @click="doLayout">重新布局</el-button>
    </div>
    
    <FuniVCurd
      ref="vcurdRef"
      :data="tableData"
      :columns="columns"
      :row-key="rowKey"
      @selection-change="handleSelectionChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const vcurdRef = ref()
const rowKey = 'id'

const columns = ref([
  { type: 'selection', width: 55 },
  { prop: 'name', label: '姓名', width: 180, sortable: true },
  { prop: 'age', label: '年龄', width: 180, sortable: true }
])

const tableData = ref([
  { id: 1, name: '张三', age: 25 },
  { id: 2, name: '李四', age: 30 }
])

// 方法调用
const clearSelection = () => {
  vcurdRef.value.clearSelection()
}

const toggleFirstRowSelection = () => {
  const firstRow = tableData.value[0]
  vcurdRef.value.toggleRowSelection(firstRow)
}

const clearSort = () => {
  vcurdRef.value.clearSort()
}

const doLayout = () => {
  vcurdRef.value.doLayout()
}

const handleSelectionChange = (selection) => {
  console.log('当前选择:', selection)
}
</script>
```

### 高级功能集成

```vue
<template>
  <FuniVCurd
    :data="tableData"
    :columns="columns"
    :row-key="rowKey"
    
    <!-- 树形数据配置 -->
    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    lazy
    :load="loadTreeNode"
    
    <!-- 样式配置 -->
    :row-class-name="getRowClassName"
    :cell-style="getCellStyle"
    
    <!-- 排序配置 -->
    :default-sort="{ prop: 'name', order: 'ascending' }"
    
    @row-click="handleRowClick"
    @expand-change="handleExpandChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const rowKey = 'id'

const columns = ref([
  { prop: 'name', label: '名称', width: 200 },
  { prop: 'type', label: '类型', width: 100 },
  { prop: 'size', label: '大小', width: 100 }
])

const tableData = ref([
  {
    id: 1,
    name: '文件夹1',
    type: 'folder',
    hasChildren: true
  }
])

// 懒加载树节点
const loadTreeNode = (tree, treeNode, resolve) => {
  setTimeout(() => {
    resolve([
      {
        id: tree.id * 10 + 1,
        name: '子文件1',
        type: 'file',
        size: '1KB'
      }
    ])
  }, 1000)
}

// 行样式
const getRowClassName = ({ row, rowIndex }) => {
  return row.type === 'folder' ? 'folder-row' : 'file-row'
}

// 单元格样式
const getCellStyle = ({ row, column, rowIndex, columnIndex }) => {
  if (column.property === 'name' && row.type === 'folder') {
    return { fontWeight: 'bold' }
  }
  return {}
}

// 事件处理
const handleRowClick = (row, column, event) => {
  console.log('行点击:', row)
}

const handleExpandChange = (row, expandedRows) => {
  console.log('展开变化:', row, expandedRows)
}
</script>
```

## 注意事项

1. **性能优化**: FuniVCurd 专为大数据量设计，通过虚拟滚动和优化的列渲染提升性能
2. **属性透传**: 所有 ElementPlus el-table 的属性都通过 `v-bind="tableAttrs"` 透传
3. **事件透传**: 所有 ElementPlus el-table 的事件都通过 `v-on="$listeners"` 透传
4. **方法访问**: 通过组件 ref 可以访问内部 el-table 的所有方法
5. **列配置**: 必须通过 columns 属性配置列，不支持 el-table-column 子组件方式
6. **插槽支持**: 支持通过 columns 配置中的 slots 属性定义动态插槽
