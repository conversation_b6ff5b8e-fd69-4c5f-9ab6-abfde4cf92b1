# FuniSearchRegion 配置结构定义

## 组件配置接口

### IFuniSearchRegionProps

```typescript
interface IFuniSearchRegionProps {
  /** 绑定值 */
  modelValue?: string | number | object
  /** 属性配置对象 */
  attribute?: IAttributeConfig
}
```

### IFuniSearchRegionEmits

```typescript
interface IFuniSearchRegionEmits {
  /** v-model 更新事件 */
  'update:modelValue': (value: Record<string, string>) => void
}
```

## 属性配置结构

### IAttributeConfig

```typescript
interface IAttributeConfig {
  /** 地区配置 JSON 字符串 */
  region?: string
  /** 自定义接口地址 */
  url?: string
}
```

### IRegionConfig

```typescript
interface IRegionConfig {
  /** 最大层级数 */
  maxLevel: number
  /** 级联列配置 */
  cascadeColumn: ICascadeColumn[]
}
```

### ICascadeColumn

```typescript
interface ICascadeColumn {
  /** 层级 */
  level: number
  /** 对应的字段名 */
  column: string
}
```

## 地区数据结构

### IRegionItem

```typescript
interface IRegionItem {
  /** 地区代码 */
  code: string
  /** 地区名称 */
  name: string
  /** 地区层级 */
  level: number
  /** 是否为叶子节点 */
  leaf?: boolean
  /** 父级代码 */
  parentCode?: string
  /** 子级地区列表 */
  children?: IRegionItem[]
}
```

### IRegionResponse

```typescript
interface IRegionResponse {
  /** 响应状态 */
  success?: boolean
  /** 地区列表 */
  list?: IRegionItem[]
  /** 直接返回数组格式 */
  0?: IRegionItem
}
```

## 级联选择器配置

### ICascaderProps

```typescript
interface ICascaderProps {
  /** 启用懒加载 */
  lazy: true
  /** 节点值字段 */
  value: 'code'
  /** 节点显示字段 */
  label: 'name'
  /** 严格选择模式 */
  checkStrictly: true
  /** 不返回完整路径 */
  emitPath: false
  /** 懒加载函数 */
  lazyLoad: (node: ICascaderNode, resolve: Function) => void
}
```

### ICascaderNode

```typescript
interface ICascaderNode {
  /** 节点层级 */
  level: number
  /** 节点值 */
  value: string
  /** 节点标签 */
  label: string
  /** 节点数据 */
  data: IRegionItem
  /** 是否为叶子节点 */
  leaf: boolean
}
```

## 内部状态结构

### IComponentState

```typescript
interface IComponentState {
  /** 级联选择器引用 */
  cascader: Ref<any>
  /** 级联选择器值 */
  cascaderValue: Ref<string | number>
  /** 地区配置（计算属性） */
  regionConfig: ComputedRef<IRegionConfig>
  /** 根地区接口地址（计算属性） */
  rootRegionURL: ComputedRef<string>
  /** 子地区接口地址（计算属性） */
  sonRegionURL: ComputedRef<string>
  /** 级联选择器属性（计算属性） */
  cascaderProps: ComputedRef<ICascaderProps>
}
```

## API 接口配置

### IApiEndpoints

```typescript
interface IApiEndpoints {
  /** 根地区接口 */
  rootRegion: '/csccs/region/findRootRegion'
  /** 子地区接口 */
  sonRegion: '/csccs/region/findSonRegionTreeNoRestriction'
}
```

### IApiRequest

```typescript
interface IApiRequest {
  /** 根地区请求 */
  rootRegionRequest: () => Promise<IRegionResponse>
  /** 子地区请求 */
  sonRegionRequest: (params: ISonRegionParams) => Promise<IRegionResponse>
}
```

### ISonRegionParams

```typescript
interface ISonRegionParams {
  /** 地区代码 */
  code: string
  /** 业务配置代码 */
  businessConfigCode: string
}
```

## 事件处理配置

### IEventHandlers

```typescript
interface IEventHandlers {
  /** 值变化处理 */
  handleValuesChange: () => void
  /** 懒加载处理 */
  lazyLoad: (node: ICascaderNode, resolve: Function) => void
  /** 根地区加载 */
  loadRootRegion: (resolve: Function) => Promise<void>
  /** 子地区加载 */
  loadSonRegion: (node: ICascaderNode, resolve: Function) => Promise<void>
}
```

## 监听器配置

### IWatcherConfig

```typescript
interface IWatcherConfig {
  /** modelValue 监听器 */
  modelValueWatcher: {
    source: () => Props['modelValue']
    handler: () => void
    options: {
      immediate: boolean
    }
  }
}
```

## 配置示例

### 基础配置

```typescript
const basicConfig: IFuniSearchRegionProps = {
  modelValue: null,
  attribute: {
    region: JSON.stringify({
      maxLevel: 3,
      cascadeColumn: [
        { level: 1, column: 'provinceCode' },
        { level: 2, column: 'cityCode' },
        { level: 3, column: 'districtCode' }
      ]
    })
  }
}
```

### 省市配置

```typescript
const provinceCityConfig: IFuniSearchRegionProps = {
  modelValue: null,
  attribute: {
    region: JSON.stringify({
      maxLevel: 2,
      cascadeColumn: [
        { level: 1, column: 'provinceCode' },
        { level: 2, column: 'cityCode' }
      ]
    })
  }
}
```

### 自定义接口配置

```typescript
const customApiConfig: IFuniSearchRegionProps = {
  modelValue: null,
  attribute: {
    url: '/api/custom/region',
    region: JSON.stringify({
      maxLevel: 4,
      cascadeColumn: [
        { level: 1, column: 'provinceCode' },
        { level: 2, column: 'cityCode' },
        { level: 3, column: 'districtCode' },
        { level: 4, column: 'streetCode' }
      ]
    })
  }
}
```

## 预设配置模板

### IRegionTemplates

```typescript
interface IRegionTemplates {
  /** 省市区配置 */
  provinceCity: IRegionConfig
  /** 省市区县配置 */
  provinceCityDistrict: IRegionConfig
  /** 省市区县街道配置 */
  provinceCityDistrictStreet: IRegionConfig
}
```

### 预设模板

```typescript
const regionTemplates: IRegionTemplates = {
  provinceCity: {
    maxLevel: 2,
    cascadeColumn: [
      { level: 1, column: 'provinceCode' },
      { level: 2, column: 'cityCode' }
    ]
  },
  provinceCityDistrict: {
    maxLevel: 3,
    cascadeColumn: [
      { level: 1, column: 'provinceCode' },
      { level: 2, column: 'cityCode' },
      { level: 3, column: 'districtCode' }
    ]
  },
  provinceCityDistrictStreet: {
    maxLevel: 4,
    cascadeColumn: [
      { level: 1, column: 'provinceCode' },
      { level: 2, column: 'cityCode' },
      { level: 3, column: 'districtCode' },
      { level: 4, column: 'streetCode' }
    ]
  }
}
```

## 样式配置

### IStyleConfig

```typescript
interface IStyleConfig {
  /** 级联选择器样式 */
  cascader: {
    width: '240px'
    clearable: boolean
    placeholder: string
  }
}
```

## 工具函数配置

### IUtilityFunctions

```typescript
interface IUtilityFunctions {
  /** 解析地区配置 */
  parseRegionConfig: (configString: string) => IRegionConfig
  /** 格式化地区数据 */
  formatRegionData: (data: any) => IRegionItem[]
  /** 获取选中节点信息 */
  getSelectedNodeInfo: (cascaderRef: any) => ICascaderNode | null
  /** 构建输出值 */
  buildOutputValue: (node: ICascaderNode, config: IRegionConfig) => Record<string, string>
}
```

## 错误处理配置

### IErrorHandling

```typescript
interface IErrorHandling {
  /** API 请求错误处理 */
  apiErrorHandler: (error: any) => void
  /** 配置解析错误处理 */
  configParseErrorHandler: (error: any) => void
  /** 数据格式错误处理 */
  dataFormatErrorHandler: (data: any) => IRegionItem[]
}
```

## 性能优化配置

### IPerformanceConfig

```typescript
interface IPerformanceConfig {
  /** 懒加载配置 */
  lazyLoad: {
    enabled: boolean
    cacheSize: number
    timeout: number
  }
  /** 数据缓存配置 */
  cache: {
    enabled: boolean
    maxSize: number
    ttl: number // 缓存时间（毫秒）
  }
}
```

## 默认配置

```typescript
const defaultConfig: Required<IFuniSearchRegionProps> = {
  modelValue: null,
  attribute: {
    region: JSON.stringify({
      maxLevel: 3,
      cascadeColumn: [
        { level: 1, column: 'provinceCode' },
        { level: 2, column: 'cityCode' },
        { level: 3, column: 'districtCode' }
      ]
    })
  }
}

const defaultApiEndpoints: IApiEndpoints = {
  rootRegion: '/csccs/region/findRootRegion',
  sonRegion: '/csccs/region/findSonRegionTreeNoRestriction'
}

const defaultCascaderProps: Omit<ICascaderProps, 'lazyLoad'> = {
  lazy: true,
  value: 'code',
  label: 'name',
  checkStrictly: true,
  emitPath: false
}
```
