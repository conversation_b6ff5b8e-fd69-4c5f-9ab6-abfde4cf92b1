# FuniSearchRegion API 文档

## 组件概述

FuniSearchRegion 是一个地区搜索选择器组件，基于 ElementPlus 的 el-cascader 组件封装。支持懒加载的级联地区选择，可以根据配置动态加载不同层级的地区数据，适用于省市区等地理位置选择场景。

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | String \| Number \| Object | - | 绑定值 |
| attribute | Object | {} | 属性配置对象 |

### Props 详细说明

#### modelValue
- 组件的绑定值，支持 v-model 双向绑定
- 可以是字符串、数字或对象类型
- 通常为地区代码或包含地区信息的对象

#### attribute
- 组件属性配置对象，包含以下配置：
  - `region`: 地区配置的 JSON 字符串
  - `url`: 自定义接口地址（可选）

### attribute 配置结构

```javascript
{
  // 地区配置 JSON 字符串
  region: JSON.stringify({
    maxLevel: 3,  // 最大层级
    cascadeColumn: [
      { level: 1, column: 'provinceCode' },
      { level: 2, column: 'cityCode' },
      { level: 3, column: 'districtCode' }
    ]
  }),
  // 自定义接口地址（可选）
  url: '/custom/region/api'
}
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: object) | 选择地区后触发，用于 v-model |

### Events 详细说明

#### update:modelValue
- 当用户选择地区时触发
- 参数为包含地区代码的对象，格式根据 cascadeColumn 配置决定
- 例如：`{ provinceCode: '110000' }` 或 `{ cityCode: '110100' }`

## 内部配置

### 级联选择器配置

```javascript
const cascaderProps = {
  lazy: true,              // 启用懒加载
  value: 'code',           // 节点值字段
  label: 'name',           // 节点显示字段
  checkStrictly: true,     // 严格选择模式
  emitPath: false,         // 不返回完整路径
  lazyLoad: Function       // 懒加载函数
}
```

### 默认接口地址

- **根地区接口**: `/csccs/region/findRootRegion`
- **子地区接口**: `/csccs/region/findSonRegionTreeNoRestriction`

## 数据结构

### 地区数据格式

```javascript
{
  code: string,      // 地区代码
  name: string,      // 地区名称
  level: number,     // 地区层级
  leaf: boolean      // 是否为叶子节点
}
```

### 配置数据格式

```javascript
{
  maxLevel: number,           // 最大层级数
  cascadeColumn: [            // 级联列配置
    {
      level: number,          // 层级
      column: string          // 对应的字段名
    }
  ]
}
```

## 使用示例

### 基础用法

```vue
<template>
  <div>
    <FuniSearchRegion 
      v-model="selectedRegion"
      :attribute="regionAttribute"
      @update:modelValue="handleRegionChange"
    />
    <p>选中的地区：{{ selectedRegion }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniSearchRegion from '@/components/FuniSearchRegion/index.vue'

const selectedRegion = ref(null)

const regionAttribute = {
  region: JSON.stringify({
    maxLevel: 3,
    cascadeColumn: [
      { level: 1, column: 'provinceCode' },
      { level: 2, column: 'cityCode' },
      { level: 3, column: 'districtCode' }
    ]
  })
}

const handleRegionChange = (value) => {
  console.log('选中的地区：', value)
}
</script>
```

### 自定义接口地址

```vue
<template>
  <div>
    <FuniSearchRegion 
      v-model="selectedRegion"
      :attribute="customAttribute"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniSearchRegion from '@/components/FuniSearchRegion/index.vue'

const selectedRegion = ref(null)

const customAttribute = {
  url: '/api/custom/region',
  region: JSON.stringify({
    maxLevel: 2,
    cascadeColumn: [
      { level: 1, column: 'provinceCode' },
      { level: 2, column: 'cityCode' }
    ]
  })
}
</script>
```

### 表单中使用

```vue
<template>
  <el-form :model="form" label-width="120px">
    <el-form-item label="所在地区：">
      <FuniSearchRegion 
        v-model="form.region"
        :attribute="regionConfig"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
import FuniSearchRegion from '@/components/FuniSearchRegion/index.vue'

const form = ref({
  region: null
})

const regionConfig = {
  region: JSON.stringify({
    maxLevel: 3,
    cascadeColumn: [
      { level: 1, column: 'provinceCode' },
      { level: 2, column: 'cityCode' },
      { level: 3, column: 'districtCode' }
    ]
  })
}

const submitForm = () => {
  console.log('表单数据：', form.value)
}
</script>
```

### 搜索表单中使用

```vue
<template>
  <div>
    <el-form :model="searchForm" inline>
      <el-form-item label="地区：">
        <FuniSearchRegion 
          v-model="searchForm.regionFilter"
          :attribute="searchRegionConfig"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FuniSearchRegion from '@/components/FuniSearchRegion/index.vue'

const searchForm = ref({
  regionFilter: null
})

const searchRegionConfig = {
  region: JSON.stringify({
    maxLevel: 2,
    cascadeColumn: [
      { level: 1, column: 'provinceCode' },
      { level: 2, column: 'cityCode' }
    ]
  })
}

const search = () => {
  console.log('搜索条件：', searchForm.value)
  // 执行搜索逻辑
}

const resetSearch = () => {
  searchForm.value.regionFilter = null
}
</script>
```

### 动态配置层级

```vue
<template>
  <div>
    <el-radio-group v-model="levelMode" @change="updateRegionConfig">
      <el-radio-button label="2">省市</el-radio-button>
      <el-radio-button label="3">省市区</el-radio-button>
      <el-radio-button label="4">省市区街道</el-radio-button>
    </el-radio-group>
    
    <FuniSearchRegion 
      v-model="selectedRegion"
      :attribute="dynamicAttribute"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import FuniSearchRegion from '@/components/FuniSearchRegion/index.vue'

const selectedRegion = ref(null)
const levelMode = ref('3')

const dynamicAttribute = computed(() => {
  const configs = {
    '2': {
      maxLevel: 2,
      cascadeColumn: [
        { level: 1, column: 'provinceCode' },
        { level: 2, column: 'cityCode' }
      ]
    },
    '3': {
      maxLevel: 3,
      cascadeColumn: [
        { level: 1, column: 'provinceCode' },
        { level: 2, column: 'cityCode' },
        { level: 3, column: 'districtCode' }
      ]
    },
    '4': {
      maxLevel: 4,
      cascadeColumn: [
        { level: 1, column: 'provinceCode' },
        { level: 2, column: 'cityCode' },
        { level: 3, column: 'districtCode' },
        { level: 4, column: 'streetCode' }
      ]
    }
  }
  
  return {
    region: JSON.stringify(configs[levelMode.value])
  }
})

const updateRegionConfig = () => {
  selectedRegion.value = null // 重置选择
}
</script>
```

## 特性功能

### 1. 懒加载
- 支持按需加载地区数据
- 减少初始加载时间
- 提高组件性能

### 2. 级联选择
- 支持多级地区选择
- 可配置最大层级数
- 支持严格选择模式

### 3. 动态配置
- 支持自定义接口地址
- 支持配置不同层级对应的字段名
- 支持动态调整最大层级

### 4. 数据适配
- 自动适配不同的数据格式
- 支持数组和对象格式的响应数据
- 自动设置叶子节点标识

## 样式定制

### 默认样式
- 宽度：240px
- 支持清空功能
- 占位符：请输入

### 自定义样式

```vue
<template>
  <FuniSearchRegion 
    v-model="selectedRegion"
    :attribute="regionAttribute"
    style="width: 300px"
  />
</template>

<style scoped>
:deep(.el-cascader) {
  width: 100% !important;
}
</style>
```

## 注意事项

1. **配置格式**：attribute.region 必须是有效的 JSON 字符串
2. **接口依赖**：需要确保地区接口返回正确的数据格式
3. **层级配置**：cascadeColumn 的 level 应与实际数据层级对应
4. **数据格式**：接口返回的数据应包含 code、name、level 字段
5. **性能考虑**：大量地区数据时建议使用懒加载
6. **错误处理**：接口请求失败时会返回空数组，不会影响组件正常使用
