# FuniFileTable API文档

## 组件概述

FuniFileTable是工作流附件管理组件，**仅用于工作流场景**，需要businessId才能正常工作。组件会自动从配置系统获取附件配置，支持文件上传、下载、预览、删除等功能。

⚠️ **重要提醒**: 
- 此组件仅用于工作流场景，需要businessId
- 普通文件上传请使用 `FuniFileTable/upload.vue`

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| params | Object | {} | ✅ | 工作流参数配置 |
| onlyShow | Boolean | false | - | 是否只读模式（详情/审核模式） |
| hideColumns | Array | [] | - | 隐藏的列名数组 |
| hideAddBtn | Boolean | false | - | 是否隐藏添加按钮 |
| fileListUrl | String | - | - | 自定义文件列表API地址 |
| isVerification | Boolean | true | - | 是否启用必传件校验 |
| callbackFun | Function | () => {} | - | 校验回调函数 |
| forceEditFileCode | Array | [] | - | 强制允许编辑的要件编码数组 |
| isAiAudit | Boolean | false | - | 是否启用AI审核功能 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| file-change | fileList: Array | 文件列表变化事件 | 文件上传、删除后 |
| file-delete | fileInfo: Object | 文件删除事件 | 删除文件时 |
| file-upload | fileInfo: Object | 文件上传事件 | 上传文件成功时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| submit | - | Promise | 提交校验，检查必传件是否完整 |
| verification | - | Boolean | 校验必传件是否完整 |
| getAllFileId | - | Array | 获取所有文件ID数组 |

## params配置结构

```typescript
interface FileTableParams {
  businessId: string;              // 工作流业务实例ID（必需）
  businessConfigCode?: string;     // 业务配置编码
  sysId?: string;                  // 系统ID
  preBusinessId?: string;          // 前置业务ID（继承附件用）
  preSysId?: string;               // 前置系统ID（继承附件用）
}
```

## 渲染条件

### 自动渲染条件
FuniFileTable会在以下条件下自动渲染：

```javascript
const shouldRender = computed(() => {
  return !!businessId.value && 
         ['新建', 'add', '编辑', 'edit', '详情', 'detail', '审核', 'audit'].includes(bizName.value)
})
```

### 工作流判断逻辑
```javascript
// 新建/编辑模式：多步骤结构，第一步返回businessId，第二步使用FuniFileTable
const isCreateEditMode = ['新建', 'add', '编辑', 'edit'].includes(bizName.value)

// 详情/审核模式：query参数或业务数据中存在businessId
const isDetailAuditMode = ['详情', 'detail', '审核', 'audit'].includes(bizName.value)
```

## 内置API接口

组件内置以下API接口：

| 接口用途 | 接口地址 | 说明 |
|----------|----------|------|
| 查询要件列表 | /bpmn/fileManage/queryBusinessFileInstanceList | 获取业务要件配置和文件列表 |
| 上传文件 | /bpmn/fileManage/uploadAttachment | 上传附件文件 |
| 删除文件 | /bpmn/fileManage/deleteAttachment | 删除指定文件 |
| 下载文件 | /bpmn/businessManage/downloadAttachmentForFile | 下载文件 |
| 预览文件 | /bpmn/fileManage/downloadAttachment | 预览文件 |
| 删除要件类型 | /bpmn/fileManage/delBusinessFileInstance | 删除整个要件类型 |

## 功能特性

### 1. 自动配置获取
- 根据businessId和businessConfigCode自动获取附件配置
- 支持必传件和可选件配置
- 支持文件类型、数量、大小限制

### 2. 文件操作
- 支持多文件上传
- 支持文件预览（图片、PDF等）
- 支持文件下载
- 支持文件删除

### 3. 校验功能
- 必传件完整性校验
- 文件格式校验
- 文件大小校验
- 文件数量校验

### 4. 权限控制
- 只读模式支持（onlyShow）
- 按钮权限控制
- 文件操作权限控制

## 使用场景

### 工作流新建场景
```vue
<template>
  <FuniDetail :steps="steps" bizName="新建">
    <!-- 第一步：基本信息 -->
    <template #basicInfo>
      <FuniForm :schema="basicSchema" v-model="formData" />
    </template>
    
    <!-- 第二步：附件上传 -->
    <template #attachments>
      <FuniFileTable 
        v-if="businessId"
        :params="fileTableParams"
        @file-change="handleFileChange"
      />
      <div v-else class="file-table-placeholder">
        <el-empty description="请先保存基本信息后再上传附件" />
      </div>
    </template>
  </FuniDetail>
</template>

<script setup>
const businessId = ref('')
const fileTableParams = computed(() => ({
  businessId: businessId.value,
  businessConfigCode: 'LEAVE_APPLICATION',
  sysId: appStore.system.id
}))

const handleFileChange = (fileList) => {
  console.log('文件列表变化:', fileList)
}
</script>
```

### 工作流详情场景
```vue
<template>
  <FuniDetail :steps="steps" bizName="详情">
    <!-- 基本信息展示 -->
    <template #basicInfo>
      <FuniForm :schema="basicSchema" v-model="formData" :readonly="true" />
    </template>
    
    <!-- 附件查看 -->
    <template #attachments>
      <FuniFileTable 
        :params="fileTableParams"
        :onlyShow="true"
        :hideAddBtn="true"
      />
    </template>
  </FuniDetail>
</template>

<script setup>
const fileTableParams = computed(() => ({
  businessId: route.query.businessId,
  sysId: appStore.system.id
}))
</script>
```

### 工作流审核场景
```vue
<template>
  <FuniDetail :steps="steps" bizName="审核">
    <!-- 申请信息 -->
    <template #applicationInfo>
      <FuniForm :schema="applicationSchema" v-model="applicationData" :readonly="true" />
    </template>
    
    <!-- 附件材料 -->
    <template #attachments>
      <FuniFileTable 
        :params="fileTableParams"
        :onlyShow="true"
        :hideColumns="['needNumber', 'attachmentAmount']"
      />
    </template>
  </FuniDetail>
</template>

<script setup>
const fileTableParams = computed(() => ({
  businessId: route.query.businessId,
  sysId: appStore.system.id
}))
</script>
```

## 与FuniFileTable/upload.vue的区别

| 特性 | FuniFileTable | FuniFileTable/upload.vue |
|------|---------------|---------------------------|
| 使用场景 | 工作流场景 | 普通文件上传场景 |
| 必需条件 | businessId | 无 |
| 配置方式 | 自动获取 | 手动配置 |
| 功能特性 | 要件管理、校验 | 基础上传 |
| 权限控制 | 工作流权限 | 手动控制 |

## 注意事项

### 1. 使用条件
- 必须在工作流场景中使用
- 必须提供有效的businessId
- 建议配置businessConfigCode以获取正确的要件配置

### 2. 渲染时机
- 新建/编辑模式：第一步保存后获得businessId，第二步显示附件组件
- 详情/审核模式：页面加载时就有businessId，直接显示

### 3. 权限控制
- onlyShow模式下所有操作按钮都会隐藏
- hideAddBtn可以单独控制添加按钮显示
- hideColumns可以隐藏指定列

### 4. 校验功能
- submit方法会校验所有必传件是否完整
- 可以通过isVerification控制是否启用校验
- callbackFun在校验通过后执行

## 常见问题

### Q: 组件不显示怎么办？
A: 检查businessId是否存在，bizName是否为支持的模式

### Q: 如何获取businessId？
A: 通常在第一步保存基本信息后，后端API会返回businessId

### Q: 如何自定义要件配置？
A: 通过businessConfigCode指定配置，后端需要配置对应的要件规则

### Q: 如何处理文件上传失败？
A: 组件内部已处理上传错误，会显示错误提示

### Q: 如何实现文件继承？
A: 通过preBusinessId和preSysId参数，可以继承其他业务实例的附件
