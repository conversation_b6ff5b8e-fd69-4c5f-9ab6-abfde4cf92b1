# FuniFileTable ElementPlus API 支持

## 基础组件说明

FuniFileTable 是基于 ElementPlus 的多个组件组合而成的工作流附件管理组件，主要使用了以下 ElementPlus 组件：

- **el-table**: 用于显示附件列表
- **el-button**: 用于各种操作按钮
- **el-upload**: 用于文件上传功能
- **el-dialog**: 用于弹窗操作
- **el-image**: 用于图片预览
- **el-popconfirm**: 用于删除确认

## 内部使用的 ElementPlus 组件

### 1. FuniCurd (基于 el-table)

FuniFileTable 内部使用 FuniCurd 组件来展示附件列表，支持所有 el-table 的属性和事件。

```vue
<funi-curd
  ref="FuniFileCurdRef"
  rowKey="businessFileInstanceInfoId"
  :columns="columns"
  :loading="loading"
  :lodaData="_lodaData"
  :pagination="false"
  default-expand-all
  :header-cell-style="{ 'background-color': '#f9f9f9' }"
  :header-row-style="{ 'background-color': '#f9f9f9' }"
>
```

#### 支持的 el-table 属性

| ElementPlus 属性 | 类型 | 默认值 | 说明 | FuniFileTable 支持 |
|-----------------|------|--------|------|-------------------|
| row-key | String/Function | 'businessFileInstanceInfoId' | 行数据的 Key | ✅ 固定配置 |
| default-expand-all | Boolean | true | 是否默认展开所有行 | ✅ 固定配置 |
| show-header | Boolean | true | 是否显示表头 | ✅ 内部控制 |
| header-cell-style | Object/Function | - | 表头单元格样式 | ✅ 固定样式 |
| header-row-style | Object/Function | - | 表头行样式 | ✅ 固定样式 |
| stripe | Boolean | false | 是否为斑马纹 | ✅ 内部控制 |
| border | Boolean | true | 是否带有纵向边框 | ✅ 默认开启 |
| size | String | 'default' | 表格尺寸 | ✅ 内部控制 |
| empty-text | String | '暂无附件' | 空数据时显示的文本 | ✅ 内部控制 |

### 2. el-button 操作按钮

#### 主要操作按钮

```vue
<!-- 添加按钮 -->
<el-button 
  v-if="!onlyShow && !hideAddBtn" 
  type="primary" 
  @click="_addEvent"
>
  添加
</el-button>

<!-- 文件操作按钮 -->
<el-button type="text" @click="handlePreview">查看</el-button>
<el-button type="text" @click="handleDownload">下载</el-button>
<el-button type="text" @click="handleDelete">删除</el-button>
```

#### 支持的 el-button 属性

| ElementPlus 属性 | 类型 | 说明 | FuniFileTable 支持 |
|-----------------|------|------|-------------------|
| type | String | 按钮类型 | ✅ primary/text/danger |
| size | String | 按钮尺寸 | ✅ small/default |
| disabled | Boolean | 是否禁用 | ✅ 根据权限控制 |
| loading | Boolean | 是否加载中 | ✅ 操作时显示 |
| icon | String/Component | 按钮图标 | ✅ 部分按钮有图标 |

### 3. el-upload 文件上传

内部的 upload.vue 组件基于 el-upload 实现：

```vue
<el-upload
  ref="uploadRef"
  :action="uploadUrl"
  :data="uploadParams"
  :headers="uploadHeaders"
  :before-upload="beforeUpload"
  :on-success="onUploadSuccess"
  :on-error="onUploadError"
  :show-file-list="false"
  :auto-upload="true"
  multiple
>
```

#### 支持的 el-upload 属性

| ElementPlus 属性 | 类型 | 说明 | FuniFileTable 支持 |
|-----------------|------|------|-------------------|
| action | String | 上传的地址 | ✅ 可配置 |
| data | Object | 上传时附带的额外参数 | ✅ 可配置 |
| headers | Object | 设置上传的请求头部 | ✅ 自动设置 |
| multiple | Boolean | 是否支持多选文件 | ✅ 支持 |
| accept | String | 接受上传的文件类型 | ✅ 根据配置控制 |
| before-upload | Function | 上传文件之前的钩子 | ✅ 内部处理 |
| on-success | Function | 文件上传成功时的钩子 | ✅ 内部处理 |
| on-error | Function | 文件上传失败时的钩子 | ✅ 内部处理 |
| on-progress | Function | 文件上传时的钩子 | ✅ 内部处理 |
| show-file-list | Boolean | 是否显示已上传文件列表 | ✅ 固定为 false |
| auto-upload | Boolean | 是否在选取文件后立即进行上传 | ✅ 固定为 true |
| limit | Number | 最大允许上传个数 | ✅ 根据配置控制 |

### 4. el-dialog 弹窗组件

用于文件上传、添加、AI审核等弹窗操作：

```vue
<el-dialog
  v-model="dialogVisible"
  :title="dialogTitle"
  :width="dialogWidth"
  :close-on-click-modal="false"
  :close-on-press-escape="false"
>
```

#### 支持的 el-dialog 属性

| ElementPlus 属性 | 类型 | 说明 | FuniFileTable 支持 |
|-----------------|------|------|-------------------|
| model-value | Boolean | 是否显示 Dialog | ✅ 内部控制 |
| title | String | Dialog 的标题 | ✅ 动态设置 |
| width | String/Number | Dialog 的宽度 | ✅ 根据内容调整 |
| close-on-click-modal | Boolean | 是否可以通过点击 modal 关闭 Dialog | ✅ 固定为 false |
| close-on-press-escape | Boolean | 是否可以通过按下 ESC 关闭 Dialog | ✅ 固定为 false |
| show-close | Boolean | 是否显示关闭按钮 | ✅ 默认显示 |
| before-close | Function | 关闭前的回调 | ✅ 内部处理 |
| destroy-on-close | Boolean | 关闭时销毁 Dialog 中的元素 | ✅ 部分弹窗启用 |

### 5. el-image 图片预览

用于图片文件的预览功能：

```vue
<el-image
  :src="imageUrl"
  :preview-src-list="previewList"
  :initial-index="initialIndex"
  fit="cover"
  preview-teleported
>
```

#### 支持的 el-image 属性

| ElementPlus 属性 | 类型 | 说明 | FuniFileTable 支持 |
|-----------------|------|------|-------------------|
| src | String | 图片源地址 | ✅ 动态设置 |
| fit | String | 确定图片如何适应容器框 | ✅ 固定为 cover |
| alt | String | 原生 alt | ✅ 使用文件名 |
| preview-src-list | Array | 开启图片预览功能 | ✅ 支持多图预览 |
| initial-index | Number | 初始预览图片索引 | ✅ 动态设置 |
| preview-teleported | Boolean | 是否将预览弹窗插入至 body 元素下 | ✅ 固定为 true |
| hide-on-click-modal | Boolean | 是否可以通过点击遮罩层关闭预览 | ✅ 默认行为 |

### 6. el-popconfirm 删除确认

用于删除文件时的确认操作：

```vue
<el-popconfirm
  title="确定删除这个文件吗？"
  confirm-button-text="确定"
  cancel-button-text="取消"
  confirm-button-type="danger"
  @confirm="handleConfirmDelete"
>
```

#### 支持的 el-popconfirm 属性

| ElementPlus 属性 | 类型 | 说明 | FuniFileTable 支持 |
|-----------------|------|------|-------------------|
| title | String | 标题 | ✅ 固定文案 |
| confirm-button-text | String | 确认按钮文字 | ✅ 可配置 |
| cancel-button-text | String | 取消按钮文字 | ✅ 可配置 |
| confirm-button-type | String | 确认按钮类型 | ✅ 固定为 danger |
| icon | String/Component | 自定义图标 | ✅ 默认警告图标 |
| icon-color | String | Icon 颜色 | ✅ 默认颜色 |
| hide-icon | Boolean | 是否隐藏 Icon | ✅ 默认显示 |
| hide-after | Number | 关闭时的延迟 | ✅ 默认值 |
| teleported | Boolean | 是否将弹层放置于 body 内 | ✅ 默认行为 |

## 事件透传说明

### 1. 表格事件

FuniFileTable 内部的 FuniCurd 组件支持所有 el-table 事件：

```javascript
// 行点击事件
@row-click="handleRowClick"

// 单元格点击事件  
@cell-click="handleCellClick"

// 选择变化事件
@selection-change="handleSelectionChange"
```

### 2. 上传事件

el-upload 的事件通过内部处理后，以自定义事件形式对外暴露：

```javascript
// 文件上传成功
emit('file-upload', fileInfo)

// 文件删除
emit('file-delete', fileInfo)

// 文件列表变化
emit('file-change', fileList)
```

### 3. 弹窗事件

Dialog 相关事件主要用于内部状态管理：

```javascript
// 弹窗打开
@open="handleDialogOpen"

// 弹窗关闭
@close="handleDialogClose"

// 弹窗关闭前
@before-close="handleBeforeClose"
```

## 样式定制支持

### 1. ElementPlus 主题变量

FuniFileTable 完全支持 ElementPlus 的主题定制：

```css
:root {
  --el-color-primary: #409eff;
  --el-color-success: #67c23a;
  --el-color-warning: #e6a23c;
  --el-color-danger: #f56c6c;
  --el-color-info: #909399;
}
```

### 2. 组件级样式定制

```css
/* 表格样式 */
.funi-file-table .el-table {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f9f9f9;
}

/* 按钮样式 */
.funi-file-table .el-button {
  --el-button-font-size: 14px;
  --el-button-border-radius: 4px;
}

/* 上传区域样式 */
.funi-file-table .el-upload {
  --el-upload-dragger-padding-horizontal: 40px;
  --el-upload-dragger-padding-vertical: 40px;
}
```

### 3. 自定义CSS类

```css
/* 文件表格容器 */
.funi-file-table {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
}

/* 操作按钮组 */
.funi-file-table .action-buttons {
  display: flex;
  gap: 8px;
}

/* 文件预览 */
.funi-file-table .file-preview {
  max-width: 100px;
  max-height: 100px;
}
```

## 使用示例

### 基础 ElementPlus 功能集成

```vue
<template>
  <div class="file-management">
    <!-- 使用 ElementPlus 布局组件 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card title="附件管理">
          <FuniFileTable
            :params="fileParams"
            :onlyShow="false"
            @file-change="handleFileChange"
          />
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card title="操作日志">
          <el-timeline>
            <el-timeline-item
              v-for="log in operationLogs"
              :key="log.id"
              :timestamp="log.time"
            >
              {{ log.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 使用 ElementPlus 消息提示 -->
    <el-backtop :right="100" :bottom="100" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'

const fileParams = ref({
  businessId: 'BIZ_001',
  businessConfigCode: 'LOAN_APPLY',
  sysId: 'LOAN_SYSTEM'
})

const operationLogs = ref([
  { id: 1, time: '2024-12-19 10:30:00', content: '上传身份证正面' },
  { id: 2, time: '2024-12-19 10:32:00', content: '上传身份证反面' }
])

const handleFileChange = (fileList) => {
  // 使用 ElementPlus 消息组件
  ElMessage.success('文件列表已更新')
  
  // 使用 ElementPlus 通知组件
  ElNotification({
    title: '附件提醒',
    message: `当前共有 ${fileList.length} 个附件`,
    type: 'info'
  })
}
</script>
```

## 注意事项

1. **版本兼容性**：确保 ElementPlus 版本与 FuniFileTable 兼容
2. **样式继承**：FuniFileTable 完全继承 ElementPlus 的主题样式
3. **事件处理**：部分 ElementPlus 事件被内部处理，通过自定义事件对外暴露
4. **组件引用**：可以通过 ref 访问内部的 ElementPlus 组件实例
5. **国际化**：支持 ElementPlus 的国际化配置
6. **无障碍访问**：继承 ElementPlus 的无障碍访问特性
