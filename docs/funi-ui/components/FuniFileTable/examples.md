# FuniFileTable 使用示例

## 基础使用示例

### 1. 新建页面附件管理

```vue
<template>
  <div class="create-page">
    <el-form ref="formRef" :model="formData" :rules="rules">
      <!-- 其他表单项 -->
      <el-form-item label="业务附件" prop="attachments">
        <FuniFileTable
          ref="fileTableRef"
          :params="fileTableParams"
          :isVerification="true"
          @file-change="handleFileChange"
        />
      </el-form-item>
    </el-form>
    
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const fileTableRef = ref()
const formRef = ref()

// 表单数据
const formData = reactive({
  title: '',
  description: '',
  attachments: []
})

// 文件表格参数
const fileTableParams = reactive({
  businessId: `BIZ_${Date.now()}`,       // 新建时生成唯一ID
  businessConfigCode: 'LOAN_APPLY',      // 业务配置编码
  sysId: 'LOAN_SYSTEM'                   // 系统ID
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  attachments: [
    {
      validator: async (rule, value, callback) => {
        // 使用文件表格的校验方法
        const isValid = await fileTableRef.value?.verification()
        if (!isValid) {
          callback(new Error('请上传必传附件'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 文件变化处理
const handleFileChange = (fileList) => {
  formData.attachments = fileList
  // 触发表单验证
  formRef.value?.validateField('attachments')
}

// 提交处理
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()
    
    // 附件校验
    const fileValid = await fileTableRef.value?.submit()
    if (!fileValid) {
      ElMessage.warning('请完善附件信息')
      return
    }
    
    // 获取所有文件ID
    const fileIds = fileTableRef.value?.getAllFileId() || []
    
    // 提交业务数据
    const submitData = {
      ...formData,
      fileIds
    }
    
    console.log('提交数据:', submitData)
    ElMessage.success('提交成功')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

const handleCancel = () => {
  // 取消逻辑
}
</script>
```

### 2. 编辑页面附件管理

```vue
<template>
  <div class="edit-page">
    <FuniFileTable
      ref="fileTableRef"
      :params="fileTableParams"
      :isVerification="true"
      :forceEditFileCode="forceEditCodes"
      @file-change="handleFileChange"
    />
    
    <div class="form-actions">
      <el-button @click="handleSave">保存</el-button>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

const props = defineProps({
  businessId: {
    type: String,
    required: true
  }
})

const fileTableRef = ref()

// 文件表格参数
const fileTableParams = reactive({
  businessId: props.businessId,
  businessConfigCode: 'LOAN_APPLY',
  sysId: 'LOAN_SYSTEM'
})

// 强制允许编辑的文件类型
const forceEditCodes = [
  'CONTRACT',                            // 合同文件
  'SUPPLEMENT'                           // 补充材料
]

const handleFileChange = (fileList) => {
  console.log('文件列表变化:', fileList)
}

const handleSave = async () => {
  // 保存草稿，不校验必传件
  ElMessage.success('保存成功')
}

const handleSubmit = async () => {
  // 提交时校验必传件
  const isValid = await fileTableRef.value?.submit()
  if (isValid) {
    ElMessage.success('提交成功')
  }
}
</script>
```

### 3. 详情页面附件展示

```vue
<template>
  <div class="detail-page">
    <el-descriptions title="业务信息" :column="2" border>
      <el-descriptions-item label="业务编号">{{ businessInfo.code }}</el-descriptions-item>
      <el-descriptions-item label="申请人">{{ businessInfo.applicant }}</el-descriptions-item>
      <el-descriptions-item label="申请时间">{{ businessInfo.createTime }}</el-descriptions-item>
      <el-descriptions-item label="状态">{{ businessInfo.status }}</el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="left">附件信息</el-divider>
    
    <FuniFileTable
      :params="fileTableParams"
      :onlyShow="true"
      :hideAddBtn="true"
      :hideColumns="hideColumns"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const props = defineProps({
  businessId: {
    type: String,
    required: true
  }
})

// 业务信息
const businessInfo = reactive({
  code: 'LOAN_20241219_001',
  applicant: '张三',
  createTime: '2024-12-19 10:30:00',
  status: '审核中'
})

// 文件表格参数
const fileTableParams = reactive({
  businessId: props.businessId,
  businessConfigCode: 'LOAN_APPLY',
  sysId: 'LOAN_SYSTEM'
})

// 详情页隐藏的列
const hideColumns = [
  'actions'                              // 隐藏操作列
]
</script>
```

## 高级功能示例

### 4. 审核页面带AI审核

```vue
<template>
  <div class="audit-page">
    <el-card title="附件审核">
      <FuniFileTable
        ref="fileTableRef"
        :params="fileTableParams"
        :onlyShow="true"
        :isAiAudit="true"
        :hideAddBtn="true"
        @file-change="handleFileChange"
      />
    </el-card>
    
    <el-card title="审核意见" style="margin-top: 20px;">
      <el-form :model="auditForm">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.result">
            <el-radio label="pass">通过</el-radio>
            <el-radio label="reject">驳回</el-radio>
            <el-radio label="return">退回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input
            v-model="auditForm.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      
      <div class="audit-actions">
        <el-button @click="handleReject">驳回</el-button>
        <el-button type="primary" @click="handleApprove">通过</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const props = defineProps({
  businessId: String,
  taskId: String
})

const fileTableRef = ref()

// 文件表格参数
const fileTableParams = reactive({
  businessId: props.businessId,
  businessConfigCode: 'LOAN_APPROVE',
  sysId: 'LOAN_SYSTEM'
})

// 审核表单
const auditForm = reactive({
  result: 'pass',
  comment: ''
})

const handleFileChange = (fileList) => {
  // 检查AI审核状态
  const aiAuditFiles = fileList.filter(file => file.isByAiCheck === false)
  if (aiAuditFiles.length > 0) {
    ElMessage.warning('存在未通过AI审核的附件，请注意核查')
  }
}

const handleApprove = async () => {
  // 审核通过逻辑
  const auditData = {
    taskId: props.taskId,
    result: 'approve',
    comment: auditForm.comment
  }
  console.log('审核通过:', auditData)
}

const handleReject = async () => {
  // 审核驳回逻辑
  const auditData = {
    taskId: props.taskId,
    result: 'reject',
    comment: auditForm.comment
  }
  console.log('审核驳回:', auditData)
}
</script>
```

### 5. 继承附件配置

```vue
<template>
  <div class="inherit-page">
    <el-alert
      title="附件继承说明"
      description="当前节点将继承上一节点的附件，您可以在此基础上添加新的附件"
      type="info"
      show-icon
      :closable="false"
      style="margin-bottom: 20px;"
    />
    
    <FuniFileTable
      ref="fileTableRef"
      :params="fileTableParams"
      :isVerification="true"
      :callbackFun="validationCallback"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const props = defineProps({
  businessId: String,
  preBusinessId: String                  // 前置业务ID
})

const fileTableRef = ref()

// 文件表格参数（带继承配置）
const fileTableParams = reactive({
  businessId: props.businessId,
  businessConfigCode: 'LOAN_APPROVE',
  sysId: 'LOAN_SYSTEM',
  preBusinessId: props.preBusinessId,    // 继承前置业务附件
  preSysId: 'LOAN_SYSTEM'
})

// 校验回调
const validationCallback = (isValid, missingFiles, allFiles) => {
  if (!isValid) {
    const missingNames = missingFiles.map(f => f.fileConfigName).join('、')
    ElMessage.warning(`请补充必传附件：${missingNames}`)
  } else {
    ElMessage.success('附件校验通过')
  }
}
</script>
```

### 6. 自定义API配置

```vue
<template>
  <div class="custom-api-page">
    <FuniFileTable
      ref="fileTableRef"
      :params="fileTableParams"
      :fileListUrl="customApis.fileListUrl"
      :uploadFileUrl="customApis.uploadFileUrl"
      :uploadFileParams="customUploadParams"
      :downloadFileUrl="customApis.downloadFileUrl"
      :downloadParams="customDownloadParams"
      :checkFileFn="customCheckFile"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const fileTableRef = ref()

// 文件表格参数
const fileTableParams = reactive({
  businessId: 'CUSTOM_BIZ_001',
  businessConfigCode: 'CUSTOM_CONFIG',
  sysId: 'CUSTOM_SYSTEM'
})

// 自定义API配置
const customApis = {
  fileListUrl: '/custom/api/files/list',
  uploadFileUrl: '/custom/api/files/upload',
  downloadFileUrl: '/custom/api/files/download'
}

// 自定义上传参数
const customUploadParams = reactive({
  module: 'loan',
  category: 'document',
  watermark: true
})

// 自定义下载参数
const customDownloadParams = reactive({
  audit: true,
  compress: false
})

// 自定义文件查看方法
const customCheckFile = (fileInfo) => {
  console.log('自定义查看文件:', fileInfo)
  // 可以打开自定义的文件预览窗口
  window.open(`/custom/preview/${fileInfo.attachmentInfoId}`)
}
</script>
```

## 业务场景示例

### 7. 完整的贷款申请流程

```vue
<template>
  <div class="loan-apply-process">
    <!-- 申请阶段 -->
    <el-steps :active="currentStep" align-center>
      <el-step title="基本信息" />
      <el-step title="附件上传" />
      <el-step title="提交审核" />
    </el-steps>
    
    <!-- 附件上传步骤 -->
    <div v-if="currentStep === 1" class="step-content">
      <el-alert
        title="请上传以下必传附件"
        type="warning"
        show-icon
        :closable="false"
        style="margin-bottom: 20px;"
      />
      
      <FuniFileTable
        ref="fileTableRef"
        :params="fileTableParams"
        :isVerification="true"
        :callbackFun="fileValidationCallback"
        @file-upload="handleFileUpload"
        @file-delete="handleFileDelete"
      />
      
      <div class="step-actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const currentStep = ref(1)
const fileTableRef = ref()

// 文件表格参数
const fileTableParams = reactive({
  businessId: `LOAN_${Date.now()}`,
  businessConfigCode: 'LOAN_APPLY',
  sysId: 'LOAN_SYSTEM'
})

// 文件校验回调
const fileValidationCallback = (isValid, missingFiles) => {
  if (!isValid) {
    const missingNames = missingFiles.map(f => f.fileConfigName).join('、')
    ElNotification.warning({
      title: '附件提醒',
      message: `请上传必传附件：${missingNames}`,
      duration: 5000
    })
  }
}

// 文件上传成功
const handleFileUpload = (fileInfo) => {
  ElMessage.success(`文件 ${fileInfo.fileName} 上传成功`)
}

// 文件删除
const handleFileDelete = (fileInfo) => {
  ElMessage.info(`文件 ${fileInfo.fileName} 已删除`)
}

// 下一步
const nextStep = async () => {
  if (currentStep.value === 1) {
    // 校验附件
    const isValid = await fileTableRef.value?.submit()
    if (!isValid) {
      ElMessage.warning('请完善必传附件后再继续')
      return
    }
  }
  
  if (currentStep.value < 2) {
    currentStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}
</script>

<style scoped>
.step-content {
  margin: 40px 0;
  min-height: 400px;
}

.step-actions {
  text-align: center;
  margin-top: 30px;
}
</style>
```

## Mock数据示例

### 8. 开发环境Mock配置

```javascript
// mock/fileTable.js
export default [
  {
    url: '/bpmn/fileManage/queryBusinessFileInstanceList',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: [
          {
            businessFileInstanceInfoId: 'BFI_001',
            fileConfigCode: 'ID_CARD',
            fileConfigName: '身份证',
            isRequired: true,
            isEdit: true,
            maxFileCount: 2,
            attachmentInfos: [
              {
                attachmentInfoId: 'ATT_001',
                fileName: '身份证正面.jpg',
                fileSize: 1024000,
                fileType: 'image/jpeg',
                filePath: '/files/id_card_front.jpg',
                uploadTime: '2024-12-19 10:30:00',
                uploadUser: '张三',
                isByAiCheck: true
              }
            ]
          },
          {
            businessFileInstanceInfoId: 'BFI_002',
            fileConfigCode: 'INCOME_PROOF',
            fileConfigName: '收入证明',
            isRequired: true,
            isEdit: true,
            maxFileCount: 5,
            attachmentInfos: []
          }
        ]
      }
    }
  }
]
```

## 注意事项

1. **工作流依赖**：FuniFileTable 仅用于工作流场景，必须提供 businessId
2. **普通上传**：非工作流场景请使用 `FuniFileTable/upload.vue` 组件
3. **权限控制**：根据业务状态控制 onlyShow 和 hideAddBtn 属性
4. **校验时机**：在表单提交前调用 submit() 方法进行附件校验
5. **继承配置**：使用 preBusinessId 可以继承前置业务的附件
6. **AI审核**：启用 isAiAudit 后需要后端支持AI审核接口
7. **自定义API**：可以根据业务需要自定义各种API接口地址
8. **文件限制**：文件大小和类型限制由后端配置系统控制
