# FuniFileTable 最佳实践

## 推荐用法和配置

### 1. 工作流场景识别

#### 推荐：正确识别工作流场景

```javascript
// ✅ 推荐：工作流场景使用 FuniFileTable
const isWorkflowContext = () => {
  return !!(businessId && businessConfigCode)
}

// 工作流场景配置
if (isWorkflowContext()) {
  const workflowFileConfig = {
    params: {
      businessId: currentBusinessId,
      businessConfigCode: 'LOAN_APPLY',
      sysId: 'LOAN_SYSTEM'
    },
    isVerification: true
  }
}

// ✅ 推荐：非工作流场景使用 upload.vue
import FuniFileUpload from '@/components/FuniFileTable/upload.vue'

const normalUploadConfig = {
  url: '/api/upload',
  params: {
    module: 'document',
    category: 'general'
  }
}
```

#### 避免：在非工作流场景使用 FuniFileTable

```javascript
// ❌ 避免：没有 businessId 的场景使用 FuniFileTable
const wrongConfig = {
  params: {
    // 缺少 businessId，会导致组件无法正常工作
    businessConfigCode: 'SOME_CONFIG'
  }
}
```

### 2. 参数配置最佳实践

#### 推荐：完整的参数配置

```javascript
// ✅ 推荐：新建业务时的参数配置
const createBusinessParams = {
  businessId: generateBusinessId(),        // 生成唯一业务ID
  businessConfigCode: 'LOAN_APPLY',        // 明确的业务配置编码
  sysId: 'LOAN_SYSTEM',                    // 系统标识
  // 可选的业务参数
  applicantId: currentUser.id,
  branchCode: currentUser.branchCode
}

// ✅ 推荐：编辑业务时的参数配置
const editBusinessParams = {
  businessId: existingBusinessId,          // 使用现有业务ID
  businessConfigCode: 'LOAN_APPLY',
  sysId: 'LOAN_SYSTEM'
}

// ✅ 推荐：继承附件的参数配置
const inheritParams = {
  businessId: newBusinessId,
  businessConfigCode: 'LOAN_APPROVE',
  sysId: 'LOAN_SYSTEM',
  preBusinessId: previousBusinessId,       // 继承前置业务附件
  preSysId: 'LOAN_SYSTEM'
}
```

#### 推荐：业务ID生成策略

```javascript
// ✅ 推荐：标准的业务ID生成
const generateBusinessId = () => {
  const prefix = 'BIZ'
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 6)
  return `${prefix}_${timestamp}_${random}`
}

// ✅ 推荐：带业务类型的ID生成
const generateTypedBusinessId = (businessType) => {
  const typeMap = {
    'LOAN_APPLY': 'LA',
    'LOAN_APPROVE': 'LAP',
    'LOAN_REVIEW': 'LR'
  }
  const prefix = typeMap[businessType] || 'BIZ'
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
  const sequence = String(Date.now()).slice(-6)
  return `${prefix}_${date}_${sequence}`
}
```

### 3. 状态控制最佳实践

#### 推荐：根据业务状态控制组件行为

```javascript
// ✅ 推荐：基于业务状态的配置
const getFileTableConfig = (businessStatus, userRole) => {
  const baseConfig = {
    params: {
      businessId: currentBusinessId,
      businessConfigCode: getBusinessConfig(businessStatus),
      sysId: 'LOAN_SYSTEM'
    }
  }
  
  switch (businessStatus) {
    case 'DRAFT':
      return {
        ...baseConfig,
        onlyShow: false,
        isVerification: false,        // 草稿状态不强制校验
        hideAddBtn: false
      }
      
    case 'SUBMITTED':
      return {
        ...baseConfig,
        onlyShow: true,               // 已提交状态只读
        hideAddBtn: true,
        hideColumns: ['actions']
      }
      
    case 'UNDER_REVIEW':
      return {
        ...baseConfig,
        onlyShow: userRole !== 'REVIEWER',  // 审核人可操作
        isAiAudit: true,              // 启用AI审核
        hideAddBtn: userRole !== 'REVIEWER'
      }
      
    case 'APPROVED':
    case 'REJECTED':
      return {
        ...baseConfig,
        onlyShow: true,               // 终态只读
        hideAddBtn: true,
        hideColumns: ['actions']
      }
      
    default:
      return baseConfig
  }
}
```

#### 推荐：权限控制集成

```javascript
// ✅ 推荐：基于权限的功能控制
const getPermissionBasedConfig = (permissions) => {
  return {
    onlyShow: !permissions.includes('FILE_EDIT'),
    hideAddBtn: !permissions.includes('FILE_UPLOAD'),
    hideDownload: !permissions.includes('FILE_DOWNLOAD'),
    hideColumns: permissions.includes('FILE_VIEW_DETAIL') ? [] : ['uploadUser', 'uploadTime'],
    forceEditFileCode: permissions.includes('FILE_FORCE_EDIT') ? ['CONTRACT'] : []
  }
}
```

### 4. 校验处理最佳实践

#### 推荐：完善的校验回调处理

```javascript
// ✅ 推荐：详细的校验回调
const validationCallback = (isValid, missingFiles, allFiles) => {
  if (!isValid) {
    // 详细的错误提示
    const missingNames = missingFiles.map(f => f.fileConfigName).join('、')
    ElNotification.warning({
      title: '附件校验失败',
      message: `请上传必传附件：${missingNames}`,
      duration: 5000,
      showClose: true
    })
    
    // 记录校验失败日志
    console.warn('附件校验失败:', {
      missingFiles: missingFiles.map(f => ({
        code: f.fileConfigCode,
        name: f.fileConfigName
      })),
      currentFiles: allFiles.length
    })
    
    return false
  } else {
    // 校验成功提示
    ElMessage.success('附件校验通过')
    return true
  }
}

// ✅ 推荐：表单集成校验
const formValidationRules = {
  attachments: [
    {
      validator: async (rule, value, callback) => {
        try {
          const isValid = await fileTableRef.value?.verification()
          if (!isValid) {
            callback(new Error('请完善必传附件'))
          } else {
            callback()
          }
        } catch (error) {
          callback(new Error('附件校验失败'))
        }
      },
      trigger: 'change'
    }
  ]
}
```

### 5. 事件处理最佳实践

#### 推荐：完整的事件处理

```javascript
// ✅ 推荐：文件变化事件处理
const handleFileChange = (fileList) => {
  // 更新表单数据
  formData.attachments = fileList
  
  // 触发表单验证
  formRef.value?.validateField('attachments')
  
  // 统计信息
  const stats = {
    total: fileList.length,
    required: fileList.filter(f => f.isRequired).length,
    uploaded: fileList.filter(f => f.attachmentInfos?.length > 0).length
  }
  
  // 更新UI状态
  updateAttachmentStats(stats)
  
  // 记录操作日志
  logOperation('FILE_LIST_CHANGED', stats)
}

// ✅ 推荐：文件上传成功处理
const handleFileUpload = (fileInfo) => {
  ElMessage.success(`文件 ${fileInfo.fileName} 上传成功`)
  
  // 更新业务状态
  updateBusinessStatus('FILE_UPLOADED')
  
  // 发送通知
  if (fileInfo.isRequired) {
    ElNotification.success({
      title: '必传附件上传成功',
      message: `${fileInfo.fileConfigName} 已上传完成`
    })
  }
  
  // 记录审计日志
  auditLog('FILE_UPLOAD', {
    fileId: fileInfo.attachmentInfoId,
    fileName: fileInfo.fileName,
    fileType: fileInfo.fileType,
    fileSize: fileInfo.fileSize
  })
}

// ✅ 推荐：文件删除处理
const handleFileDelete = (fileInfo) => {
  ElMessage.info(`文件 ${fileInfo.fileName} 已删除`)
  
  // 检查是否为必传件
  if (fileInfo.isRequired) {
    ElNotification.warning({
      title: '必传附件已删除',
      message: `请重新上传 ${fileInfo.fileConfigName}`
    })
  }
  
  // 记录删除日志
  auditLog('FILE_DELETE', {
    fileId: fileInfo.attachmentInfoId,
    fileName: fileInfo.fileName
  })
}
```

### 6. 错误处理最佳实践

#### 推荐：完善的错误处理机制

```javascript
// ✅ 推荐：API错误处理
const handleApiError = (error, operation) => {
  const errorMap = {
    'FILE_UPLOAD': '文件上传失败',
    'FILE_DELETE': '文件删除失败',
    'FILE_LIST': '获取文件列表失败',
    'FILE_DOWNLOAD': '文件下载失败'
  }
  
  const message = errorMap[operation] || '操作失败'
  
  ElMessage.error({
    message: `${message}：${error.message || '未知错误'}`,
    duration: 5000,
    showClose: true
  })
  
  // 记录错误日志
  console.error(`${operation} 失败:`, error)
  
  // 发送错误报告（生产环境）
  if (process.env.NODE_ENV === 'production') {
    reportError(operation, error)
  }
}

// ✅ 推荐：文件校验错误处理
const handleValidationError = (error) => {
  if (error.code === 'FILE_SIZE_EXCEEDED') {
    ElMessage.warning('文件大小超出限制')
  } else if (error.code === 'FILE_TYPE_NOT_ALLOWED') {
    ElMessage.warning('不支持的文件类型')
  } else if (error.code === 'FILE_COUNT_EXCEEDED') {
    ElMessage.warning('文件数量超出限制')
  } else {
    ElMessage.error('文件校验失败')
  }
}
```

## 避免的用法和常见错误

### 1. 避免的配置错误

```javascript
// ❌ 避免：缺少必要参数
const wrongConfig1 = {
  params: {
    // 缺少 businessId
    businessConfigCode: 'LOAN_APPLY'
  }
}

// ❌ 避免：参数类型错误
const wrongConfig2 = {
  params: {
    businessId: 123,                     // 应该是字符串
    businessConfigCode: null             // 不应该为 null
  }
}

// ❌ 避免：在非工作流场景使用
const wrongUsage = {
  // 普通文件上传场景错误使用 FuniFileTable
  params: {
    businessId: 'fake_id',               // 伪造的业务ID
    businessConfigCode: 'GENERAL'
  }
}
```

### 2. 避免的状态管理错误

```javascript
// ❌ 避免：状态不一致
const wrongStateManagement = () => {
  // 业务状态已经是已提交，但仍允许编辑
  if (businessStatus === 'SUBMITTED') {
    return {
      onlyShow: false,                   // 错误：应该是只读
      hideAddBtn: false                  // 错误：应该隐藏添加按钮
    }
  }
}

// ❌ 避免：权限检查不充分
const insufficientPermissionCheck = () => {
  // 没有检查用户权限就允许操作
  return {
    onlyShow: false,                     // 应该基于权限判断
    hideAddBtn: false
  }
}
```

### 3. 避免的性能问题

```javascript
// ❌ 避免：频繁的API调用
const wrongApiUsage = () => {
  // 每次文件变化都调用API
  const handleFileChange = (fileList) => {
    fileList.forEach(file => {
      api.updateFileStatus(file.id)      // 频繁API调用
    })
  }
}

// ✅ 推荐：批量处理
const correctApiUsage = () => {
  const handleFileChange = (fileList) => {
    // 批量更新或防抖处理
    debounce(() => {
      const fileIds = fileList.map(f => f.id)
      api.batchUpdateFileStatus(fileIds)
    }, 500)()
  }
}
```

## 业务场景最佳实践

### 1. 贷款申请流程

```javascript
// 完整的贷款申请附件管理
const loanApplyFileManagement = {
  // 申请阶段
  apply: {
    params: {
      businessId: generateBusinessId('LOAN_APPLY'),
      businessConfigCode: 'LOAN_APPLY',
      sysId: 'LOAN_SYSTEM'
    },
    isVerification: true,
    callbackFun: (isValid, missingFiles) => {
      if (!isValid) {
        showMissingFilesDialog(missingFiles)
        return false
      }
      return true
    }
  },
  
  // 审核阶段
  review: {
    params: {
      businessId: existingBusinessId,
      businessConfigCode: 'LOAN_REVIEW',
      sysId: 'LOAN_SYSTEM',
      preBusinessId: applyBusinessId    // 继承申请阶段附件
    },
    onlyShow: !hasReviewPermission(),
    isAiAudit: true,
    hideAddBtn: !hasReviewPermission()
  },
  
  // 放款阶段
  disbursement: {
    params: {
      businessId: disbursementBusinessId,
      businessConfigCode: 'LOAN_DISBURSEMENT',
      sysId: 'LOAN_SYSTEM',
      preBusinessId: reviewBusinessId
    },
    forceEditFileCode: ['DISBURSEMENT_VOUCHER'],
    isVerification: true
  }
}
```

### 2. 多系统集成

```javascript
// 跨系统附件管理
const crossSystemFileManagement = {
  // 主系统
  mainSystem: {
    params: {
      businessId: mainBusinessId,
      businessConfigCode: 'MAIN_PROCESS',
      sysId: 'MAIN_SYSTEM'
    }
  },
  
  // 子系统继承
  subSystem: {
    params: {
      businessId: subBusinessId,
      businessConfigCode: 'SUB_PROCESS',
      sysId: 'SUB_SYSTEM',
      preBusinessId: mainBusinessId,
      preSysId: 'MAIN_SYSTEM'          // 跨系统继承
    }
  }
}
```

### 3. 移动端适配

```javascript
// 移动端优化配置
const mobileOptimizedConfig = {
  params: {
    businessId: currentBusinessId,
    businessConfigCode: 'MOBILE_PROCESS',
    sysId: 'MOBILE_SYSTEM'
  },
  hideColumns: [
    'uploadUser',                        // 移动端隐藏不重要列
    'uploadTime',
    'fileSize'
  ],
  uploadFileParams: {
    compress: true,                      // 移动端压缩上传
    quality: 0.8
  }
}
```

## 性能优化建议

1. **合理使用继承**：避免不必要的附件继承，减少数据传输
2. **批量操作**：文件操作使用批量API，减少请求次数
3. **懒加载**：大文件列表使用分页或虚拟滚动
4. **缓存策略**：合理缓存文件配置和用户权限信息
5. **压缩上传**：移动端或大文件场景启用压缩上传

## 安全性建议

1. **权限校验**：前后端都要进行权限校验
2. **文件类型限制**：严格限制允许上传的文件类型
3. **文件大小限制**：设置合理的文件大小限制
4. **病毒扫描**：上传文件进行病毒扫描
5. **水印保护**：敏感文件添加水印保护

## 可访问性建议

1. **键盘导航**：确保所有操作支持键盘访问
2. **屏幕阅读器**：为重要元素添加适当的 aria 标签
3. **颜色对比度**：确保文字和背景有足够的对比度
4. **错误提示**：提供清晰的错误信息和操作指导
