# FuniFileTable 配置结构

## 基础配置结构

```typescript
interface FuniFileTableConfig {
  // 工作流参数配置（必需）
  params: FileTableParams;
  
  // 显示控制
  onlyShow?: boolean;                    // 是否只读模式
  hideColumns?: string[];                // 隐藏的列名数组
  hideAddBtn?: boolean;                  // 是否隐藏添加按钮
  hideDownload?: boolean;                // 是否隐藏下载按钮
  
  // 校验配置
  isVerification?: boolean;              // 是否启用必传件校验
  callbackFun?: Function;                // 校验回调函数
  forceEditFileCode?: string[];          // 强制允许编辑的要件编码数组
  
  // AI审核配置
  isAiAudit?: boolean;                   // 是否启用AI审核功能
  
  // 自定义API配置
  fileListUrl?: string;                  // 自定义文件列表API地址
  delFileUrl?: string;                   // 自定义删除文件API地址
  uploadFileUrl?: string;                // 自定义上传文件API地址
  uploadFileParams?: Record<string, any>; // 上传文件额外参数
  checkFileUrl?: string;                 // 自定义预览文件API地址
  checkFileParams?: Record<string, any>; // 预览文件参数
  downloadFileUrl?: string;              // 自定义下载文件API地址
  downloadParams?: Record<string, any>;  // 下载文件参数
  
  // 自定义事件处理
  checkFileFn?: Function;                // 自定义查看文件方法
}

// 工作流参数配置
interface FileTableParams {
  businessId: string;                   // 工作流业务实例ID（必需）
  businessConfigCode?: string;          // 业务配置编码
  sysId?: string;                       // 系统ID
  preBusinessId?: string;               // 前置业务ID（继承附件用）
  preSysId?: string;                    // 前置系统ID（继承附件用）
  [key: string]: any;                   // 其他业务参数
}

// 文件信息结构
interface FileInfo {
  attachmentInfoId: string;             // 附件ID
  fileName: string;                     // 文件名
  fileSize: number;                     // 文件大小（字节）
  fileType: string;                     // 文件类型
  filePath: string;                     // 文件路径
  uploadTime: string;                   // 上传时间
  uploadUser: string;                   // 上传用户
  businessFileInstanceInfoId: string;   // 业务文件实例ID
  fileConfigCode: string;               // 文件配置编码
  isRequired: boolean;                  // 是否必传
  isEdit: boolean;                      // 是否可编辑
  isByAiCheck: boolean;                 // 是否通过AI审核
}

// 文件配置结构
interface FileConfig {
  fileConfigCode: string;               // 文件配置编码
  fileConfigName: string;               // 文件配置名称
  isRequired: boolean;                  // 是否必传
  maxFileCount: number;                 // 最大文件数量
  maxFileSize: number;                  // 最大文件大小（MB）
  allowFileTypes: string[];             // 允许的文件类型
  description: string;                  // 描述信息
}
```

## 详细配置说明

### 1. params 工作流参数配置

工作流附件组件的核心配置，用于标识业务实例和获取附件配置。

```typescript
// 基础工作流配置
const basicParams = {
  businessId: 'BIZ_20241219_001',        // 必需：业务实例ID
  businessConfigCode: 'LOAN_APPLY',      // 业务配置编码
  sysId: 'LOAN_SYSTEM'                   // 系统ID
}

// 带继承配置的工作流参数
const inheritParams = {
  businessId: 'BIZ_20241219_002',
  businessConfigCode: 'LOAN_APPROVE',
  sysId: 'LOAN_SYSTEM',
  preBusinessId: 'BIZ_20241219_001',     // 继承前置业务的附件
  preSysId: 'LOAN_SYSTEM'                // 前置系统ID
}

// 带额外业务参数
const extendedParams = {
  businessId: 'BIZ_20241219_003',
  businessConfigCode: 'LOAN_REVIEW',
  sysId: 'LOAN_SYSTEM',
  customField1: 'value1',                // 自定义业务字段
  customField2: 'value2'
}
```

### 2. 显示控制配置

控制组件的显示状态和可用功能。

```typescript
// 只读模式配置（详情页/审核页）
const readOnlyConfig = {
  onlyShow: true,                        // 只读模式
  hideAddBtn: true,                      // 隐藏添加按钮
  hideDownload: false                    // 显示下载按钮
}

// 编辑模式配置
const editConfig = {
  onlyShow: false,                       // 编辑模式
  hideAddBtn: false,                     // 显示添加按钮
  hideColumns: ['uploadUser', 'uploadTime'] // 隐藏指定列
}

// 自定义显示配置
const customDisplayConfig = {
  hideColumns: [
    'fileSize',                          // 隐藏文件大小列
    'uploadTime',                        // 隐藏上传时间列
    'uploadUser'                         // 隐藏上传用户列
  ]
}
```

### 3. 校验配置

配置必传件校验和回调处理。

```typescript
// 基础校验配置
const validationConfig = {
  isVerification: true,                  // 启用校验
  callbackFun: (isValid, missingFiles) => {
    if (!isValid) {
      console.log('缺少必传件:', missingFiles)
      ElMessage.warning('请上传必传附件')
    }
  }
}

// 强制编辑配置
const forceEditConfig = {
  forceEditFileCode: [
    'ID_CARD',                           // 身份证
    'BANK_CARD',                         // 银行卡
    'INCOME_PROOF'                       // 收入证明
  ]
}

// 完整校验配置
const fullValidationConfig = {
  isVerification: true,
  callbackFun: (isValid, missingFiles, allFiles) => {
    if (isValid) {
      ElMessage.success('附件校验通过')
    } else {
      const missingNames = missingFiles.map(f => f.fileConfigName).join('、')
      ElMessage.error(`请上传必传附件：${missingNames}`)
    }
  },
  forceEditFileCode: ['SPECIAL_DOC']
}
```

### 4. AI审核配置

配置AI智能审核功能。

```typescript
// AI审核配置
const aiAuditConfig = {
  isAiAudit: true,                       // 启用AI审核
  params: {
    businessId: 'BIZ_20241219_001',
    aiAuditLevel: 'HIGH',                // AI审核级别
    autoApprove: false                   // 是否自动通过
  }
}
```

### 5. 自定义API配置

自定义各种API接口地址和参数。

```typescript
// 自定义API配置
const customApiConfig = {
  fileListUrl: '/custom/api/file/list',
  delFileUrl: '/custom/api/file/delete',
  uploadFileUrl: '/custom/api/file/upload',
  uploadFileParams: {
    module: 'loan',                      // 业务模块
    category: 'document'                 // 文件分类
  },
  checkFileUrl: '/custom/api/file/preview',
  checkFileParams: {
    watermark: true                      // 添加水印
  },
  downloadFileUrl: '/custom/api/file/download',
  downloadParams: {
    audit: true                          // 下载审计
  }
}
```

## 常用配置组合

### 1. 新建页面配置

```typescript
const createPageConfig = {
  params: {
    businessId: newBusinessId,
    businessConfigCode: 'LOAN_APPLY',
    sysId: 'LOAN_SYSTEM'
  },
  onlyShow: false,
  isVerification: true,
  callbackFun: (isValid, missingFiles) => {
    if (!isValid) {
      ElMessage.warning('请上传所有必传附件后再提交')
      return false
    }
    return true
  }
}
```

### 2. 编辑页面配置

```typescript
const editPageConfig = {
  params: {
    businessId: existingBusinessId,
    businessConfigCode: 'LOAN_APPLY',
    sysId: 'LOAN_SYSTEM'
  },
  onlyShow: false,
  isVerification: true,
  forceEditFileCode: ['CONTRACT', 'AGREEMENT']
}
```

### 3. 详情页面配置

```typescript
const detailPageConfig = {
  params: {
    businessId: businessId,
    businessConfigCode: 'LOAN_APPLY',
    sysId: 'LOAN_SYSTEM'
  },
  onlyShow: true,
  hideAddBtn: true,
  hideColumns: ['actions']
}
```

### 4. 审核页面配置

```typescript
const auditPageConfig = {
  params: {
    businessId: businessId,
    businessConfigCode: 'LOAN_APPROVE',
    sysId: 'LOAN_SYSTEM'
  },
  onlyShow: true,
  isAiAudit: true,
  hideAddBtn: true
}
```

### 5. 继承附件配置

```typescript
const inheritConfig = {
  params: {
    businessId: newBusinessId,
    businessConfigCode: 'LOAN_APPROVE',
    sysId: 'LOAN_SYSTEM',
    preBusinessId: previousBusinessId,    // 继承前置业务附件
    preSysId: 'LOAN_SYSTEM'
  },
  onlyShow: false,
  isVerification: true
}
```

## 最佳实践建议

### 1. 参数配置建议

- **businessId**: 必须唯一，建议使用业务前缀+时间戳+序号的格式
- **businessConfigCode**: 与后端配置系统保持一致
- **sysId**: 标识系统来源，便于数据隔离和统计

### 2. 显示控制建议

- 详情页和审核页设置 `onlyShow: true`
- 根据业务需要合理隐藏不必要的列
- 审核页面可以隐藏操作列但保留下载功能

### 3. 校验配置建议

- 生产环境建议启用 `isVerification`
- 提供友好的校验回调提示
- 合理配置强制编辑的文件类型

### 4. 性能优化建议

- 大文件上传时配置合适的 `uploadFileParams`
- 预览大文件时考虑添加水印等参数
- 合理设置文件大小和数量限制
