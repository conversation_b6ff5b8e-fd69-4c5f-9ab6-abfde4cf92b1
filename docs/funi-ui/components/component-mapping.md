# 业务场景与组件映射关系

## 页面类型映射

### 强制映射（必须使用）
- **列表页** → FuniListPageV2 ✅
- **详情页** → FuniDetail ✅
- **新建页** → FuniDetail ✅
- **编辑页** → FuniDetail ✅

### 可选映射
- **搜索页** → FuniSearch + FuniCurd
- **表单页** → FuniForm
- **数据展示页** → FuniCurd
- **图表页** → FuniChart系列

## 功能需求映射

### 数据操作
- **数据录入** → FuniForm
- **数据搜索** → FuniSearch
- **数据展示** → FuniCurd / FuniCurdV2
- **数据导入导出** → FuniListPageV2（内置功能）

### 文件操作
- **工作流附件管理** → FuniFileTable（条件：需businessId）
- **普通文件上传** → FuniFileTable/upload.vue（手动配置）
- **图片预览** → FuniImageView
- **文件预览** → FuniPreview

### 内容编辑
- **富文本编辑** → FuniEditor
- **代码编辑** → FuniCodemirror
- **表单设计** → FuniFormEngine

### 地图功能
- **地图展示** → FuniOlMap
- **区域选择** → FuniRegion

### 权限控制
- **按钮权限** → FuniAuthButton + v-auth指令
- **角色用户选择** → FuniRUOC

### 工作流功能
- **工作流审核** → FuniAuditButtomBtn（动态配置）+ FuniBusAuditDrawer
- **流程图展示** → FuniBpmn

## 工作流场景特殊映射

### 工作流新建页面
```vue
<template>
  <FuniDetail :steps="steps" bizName="新建">
    <!-- 第一步：基本信息 -->
    <template #basicInfo>
      <FuniForm :schema="basicSchema" v-model="formData" />
    </template>
    
    <!-- 第二步：附件上传（需要第一步返回businessId） -->
    <template #attachments>
      <FuniFileTable 
        v-if="businessId"
        :params="fileTableParams"
      />
      <div v-else class="placeholder">
        请先保存基本信息后再上传附件
      </div>
    </template>
  </FuniDetail>
</template>
```

### 工作流编辑页面
```vue
<template>
  <FuniDetail :steps="steps" bizName="编辑">
    <!-- 基本信息编辑 -->
    <template #basicInfo>
      <FuniForm :schema="basicSchema" v-model="formData" />
    </template>
    
    <!-- 附件管理 -->
    <template #attachments>
      <FuniFileTable :params="fileTableParams" />
    </template>
  </FuniDetail>
</template>
```

### 工作流详情页面
```vue
<template>
  <FuniDetail :steps="steps" bizName="详情">
    <!-- 基本信息展示 -->
    <template #basicInfo>
      <FuniForm :schema="basicSchema" v-model="formData" :readonly="true" />
    </template>
    
    <!-- 附件查看 -->
    <template #attachments>
      <FuniFileTable :params="fileTableParams" :onlyShow="true" />
    </template>
    
    <!-- 操作记录 -->
    <template #operationLog>
      <FuniOperationLog :businessId="businessId" />
    </template>
  </FuniDetail>
  
  <!-- 审核抽屉 -->
  <FuniBusAuditDrawer 
    :businessId="businessId"
    :sysId="sysId"
    :onlyShow="true"
  />
</template>
```

### 工作流审核页面
```vue
<template>
  <FuniDetail :steps="steps" bizName="审核">
    <!-- 基本信息查看 -->
    <template #basicInfo>
      <FuniForm :schema="basicSchema" v-model="formData" :readonly="true" />
    </template>
    
    <!-- 附件查看 -->
    <template #attachments>
      <FuniFileTable :params="fileTableParams" :onlyShow="true" />
    </template>
    
    <!-- 审核按钮插槽 -->
    <template #auditbtns="{ auditButtons }">
      <FuniAuditButtomBtn
        :auditButtons="auditButtons || mockAuditButtons"
        @audit-click="handleAuditClick"
      />
    </template>
  </FuniDetail>
  
  <!-- 审核抽屉 -->
  <FuniBusAuditDrawer 
    :businessId="businessId"
    :sysId="sysId"
    @audit-event="handleAuditEvent"
  />
</template>
```

## 文件组件选择决策

### 决策流程图
```mermaid
graph TD
    A[需要文件功能] --> B{是否有工作流}
    B -->|是| C{是否有businessId}
    C -->|是| D[使用 FuniFileTable]
    C -->|否| E[等待businessId生成<br/>显示占位提示]
    B -->|否| F[使用 FuniFileTable/upload.vue]
    
    D --> D1[自动获取附件配置]
    D --> D2[工作流模式渲染]
    F --> F1[手动配置上传参数]
    F --> F2[普通模式渲染]
```

### 使用条件对比

| 组件 | 使用场景 | 必需条件 | 配置方式 | 功能特点 |
|------|----------|----------|----------|----------|
| FuniFileTable | 工作流附件 | businessId | 自动获取 | 条件渲染、流程集成 |
| FuniFileTable/upload.vue | 普通上传 | 无 | 手动配置 | 通用上传、灵活配置 |

## 工作流集成判断条件

### 新建/编辑模式判断
```javascript
// 工作流新建/编辑判断逻辑
const isWorkflowMode = computed(() => {
  return route.meta.isWorkflow || !!route.meta.businessConfigCode
})

const shouldShowFileTable = computed(() => {
  return isWorkflowMode.value && !!businessId.value
})

// 多步骤结构
const steps = computed(() => {
  const baseSteps = [
    {
      title: '基本信息',
      slot: 'basicInfo'
    }
  ]
  
  // 工作流模式添加附件步骤
  if (isWorkflowMode.value) {
    baseSteps.push({
      title: '附件上传',
      slot: 'attachments'
    })
  }
  
  return baseSteps
})
```

### 详情/审核模式判断
```javascript
// 工作流详情/审核判断逻辑
const isWorkflowDetail = computed(() => {
  return !!route.query.businessId || !!businessData.value?.businessId
})

const isAuditMode = computed(() => {
  return (bizName.value === '审核' || bizName.value === 'audit') && 
         isWorkflowDetail.value
})

const shouldShowAuditComponents = computed(() => {
  return isAuditMode.value && !!businessId.value
})
```

## 组件选择决策树

### 页面级组件选择
```mermaid
graph TD
    A[开始] --> B{页面类型}
    B --> C[列表页]
    B --> D[详情页]
    B --> E[新建页]
    B --> F[编辑页]
    B --> G[其他页面]
    
    C --> C1[FuniListPageV2<br/>强制使用]
    D --> D1[FuniDetail<br/>强制使用]
    E --> E1[FuniDetail<br/>强制使用]
    F --> F1[FuniDetail<br/>强制使用]
    G --> G1[根据具体需求选择]
```

### 表单组件选择
```mermaid
graph TD
    A[表单需求] --> B{表单复杂度}
    B --> C[简单表单<br/>< 10个字段]
    B --> D[复杂表单<br/>> 10个字段]
    B --> E[动态表单]
    
    C --> C1[直接使用ElementPlus]
    D --> D1[FuniForm]
    E --> E1[FuniFormEngine]
```

### 数据展示组件选择
```mermaid
graph TD
    A[数据展示需求] --> B{展示类型}
    B --> C[表格数据]
    B --> D[图表数据]
    B --> E[列表数据]
    
    C --> C1{功能需求}
    C1 --> C2[基础表格] --> C3[FuniCurd]
    C1 --> C4[高级表格] --> C5[FuniCurdV2]
    
    D --> D1[FuniChart系列]
    E --> E1[FuniListPageV2]
```

## 最佳实践建议

### 1. 页面结构规范
```vue
<!-- 推荐的页面结构 -->
<template>
  <!-- 列表页 -->
  <FuniListPageV2 :cardTab="cardTabConfig" />
  
  <!-- 详情页 -->
  <FuniDetail :steps="steps" :bizName="bizName">
    <template #stepSlot>
      <!-- 步骤内容 -->
    </template>
  </FuniDetail>
</template>
```

### 2. 工作流组件使用规范
```vue
<!-- 工作流组件正确使用方式 -->
<template>
  <FuniDetail bizName="审核">
    <!-- 附件组件 -->
    <FuniFileTable 
      v-if="businessId"
      :params="fileTableParams"
    />
    
    <!-- 审核按钮 -->
    <template #auditbtns>
      <FuniAuditButtomBtn 
        :auditButtons="auditButtons"
        @audit-click="handleAudit"
      />
    </template>
  </FuniDetail>
  
  <!-- 审核抽屉 -->
  <FuniBusAuditDrawer 
    :businessId="businessId"
    :sysId="sysId"
  />
</template>
```

### 3. 避免的错误用法
```vue
<!-- ❌ 错误：列表页不使用FuniListPageV2 -->
<template>
  <div>
    <FuniSearch />
    <FuniCurd />
  </div>
</template>

<!-- ❌ 错误：详情页不使用FuniDetail -->
<template>
  <div>
    <FuniForm />
  </div>
</template>

<!-- ❌ 错误：非工作流场景使用FuniFileTable -->
<template>
  <FuniFileTable :params="params" />
</template>

<!-- ✅ 正确：非工作流场景使用upload.vue -->
<template>
  <FuniFileTableUpload :config="uploadConfig" />
</template>
```

## 组件组合使用方案

### 常见组合1：标准管理页面
- FuniListPageV2 + FuniDetail + FuniForm

### 常见组合2：工作流管理页面
- FuniListPageV2 + FuniDetail + FuniFileTable + FuniAuditButtomBtn + FuniBusAuditDrawer

### 常见组合3：数据分析页面
- FuniSearch + FuniChart + FuniCurd

### 常见组合4：内容管理页面
- FuniListPageV2 + FuniDetail + FuniEditor + FuniImageView
