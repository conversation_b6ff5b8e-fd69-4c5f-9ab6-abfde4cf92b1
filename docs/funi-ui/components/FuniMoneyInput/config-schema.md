# FuniMoneyInput 配置结构定义

## 基础配置结构

### FuniMoneyInputProps 接口定义

```typescript
interface FuniMoneyInputProps {
  // 基础配置
  modelValue?: string | number
  isEdit?: boolean
  
  // 显示配置
  moneyCapitalShow?: boolean
  showPrefix?: boolean
  precision?: number
  unit?: number
  defaultZero?: boolean
  
  // 金额格式化配置
  currency?: string
  showCurrency?: boolean
  showSeparator?: boolean
  separator?: string
  allowNegative?: boolean
  formatOnBlur?: boolean
  
  // 数值范围配置
  min?: number
  max?: number
  step?: number
  showControls?: boolean
  controlsPosition?: 'right' | 'left'
  
  // ElementPlus el-input-number 透传属性
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  size?: 'large' | 'default' | 'small'
  clearable?: boolean
  controls?: boolean
  name?: string
  label?: string
  id?: string
  tabindex?: number
  validateEvent?: boolean
}
```

### 金额格式化配置

```typescript
interface MoneyFormatConfig {
  // 货币符号配置
  currency: string           // 货币符号，如 '¥', '$', '€'
  showCurrency: boolean      // 是否显示货币符号
  currencyPosition: 'prefix' | 'suffix'  // 货币符号位置
  
  // 数值格式配置
  precision: number          // 小数位数
  showSeparator: boolean     // 是否显示千分位分隔符
  separator: string          // 千分位分隔符，默认为 ','
  decimalSeparator: string   // 小数点分隔符，默认为 '.'
  
  // 特殊处理
  allowNegative: boolean     // 是否允许负数
  formatOnBlur: boolean      // 是否在失焦时格式化
  unit: number              // 单位转换，如万元时设为10000
}
```

### 验证规则配置

```typescript
interface MoneyValidationConfig {
  // 数值范围
  min?: number              // 最小值
  max?: number              // 最大值
  
  // 自定义验证
  validator?: (value: number) => boolean | string
  
  // 错误提示
  errorMessages?: {
    required?: string
    min?: string
    max?: string
    invalid?: string
  }
}
```

## 详细配置说明

### 基础配置项

#### modelValue
- **类型**: `string | number`
- **默认值**: `undefined`
- **说明**: 绑定值，支持字符串和数字类型
- **示例**:
  ```javascript
  // 数字类型
  const amount = ref(1234.56)
  
  // 字符串类型
  const amount = ref('1234.56')
  ```

#### isEdit
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否为编辑模式，false时为只读显示
- **示例**:
  ```javascript
  // 编辑模式
  isEdit: true
  
  // 只读模式
  isEdit: false
  ```

#### precision
- **类型**: `number`
- **默认值**: `2`
- **说明**: 小数位数精度
- **示例**:
  ```javascript
  // 2位小数
  precision: 2  // 1234.56
  
  // 无小数
  precision: 0  // 1234
  
  // 3位小数
  precision: 3  // 1234.567
  ```

### 显示配置项

#### moneyCapitalShow
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否显示金额大写
- **示例**:
  ```javascript
  // 显示大写：壹千贰佰叁拾肆元伍角陆分
  moneyCapitalShow: true
  
  // 不显示大写
  moneyCapitalShow: false
  ```

#### showPrefix
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否显示货币符号前缀
- **示例**:
  ```javascript
  // 显示前缀：¥1,234.56
  showPrefix: true
  
  // 不显示前缀：1,234.56
  showPrefix: false
  ```

#### unit
- **类型**: `number`
- **默认值**: `1`
- **说明**: 单位转换倍数
- **示例**:
  ```javascript
  // 元为单位
  unit: 1
  
  // 万元为单位
  unit: 10000
  
  // 分为单位
  unit: 0.01
  ```

#### defaultZero
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 空值时是否默认显示0
- **示例**:
  ```javascript
  // 空值显示0
  defaultZero: true
  
  // 空值显示空
  defaultZero: false
  ```

### 格式化配置项

#### currency
- **类型**: `string`
- **默认值**: `'¥'`
- **说明**: 货币符号
- **示例**:
  ```javascript
  // 人民币
  currency: '¥'
  
  // 美元
  currency: '$'
  
  // 欧元
  currency: '€'
  ```

#### showSeparator
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否显示千分位分隔符
- **示例**:
  ```javascript
  // 显示分隔符：1,234,567.89
  showSeparator: true
  
  // 不显示分隔符：1234567.89
  showSeparator: false
  ```

#### separator
- **类型**: `string`
- **默认值**: `','`
- **说明**: 千分位分隔符
- **示例**:
  ```javascript
  // 逗号分隔：1,234,567
  separator: ','
  
  // 空格分隔：1 234 567
  separator: ' '
  
  // 点分隔：1.234.567
  separator: '.'
  ```

## 常用配置组合示例

### 基础金额输入
```javascript
const basicConfig = {
  modelValue: 0,
  precision: 2,
  showPrefix: true,
  moneyCapitalShow: true,
  isEdit: true
}
```

### 只读金额显示
```javascript
const readonlyConfig = {
  modelValue: 1234.56,
  isEdit: false,
  showPrefix: true,
  moneyCapitalShow: false,
  precision: 2
}
```

### 万元单位配置
```javascript
const tenThousandConfig = {
  modelValue: 12.3456,  // 实际表示123456元
  unit: 10000,
  precision: 4,
  showPrefix: true,
  moneyCapitalShow: true
}
```

### 美元配置
```javascript
const usdConfig = {
  modelValue: 1234.56,
  currency: '$',
  showPrefix: true,
  precision: 2,
  separator: ',',
  moneyCapitalShow: false
}
```

### 欧元配置（空格分隔）
```javascript
const eurConfig = {
  modelValue: 1234.56,
  currency: '€',
  showPrefix: true,
  precision: 2,
  separator: ' ',
  moneyCapitalShow: false
}
```

### 带范围限制的配置
```javascript
const rangeConfig = {
  modelValue: 0,
  min: 0.01,
  max: 999999.99,
  precision: 2,
  showControls: true,
  step: 0.01
}
```

### 整数金额配置
```javascript
const integerConfig = {
  modelValue: 1000,
  precision: 0,
  showPrefix: true,
  moneyCapitalShow: true,
  step: 1
}
```

### 表单验证配置
```javascript
const formConfig = {
  modelValue: 0,
  precision: 2,
  validateEvent: true,
  placeholder: '请输入金额',
  clearable: true,
  size: 'default'
}
```

## 最佳实践建议

### 1. 精度设置建议
- **人民币**：通常使用2位小数（分）
- **日元**：通常使用0位小数（整数）
- **加密货币**：可能需要8位或更多小数位
- **大额交易**：考虑使用万元单位减少输入

### 2. 单位转换建议
- 根据业务场景选择合适的单位
- 大额资金建议使用万元或十万元单位
- 确保前端显示和后端存储单位一致
- 提供清晰的单位说明

### 3. 格式化建议
- 根据地区习惯选择分隔符
- 保持整个应用的货币格式一致
- 考虑国际化需求
- 提供格式化预览功能

### 4. 验证规则建议
- 设置合理的最小值和最大值
- 考虑业务场景的特殊限制
- 提供友好的错误提示
- 支持实时验证反馈

### 5. 用户体验建议
- 提供清晰的输入提示
- 支持键盘快捷操作
- 显示格式化后的预览
- 提供撤销和重置功能
