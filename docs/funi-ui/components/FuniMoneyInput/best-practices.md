# FuniMoneyInput 最佳实践

## 推荐用法和配置

### 1. 基础配置最佳实践

#### 选择合适的精度
```vue
<template>
  <!-- ✅ 推荐：根据业务场景选择合适的精度 -->
  
  <!-- 人民币：2位小数 -->
  <FuniMoneyInput
    v-model="cnyAmount"
    :precision="2"
    currency="¥"
    placeholder="请输入人民币金额"
  />
  
  <!-- 日元：无小数 -->
  <FuniMoneyInput
    v-model="jpyAmount"
    :precision="0"
    currency="¥"
    placeholder="请输入日元金额"
  />
  
  <!-- 加密货币：高精度 -->
  <FuniMoneyInput
    v-model="cryptoAmount"
    :precision="8"
    currency="BTC"
    placeholder="请输入比特币数量"
  />
  
  <!-- ❌ 不推荐：使用不合适的精度 -->
  <FuniMoneyInput
    v-model="amount"
    :precision="5"
    placeholder="人民币金额"
  />
</template>
```

#### 合理设置单位转换
```vue
<template>
  <!-- ✅ 推荐：大额资金使用万元单位 -->
  <FuniMoneyInput
    v-model="budgetAmount"
    :unit="10000"
    :precision="2"
    placeholder="请输入预算（万元）"
  />
  <span class="unit-hint">万元</span>
  
  <!-- ✅ 推荐：提供清晰的单位说明 -->
  <div class="amount-input-group">
    <label>注册资本：</label>
    <FuniMoneyInput
      v-model="capitalAmount"
      :unit="10000"
      :precision="4"
      placeholder="请输入注册资本"
    />
    <span class="unit-label">万元</span>
    <div class="help-text">
      实际金额：{{ (capitalAmount * 10000).toLocaleString() }} 元
    </div>
  </div>
</template>

<style scoped>
.unit-hint {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

.amount-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.help-text {
  width: 100%;
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style>
```

### 2. 显示模式最佳实践

#### 编辑与只读模式切换
```vue
<template>
  <div class="amount-display">
    <!-- ✅ 推荐：根据权限控制编辑模式 -->
    <FuniMoneyInput
      v-model="contractAmount"
      :isEdit="hasEditPermission && !isSubmitted"
      :moneyCapitalShow="true"
      :showPrefix="true"
      placeholder="请输入合同金额"
    />
    
    <!-- ✅ 推荐：只读模式下隐藏大写显示 -->
    <FuniMoneyInput
      v-model="displayAmount"
      :isEdit="false"
      :moneyCapitalShow="false"
      :showPrefix="true"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserPermissions } from '@/composables/usePermissions'

const { hasPermission } = useUserPermissions()

const contractAmount = ref(0)
const displayAmount = ref(123456.78)
const isSubmitted = ref(false)

const hasEditPermission = computed(() => 
  hasPermission('contract.edit')
)
</script>
```

#### 大写显示控制
```vue
<template>
  <div class="capital-display-examples">
    <!-- ✅ 推荐：重要金额显示大写 -->
    <div class="important-amount">
      <label>合同总金额：</label>
      <FuniMoneyInput
        v-model="contractTotal"
        :isEdit="false"
        :moneyCapitalShow="true"
        :showPrefix="true"
      />
    </div>
    
    <!-- ✅ 推荐：列表中的金额不显示大写 -->
    <div class="amount-list">
      <div v-for="item in amountList" :key="item.id" class="list-item">
        <span>{{ item.name }}：</span>
        <FuniMoneyInput
          :model-value="item.amount"
          :isEdit="false"
          :moneyCapitalShow="false"
          :showPrefix="true"
        />
      </div>
    </div>
    
    <!-- ✅ 推荐：表单输入时显示大写预览 -->
    <div class="form-input">
      <label>输入金额：</label>
      <FuniMoneyInput
        v-model="inputAmount"
        :isEdit="true"
        :moneyCapitalShow="true"
        :showPrefix="true"
        placeholder="请输入金额"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const contractTotal = ref(1234567.89)
const inputAmount = ref(0)

const amountList = ref([
  { id: 1, name: '项目A', amount: 50000 },
  { id: 2, name: '项目B', amount: 75000 },
  { id: 3, name: '项目C', amount: 30000 }
])
</script>
```

### 3. 数据处理最佳实践

#### 数值验证和范围控制
```vue
<template>
  <div class="validation-examples">
    <!-- ✅ 推荐：设置合理的数值范围 -->
    <el-form :model="form" :rules="rules" ref="formRef">
      <el-form-item label="订单金额" prop="orderAmount">
        <FuniMoneyInput
          v-model="form.orderAmount"
          :min="0.01"
          :max="999999.99"
          :precision="2"
          placeholder="请输入订单金额（0.01-999,999.99）"
          @change="validateOrderAmount"
        />
      </el-form-item>
      
      <el-form-item label="优惠金额" prop="discountAmount">
        <FuniMoneyInput
          v-model="form.discountAmount"
          :min="0"
          :max="form.orderAmount"
          :precision="2"
          placeholder="请输入优惠金额"
          @change="validateDiscountAmount"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const formRef = ref()

const form = reactive({
  orderAmount: 0,
  discountAmount: 0
})

const rules = {
  orderAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '订单金额不能小于0.01', trigger: 'blur' },
    { type: 'number', max: 999999.99, message: '订单金额不能超过999,999.99', trigger: 'blur' }
  ],
  discountAmount: [
    { type: 'number', min: 0, message: '优惠金额不能小于0', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value > form.orderAmount) {
          callback(new Error('优惠金额不能超过订单金额'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// ✅ 推荐：实时验证和提示
const validateOrderAmount = (value) => {
  if (value < 0.01) {
    ElMessage.warning('订单金额不能小于0.01元')
    return
  }
  
  if (value > 999999.99) {
    ElMessage.warning('订单金额不能超过999,999.99元')
    return
  }
  
  // 如果优惠金额超过订单金额，自动调整
  if (form.discountAmount > value) {
    form.discountAmount = value
    ElMessage.info('优惠金额已自动调整为订单金额')
  }
}

const validateDiscountAmount = (value) => {
  if (value > form.orderAmount) {
    form.discountAmount = form.orderAmount
    ElMessage.warning('优惠金额不能超过订单金额，已自动调整')
  }
}
</script>
```

#### 精度处理和计算
```javascript
// ✅ 推荐：使用专门的数值处理函数
const calculateTotal = (amounts) => {
  // 避免浮点数精度问题
  const total = amounts.reduce((sum, amount) => {
    return Math.round((sum + amount) * 100) / 100
  }, 0)
  
  return Number(total.toFixed(2))
}

// ✅ 推荐：统一的金额格式化函数
const formatAmount = (amount, options = {}) => {
  const {
    precision = 2,
    currency = '¥',
    showCurrency = true,
    locale = 'zh-CN'
  } = options
  
  const formattedNumber = Number(amount).toLocaleString(locale, {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  })
  
  return showCurrency ? `${currency}${formattedNumber}` : formattedNumber
}

// ❌ 避免：直接进行浮点数运算
const badCalculation = (a, b) => {
  return a + b  // 可能产生精度问题
}

// ✅ 推荐：安全的浮点数运算
const safeAdd = (a, b) => {
  return Math.round((a + b) * 100) / 100
}
```

### 4. 用户体验最佳实践

#### 输入提示和帮助信息
```vue
<template>
  <div class="user-experience-examples">
    <!-- ✅ 推荐：提供清晰的输入提示 -->
    <div class="input-with-help">
      <label>投资金额：</label>
      <FuniMoneyInput
        v-model="investmentAmount"
        :min="1000"
        :max="1000000"
        :precision="2"
        placeholder="请输入投资金额"
        @focus="showHelp = true"
        @blur="showHelp = false"
      />
      <div v-if="showHelp" class="help-tooltip">
        <p>投资金额范围：1,000 - 1,000,000 元</p>
        <p>支持小数点后2位</p>
      </div>
    </div>
    
    <!-- ✅ 推荐：实时显示格式化结果 -->
    <div class="input-with-preview">
      <label>输入金额：</label>
      <FuniMoneyInput
        v-model="previewAmount"
        :precision="2"
        placeholder="请输入金额"
      />
      <div class="preview-text">
        格式化显示：{{ formatAmount(previewAmount) }}
      </div>
    </div>
    
    <!-- ✅ 推荐：提供快捷金额选择 -->
    <div class="quick-amount-selector">
      <label>快速选择：</label>
      <div class="quick-buttons">
        <el-button 
          v-for="amount in quickAmounts" 
          :key="amount"
          size="small"
          @click="selectQuickAmount(amount)"
        >
          {{ formatAmount(amount) }}
        </el-button>
      </div>
      <FuniMoneyInput
        v-model="selectedAmount"
        :precision="2"
        placeholder="或手动输入金额"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const showHelp = ref(false)
const investmentAmount = ref(0)
const previewAmount = ref(0)
const selectedAmount = ref(0)

const quickAmounts = [1000, 5000, 10000, 50000, 100000]

const formatAmount = (amount) => {
  if (!amount) return '¥0.00'
  return `¥${Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`
}

const selectQuickAmount = (amount) => {
  selectedAmount.value = amount
}
</script>

<style scoped>
.input-with-help {
  position: relative;
}

.help-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 1000;
  font-size: 12px;
  color: #666;
}

.preview-text {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

.quick-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}
</style>
```

#### 错误处理和反馈
```vue
<template>
  <div class="error-handling-examples">
    <!-- ✅ 推荐：友好的错误提示 -->
    <div class="error-demo">
      <label>金额输入：</label>
      <FuniMoneyInput
        v-model="errorAmount"
        :min="0"
        :max="100000"
        :precision="2"
        placeholder="请输入金额（0-100,000）"
        @change="handleAmountChange"
        :class="{ 'error-input': hasError }"
      />
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
    </div>
    
    <!-- ✅ 推荐：加载状态处理 -->
    <div class="loading-demo">
      <label>保存中的金额：</label>
      <FuniMoneyInput
        v-model="savingAmount"
        :disabled="isSaving"
        :precision="2"
        placeholder="请输入金额"
      />
      <el-button 
        type="primary" 
        :loading="isSaving"
        @click="saveAmount"
      >
        {{ isSaving ? '保存中...' : '保存' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const errorAmount = ref(0)
const hasError = ref(false)
const errorMessage = ref('')
const savingAmount = ref(0)
const isSaving = ref(false)

// ✅ 推荐：详细的错误处理
const handleAmountChange = (value) => {
  hasError.value = false
  errorMessage.value = ''
  
  if (value < 0) {
    hasError.value = true
    errorMessage.value = '金额不能为负数'
    return
  }
  
  if (value > 100000) {
    hasError.value = true
    errorMessage.value = '金额不能超过100,000元'
    return
  }
  
  if (value > 0 && value < 0.01) {
    hasError.value = true
    errorMessage.value = '金额不能小于0.01元'
    return
  }
  
  // 验证通过
  ElMessage.success('金额输入有效')
}

// ✅ 推荐：异步操作的状态管理
const saveAmount = async () => {
  if (savingAmount.value <= 0) {
    ElMessage.warning('请输入有效金额')
    return
  }
  
  isSaving.value = true
  
  try {
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('金额保存成功')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
    console.error('保存错误:', error)
  } finally {
    isSaving.value = false
  }
}
</script>

<style scoped>
.error-input {
  border-color: #f56c6c !important;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
}

.loading-demo {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
```

## 避免的用法和常见错误

### 1. 精度设置错误

```vue
<!-- ❌ 错误：人民币使用过高精度 -->
<FuniMoneyInput
  v-model="amount"
  :precision="5"
  placeholder="人民币金额"
/>

<!-- ✅ 正确：人民币使用2位小数 -->
<FuniMoneyInput
  v-model="amount"
  :precision="2"
  placeholder="人民币金额"
/>
```

### 2. 单位转换混乱

```javascript
// ❌ 错误：前端显示和后端存储单位不一致
const saveData = () => {
  const data = {
    amount: displayAmount.value  // 前端显示万元，后端期望元
  }
  api.save(data)
}

// ✅ 正确：明确单位转换
const saveData = () => {
  const data = {
    amount: displayAmount.value * 10000  // 万元转换为元
  }
  api.save(data)
}
```

### 3. 浮点数精度问题

```javascript
// ❌ 错误：直接进行浮点数运算
const calculateTotal = (amounts) => {
  return amounts.reduce((sum, amount) => sum + amount, 0)
}

// ✅ 正确：使用精度安全的计算
const calculateTotal = (amounts) => {
  return amounts.reduce((sum, amount) => {
    return Math.round((sum + amount) * 100) / 100
  }, 0)
}
```

## 性能优化建议

### 1. 大量数据处理
```javascript
// ✅ 推荐：使用计算属性缓存格式化结果
const formattedData = computed(() => {
  return largeDataList.value.map(item => ({
    ...item,
    formattedAmount: formatAmount(item.amount)
  }))
})

// ✅ 推荐：防抖处理频繁计算
import { debounce } from 'lodash-es'

const debouncedCalculate = debounce((amounts) => {
  const total = calculateTotal(amounts)
  totalAmount.value = total
}, 300)
```

### 2. 内存管理
```javascript
// ✅ 推荐：及时清理不需要的数据
const cleanup = () => {
  largeAmountList.value = []
  calculationCache.clear()

  if (pendingRequest.value) {
    pendingRequest.value.abort()
    pendingRequest.value = null
  }
}

onUnmounted(() => {
  cleanup()
})
```

## 业务场景最佳实践

### 1. 财务系统
- 统一使用2位小数精度
- 重要金额显示大写
- 提供完整的审计日志
- 实现严格的权限控制

### 2. 电商系统
- 商品价格使用2位小数
- 支持多货币显示
- 提供价格比较功能
- 实现促销价格计算

### 3. 投资理财
- 支持高精度计算
- 提供收益率计算
- 实现风险提示
- 支持历史数据查询

## 总结

### 核心原则
1. **精度优先**：根据业务场景选择合适的精度
2. **用户体验**：提供清晰的输入提示和错误反馈
3. **数据安全**：使用安全的数值计算方法
4. **性能考虑**：在大数据场景下优化性能
5. **一致性**：保持整个应用的金额格式一致

### 开发建议
1. 在项目初期制定金额处理规范
2. 使用TypeScript提高代码质量
3. 编写完整的单元测试
4. 定期review金额相关的业务逻辑
5. 建立金额数据的监控和审计机制
