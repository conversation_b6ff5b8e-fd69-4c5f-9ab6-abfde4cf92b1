# FuniMoneyInput API文档

## 组件概述

FuniMoneyInput是基于ElementPlus的el-input封装的金额输入组件，支持金额格式化、千分位分隔符、货币符号、精度控制等功能，适用于财务和金额相关的输入场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| modelValue | Number/String | - | - | 绑定值 | el-input.model-value |
| precision | Number | 2 | - | 小数位数 | - |
| min | Number | 0 | - | 最小值 | - |
| max | Number | Infinity | - | 最大值 | - |
| currency | String | '¥' | - | 货币符号 | - |
| showCurrency | Boolean | true | - | 是否显示货币符号 | - |
| separator | String | ',' | - | 千分位分隔符 | - |
| showSeparator | Boolean | true | - | 是否显示千分位分隔符 | - |
| placeholder | String | '请输入金额' | - | 占位符文本 | el-input.placeholder |
| disabled | Boolean | false | - | 是否禁用 | el-input.disabled |
| readonly | Boolean | false | - | 是否只读 | el-input.readonly |
| size | String | 'default' | - | 组件尺寸 | el-input.size |
| clearable | Boolean | false | - | 是否可清空 | el-input.clearable |
| showWordLimit | Boolean | false | - | 是否显示字数统计 | el-input.show-word-limit |
| prefixIcon | String | '' | - | 前缀图标 | el-input.prefix-icon |
| suffixIcon | String | '' | - | 后缀图标 | el-input.suffix-icon |
| validateEvent | Boolean | true | - | 是否触发表单验证 | el-input.validate-event |
| inputStyle | Object | {} | - | 输入框样式 | el-input.input-style |
| allowNegative | Boolean | false | - | 是否允许负数 | - |
| autoFormat | Boolean | true | - | 是否自动格式化 | - |
| formatOnBlur | Boolean | true | - | 失焦时是否格式化 | - |
| strictMode | Boolean | false | - | 严格模式（只允许数字和小数点） | - |
| maxLength | Number | - | - | 最大输入长度 | el-input.maxlength |
| showControls | Boolean | false | - | 是否显示增减按钮 | - |
| controlsPosition | String | 'right' | - | 控制按钮位置 | - |
| step | Number | 1 | - | 步长 | - |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: Number | 值更新事件 | 输入值变化时 |
| change | value: Number | 值变化事件 | 输入值变化且失焦时 |
| input | value: String | 输入事件 | 用户输入时 |
| focus | event: Event | 获得焦点事件 | 输入框获得焦点时 |
| blur | event: Event | 失去焦点事件 | 输入框失去焦点时 |
| clear | - | 清空事件 | 点击清空按钮时 |
| keydown | event: KeyboardEvent | 键盘按下事件 | 按键按下时 |
| keyup | event: KeyboardEvent | 键盘抬起事件 | 按键抬起时 |
| exceed-max | value: Number | 超出最大值事件 | 输入值超出最大值时 |
| below-min | value: Number | 低于最小值事件 | 输入值低于最小值时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| focus | - | void | 使输入框获得焦点 |
| blur | - | void | 使输入框失去焦点 |
| select | - | void | 选中输入框文本 |
| clear | - | void | 清空输入框 |
| format | - | void | 手动格式化当前值 |
| getFormattedValue | - | String | 获取格式化后的显示值 |
| getRawValue | - | Number | 获取原始数值 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| prefix | - | 输入框头部内容 |
| suffix | - | 输入框尾部内容 |
| prepend | - | 输入框前置内容 |
| append | - | 输入框后置内容 |

## 使用示例

### 基础金额输入
```vue
<template>
  <div>
    <FuniMoneyInput
      v-model="amount"
      placeholder="请输入金额"
      @change="handleAmountChange"
    />
    <p>当前金额：{{ amount }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const amount = ref(0)

const handleAmountChange = (value) => {
  console.log('金额变化:', value)
}
</script>
```

### 自定义精度和货币符号
```vue
<template>
  <div class="money-inputs">
    <div class="input-group">
      <label>人民币（2位小数）：</label>
      <FuniMoneyInput
        v-model="cnyAmount"
        currency="¥"
        :precision="2"
        placeholder="请输入人民币金额"
      />
    </div>
    
    <div class="input-group">
      <label>美元（2位小数）：</label>
      <FuniMoneyInput
        v-model="usdAmount"
        currency="$"
        :precision="2"
        placeholder="请输入美元金额"
      />
    </div>
    
    <div class="input-group">
      <label>日元（无小数）：</label>
      <FuniMoneyInput
        v-model="jpyAmount"
        currency="¥"
        :precision="0"
        placeholder="请输入日元金额"
      />
    </div>
    
    <div class="input-group">
      <label>无货币符号：</label>
      <FuniMoneyInput
        v-model="noSymbolAmount"
        :show-currency="false"
        :precision="3"
        placeholder="请输入金额"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const cnyAmount = ref(1234.56)
const usdAmount = ref(999.99)
const jpyAmount = ref(10000)
const noSymbolAmount = ref(123.456)
</script>

<style scoped>
.money-inputs {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-group label {
  width: 150px;
  text-align: right;
}
</style>
```

### 带范围限制的金额输入
```vue
<template>
  <div>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="订单金额" prop="orderAmount">
        <FuniMoneyInput
          v-model="form.orderAmount"
          :min="0.01"
          :max="999999.99"
          placeholder="请输入订单金额"
          @exceed-max="handleExceedMax"
          @below-min="handleBelowMin"
        />
        <div class="help-text">金额范围：0.01 - 999,999.99</div>
      </el-form-item>
      
      <el-form-item label="优惠金额" prop="discountAmount">
        <FuniMoneyInput
          v-model="form.discountAmount"
          :min="0"
          :max="form.orderAmount"
          placeholder="请输入优惠金额"
        />
        <div class="help-text">不能超过订单金额</div>
      </el-form-item>
      
      <el-form-item label="实付金额">
        <FuniMoneyInput
          :model-value="actualAmount"
          readonly
          placeholder="自动计算"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const formRef = ref()

const form = reactive({
  orderAmount: 0,
  discountAmount: 0
})

const rules = {
  orderAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '订单金额不能小于0.01', trigger: 'blur' }
  ],
  discountAmount: [
    { type: 'number', min: 0, message: '优惠金额不能小于0', trigger: 'blur' }
  ]
}

const actualAmount = computed(() => {
  return Math.max(0, form.orderAmount - form.discountAmount)
})

const handleExceedMax = (value) => {
  ElMessage.warning(`输入金额 ${value} 超出最大限制`)
}

const handleBelowMin = (value) => {
  ElMessage.warning(`输入金额 ${value} 低于最小限制`)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    console.log('提交数据:', form)
    ElMessage.success('提交成功')
  } catch (error) {
    console.error('验证失败:', error)
  }
}

const handleReset = () => {
  formRef.value.resetFields()
}
</script>

<style scoped>
.help-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style>
```

### 带控制按钮的金额输入
```vue
<template>
  <div>
    <div class="control-examples">
      <div class="example-item">
        <label>基础控制按钮：</label>
        <FuniMoneyInput
          v-model="amount1"
          show-controls
          :step="1"
          :min="0"
          :max="10000"
        />
      </div>
      
      <div class="example-item">
        <label>小数步长：</label>
        <FuniMoneyInput
          v-model="amount2"
          show-controls
          :step="0.1"
          :precision="1"
          :min="0"
          :max="100"
        />
      </div>
      
      <div class="example-item">
        <label>大步长：</label>
        <FuniMoneyInput
          v-model="amount3"
          show-controls
          :step="100"
          :precision="0"
          :min="0"
          :max="100000"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const amount1 = ref(10)
const amount2 = ref(5.5)
const amount3 = ref(1000)
</script>

<style scoped>
.control-examples {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.example-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.example-item label {
  width: 120px;
  text-align: right;
}
</style>
```

### 自定义格式化规则
```vue
<template>
  <div>
    <div class="format-examples">
      <div class="example-item">
        <label>标准格式：</label>
        <FuniMoneyInput
          v-model="standardAmount"
          :show-separator="true"
          :precision="2"
          currency="¥"
        />
        <span class="display-value">{{ formatStandard(standardAmount) }}</span>
      </div>
      
      <div class="example-item">
        <label>无分隔符：</label>
        <FuniMoneyInput
          v-model="noSeparatorAmount"
          :show-separator="false"
          :precision="2"
          currency="¥"
        />
        <span class="display-value">{{ formatNoSeparator(noSeparatorAmount) }}</span>
      </div>
      
      <div class="example-item">
        <label>自定义分隔符：</label>
        <FuniMoneyInput
          v-model="customSeparatorAmount"
          separator=" "
          :precision="2"
          currency="€"
        />
        <span class="display-value">{{ formatCustom(customSeparatorAmount) }}</span>
      </div>
      
      <div class="example-item">
        <label>允许负数：</label>
        <FuniMoneyInput
          v-model="negativeAmount"
          allow-negative
          :precision="2"
          currency="$"
        />
        <span class="display-value">{{ formatNegative(negativeAmount) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const standardAmount = ref(12345.67)
const noSeparatorAmount = ref(12345.67)
const customSeparatorAmount = ref(12345.67)
const negativeAmount = ref(-1234.56)

const formatStandard = (value) => {
  return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

const formatNoSeparator = (value) => {
  return `¥${value.toFixed(2)}`
}

const formatCustom = (value) => {
  return `€${value.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

const formatNegative = (value) => {
  const formatted = Math.abs(value).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  return value < 0 ? `-$${formatted}` : `$${formatted}`
}
</script>

<style scoped>
.format-examples {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.example-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.example-item label {
  width: 120px;
  text-align: right;
}

.display-value {
  font-family: monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}
</style>
```

### 表单集成示例
```vue
<template>
  <el-form :model="orderForm" :rules="orderRules" ref="orderFormRef" label-width="100px">
    <el-form-item label="商品单价" prop="unitPrice">
      <FuniMoneyInput
        v-model="orderForm.unitPrice"
        :min="0.01"
        placeholder="请输入商品单价"
      />
    </el-form-item>
    
    <el-form-item label="购买数量" prop="quantity">
      <el-input-number
        v-model="orderForm.quantity"
        :min="1"
        :max="999"
        placeholder="请输入数量"
      />
    </el-form-item>
    
    <el-form-item label="小计金额">
      <FuniMoneyInput
        :model-value="subtotalAmount"
        readonly
      />
    </el-form-item>
    
    <el-form-item label="运费" prop="shippingFee">
      <FuniMoneyInput
        v-model="orderForm.shippingFee"
        :min="0"
        placeholder="请输入运费"
      />
    </el-form-item>
    
    <el-form-item label="优惠券" prop="couponAmount">
      <FuniMoneyInput
        v-model="orderForm.couponAmount"
        :min="0"
        :max="subtotalAmount"
        placeholder="请输入优惠金额"
      />
    </el-form-item>
    
    <el-form-item label="总金额">
      <FuniMoneyInput
        :model-value="totalAmount"
        readonly
        size="large"
        style="font-weight: bold;"
      />
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="handleSubmitOrder">提交订单</el-button>
      <el-button @click="handleResetOrder">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const orderFormRef = ref()

const orderForm = reactive({
  unitPrice: 0,
  quantity: 1,
  shippingFee: 0,
  couponAmount: 0
})

const orderRules = {
  unitPrice: [
    { required: true, message: '请输入商品单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '单价必须大于0', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入购买数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ]
}

const subtotalAmount = computed(() => {
  return orderForm.unitPrice * orderForm.quantity
})

const totalAmount = computed(() => {
  return Math.max(0, subtotalAmount.value + orderForm.shippingFee - orderForm.couponAmount)
})

const handleSubmitOrder = async () => {
  try {
    await orderFormRef.value.validate()
    
    const orderData = {
      ...orderForm,
      subtotal: subtotalAmount.value,
      total: totalAmount.value
    }
    
    console.log('订单数据:', orderData)
    ElMessage.success('订单提交成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleResetOrder = () => {
  orderFormRef.value.resetFields()
}
</script>
```

## ElementPlus API支持

FuniMoneyInput基于el-input封装，支持所有el-input的API：

```vue
<template>
  <FuniMoneyInput
    v-model="amount"
    
    <!-- ElementPlus el-input 所有属性 -->
    placeholder="请输入金额"
    clearable
    :disabled="false"
    :readonly="false"
    size="default"
    :maxlength="20"
    :minlength="0"
    :show-word-limit="false"
    prefix-icon=""
    suffix-icon=""
    :rows="1"
    :autosize="false"
    :autocomplete="off"
    :name="inputName"
    :form="formName"
    :label="labelText"
    :tabindex="1"
    :validate-event="true"
    :input-style="{}"
    
    <!-- ElementPlus el-input 所有事件 -->
    @input="handleInput"
    @change="handleChange"
    @focus="handleFocus"
    @blur="handleBlur"
    @clear="handleClear"
    @keydown="handleKeydown"
    @keyup="handleKeyup"
  />
</template>
```

## 注意事项

### 1. 数值精度
- 使用Number类型存储，避免精度丢失
- 合理设置precision属性
- 注意JavaScript浮点数计算问题

### 2. 格式化时机
- 默认在失焦时格式化
- 可以通过formatOnBlur控制格式化时机
- 支持手动调用format方法

### 3. 验证规则
- 配合表单验证使用
- 提供范围验证
- 支持自定义验证规则

### 4. 用户体验
- 提供清晰的输入提示
- 合理的错误反馈
- 支持键盘操作

## 常见问题

### Q: 如何处理大金额的输入？
A: 设置合适的max值，使用千分位分隔符提高可读性

### Q: 如何实现金额的实时计算？
A: 监听change事件，在事件处理函数中进行计算

### Q: 如何自定义货币符号的位置？
A: 使用prefix或suffix插槽自定义货币符号的显示

### Q: 如何处理不同国家的金额格式？
A: 通过currency、separator、precision等属性配置不同的格式
