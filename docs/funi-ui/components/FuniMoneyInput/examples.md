# FuniMoneyInput 使用示例

## 基础示例

### 1. 基础金额输入
```vue
<template>
  <div class="example-container">
    <h3>基础金额输入</h3>
    <div class="input-group">
      <label>金额：</label>
      <FuniMoneyInput
        v-model="basicAmount"
        placeholder="请输入金额"
        @change="handleBasicChange"
      />
    </div>
    
    <div class="result">
      <p>输入值：{{ basicAmount }}</p>
      <p>格式化显示：{{ formatAmount(basicAmount) }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const basicAmount = ref(1234.56)

const handleBasicChange = (value) => {
  console.log('金额变化:', value)
}

const formatAmount = (value) => {
  if (!value) return '¥0.00'
  return `¥${Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`
}
</script>

<style scoped>
.example-container {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 20px;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.input-group label {
  width: 80px;
  text-align: right;
}

.result {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
```

### 2. 不同精度配置
```vue
<template>
  <div class="example-container">
    <h3>不同精度配置</h3>
    
    <div class="precision-examples">
      <div class="input-group">
        <label>2位小数（默认）：</label>
        <FuniMoneyInput
          v-model="amount2"
          :precision="2"
          placeholder="0.00"
        />
        <span class="display-value">{{ amount2 }}</span>
      </div>
      
      <div class="input-group">
        <label>无小数：</label>
        <FuniMoneyInput
          v-model="amount0"
          :precision="0"
          placeholder="0"
        />
        <span class="display-value">{{ amount0 }}</span>
      </div>
      
      <div class="input-group">
        <label>4位小数：</label>
        <FuniMoneyInput
          v-model="amount4"
          :precision="4"
          placeholder="0.0000"
        />
        <span class="display-value">{{ amount4 }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const amount2 = ref(1234.56)
const amount0 = ref(1234)
const amount4 = ref(1234.5678)
</script>

<style scoped>
.precision-examples {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.display-value {
  margin-left: 10px;
  color: #666;
  font-family: monospace;
}
</style>
```

### 3. 编辑模式与只读模式
```vue
<template>
  <div class="example-container">
    <h3>编辑模式与只读模式</h3>
    
    <div class="mode-controls">
      <el-radio-group v-model="editMode">
        <el-radio :label="true">编辑模式</el-radio>
        <el-radio :label="false">只读模式</el-radio>
      </el-radio-group>
    </div>
    
    <div class="input-group">
      <label>金额：</label>
      <FuniMoneyInput
        v-model="modeAmount"
        :isEdit="editMode"
        :moneyCapitalShow="true"
        :showPrefix="true"
        placeholder="请输入金额"
      />
    </div>
    
    <div class="result">
      <p>当前模式：{{ editMode ? '编辑模式' : '只读模式' }}</p>
      <p>金额值：{{ modeAmount }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const editMode = ref(true)
const modeAmount = ref(12345.67)
</script>

<style scoped>
.mode-controls {
  margin-bottom: 20px;
}
</style>
```

## 高级示例

### 4. 不同货币配置
```vue
<template>
  <div class="example-container">
    <h3>不同货币配置</h3>
    
    <div class="currency-examples">
      <div class="input-group">
        <label>人民币：</label>
        <FuniMoneyInput
          v-model="cnyAmount"
          currency="¥"
          :showPrefix="true"
          :precision="2"
          placeholder="请输入人民币金额"
        />
      </div>
      
      <div class="input-group">
        <label>美元：</label>
        <FuniMoneyInput
          v-model="usdAmount"
          currency="$"
          :showPrefix="true"
          :precision="2"
          :moneyCapitalShow="false"
          placeholder="请输入美元金额"
        />
      </div>
      
      <div class="input-group">
        <label>欧元：</label>
        <FuniMoneyInput
          v-model="eurAmount"
          currency="€"
          :showPrefix="true"
          :precision="2"
          :moneyCapitalShow="false"
          placeholder="请输入欧元金额"
        />
      </div>
      
      <div class="input-group">
        <label>日元（无小数）：</label>
        <FuniMoneyInput
          v-model="jpyAmount"
          currency="¥"
          :showPrefix="true"
          :precision="0"
          :moneyCapitalShow="false"
          placeholder="请输入日元金额"
        />
      </div>
      
      <div class="input-group">
        <label>无货币符号：</label>
        <FuniMoneyInput
          v-model="noSymbolAmount"
          :showPrefix="false"
          :precision="2"
          :moneyCapitalShow="false"
          placeholder="请输入金额"
        />
      </div>
    </div>
    
    <div class="currency-summary">
      <h4>汇率换算示例：</h4>
      <div class="exchange-rates">
        <div>人民币：¥{{ cnyAmount }}</div>
        <div>美元：${{ usdAmount }} ≈ ¥{{ (usdAmount * 7.2).toFixed(2) }}</div>
        <div>欧元：€{{ eurAmount }} ≈ ¥{{ (eurAmount * 7.8).toFixed(2) }}</div>
        <div>日元：¥{{ jpyAmount }} ≈ ¥{{ (jpyAmount * 0.048).toFixed(2) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const cnyAmount = ref(1000.00)
const usdAmount = ref(138.89)
const eurAmount = ref(128.21)
const jpyAmount = ref(20833)
const noSymbolAmount = ref(1000.00)
</script>

<style scoped>
.currency-examples {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.currency-summary {
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.exchange-rates {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}
</style>
```

## 业务场景示例

### 7. 财务报表场景
```vue
<template>
  <div class="example-container">
    <h3>财务报表场景</h3>

    <div class="financial-report">
      <h4>月度财务报表</h4>

      <el-table :data="reportData" border style="width: 100%">
        <el-table-column prop="item" label="项目" width="150" />
        <el-table-column label="本月金额" width="200">
          <template #default="{ row }">
            <FuniMoneyInput
              v-model="row.currentMonth"
              :isEdit="row.editable"
              :precision="2"
              :moneyCapitalShow="false"
              size="small"
              @change="handleAmountChange(row, 'currentMonth', $event)"
            />
          </template>
        </el-table-column>
        <el-table-column label="上月金额" width="200">
          <template #default="{ row }">
            <FuniMoneyInput
              v-model="row.lastMonth"
              :isEdit="false"
              :precision="2"
              :moneyCapitalShow="false"
              size="small"
            />
          </template>
        </el-table-column>
        <el-table-column label="环比变化" width="150">
          <template #default="{ row }">
            <span :class="getChangeClass(row.change)">
              {{ formatChange(row.change) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button
              v-if="row.editable"
              type="primary"
              size="small"
              @click="saveRow(row)"
            >
              保存
            </el-button>
            <el-button
              v-else
              type="text"
              size="small"
              @click="editRow(row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="report-summary">
        <div class="summary-row">
          <span>总收入：</span>
          <FuniMoneyInput
            :model-value="totalIncome"
            :isEdit="false"
            :precision="2"
            :moneyCapitalShow="true"
          />
        </div>
        <div class="summary-row">
          <span>总支出：</span>
          <FuniMoneyInput
            :model-value="totalExpense"
            :isEdit="false"
            :precision="2"
            :moneyCapitalShow="true"
          />
        </div>
        <div class="summary-row profit">
          <span>净利润：</span>
          <FuniMoneyInput
            :model-value="netProfit"
            :isEdit="false"
            :precision="2"
            :moneyCapitalShow="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const reportData = ref([
  { item: '营业收入', currentMonth: 150000, lastMonth: 140000, editable: false },
  { item: '销售成本', currentMonth: 80000, lastMonth: 75000, editable: false },
  { item: '管理费用', currentMonth: 25000, lastMonth: 28000, editable: false },
  { item: '销售费用', currentMonth: 15000, lastMonth: 12000, editable: false },
  { item: '财务费用', currentMonth: 3000, lastMonth: 3500, editable: false }
])

const totalIncome = computed(() => {
  return reportData.value
    .filter(item => item.item === '营业收入')
    .reduce((sum, item) => sum + item.currentMonth, 0)
})

const totalExpense = computed(() => {
  return reportData.value
    .filter(item => item.item !== '营业收入')
    .reduce((sum, item) => sum + item.currentMonth, 0)
})

const netProfit = computed(() => {
  return totalIncome.value - totalExpense.value
})

const handleAmountChange = (row, field, value) => {
  row[field] = value
  calculateChange(row)
}

const calculateChange = (row) => {
  if (row.lastMonth > 0) {
    row.change = ((row.currentMonth - row.lastMonth) / row.lastMonth * 100).toFixed(2)
  } else {
    row.change = 0
  }
}

const formatChange = (change) => {
  const num = Number(change)
  if (num > 0) return `+${num}%`
  if (num < 0) return `${num}%`
  return '0%'
}

const getChangeClass = (change) => {
  const num = Number(change)
  if (num > 0) return 'positive-change'
  if (num < 0) return 'negative-change'
  return 'no-change'
}

const editRow = (row) => {
  row.editable = true
}

const saveRow = (row) => {
  row.editable = false
  calculateChange(row)
  ElMessage.success('保存成功')
}

// 初始化计算变化率
reportData.value.forEach(calculateChange)
</script>

<style scoped>
.financial-report {
  width: 100%;
}

.report-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-row.profit {
  font-weight: bold;
  font-size: 16px;
  margin-top: 10px;
  padding-top: 15px;
  border-top: 2px solid #3b82f6;
}

.positive-change {
  color: #10b981;
  font-weight: bold;
}

.negative-change {
  color: #ef4444;
  font-weight: bold;
}

.no-change {
  color: #6b7280;
}
</style>
```

### 8. 预算管理场景
```vue
<template>
  <div class="example-container">
    <h3>预算管理场景</h3>

    <div class="budget-manager">
      <div class="budget-header">
        <h4>2024年度预算管理</h4>
        <div class="budget-controls">
          <el-button type="primary" @click="addBudgetItem">添加预算项</el-button>
          <el-button type="success" @click="saveBudget">保存预算</el-button>
          <el-button @click="resetBudget">重置</el-button>
        </div>
      </div>

      <div class="budget-items">
        <div
          v-for="(item, index) in budgetItems"
          :key="item.id"
          class="budget-item"
        >
          <div class="item-header">
            <el-input
              v-model="item.name"
              placeholder="预算项目名称"
              style="width: 200px"
            />
            <el-button
              type="danger"
              size="small"
              @click="removeBudgetItem(index)"
              v-if="budgetItems.length > 1"
            >
              删除
            </el-button>
          </div>

          <div class="item-content">
            <div class="amount-inputs">
              <div class="input-group">
                <label>预算金额：</label>
                <FuniMoneyInput
                  v-model="item.budgetAmount"
                  :precision="2"
                  placeholder="请输入预算金额"
                  @change="calculateProgress(item)"
                />
              </div>

              <div class="input-group">
                <label>已使用金额：</label>
                <FuniMoneyInput
                  v-model="item.usedAmount"
                  :precision="2"
                  placeholder="请输入已使用金额"
                  @change="calculateProgress(item)"
                />
              </div>

              <div class="input-group">
                <label>剩余金额：</label>
                <FuniMoneyInput
                  :model-value="item.remainingAmount"
                  :isEdit="false"
                  :precision="2"
                  :moneyCapitalShow="false"
                />
              </div>
            </div>

            <div class="progress-section">
              <div class="progress-info">
                <span>使用进度：{{ item.usagePercentage }}%</span>
                <span :class="getProgressClass(item.usagePercentage)">
                  {{ getProgressStatus(item.usagePercentage) }}
                </span>
              </div>
              <el-progress
                :percentage="item.usagePercentage"
                :status="getProgressType(item.usagePercentage)"
                :stroke-width="8"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="budget-summary">
        <h4>预算汇总：</h4>
        <div class="summary-grid">
          <div class="summary-item">
            <span>总预算：</span>
            <FuniMoneyInput
              :model-value="totalBudget"
              :isEdit="false"
              :precision="2"
              :moneyCapitalShow="true"
            />
          </div>
          <div class="summary-item">
            <span>总使用：</span>
            <FuniMoneyInput
              :model-value="totalUsed"
              :isEdit="false"
              :precision="2"
              :moneyCapitalShow="true"
            />
          </div>
          <div class="summary-item">
            <span>总剩余：</span>
            <FuniMoneyInput
              :model-value="totalRemaining"
              :isEdit="false"
              :precision="2"
              :moneyCapitalShow="true"
            />
          </div>
          <div class="summary-item">
            <span>整体进度：</span>
            <span class="overall-progress">{{ overallProgress }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const budgetItems = ref([
  {
    id: 1,
    name: '市场推广费用',
    budgetAmount: 100000,
    usedAmount: 35000,
    remainingAmount: 65000,
    usagePercentage: 35
  },
  {
    id: 2,
    name: '研发费用',
    budgetAmount: 200000,
    usedAmount: 120000,
    remainingAmount: 80000,
    usagePercentage: 60
  },
  {
    id: 3,
    name: '办公费用',
    budgetAmount: 50000,
    usedAmount: 48000,
    remainingAmount: 2000,
    usagePercentage: 96
  }
])

const totalBudget = computed(() => {
  return budgetItems.value.reduce((sum, item) => sum + (item.budgetAmount || 0), 0)
})

const totalUsed = computed(() => {
  return budgetItems.value.reduce((sum, item) => sum + (item.usedAmount || 0), 0)
})

const totalRemaining = computed(() => {
  return totalBudget.value - totalUsed.value
})

const overallProgress = computed(() => {
  if (totalBudget.value === 0) return 0
  return Math.round((totalUsed.value / totalBudget.value) * 100)
})

const calculateProgress = (item) => {
  if (item.budgetAmount > 0) {
    item.usagePercentage = Math.round((item.usedAmount / item.budgetAmount) * 100)
    item.remainingAmount = item.budgetAmount - item.usedAmount
  } else {
    item.usagePercentage = 0
    item.remainingAmount = 0
  }
}

const getProgressClass = (percentage) => {
  if (percentage >= 90) return 'danger-status'
  if (percentage >= 70) return 'warning-status'
  return 'normal-status'
}

const getProgressStatus = (percentage) => {
  if (percentage >= 100) return '已超支'
  if (percentage >= 90) return '即将超支'
  if (percentage >= 70) return '使用较多'
  return '正常'
}

const getProgressType = (percentage) => {
  if (percentage >= 100) return 'exception'
  if (percentage >= 90) return 'warning'
  return 'success'
}

const addBudgetItem = () => {
  const newId = Math.max(...budgetItems.value.map(item => item.id)) + 1
  budgetItems.value.push({
    id: newId,
    name: '',
    budgetAmount: 0,
    usedAmount: 0,
    remainingAmount: 0,
    usagePercentage: 0
  })
}

const removeBudgetItem = (index) => {
  budgetItems.value.splice(index, 1)
}

const saveBudget = () => {
  const budgetData = {
    items: budgetItems.value,
    summary: {
      totalBudget: totalBudget.value,
      totalUsed: totalUsed.value,
      totalRemaining: totalRemaining.value,
      overallProgress: overallProgress.value
    }
  }

  console.log('保存预算数据:', budgetData)
  ElMessage.success('预算保存成功')
}

const resetBudget = () => {
  budgetItems.value.forEach(item => {
    item.usedAmount = 0
    calculateProgress(item)
  })
  ElMessage.info('预算已重置')
}

// 初始化计算
budgetItems.value.forEach(calculateProgress)
</script>

<style scoped>
.budget-manager {
  width: 100%;
}

.budget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e2e8f0;
}

.budget-controls {
  display: flex;
  gap: 10px;
}

.budget-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.budget-item {
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.amount-inputs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.progress-section {
  margin-top: 10px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.danger-status {
  color: #ef4444;
  font-weight: bold;
}

.warning-status {
  color: #f59e0b;
  font-weight: bold;
}

.normal-status {
  color: #10b981;
  font-weight: bold;
}

.budget-summary {
  padding: 20px;
  background-color: #f1f5f9;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.overall-progress {
  font-weight: bold;
  font-size: 16px;
  color: #3b82f6;
}
</style>
```

## 注意事项

### 使用建议
1. **精度设置**：根据业务需求选择合适的小数位数
2. **单位转换**：大额资金建议使用万元或十万元单位
3. **格式化显示**：保持整个应用的金额格式一致
4. **验证规则**：设置合理的最小值和最大值限制

### 性能优化
1. **防抖处理**：在频繁计算场景下使用防抖优化
2. **数据缓存**：缓存格式化结果避免重复计算
3. **懒加载**：大量数据时考虑分页或虚拟滚动

### 错误处理
1. **输入验证**：验证输入的有效性
2. **精度处理**：注意JavaScript浮点数精度问题
3. **边界情况**：处理空值、负数等特殊情况

### 5. 单位转换示例
```vue
<template>
  <div class="example-container">
    <h3>单位转换示例</h3>
    
    <div class="unit-examples">
      <div class="input-group">
        <label>元（基础单位）：</label>
        <FuniMoneyInput
          v-model="yuanAmount"
          :unit="1"
          :precision="2"
          placeholder="请输入元"
        />
        <span class="unit-label">元</span>
      </div>
      
      <div class="input-group">
        <label>万元：</label>
        <FuniMoneyInput
          v-model="wanAmount"
          :unit="10000"
          :precision="4"
          placeholder="请输入万元"
        />
        <span class="unit-label">万元</span>
      </div>
      
      <div class="input-group">
        <label>十万元：</label>
        <FuniMoneyInput
          v-model="shiWanAmount"
          :unit="100000"
          :precision="2"
          placeholder="请输入十万元"
        />
        <span class="unit-label">十万元</span>
      </div>
      
      <div class="input-group">
        <label>分（0.01元）：</label>
        <FuniMoneyInput
          v-model="fenAmount"
          :unit="0.01"
          :precision="0"
          placeholder="请输入分"
        />
        <span class="unit-label">分</span>
      </div>
    </div>
    
    <div class="conversion-result">
      <h4>单位换算结果：</h4>
      <div class="conversion-table">
        <div class="conversion-row">
          <span>元：</span>
          <span>{{ yuanAmount || 0 }}</span>
        </div>
        <div class="conversion-row">
          <span>万元：</span>
          <span>{{ wanAmount || 0 }} 万元 = {{ ((wanAmount || 0) * 10000).toLocaleString() }} 元</span>
        </div>
        <div class="conversion-row">
          <span>十万元：</span>
          <span>{{ shiWanAmount || 0 }} 十万元 = {{ ((shiWanAmount || 0) * 100000).toLocaleString() }} 元</span>
        </div>
        <div class="conversion-row">
          <span>分：</span>
          <span>{{ fenAmount || 0 }} 分 = {{ ((fenAmount || 0) * 0.01).toFixed(2) }} 元</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const yuanAmount = ref(12345.67)
const wanAmount = ref(1.2346)
const shiWanAmount = ref(0.12)
const fenAmount = ref(1234567)
</script>

<style scoped>
.unit-examples {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.unit-label {
  margin-left: 10px;
  color: #666;
  font-weight: bold;
}

.conversion-result {
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.conversion-table {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
}

.conversion-row {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #e2e8f0;
}

.conversion-row:last-child {
  border-bottom: none;
}
</style>
```

### 6. 表单集成示例
```vue
<template>
  <div class="example-container">
    <h3>表单集成示例</h3>
    
    <el-form 
      :model="orderForm" 
      :rules="orderRules" 
      ref="orderFormRef" 
      label-width="120px"
    >
      <el-form-item label="商品单价" prop="unitPrice">
        <FuniMoneyInput
          v-model="orderForm.unitPrice"
          :precision="2"
          placeholder="请输入商品单价"
        />
      </el-form-item>
      
      <el-form-item label="购买数量" prop="quantity">
        <el-input-number
          v-model="orderForm.quantity"
          :min="1"
          :max="999"
          placeholder="请输入数量"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="小计金额">
        <FuniMoneyInput
          :model-value="subtotalAmount"
          :isEdit="false"
          :moneyCapitalShow="false"
        />
      </el-form-item>
      
      <el-form-item label="运费" prop="shippingFee">
        <FuniMoneyInput
          v-model="orderForm.shippingFee"
          :precision="2"
          :defaultZero="true"
          placeholder="请输入运费"
        />
      </el-form-item>
      
      <el-form-item label="优惠金额" prop="discountAmount">
        <FuniMoneyInput
          v-model="orderForm.discountAmount"
          :precision="2"
          :defaultZero="true"
          placeholder="请输入优惠金额"
        />
      </el-form-item>
      
      <el-form-item label="总金额">
        <FuniMoneyInput
          :model-value="totalAmount"
          :isEdit="false"
          :moneyCapitalShow="true"
          size="large"
          style="font-weight: bold;"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交订单</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button @click="handleCalculate">重新计算</el-button>
      </el-form-item>
    </el-form>
    
    <div class="order-summary">
      <h4>订单摘要：</h4>
      <div class="summary-item">
        <span>商品：{{ orderForm.unitPrice }} × {{ orderForm.quantity }} = {{ subtotalAmount }}</span>
      </div>
      <div class="summary-item">
        <span>运费：{{ orderForm.shippingFee }}</span>
      </div>
      <div class="summary-item">
        <span>优惠：-{{ orderForm.discountAmount }}</span>
      </div>
      <div class="summary-item total">
        <span>总计：{{ totalAmount }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const orderFormRef = ref()

const orderForm = reactive({
  unitPrice: 99.99,
  quantity: 2,
  shippingFee: 10.00,
  discountAmount: 5.00
})

const orderRules = {
  unitPrice: [
    { required: true, message: '请输入商品单价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '单价不能小于0.01', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入购买数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量不能小于1', trigger: 'blur' }
  ],
  shippingFee: [
    { type: 'number', min: 0, message: '运费不能小于0', trigger: 'blur' }
  ],
  discountAmount: [
    { type: 'number', min: 0, message: '优惠金额不能小于0', trigger: 'blur' }
  ]
}

const subtotalAmount = computed(() => {
  return Number((orderForm.unitPrice * orderForm.quantity).toFixed(2))
})

const totalAmount = computed(() => {
  const total = subtotalAmount.value + orderForm.shippingFee - orderForm.discountAmount
  return Number(Math.max(0, total).toFixed(2))
})

const handleSubmit = async () => {
  try {
    await orderFormRef.value.validate()
    
    const orderData = {
      ...orderForm,
      subtotal: subtotalAmount.value,
      total: totalAmount.value
    }
    
    console.log('订单数据:', orderData)
    ElMessage.success('订单提交成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  }
}

const handleReset = () => {
  orderFormRef.value.resetFields()
}

const handleCalculate = () => {
  // 触发重新计算
  ElMessage.info(`重新计算完成，总金额：¥${totalAmount.value}`)
}
</script>

<style scoped>
.order-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #e5e7eb;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item.total {
  font-weight: bold;
  font-size: 16px;
  color: #dc2626;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 2px solid #dc2626;
}
</style>
```
