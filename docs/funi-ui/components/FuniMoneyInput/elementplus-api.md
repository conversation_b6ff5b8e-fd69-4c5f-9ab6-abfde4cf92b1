# FuniMoneyInput ElementPlus API 支持

## 基础组件说明

FuniMoneyInput 基于 ElementPlus 的 `el-input-number` 组件封装，专门用于金额输入场景。组件在保持 ElementPlus 原有功能的基础上，增加了金额格式化、大写显示、单位转换等业务功能。

### 核心组件构成
- **el-input-number**: 数字输入框，提供基础的数值输入功能
- **自定义格式化逻辑**: 金额显示格式化和大写转换
- **工具函数集成**: 使用 `$utils.amountIntl` 和 `$utils.moneyCapital` 进行格式化

## 支持的 ElementPlus API

### el-input-number 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| model-value | ✅ | 内部处理 | 绑定值，支持数字和字符串 |
| min | ✅ | 直接透传 | 最小值 |
| max | ✅ | 直接透传 | 最大值 |
| step | ✅ | 直接透传 | 步长 |
| step-strictly | ✅ | 直接透传 | 是否只能输入步长的倍数 |
| precision | ✅ | 组件处理 | 数值精度，组件内部使用 |
| size | ✅ | 直接透传 | 输入框尺寸 |
| readonly | ✅ | 直接透传 | 是否只读 |
| disabled | ✅ | 直接透传 | 是否禁用 |
| controls | ✅ | 固定为false | 控制按钮显示，组件内部设为false |
| controls-position | ✅ | 直接透传 | 控制按钮位置 |
| name | ✅ | 直接透传 | 原生name属性 |
| label | ✅ | 直接透传 | 标签文本 |
| placeholder | ✅ | 直接透传 | 占位符 |
| id | ✅ | 直接透传 | 输入框id |
| value-on-clear | ✅ | 固定为null | 清空时的值 |
| validate-event | ✅ | 直接透传 | 是否触发表单验证 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| change | ✅ | 内部处理 | 值变化事件，同时触发update:modelValue |
| blur | ✅ | 直接透传 | 失去焦点事件 |
| focus | ✅ | 直接透传 | 获得焦点事件 |
| input | ✅ | 直接透传 | 输入事件 |

#### Methods 透传支持
| ElementPlus Method | 支持状态 | 透传方式 | 说明 |
|-------------------|----------|----------|------|
| focus | ✅ | 直接透传 | 使输入框获得焦点 |
| blur | ✅ | 直接透传 | 使输入框失去焦点 |
| select | ✅ | 直接透传 | 选中输入框文本 |

## 透传方式说明

### 1. 直接透传
组件属性直接传递给 `el-input-number`，无需额外处理。

```vue
<template>
  <FuniMoneyInput
    v-model="amount"
    :min="0"
    :max="999999"
    :step="0.01"
    size="large"
    placeholder="请输入金额"
    :disabled="false"
    :readonly="false"
  />
</template>
```

### 2. 内部处理
组件内部处理后再传递给 ElementPlus 组件。

```vue
<template>
  <FuniMoneyInput
    v-model="amount"
    :precision="2"
    @change="handleChange"
  />
</template>

<script setup>
// precision 由组件内部处理
// change 事件会同时触发 update:modelValue 和 change
const handleChange = (value) => {
  console.log('金额变化:', value)
}
</script>
```

### 3. 固定配置
某些属性被组件固定设置，以确保金额输入的正确性。

```javascript
// 组件内部固定配置
{
  controls: false,        // 隐藏控制按钮
  valueOnClear: null,     // 清空时设为null
  maxlength: 20          // 最大输入长度
}
```

## 使用示例

### 基础透传示例
```vue
<template>
  <div class="example-container">
    <h3>ElementPlus API 透传示例</h3>
    
    <!-- 基础属性透传 -->
    <FuniMoneyInput
      v-model="amount1"
      :min="0"
      :max="100000"
      :step="0.01"
      size="large"
      placeholder="请输入金额（0-100,000）"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
    />
    
    <!-- 只读状态 -->
    <FuniMoneyInput
      v-model="amount2"
      readonly
      size="default"
      placeholder="只读金额"
    />
    
    <!-- 禁用状态 -->
    <FuniMoneyInput
      v-model="amount3"
      disabled
      size="small"
      placeholder="禁用状态"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const amount1 = ref(1234.56)
const amount2 = ref(9876.54)
const amount3 = ref(5555.55)

const handleChange = (value) => {
  console.log('金额变化:', value)
}

const handleFocus = (event) => {
  console.log('获得焦点:', event)
}

const handleBlur = (event) => {
  console.log('失去焦点:', event)
}
</script>
```

### 表单验证集成
```vue
<template>
  <div class="example-container">
    <h3>表单验证集成</h3>
    
    <el-form 
      :model="form" 
      :rules="rules" 
      ref="formRef" 
      label-width="120px"
    >
      <el-form-item label="订单金额" prop="orderAmount">
        <FuniMoneyInput
          v-model="form.orderAmount"
          :min="0.01"
          :max="999999.99"
          :step="0.01"
          placeholder="请输入订单金额"
          :validate-event="true"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="优惠金额" prop="discountAmount">
        <FuniMoneyInput
          v-model="form.discountAmount"
          :min="0"
          :max="form.orderAmount"
          :step="0.01"
          placeholder="请输入优惠金额"
          :validate-event="true"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const formRef = ref()

const form = reactive({
  orderAmount: 0,
  discountAmount: 0
})

const rules = {
  orderAmount: [
    { required: true, message: '请输入订单金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '订单金额不能小于0.01', trigger: 'blur' }
  ],
  discountAmount: [
    { type: 'number', min: 0, message: '优惠金额不能小于0', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value > form.orderAmount) {
          callback(new Error('优惠金额不能超过订单金额'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    console.log('表单数据:', form)
  } catch (error) {
    console.error('验证失败:', error)
  }
}

const handleReset = () => {
  formRef.value.resetFields()
}
</script>
```

### 方法调用示例
```vue
<template>
  <div class="example-container">
    <h3>方法调用示例</h3>
    
    <div class="method-controls">
      <el-button @click="focusInput">聚焦输入框</el-button>
      <el-button @click="blurInput">失去焦点</el-button>
      <el-button @click="selectText">选中文本</el-button>
    </div>
    
    <FuniMoneyInput
      ref="moneyInputRef"
      v-model="amount"
      placeholder="请输入金额"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const moneyInputRef = ref()
const amount = ref(1234.56)

const focusInput = () => {
  moneyInputRef.value?.focus()
}

const blurInput = () => {
  moneyInputRef.value?.blur()
}

const selectText = () => {
  moneyInputRef.value?.select()
}
</script>

<style scoped>
.method-controls {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
}
</style>
```

### 高级配置示例
```vue
<template>
  <div class="example-container">
    <h3>高级配置示例</h3>
    
    <div class="advanced-examples">
      <!-- 步长控制 -->
      <div class="example-item">
        <label>步长控制（0.1）：</label>
        <FuniMoneyInput
          v-model="stepAmount"
          :step="0.1"
          :step-strictly="true"
          :precision="1"
          placeholder="只能输入0.1的倍数"
        />
      </div>
      
      <!-- 大数值范围 -->
      <div class="example-item">
        <label>大数值范围：</label>
        <FuniMoneyInput
          v-model="largeAmount"
          :min="1000000"
          :max="999999999"
          :step="10000"
          :precision="0"
          placeholder="百万级金额"
        />
      </div>
      
      <!-- 自定义ID和名称 -->
      <div class="example-item">
        <label>自定义属性：</label>
        <FuniMoneyInput
          v-model="customAmount"
          id="custom-money-input"
          name="customAmount"
          label="自定义金额输入"
          placeholder="带自定义属性"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const stepAmount = ref(1.5)
const largeAmount = ref(5000000)
const customAmount = ref(0)
</script>

<style scoped>
.advanced-examples {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.example-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.example-item label {
  width: 150px;
  text-align: right;
}
</style>
```

## 注意事项

### 1. API 兼容性
- 确保使用的 ElementPlus 版本支持相应的 API
- 建议使用 ElementPlus 2.0+ 版本以获得最佳兼容性
- 某些新增的 API 可能在旧版本中不可用

### 2. 属性覆盖
- `controls` 属性被组件固定为 `false`
- `value-on-clear` 属性被组件固定为 `null`
- `maxlength` 属性被组件固定为 `20`
- 这些设置是为了确保金额输入的正确性

### 3. 事件处理
- `change` 事件会同时触发 `update:modelValue` 和 `change`
- 建议使用组件提供的事件而非直接监听 ElementPlus 事件
- 复杂的事件处理逻辑建议在组件外部实现

### 4. 精度处理
- `precision` 属性由组件内部处理，用于控制小数位数
- 注意 JavaScript 浮点数精度问题
- 建议在业务逻辑中使用专门的数值处理库

### 5. 样式定制
- 可以通过 CSS 变量或类名覆盖默认样式
- 组件内部设置了左对齐样式：`.funiMoneyInput.align-left`
- 注意样式优先级，避免样式冲突

## 版本兼容性

| FuniMoneyInput 版本 | ElementPlus 版本 | 兼容性 |
|-------------------|-----------------|--------|
| 1.0.x | 2.0.x | ✅ 完全兼容 |
| 1.0.x | 2.1.x | ✅ 完全兼容 |
| 1.0.x | 2.2.x | ✅ 完全兼容 |
| 1.0.x | 2.3.x | ✅ 完全兼容 |
| 1.0.x | 1.x.x | ⚠️ 部分兼容 |

## 迁移指南

### 从 el-input-number 迁移
```vue
<!-- 原来的 el-input-number -->
<el-input-number
  v-model="amount"
  :precision="2"
  :min="0"
  :max="999999"
  controls-position="right"
/>

<!-- 迁移到 FuniMoneyInput -->
<FuniMoneyInput
  v-model="amount"
  :precision="2"
  :min="0"
  :max="999999"
  <!-- controls-position 不需要，组件内部已禁用 controls -->
/>
```

### 新增功能使用
```vue
<!-- 使用新增的金额功能 -->
<FuniMoneyInput
  v-model="amount"
  :precision="2"
  :isEdit="true"
  :moneyCapitalShow="true"
  :showPrefix="true"
  :unit="1"
  :defaultZero="false"
/>
```
