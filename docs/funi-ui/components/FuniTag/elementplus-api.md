# FuniTag ElementPlus API 支持

## 基础组件说明

FuniTag 是基于 ElementPlus 的 `el-tag` 组件进行二次封装的标签组件。它完全兼容 `el-tag` 的所有 API，并在此基础上扩展了图标显示、选择状态、复制功能、拖拽支持、动画效果等特性。

## ElementPlus el-tag 原生 API 支持

### Props 属性

| 属性名 | 类型 | 默认值 | 说明 | FuniTag 支持 |
|--------|------|--------|------|----------------|
| type | string | '' | 标签类型 | ✅ 完全支持 |
| closable | boolean | false | 是否可关闭 | ✅ 完全支持 |
| disable-transitions | boolean | false | 是否禁用渐变动画 | ✅ 完全支持 |
| hit | boolean | false | 是否有边框描边 | ✅ 完全支持 |
| color | string | '' | 背景色 | ✅ 完全支持 |
| size | string | 'default' | 标签尺寸 | ✅ 完全支持 |
| effect | string | 'light' | 主题效果 | ✅ 完全支持 |
| round | boolean | false | 是否圆角 | ✅ 完全支持 |

### Events 事件

| 事件名 | 说明 | 回调参数 | FuniTag 支持 |
|--------|------|----------|----------------|
| close | 关闭标签时触发 | (event: Event) | ✅ 完全支持 |
| click | 点击标签时触发 | (event: Event) | ✅ 完全支持 |

### Slots 插槽

| 插槽名 | 说明 | FuniTag 支持 |
|--------|------|----------------|
| default | 自定义标签内容 | ✅ 完全支持 |

## API 透传方式

FuniTag 通过 `v-bind="$attrs"` 的方式将所有 ElementPlus el-tag 的属性透传给底层组件，确保 100% 兼容性。

### 基础使用示例

```vue
<template>
  <!-- 直接使用 ElementPlus el-tag 的所有属性 -->
  <FuniTag
    type="primary"
    :closable="true"
    :disable-transitions="false"
    :hit="false"
    color=""
    size="default"
    effect="light"
    :round="false"
    @close="handleClose"
    @click="handleClick"
  >
    标签内容
  </FuniTag>
</template>

<script setup>
const handleClose = (event) => {
  console.log('标签关闭:', event)
}

const handleClick = (event) => {
  console.log('标签点击:', event)
}
</script>
```

### 标签类型

```vue
<template>
  <div class="tag-types">
    <!-- 使用 ElementPlus 原生类型 -->
    <FuniTag type="primary">主要标签</FuniTag>
    <FuniTag type="success">成功标签</FuniTag>
    <FuniTag type="info">信息标签</FuniTag>
    <FuniTag type="warning">警告标签</FuniTag>
    <FuniTag type="danger">危险标签</FuniTag>
  </div>
</template>
```

### 标签尺寸

```vue
<template>
  <div class="tag-sizes">
    <!-- 使用 ElementPlus 原生尺寸 -->
    <FuniTag size="large" type="primary">大尺寸</FuniTag>
    <FuniTag size="default" type="primary">默认尺寸</FuniTag>
    <FuniTag size="small" type="primary">小尺寸</FuniTag>
  </div>
</template>
```

### 主题效果

```vue
<template>
  <div class="tag-effects">
    <!-- 使用 ElementPlus 原生效果 -->
    <FuniTag type="primary" effect="dark">深色主题</FuniTag>
    <FuniTag type="primary" effect="light">浅色主题</FuniTag>
    <FuniTag type="primary" effect="plain">朴素主题</FuniTag>
  </div>
</template>
```

### 可关闭标签

```vue
<template>
  <div class="closable-tags">
    <!-- 使用 ElementPlus 原生关闭功能 -->
    <FuniTag
      v-for="tag in tags"
      :key="tag.id"
      :type="tag.type"
      closable
      @close="handleTagClose(tag.id)"
    >
      {{ tag.name }}
    </FuniTag>
  </div>
</template>

<script setup>
import { reactive } from 'vue'

const tags = reactive([
  { id: 1, name: '标签1', type: 'primary' },
  { id: 2, name: '标签2', type: 'success' },
  { id: 3, name: '标签3', type: 'warning' }
])

const handleTagClose = (tagId) => {
  const index = tags.findIndex(tag => tag.id === tagId)
  if (index > -1) {
    tags.splice(index, 1)
  }
}
</script>
```

### 自定义颜色

```vue
<template>
  <div class="custom-color-tags">
    <!-- 使用 ElementPlus 原生颜色自定义 -->
    <FuniTag color="#f50">自定义红色</FuniTag>
    <FuniTag color="#2db7f5">自定义蓝色</FuniTag>
    <FuniTag color="#87d068">自定义绿色</FuniTag>
    <FuniTag color="#108ee9">自定义深蓝</FuniTag>
  </div>
</template>
```

### 圆角和描边

```vue
<template>
  <div class="styled-tags">
    <!-- 使用 ElementPlus 原生样式属性 -->
    <FuniTag type="primary" round>圆角标签</FuniTag>
    <FuniTag type="success" hit>描边标签</FuniTag>
    <FuniTag type="warning" round hit>圆角描边</FuniTag>
  </div>
</template>
```

### 禁用动画

```vue
<template>
  <div class="animation-tags">
    <!-- 使用 ElementPlus 原生动画控制 -->
    <FuniTag type="primary" :disable-transitions="false">有动画</FuniTag>
    <FuniTag type="success" :disable-transitions="true">无动画</FuniTag>
  </div>
</template>
```

## FuniTag 扩展功能

在完全支持 ElementPlus el-tag API 的基础上，FuniTag 还提供了以下扩展功能：

### 1. 图标支持

```vue
<template>
  <FuniTag 
    type="primary"
    
    <!-- ElementPlus 原生属性 -->
    size="default"
    effect="light"
    
    <!-- FuniTag 扩展属性 -->
    icon="User"
    icon-position="left"
  >
    用户标签
  </FuniTag>
</template>
```

### 2. 选择状态

```vue
<template>
  <FuniTag 
    type="info"
    
    <!-- ElementPlus 原生属性 -->
    clickable
    
    <!-- FuniTag 扩展属性 -->
    selectable
    :selected="isSelected"
    @select="handleSelect"
  >
    可选择标签
  </FuniTag>
</template>
```

### 3. 复制功能

```vue
<template>
  <FuniTag 
    type="info"
    
    <!-- ElementPlus 原生属性 -->
    size="small"
    
    <!-- FuniTag 扩展属性 -->
    copyable
    tooltip="点击复制"
    @copy="handleCopy"
  >
    可复制内容
  </FuniTag>
</template>
```

### 4. 拖拽支持

```vue
<template>
  <FuniTag 
    type="primary"
    
    <!-- ElementPlus 原生属性 -->
    effect="dark"
    
    <!-- FuniTag 扩展属性 -->
    draggable
    @drag-start="handleDragStart"
    @drag-end="handleDragEnd"
  >
    可拖拽标签
  </FuniTag>
</template>
```

### 5. 动画效果

```vue
<template>
  <FuniTag 
    type="primary"
    
    <!-- ElementPlus 原生属性 -->
    size="default"
    
    <!-- FuniTag 扩展属性 -->
    animation="pulse"
  >
    动画标签
  </FuniTag>
</template>
```

### 6. 渐变背景

```vue
<template>
  <FuniTag 
    <!-- ElementPlus 原生属性 -->
    effect="dark"
    
    <!-- FuniTag 扩展属性 -->
    gradient
    :gradient-colors="['#ff6b6b', '#feca57']"
  >
    渐变标签
  </FuniTag>
</template>
```

## 兼容性说明

### 完全兼容

- ✅ 所有 ElementPlus el-tag 的 Props 属性
- ✅ 所有 ElementPlus el-tag 的 Events 事件
- ✅ 所有 ElementPlus el-tag 的 Slots 插槽
- ✅ ElementPlus el-tag 的样式类名
- ✅ ElementPlus el-tag 的 CSS 变量

### 扩展兼容

- ✅ 在原有功能基础上增加新特性
- ✅ 不影响原有 API 的使用方式
- ✅ 向后兼容，可以直接替换 el-tag

### 迁移指南

如果你正在使用 ElementPlus 的 el-tag，可以直接替换为 FuniTag：

```vue
<!-- 原来的 el-tag -->
<el-tag 
  type="primary"
  closable
  size="default"
  @close="handleClose"
>
  标签内容
</el-tag>

<!-- 替换为 FuniTag，功能完全一致 -->
<FuniTag 
  type="primary"
  closable
  size="default"
  @close="handleClose"
>
  标签内容
</FuniTag>

<!-- 还可以使用扩展功能 -->
<FuniTag 
  type="primary"
  closable
  size="default"
  @close="handleClose"
  
  <!-- 新增的扩展功能 -->
  icon="User"
  selectable
  tooltip="用户标签"
/>
```

## 注意事项

1. **样式继承**：FuniTag 继承了 el-tag 的所有样式，确保视觉效果一致
2. **事件透传**：所有 el-tag 的事件都会正确透传，不会丢失
3. **插槽支持**：default 插槽完全兼容，可以自定义标签内容
4. **CSS 变量**：支持 ElementPlus 主题定制的 CSS 变量
5. **无障碍访问**：保持 el-tag 的无障碍访问特性

## 最佳实践

1. **渐进式升级**：可以逐步将项目中的 el-tag 替换为 FuniTag
2. **功能按需使用**：不需要扩展功能时，FuniTag 表现与 el-tag 完全一致
3. **主题兼容**：使用 ElementPlus 主题时，FuniTag 会自动适配
4. **性能优化**：扩展功能采用按需渲染，不影响基础性能
