# FuniTag 使用示例

## 基础使用示例

### 1. 基础标签类型

```vue
<template>
  <div class="basic-tag-demo">
    <h3>标签类型</h3>
    <div class="tag-row">
      <FuniTag>默认标签</FuniTag>
      <FuniTag type="primary">主要标签</FuniTag>
      <FuniTag type="success">成功标签</FuniTag>
      <FuniTag type="info">信息标签</FuniTag>
      <FuniTag type="warning">警告标签</FuniTag>
      <FuniTag type="danger">危险标签</FuniTag>
    </div>
    
    <h3>标签尺寸</h3>
    <div class="tag-row">
      <FuniTag size="small" type="primary">小型标签</FuniTag>
      <FuniTag size="default" type="primary">默认标签</FuniTag>
      <FuniTag size="large" type="primary">大型标签</FuniTag>
    </div>
    
    <h3>主题效果</h3>
    <div class="tag-row">
      <FuniTag type="primary" effect="dark">深色主题</FuniTag>
      <FuniTag type="primary" effect="light">浅色主题</FuniTag>
      <FuniTag type="primary" effect="plain">朴素主题</FuniTag>
    </div>
    
    <h3>圆角标签</h3>
    <div class="tag-row">
      <FuniTag type="primary" round>圆角标签</FuniTag>
      <FuniTag type="success" round>成功标签</FuniTag>
      <FuniTag type="warning" round>警告标签</FuniTag>
    </div>
  </div>
</template>

<style scoped>
.basic-tag-demo {
  padding: 20px;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
  flex-wrap: wrap;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

### 2. 可关闭标签

```vue
<template>
  <div class="closable-tag-demo">
    <h3>可关闭标签</h3>
    <div class="tag-row">
      <FuniTag
        v-for="tag in closableTags"
        :key="tag.id"
        :type="tag.type"
        closable
        @close="handleTagClose(tag.id)"
      >
        {{ tag.name }}
      </FuniTag>
    </div>
    
    <h3>动态添加标签</h3>
    <div class="tag-row">
      <FuniTag
        v-for="tag in dynamicTags"
        :key="tag"
        type="info"
        closable
        @close="removeDynamicTag(tag)"
      >
        {{ tag }}
      </FuniTag>
      
      <el-input
        v-if="inputVisible"
        ref="inputRef"
        v-model="inputValue"
        size="small"
        style="width: 100px;"
        @keyup.enter="handleInputConfirm"
        @blur="handleInputConfirm"
      />
      <el-button
        v-else
        size="small"
        @click="showInput"
      >
        + 新标签
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'

const inputRef = ref()
const inputVisible = ref(false)
const inputValue = ref('')

const closableTags = reactive([
  { id: 1, name: 'JavaScript', type: 'primary' },
  { id: 2, name: 'Vue.js', type: 'success' },
  { id: 3, name: 'React', type: 'info' },
  { id: 4, name: 'Angular', type: 'warning' }
])

const dynamicTags = reactive(['标签一', '标签二', '标签三'])

const handleTagClose = (tagId) => {
  const index = closableTags.findIndex(tag => tag.id === tagId)
  if (index > -1) {
    closableTags.splice(index, 1)
  }
}

const removeDynamicTag = (tag) => {
  const index = dynamicTags.indexOf(tag)
  if (index > -1) {
    dynamicTags.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !dynamicTags.includes(inputValue.value)) {
    dynamicTags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}
</script>

<style scoped>
.closable-tag-demo {
  padding: 20px;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
  flex-wrap: wrap;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

### 3. 带图标的标签

```vue
<template>
  <div class="icon-tag-demo">
    <h3>左侧图标</h3>
    <div class="tag-row">
      <FuniTag type="primary" icon="User">用户</FuniTag>
      <FuniTag type="success" icon="Check">已完成</FuniTag>
      <FuniTag type="warning" icon="Warning">警告</FuniTag>
      <FuniTag type="danger" icon="Close">错误</FuniTag>
      <FuniTag type="info" icon="InfoFilled">信息</FuniTag>
    </div>
    
    <h3>右侧图标</h3>
    <div class="tag-row">
      <FuniTag type="primary" icon="ArrowRight" icon-position="right">下一步</FuniTag>
      <FuniTag type="success" icon="Download" icon-position="right">下载</FuniTag>
      <FuniTag type="info" icon="Link" icon-position="right">链接</FuniTag>
    </div>
    
    <h3>加载状态</h3>
    <div class="tag-row">
      <FuniTag type="primary" loading>加载中</FuniTag>
      <FuniTag type="info" loading loading-icon="Refresh">刷新中</FuniTag>
    </div>
  </div>
</template>

<style scoped>
.icon-tag-demo {
  padding: 20px;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
  flex-wrap: wrap;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

### 4. 可选择标签

```vue
<template>
  <div class="selectable-tag-demo">
    <h3>单选标签</h3>
    <div class="tag-row">
      <FuniTag
        v-for="tag in singleSelectTags"
        :key="tag.value"
        :type="tag.value === selectedSingle ? 'primary' : 'info'"
        :selected="tag.value === selectedSingle"
        selectable
        clickable
        @click="handleSingleSelect(tag.value)"
      >
        {{ tag.label }}
      </FuniTag>
    </div>
    <p class="result">选中：{{ selectedSingle || '无' }}</p>
    
    <h3>多选标签</h3>
    <div class="tag-row">
      <FuniTag
        v-for="tag in multiSelectTags"
        :key="tag.value"
        :type="selectedMultiple.includes(tag.value) ? 'success' : 'info'"
        :selected="selectedMultiple.includes(tag.value)"
        selectable
        clickable
        @click="handleMultipleSelect(tag.value)"
      >
        {{ tag.label }}
      </FuniTag>
    </div>
    <p class="result">选中：{{ selectedMultiple.join(', ') || '无' }}</p>
    
    <h3>筛选标签</h3>
    <div class="tag-row">
      <FuniTag
        v-for="filter in filterTags"
        :key="filter.key"
        :type="activeFilters.includes(filter.key) ? 'success' : ''"
        :selected="activeFilters.includes(filter.key)"
        selectable
        clickable
        closable
        @click="toggleFilter(filter.key)"
        @close="removeFilter(filter.key)"
      >
        {{ filter.label }}
      </FuniTag>
    </div>
    <div class="filter-result">
      <p>当前筛选：{{ getFilterText() }}</p>
      <el-button size="small" @click="clearAllFilters">清空筛选</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const selectedSingle = ref('')
const selectedMultiple = reactive([])
const activeFilters = reactive([])

const singleSelectTags = reactive([
  { value: 'frontend', label: '前端' },
  { value: 'backend', label: '后端' },
  { value: 'mobile', label: '移动端' },
  { value: 'devops', label: '运维' }
])

const multiSelectTags = reactive([
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'vue', label: 'Vue.js' },
  { value: 'react', label: 'React' },
  { value: 'angular', label: 'Angular' }
])

const filterTags = reactive([
  { key: 'status', label: '状态' },
  { key: 'priority', label: '优先级' },
  { key: 'category', label: '分类' },
  { key: 'assignee', label: '负责人' }
])

const handleSingleSelect = (value) => {
  selectedSingle.value = selectedSingle.value === value ? '' : value
}

const handleMultipleSelect = (value) => {
  const index = selectedMultiple.indexOf(value)
  if (index > -1) {
    selectedMultiple.splice(index, 1)
  } else {
    selectedMultiple.push(value)
  }
}

const toggleFilter = (key) => {
  const index = activeFilters.indexOf(key)
  if (index > -1) {
    activeFilters.splice(index, 1)
  } else {
    activeFilters.push(key)
  }
}

const removeFilter = (key) => {
  const index = activeFilters.indexOf(key)
  if (index > -1) {
    activeFilters.splice(index, 1)
  }
}

const getFilterText = () => {
  if (activeFilters.length === 0) return '无'
  return filterTags
    .filter(filter => activeFilters.includes(filter.key))
    .map(filter => filter.label)
    .join('、')
}

const clearAllFilters = () => {
  activeFilters.splice(0)
}
</script>

<style scoped>
.selectable-tag-demo {
  padding: 20px;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
  flex-wrap: wrap;
}

.result {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.filter-result {
  margin-top: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.filter-result p {
  margin: 0 0 8px 0;
  color: #606266;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

## 高级功能示例

### 5. 特殊效果标签

```vue
<template>
  <div class="special-effect-demo">
    <h3>渐变标签</h3>
    <div class="tag-row">
      <FuniTag
        gradient
        :gradient-colors="['#ff6b6b', '#feca57']"
        effect="dark"
      >
        渐变标签1
      </FuniTag>
      <FuniTag
        gradient
        :gradient-colors="['#48cae4', '#023e8a']"
        effect="dark"
      >
        渐变标签2
      </FuniTag>
      <FuniTag
        gradient
        :gradient-colors="['#f093fb', '#f5576c']"
        effect="dark"
      >
        渐变标签3
      </FuniTag>
    </div>

    <h3>动画标签</h3>
    <div class="tag-row">
      <FuniTag type="primary" animation="pulse">脉冲动画</FuniTag>
      <FuniTag type="success" animation="bounce">弹跳动画</FuniTag>
      <FuniTag type="warning" animation="shake">摇摆动画</FuniTag>
      <FuniTag type="danger" animation="flip">翻转动画</FuniTag>
    </div>

    <h3>带徽章的标签</h3>
    <div class="tag-row">
      <FuniTag type="primary" badge="5">消息</FuniTag>
      <FuniTag type="success" badge="99+" badge-type="warning">通知</FuniTag>
      <FuniTag type="info" badge="NEW" badge-type="danger">功能</FuniTag>
    </div>

    <h3>阴影效果</h3>
    <div class="tag-row">
      <FuniTag type="primary" shadow="always">总是阴影</FuniTag>
      <FuniTag type="success" shadow="hover">悬停阴影</FuniTag>
      <FuniTag type="info" shadow="never">无阴影</FuniTag>
    </div>
  </div>
</template>

<style scoped>
.special-effect-demo {
  padding: 20px;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 16px 0;
  flex-wrap: wrap;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

### 6. 功能性标签

```vue
<template>
  <div class="functional-tag-demo">
    <h3>可复制标签</h3>
    <div class="tag-row">
      <FuniTag
        type="info"
        copyable
        tooltip="点击复制"
        @copy="handleCopy"
      >
        复制这段文本
      </FuniTag>
      <FuniTag
        type="primary"
        copyable
        tooltip="点击复制代码"
        @copy="handleCopy"
      >
        npm install funi-ui
      </FuniTag>
    </div>

    <h3>省略显示标签</h3>
    <div class="tag-row">
      <FuniTag
        type="info"
        max-width="100px"
        ellipsis
        tooltip="这是一个很长的标签文本内容"
      >
        这是一个很长的标签文本内容
      </FuniTag>
      <FuniTag
        type="primary"
        max-width="80px"
        ellipsis
        tooltip="超长文本标签"
      >
        超长文本标签
      </FuniTag>
    </div>

    <h3>禁用状态标签</h3>
    <div class="tag-row">
      <FuniTag type="primary" disabled>禁用标签</FuniTag>
      <FuniTag type="success" disabled clickable>禁用可点击</FuniTag>
      <FuniTag type="warning" disabled closable>禁用可关闭</FuniTag>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const handleCopy = (text) => {
  console.log('复制文本:', text)
  ElMessage.success(`已复制：${text}`)
}
</script>

<style scoped>
.functional-tag-demo {
  padding: 20px;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
  flex-wrap: wrap;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```
