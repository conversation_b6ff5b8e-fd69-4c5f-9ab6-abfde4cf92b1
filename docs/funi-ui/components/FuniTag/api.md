# FuniTag API文档

## 组件概述

FuniTag是基于ElementPlus的el-tag封装的标签组件，支持多种样式、状态、交互效果，提供了图标、关闭、点击、拖拽等功能，适用于标签展示、状态标识、分类筛选等场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| type | String | '' | - | 标签类型 | el-tag.type |
| closable | Boolean | false | - | 是否可关闭 | el-tag.closable |
| disableTransitions | Boolean | false | - | 是否禁用渐变动画 | el-tag.disable-transitions |
| hit | Boolean | false | - | 是否有边框描边 | el-tag.hit |
| color | String | '' | - | 背景色 | el-tag.color |
| size | String | 'default' | - | 标签尺寸 | el-tag.size |
| effect | String | 'light' | - | 主题效果 | el-tag.effect |
| round | Boolean | false | - | 是否圆角 | el-tag.round |
| icon | String | '' | - | 图标 | - |
| iconPosition | String | 'left' | - | 图标位置 | - |
| clickable | Boolean | false | - | 是否可点击 | - |
| selectable | Boolean | false | - | 是否可选择 | - |
| selected | Boolean | false | - | 是否选中 | - |
| disabled | Boolean | false | - | 是否禁用 | - |
| loading | Boolean | false | - | 是否加载中 | - |
| loadingIcon | String | 'Loading' | - | 加载图标 | - |
| tooltip | String | '' | - | 提示信息 | - |
| tooltipPlacement | String | 'top' | - | 提示位置 | - |
| maxWidth | String | '' | - | 最大宽度 | - |
| ellipsis | Boolean | false | - | 是否省略显示 | - |
| copyable | Boolean | false | - | 是否可复制 | - |
| draggable | Boolean | false | - | 是否可拖拽 | - |
| badge | String/Number | '' | - | 徽章内容 | - |
| badgeType | String | 'danger' | - | 徽章类型 | - |
| animation | String | '' | - | 动画效果 | - |
| gradient | Boolean | false | - | 是否渐变背景 | - |
| gradientColors | Array | [] | - | 渐变颜色 | - |
| border | Boolean | true | - | 是否显示边框 | - |
| borderStyle | String | 'solid' | - | 边框样式 | - |
| shadow | String | 'never' | - | 阴影效果 | - |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| close | event: Event | 关闭事件 | 点击关闭按钮时 |
| click | event: Event | 点击事件 | 点击标签时 |
| select | selected: Boolean | 选择事件 | 选择状态变化时 |
| copy | text: String | 复制事件 | 复制文本时 |
| drag-start | event: DragEvent | 拖拽开始事件 | 开始拖拽时 |
| drag-end | event: DragEvent | 拖拽结束事件 | 拖拽结束时 |
| mouseenter | event: Event | 鼠标进入事件 | 鼠标进入时 |
| mouseleave | event: Event | 鼠标离开事件 | 鼠标离开时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| close | - | void | 关闭标签 |
| select | (selected?: Boolean) | void | 设置选择状态 |
| copy | - | Promise | 复制标签内容 |
| focus | - | void | 获得焦点 |
| blur | - | void | 失去焦点 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 标签内容 |
| icon | - | 自定义图标 |
| close | - | 自定义关闭按钮 |
| badge | { badge } | 自定义徽章内容 |

## 标签类型

```typescript
type TagType =
  | 'primary'   // 主要
  | 'success'   // 成功
  | 'info'      // 信息
  | 'warning'   // 警告
  | 'danger';   // 危险
```

## 主题效果

```typescript
type TagEffect =
  | 'dark'      // 深色主题
  | 'light'     // 浅色主题
  | 'plain';    // 朴素主题
```

## 动画效果

```typescript
type AnimationType =
  | 'fade'      // 淡入淡出
  | 'slide'     // 滑动
  | 'bounce'    // 弹跳
  | 'pulse'     // 脉冲
  | 'shake'     // 摇摆
  | 'flip';     // 翻转
```

## 使用示例

### 基础标签
```vue
<template>
  <div class="tag-examples">
    <div class="example-group">
      <h4>基础类型</h4>
      <div class="tag-row">
        <FuniTag>默认标签</FuniTag>
        <FuniTag type="primary">主要标签</FuniTag>
        <FuniTag type="success">成功标签</FuniTag>
        <FuniTag type="info">信息标签</FuniTag>
        <FuniTag type="warning">警告标签</FuniTag>
        <FuniTag type="danger">危险标签</FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>不同尺寸</h4>
      <div class="tag-row">
        <FuniTag size="small" type="primary">小型标签</FuniTag>
        <FuniTag size="default" type="primary">默认标签</FuniTag>
        <FuniTag size="large" type="primary">大型标签</FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>主题效果</h4>
      <div class="tag-row">
        <FuniTag type="primary" effect="dark">深色主题</FuniTag>
        <FuniTag type="primary" effect="light">浅色主题</FuniTag>
        <FuniTag type="primary" effect="plain">朴素主题</FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>圆角标签</h4>
      <div class="tag-row">
        <FuniTag type="primary" round>圆角标签</FuniTag>
        <FuniTag type="success" round>成功标签</FuniTag>
        <FuniTag type="warning" round>警告标签</FuniTag>
      </div>
    </div>
  </div>
</template>

<style scoped>
.tag-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-group h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
```

### 可关闭标签
```vue
<template>
  <div class="closable-tags">
    <div class="example-group">
      <h4>可关闭标签</h4>
      <div class="tag-row">
        <FuniTag
          v-for="tag in closableTags"
          :key="tag.id"
          :type="tag.type"
          closable
          @close="handleTagClose(tag.id)"
        >
          {{ tag.name }}
        </FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>动态添加标签</h4>
      <div class="tag-row">
        <FuniTag
          v-for="tag in dynamicTags"
          :key="tag"
          type="info"
          closable
          @close="removeDynamicTag(tag)"
        >
          {{ tag }}
        </FuniTag>

        <el-input
          v-if="inputVisible"
          ref="inputRef"
          v-model="inputValue"
          size="small"
          style="width: 100px;"
          @keyup.enter="handleInputConfirm"
          @blur="handleInputConfirm"
        />
        <el-button
          v-else
          size="small"
          @click="showInput"
        >
          + 新标签
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'

const inputRef = ref()
const inputVisible = ref(false)
const inputValue = ref('')

const closableTags = reactive([
  { id: 1, name: 'JavaScript', type: 'primary' },
  { id: 2, name: 'Vue.js', type: 'success' },
  { id: 3, name: 'React', type: 'info' },
  { id: 4, name: 'Angular', type: 'warning' }
])

const dynamicTags = reactive(['标签一', '标签二', '标签三'])

const handleTagClose = (tagId) => {
  const index = closableTags.findIndex(tag => tag.id === tagId)
  if (index > -1) {
    closableTags.splice(index, 1)
  }
}

const removeDynamicTag = (tag) => {
  const index = dynamicTags.indexOf(tag)
  if (index > -1) {
    dynamicTags.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !dynamicTags.includes(inputValue.value)) {
    dynamicTags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}
</script>

<style scoped>
.closable-tags {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-group h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
```

### 带图标的标签
```vue
<template>
  <div class="icon-tags">
    <div class="example-group">
      <h4>左侧图标</h4>
      <div class="tag-row">
        <FuniTag type="primary" icon="User">用户</FuniTag>
        <FuniTag type="success" icon="Check">已完成</FuniTag>
        <FuniTag type="warning" icon="Warning">警告</FuniTag>
        <FuniTag type="danger" icon="Close">错误</FuniTag>
        <FuniTag type="info" icon="InfoFilled">信息</FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>右侧图标</h4>
      <div class="tag-row">
        <FuniTag type="primary" icon="ArrowRight" icon-position="right">下一步</FuniTag>
        <FuniTag type="success" icon="Download" icon-position="right">下载</FuniTag>
        <FuniTag type="info" icon="Link" icon-position="right">链接</FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>加载状态</h4>
      <div class="tag-row">
        <FuniTag type="primary" loading>加载中</FuniTag>
        <FuniTag type="success" loading loading-icon="Refresh">刷新中</FuniTag>
      </div>
    </div>
  </div>
</template>

<style scoped>
.icon-tags {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-group h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
```

### 可选择标签
```vue
<template>
  <div class="selectable-tags">
    <div class="example-group">
      <h4>单选标签</h4>
      <div class="tag-row">
        <FuniTag
          v-for="tag in singleSelectTags"
          :key="tag.value"
          :type="tag.value === selectedSingle ? 'primary' : 'info'"
          :selected="tag.value === selectedSingle"
          selectable
          clickable
          @click="handleSingleSelect(tag.value)"
        >
          {{ tag.label }}
        </FuniTag>
      </div>
      <p class="result">选中：{{ selectedSingle || '无' }}</p>
    </div>

    <div class="example-group">
      <h4>多选标签</h4>
      <div class="tag-row">
        <FuniTag
          v-for="tag in multiSelectTags"
          :key="tag.value"
          :type="selectedMultiple.includes(tag.value) ? 'primary' : 'info'"
          :selected="selectedMultiple.includes(tag.value)"
          selectable
          clickable
          @click="handleMultipleSelect(tag.value)"
        >
          {{ tag.label }}
        </FuniTag>
      </div>
      <p class="result">选中：{{ selectedMultiple.join(', ') || '无' }}</p>
    </div>

    <div class="example-group">
      <h4>筛选标签</h4>
      <div class="tag-row">
        <FuniTag
          v-for="filter in filterTags"
          :key="filter.key"
          :type="activeFilters.includes(filter.key) ? 'success' : ''"
          :selected="activeFilters.includes(filter.key)"
          selectable
          clickable
          closable
          @click="toggleFilter(filter.key)"
          @close="removeFilter(filter.key)"
        >
          {{ filter.label }}
        </FuniTag>
      </div>
      <div class="filter-result">
        <p>当前筛选：{{ getFilterText() }}</p>
        <el-button size="small" @click="clearAllFilters">清空筛选</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const selectedSingle = ref('')
const selectedMultiple = ref([])
const activeFilters = ref([])

const singleSelectTags = reactive([
  { label: '全部', value: 'all' },
  { label: '进行中', value: 'progress' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
])

const multiSelectTags = reactive([
  { label: 'JavaScript', value: 'js' },
  { label: 'Vue.js', value: 'vue' },
  { label: 'React', value: 'react' },
  { label: 'Angular', value: 'angular' },
  { label: 'Node.js', value: 'node' }
])

const filterTags = reactive([
  { key: 'type', label: '类型：前端' },
  { key: 'level', label: '级别：高级' },
  { key: 'status', label: '状态：在线' },
  { key: 'location', label: '地区：北京' }
])

const handleSingleSelect = (value) => {
  selectedSingle.value = selectedSingle.value === value ? '' : value
}

const handleMultipleSelect = (value) => {
  const index = selectedMultiple.value.indexOf(value)
  if (index > -1) {
    selectedMultiple.value.splice(index, 1)
  } else {
    selectedMultiple.value.push(value)
  }
}

const toggleFilter = (key) => {
  const index = activeFilters.value.indexOf(key)
  if (index > -1) {
    activeFilters.value.splice(index, 1)
  } else {
    activeFilters.value.push(key)
  }
}

const removeFilter = (key) => {
  const index = activeFilters.value.indexOf(key)
  if (index > -1) {
    activeFilters.value.splice(index, 1)
  }
}

const clearAllFilters = () => {
  activeFilters.value = []
}

const getFilterText = () => {
  if (activeFilters.value.length === 0) return '无'
  return filterTags
    .filter(filter => activeFilters.value.includes(filter.key))
    .map(filter => filter.label)
    .join('、')
}
</script>

<style scoped>
.selectable-tags {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-group h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.result {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.filter-result {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.filter-result p {
  margin: 0;
  font-size: 14px;
  color: #666;
}
</style>
```

### 特殊效果标签
```vue
<template>
  <div class="special-tags">
    <div class="example-group">
      <h4>渐变标签</h4>
      <div class="tag-row">
        <FuniTag
          gradient
          :gradient-colors="['#ff6b6b', '#feca57']"
          effect="dark"
        >
          渐变标签1
        </FuniTag>
        <FuniTag
          gradient
          :gradient-colors="['#48cae4', '#023e8a']"
          effect="dark"
        >
          渐变标签2
        </FuniTag>
        <FuniTag
          gradient
          :gradient-colors="['#f093fb', '#f5576c']"
          effect="dark"
        >
          渐变标签3
        </FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>动画标签</h4>
      <div class="tag-row">
        <FuniTag type="primary" animation="pulse">脉冲动画</FuniTag>
        <FuniTag type="success" animation="bounce">弹跳动画</FuniTag>
        <FuniTag type="warning" animation="shake">摇摆动画</FuniTag>
        <FuniTag type="danger" animation="flip">翻转动画</FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>带徽章的标签</h4>
      <div class="tag-row">
        <FuniTag type="primary" badge="5">消息</FuniTag>
        <FuniTag type="success" badge="99+" badge-type="warning">通知</FuniTag>
        <FuniTag type="info" badge="NEW" badge-type="danger">功能</FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>可复制标签</h4>
      <div class="tag-row">
        <FuniTag
          type="info"
          copyable
          tooltip="点击复制"
          @copy="handleCopy"
        >
          复制这段文本
        </FuniTag>
        <FuniTag
          type="primary"
          copyable
          tooltip="点击复制代码"
          @copy="handleCopy"
        >
          npm install funi-ui
        </FuniTag>
      </div>
    </div>

    <div class="example-group">
      <h4>可拖拽标签</h4>
      <div class="tag-row">
        <FuniTag
          v-for="tag in draggableTags"
          :key="tag.id"
          :type="tag.type"
          draggable
          @drag-start="handleDragStart"
          @drag-end="handleDragEnd"
        >
          {{ tag.name }}
        </FuniTag>
      </div>
      <div class="drop-zone" @drop="handleDrop" @dragover.prevent>
        拖拽标签到这里
        <div v-if="droppedTags.length" class="dropped-tags">
          <FuniTag
            v-for="tag in droppedTags"
            :key="tag.id"
            :type="tag.type"
            closable
            @close="removeDroppedTag(tag.id)"
          >
            {{ tag.name }}
          </FuniTag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const draggableTags = reactive([
  { id: 1, name: '标签A', type: 'primary' },
  { id: 2, name: '标签B', type: 'success' },
  { id: 3, name: '标签C', type: 'warning' },
  { id: 4, name: '标签D', type: 'danger' }
])

const droppedTags = reactive([])
let draggedTag = null

const handleCopy = (text) => {
  console.log('复制文本:', text)
  ElMessage.success(`已复制：${text}`)
}

const handleDragStart = (event) => {
  const tagElement = event.target.closest('.el-tag')
  const tagText = tagElement.textContent
  draggedTag = draggableTags.find(tag => tag.name === tagText)
  console.log('开始拖拽:', draggedTag)
}

const handleDragEnd = (event) => {
  console.log('拖拽结束')
  draggedTag = null
}

const handleDrop = (event) => {
  event.preventDefault()
  if (draggedTag && !droppedTags.find(tag => tag.id === draggedTag.id)) {
    droppedTags.push({ ...draggedTag })
    ElMessage.success(`已添加标签：${draggedTag.name}`)
  }
}

const removeDroppedTag = (tagId) => {
  const index = droppedTags.findIndex(tag => tag.id === tagId)
  if (index > -1) {
    droppedTags.splice(index, 1)
  }
}
</script>

<style scoped>
.special-tags {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-group h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.tag-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.drop-zone {
  margin-top: 12px;
  padding: 20px;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  text-align: center;
  color: #909399;
  transition: border-color 0.3s;
}

.drop-zone:hover {
  border-color: #409eff;
}

.dropped-tags {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}
</style>
```

## ElementPlus API支持

FuniTag基于el-tag封装，支持所有el-tag的API：

```vue
<template>
  <FuniTag
    <!-- ElementPlus el-tag 所有属性 -->
    type="primary"
    :closable="false"
    :disable-transitions="false"
    :hit="false"
    color=""
    size="default"
    effect="light"
    :round="false"

    <!-- ElementPlus el-tag 所有事件 -->
    @close="handleClose"
    @click="handleClick"
  />
</template>
```

## 注意事项

### 1. 性能优化
- 大量标签时使用虚拟滚动
- 避免频繁的动画效果
- 合理使用防抖处理点击事件
- 优化拖拽操作的性能

### 2. 用户体验
- 提供清晰的交互反馈
- 合理设置标签的尺寸和间距
- 支持键盘导航
- 提供无障碍访问支持

### 3. 视觉设计
- 保持标签样式的一致性
- 合理使用颜色和对比度
- 避免过度的视觉效果
- 考虑不同主题的适配

### 4. 功能设计
- 合理设计标签的交互逻辑
- 提供清晰的状态反馈
- 支持标签的批量操作
- 处理异常情况和边界条件

## 常见问题

### Q: 如何实现标签的批量选择？
A: 结合复选框或使用Ctrl+点击的方式实现多选

### Q: 如何自定义标签的样式？
A: 使用CSS变量或通过color、gradient等属性自定义

### Q: 如何实现标签的搜索和过滤？
A: 结合搜索框组件，根据关键字过滤标签列表

### Q: 如何处理标签内容过长的问题？
A: 使用ellipsis属性启用省略显示，或设置maxWidth限制宽度