# FuniTag 最佳实践

## 推荐用法和配置

### 1. 标签类型选择最佳实践

#### 语义化类型使用

```vue
<template>
  <div class="semantic-tags">
    <!-- 状态标签 -->
    <div class="status-section">
      <h4>任务状态</h4>
      <FuniTag type="success" icon="Check">已完成</FuniTag>
      <FuniTag type="primary" icon="Loading">进行中</FuniTag>
      <FuniTag type="info" icon="Clock">待开始</FuniTag>
      <FuniTag type="danger" icon="Close">已取消</FuniTag>
    </div>
    
    <!-- 优先级标签 -->
    <div class="priority-section">
      <h4>优先级</h4>
      <FuniTag type="danger" effect="dark">紧急</FuniTag>
      <FuniTag type="warning" effect="dark">高</FuniTag>
      <FuniTag type="primary" effect="light">中</FuniTag>
      <FuniTag type="info" effect="light">低</FuniTag>
    </div>
    
    <!-- 分类标签 -->
    <div class="category-section">
      <h4>技术栈</h4>
      <FuniTag type="primary" round>Vue.js</FuniTag>
      <FuniTag type="success" round>React</FuniTag>
      <FuniTag type="warning" round>Angular</FuniTag>
      <FuniTag type="info" round>Node.js</FuniTag>
    </div>
  </div>
</template>

<style scoped>
.semantic-tags {
  padding: 20px;
}

.status-section,
.priority-section,
.category-section {
  margin-bottom: 20px;
}

h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}
</style>
```

#### 颜色对比度考虑

```vue
<template>
  <div class="contrast-demo">
    <!-- ✅ 推荐：良好的对比度 -->
    <div class="good-contrast">
      <h4>良好对比度</h4>
      <FuniTag type="primary" effect="dark">深色主题</FuniTag>
      <FuniTag type="success" effect="light">浅色主题</FuniTag>
      <FuniTag color="#1890ff" effect="dark">自定义深色</FuniTag>
    </div>
    
    <!-- ❌ 避免：对比度不足 -->
    <div class="poor-contrast">
      <h4>避免使用（对比度不足）</h4>
      <FuniTag color="#f0f0f0" style="color: #ccc;">对比度不足</FuniTag>
      <FuniTag color="#ffeb3b" style="color: #fff;">黄底白字</FuniTag>
    </div>
  </div>
</template>
```

### 2. 尺寸选择最佳实践

#### 不同场景的尺寸选择

```vue
<template>
  <div class="size-practices">
    <!-- 表格中的标签：使用小尺寸 -->
    <div class="table-tags">
      <h4>表格中的状态标签</h4>
      <div class="mock-table">
        <div class="table-row">
          <span class="table-cell">任务名称</span>
          <span class="table-cell">
            <FuniTag size="small" type="success">已完成</FuniTag>
          </span>
        </div>
        <div class="table-row">
          <span class="table-cell">项目开发</span>
          <span class="table-cell">
            <FuniTag size="small" type="primary">进行中</FuniTag>
          </span>
        </div>
      </div>
    </div>
    
    <!-- 卡片中的标签：使用默认尺寸 -->
    <div class="card-tags">
      <h4>卡片中的分类标签</h4>
      <div class="mock-card">
        <h5>文章标题</h5>
        <div class="card-tags-container">
          <FuniTag size="default" type="primary">前端</FuniTag>
          <FuniTag size="default" type="success">Vue.js</FuniTag>
          <FuniTag size="default" type="info">教程</FuniTag>
        </div>
      </div>
    </div>
    
    <!-- 重要标签：使用大尺寸 -->
    <div class="important-tags">
      <h4>重要提示标签</h4>
      <FuniTag size="large" type="danger" icon="Warning">重要通知</FuniTag>
      <FuniTag size="large" type="success" icon="Check">操作成功</FuniTag>
    </div>
  </div>
</template>

<style scoped>
.size-practices {
  padding: 20px;
}

.table-tags,
.card-tags,
.important-tags {
  margin-bottom: 24px;
}

.mock-table {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 8px 12px;
  border-right: 1px solid #f0f0f0;
}

.table-cell:last-child {
  border-right: none;
}

.mock-card {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
}

.card-tags-container {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

h4, h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

h4 {
  font-size: 14px;
}

h5 {
  font-size: 16px;
}
</style>
```

### 3. 交互设计最佳实践

#### 可点击标签的反馈设计

```vue
<template>
  <div class="interactive-practices">
    <!-- 筛选标签 -->
    <div class="filter-section">
      <h4>筛选标签（提供明确的选中状态）</h4>
      <div class="filter-tags">
        <FuniTag
          v-for="filter in filters"
          :key="filter.key"
          :type="activeFilters.includes(filter.key) ? 'primary' : ''"
          :selected="activeFilters.includes(filter.key)"
          clickable
          selectable
          class="filter-tag"
          @click="toggleFilter(filter.key)"
        >
          {{ filter.label }}
        </FuniTag>
      </div>
    </div>
    
    <!-- 操作标签 -->
    <div class="action-section">
      <h4>操作标签（提供悬停和点击反馈）</h4>
      <div class="action-tags">
        <FuniTag
          type="primary"
          clickable
          icon="Edit"
          class="action-tag"
          @click="handleEdit"
        >
          编辑
        </FuniTag>
        <FuniTag
          type="success"
          clickable
          icon="Download"
          class="action-tag"
          @click="handleDownload"
        >
          下载
        </FuniTag>
        <FuniTag
          type="danger"
          clickable
          icon="Delete"
          class="action-tag"
          @click="handleDelete"
        >
          删除
        </FuniTag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const activeFilters = reactive([])

const filters = reactive([
  { key: 'frontend', label: '前端' },
  { key: 'backend', label: '后端' },
  { key: 'mobile', label: '移动端' },
  { key: 'devops', label: '运维' }
])

const toggleFilter = (key) => {
  const index = activeFilters.indexOf(key)
  if (index > -1) {
    activeFilters.splice(index, 1)
  } else {
    activeFilters.push(key)
  }
}

const handleEdit = () => {
  ElMessage.info('编辑操作')
}

const handleDownload = () => {
  ElMessage.success('下载开始')
}

const handleDelete = () => {
  ElMessage.warning('删除操作')
}
</script>

<style scoped>
.interactive-practices {
  padding: 20px;
}

.filter-section,
.action-section {
  margin-bottom: 24px;
}

.filter-tags,
.action-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-tag:active {
  transform: scale(0.98);
}

h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}
</style>
```

#### 键盘导航支持

```vue
<template>
  <div class="keyboard-navigation">
    <h4>支持键盘导航的标签</h4>
    <div class="navigable-tags">
      <FuniTag
        v-for="(tag, index) in navigableTags"
        :key="tag.id"
        :type="tag.type"
        clickable
        tabindex="0"
        class="navigable-tag"
        @click="handleTagClick(tag)"
        @keydown.enter="handleTagClick(tag)"
        @keydown.space.prevent="handleTagClick(tag)"
        @focus="handleTagFocus(index)"
      >
        {{ tag.name }}
      </FuniTag>
    </div>
    <p class="keyboard-hint">使用 Tab 键导航，Enter 或 Space 键选择</p>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'

const navigableTags = reactive([
  { id: 1, name: '标签1', type: 'primary' },
  { id: 2, name: '标签2', type: 'success' },
  { id: 3, name: '标签3', type: 'warning' },
  { id: 4, name: '标签4', type: 'info' }
])

const handleTagClick = (tag) => {
  ElMessage.info(`选择了：${tag.name}`)
}

const handleTagFocus = (index) => {
  console.log(`焦点在第 ${index + 1} 个标签`)
}
</script>

<style scoped>
.keyboard-navigation {
  padding: 20px;
}

.navigable-tags {
  display: flex;
  gap: 8px;
  margin: 12px 0;
}

.navigable-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.navigable-tag:focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

.keyboard-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}
</style>
```

### 4. 动态标签管理最佳实践

#### 标签的添加和删除

```vue
<template>
  <div class="dynamic-tag-management">
    <h4>技能标签管理</h4>
    <div class="tag-container">
      <FuniTag
        v-for="skill in skills"
        :key="skill"
        type="primary"
        closable
        class="skill-tag"
        @close="removeSkill(skill)"
      >
        {{ skill }}
      </FuniTag>
      
      <!-- 添加新标签 -->
      <div class="add-tag-container">
        <el-input
          v-if="inputVisible"
          ref="inputRef"
          v-model="inputValue"
          size="small"
          class="tag-input"
          @keyup.enter="handleInputConfirm"
          @blur="handleInputConfirm"
          @keyup.esc="cancelInput"
        />
        <FuniTag
          v-else
          type="info"
          effect="plain"
          clickable
          class="add-tag"
          @click="showInput"
        >
          <template #icon>
            <el-icon><Plus /></el-icon>
          </template>
          添加技能
        </FuniTag>
      </div>
    </div>
    
    <div class="tag-suggestions" v-if="inputVisible && suggestions.length > 0">
      <h5>建议标签：</h5>
      <div class="suggestion-tags">
        <FuniTag
          v-for="suggestion in filteredSuggestions"
          :key="suggestion"
          type="info"
          size="small"
          clickable
          @click="addSuggestion(suggestion)"
        >
          {{ suggestion }}
        </FuniTag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { Plus } from '@element-plus/icons-vue'

const inputRef = ref()
const inputVisible = ref(false)
const inputValue = ref('')

const skills = reactive(['JavaScript', 'Vue.js', 'CSS'])

const suggestions = reactive([
  'TypeScript', 'React', 'Angular', 'Node.js', 'Python', 
  'Java', 'HTML', 'SCSS', 'Webpack', 'Vite'
])

const filteredSuggestions = computed(() => {
  if (!inputValue.value) return suggestions.slice(0, 5)
  return suggestions
    .filter(s => 
      s.toLowerCase().includes(inputValue.value.toLowerCase()) &&
      !skills.includes(s)
    )
    .slice(0, 5)
})

const removeSkill = (skill) => {
  const index = skills.indexOf(skill)
  if (index > -1) {
    skills.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  const value = inputValue.value.trim()
  if (value && !skills.includes(value)) {
    skills.push(value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const cancelInput = () => {
  inputVisible.value = false
  inputValue.value = ''
}

const addSuggestion = (suggestion) => {
  if (!skills.includes(suggestion)) {
    skills.push(suggestion)
  }
  inputVisible.value = false
  inputValue.value = ''
}
</script>

<style scoped>
.dynamic-tag-management {
  padding: 20px;
}

.tag-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
  margin: 12px 0;
}

.skill-tag {
  margin-bottom: 4px;
}

.add-tag-container {
  display: flex;
  align-items: center;
}

.tag-input {
  width: 120px;
}

.add-tag {
  cursor: pointer;
  border-style: dashed;
}

.tag-suggestions {
  margin-top: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.suggestion-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-top: 8px;
}

h4, h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

h4 {
  font-size: 14px;
}

h5 {
  font-size: 12px;
  color: #606266;
}
</style>
```

## 避免的用法和常见错误

### 1. 标签过度使用

```vue
<!-- ❌ 错误：标签过多，视觉混乱 -->
<template>
  <div class="bad-example">
    <div class="article-item">
      <h3>文章标题</h3>
      <div class="too-many-tags">
        <FuniTag type="primary">前端</FuniTag>
        <FuniTag type="success">Vue.js</FuniTag>
        <FuniTag type="info">JavaScript</FuniTag>
        <FuniTag type="warning">TypeScript</FuniTag>
        <FuniTag type="danger">CSS</FuniTag>
        <FuniTag type="primary">HTML</FuniTag>
        <FuniTag type="success">Webpack</FuniTag>
        <FuniTag type="info">Vite</FuniTag>
        <FuniTag type="warning">ESLint</FuniTag>
        <FuniTag type="danger">Prettier</FuniTag>
      </div>
    </div>
  </div>
</template>

<!-- ✅ 正确：合理数量的标签 -->
<template>
  <div class="good-example">
    <div class="article-item">
      <h3>文章标题</h3>
      <div class="reasonable-tags">
        <FuniTag type="primary">前端</FuniTag>
        <FuniTag type="success">Vue.js</FuniTag>
        <FuniTag type="info">教程</FuniTag>
        <FuniTag
          v-if="hasMoreTags"
          type=""
          effect="plain"
          clickable
          @click="showAllTags"
        >
          +3 更多
        </FuniTag>
      </div>
    </div>
  </div>
</template>
```

### 2. 颜色语义混乱

```vue
<!-- ❌ 错误：颜色语义不一致 -->
<template>
  <div class="semantic-error">
    <FuniTag type="success">错误状态</FuniTag>  <!-- 成功色表示错误 -->
    <FuniTag type="danger">正常状态</FuniTag>   <!-- 危险色表示正常 -->
    <FuniTag type="warning">已完成</FuniTag>   <!-- 警告色表示完成 -->
  </div>
</template>

<!-- ✅ 正确：颜色语义一致 -->
<template>
  <div class="semantic-correct">
    <FuniTag type="danger" icon="Close">错误状态</FuniTag>
    <FuniTag type="success" icon="Check">正常状态</FuniTag>
    <FuniTag type="success" icon="Check">已完成</FuniTag>
  </div>
</template>
```

### 3. 交互反馈不足

```vue
<!-- ❌ 错误：缺少交互反馈 -->
<template>
  <div class="poor-interaction">
    <FuniTag
      clickable
      @click="handleClick"
    >
      点击我（无反馈）
    </FuniTag>
  </div>
</template>

<!-- ✅ 正确：提供清晰的交互反馈 -->
<template>
  <div class="good-interaction">
    <FuniTag
      clickable
      hoverable
      tooltip="点击查看详情"
      class="interactive-tag"
      @click="handleClick"
    >
      点击我
    </FuniTag>
  </div>
</template>

<style scoped>
.interactive-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.interactive-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
```

## 性能优化建议

### 1. 大量标签的虚拟化

```vue
<template>
  <div class="virtual-tags">
    <h4>大量标签优化</h4>
    <!-- 使用虚拟滚动处理大量标签 -->
    <el-virtual-list
      :data="largeTags"
      :height="200"
      :item-size="32"
      class="tag-virtual-list"
    >
      <template #default="{ item, index }">
        <div class="virtual-tag-item">
          <FuniTag
            :type="item.type"
            :key="item.id"
            size="small"
            @click="handleTagClick(item)"
          >
            {{ item.name }}
          </FuniTag>
        </div>
      </template>
    </el-virtual-list>
  </div>
</template>

<script setup>
import { reactive } from 'vue'

// 模拟大量标签数据
const largeTags = reactive(
  Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    name: `标签${i + 1}`,
    type: ['primary', 'success', 'info', 'warning', 'danger'][i % 5]
  }))
)

const handleTagClick = (tag) => {
  console.log('点击标签:', tag.name)
}
</script>

<style scoped>
.virtual-tags {
  padding: 20px;
}

.tag-virtual-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.virtual-tag-item {
  padding: 4px 8px;
  border-bottom: 1px solid #f0f0f0;
}

h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}
</style>
```

### 2. 防抖处理

```vue
<template>
  <div class="debounced-tags">
    <h4>防抖处理的搜索标签</h4>
    <div class="search-tags">
      <FuniTag
        v-for="tag in searchTags"
        :key="tag.id"
        :type="tag.selected ? 'primary' : 'info'"
        clickable
        @click="debouncedToggle(tag)"
      >
        {{ tag.name }}
      </FuniTag>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { debounce } from 'lodash-es'

const searchTags = reactive([
  { id: 1, name: '前端', selected: false },
  { id: 2, name: '后端', selected: false },
  { id: 3, name: '移动端', selected: false },
  { id: 4, name: '全栈', selected: false }
])

const toggleTag = (tag) => {
  tag.selected = !tag.selected
  // 执行搜索逻辑
  console.log('执行搜索:', searchTags.filter(t => t.selected).map(t => t.name))
}

// 使用防抖避免频繁触发搜索
const debouncedToggle = debounce(toggleTag, 300)
</script>
```

### 3. 内存管理

```vue
<template>
  <div class="memory-optimized">
    <h4>内存优化的动态标签</h4>
    <div class="dynamic-tags">
      <FuniTag
        v-for="tag in visibleTags"
        :key="tag.id"
        :type="tag.type"
        closable
        @close="removeTag(tag.id)"
      >
        {{ tag.name }}
      </FuniTag>
    </div>
    <p class="tag-count">显示 {{ visibleTags.length }} / {{ totalTags.length }} 个标签</p>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from 'vue'

const totalTags = reactive([])
const maxVisibleTags = 50

// 只显示前50个标签，避免DOM节点过多
const visibleTags = computed(() =>
  totalTags.slice(0, maxVisibleTags)
)

const removeTag = (tagId) => {
  const index = totalTags.findIndex(tag => tag.id === tagId)
  if (index > -1) {
    totalTags.splice(index, 1)
  }
}

// 组件卸载时清理数据
onUnmounted(() => {
  totalTags.splice(0)
})
</script>
```

## 业务场景最佳实践

### 1. 电商系统标签

```vue
<template>
  <div class="ecommerce-tags">
    <!-- 商品标签 -->
    <div class="product-tags">
      <h4>商品标签</h4>
      <FuniTag type="danger" effect="dark" animation="pulse">限时特价</FuniTag>
      <FuniTag type="warning" badge="HOT">热销商品</FuniTag>
      <FuniTag type="success" icon="Check">现货</FuniTag>
      <FuniTag type="info" round>包邮</FuniTag>
    </div>

    <!-- 筛选标签 -->
    <div class="filter-tags">
      <h4>商品筛选</h4>
      <div class="filter-group">
        <span class="filter-label">品牌：</span>
        <FuniTag
          v-for="brand in brands"
          :key="brand"
          :type="selectedBrands.includes(brand) ? 'primary' : ''"
          selectable
          clickable
          @click="toggleBrand(brand)"
        >
          {{ brand }}
        </FuniTag>
      </div>
    </div>
  </div>
</template>
```

### 2. 内容管理系统

```vue
<template>
  <div class="cms-tags">
    <!-- 文章状态 -->
    <div class="article-status">
      <h4>文章状态</h4>
      <FuniTag type="success" icon="Check">已发布</FuniTag>
      <FuniTag type="warning" icon="Edit">草稿</FuniTag>
      <FuniTag type="info" icon="Clock">定时发布</FuniTag>
      <FuniTag type="danger" icon="Hide">已下线</FuniTag>
    </div>

    <!-- 内容分类 -->
    <div class="content-categories">
      <h4>内容分类</h4>
      <FuniTag type="primary" round>技术</FuniTag>
      <FuniTag type="success" round>生活</FuniTag>
      <FuniTag type="info" round>娱乐</FuniTag>
      <FuniTag type="warning" round>教育</FuniTag>
    </div>
  </div>
</template>
```

### 3. 项目管理系统

```vue
<template>
  <div class="project-management">
    <!-- 任务优先级 -->
    <div class="task-priority">
      <h4>任务优先级</h4>
      <FuniTag type="danger" effect="dark" animation="pulse">紧急</FuniTag>
      <FuniTag type="warning" effect="dark">高</FuniTag>
      <FuniTag type="primary" effect="light">中</FuniTag>
      <FuniTag type="info" effect="light">低</FuniTag>
    </div>

    <!-- 团队成员 -->
    <div class="team-members">
      <h4>负责人</h4>
      <FuniTag
        v-for="member in teamMembers"
        :key="member.id"
        :type="member.role === 'leader' ? 'danger' : 'primary'"
        :badge="member.taskCount"
        clickable
        @click="viewMemberTasks(member)"
      >
        {{ member.name }}
      </FuniTag>
    </div>
  </div>
</template>
```

## 无障碍访问最佳实践

### 1. 语义化标签

```vue
<template>
  <div class="accessible-tags">
    <div role="group" aria-label="文章标签">
      <FuniTag
        v-for="tag in articleTags"
        :key="tag.id"
        :aria-label="`标签: ${tag.name}`"
        role="button"
        tabindex="0"
        @click="handleTagClick(tag)"
        @keydown.enter="handleTagClick(tag)"
      >
        {{ tag.name }}
      </FuniTag>
    </div>
  </div>
</template>
```

### 2. 屏幕阅读器支持

```vue
<template>
  <div class="screen-reader-friendly">
    <FuniTag
      type="success"
      :aria-label="`任务状态: 已完成`"
      role="status"
    >
      已完成
    </FuniTag>

    <FuniTag
      closable
      :aria-label="`删除标签: ${tagName}`"
      @close="removeTag"
    >
      {{ tagName }}
    </FuniTag>
  </div>
</template>
```
