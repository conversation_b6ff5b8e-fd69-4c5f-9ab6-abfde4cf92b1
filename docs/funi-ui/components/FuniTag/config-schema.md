# FuniTag 配置结构定义

## 基础配置结构

### TagConfig 接口定义

```typescript
interface TagConfig {
  // ElementPlus 原生属性
  type?: TagType;
  closable?: boolean;
  disableTransitions?: boolean;
  hit?: boolean;
  color?: string;
  size?: TagSize;
  effect?: TagEffect;
  round?: boolean;
  
  // 图标配置
  icon?: string;
  iconPosition?: IconPosition;
  
  // 交互配置
  clickable?: boolean;
  selectable?: boolean;
  selected?: boolean;
  disabled?: boolean;
  
  // 状态配置
  loading?: boolean;
  loadingIcon?: string;
  
  // 显示配置
  tooltip?: string;
  tooltipPlacement?: TooltipPlacement;
  maxWidth?: string;
  ellipsis?: boolean;
  
  // 功能配置
  copyable?: boolean;
  draggable?: boolean;
  
  // 徽章配置
  badge?: string | number;
  badgeType?: BadgeType;
  
  // 视觉效果
  animation?: AnimationType;
  gradient?: boolean;
  gradientColors?: string[];
  border?: boolean;
  borderStyle?: BorderStyle;
  shadow?: ShadowType;
}
```

### 类型定义

#### 标签类型

```typescript
type TagType = 
  | 'primary'   // 主要标签（蓝色）
  | 'success'   // 成功标签（绿色）
  | 'info'      // 信息标签（灰色）
  | 'warning'   // 警告标签（橙色）
  | 'danger'    // 危险标签（红色）
  | '';         // 默认标签
```

#### 标签尺寸

```typescript
type TagSize = 
  | 'large'     // 大尺寸
  | 'default'   // 默认尺寸
  | 'small';    // 小尺寸
```

#### 主题效果

```typescript
type TagEffect = 
  | 'dark'      // 深色主题
  | 'light'     // 浅色主题
  | 'plain';    // 朴素主题
```

#### 图标位置

```typescript
type IconPosition = 
  | 'left'      // 左侧图标
  | 'right';    // 右侧图标
```

#### 动画类型

```typescript
type AnimationType = 
  | 'fade'      // 淡入淡出
  | 'slide'     // 滑动
  | 'bounce'    // 弹跳
  | 'pulse'     // 脉冲
  | 'shake'     // 摇摆
  | 'flip'      // 翻转
  | '';         // 无动画
```

#### 徽章类型

```typescript
type BadgeType = 
  | 'primary'
  | 'success'
  | 'warning'
  | 'danger'
  | 'info';
```

#### 边框样式

```typescript
type BorderStyle = 
  | 'solid'     // 实线
  | 'dashed'    // 虚线
  | 'dotted'    // 点线
  | 'double';   // 双线
```

#### 阴影类型

```typescript
type ShadowType = 
  | 'always'    // 总是显示
  | 'hover'     // 悬停显示
  | 'never';    // 从不显示
```

#### 提示位置

```typescript
type TooltipPlacement = 
  | 'top'
  | 'top-start'
  | 'top-end'
  | 'bottom'
  | 'bottom-start'
  | 'bottom-end'
  | 'left'
  | 'left-start'
  | 'left-end'
  | 'right'
  | 'right-start'
  | 'right-end';
```

## 详细配置说明

### 基础显示配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| type | TagType | '' | 标签类型，影响颜色主题 |
| size | TagSize | 'default' | 标签尺寸 |
| effect | TagEffect | 'light' | 主题效果 |
| round | boolean | false | 是否圆角显示 |
| color | string | '' | 自定义背景色 |
| hit | boolean | false | 是否有边框描边 |

### 交互功能配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| clickable | boolean | false | 是否可点击 |
| selectable | boolean | false | 是否可选择 |
| selected | boolean | false | 是否选中状态 |
| disabled | boolean | false | 是否禁用 |
| closable | boolean | false | 是否可关闭 |
| copyable | boolean | false | 是否可复制 |
| draggable | boolean | false | 是否可拖拽 |

### 图标配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| icon | string | '' | 图标名称（ElementPlus图标） |
| iconPosition | IconPosition | 'left' | 图标位置 |
| loading | boolean | false | 是否显示加载状态 |
| loadingIcon | string | 'Loading' | 加载图标名称 |

### 显示效果配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| tooltip | string | '' | 提示信息 |
| tooltipPlacement | TooltipPlacement | 'top' | 提示位置 |
| maxWidth | string | '' | 最大宽度 |
| ellipsis | boolean | false | 是否省略显示 |
| disableTransitions | boolean | false | 是否禁用过渡动画 |

### 视觉效果配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| animation | AnimationType | '' | 动画效果 |
| gradient | boolean | false | 是否渐变背景 |
| gradientColors | string[] | [] | 渐变颜色数组 |
| border | boolean | true | 是否显示边框 |
| borderStyle | BorderStyle | 'solid' | 边框样式 |
| shadow | ShadowType | 'never' | 阴影效果 |

### 徽章配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| badge | string/number | '' | 徽章内容 |
| badgeType | BadgeType | 'danger' | 徽章类型 |

## 常用配置组合

### 1. 基础标签组合

```typescript
// 状态标签
const statusTagConfig: TagConfig = {
  type: 'success',
  size: 'small',
  effect: 'light',
  icon: 'Check'
};

// 分类标签
const categoryTagConfig: TagConfig = {
  type: 'info',
  size: 'default',
  effect: 'plain',
  round: true
};

// 操作标签
const actionTagConfig: TagConfig = {
  type: 'primary',
  clickable: true,
  icon: 'Edit',
  tooltip: '点击编辑'
};
```

### 2. 可关闭标签组合

```typescript
const closableTagConfig: TagConfig = {
  type: 'info',
  closable: true,
  size: 'small',
  effect: 'light'
};

// 动态标签
const dynamicTagConfig: TagConfig = {
  type: 'primary',
  closable: true,
  animation: 'fade',
  disableTransitions: false
};
```

### 3. 可选择标签组合

```typescript
// 单选标签
const selectableTagConfig: TagConfig = {
  selectable: true,
  clickable: true,
  type: 'info',
  effect: 'plain'
};

// 多选筛选标签
const filterTagConfig: TagConfig = {
  selectable: true,
  clickable: true,
  closable: true,
  type: 'success',
  effect: 'light'
};
```

### 4. 特殊效果标签组合

```typescript
// 渐变标签
const gradientTagConfig: TagConfig = {
  gradient: true,
  gradientColors: ['#ff6b6b', '#feca57'],
  effect: 'dark',
  round: true
};

// 动画标签
const animatedTagConfig: TagConfig = {
  type: 'primary',
  animation: 'pulse',
  shadow: 'hover'
};

// 徽章标签
const badgeTagConfig: TagConfig = {
  type: 'info',
  badge: '5',
  badgeType: 'danger',
  clickable: true
};
```

### 5. 功能性标签组合

```typescript
// 可复制标签
const copyableTagConfig: TagConfig = {
  type: 'info',
  copyable: true,
  icon: 'DocumentCopy',
  tooltip: '点击复制'
};

// 可拖拽标签
const draggableTagConfig: TagConfig = {
  type: 'primary',
  draggable: true,
  icon: 'Rank',
  tooltip: '可拖拽排序'
};

// 加载状态标签
const loadingTagConfig: TagConfig = {
  type: 'info',
  loading: true,
  loadingIcon: 'Loading',
  disabled: true
};
```

## 最佳实践建议

### 1. 类型选择
- **primary**: 主要操作、重要信息
- **success**: 成功状态、完成状态
- **info**: 一般信息、默认状态
- **warning**: 警告信息、注意事项
- **danger**: 错误状态、危险操作

### 2. 尺寸选择
- **small**: 密集列表、次要信息
- **default**: 常规使用、标准尺寸
- **large**: 重要标签、突出显示

### 3. 效果选择
- **light**: 浅色背景，适合大多数场景
- **dark**: 深色背景，突出显示
- **plain**: 朴素样式，简洁场景

### 4. 交互设计
- 可点击标签提供明确的视觉反馈
- 可选择标签要有清晰的选中状态
- 可关闭标签要有确认机制
- 拖拽标签要有拖拽指示

### 5. 性能优化
- 大量标签使用虚拟滚动
- 避免过度的动画效果
- 合理使用防抖处理交互事件
- 优化渲染性能

### 6. 无障碍访问
- 提供合适的 aria 标签
- 支持键盘导航
- 确保颜色对比度
- 提供文字说明
