# FuniUI组件索引

## 文档完成状态

### ✅ 已完成完整文档的组件

| 组件名 | API文档 | 配置结构 | 使用示例 | ElementPlus集成 | 最佳实践 |
|--------|---------|----------|----------|----------------|----------|
| FuniListPageV2 | ✅ | ✅ | ✅ | ✅ | ✅ |
| FuniDetail | ✅ | ✅ | - | ✅ | ✅ |
| FuniForm | ✅ | ✅ | - | ✅ | - |
| FuniCurd | ✅ | ✅ | - | ✅ | - |
| FuniSelect | ✅ | - | - | ✅ | - |
| FuniChart | ✅ | ✅ | ✅ | - | - |
| FuniEditor | ✅ | - | - | - | - |
| FuniFileTable | ✅ | - | - | - | - |
| FuniAuditButtomBtn | ✅ | - | - | - | - |
| FuniTreeSelect | ✅ | - | - | ✅ | - |
| FuniRUOC | ✅ | - | - | ✅ | - |
| FuniDialog | ✅ | - | - | ✅ | - |
| FuniCurdV2 | ✅ | - | - | ✅ | - |
| FuniSearch | ✅ | - | - | ✅ | - |
| FuniBusAuditDrawer | ✅ | - | - | ✅ | - |
| FuniMoneyInput | ✅ | - | - | ✅ | - |
| FuniImage | ✅ | - | - | ✅ | - |
| FuniIcon | ✅ | - | - | ✅ | - |
| FuniFormV2 | ✅ | ✅ | - | ✅ | - |
| FuniAuthButton | ✅ | - | - | ✅ | - |
| FuniRegion | ✅ | - | - | ✅ | - |
| FuniAvatar | ✅ | - | - | ✅ | - |
| FuniTag | ✅ | - | - | ✅ | - |
| FuniCard | ✅ | - | - | ✅ | - |
| FuniEmpty | ✅ | - | - | ✅ | - |

### 📝 需要补充文档的组件

#### 核心业务组件
- **FuniListPage** - 列表页面(旧版)
- **FuniFormV2** - 表单组件V2
- **FuniCurdPro** - 专业数据表格
- **FuniVCurd** - 虚拟数据表格
- **FuniCurdColumn** - 表格列组件
- **FuniSearchForm** - 搜索表单
- **FuniSearchFormV2** - 搜索表单V2
- **FuniSearchFormV3** - 搜索表单V3

#### 工作流组件
- **FuniProcessBottomBtn** - 流程底部按钮
- **FuniBpmn** - 流程图组件

#### 表单控件组件
- **FuniRUOCLowCode** - 低代码RUOC选择
- **FuniOrgSelect** - 组织选择器
- **FuniAutocomplete** - 自动完成
- **FuniInputNumber** - 数字输入
- **FuniInputNumberRange** - 数字范围
- **FuniIconSelect** - 图标选择器
- **FuniRegion** - 地区选择器
- **FuniSearchRegion** - 搜索地区选择

#### 展示组件
- **FuniLineChart** - 折线图
- **FuniPieChart** - 饼图
- **FuniHistogramChart** - 柱状图
- **FuniRadarChart** - 雷达图
- **FuniScatterplotChart** - 散点图
- **FuniGantt** - 甘特图
- **FuniImageView** - 图片预览
- **FuniCodemirror** - 代码编辑器
- **FuniPreview** - 文件预览
- **FuniFilePreview** - 文件预览器
- **FuniSvg** - SVG图标
- **FuniHighlightCode** - 代码高亮
- **FuniVideo** - 视频播放器
- **FuniReportView** - 报表视图
- **FuniOlMap** - 地图组件
- **FuniCimMapDialog** - CIM地图弹窗

#### 交互组件
- **FuniAuthButton** - 权限按钮
- **FuniActions** - 操作按钮组
- **FuniTeleport** - 传送组件
- **FuniHyperLink** - 超链接组件
- **FuniShareAction** - 分享操作

#### 布局组件
- **FuniWrap** - 包装容器
- **FuniGroupTitle** - 分组标题
- **FuniLabel** - 标签组件
- **FuniSiteHeader** - 站点头部
- **FuniSiteFooter** - 站点底部

#### 高级组件
- **FuniFormEngine** - 表单设计器
- **FuniFormRender** - 表单渲染器
- **FuniEventEditor** - 事件编辑器
- **FuniPageRender** - 页面渲染器
- **FuniVariableSetter** - 变量设置器

#### 日志和记录组件
- **FuniLog** - 日志组件
- **FuniOperationLog** - 操作日志
- **FuniWorkRecord** - 工作记录

## 文档结构规范

每个组件应包含以下文档文件：

```
docs/funi-ui/components/[ComponentName]/
├── api.md                      # API文档（必需）
├── config-schema.md            # 配置结构定义（推荐）
├── examples.md                 # 使用示例（推荐）
├── elementplus-api.md          # ElementPlus API集成说明（如适用）
└── best-practices.md           # 最佳实践（可选）
```

### API文档模板结构
1. **组件概述** - 组件功能和用途说明
2. **Props** - 属性列表和说明
3. **Events** - 事件列表和说明
4. **Methods** - 方法列表和说明
5. **Slots** - 插槽列表和说明（如适用）
6. **使用示例** - 基础使用示例
7. **ElementPlus API支持** - 透传API说明（如适用）
8. **注意事项** - 使用注意事项
9. **常见问题** - FAQ

### 配置结构文档模板
1. **基础配置结构** - TypeScript接口定义
2. **详细配置说明** - 每个配置项的详细说明
3. **常用配置组合** - 典型使用场景的配置示例
4. **最佳实践建议** - 配置使用建议

## 优先级排序

### 高优先级（强制使用组件）
1. **FuniListPageV2** - ✅ 已完成
2. **FuniDetail** - ✅ 已完成

### 中优先级（常用组件）
1. **FuniForm** - ✅ 已完成
2. **FuniCurd** - ✅ 已完成
3. **FuniSelect** - ✅ 已完成
4. **FuniCurdV2** - 📝 待补充
5. **FuniTreeSelect** - 📝 待补充
6. **FuniRUOC** - 📝 待补充
7. **FuniSearch** - 📝 待补充

### 低优先级（特殊场景组件）
1. **工作流组件** - 部分完成
2. **图表组件** - 部分完成
3. **高级组件** - 📝 待补充
4. **布局组件** - 📝 待补充

## 下一步工作计划

### 第一阶段：补充核心组件文档
- [ ] FuniCurdV2 完整文档
- [ ] FuniTreeSelect 完整文档
- [ ] FuniRUOC 完整文档
- [ ] FuniSearch 完整文档
- [ ] FuniSearchForm 完整文档

### 第二阶段：补充工作流组件文档
- [ ] FuniBusAuditDrawer 完整文档
- [ ] FuniProcessBottomBtn 完整文档
- [ ] FuniBpmn 完整文档

### 第三阶段：补充表单控件文档
- [ ] FuniMoneyInput 完整文档
- [ ] FuniInputNumber 完整文档
- [ ] FuniAutocomplete 完整文档
- [ ] FuniRegion 完整文档

### 第四阶段：补充展示组件文档
- [ ] FuniImage 完整文档
- [ ] FuniImageView 完整文档
- [ ] FuniIcon 完整文档
- [ ] FuniDialog 完整文档

### 第五阶段：补充高级组件文档
- [ ] FuniFormEngine 完整文档
- [ ] FuniFormRender 完整文档
- [ ] FuniOlMap 完整文档

## 文档质量标准

### 必需内容
- ✅ 组件概述和用途说明
- ✅ 完整的Props、Events、Methods列表
- ✅ 基础使用示例
- ✅ ElementPlus API透传说明（如适用）

### 推荐内容
- 📝 详细的配置结构定义
- 📝 多种使用场景示例
- 📝 最佳实践建议
- 📝 常见问题解答

### 可选内容
- 📝 高级用法示例
- 📝 性能优化建议
- 📝 自定义扩展方法

## 维护说明

1. **文档更新** - 组件API变更时同步更新文档
2. **示例验证** - 定期验证示例代码的有效性
3. **用户反馈** - 收集用户使用反馈，完善文档内容
4. **版本管理** - 记录文档版本变更历史
