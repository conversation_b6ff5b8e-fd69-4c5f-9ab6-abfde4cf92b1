# FuniListPageV2 ElementPlus API支持

## 基础组件

FuniListPageV2 基于 Element Plus 的以下组件进行封装：

- **el-tabs** - 页签容器（核心组件）
- **FuniCurdV2** - 表格组件（内置使用）

> **关于表格 API**：FuniListPageV2 内置使用 FuniCurdV2 组件处理表格相关功能，表格的 ElementPlus API 透传由 FuniCurdV2 处理。详细的表格 API 支持请参考 [FuniCurdV2 组件文档](../FuniCurdV2/)。

## FuniListPageV2 支持的 ElementPlus API

### el-tabs API 透传

FuniListPageV2 基于 el-tabs 组件，支持以下 ElementPlus API：

| API名称      | 类型     | 默认值 | 说明                     | 透传方式         |
| ------------ | -------- | ------ | ------------------------ | ---------------- |
| model-value  | String   | -      | 选中选项卡的 name        | 通过 active 属性 |
| type         | String   | -      | 风格类型                 | v-bind 透传      |
| closable     | Boolean  | false  | 标签是否可关闭           | v-bind 透传      |
| addable      | Boolean  | false  | 标签是否可增加           | v-bind 透传      |
| editable     | Boolean  | false  | 标签是否同时可增加和关闭 | v-bind 透传      |
| tab-position | String   | top    | 选项卡所在位置           | v-bind 透传      |
| stretch      | Boolean  | false  | 标签的宽度是否自撑开     | v-bind 透传      |
| before-leave | Function | -      | 切换标签之前的钩子       | v-bind 透传      |

### el-tabs 事件透传

| 事件名     | 说明                                    | 回调参数                                                          |
| ---------- | --------------------------------------- | ----------------------------------------------------------------- |
| tab-click  | tab 被选中时触发                        | (pane: TabsPaneContext, ev: Event)                                |
| tab-change | activeName 改变时触发                   | (name: TabPaneName)                                               |
| tab-remove | 点击 tab 移除按钮时触发                 | (name: TabPaneName)                                               |
| tab-add    | 点击 tabs 的新增按钮时触发              | -                                                                 |
| edit       | 点击 tabs 的新增按钮或 tab 被关闭时触发 | (targetName: TabPaneName \| undefined, action: 'add' \| 'remove') |

## 使用示例

### 基础页签配置

```vue
<template>
  <FuniListPageV2 :cardTab="cardTab" type="border-card" tab-position="top" stretch />
</template>

<script setup>
const cardTab = [
  {
    label: '用户管理',
    api: '/api/users',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [...],
      // ... 其他配置
    }
  }
];
</script>
```

### 自定义页签样式

```vue
<template>
  <FuniListPageV2 :cardTab="cardTab" type="card" tab-position="left" :before-leave="handleBeforeLeave" @tab-click="handleTabClick" @tab-change="handleTabChange" />
</template>

<script setup>
const handleBeforeLeave = (activeName, oldActiveName) => {
  console.log('切换前:', oldActiveName, '->', activeName);
  return true; // 返回 false 可以阻止切换
};

const handleTabClick = (pane, event) => {
  console.log('页签点击:', pane.props.name);
};

const handleTabChange = name => {
  console.log('页签切换:', name);
};
</script>
```

## 注意事项

### 1. API 透传限制

- **页签管理优先**：FuniListPageV2 主要负责页签管理，表格相关的 ElementPlus API 由内置的 FuniCurdV2 处理
- **配置传递**：el-tabs 的配置通过组件 props 传递，表格配置通过 `curdOption` 传递给 FuniCurdV2

### 2. 事件处理

- **页签事件**：el-tabs 的事件直接透传，可以监听页签的点击、切换等行为
- **表格事件**：表格相关事件由 FuniCurdV2 处理，详见 [FuniCurdV2 组件文档](../FuniCurdV2/)

### 3. 样式定制

- **页签样式**：可以通过 el-tabs 的 API 定制页签外观
- **表格样式**：表格样式通过 FuniCurdV2 的配置进行定制

## 相关文档

- [Element Plus Tabs 组件文档](https://element-plus.org/zh-CN/component/tabs.html)
- [FuniCurdV2 组件文档](../FuniCurdV2/)
- [FuniListPageV2 API 文档](./api.md)
