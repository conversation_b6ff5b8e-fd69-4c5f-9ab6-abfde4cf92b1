# FuniListPageV2 API 文档

## 组件概述

FuniListPageV2 是基于 Element Plus 的 `el-tabs` 组件构建的多页签列表页面组件，是 CLI 框架中的核心列表页组件。该组件专注于**页签管理和配置传递**，通过配置化方式支持多个页签的创建和管理，每个页签内容默认使用 FuniCurdV2 组件进行数据展示和操作。

> **关于表格功能**：FuniListPageV2 内置使用 FuniCurdV2 组件处理表格相关功能，详细的表格配置、列定义、操作按钮等请参考 [FuniCurdV2 组件文档](../FuniCurdV2/)。

## Props

| 参数名         | 类型    | 默认值 | 必填 | 说明                                  | ElementPlus 对应   |
| -------------- | ------- | ------ | ---- | ------------------------------------- | ------------------ |
| cardTab        | Array   | []     | ✅   | 页签配置数组（必填）                  | -                  |
| isShowSearch   | Boolean | true   | -    | 是否显示搜索功能（传递给 FuniCurdV2） | -                  |
| active         | String  | -      | -    | 默认激活的页签 key                    | el-tabs.modelValue |
| showTab        | Boolean | -      | -    | 是否显示页签头部                      | -                  |
| teleported     | Boolean | true   | -    | 是否传送到布局容器                    | -                  |
| reloadOnActive | Boolean | false  | -    | 页签切换时是否重载数据                | -                  |

## Events

| 事件名        | 参数                            | 说明             | 触发时机                        |
| ------------- | ------------------------------- | ---------------- | ------------------------------- |
| headBtnClick  | btnKey: string                  | 头部按钮点击事件 | 点击 cardTab 中配置的头部按钮时 |
| beforeRequest | -                               | 请求前事件       | 发起 API 请求前                 |
| afterRequest  | response: any                   | 请求后事件       | API 请求成功后                  |
| requestError  | error: any                      | 请求错误事件     | API 请求失败时                  |
| badgeChange   | (tabKey: string, count: number) | 徽章数量变化事件 | 标签页徽章数量更新时            |

## Slots

| 插槽名                 | 参数                         | 说明                 | 使用场景                         |
| ---------------------- | ---------------------------- | -------------------- | -------------------------------- |
| [column.slots.default] | { row, column, $index }      | 自定义列内容插槽     | 表格列自定义渲染                 |
| empty                  | -                            | 空数据插槽           | 表格无数据时显示                 |
| append                 | -                            | 表格底部插槽         | 表格底部追加内容                 |
| pagination_extra       | { total, pageNum, pageSize } | 分页额外内容插槽     | 分页组件旁边显示额外信息         |
| [item.slot]            | -                            | 自定义标签页内容插槽 | 不使用 curdOption 时的自定义内容 |

## Methods (通过 ref 访问)

| 方法名           | 参数                            | 说明                                                           |
| ---------------- | ------------------------------- | -------------------------------------------------------------- |
| reload           | ({ resetPage?: boolean })       | 重载当前页签数据（调用当前活动 FuniCurdV2 实例的 reload 方法） |
| updateBadgeCount | (tabKey: string, count: number) | 更新指定页签的徽章计数                                         |

## 内部属性 (通过 ref 访问)

| 属性名     | 类型          | 说明                               |
| ---------- | ------------- | ---------------------------------- |
| activeTab  | Ref\<string\> | 当前活动页签的 key                 |
| activeCurd | ComputedRef   | 当前活动页签对应的 FuniCurdV2 实例 |

> **FuniCurdV2 实例访问**：通过 `activeCurd` 可以访问当前页签的 FuniCurdV2 实例，进而调用 FuniCurdV2 的所有方法和属性。具体可用的方法和属性请参考 [FuniCurdV2 组件文档](../FuniCurdV2/)。

## cardTab 配置结构

### 基础配置

```typescript
interface CardTabItem {
  // 基础信息
  key?: string; // 标签页唯一标识，不传则自动生成
  label: string; // 标签页显示名称
  badge?: boolean; // 是否显示徽章

  // API配置
  api?: string; // API接口地址
  requestParams?: object; // 固定请求参数
  fixSearchParams?: (searchParams: any) => any; // 搜索参数转换函数

  // 搜索配置
  searchConfig?: SearchConfig; // 传递给 FuniCurdV2，一般只需配置 pageCode

  curdOption: CurdOptionConfig; // 传递给 FuniCurdV2 的配置，详见 FuniCurdV2 文档

  // 自定义内容（与curdOption互斥）
  slot?: string; // 自定义插槽名称
}
```

### 配置传递机制

> **配置传递机制**：FuniListPageV2 会将页签级别的 `api`、`requestParams`、`searchConfig` 等配置与 `curdOption` 合并后传递给 FuniCurdV2。如果同时配置了页签级别的 `api` 和 `curdOption.api`，页签级别的配置优先级更高。

详细的 `curdOption` 配置项请参考 [FuniCurdV2 组件文档](../FuniCurdV2/)。

### 操作按钮配置

```typescript
interface ActionConfig {
  // 按钮基础信息
  name: string; // 按钮显示文本
  key: string; // 按钮唯一标识

  // ElementPlus按钮属性
  props?: {
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
    size?: 'large' | 'default' | 'small';
    icon?: string;
    disabled?: boolean;
    loading?: boolean;
    // ... 更多el-button属性
  };

  // 权限控制
  auth?: string | string[]; // 权限标识
  show?: (row?: any) => boolean; // 显示条件
}
```

## ElementPlus API 支持

FuniListPageV2 基于以下 ElementPlus 组件构建：

### el-tabs API 支持

| API 名称     | 类型    | 说明           | 透传方式         |
| ------------ | ------- | -------------- | ---------------- |
| model-value  | string  | 当前激活标签页 | 通过 active 属性 |
| tab-position | string  | 标签页位置     | 暂不支持         |
| type         | string  | 标签页类型     | 暂不支持         |
| closable     | boolean | 是否可关闭     | 暂不支持         |

### FuniCurdV2 API 支持

FuniListPageV2 内部使用 FuniCurdV2，支持其所有 API 配置：

```vue
<template>
  <FuniListPageV2
    :cardTab="[{
    label: '用户列表',
    curdOption: {
      // 支持FuniCurdV2的所有配置
      columns: [...],
      btns: [...],
      // ... 更多配置
    }
  }]"
  />
</template>
```

## 使用注意事项

### 1. 强制使用规则

- 所有 PC 端列表页面必须使用 FuniListPageV2
- 不得直接使用 FuniCurd 或其他表格组件作为列表页

### 2. 标签页配置

- cardTab 数组至少包含一个元素
- 每个标签页必须有 label 属性
- key 属性建议手动指定，避免自动生成的 key 不稳定

### 3. API 配置

- api 和 curdOption.api 二选一配置
- 建议在 cardTab 项目级别配置 api，便于管理

### 4. 搜索配置

- searchConfig.schema 配置参考 FuniSearch 组件文档
- 默认提供 keyword 关键字搜索

### 5. 权限控制

- 操作按钮支持 auth 权限配置
- 列显示支持 show 动态控制

## ⚠️ 重要配置说明

### 数据加载配置

```javascript
curdOption: {
  api: '/api/users', // API接口地址
}
```

### 按钮配置

```javascript
// 功能按钮（页面顶部）
btns: [
  {
    type: 'primary',
    icon: 'Plus',
    label: '新建',
    key: 'create'
  }
];
```

### 操作列配置

```javascript
{
  label: '操作',
  prop: 'oper',
  width: 200,
  fixed: 'right',
  render: ({ row }) => {
    return (
      <div style="display:flex;justify-content:flex-start;align-items:center;gap:8px;">
        <el-button type="info" size="small" link onClick={() => handleView(row.id)}>
          查看
        </el-button>
        <el-button type="primary" size="small" link onClick={() => handleEdit(row.id)}>
          编辑
        </el-button>
        <el-popconfirm
          title="您确定删除吗？"
          width={220}
          onConfirm={() => handleDelete(row.id)}
          v-slots={{
            reference: () => (
              <el-button type="danger" size="small" link>
                删除
              </el-button>
            )
          }}
        />
      </div>
    )
  }
}
```

## 常见问题

### Q: 如何隐藏标签页头部？

A: 设置 showTab 为 false，或者只配置一个标签页（自动隐藏）

### Q: 如何自定义列内容？

A: 在 columns 配置中设置 slots.default，然后使用对应的插槽

### Q: 如何处理 API 错误？

A: CLI 框架的 bpaas.js 已处理所有 HTTP 错误，业务代码只需处理成功数据

### Q: 如何实现数据联动？

A: 使用 beforeRequest 和 afterRequest 事件，或者通过 fixSearchParams 转换参数

### Q: 如何实现徽章功能？

A: 在 cardTab 中设置 badge: true，组件会自动显示数据总数作为徽章
