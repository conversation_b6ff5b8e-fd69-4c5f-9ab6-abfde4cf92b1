# FuniListPageV2 最佳实践

> **关于表格配置**：FuniListPageV2 内置使用 FuniCurdV2 组件处理表格相关功能，详细的表格配置、列定义、操作按钮等请参考 [FuniCurdV2 组件文档](../FuniCurdV2/)。本文档重点介绍页签管理相关的最佳实践。

## 页签配置最佳实践

### 1. 单页签配置

```vue
<template>
  <funi-list-page-v2 :cardTab="singleTabConfig" />
</template>

<script setup>
import { reactive } from 'vue';

// 推荐：使用响应式数据管理页签配置
const singleTabConfig = reactive([
  {
    label: '用户管理',
    api: '/api/users',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [...],  // 列配置
      btns: [...],     // 操作按钮配置
      // ... 其他配置
    }
  }
]);
</script>
```

### 2. 多页签配置

```vue
<template>
  <funi-list-page-v2 :cardTab="multiTabConfig" />
</template>

<script setup lang="jsx">
import { ref, reactive } from 'vue';

const activeTab = ref('all');

// 推荐：合理的多页签配置
const multiTabConfig = reactive([
  {
    key: 'all',
    label: '全部用户',
    api: '/api/users/all',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [...],  // 列配置
      // ... 其他配置
    }
  },
  {
    key: 'active',
    label: '启用用户',
    api: '/api/users/active',
    requestParams: { status: 1 },   // 推荐：页签级别的固定参数
    curdOption: {
      columns: [...],  // 不同页签可以有不同的列配置
      // ... 其他配置
    }
  },
  {
    key: 'inactive',
    label: '禁用用户',
    api: '/api/users/inactive',
    requestParams: { status: 0 },
    curdOption: {
      columns: [...],
      // ... 其他配置
    }
  }
]);
</script>
```

### 3. 混合内容页签

```vue
<template>
  <funi-list-page-v2 :cardTab="mixedTabConfig">
    <!-- 自定义统计页签内容 -->
    <template #statistics>
      <div class="statistics-dashboard">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card>
              <el-statistic title="总用户数" :value="stats.totalUsers" />
            </el-card>
          </el-col>
        </el-row>
      </div>
    </template>
  </FuniListPageV2>
</template>

<script setup lang="jsx">
import { reactive } from 'vue';

const mixedTabConfig = reactive([
  {
    key: 'list',
    label: '用户列表',
    api: '/api/users',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [...],  // 列配置，支持插槽
      // ... 其他配置
    }
  },
  {
    key: 'statistics',
    label: '数据统计',
    slot: 'statistics'  // 推荐：使用自定义插槽替代默认表格
  }
]);

const stats = reactive({
  totalUsers: 1234
});

const getStatusType = (status) => {
  const typeMap = { 1: 'success', 0: 'danger', 2: 'warning' };
  return typeMap[status] || 'info';
};

const getStatusText = (status) => {
  const textMap = { 1: '启用', 0: '禁用', 2: '待审核' };
  return textMap[status] || '未知';
};
</script>
```

## 配置优化建议

### 1. 页签 key 管理

```javascript
// ✅ 推荐：手动指定页签 key，确保稳定性
const cardTab = reactive([
  {
    key: 'all-users', // 明确指定 key
    label: '全部用户',
    api: '/api/users'
  },
  {
    key: 'active-users', // 使用语义化的 key
    label: '启用用户',
    api: '/api/users/active'
  }
]);

// ❌ 避免：依赖自动生成的 key
const cardTab = reactive([
  {
    // 没有指定 key，组件会自动生成，可能不稳定
    label: '全部用户',
    api: '/api/users'
  }
]);
```

### 2. API 配置优先级

```javascript
// ✅ 推荐：在页签级别配置 API，优先级更高
const cardTab = reactive([
  {
    label: '用户列表',
    api: '/api/users',              // 页签级别 API
    requestParams: { type: 'all' }, // 页签级别参数
    curdOption: {
      // api: '/api/other',        // 会被页签级别的 API 覆盖
      columns: [...],
    }
  }
]);
```

### 3. 搜索配置最佳实践

```javascript
// ✅ 推荐：一般情况下无需配置 searchConfig
const cardTab = reactive([
  {
    label: '用户列表',
    api: '/api/users'
    // searchConfig 一般无需配置，系统会自动处理
  }
]);

// ✅ 推荐：只在需要自定义时才配置
const cardTab = reactive([
  {
    label: '用户列表',
    api: '/api/users',
    searchConfig: {
      pageCode: 'custom-user-page' // 只在需要自定义页面代码时配置
    }
  }
]);
```

## 常见问题和解决方案

### 1. 页签配置冲突

```javascript
// ❌ 错误：同时配置 curdOption 和 slot
const cardTab = reactive([
  {
    label: '用户列表',
    curdOption: { columns: [...] },
    slot: 'custom-content'  // 错误：两者互斥
  }
]);

// ✅ 正确：根据需要选择其一
const cardTab = reactive([
  {
    label: '用户列表',
    curdOption: { columns: [...] }  // 使用表格
  },
  {
    label: '统计图表',
    slot: 'statistics'  // 使用自定义内容
  }
]);
```

### 2. 数据重载控制

```javascript
// ✅ 推荐：合理配置数据重载
const cardTab = reactive([
  {
    label: '实时数据',
    api: '/api/realtime',
    curdOption: {
      reloadOnActive: true // 切换到此页签时总是重载
    }
  },
  {
    label: '历史数据',
    api: '/api/history',
    curdOption: {
      reloadOnActive: false // 切换到此页签时不重载
    }
  }
]);

// 全局配置
// <funi-list-page-v2 :cardTab="cardTab" :reloadOnActive="false" />
```

### 3. 徽章计数优化

```javascript
// ✅ 推荐：合理使用徽章计数
const cardTab = reactive([
  {
    label: '待处理',
    badge: true, // 重要页签启用徽章
    api: '/api/pending'
  },
  {
    label: '历史记录',
    badge: false, // 历史数据不需要徽章
    api: '/api/history'
  }
]);

// 监听徽章变化
const handleBadgeChange = (tabKey, count) => {
  // 可以用于更新页面标题、通知等
  if (tabKey === 'pending' && count > 0) {
    document.title = `(${count}) 待处理任务`;
  }
};
```

## 性能优化建议

### 1. 合理设置页签数量

```javascript
// ✅ 推荐：页签数量控制在 2-6 个
const cardTab = reactive([
  { label: '全部', api: '/api/all' },
  { label: '待处理', api: '/api/pending' },
  { label: '已完成', api: '/api/completed' },
  { label: '已取消', api: '/api/cancelled' }
]);

// ❌ 避免：过多页签影响用户体验
// 超过 6 个页签建议使用下拉菜单或其他方式
```

### 2. 数据加载优化

```javascript
// ✅ 推荐：合理使用 reloadOnActive
const cardTab = reactive([
  {
    label: '实时监控',
    api: '/api/monitor',
    curdOption: {
      reloadOnActive: true // 需要实时数据的页签
    }
  },
  {
    label: '历史报表',
    api: '/api/reports',
    curdOption: {
      reloadOnActive: false // 静态数据无需频繁重载
    }
  }
]);
```

### 3. 徽章计数性能

```javascript
// ✅ 推荐：只在必要的页签启用徽章
const cardTab = reactive([
  {
    label: '待审核',
    badge: true, // 需要关注数量的页签
    api: '/api/pending'
  },
  {
    label: '全部记录',
    badge: false, // 总量很大的页签不建议显示徽章
    api: '/api/all'
  }
]);
```

## 总结

### 关键原则

1. **页签管理优先**：重点关注页签的配置和交互，表格配置交给 FuniCurdV2
2. **配置传递机制**：理解页签级别配置如何传递给 FuniCurdV2 实例
3. **状态管理**：合理利用组件的状态管理功能，如徽章计数、加载状态等
4. **性能考虑**：合理设置页签数量和数据重载策略
5. **用户体验**：提供清晰的页签标识和合理的交互反馈

### 开发建议

1. **明确职责边界**：FuniListPageV2 负责页签管理，FuniCurdV2 负责表格功能
2. **优先使用默认配置**：搜索配置等尽量使用系统默认，减少手动配置
3. **合理使用插槽**：对于特殊需求使用插槽自定义，而不是修改组件源码
4. **关注配置优先级**：页签级别配置优先于 curdOption 中的配置
5. **统一错误处理**：通过组件事件统一处理各种状态和错误情况
