# FuniListPageV2 配置结构

> **关于表格配置**：FuniListPageV2 内置使用 FuniCurdV2 组件处理表格相关功能，详细的 `CurdOptionConfig` 配置结构请参考 [FuniCurdV2 组件文档](../FuniCurdV2/)。

## 基础配置结构

```typescript
interface FuniListPageV2Config {
  // 组件属性
  cardTab: CardTabItem[]; // 页签配置数组（必填）
  isShowSearch?: boolean; // 是否显示搜索功能（传递给 FuniCurdV2）
  active?: string; // 默认激活的页签 key
  showTab?: boolean; // 是否显示页签头部
  teleported?: boolean; // 是否传送到布局容器
  reloadOnActive?: boolean; // 页签切换时是否重载数据
}

interface CardTabItem {
  // 页签基础信息
  key?: string; // 页签唯一标识，未设置时自动生成
  label: string; // 页签显示名称（必填）
  badge?: boolean; // 是否显示徽章计数，默认 false

  // API 配置
  api?: string; // API接口地址
  requestParams?: Record<string, any>; // 固定请求参数
  fixSearchParams?: (searchParams: any) => any; // 搜索参数预处理函数

  // 搜索配置
  searchConfig?: SearchConfig; // 传递给 FuniCurdV2，一般只需配置 pageCode

  // CRUD 配置（与 slot 互斥）
  curdOption?: CurdOptionConfig; // 传递给 FuniCurdV2 的配置，详见 FuniCurdV2 文档

  // 自定义内容配置（与 curdOption 互斥）
  slot?: string; // 自定义插槽名称
}
```

## 搜索配置结构

> **搜索配置说明**：搜索配置一般通过内部接口数据自动生成，内部有自动生成 pageCode 的算法，通常无需手动配置。

```typescript
interface SearchConfig {
  pageCode?: string; // 页面代码，用于获取远程搜索配置（可选）
  schema?: SearchSchemaItem[]; // 搜索表单配置（一般无需手动配置）
}

interface SearchSchemaItem {
  prop: string; // 字段名
  label: string; // 显示标签
  component: string; // 组件类型
  props?: Record<string, any>; // 组件属性
  options?: Array<{
    // 选项数据（select等组件）
    label: string;
    value: any;
  }>;
}
```

## 配置传递机制

> **配置传递机制**：FuniListPageV2 会将页签级别的 `api`、`requestParams`、`searchConfig` 等配置与 `curdOption` 合并后传递给 FuniCurdV2。如果同时配置了页签级别的 `api` 和 `curdOption.api`，页签级别的配置优先级更高。

```typescript
// 配置优先级示例
interface CardTabItem {
  api: '/api/users';              // 页签级别 API（优先级高）
  requestParams: { status: 1 };   // 页签级别参数（优先级高）
  curdOption: {
    api: '/api/other';            // 会被页签级别的 API 覆盖
    requestParams: { type: 'all' }; // 会与页签级别参数合并
    columns: [...];               // 表格列配置
    // ... 其他 FuniCurdV2 配置
  };
}
```

## CurdOptionConfig 配置结构

> **详细配置说明**：`CurdOptionConfig` 的完整配置结构和使用方法请参考 [FuniCurdV2 组件文档](../FuniCurdV2/)。以下仅列出主要配置项：

```typescript
interface CurdOptionConfig {
  // 表格配置
  columns: ColumnConfig[]; // 列配置（详见 FuniCurdV2 文档）
  btns?: ButtonConfig[]; // 操作按钮配置

  // 数据配置
  api?: string; // API接口地址（页签级别配置优先）
  requestParams?: Record<string, any>; // 请求参数
  reloadOnActive?: boolean; // 页签切换时是否重载数据

  // 其他配置
  selection?: boolean; // 是否显示选择框
  pagination?: PaginationConfig; // 分页配置
  // ... 更多配置详见 FuniCurdV2 文档
}
```

## 使用示例

### 基础页签配置

```javascript
const cardTabConfig = [
  {
    key: 'all-users',
    label: '全部用户',
    api: '/api/users',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [...],
      btns: [...],
      // ... 其他配置
    }
  }
];
```

### 多页签配置

```javascript
const multiTabConfig = [
  {
    key: 'active',
    label: '启用用户',
    api: '/api/users/active',
    requestParams: { status: 1 },  // 页签级别的固定参数
    curdOption: {
      columns: [...],
      // ... 其他配置
    }
  },
  {
    key: 'inactive',
    label: '禁用用户',
    api: '/api/users/inactive',
    requestParams: { status: 0 },
    curdOption: {
      columns: [...],
      // ... 其他配置
    }
  }
];
```

### 自定义插槽配置

```javascript
const mixedTabConfig = [
  {
    key: 'list',
    label: '用户列表',
    api: '/api/users',
    curdOption: {
      columns: [...],
      // ... 其他配置
    }
  },
  {
    key: 'statistics',
    label: '数据统计',
    slot: 'statistics'  // 使用自定义插槽
  }
];
```

## 配置注意事项

### 1. 页签配置规范

- **key 属性**：建议手动指定页签的 `key` 属性，避免自动生成的 key 不稳定
- **互斥配置**：`curdOption` 和 `slot` 属性互斥，不能同时配置
- **API 配置优先级**：页签级别的 `api` 优先级高于 `curdOption.api`

### 2. 搜索配置建议

- **一般无需配置**：搜索配置通常由系统自动生成，无需手动配置
- **pageCode 可选**：内部有自动生成 pageCode 的算法，通常无需指定

### 3. 表格配置引用

- **详细配置**：`curdOption` 中的具体配置项请参考 [FuniCurdV2 组件文档](../FuniCurdV2/)
- **配置传递**：页签级别的配置会与 `curdOption` 合并后传递给 FuniCurdV2
