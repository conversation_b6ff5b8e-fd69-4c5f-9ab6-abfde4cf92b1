# FuniListPageV2 使用示例

> **关于表格配置**：以下示例中的 `curdOption` 配置会直接传递给 FuniCurdV2 组件，详细的表格配置、列定义、操作按钮等请参考 [FuniCurdV2 组件文档](../FuniCurdV2/)。

## 基础使用

### 单页签列表

```vue
<template>
  <funi-list-page-v2 :cardTab="cardTabConfig" />
</template>

<script setup lang="jsx">
import { reactive } from 'vue';

const cardTabConfig = reactive([
  {
    label: '用户列表',
    api: '/api/users',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [...],  // 列配置
      btns: [...],     // 操作按钮配置
      // ... 其他配置
    }
  }
]);

// 事件处理函数
const handleEdit = row => {
  console.log('编辑用户:', row);
};

const handleDelete = row => {
  console.log('删除用户:', row);
};
</script>
```

### 多页签列表

```vue
<template>
  <funi-list-page-v2 :cardTab="cardTabConfig" />
</template>

<script setup lang="jsx">
import { reactive } from 'vue';

const cardTabConfig = reactive([
  {
    key: 'all',
    label: '全部用户',
    api: '/api/users/all',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [...],  // 列配置
      // ... 其他配置
    }
  },
  {
    key: 'active',
    label: '启用用户',
    api: '/api/users/active',
    requestParams: { status: 1 },   // 页签级别的固定参数
    curdOption: {
      columns: [...],  // 不同页签可以有不同的列配置
      // ... 其他配置
    }
  },
  {
    key: 'inactive',
    label: '禁用用户',
    api: '/api/users/inactive',
    requestParams: { status: 0 },
    curdOption: {
      columns: [...],
      // ... 其他配置
    }
  }
]);

const handleBadgeChange = (tabKey, count) => {
  console.log(`页签 ${tabKey} 数量更新为: ${count}`);
};
</script>
```

## 高级功能

### 自定义渲染内容

```vue
<template>
  <funi-list-page-v2 :cardTab="cardTabConfig" />
</template>

<script setup lang="jsx">
import { reactive } from 'vue';

const cardTabConfig = reactive([
  {
    label: '用户列表',
    api: '/api/users',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [
        { label: '头像', prop: 'avatar', width: 80, render: ({row}) => <el-avatar :src="row.avatar" :alt="row.name" size="small" /> },
        { label: '用户名', prop: 'username' }, // 默认不设置width，自动撑开
        { label: '邮箱', prop: 'email' }, // 默认不设置width，自动撑开
        { label: '状态', prop: 'status', width: 100, render: ({row}) => {
          return <el-tag :type="getStatusType(row.status)" size="small">
        {{ getStatusText(row.status) }}
      </el-tag>
        } }
      ]
    }
  }
]);

const getStatusType = status => {
  const typeMap = { 1: 'success', 0: 'danger', 2: 'warning' };
  return typeMap[status] || 'info';
};

const getStatusText = status => {
  const textMap = { 1: '启用', 0: '禁用', 2: '待审核' };
  return textMap[status] || '未知';
};
</script>
```

### 混合内容页签

```vue
<template>
  <FuniListPageV2 :cardTab="mixedTabConfig">
    <!-- 自定义统计页签内容 -->
    <template #statistics>
      <div class="statistics-dashboard">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card>
              <el-statistic title="总用户数" :value="stats.totalUsers" />
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <el-statistic title="活跃用户" :value="stats.activeUsers" />
            </el-card>
          </el-col>
        </el-row>
      </div>
    </template>
  </FuniListPageV2>
</template>

<script setup lang="jsx">
import { reactive } from 'vue';

const mixedTabConfig = [
  {
    key: 'list',
    label: '用户列表',
    api: '/api/users',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [...],  // 列配置，支持插槽
      // ... 其他配置
    }
  },
  {
    key: 'statistics',
    label: '数据统计',
    slot: 'statistics'  // 使用自定义插槽替代默认表格
  }
];

const stats = reactive({
  totalUsers: 1234,
  activeUsers: 856
});

const getStatusType = (status) => {
  const typeMap = { 1: 'success', 0: 'danger', 2: 'warning' };
  return typeMap[status] || 'info';
};

const getStatusText = (status) => {
  const textMap = { 1: '启用', 0: '禁用', 2: '待审核' };
  return textMap[status] || '未知';
};
</script>
```

## 实际业务场景

### 订单管理多页签

```vue
<template>
  <funi-list-page-v2 :cardTab="orderTabs" @headBtnClick="handleHeadBtnClick" />
</template>

<script setup>
import { ref, reactive } from 'vue';

const orderTabs = reactive([
  {
    key: 'all',
    label: '全部订单',
    api: '/api/orders/all',
    curdOption: {
      // 表格配置详见 FuniCurdV2 文档
      columns: [...],  // 列配置，支持插槽
      btns: [...],     // 操作按钮配置
      // ... 其他配置
    }
  },
  {
    key: 'pending',
    label: '待处理',
    api: '/api/orders/pending',
    requestParams: { status: 'pending' },  // 页签级别的固定参数
    curdOption: {
      columns: [...],  // 不同页签可以有不同的列配置
      // ... 其他配置
    }
  },
  {
    key: 'completed',
    label: '已完成',
    api: '/api/orders/completed',
    requestParams: { status: 'completed' },
    curdOption: {
      columns: [...],
      // ... 其他配置
    }
  }
]);

// 头部按钮点击处理
const handleHeadBtnClick = (btnKey) => {
  console.log('按钮点击:', btnKey);
};

// 状态相关工具函数
const getOrderStatusType = (status) => {
  const typeMap = {
    pending: 'warning',
    completed: 'success',
    cancelled: 'danger'
  };
  return typeMap[status] || 'info';
};

const getOrderStatusText = (status) => {
  const textMap = {
    pending: '待处理',
    completed: '已完成',
    cancelled: '已取消'
  };
  return textMap[status] || '未知';
};
</script>

<style scoped>
.amount {
  font-weight: bold;
  color: #f56c6c;
}
</style>
```
