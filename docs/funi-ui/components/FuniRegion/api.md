# FuniRegion API文档

## 组件概述

FuniRegion是地区选择器组件，支持省市区三级联动选择，集成了高德地图和腾讯地图API，提供地图选点、地址解析、坐标转换等功能，适用于地址录入、位置选择等场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| modelValue | String/Array/Object | '' | - | 绑定值 |
| level | Number | 3 | - | 选择层级：1-省，2-市，3-区 |
| separator | String | '/' | - | 分隔符（字符串模式） |
| placeholder | String | '请选择地区' | - | 占位符文本 |
| clearable | Boolean | true | - | 是否可清空 |
| disabled | Boolean | false | - | 是否禁用 |
| size | String | 'default' | - | 组件尺寸 |
| filterable | Boolean | true | - | 是否可搜索 |
| multiple | Boolean | false | - | 是否多选 |
| collapseTags | Boolean | false | - | 多选时是否折叠标签 |
| maxCollapseTags | Number | 1 | - | 最大显示标签数 |
| valueFormat | String | 'code' | - | 值格式：'code'、'name'、'object' |
| showAllLevels | Boolean | true | - | 是否显示完整路径 |
| props | Object | {} | - | 配置选项 |
| lazy | Boolean | false | - | 是否懒加载 |
| lazyLoad | Function | - | - | 懒加载函数 |
| checkStrictly | Boolean | false | - | 是否严格模式（可选择任意级别） |
| emitPath | Boolean | true | - | 是否返回完整路径 |
| expandTrigger | String | 'click' | - | 展开触发方式 |
| showMap | Boolean | false | - | 是否显示地图选择 |
| mapType | String | 'amap' | - | 地图类型：'amap'、'tmap' |
| mapConfig | Object | {} | - | 地图配置 |
| enableGeolocation | Boolean | false | - | 是否启用定位 |
| enableSearch | Boolean | false | - | 是否启用地址搜索 |
| enableReverse | Boolean | false | - | 是否启用逆地理编码 |
| coordinate | String | 'gcj02' | - | 坐标系：'gcj02'、'wgs84'、'bd09' |
| precision | Number | 6 | - | 坐标精度 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: String/Array/Object | 值更新事件 | 选择地区时 |
| change | value: String/Array/Object, selectedData: Object | 值变化事件 | 选择地区时 |
| visible-change | visible: Boolean | 下拉框显示状态变化 | 下拉框显示/隐藏时 |
| remove-tag | value: String/Array | 移除标签事件 | 多选模式移除标签时 |
| clear | - | 清空事件 | 点击清空按钮时 |
| blur | event: Event | 失去焦点事件 | 组件失去焦点时 |
| focus | event: Event | 获得焦点事件 | 组件获得焦点时 |
| map-click | coordinate: Object, address: String | 地图点击事件 | 点击地图时 |
| location-success | position: Object | 定位成功事件 | 定位成功时 |
| location-error | error: Object | 定位失败事件 | 定位失败时 |
| address-search | keyword: String, results: Array | 地址搜索事件 | 搜索地址时 |
| reverse-geocode | coordinate: Object, address: Object | 逆地理编码事件 | 坐标转地址时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| focus | - | void | 使组件获得焦点 |
| blur | - | void | 使组件失去焦点 |
| clear | - | void | 清空选择 |
| getSelectedData | - | Object | 获取选中的完整数据 |
| getCoordinate | - | Object | 获取坐标信息 |
| setCoordinate | (lng: Number, lat: Number) | void | 设置坐标 |
| getCurrentLocation | - | Promise | 获取当前位置 |
| searchAddress | (keyword: String) | Promise | 搜索地址 |
| reverseGeocode | (lng: Number, lat: Number) | Promise | 逆地理编码 |
| showMap | - | void | 显示地图选择器 |
| hideMap | - | void | 隐藏地图选择器 |

## 数据结构

### 地区数据结构
```typescript
interface RegionData {
  code: string;                    // 地区编码
  name: string;                    // 地区名称
  level: number;                   // 层级：1-省，2-市，3-区
  parentCode?: string;             // 父级编码
  children?: RegionData[];         // 子级数据
  coordinate?: {                   // 坐标信息
    lng: number;                   // 经度
    lat: number;                   // 纬度
  };
  [key: string]: any;              // 其他扩展字段
}
```

### 坐标数据结构
```typescript
interface CoordinateData {
  lng: number;                     // 经度
  lat: number;                     // 纬度
  address?: string;                // 详细地址
  province?: string;               // 省份
  city?: string;                   // 城市
  district?: string;               // 区县
  street?: string;                 // 街道
  streetNumber?: string;           // 门牌号
  adcode?: string;                 // 行政区划代码
  citycode?: string;               // 城市编码
}
```

### 地图配置结构
```typescript
interface MapConfig {
  // 高德地图配置
  amap?: {
    key: string;                   // API密钥
    version?: string;              // 版本号
    plugins?: string[];            // 插件列表
    zoom?: number;                 // 缩放级别
    center?: [number, number];     // 中心点坐标
    mapStyle?: string;             // 地图样式
  };
  
  // 腾讯地图配置
  tmap?: {
    key: string;                   // API密钥
    version?: string;              // 版本号
    zoom?: number;                 // 缩放级别
    center?: [number, number];     // 中心点坐标
    mapTypeId?: string;            // 地图类型
  };
  
  // 通用配置
  width?: string;                  // 地图宽度
  height?: string;                 // 地图高度
  showToolbar?: boolean;           // 是否显示工具栏
  showScale?: boolean;             // 是否显示比例尺
  enableScrollWheelZoom?: boolean; // 是否启用滚轮缩放
  enableDoubleClickZoom?: boolean; // 是否启用双击缩放
  enableKeyboard?: boolean;        // 是否启用键盘操作
}
```

## 使用示例

### 基础地区选择
```vue
<template>
  <div>
    <div class="region-examples">
      <div class="example-item">
        <label>省市区三级选择：</label>
        <FuniRegion
          v-model="region1"
          :level="3"
          placeholder="请选择省市区"
          @change="handleRegionChange"
        />
        <span class="result">{{ region1 }}</span>
      </div>
      
      <div class="example-item">
        <label>省市二级选择：</label>
        <FuniRegion
          v-model="region2"
          :level="2"
          placeholder="请选择省市"
        />
        <span class="result">{{ region2 }}</span>
      </div>
      
      <div class="example-item">
        <label>仅省份选择：</label>
        <FuniRegion
          v-model="region3"
          :level="1"
          placeholder="请选择省份"
        />
        <span class="result">{{ region3 }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const region1 = ref('')
const region2 = ref('')
const region3 = ref('')

const handleRegionChange = (value, selectedData) => {
  console.log('地区变化:', value, selectedData)
}
</script>

<style scoped>
.region-examples {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.example-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.example-item label {
  width: 120px;
  text-align: right;
}

.result {
  color: #666;
  font-size: 14px;
}
</style>
```

### 不同值格式示例
```vue
<template>
  <div class="value-format-examples">
    <div class="example-item">
      <label>编码格式：</label>
      <FuniRegion
        v-model="codeValue"
        value-format="code"
        placeholder="返回地区编码"
      />
      <span class="result">{{ codeValue }}</span>
    </div>
    
    <div class="example-item">
      <label>名称格式：</label>
      <FuniRegion
        v-model="nameValue"
        value-format="name"
        separator=" - "
        placeholder="返回地区名称"
      />
      <span class="result">{{ nameValue }}</span>
    </div>
    
    <div class="example-item">
      <label>对象格式：</label>
      <FuniRegion
        v-model="objectValue"
        value-format="object"
        placeholder="返回完整对象"
      />
      <div class="result">
        <pre>{{ JSON.stringify(objectValue, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const codeValue = ref('')
const nameValue = ref('')
const objectValue = ref(null)
</script>

<style scoped>
.value-format-examples {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.example-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.example-item label {
  width: 120px;
  text-align: right;
  margin-top: 8px;
}

.result {
  color: #666;
  font-size: 14px;
}

.result pre {
  background: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  max-width: 300px;
  overflow: auto;
}
</style>
```

### 多选地区示例
```vue
<template>
  <div>
    <div class="multiple-examples">
      <div class="example-item">
        <label>多选地区：</label>
        <FuniRegion
          v-model="multipleRegions"
          multiple
          :collapse-tags="false"
          placeholder="可选择多个地区"
          @change="handleMultipleChange"
        />
      </div>
      
      <div class="example-item">
        <label>折叠标签：</label>
        <FuniRegion
          v-model="collapseRegions"
          multiple
          collapse-tags
          :max-collapse-tags="2"
          placeholder="标签折叠显示"
        />
      </div>
    </div>
    
    <div class="selected-regions">
      <h4>已选择的地区：</h4>
      <ul>
        <li v-for="(region, index) in multipleRegions" :key="index">
          {{ region }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const multipleRegions = ref([])
const collapseRegions = ref([])

const handleMultipleChange = (value, selectedData) => {
  console.log('多选地区变化:', value, selectedData)
}
</script>

<style scoped>
.multiple-examples {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.example-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.example-item label {
  width: 120px;
  text-align: right;
}

.selected-regions {
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.selected-regions ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}
</style>
```

### 地图选择示例
```vue
<template>
  <div>
    <div class="map-examples">
      <div class="example-item">
        <label>高德地图选择：</label>
        <FuniRegion
          v-model="amapRegion"
          show-map
          map-type="amap"
          :map-config="amapConfig"
          enable-geolocation
          enable-search
          enable-reverse
          @map-click="handleMapClick"
          @location-success="handleLocationSuccess"
        />
      </div>
      
      <div class="example-item">
        <label>腾讯地图选择：</label>
        <FuniRegion
          v-model="tmapRegion"
          show-map
          map-type="tmap"
          :map-config="tmapConfig"
          enable-geolocation
          @map-click="handleMapClick"
        />
      </div>
    </div>
    
    <div class="coordinate-info" v-if="selectedCoordinate">
      <h4>选择的坐标信息：</h4>
      <p>经度：{{ selectedCoordinate.lng }}</p>
      <p>纬度：{{ selectedCoordinate.lat }}</p>
      <p>地址：{{ selectedCoordinate.address }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const amapRegion = ref('')
const tmapRegion = ref('')
const selectedCoordinate = ref(null)

const amapConfig = reactive({
  amap: {
    key: 'your-amap-key',
    version: '2.0',
    plugins: ['AMap.Geolocation', 'AMap.PlaceSearch'],
    zoom: 13,
    center: [116.397428, 39.90923],
    mapStyle: 'normal'
  },
  width: '100%',
  height: '400px',
  showToolbar: true,
  showScale: true,
  enableScrollWheelZoom: true
})

const tmapConfig = reactive({
  tmap: {
    key: 'your-tmap-key',
    version: '1.0',
    zoom: 13,
    center: [116.397428, 39.90923],
    mapTypeId: 'ROADMAP'
  },
  width: '100%',
  height: '400px',
  showToolbar: true
})

const handleMapClick = (coordinate, address) => {
  console.log('地图点击:', coordinate, address)
  selectedCoordinate.value = { ...coordinate, address }
}

const handleLocationSuccess = (position) => {
  console.log('定位成功:', position)
  selectedCoordinate.value = position
}
</script>

<style scoped>
.map-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.example-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-item label {
  font-weight: 500;
}

.coordinate-info {
  padding: 16px;
  background: #f0f9ff;
  border-radius: 4px;
  border-left: 4px solid #3b82f6;
}

.coordinate-info h4 {
  margin: 0 0 12px 0;
  color: #1e40af;
}

.coordinate-info p {
  margin: 4px 0;
  font-size: 14px;
}
</style>
```

### 懒加载示例
```vue
<template>
  <div>
    <FuniRegion
      v-model="lazyRegion"
      lazy
      :lazy-load="loadRegionData"
      placeholder="懒加载地区数据"
      @change="handleLazyChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const lazyRegion = ref('')

const loadRegionData = async (node, resolve) => {
  const { level, value } = node
  
  try {
    // 模拟异步加载数据
    const data = await fetchRegionData(level, value)
    resolve(data)
  } catch (error) {
    console.error('加载地区数据失败:', error)
    resolve([])
  }
}

const fetchRegionData = async (level, parentCode) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = generateMockData(level, parentCode)
      resolve(mockData)
    }, 500)
  })
}

const generateMockData = (level, parentCode) => {
  // 生成模拟数据
  const data = []
  const count = level === 0 ? 5 : 3 // 省份5个，市区3个
  
  for (let i = 1; i <= count; i++) {
    const code = parentCode ? `${parentCode}${i.toString().padStart(2, '0')}` : `${i.toString().padStart(2, '0')}`
    const name = level === 0 ? `省份${i}` : level === 1 ? `城市${i}` : `区县${i}`
    
    data.push({
      code,
      name,
      level: level + 1,
      parentCode,
      leaf: level >= 2 // 区县级别为叶子节点
    })
  }
  
  return data
}

const handleLazyChange = (value, selectedData) => {
  console.log('懒加载地区变化:', value, selectedData)
}
</script>
```

### 地址搜索示例
```vue
<template>
  <div>
    <div class="search-examples">
      <div class="search-input">
        <el-input
          v-model="searchKeyword"
          placeholder="输入地址关键字搜索"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch" :loading="searchLoading">
              搜索
            </el-button>
          </template>
        </el-input>
      </div>
      
      <div class="search-results" v-if="searchResults.length">
        <h4>搜索结果：</h4>
        <ul>
          <li 
            v-for="(result, index) in searchResults" 
            :key="index"
            @click="selectSearchResult(result)"
            class="search-result-item"
          >
            <div class="result-name">{{ result.name }}</div>
            <div class="result-address">{{ result.address }}</div>
            <div class="result-coordinate">
              {{ result.location.lng }}, {{ result.location.lat }}
            </div>
          </li>
        </ul>
      </div>
      
      <div class="selected-result" v-if="selectedResult">
        <h4>选择的地址：</h4>
        <FuniRegion
          v-model="searchRegion"
          :coordinate="selectedResult.location"
          show-map
          enable-reverse
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const searchKeyword = ref('')
const searchLoading = ref(false)
const searchResults = ref([])
const selectedResult = ref(null)
const searchRegion = ref('')

const handleSearch = async () => {
  if (!searchKeyword.value.trim()) return
  
  searchLoading.value = true
  try {
    // 模拟地址搜索API调用
    const results = await searchAddress(searchKeyword.value)
    searchResults.value = results
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searchLoading.value = false
  }
}

const searchAddress = async (keyword) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockResults = [
        {
          name: '北京市朝阳区',
          address: '北京市朝阳区建国门外大街1号',
          location: { lng: 116.434307, lat: 39.902486 }
        },
        {
          name: '北京市海淀区',
          address: '北京市海淀区中关村大街1号',
          location: { lng: 116.310003, lat: 39.992853 }
        }
      ].filter(item => item.name.includes(keyword) || item.address.includes(keyword))
      
      resolve(mockResults)
    }, 1000)
  })
}

const selectSearchResult = (result) => {
  selectedResult.value = result
  searchRegion.value = result.address
}
</script>

<style scoped>
.search-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-results ul {
  list-style: none;
  padding: 0;
  margin: 8px 0 0 0;
}

.search-result-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.search-result-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.result-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.result-address {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.result-coordinate {
  color: #999;
  font-size: 12px;
}

.selected-result {
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
```

## 地图集成

### 高德地图集成
```javascript
// 高德地图工具函数
export const amapUtils = {
  // 初始化地图
  initMap(container, config) {
    return new Promise((resolve, reject) => {
      if (window.AMap) {
        const map = new AMap.Map(container, config)
        resolve(map)
      } else {
        this.loadScript(config.key, config.version)
          .then(() => {
            const map = new AMap.Map(container, config)
            resolve(map)
          })
          .catch(reject)
      }
    })
  },
  
  // 加载地图脚本
  loadScript(key, version = '2.0') {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = `https://webapi.amap.com/maps?v=${version}&key=${key}&callback=initAMap`
      script.onerror = reject
      
      window.initAMap = () => {
        resolve()
        delete window.initAMap
      }
      
      document.head.appendChild(script)
    })
  },
  
  // 地理编码
  geocode(address) {
    return new Promise((resolve, reject) => {
      AMap.plugin('AMap.Geocoder', () => {
        const geocoder = new AMap.Geocoder()
        geocoder.getLocation(address, (status, result) => {
          if (status === 'complete' && result.geocodes.length) {
            resolve(result.geocodes[0])
          } else {
            reject(new Error('地理编码失败'))
          }
        })
      })
    })
  },
  
  // 逆地理编码
  reverseGeocode(lng, lat) {
    return new Promise((resolve, reject) => {
      AMap.plugin('AMap.Geocoder', () => {
        const geocoder = new AMap.Geocoder()
        geocoder.getAddress([lng, lat], (status, result) => {
          if (status === 'complete' && result.regeocode) {
            resolve(result.regeocode)
          } else {
            reject(new Error('逆地理编码失败'))
          }
        })
      })
    })
  }
}
```

### 腾讯地图集成
```javascript
// 腾讯地图工具函数
export const tmapUtils = {
  // 初始化地图
  initMap(container, config) {
    return new Promise((resolve, reject) => {
      if (window.qq && window.qq.maps) {
        const map = new qq.maps.Map(container, config)
        resolve(map)
      } else {
        this.loadScript(config.key)
          .then(() => {
            const map = new qq.maps.Map(container, config)
            resolve(map)
          })
          .catch(reject)
      }
    })
  },
  
  // 加载地图脚本
  loadScript(key) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = `https://map.qq.com/api/gljs?v=1.exp&key=${key}&callback=initTMap`
      script.onerror = reject
      
      window.initTMap = () => {
        resolve()
        delete window.initTMap
      }
      
      document.head.appendChild(script)
    })
  },
  
  // 地理编码
  geocode(address) {
    return new Promise((resolve, reject) => {
      const geocoder = new qq.maps.Geocoder()
      geocoder.getLocation(address)
        .then(resolve)
        .catch(reject)
    })
  },
  
  // 逆地理编码
  reverseGeocode(lng, lat) {
    return new Promise((resolve, reject) => {
      const geocoder = new qq.maps.Geocoder()
      const latLng = new qq.maps.LatLng(lat, lng)
      geocoder.getAddress(latLng)
        .then(resolve)
        .catch(reject)
    })
  }
}
```

## 注意事项

### 1. 地图API配置
- 需要申请对应的地图API密钥
- 注意API调用频率限制
- 合理设置地图加载超时时间
- 处理网络异常和API错误

### 2. 数据格式
- 统一地区编码标准（如国标GB/T 2260）
- 注意坐标系转换（WGS84、GCJ02、BD09）
- 处理数据更新和同步问题
- 提供数据校验和容错机制

### 3. 用户体验
- 提供清晰的选择提示和帮助
- 支持键盘导航和快捷操作
- 合理设置加载状态和错误提示
- 考虑移动端的触摸体验

### 4. 性能优化
- 使用懒加载减少初始数据量
- 合理缓存地区数据
- 避免频繁的地图API调用
- 优化大数据量的渲染性能

## 常见问题

### Q: 如何自定义地区数据源？
A: 通过lazy和lazyLoad属性实现自定义数据加载

### Q: 如何处理坐标系转换？
A: 使用coordinate属性指定坐标系，组件内部会进行转换

### Q: 如何实现地址的模糊搜索？
A: 启用enableSearch功能，结合地图API的搜索服务

### Q: 如何处理海外地区选择？
A: 扩展地区数据源，支持国际地区编码标准
