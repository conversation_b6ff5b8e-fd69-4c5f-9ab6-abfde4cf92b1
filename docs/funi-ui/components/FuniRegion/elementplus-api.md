# FuniRegion ElementPlus API支持

## 概述

FuniRegion是基于ElementPlus的`el-select`组件构建的复合组件，内部使用多个`el-select`实现级联地区选择功能。通过`regionProps`配置可以为每个级别的选择器设置不同的ElementPlus属性，实现对ElementPlus选择器API的灵活支持。

## 支持的ElementPlus API

### el-select 属性透传

FuniRegion通过`regionProps`配置支持所有`el-select`的原生属性：

```vue
<template>
  <FuniRegion
    v-model="regionData"
    :lvl="3"
    :region-props="regionProps"
  />
</template>

<script setup>
const regionProps = {
  // 省份选择器属性
  province: {
    // ElementPlus el-select 原生属性
    placeholder: '请选择省份',
    disabled: false,
    clearable: true,
    filterable: true,
    size: 'default',
    'collapse-tags': false,
    'collapse-tags-tooltip': false,
    'multiple-limit': 0,
    'allow-create': false,
    'filter-method': customFilterMethod,
    remote: false,
    'remote-method': remoteMethod,
    loading: false,
    'loading-text': '加载中',
    'no-match-text': '无匹配数据',
    'no-data-text': '无数据',
    'popper-class': 'custom-province-dropdown',
    'reserve-keyword': false,
    'default-first-option': false,
    teleported: true,
    persistent: true,
    'automatic-dropdown': false,
    'clear-icon': 'CircleClose',
    'fit-input-width': false,
    'suffix-icon': 'ArrowDown',
    'tag-type': 'info',
    'validate-event': false // 注意：FuniRegion内部设置为false
  },
  
  // 城市选择器属性
  city: {
    placeholder: '请选择城市',
    clearable: true,
    filterable: true,
    size: 'default'
  },
  
  // 区县选择器属性
  district: {
    placeholder: '请选择区县',
    clearable: true,
    filterable: true,
    size: 'default'
  }
}
</script>
```

### 属性详细说明

#### 基础属性
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| placeholder | String | — | 占位符 |
| disabled | Boolean | false | 是否禁用 |
| clearable | Boolean | false | 是否可以清空选项 |
| filterable | Boolean | false | 是否可搜索 |
| size | String | default | 输入框尺寸，可选值：large/default/small |
| multiple | Boolean | false | 是否多选（地区选择通常不使用） |
| collapse-tags | Boolean | false | 多选时是否将选中值按文字的形式展示 |
| collapse-tags-tooltip | Boolean | false | 当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签 |
| multiple-limit | Number | 0 | 多选时用户最多可以选择的项目数 |
| allow-create | Boolean | false | 是否允许用户创建新条目 |
| filter-method | Function | — | 自定义搜索方法 |
| remote | Boolean | false | 是否为远程搜索 |
| remote-method | Function | — | 远程搜索方法 |
| loading | Boolean | false | 是否正在从远程获取数据 |
| loading-text | String | 加载中 | 远程加载时显示的文字 |
| no-match-text | String | 无匹配数据 | 搜索条件无匹配时显示的文字 |
| no-data-text | String | 无数据 | 选项为空时显示的文字 |
| popper-class | String | — | Select下拉框的类名 |
| reserve-keyword | Boolean | false | 多选且可搜索时，是否在选中一个选项后保留当前的搜索关键词 |
| default-first-option | Boolean | false | 在输入框按下回车，选择第一个匹配项 |
| teleported | Boolean | true | 是否将弹出框插入至body元素 |
| persistent | Boolean | true | 当下拉选择器未被激活并且persistent设置为false时，选择器会被删除 |
| automatic-dropdown | Boolean | false | 对于不可搜索的Select，是否在输入框获得焦点后自动弹出选项菜单 |
| clear-icon | String/Component | CircleClose | 自定义清空图标 |
| fit-input-width | Boolean | false | 下拉框的宽度是否与输入框相同 |
| suffix-icon | String/Component | ArrowDown | 自定义后缀图标 |
| tag-type | String | info | 标签类型 |
| validate-event | Boolean | false | 输入时是否触发表单的校验（FuniRegion内部设置为false） |

### 事件处理

FuniRegion内部处理了`el-select`的事件，主要通过`change`事件对外暴露：

```vue
<template>
  <FuniRegion
    v-model="regionData"
    :lvl="3"
    @change="handleRegionChange"
  />
</template>

<script setup>
// FuniRegion的change事件会在任何级别的选择器发生变化时触发
const handleRegionChange = (regionValue) => {
  console.log('地区选择变化:', regionValue)
  // regionValue包含所有级别的选择结果
}

// 如果需要监听特定级别的变化，可以通过watch监听modelValue
import { watch } from 'vue'

const regionData = ref({
  province: { value: undefined, label: undefined },
  city: { value: undefined, label: undefined },
  district: { value: undefined, label: undefined }
})

watch(() => regionData.value.province, (newProvince, oldProvince) => {
  console.log('省份选择变化:', newProvince, oldProvince)
})

watch(() => regionData.value.city, (newCity, oldCity) => {
  console.log('城市选择变化:', newCity, oldCity)
})
</script>
```

### 详细地址输入框API

FuniRegion还包含一个`el-input`组件用于输入详细地址：

```vue
<template>
  <FuniRegion
    v-model="regionData"
    :lvl="3"
    :show-address-full="true"
    :address-full-width="'100%'"
    @address-change="handleAddressChange"
  />
</template>

<script setup>
const handleAddressChange = (address) => {
  console.log('详细地址变化:', address)
}
</script>
```

详细地址输入框支持的属性：
- `placeholder`: 默认为"详细地址"
- `validate-event`: 设置为false
- 右侧附加了地图图标按钮

## 自定义过滤方法

### 省份搜索过滤
```vue
<script setup>
const regionProps = {
  province: {
    filterable: true,
    filterMethod: (value) => {
      return (option) => {
        // 支持拼音搜索
        return option.label.toLowerCase().includes(value.toLowerCase()) ||
               option.pinyin?.toLowerCase().includes(value.toLowerCase())
      }
    }
  }
}
</script>
```

### 城市搜索过滤
```vue
<script setup>
const regionProps = {
  city: {
    filterable: true,
    filterMethod: (value) => {
      return (option) => {
        // 支持简称搜索
        return option.label.includes(value) ||
               option.shortName?.includes(value) ||
               option.alias?.includes(value)
      }
    }
  }
}
</script>
```

## 远程搜索支持

虽然FuniRegion主要用于本地数据选择，但也可以配置远程搜索：

```vue
<script setup>
const regionProps = {
  province: {
    remote: true,
    filterable: true,
    remoteMethod: async (query) => {
      if (!query) return
      
      try {
        const response = await searchProvinces(query)
        // 更新省份选项数据
        updateProvinceOptions(response.data)
      } catch (error) {
        console.error('搜索省份失败:', error)
      }
    },
    loading: false,
    loadingText: '搜索中...',
    noMatchText: '无匹配省份',
    noDataText: '请输入关键词搜索'
  }
}

const searchProvinces = async (keyword) => {
  return await $http.get('/api/provinces/search', { keyword })
}

const updateProvinceOptions = (provinces) => {
  // 更新省份选项逻辑
}
</script>
```

## 样式定制

### CSS变量支持

FuniRegion继承了ElementPlus的CSS变量系统：

```css
:root {
  /* 选择器相关变量 */
  --el-select-border-color-hover: #c0c4cc;
  --el-select-disabled-border: #e4e7ed;
  --el-select-font-size: 14px;
  --el-select-close-hover-color: #909399;
  --el-select-input-color: #666;
  --el-select-multiple-input-color: #666;
  --el-select-input-focus-border-color: #409eff;
  --el-select-input-font-size: 14px;
  
  /* 下拉框相关变量 */
  --el-select-dropdown-bg: #fff;
  --el-select-dropdown-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --el-select-dropdown-empty-color: #999;
  --el-select-dropdown-max-height: 274px;
  --el-select-dropdown-padding: 6px 0;
  --el-select-dropdown-empty-padding: 10px 0;
  
  /* 选项相关变量 */
  --el-select-option-color: #606266;
  --el-select-option-disabled-color: #c0c4cc;
  --el-select-option-height: 34px;
  --el-select-option-hover-bg: #f5f7fa;
  --el-select-option-selected-color: #409eff;
  --el-select-option-selected-bg: #f5f7fa;
}
```

### 自定义样式类
```vue
<template>
  <FuniRegion
    v-model="regionData"
    :lvl="3"
    :region-props="customRegionProps"
    class="custom-region"
  />
</template>

<script setup>
const customRegionProps = {
  province: {
    popperClass: 'custom-province-dropdown'
  },
  city: {
    popperClass: 'custom-city-dropdown'
  },
  district: {
    popperClass: 'custom-district-dropdown'
  }
}
</script>

<style>
.custom-region {
  /* 自定义地区选择器样式 */
}

.custom-region .regionGroup {
  /* 自定义地区组样式 */
  display: grid;
  grid-template-columns: repeat(var(--length), 1fr);
  gap: 10px;
}

.custom-region .regionItem {
  /* 自定义地区项样式 */
}

.custom-region .addressFull {
  /* 自定义详细地址样式 */
  grid-column: 1 / -1;
  margin-top: 10px;
}

.custom-province-dropdown {
  /* 自定义省份下拉框样式 */
}

.custom-city-dropdown {
  /* 自定义城市下拉框样式 */
}

.custom-district-dropdown {
  /* 自定义区县下拉框样式 */
}
</style>
```

## 兼容性说明

FuniRegion与ElementPlus版本兼容性：
- 支持ElementPlus 2.0+
- 建议使用ElementPlus 2.3.0及以上版本以获得最佳体验
- 所有ElementPlus选择器相关组件的API都得到支持

## 注意事项

### 1. 属性透传机制
- FuniRegion通过`regionProps`配置为每个级别的选择器设置属性
- 每个级别的选择器都是独立的`el-select`实例
- 属性配置只影响对应级别的选择器

### 2. 事件处理
- FuniRegion内部处理了选择器的`change`事件
- 对外只暴露统一的`change`事件
- `validate-event`属性被强制设置为`false`以避免表单验证冲突

### 3. 数据格式
- FuniRegion使用特定的数据格式：`{ value, label }`
- 内部会自动处理数据格式转换
- 选项数据格式：`{ code, name }`，会自动映射为`{ value, label }`

### 4. 样式布局
- FuniRegion使用CSS Grid布局
- 通过CSS变量`--length`控制列数
- 详细地址输入框占据整行宽度

### 5. 表单集成
- 在表单中使用时，建议使用自定义验证器
- 组件内部的`validate-event`设置为`false`
- 需要手动触发表单验证
