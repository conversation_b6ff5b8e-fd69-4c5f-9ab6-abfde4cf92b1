# FuniRegion 配置架构

## 组件配置接口

### 基础配置类型
```typescript
interface FuniRegionConfig {
  // 基础属性
  modelValue?: RegionValue;            // 绑定值
  lvl?: number;                        // 选择层级：1-5（省市区社区街道）
  showAddressFull?: boolean;           // 是否显示详细地址输入框
  addressFullWidth?: string;           // 详细地址输入框宽度
  regionProps?: RegionPropsConfig;     // 各级选择器的属性配置
  startFather?: StartFatherConfig;     // 起始节点配置
  extension?: Function | string;       // 扩展组件
  
  // 事件处理
  onChange?: (value: RegionValue) => void;
  onAddressChange?: (address: string) => void;
  onMapClick?: (coordinate: Coordinate) => void;
  onLocationSuccess?: (location: Location) => void;
}
```

### 地区值类型
```typescript
interface RegionValue {
  province?: RegionItem;               // 省份
  city?: RegionItem;                   // 城市
  district?: RegionItem;               // 区县
  community?: RegionItem;              // 社区
  street?: RegionItem;                 // 街道
  addressFull?: string;                // 详细地址
}

interface RegionItem {
  value?: string;                      // 地区编码
  label?: string;                      // 地区名称
}
```

### 起始节点配置
```typescript
interface StartFatherConfig {
  lvl?: number;                        // 起始级别：0-省，1-市，2-区，3-社区，4-街道
  code?: string;                       // 父级编码
}

// 默认配置
const defaultStartFather: StartFatherConfig = {
  lvl: 0,
  code: undefined
};
```

### 地区属性配置
```typescript
interface RegionPropsConfig {
  province?: SelectProps;              // 省份选择器属性
  city?: SelectProps;                  // 城市选择器属性
  district?: SelectProps;              // 区县选择器属性
  community?: SelectProps;             // 社区选择器属性
  street?: SelectProps;                // 街道选择器属性
}

interface SelectProps {
  placeholder?: string;                // 占位符
  disabled?: boolean;                  // 是否禁用
  clearable?: boolean;                 // 是否可清空
  filterable?: boolean;                // 是否可搜索
  size?: ComponentSize;                // 尺寸
  [key: string]: any;                  // 其他ElementPlus Select属性
}
```

### 坐标和位置类型
```typescript
interface Coordinate {
  lng: number;                         // 经度
  lat: number;                         // 纬度
}

interface Location {
  coordinate: Coordinate;              // 坐标
  address: string;                     // 地址
  province?: string;                   // 省份
  city?: string;                       // 城市
  district?: string;                   // 区县
  street?: string;                     // 街道
  adcode?: string;                     // 行政区划代码
}
```

### API配置类型
```typescript
interface RegionApiConfig {
  getProvince: ApiEndpoint;            // 获取省份列表
  getChild: ApiEndpoint;               // 获取下级地区列表
}

interface ApiEndpoint {
  url: string;                         // 接口地址
  method: 'get' | 'post' | 'fetch';    // 请求方法
  returnName?: string;                 // 返回数据字段名
  request?: Record<string, any>;       // 请求参数
  defaultProps: {                      // 字段映射
    code: string;                      // 编码字段名
    name: string;                      // 名称字段名
  };
}
```

## 配置示例

### 基础地区选择器配置
```typescript
const basicRegionConfig: FuniRegionConfig = {
  modelValue: {
    province: { value: undefined, label: undefined },
    city: { value: undefined, label: undefined },
    district: { value: undefined, label: undefined },
    community: { value: undefined, label: undefined },
    street: { value: undefined, label: undefined },
    addressFull: undefined
  },
  lvl: 3, // 省市区三级
  showAddressFull: true,
  addressFullWidth: '100%'
};
```

### 省市区三级选择配置
```typescript
const threeLevelConfig: FuniRegionConfig = {
  modelValue: {
    province: { value: undefined, label: undefined },
    city: { value: undefined, label: undefined },
    district: { value: undefined, label: undefined },
    addressFull: undefined
  },
  lvl: 3,
  showAddressFull: true,
  regionProps: {
    province: {
      placeholder: '请选择省份',
      clearable: true,
      filterable: true
    },
    city: {
      placeholder: '请选择城市',
      clearable: true,
      filterable: true
    },
    district: {
      placeholder: '请选择区县',
      clearable: true,
      filterable: true
    }
  },
  startFather: {
    lvl: 0,
    code: undefined
  }
};
```

### 从指定城市开始选择配置
```typescript
const cityStartConfig: FuniRegionConfig = {
  modelValue: {
    district: { value: undefined, label: undefined },
    community: { value: undefined, label: undefined },
    street: { value: undefined, label: undefined },
    addressFull: undefined
  },
  lvl: 3, // 从区县开始，选择3级
  showAddressFull: true,
  regionProps: {
    district: {
      placeholder: '请选择区县',
      clearable: true
    },
    community: {
      placeholder: '请选择社区',
      clearable: true
    },
    street: {
      placeholder: '请选择街道',
      clearable: true
    }
  },
  startFather: {
    lvl: 1, // 从市级开始
    code: '110100' // 北京市编码
  }
};
```

### 带扩展组件配置
```typescript
const extensionConfig: FuniRegionConfig = {
  modelValue: {
    province: { value: undefined, label: undefined },
    city: { value: undefined, label: undefined },
    district: { value: undefined, label: undefined },
    addressFull: undefined
  },
  lvl: 3,
  showAddressFull: true,
  extension: (regionValue) => {
    // 返回自定义组件或内容
    return `当前选择：${regionValue.province?.label || ''} ${regionValue.city?.label || ''} ${regionValue.district?.label || ''}`;
  }
};
```

### 自定义API配置
```typescript
const customApiConfig = {
  getProvince: {
    url: '/api/regions/provinces',
    method: 'get',
    returnName: 'data',
    request: {
      level: 0
    },
    defaultProps: {
      code: 'regionCode',
      name: 'regionName'
    }
  },
  getChild: {
    url: '/api/regions/children',
    method: 'post',
    returnName: 'list',
    defaultProps: {
      code: 'regionCode',
      name: 'regionName'
    }
  }
};
```

## 表单集成配置

### 在FuniForm中使用
```typescript
interface FormRegionSchema {
  prop: string;
  label: string;
  component: 'FuniRegion';
  props: FuniRegionConfig;
  rules?: FormRule[];
  span?: number;
  hidden?: boolean | ((formData: any) => boolean);
}

const formSchema: FormRegionSchema = {
  prop: 'address',
  label: '地址',
  component: 'FuniRegion',
  props: {
    lvl: 3,
    showAddressFull: true,
    regionProps: {
      province: { placeholder: '请选择省份' },
      city: { placeholder: '请选择城市' },
      district: { placeholder: '请选择区县' }
    }
  },
  rules: [
    {
      validator: (rule, value, callback) => {
        if (!value?.province?.value) {
          callback(new Error('请选择省份'));
        } else if (!value?.city?.value) {
          callback(new Error('请选择城市'));
        } else if (!value?.district?.value) {
          callback(new Error('请选择区县'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  span: 24
};
```

### 在FuniSearch中使用
```typescript
interface SearchRegionSchema {
  prop: string;
  label: string;
  component: 'FuniRegion';
  props: FuniRegionConfig;
  span?: number;
}

const searchSchema: SearchRegionSchema = {
  prop: 'region',
  label: '地区',
  component: 'FuniRegion',
  props: {
    lvl: 2, // 搜索条件通常只需要省市
    showAddressFull: false,
    regionProps: {
      province: { 
        placeholder: '省份',
        clearable: true
      },
      city: { 
        placeholder: '城市',
        clearable: true
      }
    }
  },
  span: 8
};
```

## 数据转换配置

### 地区数据标准化
```typescript
interface RegionDataTransform {
  // 原始数据转换为标准格式
  normalize?: (rawData: any[]) => RegionItem[];
  
  // 地区编码转换
  codeTransform?: (code: string) => string;
  
  // 地区名称格式化
  nameFormat?: (name: string, level: number) => string;
  
  // 坐标系转换
  coordinateTransform?: (lng: number, lat: number, from: string, to: string) => Coordinate;
}

const transformConfig: RegionDataTransform = {
  normalize: (rawData) => {
    return rawData.map(item => ({
      value: item.districtCode || item.code,
      label: item.districtName || item.name,
      level: item.level,
      parentCode: item.parentCode
    }));
  },
  
  codeTransform: (code) => {
    // 处理编码格式，如去除前缀等
    return code.startsWith('156') ? code.slice(3) : code;
  },
  
  nameFormat: (name, level) => {
    // 根据级别格式化名称
    const suffixes = ['省', '市', '区', '街道', '社区'];
    return level < suffixes.length ? `${name}${suffixes[level]}` : name;
  },
  
  coordinateTransform: (lng, lat, from, to) => {
    // 坐标系转换，如GCJ02转WGS84
    if (from === 'GCJ02' && to === 'WGS84') {
      return gcj02towgs84(lng, lat);
    }
    return { lng, lat };
  }
};
```

## 验证规则配置

### 常用验证规则
```typescript
interface RegionValidationRules {
  required?: boolean;
  minLevel?: number;                   // 最少选择级别
  maxLevel?: number;                   // 最多选择级别
  requiredAddress?: boolean;           // 是否必须填写详细地址
  addressMinLength?: number;           // 详细地址最小长度
  customValidator?: (value: RegionValue) => boolean | string;
}

const validationRules: RegionValidationRules = {
  required: true,
  minLevel: 2, // 至少选择到市级
  maxLevel: 3, // 最多选择到区级
  requiredAddress: true,
  addressMinLength: 5,
  customValidator: (value) => {
    // 自定义验证逻辑
    if (value.province?.value === '110000' && !value.district?.value) {
      return '北京市必须选择到区级';
    }
    return true;
  }
};

// 验证器实现
const createRegionValidator = (rules: RegionValidationRules) => {
  return (rule: any, value: RegionValue, callback: Function) => {
    if (rules.required && !value) {
      return callback(new Error('请选择地区'));
    }
    
    if (rules.minLevel) {
      const levels = ['province', 'city', 'district', 'community', 'street'];
      const selectedLevel = levels.findIndex(level => !value[level]?.value);
      if (selectedLevel < rules.minLevel) {
        return callback(new Error(`请至少选择到${levels[rules.minLevel - 1]}`));
      }
    }
    
    if (rules.requiredAddress && !value.addressFull) {
      return callback(new Error('请填写详细地址'));
    }
    
    if (rules.addressMinLength && value.addressFull && value.addressFull.length < rules.addressMinLength) {
      return callback(new Error(`详细地址至少${rules.addressMinLength}个字符`));
    }
    
    if (rules.customValidator) {
      const result = rules.customValidator(value);
      if (result !== true) {
        return callback(new Error(typeof result === 'string' ? result : '地区选择不符合要求'));
      }
    }
    
    callback();
  };
};
```

## 性能优化配置

### 缓存配置
```typescript
interface RegionCacheConfig {
  enabled?: boolean;                   // 是否启用缓存
  expireTime?: number;                 // 缓存过期时间（毫秒）
  maxSize?: number;                    // 最大缓存条目数
  storageType?: 'memory' | 'localStorage' | 'sessionStorage';
}

const cacheConfig: RegionCacheConfig = {
  enabled: true,
  expireTime: 30 * 60 * 1000, // 30分钟
  maxSize: 1000,
  storageType: 'memory'
};
```

### 懒加载配置
```typescript
interface LazyLoadConfig {
  enabled?: boolean;                   // 是否启用懒加载
  preloadLevel?: number;               // 预加载级别
  batchSize?: number;                  // 批量加载大小
  debounceTime?: number;               // 防抖时间
}

const lazyLoadConfig: LazyLoadConfig = {
  enabled: true,
  preloadLevel: 1, // 预加载到市级
  batchSize: 50,
  debounceTime: 300
};
```
