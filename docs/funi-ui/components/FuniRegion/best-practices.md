# FuniRegion 最佳实践

## 推荐用法

### 1. 标准地区选择器配置
```vue
<template>
  <FuniRegion
    v-model="regionData"
    :lvl="3"
    :show-address-full="true"
    :region-props="regionProps"
    @change="handleRegionChange"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

// 推荐：使用响应式数据管理地区值
const regionData = ref({
  province: { value: undefined, label: undefined },
  city: { value: undefined, label: undefined },
  district: { value: undefined, label: undefined },
  addressFull: undefined
})

// 推荐：为每个级别配置合适的属性
const regionProps = reactive({
  province: {
    placeholder: '请选择省份',
    clearable: true,
    filterable: true,
    size: 'default'
  },
  city: {
    placeholder: '请选择城市',
    clearable: true,
    filterable: true,
    size: 'default'
  },
  district: {
    placeholder: '请选择区县',
    clearable: true,
    filterable: true,
    size: 'default'
  }
})

// 推荐：统一的变化处理
const handleRegionChange = (value) => {
  console.log('地区选择变化:', value)
  // 在这里处理业务逻辑
}
</script>
```

### 2. 根据业务场景选择合适的级别
```vue
<script setup>
import { ref, computed } from 'vue'

const businessType = ref('shipping') // shipping, billing, company

// 推荐：根据业务类型动态配置级别
const regionLevel = computed(() => {
  switch (businessType.value) {
    case 'shipping':
      return 3 // 收货地址：省市区
    case 'billing':
      return 3 // 账单地址：省市区
    case 'company':
      return 4 // 公司地址：省市区社区
    case 'detailed':
      return 5 // 详细地址：省市区社区街道
    default:
      return 3
  }
})

// 推荐：根据业务类型配置不同的属性
const regionProps = computed(() => {
  const baseProps = {
    clearable: true,
    filterable: true
  }
  
  switch (businessType.value) {
    case 'shipping':
      return {
        province: { ...baseProps, placeholder: '收货省份' },
        city: { ...baseProps, placeholder: '收货城市' },
        district: { ...baseProps, placeholder: '收货区县' }
      }
    case 'billing':
      return {
        province: { ...baseProps, placeholder: '账单省份' },
        city: { ...baseProps, placeholder: '账单城市' },
        district: { ...baseProps, placeholder: '账单区县' }
      }
    default:
      return {
        province: { ...baseProps, placeholder: '请选择省份' },
        city: { ...baseProps, placeholder: '请选择城市' },
        district: { ...baseProps, placeholder: '请选择区县' }
      }
  }
})
</script>
```

### 3. 起始节点最佳实践
```vue
<script setup>
import { ref, computed } from 'vue'

const userLocation = ref('beijing') // 用户当前位置

// 推荐：根据用户位置智能设置起始节点
const startFather = computed(() => {
  const locationMap = {
    'beijing': { lvl: 1, code: '110100' }, // 北京市
    'shanghai': { lvl: 1, code: '310100' }, // 上海市
    'guangzhou': { lvl: 1, code: '440100' }, // 广州市
    'shenzhen': { lvl: 1, code: '440300' }  // 深圳市
  }
  
  return locationMap[userLocation.value] || { lvl: 0, code: undefined }
})

// 推荐：根据起始节点调整级别配置
const adjustedLevel = computed(() => {
  return startFather.value.lvl === 0 ? 3 : 2 // 如果从省开始选3级，从市开始选2级
})
</script>
```

## 性能优化

### 1. 数据缓存策略
```vue
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 推荐：实现地区数据缓存
const regionCache = new Map()
const cacheExpireTime = 30 * 60 * 1000 // 30分钟

const getCachedRegionData = (key) => {
  const cached = regionCache.get(key)
  if (!cached) return null
  
  if (Date.now() - cached.timestamp > cacheExpireTime) {
    regionCache.delete(key)
    return null
  }
  
  return cached.data
}

const setCachedRegionData = (key, data) => {
  regionCache.set(key, {
    data,
    timestamp: Date.now()
  })
}

// 推荐：预加载常用地区数据
const preloadCommonRegions = async () => {
  const commonProvinces = ['110000', '310000', '440000'] // 北京、上海、广东
  
  for (const provinceCode of commonProvinces) {
    try {
      const cities = await loadCities(provinceCode)
      setCachedRegionData(`cities_${provinceCode}`, cities)
    } catch (error) {
      console.warn('预加载地区数据失败:', error)
    }
  }
}

onMounted(() => {
  preloadCommonRegions()
})

// 推荐：清理缓存
onUnmounted(() => {
  regionCache.clear()
})
</script>
```

### 2. 防抖处理
```vue
<script setup>
import { ref } from 'vue'
import { debounce } from 'lodash-es'

// 推荐：对地区变化进行防抖处理
const debouncedRegionChange = debounce((value) => {
  // 处理地区变化的业务逻辑
  handleBusinessLogic(value)
}, 300)

const handleRegionChange = (value) => {
  // 立即更新UI
  updateUI(value)
  
  // 防抖处理业务逻辑
  debouncedRegionChange(value)
}

const updateUI = (value) => {
  // 立即更新界面显示
  console.log('UI更新:', value)
}

const handleBusinessLogic = (value) => {
  // 处理需要防抖的业务逻辑，如API调用
  console.log('业务逻辑处理:', value)
}
</script>
```

## 数据管理

### 1. 地区数据标准化
```vue
<script setup>
import { ref, computed } from 'vue'

const rawRegionData = ref({})

// 推荐：标准化地区数据格式
const normalizedRegionData = computed(() => {
  const normalize = (data) => {
    if (!data || typeof data !== 'object') return {}
    
    return {
      province: normalizeRegionItem(data.province),
      city: normalizeRegionItem(data.city),
      district: normalizeRegionItem(data.district),
      community: normalizeRegionItem(data.community),
      street: normalizeRegionItem(data.street),
      addressFull: data.addressFull || data.address || ''
    }
  }
  
  const normalizeRegionItem = (item) => {
    if (!item) return { value: undefined, label: undefined }
    
    if (typeof item === 'string') {
      return { value: item, label: item }
    }
    
    return {
      value: item.value || item.code || item.id,
      label: item.label || item.name || item.title
    }
  }
  
  return normalize(rawRegionData.value)
})

// 推荐：提供数据转换工具
const regionUtils = {
  // 转换为字符串格式
  toString: (regionData, separator = ' ') => {
    const parts = []
    if (regionData.province?.label) parts.push(regionData.province.label)
    if (regionData.city?.label) parts.push(regionData.city.label)
    if (regionData.district?.label) parts.push(regionData.district.label)
    if (regionData.community?.label) parts.push(regionData.community.label)
    if (regionData.street?.label) parts.push(regionData.street.label)
    if (regionData.addressFull) parts.push(regionData.addressFull)
    
    return parts.join(separator)
  },
  
  // 转换为编码数组
  toCodes: (regionData) => {
    const codes = []
    if (regionData.province?.value) codes.push(regionData.province.value)
    if (regionData.city?.value) codes.push(regionData.city.value)
    if (regionData.district?.value) codes.push(regionData.district.value)
    if (regionData.community?.value) codes.push(regionData.community.value)
    if (regionData.street?.value) codes.push(regionData.street.value)
    
    return codes
  },
  
  // 检查是否完整
  isComplete: (regionData, requiredLevel = 3) => {
    const levels = ['province', 'city', 'district', 'community', 'street']
    
    for (let i = 0; i < requiredLevel; i++) {
      if (!regionData[levels[i]]?.value) {
        return false
      }
    }
    
    return true
  }
}
</script>
```

### 2. 表单验证最佳实践
```vue
<script setup>
import { ref } from 'vue'

// 推荐：创建地区验证器工厂函数
const createRegionValidator = (options = {}) => {
  const {
    requiredLevel = 3,
    requiredAddress = false,
    addressMinLength = 5,
    customRules = []
  } = options
  
  return (rule, value, callback) => {
    // 检查必填级别
    const levels = ['province', 'city', 'district', 'community', 'street']
    const levelNames = ['省份', '城市', '区县', '社区', '街道']
    
    for (let i = 0; i < requiredLevel; i++) {
      if (!value?.[levels[i]]?.value) {
        return callback(new Error(`请选择${levelNames[i]}`))
      }
    }
    
    // 检查详细地址
    if (requiredAddress && !value?.addressFull) {
      return callback(new Error('请填写详细地址'))
    }
    
    if (value?.addressFull && value.addressFull.length < addressMinLength) {
      return callback(new Error(`详细地址至少${addressMinLength}个字符`))
    }
    
    // 执行自定义验证规则
    for (const customRule of customRules) {
      const result = customRule(value)
      if (result !== true) {
        return callback(new Error(typeof result === 'string' ? result : '地区选择不符合要求'))
      }
    }
    
    callback()
  }
}

// 使用示例
const regionRules = [
  {
    validator: createRegionValidator({
      requiredLevel: 3,
      requiredAddress: true,
      addressMinLength: 10,
      customRules: [
        (value) => {
          // 北京市必须选择到区级
          if (value.province?.value === '110000' && !value.district?.value) {
            return '北京市必须选择到区级'
          }
          return true
        }
      ]
    }),
    trigger: 'change'
  }
]
</script>
```

## 错误处理

### 1. API错误处理
```vue
<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)
const error = ref(null)

// 推荐：统一的错误处理函数
const handleApiError = (error, context = '操作') => {
  console.error(`${context}失败:`, error)
  
  let message = `${context}失败`
  if (error.response?.data?.message) {
    message = error.response.data.message
  } else if (error.message) {
    message = error.message
  }
  
  ElMessage.error(message)
  return message
}

// 推荐：带重试机制的数据加载
const loadRegionDataWithRetry = async (params, maxRetries = 3) => {
  let retries = 0
  
  while (retries < maxRetries) {
    try {
      loading.value = true
      error.value = null
      
      const data = await loadRegionData(params)
      return data
    } catch (err) {
      retries++
      error.value = err
      
      if (retries >= maxRetries) {
        handleApiError(err, '加载地区数据')
        throw err
      }
      
      // 指数退避重试
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000))
    } finally {
      loading.value = false
    }
  }
}

// 推荐：网络状态检查
const checkNetworkStatus = () => {
  if (!navigator.onLine) {
    ElMessage.warning('网络连接已断开，请检查网络设置')
    return false
  }
  return true
}
</script>
```

### 2. 数据完整性检查
```vue
<script setup>
import { ref, watch } from 'vue'

const regionData = ref({})

// 推荐：监听数据变化并进行完整性检查
watch(regionData, (newValue, oldValue) => {
  validateDataIntegrity(newValue)
}, { deep: true })

const validateDataIntegrity = (data) => {
  // 检查数据结构
  if (!data || typeof data !== 'object') {
    console.warn('地区数据格式错误:', data)
    return false
  }
  
  // 检查级联关系
  if (data.city?.value && !data.province?.value) {
    console.warn('选择了城市但未选择省份')
    // 自动修复：清空城市选择
    data.city = { value: undefined, label: undefined }
  }
  
  if (data.district?.value && !data.city?.value) {
    console.warn('选择了区县但未选择城市')
    // 自动修复：清空区县选择
    data.district = { value: undefined, label: undefined }
  }
  
  return true
}
</script>
```

## 可访问性

### 1. 无障碍支持
```vue
<template>
  <div class="region-container">
    <fieldset>
      <legend>地址信息</legend>
      <FuniRegion
        v-model="regionData"
        :lvl="3"
        :show-address-full="true"
        :region-props="accessibleRegionProps"
        @change="handleRegionChange"
      />
    </fieldset>
    
    <div class="region-summary" aria-live="polite">
      {{ regionSummary }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// 推荐：为无障碍访问配置适当的属性
const accessibleRegionProps = {
  province: {
    placeholder: '请选择省份',
    'aria-label': '省份选择',
    clearable: true,
    filterable: true
  },
  city: {
    placeholder: '请选择城市',
    'aria-label': '城市选择',
    clearable: true,
    filterable: true
  },
  district: {
    placeholder: '请选择区县',
    'aria-label': '区县选择',
    clearable: true,
    filterable: true
  }
}

// 推荐：提供地区选择摘要
const regionSummary = computed(() => {
  const parts = []
  if (regionData.value.province?.label) parts.push(regionData.value.province.label)
  if (regionData.value.city?.label) parts.push(regionData.value.city.label)
  if (regionData.value.district?.label) parts.push(regionData.value.district.label)
  
  if (parts.length === 0) {
    return '尚未选择地区'
  }
  
  return `已选择：${parts.join(' ')}`
})
</script>

<style scoped>
.region-container fieldset {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.region-container legend {
  padding: 0 10px;
  font-weight: 500;
  color: #606266;
}

.region-summary {
  font-size: 14px;
  color: #909399;
  margin-top: 10px;
}
</style>
```

## 测试建议

### 1. 单元测试
```javascript
// 推荐：测试地区选择器的基本功能
describe('FuniRegion', () => {
  test('should emit change event when region changes', async () => {
    const wrapper = mount(FuniRegion, {
      props: {
        modelValue: {
          province: { value: undefined, label: undefined },
          city: { value: undefined, label: undefined },
          district: { value: undefined, label: undefined }
        },
        lvl: 3
      }
    })
    
    // 模拟选择省份
    await wrapper.vm.changeSelect('province', 0, '110000')
    
    expect(wrapper.emitted('change')).toBeTruthy()
    expect(wrapper.emitted('change')[0][0].province.value).toBe('110000')
  })
  
  test('should validate region data integrity', () => {
    const regionData = {
      province: { value: '110000', label: '北京市' },
      city: { value: undefined, label: undefined },
      district: { value: '110105', label: '朝阳区' }
    }
    
    const isValid = validateDataIntegrity(regionData)
    expect(isValid).toBe(false) // 应该检测到数据不完整
  })
})
```

### 2. 集成测试
```javascript
// 推荐：测试与表单的集成
describe('FuniRegion in Form', () => {
  test('should validate required region fields', async () => {
    const wrapper = mount(FormComponent, {
      props: {
        schema: [
          {
            prop: 'address',
            component: 'FuniRegion',
            props: { lvl: 3 },
            rules: [{ validator: createRegionValidator({ requiredLevel: 3 }) }]
          }
        ]
      }
    })
    
    const form = wrapper.findComponent({ name: 'ElForm' })
    const result = await form.vm.validate()
    
    expect(result).toBe(false)
  })
})
```

## 常见问题

### 1. 数据格式问题
```vue
<script setup>
// ❌ 错误：数据格式不正确
const badRegionData = {
  province: '110000', // 应该是对象格式
  city: { code: '110100' }, // 缺少必要字段
  district: null // 应该是对象格式
}

// ✅ 正确：使用标准数据格式
const goodRegionData = {
  province: { value: '110000', label: '北京市' },
  city: { value: '110100', label: '北京市' },
  district: { value: '110105', label: '朝阳区' },
  addressFull: '某某街道某某号'
}
</script>
```

### 2. 级联关系问题
```vue
<script setup>
// ❌ 错误：破坏级联关系
const handleProvinceChange = (value) => {
  // 没有清空下级选择
  regionData.value.province = value
}

// ✅ 正确：维护级联关系
const handleProvinceChange = (value) => {
  regionData.value.province = value
  // 清空下级选择
  regionData.value.city = { value: undefined, label: undefined }
  regionData.value.district = { value: undefined, label: undefined }
}
</script>
```

### 3. 性能问题
```vue
<script setup>
// ❌ 错误：没有缓存的重复请求
const loadCities = async (provinceCode) => {
  // 每次都重新请求
  const response = await api.getCities(provinceCode)
  return response.data
}

// ✅ 正确：使用缓存避免重复请求
const cityCache = new Map()

const loadCities = async (provinceCode) => {
  if (cityCache.has(provinceCode)) {
    return cityCache.get(provinceCode)
  }
  
  const response = await api.getCities(provinceCode)
  cityCache.set(provinceCode, response.data)
  return response.data
}
</script>
```
