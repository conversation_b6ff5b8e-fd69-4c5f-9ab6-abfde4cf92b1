# FuniRegion 使用示例

## 基础使用

### 省市区三级选择
```vue
<template>
  <div class="basic-region-demo">
    <h3>省市区三级选择</h3>
    <FuniRegion
      v-model="regionData"
      :lvl="3"
      :show-address-full="true"
      @change="handleRegionChange"
    />
    <div class="result">
      <h4>选择结果：</h4>
      <pre>{{ JSON.stringify(regionData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const regionData = ref({
  province: { value: undefined, label: undefined },
  city: { value: undefined, label: undefined },
  district: { value: undefined, label: undefined },
  addressFull: undefined
})

const handleRegionChange = (value) => {
  console.log('地区选择变化:', value)
}
</script>

<style scoped>
.basic-region-demo {
  padding: 20px;
}

.result {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.result pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
}
</style>
```

### 自定义级别选择
```vue
<template>
  <div class="custom-level-demo">
    <h3>自定义级别选择</h3>
    
    <div class="demo-item">
      <h4>省市二级选择</h4>
      <FuniRegion
        v-model="twoLevelData"
        :lvl="2"
        :show-address-full="false"
        :region-props="twoLevelProps"
      />
    </div>
    
    <div class="demo-item">
      <h4>完整五级选择（省市区社区街道）</h4>
      <FuniRegion
        v-model="fiveLevelData"
        :lvl="5"
        :show-address-full="true"
        :region-props="fiveLevelProps"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const twoLevelData = ref({
  province: { value: undefined, label: undefined },
  city: { value: undefined, label: undefined }
})

const fiveLevelData = ref({
  province: { value: undefined, label: undefined },
  city: { value: undefined, label: undefined },
  district: { value: undefined, label: undefined },
  community: { value: undefined, label: undefined },
  street: { value: undefined, label: undefined },
  addressFull: undefined
})

const twoLevelProps = reactive({
  province: {
    placeholder: '请选择省份',
    clearable: true,
    filterable: true
  },
  city: {
    placeholder: '请选择城市',
    clearable: true,
    filterable: true
  }
})

const fiveLevelProps = reactive({
  province: { placeholder: '省份' },
  city: { placeholder: '城市' },
  district: { placeholder: '区县' },
  community: { placeholder: '社区' },
  street: { placeholder: '街道' }
})
</script>

<style scoped>
.custom-level-demo {
  padding: 20px;
}

.demo-item {
  margin-bottom: 30px;
}

.demo-item h4 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
```

### 指定起始节点
```vue
<template>
  <div class="start-father-demo">
    <h3>指定起始节点选择</h3>
    
    <div class="demo-item">
      <h4>从北京市开始选择区县</h4>
      <FuniRegion
        v-model="beijingData"
        :lvl="2"
        :start-father="beijingStartFather"
        :show-address-full="true"
      />
    </div>
    
    <div class="demo-item">
      <h4>从广东省开始选择城市</h4>
      <FuniRegion
        v-model="guangdongData"
        :lvl="2"
        :start-father="guangdongStartFather"
        :show-address-full="false"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const beijingData = ref({
  district: { value: undefined, label: undefined },
  community: { value: undefined, label: undefined },
  addressFull: undefined
})

const guangdongData = ref({
  city: { value: undefined, label: undefined },
  district: { value: undefined, label: undefined }
})

const beijingStartFather = reactive({
  lvl: 1, // 从市级开始
  code: '110100' // 北京市编码
})

const guangdongStartFather = reactive({
  lvl: 0, // 从省级开始
  code: '440000' // 广东省编码
})
</script>

<style scoped>
.start-father-demo {
  padding: 20px;
}

.demo-item {
  margin-bottom: 30px;
}

.demo-item h4 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
```

## 高级功能

### 带扩展组件
```vue
<template>
  <div class="extension-demo">
    <h3>带扩展组件</h3>
    
    <div class="demo-item">
      <h4>字符串扩展</h4>
      <FuniRegion
        v-model="stringExtensionData"
        :lvl="3"
        :extension="stringExtension"
        :show-address-full="false"
      />
    </div>
    
    <div class="demo-item">
      <h4>函数扩展</h4>
      <FuniRegion
        v-model="functionExtensionData"
        :lvl="3"
        :extension="functionExtension"
        :show-address-full="false"
      />
    </div>
    
    <div class="demo-item">
      <h4>组件扩展</h4>
      <FuniRegion
        v-model="componentExtensionData"
        :lvl="3"
        :extension="componentExtension"
        :show-address-full="false"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { ElButton, ElTag } from 'element-plus'

const stringExtensionData = ref({
  province: { value: undefined, label: undefined },
  city: { value: undefined, label: undefined },
  district: { value: undefined, label: undefined }
})

const functionExtensionData = ref({
  province: { value: undefined, label: undefined },
  city: { value: undefined, label: undefined },
  district: { value: undefined, label: undefined }
})

const componentExtensionData = ref({
  province: { value: undefined, label: undefined },
  city: { value: undefined, label: undefined },
  district: { value: undefined, label: undefined }
})

// 字符串扩展
const stringExtension = '（必填）'

// 函数扩展
const functionExtension = (regionValue) => {
  const parts = []
  if (regionValue.province?.label) parts.push(regionValue.province.label)
  if (regionValue.city?.label) parts.push(regionValue.city.label)
  if (regionValue.district?.label) parts.push(regionValue.district.label)
  
  return parts.length > 0 ? `当前选择：${parts.join(' - ')}` : '请选择地区'
}

// 组件扩展
const componentExtension = (regionValue) => {
  const hasSelection = regionValue.province?.value || regionValue.city?.value || regionValue.district?.value
  
  return h('div', { style: 'margin-left: 10px; display: inline-flex; align-items: center; gap: 8px;' }, [
    hasSelection 
      ? h(ElTag, { type: 'success', size: 'small' }, () => '已选择')
      : h(ElTag, { type: 'info', size: 'small' }, () => '未选择'),
    h(ElButton, { 
      size: 'small', 
      type: 'text',
      onClick: () => clearSelection(regionValue)
    }, () => '清空')
  ])
}

const clearSelection = (regionValue) => {
  Object.keys(regionValue).forEach(key => {
    if (typeof regionValue[key] === 'object' && regionValue[key] !== null) {
      regionValue[key].value = undefined
      regionValue[key].label = undefined
    } else {
      regionValue[key] = undefined
    }
  })
}
</script>

<style scoped>
.extension-demo {
  padding: 20px;
}

.demo-item {
  margin-bottom: 30px;
}

.demo-item h4 {
  margin-bottom: 10px;
  color: #606266;
}
</style>
```

### 地图选择功能
```vue
<template>
  <div class="map-demo">
    <h3>地图选择功能</h3>
    
    <div class="demo-item">
      <h4>带地图选择的地区选择器</h4>
      <FuniRegion
        ref="mapRegionRef"
        v-model="mapRegionData"
        :lvl="3"
        :show-address-full="true"
        @change="handleMapRegionChange"
        @address-change="handleAddressChange"
      />
      
      <div class="map-controls">
        <el-button @click="openMap">打开地图选择</el-button>
        <el-button @click="getCurrentLocation">获取当前位置</el-button>
        <el-button @click="clearLocation">清空位置</el-button>
      </div>
      
      <div class="location-info" v-if="locationInfo">
        <h5>位置信息：</h5>
        <p>经度：{{ locationInfo.lng }}</p>
        <p>纬度：{{ locationInfo.lat }}</p>
        <p>地址：{{ locationInfo.address }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const mapRegionRef = ref()
const mapRegionData = ref({
  province: { value: undefined, label: undefined },
  city: { value: undefined, label: undefined },
  district: { value: undefined, label: undefined },
  addressFull: undefined
})

const locationInfo = ref(null)

const handleMapRegionChange = (value) => {
  console.log('地区选择变化:', value)
}

const handleAddressChange = (address) => {
  console.log('详细地址变化:', address)
}

const openMap = () => {
  // 调用组件的地图打开方法
  if (mapRegionRef.value && mapRegionRef.value.openMap) {
    mapRegionRef.value.openMap()
  } else {
    ElMessage.info('地图功能需要配置地图API')
  }
}

const getCurrentLocation = () => {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { longitude, latitude } = position.coords
        locationInfo.value = {
          lng: longitude,
          lat: latitude,
          address: '当前位置'
        }
        ElMessage.success('获取位置成功')
      },
      (error) => {
        console.error('获取位置失败:', error)
        ElMessage.error('获取位置失败')
      }
    )
  } else {
    ElMessage.error('浏览器不支持地理定位')
  }
}

const clearLocation = () => {
  locationInfo.value = null
  mapRegionData.value = {
    province: { value: undefined, label: undefined },
    city: { value: undefined, label: undefined },
    district: { value: undefined, label: undefined },
    addressFull: undefined
  }
  ElMessage.info('位置信息已清空')
}
</script>

<style scoped>
.map-demo {
  padding: 20px;
}

.demo-item {
  margin-bottom: 30px;
}

.demo-item h4 {
  margin-bottom: 10px;
  color: #606266;
}

.map-controls {
  margin: 15px 0;
}

.map-controls .el-button {
  margin-right: 10px;
}

.location-info {
  margin-top: 15px;
  padding: 10px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
}

.location-info h5 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.location-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #606266;
}
</style>
```

## 表单集成示例

### 在FuniForm中使用
```vue
<template>
  <div class="form-integration-demo">
    <h3>表单集成示例</h3>
    <FuniForm
      v-model="formData"
      :schema="formSchema"
      :col="1"
      @submit="handleSubmit"
      @reset="handleReset"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const formData = ref({
  name: '',
  phone: '',
  homeAddress: {
    province: { value: undefined, label: undefined },
    city: { value: undefined, label: undefined },
    district: { value: undefined, label: undefined },
    addressFull: undefined
  },
  workAddress: {
    province: { value: undefined, label: undefined },
    city: { value: undefined, label: undefined },
    district: { value: undefined, label: undefined },
    community: { value: undefined, label: undefined },
    addressFull: undefined
  }
})

const formSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号' },
    rules: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ]
  },
  {
    prop: 'homeAddress',
    label: '家庭地址',
    component: 'FuniRegion',
    props: {
      lvl: 3,
      showAddressFull: true,
      regionProps: {
        province: { placeholder: '请选择省份' },
        city: { placeholder: '请选择城市' },
        district: { placeholder: '请选择区县' }
      }
    },
    rules: [
      {
        validator: (rule, value, callback) => {
          if (!value?.province?.value) {
            callback(new Error('请选择家庭地址省份'))
          } else if (!value?.city?.value) {
            callback(new Error('请选择家庭地址城市'))
          } else if (!value?.district?.value) {
            callback(new Error('请选择家庭地址区县'))
          } else if (!value?.addressFull) {
            callback(new Error('请填写详细家庭地址'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ]
  },
  {
    prop: 'workAddress',
    label: '工作地址',
    component: 'FuniRegion',
    props: {
      lvl: 4,
      showAddressFull: true,
      regionProps: {
        province: { placeholder: '请选择省份' },
        city: { placeholder: '请选择城市' },
        district: { placeholder: '请选择区县' },
        community: { placeholder: '请选择社区' }
      }
    },
    rules: [
      {
        validator: (rule, value, callback) => {
          if (!value?.province?.value) {
            callback(new Error('请选择工作地址省份'))
          } else if (!value?.city?.value) {
            callback(new Error('请选择工作地址城市'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ]
  }
])

const handleSubmit = (data) => {
  console.log('提交表单:', data)
  ElMessage.success('提交成功')
}

const handleReset = () => {
  ElMessage.info('表单已重置')
}
</script>

<style scoped>
.form-integration-demo {
  padding: 20px;
  max-width: 800px;
}
</style>
```

## 业务场景示例

### 用户信息管理
```vue
<template>
  <div class="user-info-demo">
    <h3>用户信息管理</h3>

    <div class="user-form">
      <div class="form-item">
        <label>收货地址：</label>
        <FuniRegion
          v-model="userInfo.shippingAddress"
          :lvl="3"
          :show-address-full="true"
          :region-props="shippingAddressProps"
          @change="handleShippingAddressChange"
        />
      </div>

      <div class="form-item">
        <label>账单地址：</label>
        <FuniRegion
          v-model="userInfo.billingAddress"
          :lvl="3"
          :show-address-full="true"
          :region-props="billingAddressProps"
        />
        <el-checkbox
          v-model="sameAsShipping"
          @change="handleSameAsShipping"
          style="margin-left: 10px;"
        >
          与收货地址相同
        </el-checkbox>
      </div>

      <div class="form-item">
        <label>公司地址：</label>
        <FuniRegion
          v-model="userInfo.companyAddress"
          :lvl="4"
          :show-address-full="true"
          :start-father="companyStartFather"
          :region-props="companyAddressProps"
        />
      </div>
    </div>

    <div class="actions">
      <el-button @click="saveUserInfo" type="primary">保存</el-button>
      <el-button @click="resetUserInfo">重置</el-button>
    </div>

    <div class="user-info-display" v-if="savedUserInfo">
      <h4>已保存的用户信息：</h4>
      <pre>{{ JSON.stringify(savedUserInfo, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

const userInfo = ref({
  shippingAddress: {
    province: { value: undefined, label: undefined },
    city: { value: undefined, label: undefined },
    district: { value: undefined, label: undefined },
    addressFull: undefined
  },
  billingAddress: {
    province: { value: undefined, label: undefined },
    city: { value: undefined, label: undefined },
    district: { value: undefined, label: undefined },
    addressFull: undefined
  },
  companyAddress: {
    district: { value: undefined, label: undefined },
    community: { value: undefined, label: undefined },
    street: { value: undefined, label: undefined },
    addressFull: undefined
  }
})

const savedUserInfo = ref(null)
const sameAsShipping = ref(false)

const shippingAddressProps = reactive({
  province: { placeholder: '收货省份', clearable: true },
  city: { placeholder: '收货城市', clearable: true },
  district: { placeholder: '收货区县', clearable: true }
})

const billingAddressProps = reactive({
  province: { placeholder: '账单省份', clearable: true },
  city: { placeholder: '账单城市', clearable: true },
  district: { placeholder: '账单区县', clearable: true }
})

const companyAddressProps = reactive({
  district: { placeholder: '公司区县', clearable: true },
  community: { placeholder: '公司社区', clearable: true },
  street: { placeholder: '公司街道', clearable: true }
})

// 假设公司在北京市朝阳区
const companyStartFather = reactive({
  lvl: 2, // 从区级开始
  code: '110105' // 朝阳区编码
})

const handleShippingAddressChange = (value) => {
  console.log('收货地址变化:', value)

  // 如果选择了"与收货地址相同"，自动同步账单地址
  if (sameAsShipping.value) {
    userInfo.value.billingAddress = JSON.parse(JSON.stringify(value))
  }
}

const handleSameAsShipping = (checked) => {
  if (checked) {
    userInfo.value.billingAddress = JSON.parse(JSON.stringify(userInfo.value.shippingAddress))
  }
}

const saveUserInfo = () => {
  // 验证必填字段
  if (!userInfo.value.shippingAddress.province?.value) {
    ElMessage.error('请选择收货地址')
    return
  }

  savedUserInfo.value = JSON.parse(JSON.stringify(userInfo.value))
  ElMessage.success('用户信息保存成功')
}

const resetUserInfo = () => {
  userInfo.value = {
    shippingAddress: {
      province: { value: undefined, label: undefined },
      city: { value: undefined, label: undefined },
      district: { value: undefined, label: undefined },
      addressFull: undefined
    },
    billingAddress: {
      province: { value: undefined, label: undefined },
      city: { value: undefined, label: undefined },
      district: { value: undefined, label: undefined },
      addressFull: undefined
    },
    companyAddress: {
      district: { value: undefined, label: undefined },
      community: { value: undefined, label: undefined },
      street: { value: undefined, label: undefined },
      addressFull: undefined
    }
  }
  sameAsShipping.value = false
  ElMessage.info('用户信息已重置')
}
</script>

<style scoped>
.user-info-demo {
  padding: 20px;
}

.user-form {
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.form-item label {
  min-width: 80px;
  font-weight: 500;
  color: #606266;
  line-height: 32px;
}

.actions {
  margin-bottom: 20px;
}

.actions .el-button {
  margin-right: 10px;
}

.user-info-display {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.user-info-display h4 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.user-info-display pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
}
</style>
```
