# FuniUI数据结构规范

## API数据结构规范

### 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "success": true,
  "timestamp": 1640995200000
}
```

### 分页数据格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 5
  },
  "success": true
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "success": false,
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ]
}
```

## 组件数据结构

### FuniListPageV2数据结构

#### cardTab配置结构
```json
[
  {
    "key": "all",
    "label": "全部用户",
    "api": "/api/users/all",
    "requestParams": {
      "status": "active"
    },
    "curdOption": {
      "columns": [
        {
          "label": "ID",
          "prop": "id",
          "width": 80
        },
        {
          "label": "用户名",
          "prop": "username",
          "minWidth": 120
        },
        {
          "label": "状态",
          "prop": "status",
        }
      ],
      "btns": [
        {
          "name": "编辑",
          "key": "edit",
          "props": {
            "type": "primary",
            "size": "small"
          }
        }
      ],
      "selection": true,
    }
  }
]
```

### FuniDetail数据结构

#### detailHeadOption配置结构
```json
{
  "title": "用户详情",
  "serialName": "用户ID",
  "no": "U001",
  "statusName": "状态",
  "status": "正常",
  "hideStatusBar": false,
  "links": [
    {
      "title": "关联订单:",
      "name": "O202301001",
      "props": {
        "type": "primary",
        "link": true
      }
    }
  ],
  "btns": [
    {
      "name": "编辑",
      "type": "edit",
      "props": {
        "type": "primary"
      }
    },
    {
      "name": "删除",
      "type": "delete",
      "props": {
        "type": "danger"
      }
    }
  ]
}
```

#### steps配置结构
```json
[
  {
    "title": "基本信息",
    "icon": "user",
    "width": 200,
    "slot": "basicInfo",
    "preservable": true
  },
  {
    "title": "详细信息",
    "slot": "detailInfo"
  },
  {
    "title": "操作记录",
    "slot": "operationLog"
  }
]
```

## 工作流数据结构

### 工作流参数结构
```json
{
  "businessId": "BIZ202301001",
  "businessConfigCode": "LEAVE_APPLICATION",
  "sysId": "SYS001",
  "preBusinessId": "BIZ202212001",
  "preSysId": "SYS001"
}
```

### 审核按钮配置结构
```json
[
  {
    "key": "同意",
    "action": "APPROVE",
    "props": {
      "type": "primary"
    }
  },
  {
    "key": "拒绝",
    "action": "REJECT",
    "props": {
      "type": "danger"
    }
  },
  {
    "key": "退回",
    "action": "RETURN",
    "props": {
      "type": "warning"
    }
  }
]
```

### 附件配置结构
```json
{
  "attachmentConfigs": [
    {
      "code": "ID_CARD",
      "name": "身份证",
      "required": true,
      "maxCount": 2,
      "maxSize": 5242880,
      "allowTypes": ["jpg", "png", "pdf"],
      "description": "请上传身份证正反面"
    },
    {
      "code": "RESUME",
      "name": "简历",
      "required": false,
      "maxCount": 1,
      "maxSize": 10485760,
      "allowTypes": ["pdf", "doc", "docx"],
      "description": "请上传个人简历"
    }
  ]
}
```

### 文件列表结构
```json
{
  "fileList": [
    {
      "attachmentConfigCode": "ID_CARD",
      "attachmentConfigName": "身份证",
      "attachmentInfos": [
        {
          "attachmentInfoId": "FILE001",
          "attachmentName": "身份证正面.jpg",
          "attachmentSize": 1024000,
          "attachmentType": "jpg",
          "uploadTime": "2023-01-01 10:00:00",
          "uploadUser": "张三",
          "downloadUrl": "/api/file/download/FILE001"
        }
      ],
      "needNumber": 2,
      "attachmentAmount": 1
    }
  ]
}
```

## 表单数据结构

### 表单配置结构
```json
{
  "schema": [
    {
      "prop": "username",
      "label": "用户名",
      "component": "el-input",
      "props": {
        "placeholder": "请输入用户名",
        "maxlength": 50
      },
      "rules": [
        {
          "required": true,
          "message": "用户名不能为空",
          "trigger": "blur"
        },
        {
          "min": 3,
          "max": 20,
          "message": "用户名长度在3到20个字符",
          "trigger": "blur"
        }
      ],
      "span": 12
    },
    {
      "prop": "email",
      "label": "邮箱",
      "component": "el-input",
      "props": {
        "type": "email",
        "placeholder": "请输入邮箱地址"
      },
      "rules": [
        {
          "required": true,
          "message": "邮箱不能为空",
          "trigger": "blur"
        },
        {
          "type": "email",
          "message": "请输入正确的邮箱地址",
          "trigger": "blur"
        }
      ],
      "span": 12
    },
    {
      "prop": "department",
      "label": "部门",
      "component": "el-select",
      "props": {
        "placeholder": "请选择部门"
      },
      "options": [
        { "label": "技术部", "value": "tech" },
        { "label": "产品部", "value": "product" },
        { "label": "运营部", "value": "operation" }
      ],
      "rules": [
        {
          "required": true,
          "message": "请选择部门",
          "trigger": "change"
        }
      ],
      "span": 12
    }
  ],
  "labelWidth": "100px",
  "size": "default"
}
```

### 表单数据结构
```json
{
  "username": "zhangsan",
  "email": "<EMAIL>",
  "department": "tech",
  "phone": "***********",
  "address": "北京市朝阳区",
  "remark": "备注信息"
}
```

## 选项数据结构

### 基础选项结构
```json
[
  {
    "label": "选项1",
    "value": "option1",
    "disabled": false
  },
  {
    "label": "选项2",
    "value": "option2",
    "disabled": true
  }
]
```

### 树形选项结构
```json
[
  {
    "label": "一级选项1",
    "value": "level1-1",
    "children": [
      {
        "label": "二级选项1-1",
        "value": "level2-1-1"
      },
      {
        "label": "二级选项1-2",
        "value": "level2-1-2"
      }
    ]
  },
  {
    "label": "一级选项2",
    "value": "level1-2",
    "children": [
      {
        "label": "二级选项2-1",
        "value": "level2-2-1"
      }
    ]
  }
]
```

### 分组选项结构
```json
[
  {
    "label": "热门城市",
    "options": [
      { "label": "北京", "value": "beijing" },
      { "label": "上海", "value": "shanghai" }
    ]
  },
  {
    "label": "其他城市",
    "options": [
      { "label": "广州", "value": "guangzhou" },
      { "label": "深圳", "value": "shenzhen" }
    ]
  }
]
```

## 权限数据结构

### 用户权限结构
```json
{
  "user": {
    "id": "U001",
    "username": "zhangsan",
    "roles": ["admin", "user"],
    "permissions": [
      "user:create",
      "user:read",
      "user:update",
      "user:delete"
    ]
  }
}
```

### 权限配置结构
```json
{
  "auth": {
    "permissions": ["user:edit"],
    "roles": ["admin"],
    "mode": "or"
  }
}
```

## Mock数据规范

### 列表数据Mock
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "username": "zhangsan",
        "email": "<EMAIL>",
        "status": 1,
        "createTime": "2023-01-01 10:00:00"
      },
      {
        "id": 2,
        "username": "lisi",
        "email": "<EMAIL>",
        "status": 0,
        "createTime": "2023-01-02 10:00:00"
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20
  },
  "success": true
}
```

### 详情数据Mock
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "email": "<EMAIL>",
    "phone": "***********",
    "department": "tech",
    "status": 1,
    "createTime": "2023-01-01 10:00:00",
    "updateTime": "2023-01-01 10:00:00"
  },
  "success": true
}
```

### 工作流Mock数据
```json
{
  "businessId": "BIZ202301001",
  "auditButtons": [
    {
      "key": "同意",
      "action": "APPROVE",
      "props": { "type": "primary" }
    },
    {
      "key": "拒绝",
      "action": "REJECT",
      "props": { "type": "danger" }
    }
  ],
  "attachmentConfigs": [
    {
      "code": "ID_CARD",
      "name": "身份证",
      "required": true,
      "maxCount": 2,
      "maxSize": 5242880,
      "allowTypes": ["jpg", "png", "pdf"]
    }
  ],
  "auditHistory": [
    {
      "id": "AH001",
      "action": "SUBMIT",
      "actionName": "提交申请",
      "operator": "张三",
      "operateTime": "2023-01-01 10:00:00",
      "opinion": "提交申请"
    }
  ]
}
```

## 数据验证规范

### 必填字段验证
```javascript
const requiredRule = {
  required: true,
  message: '此字段不能为空',
  trigger: 'blur'
};
```

### 长度验证
```javascript
const lengthRule = {
  min: 3,
  max: 20,
  message: '长度在3到20个字符',
  trigger: 'blur'
};
```

### 格式验证
```javascript
const emailRule = {
  type: 'email',
  message: '请输入正确的邮箱地址',
  trigger: 'blur'
};

const phoneRule = {
  pattern: /^1[3-9]\d{9}$/,
  message: '请输入正确的手机号码',
  trigger: 'blur'
};
```

### 自定义验证
```javascript
const customRule = {
  validator: (rule, value, callback) => {
    if (!value) {
      callback(new Error('请输入密码'));
    } else if (value.length < 6) {
      callback(new Error('密码长度不能少于6位'));
    } else {
      callback();
    }
  },
  trigger: 'blur'
};
```

## 最佳实践

### 1. 数据命名规范
- 使用驼峰命名法
- 布尔值使用is/has/can等前缀
- 时间字段使用Time后缀
- ID字段使用Id后缀

### 2. 数据类型规范
- 字符串使用string类型
- 数字使用number类型
- 布尔值使用boolean类型
- 数组使用Array类型
- 对象使用Object类型

### 3. 默认值规范
- 字符串默认值为空字符串''
- 数字默认值为0
- 布尔值默认值为false
- 数组默认值为空数组[]
- 对象默认值为空对象{}

### 4. 错误处理规范
- 统一错误码定义
- 提供友好的错误信息
- 区分业务错误和系统错误
- 提供错误恢复建议
