# FuniUI通用类型定义

## 基础类型

### 组件尺寸类型
```typescript
type ComponentSize = 'large' | 'default' | 'small';
```

### 按钮类型
```typescript
type ButtonType = 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default';
```

### 对齐方式类型
```typescript
type AlignType = 'left' | 'center' | 'right';
```

### 业务操作类型
```typescript
type BizNameType = '新建' | 'add' | '编辑' | 'edit' | '详情' | 'detail' | '审核' | 'audit';
```

## 通用接口类型

### API响应类型
```typescript
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

interface PageResponse<T = any> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

interface PageRequest {
  pageNum: number;
  pageSize: number;
  [key: string]: any;
}
```

### 路由配置类型
```typescript
interface RouterConfig {
  path: string;
  title?: string;
  query?: Record<string, any>;
  params?: Record<string, any>;
}
```

### 选项数据类型
```typescript
interface OptionItem {
  label: string;
  value: any;
  disabled?: boolean;
  children?: OptionItem[];
  [key: string]: any;
}

type OptionsData = OptionItem[];
```

## 表单相关类型

### 表单项配置类型
```typescript
interface FormItemConfig {
  prop: string;
  label: string;
  component: string;
  props?: Record<string, any>;
  rules?: FormRule[];
  show?: boolean | ComputedRef<boolean>;
  span?: number;
  offset?: number;
  [key: string]: any;
}

interface FormRule {
  required?: boolean;
  message?: string;
  trigger?: 'blur' | 'change' | string[];
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email';
  validator?: (rule: any, value: any, callback: any) => void;
  min?: number;
  max?: number;
  len?: number;
  pattern?: RegExp;
  [key: string]: any;
}
```

### 搜索配置类型
```typescript
interface SearchConfig {
  schema: SearchSchemaItem[];
  labelWidth?: string;
  inline?: boolean;
  size?: ComponentSize;
}

interface SearchSchemaItem {
  prop: string;
  label: string;
  component: string;
  props?: Record<string, any>;
  options?: OptionsData;
  span?: number;
  show?: boolean | ComputedRef<boolean>;
}
```

## 表格相关类型

### 列配置类型
```typescript
interface ColumnConfig {
  label: string;
  prop: string;
  width?: number;
  minWidth?: number;
  fixed?: 'left' | 'right' | boolean;
  align?: AlignType;
  headerAlign?: AlignType;
  showOverflowTooltip?: boolean;
  sortable?: boolean | 'custom';
  resizable?: boolean;
  slots?: {
    default?: string;
    header?: string;
  };
  formatter?: (row: any, column: any, cellValue: any, index: number) => string;
  show?: boolean | ComputedRef<boolean>;
  type?: 'selection' | 'index' | 'expand';
  index?: number | ((index: number) => number);
  columnKey?: string;
  className?: string;
  labelClassName?: string;
  selectable?: (row: any, index: number) => boolean;
  reserveSelection?: boolean;
  filters?: FilterItem[];
  filterPlacement?: string;
  filterMultiple?: boolean;
  filterMethod?: (value: any, row: any, column: any) => boolean;
  filteredValue?: any[];
}

interface FilterItem {
  text: string;
  value: any;
}
```

### 操作按钮配置类型
```typescript
interface ActionConfig {
  name: string;
  key: string;
  props?: {
    type?: ButtonType;
    size?: ComponentSize;
    icon?: string;
    disabled?: boolean;
    loading?: boolean;
    plain?: boolean;
    round?: boolean;
    circle?: boolean;
    link?: boolean;
    [key: string]: any;
  };
  on?: {
    click?: (row?: any, index?: number) => void;
    [key: string]: Function;
  };
  auth?: string | string[];
  show?: (row?: any, index?: number) => boolean;
  position?: 'header' | 'row';
}
```

### 分页配置类型
```typescript
interface PaginationConfig {
  pageSize?: number;
  pageSizes?: number[];
  layout?: string;
  small?: boolean;
  background?: boolean;
  pagerCount?: number;
  hideOnSinglePage?: boolean;
  prevText?: string;
  nextText?: string;
  disabled?: boolean;
}
```

## 工作流相关类型

### 工作流参数类型
```typescript
interface WorkflowParams {
  businessId: string;
  businessConfigCode?: string;
  sysId?: string;
  preBusinessId?: string;
  preSysId?: string;
}
```

### 审核按钮配置类型
```typescript
interface AuditButtonConfig {
  key: string;
  action: string;
  props?: {
    type?: ButtonType;
    size?: ComponentSize;
    disabled?: boolean;
    loading?: boolean;
    [key: string]: any;
  };
}
```

### 文件信息类型
```typescript
interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadTime: string;
  uploadUser: string;
  [key: string]: any;
}

interface AttachmentConfig {
  code: string;
  name: string;
  required: boolean;
  maxCount: number;
  maxSize: number;
  allowTypes: string[];
  description?: string;
}
```

## 组件配置类型

### FuniListPageV2配置类型
```typescript
interface FuniListPageV2Config {
  cardTab: CardTabItem[];
  isShowSearch?: boolean;
  active?: string;
  showTab?: boolean;
  teleported?: boolean;
  reloadOnActive?: boolean;
}

interface CardTabItem {
  key?: string;
  label: string;
  badge?: boolean;
  api?: string;
  requestParams?: Record<string, any>;
  fixSearchParams?: (searchParams: any) => any;
  searchConfig?: SearchConfig;
  curdOption?: CurdOptionConfig;
  slot?: string;
}

interface CurdOptionConfig {
  api?: string;
  requestParams?: Record<string, any>;
  dataCallback?: DataCallback;
  columns: ColumnConfig[];
  selection?: boolean;
  actions?: ActionConfig[];
  pagination?: PaginationConfig;
  on?: Record<string, Function>;
  paginationExtra?: (params: PaginationExtraParams) => VNode;
  [key: string]: any;
}
```

### FuniDetail配置类型
```typescript
interface FuniDetailConfig {
  detailHeadOption: DetailHeadOption;
  steps: StepConfig[];
  bizName: BizNameType;
  current?: number;
  showHead?: boolean;
  homeRouter?: RouterConfig;
  parentRouter?: RouterConfig;
  businessId?: string;
  sysId?: string;
  showWorkflow?: boolean;
  auditButtons?: AuditButtonConfig[];
  isAuthFixedBtn?: boolean;
  isFormOpinion?: boolean;
  beforeLeave?: (activeName: string, oldActiveName: string) => boolean | Promise<boolean>;
  tsKey?: string;
  tsType?: string;
}

interface DetailHeadOption {
  title?: string;
  serialName?: string;
  no?: string;
  statusName?: string;
  status?: string;
  hideStatusBar?: boolean;
  hideStatusName?: boolean;
  links?: LinkConfig[];
  btns?: HeaderButtonConfig[];
}

interface StepConfig {
  title: string;
  icon?: string;
  width?: number;
  type?: Component;
  slot?: string;
  props?: Record<string, any>;
  on?: Record<string, Function>;
  preservable?: boolean;
}

interface LinkConfig {
  title: string;
  name: string;
  props?: Record<string, any>;
  on?: Record<string, Function>;
}

interface HeaderButtonConfig {
  name: string;
  type?: string;
  triggerEvent?: boolean;
  props?: Record<string, any>;
  on?: Record<string, Function>;
}
```

## 回调函数类型

### 数据处理回调类型
```typescript
interface DataCallback {
  (params: {
    response: any;
    params: any;
  }): {
    list: any[];
    total: number;
    pageNum: number;
    pageSize: number;
  };
}
```

### 事件处理类型
```typescript
interface EventHandlers {
  'selection-change'?: (selection: any[]) => void;
  'sort-change'?: (params: SortChangeParams) => void;
  'row-click'?: (row: any, column: any, event: Event) => void;
  'row-dblclick'?: (row: any, column: any, event: Event) => void;
  'cell-click'?: (row: any, column: any, cell: any, event: Event) => void;
  'header-click'?: (column: any, event: Event) => void;
  'filter-change'?: (filters: Record<string, any[]>) => void;
  'current-change'?: (currentRow: any, oldCurrentRow: any) => void;
  [key: string]: Function;
}

interface SortChangeParams {
  column: any;
  prop: string;
  order: 'ascending' | 'descending' | null;
}

interface PaginationExtraParams {
  total: number;
  pageNum: number;
  pageSize: number;
}
```

## 权限相关类型

### 权限配置类型
```typescript
interface AuthConfig {
  permissions?: string | string[];
  roles?: string | string[];
  mode?: 'and' | 'or';
}

type AuthValue = string | string[] | AuthConfig;
```

## 工具类型

### 深度只读类型
```typescript
type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};
```

### 可选属性类型
```typescript
type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
```

### 必需属性类型
```typescript
type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;
```
