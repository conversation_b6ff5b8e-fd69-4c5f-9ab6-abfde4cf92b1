# FuniUI组件库文档

## 概述

FuniUI是基于ElementPlus进行二次封装的企业级Vue3组件库，专为管理系统和工作流应用设计。所有组件都支持ElementPlus的原生API，通过v-bind透传实现无缝集成。

## 核心特性

- 🎯 **业务导向**：专为管理系统场景设计的组件
- 🔄 **工作流集成**：内置工作流组件支持
- 🎨 **ElementPlus兼容**：完全兼容ElementPlus API
- 🛠️ **开箱即用**：提供完整的配置示例

## 组件分类

### 核心业务组件
- **FuniListPageV2** - 列表页面组件（强制使用）
- **FuniDetail** - 详情页面组件（强制使用）
- **FuniForm** - 表单组件
- **FuniCurd** - 数据表格组件
- **FuniSearch** - 搜索组件

### 工作流组件
- **FuniFileTable** - 工作流附件管理（需businessId）
- **FuniAuditButtomBtn** - 工作流审核按钮（动态配置）
- **FuniBusAuditDrawer** - 审核意见抽屉

### 表单控件
- **FuniSelect** - 下拉选择器
- **FuniTreeSelect** - 树形选择器
- **FuniRUOC** - 角色用户组织选择器
- **FuniMoneyInput** - 金额输入框
- **FuniInputNumber** - 数字输入框

### 展示组件
- **FuniChart** - 图表组件
- **FuniImage** - 图片组件
- **FuniImageView** - 图片预览
- **FuniEditor** - 富文本编辑器

### 交互组件
- **FuniDialog** - 对话框
- **FuniAuthButton** - 权限按钮
- **FuniActions** - 操作按钮组

## 快速开始

### 安装使用

```javascript
// main.js
import FuniUI from '@/components'
app.use(FuniUI)
```

### 基础使用

```vue
<template>
  <!-- 列表页面 -->
  <FuniListPageV2 :cardTab="cardTabConfig" />
  
  <!-- 详情页面 -->
  <FuniDetail :steps="steps" :bizName="bizName" />
  
  <!-- 表单组件 -->
  <FuniForm :schema="formSchema" v-model="formData" />
</template>
```

## 组件选择指南

### 页面类型映射
- **列表页** → FuniListPageV2（强制）
- **详情页** → FuniDetail（强制）
- **新建页** → FuniDetail（强制）
- **编辑页** → FuniDetail（强制）

### 工作流场景
- **工作流附件** → FuniFileTable（需businessId）
- **普通文件上传** → FuniFileTable/upload.vue
- **工作流审核** → FuniAuditButtomBtn + FuniBusAuditDrawer

## 文档结构

```
docs/funi-ui/
├── README.md                    # 组件库总览
├── components/
│   ├── summary.md              # 组件使用总览
│   ├── component-mapping.md    # 业务场景与组件映射
│   ├── FuniListPageV2/         # 核心组件文档
│   ├── FuniDetail/
│   ├── FuniForm/
│   └── ...
└── schemas/
    ├── common-types.md         # 通用类型定义
    └── data-structures.md      # 数据结构规范
```

## 注意事项

### 强制使用规则
1. PC管理页面必须使用FuniListPageV2作为列表页
2. PC管理页面必须使用FuniDetail作为详情/新建/编辑页
3. 工作流附件必须使用FuniFileTable（需businessId）
4. 普通文件上传使用FuniFileTable/upload.vue

### ElementPlus API支持
所有FuniUI组件都支持对应ElementPlus组件的API：

```vue
<template>
  <!-- 透传ElementPlus API -->
  <FuniSelect 
    v-bind="elementPlusProps"
    placeholder="请选择"
    clearable
    filterable
  />
</template>
```

### 工作流组件特殊要求
- FuniFileTable仅在有businessId时渲染
- FuniAuditButtomBtn按钮配置由工作流系统动态返回
- 工作流组件需要完整的Mock数据支持

## 技术支持

- 组件源码：`src/components/`
- 使用示例：`src/apps/demo/`
- 技术文档：`docs/funi-ui/components/`

## 更新日志

### v2.0.0
- 新增FuniListPageV2组件
- 优化工作流组件集成
- 完善ElementPlus API透传

### v1.0.0
- 初始版本发布
- 基础组件库搭建
