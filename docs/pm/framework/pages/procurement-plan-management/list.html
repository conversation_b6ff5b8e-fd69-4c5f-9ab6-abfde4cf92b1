<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>采购计划管理 - 列表 - Funi管理系统</title>
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-list.css" />
</head>

<body>
    <div id="app" class="container">
        <!-- 1. 头部Tab切换 -->
        <div class="tabs">
            <div class="tab-item active" data-tab="todo">待办</div>
            <div class="tab-item" data-tab="done">已办</div>
        </div>

        <!-- 2. 搜索区域 -->
        <div class="search-area collapsed"> <!-- Added 'collapsed' class -->
            <form class="search-form">
                <div class="search-form-item">
                    <label for="planProjectName">计划项目名称:</label>
                    <input type="text" id="planProjectName" name="planProjectName" placeholder="2位以上进行模糊查询" />
                </div>
                <div class="search-form-item">
                    <label for="auditStatus">审核状态:</label>
                    <select id="auditStatus" name="auditStatus">
                        <option value="">请选择</option>
                        <option value="待审核">待审核</option>
                        <option value="审核中">审核中</option>
                        <option value="审核通过">审核通过</option>
                        <option value="审核未过">审核未过</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <label for="procurementType">采购类型:</label>
                    <select id="procurementType" name="procurementType">
                        <option value="">请选择</option>
                        <option value="货物">货物</option>
                        <option value="施工">施工</option>
                        <option value="服务">服务</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <label for="procurementMethod">采购方式:</label>
                    <select id="procurementMethod" name="procurementMethod">
                        <option value="">请选择</option>
                        <option value="公告比选">公告比选</option>
                        <option value="邀请比选">邀请比选</option>
                        <option value="竞争性磋商">竞争性磋商</option>
                        <option value="竞争性谈判">竞争性谈判</option>
                        <option value="询价择优">询价择优</option>
                        <option value="单一来源">单一来源</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <label for="procurementOrganizationMethod">采购组织方式:</label>
                    <select id="procurementOrganizationMethod" name="procurementOrganizationMethod">
                        <option value="">请选择</option>
                        <option value="自主招标">自主招标</option>
                        <option value="委托招标">委托招标</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <label for="fundSource">资金来源:</label>
                    <select id="fundSource" name="fundSource">
                        <option value="">请选择</option>
                        <option value="自有资金">自有资金</option>
                        <option value="政府资本">政府资本</option>
                        <option value="其他社会资本">其他社会资本</option>
                    </select>
                </div>
                <div class="search-form-item">
                    <label for="procurementBudgetAmountMin">采购预算金额(万元):</label>
                    <input type="number" id="procurementBudgetAmountMin" name="procurementBudgetAmountMin"
                        placeholder="最小" />
                    <span>-</span>
                    <input type="number" id="procurementBudgetAmountMax" name="procurementBudgetAmountMax"
                        placeholder="最大" />
                </div>
                <div class="search-form-item">
                    <label for="projectHandler">项目经办人:</label>
                    <input type="text" id="projectHandler" name="projectHandler" placeholder="2位以上进行模糊查询" />
                </div>
                <div class="search-form-item ">
                    <label for="projectApprovalDateStart">立项决策日期:</label>
                    <div class="date-range-picker">
                        <input type="date" id="projectApprovalDateStart" name="projectApprovalDateStart" />
                        <span>~</span>
                        <input type="date" id="projectApprovalDateEnd" name="projectApprovalDateEnd" />
                    </div>
                </div>
                <div class="search-form-item">
                    <label for="createTimeStart">创建时间:</label>
                    <div class="date-range-picker">
                        <input type="date" id="createTimeStart" name="createTimeStart" />
                        <span>~</span>
                        <input type="date" id="createTimeEnd" name="createTimeEnd" />
                    </div>
                </div>
                <div class="search-form-item search-buttons-item">
                    <!-- Added search-form-item and search-buttons-item -->
                    <button type="button" class="button primary" id="queryButton">查询</button>
                    <button type="button" class="button" id="resetButton">重置</button>
                    <button type="button" class="button text" id="toggleCollapseButton">高级查询</button>
                    <!-- Changed text to '高级查询' -->
                </div>
            </form>
        </div>

        <style>
            .search-area.collapsed .search-form {
                grid-template-columns: repeat(4, 1fr);
                /* 4 columns when collapsed (default state) */
            }

            .search-area.collapsed .search-form-item {
                display: none;
                /* Hide all items by default when collapsed */
                grid-column: span 1;
                /* Ensure they take 1 column if shown */
            }

            .search-area.collapsed .search-form-item:nth-child(1),
            .search-area.collapsed .search-form-item:nth-child(2),
            .search-area.collapsed .search-form-item:nth-child(3),
            .search-area.collapsed .search-form-item.search-buttons-item {
                display: flex;
                /* Show first three and the buttons item */
            }

            .search-form-item.search-buttons-item {
                grid-column: span 1;
                /* Align with other 3-column items */
                justify-content: flex-end;
                /* Align buttons to the right */
                margin-left: auto;
                /* Push to the right */
                margin-bottom: 15px;
                /* Keep consistent margin */
            }
        </style>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
            <button class="button primary">新建</button>
        </div>

        <!-- 3. 列表区域 -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>计划编号</th>
                        <th>计划项目名称</th>
                        <th>采购类型</th>
                        <th>采购方式</th>
                        <th>审核状态</th>
                        <th>采购组织方式</th>
                        <th>代理机构</th>
                        <th>项目经办人</th>
                        <th>项目业主</th>
                        <th>招标时间</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    <!-- Table rows will be inserted here by JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- 分页组件 -->
        <div class="pagination-container">
            <span>总共 <span id="totalItems">0</span> 条</span>
            <select id="pageSizeSelect">
                <option value="10">10 条/页</option>
                <option value="20">20 条/页</option>
                <option value="50">50 条/页</option>
                <option value="100">100 条/页</option>
            </select>
            <div class="page-buttons">
                <button id="prevPageButton" disabled>上一页</button>
                <span id="currentPageSpan">1</span>
                <button id="nextPageButton">下一页</button>
            </div>
        </div>
    </div>
    </div>
    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const tabs = document.querySelectorAll('.tab-item');
            const searchForm = document.querySelector('.search-form');
            const queryButton = document.getElementById('queryButton');
            const resetButton = document.getElementById('resetButton');
            const toggleCollapseButton = document.getElementById('toggleCollapseButton');
            const tableBody = document.getElementById('tableBody');
            const pageSizeSelect = document.getElementById('pageSizeSelect');
            const prevPageButton = document.getElementById('prevPageButton');
            const nextPageButton = document.getElementById('nextPageButton');
            const currentPageSpan = document.getElementById('currentPageSpan');
            const totalItemsSpan = document.getElementById('totalItems');

            let activeTab = 'todo'; // Default to '待办'
            let isCollapsed = true; // Default to collapsed
            let currentPage = 1;
            let pageSize = parseInt(pageSizeSelect.value);

            // Initial state for the search area
            const searchArea = document.querySelector('.search-area');
            if (isCollapsed) {
                searchArea.classList.add('collapsed');
                toggleCollapseButton.textContent = '高级查询';
            } else {
                searchArea.classList.remove('collapsed');
                toggleCollapseButton.textContent = '收起';
            }

            const allTableData = [
                // Data for '待办' tab (待审核, 审核中, 审核未过)
                {
                    planNumber: 'CG—20230101—0001',
                    planProjectName: '某项目采购计划',
                    procurementType: '货物',
                    procurementMethod: '公告比选',
                    auditStatus: '待审核',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '张三',
                    projectOwner: '某业主单位',
                    biddingTime: '2023年7月',
                    createTime: '2023-01-01 09:00:00'
                },
                {
                    planNumber: 'CG—20230102—0002',
                    planProjectName: '另一项目采购计划',
                    procurementType: '服务',
                    procurementMethod: '竞争性磋商',
                    auditStatus: '审核中',
                    procurementOrganizationMethod: '委托招标',
                    agency: '某代理机构',
                    projectHandler: '李四',
                    projectOwner: '另一业主单位',
                    biddingTime: '2023年8月',
                    createTime: '2023-01-02 10:00:00'
                },
                {
                    planNumber: 'CG—20230104—0004',
                    planProjectName: '未通过审核项目',
                    procurementType: '施工',
                    procurementMethod: '邀请比选',
                    auditStatus: '审核未过',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '赵六',
                    projectOwner: '某公司',
                    biddingTime: '2023年10月',
                    createTime: '2023-01-04 12:00:00'
                },
                // Data for '已办' tab (已审核)
                {
                    planNumber: 'CG—20230103—0003',
                    planProjectName: '已审核项目',
                    procurementType: '施工',
                    procurementMethod: '竞争性磋商',
                    auditStatus: '审核通过',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '王五',
                    projectOwner: '某公司',
                    biddingTime: '2023年9月',
                    createTime: '2023-01-03 11:00:00'
                },
                {
                    planNumber: 'CG—20230105—0005',
                    planProjectName: '另一个已审核项目',
                    procurementType: '货物',
                    procurementMethod: '单一来源',
                    auditStatus: '审核通过',
                    procurementOrganizationMethod: '委托招标',
                    agency: '另一代理机构',
                    projectHandler: '钱七',
                    projectOwner: '另一公司',
                    biddingTime: '2023年11月',
                    createTime: '2023-01-05 13:00:00'
                }
            ];

            let currentTableData = []; // Data for the currently active tab

            const filterDataByTab = () => {
                if (activeTab === 'todo') {
                    currentTableData = allTableData.filter(item =>
                        item.auditStatus === '待审核' || item.auditStatus === '审核中' || item.auditStatus === '审核未过'
                    );
                } else if (activeTab === 'done') {
                    currentTableData = allTableData.filter(item => item.auditStatus === '审核通过');
                }
            };

            const renderTable = () => {
                tableBody.innerHTML = ''; // Clear existing rows
                filterDataByTab(); // Filter data based on active tab before pagination

                const start = (currentPage - 1) * pageSize;
                const end = start + pageSize;
                const paginatedData = currentTableData.slice(start, end);

                paginatedData.forEach(rowData => {
                    const row = document.createElement('tr');
                    let operationsHtml = '';
                    if (rowData.auditStatus === '待审核') {
                        operationsHtml = `
                            <button type="button" class="button text" data-action="edit" data-id="${rowData.planNumber}">编辑</button>
                            <button type="button" class="button text" data-action="delete" data-id="${rowData.planNumber}">删除</button>
                            <button type="button" class="button text" data-action="submit" data-id="${rowData.planNumber}">提交</button>
                        `;
                    } else if (rowData.auditStatus === '审核中') {
                        operationsHtml = `
                            <button type="button" class="button text" data-action="audit" data-id="${rowData.planNumber}">审核</button>
                        `;
                    } else if (rowData.auditStatus === '审核通过') {
                        operationsHtml = `<!-- 已审核无操作 -->`;
                    }

                    row.innerHTML = `
                        <td>${rowData.planNumber}</td>
                        <td>${rowData.planProjectName}</td>
                        <td>${rowData.procurementType}</td>
                        <td>${rowData.procurementMethod}</td>
                        <td>${rowData.auditStatus}</td>
                        <td>${rowData.procurementOrganizationMethod}</td>
                        <td>${rowData.agency}</td>
                        <td>${rowData.projectHandler}</td>
                        <td>${rowData.projectOwner}</td>
                        <td>${rowData.biddingTime}</td>
                        <td>${rowData.createTime}</td>
                        <td>${operationsHtml}</td>
                    `;
                    tableBody.appendChild(row);
                });

                totalItemsSpan.textContent = currentTableData.length;
                currentPageSpan.textContent = currentPage;
                prevPageButton.disabled = currentPage === 1;
                nextPageButton.disabled = currentPage * pageSize >= currentTableData.length;
            };

            const handleAction = (event) => {
                const button = event.target;
                if (button.tagName === 'BUTTON' && button.dataset.action) {
                    const action = button.dataset.action;
                    const id = button.dataset.id;
                    const rowData = allTableData.find(item => item.planNumber === id); // Find from all data
                    if (rowData) {
                        switch (action) {
                            case 'edit':
                                alert(`编辑: ${rowData.planProjectName} (ID: ${id})`);
                                // In a real app, navigate to add-edit page
                                break;
                            case 'delete':
                                if (confirm(`确定要删除 ${rowData.planProjectName} (ID: ${id}) 吗？`)) {
                                    // In a real app, send delete request and re-render
                                    alert(`删除: ${rowData.planProjectName} (ID: ${id})`);
                                }
                                break;
                            case 'submit':
                                if (confirm(`确定要提交 ${rowData.planProjectName} (ID: ${id}) 吗？`)) {
                                    // In a real app, send submit request and re-render
                                    alert(`提交: ${rowData.planProjectName} (ID: ${id})`);
                                }
                                break;
                            case 'audit':
                                alert(`审核: ${rowData.planProjectName} (ID: ${id})`);
                                // In a real app, navigate to detail-review page for audit
                                break;
                        }
                    }
                }
            };

            // Event Listeners
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    activeTab = tab.dataset.tab;
                    currentPage = 1; // Reset page on tab change
                    renderTable();
                });
            });

            queryButton.addEventListener('click', () => {
                const formData = new FormData(searchForm);
                const searchParams = {};
                for (let [key, value] of formData.entries()) {
                    searchParams[key] = value;
                }
                console.log('查询条件:', searchParams);

                // Simple filtering logic for demo
                currentTableData = allTableData.filter(item => {
                    let match = true;
                    if (searchParams.planProjectName && !item.planProjectName.includes(searchParams.planProjectName)) {
                        match = false;
                    }
                    if (searchParams.auditStatus && item.auditStatus !== searchParams.auditStatus) {
                        match = false;
                    }
                    // Add more filtering logic for other fields as needed
                    return match;
                });

                currentPage = 1;
                renderTable();
            });

            resetButton.addEventListener('click', () => {
                searchForm.reset();
                activeTab = 'todo'; // Reset active tab to default
                tabs.forEach(t => t.classList.remove('active'));
                document.querySelector('.tab-item[data-tab="todo"]').classList.add('active');

                currentTableData = [...allTableData]; // Reset filtered data to all
                currentPage = 1;
                renderTable();
            });

            toggleCollapseButton.addEventListener('click', () => {
                const searchArea = document.querySelector('.search-area');
                isCollapsed = !isCollapsed;
                if (isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询'; // Changed text to '高级查询'
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
            });

            tableBody.addEventListener('click', handleAction);

            pageSizeSelect.addEventListener('change', (event) => {
                pageSize = parseInt(event.target.value);
                currentPage = 1; // Reset to first page when page size changes
                renderTable();
            });

            prevPageButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderTable();
                }
            });

            nextPageButton.addEventListener('click', () => {
                const totalPages = Math.ceil(currentTableData.length / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderTable();
                }
            });

            // Initial render
            renderTable();
        });
    </script>
</body>

</html>