<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>采购计划管理 - 详情/审核 - Funi管理系统</title>
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/detail.css" />
</head>

<body>
    <div class="funi-tabs">
        <div class="funi-tabs-header">
            <button class="funi-tab-button funi-tab-button-active" data-tab="basicInfo">基本信息</button>
            <button class="funi-tab-button" data-tab="processRecords">流程记录</button>
        </div>
        <div class="funi-tab-content">
            <div id="basicInfo" class="funi-tab-pane funi-tab-pane-active">
                <div class="funi-detail-group">
                    <div class="funi-group-title">招标信息</div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">计划编号:</span>
                        <span class="funi-detail-value">CG—YYYYMMDD—4位序号</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">计划项目名称:</span>
                        <span class="funi-detail-value">示例计划项目名称</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">审核状态:</span>
                        <span class="funi-detail-value">待审核</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">采购类型:</span>
                        <span class="funi-detail-value">货物</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">采购方式:</span>
                        <span class="funi-detail-value">公告比选</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">招标类别:</span>
                        <span class="funi-detail-value">工程类(施工)</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">采购预算金额:</span>
                        <span class="funi-detail-value">1000.00 万元</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">资金来源:</span>
                        <span class="funi-detail-value">自有资金</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">招标时间:</span>
                        <span class="funi-detail-value">2023年7月</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">采购组织方式:</span>
                        <span class="funi-detail-value">委托招标</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">代理机构:</span>
                        <span class="funi-detail-value">某代理机构</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">年采购计划:</span>
                        <span class="funi-detail-value">900.00 万元</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">项目经办人:</span>
                        <span class="funi-detail-value">张三</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">立项决策日期:</span>
                        <span class="funi-detail-value">2023-01-01</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">驳回原因:</span>
                        <span class="funi-detail-value">（若有）</span>
                    </div>
                </div>

                <div class="funi-detail-group">
                    <div class="funi-group-title">项目信息</div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">项目类型:</span>
                        <span class="funi-detail-value">依法必须招标项目</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">项目业主:</span>
                        <span class="funi-detail-value">某项目业主</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">所属二级公司单位:</span>
                        <span class="funi-detail-value">某二级公司</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">备注:</span>
                        <span class="funi-detail-value">这是备注信息。</span>
                    </div>
                    <div class="funi-detail-item">
                        <span class="funi-detail-label">立项决策文件:</span>
                        <div class="funi-file-list">
                            <a href="#" class="funi-file-link">文件1.pdf</a>
                            <a href="#" class="funi-file-link">文件2.docx</a>
                        </div>
                    </div>
                </div>
            </div>
            <div id="processRecords" class="funi-tab-pane" style="display: none;">
                <div class="funi-timeline">
                    <div class="funi-timeline-item">
                        <div class="funi-timeline-dot"></div>
                        <div class="funi-timeline-content">
                            <div class="funi-timeline-title">操作人: 张三</div>
                            <div class="funi-timeline-description">操作节点: 提交</div>
                            <div class="funi-timeline-time">操作时间: 2023-01-01 10:00:00</div>
                        </div>
                    </div>
                    <div class="funi-timeline-item">
                        <div class="funi-timeline-dot"></div>
                        <div class="funi-timeline-content">
                            <div class="funi-timeline-title">操作人: 李四</div>
                            <div class="funi-timeline-description">操作节点: 审核通过</div>
                            <div class="funi-timeline-time">操作时间: 2023-01-01 11:00:00</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Tab switching logic
            const tabButtons = document.querySelectorAll('.funi-tabs-header .funi-tab-button');
            const tabPanes = document.querySelectorAll('.funi-tab-content .funi-tab-pane');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.dataset.tab;

                    tabButtons.forEach(btn => btn.classList.remove('funi-tab-button-active'));
                    button.classList.add('funi-tab-button-active');

                    tabPanes.forEach(pane => {
                        if (pane.id === targetTab) {
                            pane.style.display = 'block';
                            pane.classList.add('funi-tab-pane-active');
                        } else {
                            pane.style.display = 'none';
                            pane.classList.remove('funi-tab-pane-active');
                        }
                    });
                });
            });
        });
    </script>
</body>

</html>