<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>采购计划管理 - 新增/编辑 - Funi管理系统</title>
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/form.css" />
</head>

<body>
    <form class="funi-form">
        <div class="funi-group-title">招标信息</div>
        <div class="form-item-row">
            <label for="planNumber">计划编号:</label>
            <input type="text" id="planNumber" name="planNumber" value="自动生成" readonly />
        </div>
        <div class="form-item-row">
            <label for="procurementType">采购类型:</label>
            <select id="procurementType" name="procurementType">
                <option value="货物">货物</option>
                <option value="施工" selected>施工</option>
                <option value="服务">服务</option>
                <option value="其他">其他</option>
            </select>
        </div>
        <div class="form-item-row">
            <label for="biddingCategory">招标类别:</label>
            <input type="text" id="biddingCategory" name="biddingCategory" placeholder="根据采购类型级联变化" />
        </div>
        <div class="form-item-row">
            <label for="procurementMethod">采购方式:</label>
            <select id="procurementMethod" name="procurementMethod">
                <option value="公告比选" selected>公告比选</option>
                <option value="邀请比选">邀请比选</option>
                <option value="竞争性磋商">竞争性磋商</option>
                <option value="竞争性谈判">竞争性谈判</option>
                <option value="询价择优">询价择优</option>
                <option value="单一来源">单一来源</option>
            </select>
        </div>
        <div class="form-item-row">
            <label for="procurementBudgetAmount">采购预算金额（万元）:</label>
            <input type="number" id="procurementBudgetAmount" name="procurementBudgetAmount" value="0.00" step="0.01" />
        </div>
        <div class="form-item-row">
            <label for="fundSource">资金来源:</label>
            <select id="fundSource" name="fundSource">
                <option value="自有资金" selected>自有资金</option>
                <option value="政府投资">政府投资</option>
                <option value="其它社会资本">其它社会资本</option>
            </select>
        </div>
        <div class="form-item-row">
            <label for="biddingTime">招标时间:</label>
            <input type="text" id="biddingTime" name="biddingTime" placeholder="当前年+3季度" maxlength="50" />
        </div>
        <div class="form-item-row">
            <label for="procurementOrganizationMethod">采购组织方式:</label>
            <select id="procurementOrganizationMethod" name="procurementOrganizationMethod">
                <option value="自主招标">自主招标</option>
                <option value="委托招标" selected>委托招标</option>
            </select>
        </div>
        <div class="form-item-row" id="agencyRow" style="display: block;">
            <label for="agency">代理机构:</label>
            <input type="text" id="agency" name="agency" placeholder="默认第一个组织" />
        </div>
        <div class="form-item-row">
            <label for="annualProcurementPlan">年采购计划（万元）:</label>
            <input type="number" id="annualProcurementPlan" name="annualProcurementPlan" value="0.00" step="0.01" />
            <span class="hint">不能大于采购预算金额</span>
        </div>
        <div class="form-item-row">
            <label for="projectHandler">项目经办人:</label>
            <input type="text" id="projectHandler" name="projectHandler" value="当前创建人" />
        </div>
        <div class="form-item-row">
            <label for="projectApprovalDate">立项决策日期:</label>
            <input type="date" id="projectApprovalDate" name="projectApprovalDate" />
        </div>

        <div class="funi-group-title">项目信息</div>
        <div class="form-item-row">
            <label for="projectType">项目类型:</label>
            <select id="projectType" name="projectType">
                <option value="依法必须招标项目" selected>依法必须招标项目</option>
                <option value="非法定招标采购项目">非法定招标采购项目</option>
            </select>
        </div>
        <div class="form-item-row">
            <label for="projectOwner">项目业主:</label>
            <input type="text" id="projectOwner" name="projectOwner" maxlength="50" />
        </div>
        <div class="form-item-row">
            <label for="projectBasicInfo">项目基本情况:</label>
            <textarea id="projectBasicInfo" name="projectBasicInfo" maxlength="255"></textarea>
        </div>
        <div class="form-item-row">
            <label for="secondaryCompany">所属二级公司单位:</label>
            <input type="text" id="secondaryCompany" name="secondaryCompany" maxlength="255" />
        </div>
        <div class="form-item-row">
            <label for="remark">备注:</label>
            <input type="text" id="remark" name="remark" maxlength="255" />
        </div>
        <div class="form-item-row">
            <label for="projectApprovalFiles">立项决策文件:</label>
            <input type="file" id="projectApprovalFiles" name="projectApprovalFiles" multiple />
        </div>

        <div class="funi-form-actions">
            <button type="submit" class="button primary">保存</button>
            <button type="button" class="button">取消</button>
        </div>
    </form>
    </div>
    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const procurementOrganizationMethodSelect = document.getElementById('procurementOrganizationMethod');
            const agencyRow = document.getElementById('agencyRow');

            if (procurementOrganizationMethodSelect && agencyRow) {
                procurementOrganizationMethodSelect.addEventListener('change', (event) => {
                    if (event.target.value === '委托招标') {
                        agencyRow.style.display = 'flex'; // Use flex to maintain layout
                    } else {
                        agencyRow.style.display = 'none';
                    }
                });
                // Set initial state
                if (procurementOrganizationMethodSelect.value === '委托招标') {
                    agencyRow.style.display = 'flex';
                } else {
                    agencyRow.style.display = 'none';
                }
            }
        });
    </script>
</body>

</html>