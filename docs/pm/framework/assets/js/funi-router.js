document.addEventListener('DOMContentLoaded', () => {
  const iframe = document.getElementById('funi-content-iframe');

  function highlightMenuItem(currentPath) {
    // Remove active class from all menu items
    document.querySelectorAll('.funi-menu-item').forEach(item => {
      item.classList.remove('funi-menu-item-active');
    });

    // Collapse all menu groups initially
    document.querySelectorAll('.funi-menu-group').forEach(group => {
      group.classList.add('collapsed');
      const menuList = group.querySelector('.funi-menu-list');
      if (menuList) {
        menuList.style.height = '0px';
      }
    });

    // Find the active menu item and its parent groups
    const activeLink = document.querySelector(`.funi-menu-link[href="#${currentPath}"]`);
    if (activeLink) {
      const activeItem = activeLink.closest('.funi-menu-item');
      if (activeItem) {
        activeItem.classList.add('funi-menu-item-active');

        // Expand parent menu groups
        let parentGroup = activeItem.closest('.funi-menu-group');
        while (parentGroup) {
          parentGroup.classList.remove('collapsed');
          const menuList = parentGroup.querySelector('.funi-menu-list');
          if (menuList) {
            menuList.style.height = 'auto'; // Allow natural height
          }
          parentGroup = parentGroup.parentElement.closest('.funi-menu-group');
        }
      }
    }
  }

  function loadContent() {
    let path = window.location.hash.substring(1); // Remove '#'

    if (!path) {
      iframe.src = 'pages/default.html'; // Load default.html if no hash
      highlightMenuItem(''); // No menu item selected
      return;
    }

    // Construct the full path to the HTML file in the 'pages' directory
    const pagePath = `pages${path}/list.html`; // Assuming 'list.html' as default for now

    iframe.src = pagePath;
    highlightMenuItem(path); // Highlight menu item based on current path
  }

  // Function to synchronize theme
  function syncThemeWithIframe() {
    const isDarkTheme = document.body.classList.contains('dark-theme');
    if (iframe.contentDocument && iframe.contentDocument.body) {
      if (isDarkTheme) {
        iframe.contentDocument.body.classList.add('dark-theme');
      } else {
        iframe.contentDocument.body.classList.remove('dark-theme');
      }
    }
  }

  // Load content on initial page load
  loadContent();

  // Listen for hash changes
  window.addEventListener('hashchange', loadContent);

  // Listen for iframe load to apply theme
  iframe.onload = () => {
    syncThemeWithIframe();
  };

  // Observe changes to the body's classList to sync theme dynamically
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        syncThemeWithIframe();
      }
    });
  });

  observer.observe(document.body, { attributes: true });

  // Handle menu clicks to update hash
  document.querySelectorAll('.funi-menu-link').forEach(link => {
    link.addEventListener('click', event => {
      event.preventDefault();
      const href = link.getAttribute('href');
      if (href && href.startsWith('#')) {
        window.location.hash = href.substring(1);
      }
    });
  });
});
