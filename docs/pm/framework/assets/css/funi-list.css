body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    margin: 0;
    background-color: #f0f2f5;
}

.container {
    background-color: var(--funi-background-color-light);
    padding: 0;
    /* Removed padding as per feedback */
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Tabs */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--funi-border-color-light);
    margin-bottom: 0;
}

.tab-item {
    padding: 10px 15px;
    cursor: pointer;
    font-size: 14px;
    color: var(--funi-text-color-secondary);
    position: relative;
    top: 1px;
    transition: color 0.3s;
}

.tab-item.active {
    color: var(--funi-primary-color);
    border-bottom: 2px solid var(--funi-primary-color);
}

.tab-item:hover {
    color: var(--funi-primary-color);
}

/* Search Area */
.search-area {
    margin-top: 0;
    padding: 20px;
    background-color: var(--funi-background-color-light);
    /* Changed to white background */
    border-radius: 8px;
    margin-bottom: 10px;
    overflow: hidden;
    /* For smooth collapse transition */
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.search-area.collapsed {
    padding-bottom: 20px;
}

.search-area.collapsed .search-form-item {
    display: none;
    /* Hide all items by default when collapsed */
}

.search-area.collapsed .search-form {
    grid-template-columns: repeat(4, 1fr);
    /* 4 columns when collapsed (default state) */
}

.search-area.collapsed .search-form-item {
    display: none;
    /* Hide all items by default when collapsed */
    grid-column: span 1;
    /* Ensure they take 1 column if shown */
}

.search-area.collapsed .search-form-item:nth-child(1),
.search-area.collapsed .search-form-item:nth-child(2),
.search-area.collapsed .search-form-item:nth-child(3),
.search-area.collapsed .search-form-item.search-buttons-item {
    display: flex;
    /* Show first three and the buttons item */
}

/* Ensure the search-buttons-item is always at the end of the visible row */
.search-area.collapsed .search-form-item.search-buttons-item {
    margin-left: auto;
    /* Push to the right */
}

.search-form {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* Default 3 columns when expanded */
    gap: 10px;
}

.search-form-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    grid-column: span 1;
    /* Default to 1 column */
}

.search-form-item label {
    flex-shrink: 0;
    width: 80px;
    /* Adjust label width as needed */
    color: var(--funi-text-color-regular);
    font-size: 14px;
    text-align: right;
    margin-right: 10px;
}

.search-form-item input,
.search-form-item select {
    flex-grow: 1;
    padding: 8px 12px;
    border: 1px solid var(--funi-border-color-base);
    border-radius: 4px;
    font-size: 14px;
    color: var(--funi-text-color-regular);
    box-sizing: border-box;
}

.search-form-item input:focus,
.search-form-item select:focus {
    outline: none;
    border-color: var(--funi-primary-color);
}



.date-range-picker {
    display: flex;
    align-items: center;
    gap: 5px;
}

.date-range-picker input {
    width: 120px;
    /* Adjust as needed */
}

/* New style for search buttons item to align with form items */
.search-form-item.search-buttons-item {
    grid-column: span 1;
    /* Align with other 3-column items */
    justify-content: flex-end;
    /* Align buttons to the right */
    margin-left: auto;
    /* Push to the right */
    margin-bottom: 15px;
    /* Keep consistent margin */
}

.search-buttons-item .button {
    margin-left: 10px;
    /* Space between buttons */
}

/* No longer need .search-buttons as a separate block, its styles are now integrated into .search-form-item.search-buttons-item */
/* .search-buttons {
    width: 100%;
    text-align: right;
    margin-top: 20px;
} */

/* Buttons */
.button {
    padding: 8px 15px;
    border: 1px solid var(--funi-border-color-base);
    border-radius: 4px;
    background-color: var(--funi-background-color-light);
    color: var(--funi-text-color-regular);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

.button.primary {
    background-color: var(--funi-primary-color);
    border-color: var(--funi-primary-color);
    color: #fff;
}

.button.text {
    border: none;
    background: none;
    color: var(--funi-primary-color);
    padding: 0 5px;
}

.button:hover {
    opacity: 0.9;
}

.action-buttons {
    margin-bottom: 0.8em;
    display: flex;
    gap: 10px;
}

/* Table */
.table-container {
    overflow-x: auto;
    position: relative;
    height: 500px;
    /* Fixed height for table body */
    border: 1px solid var(--funi-border-color-lighter);
    border-radius: 4px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1200px;
    /* Ensure table doesn't shrink too much */
}

.data-table th,
.data-table td {
    padding: 12px 10px;
    border-bottom: 1px solid var(--funi-border-color-lighter);
    text-align: left;
    font-size: 14px;
    color: var(--funi-text-color-regular);
    white-space: nowrap;
    /* Prevent text wrapping in cells */
}

.data-table th {
    background-color: var(--funi-background-color-base);
    color: var(--funi-text-color-secondary);
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 2;
}

.data-table tbody tr:hover {
    background-color: var(--funi-background-color-base);
}

.data-table td .button.text {
    margin-right: 5px;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
    padding: 10px 0;
    border-top: 1px solid var(--funi-border-color-lighter);
    background-color: var(--funi-background-color-light);
    position: sticky;
    bottom: 0;
    z-index: 100;
}

.pagination-container span {
    font-size: 14px;
    color: var(--funi-text-color-regular);
    margin-right: 10px;
}

.pagination-container select {
    padding: 6px 8px;
    border: 1px solid var(--funi-border-color-base);
    border-radius: 4px;
    margin-right: 10px;
}

.pagination-container .page-buttons button {
    min-width: 30px;
    height: 30px;
    border: 1px solid var(--funi-border-color-base);
    background-color: var(--funi-background-color-light);
    color: var(--funi-text-color-regular);
    border-radius: 4px;
    cursor: pointer;
    margin: 0 2px;
    font-size: 14px;
}

.pagination-container .page-buttons button.active {
    background-color: var(--funi-primary-color);
    border-color: var(--funi-primary-color);
    color: #fff;
}

.pagination-container .page-buttons button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}