/* General form styling */
.funi-form {
    background-color: var(--funi-background-color-light);
    padding: 0;
    /* Removed padding as per feedback */
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.funi-group-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--funi-text-color-primary);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--funi-border-color-lighter);
    margin-top: 20px;
}

.funi-group-title:first-child {
    margin-top: 0;
}

.form-item-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.form-item-row label {
    flex-shrink: 0;
    width: 120px;
    /* Adjust label width as needed */
    color: var(--funi-text-color-regular);
    font-size: 14px;
    text-align: right;
    margin-right: 10px;
}

.form-item-row input[type="text"],
.form-item-row input[type="number"],
.form-item-row input[type="date"],
.form-item-row select,
.form-item-row textarea {
    flex-grow: 1;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    color: var(--funi-text-color-regular);
    box-sizing: border-box;
    max-width: 400px;
    /* Limit input width for better readability */
}

.form-item-row input[type="file"] {
    padding: 8px 0;
}

.form-item-row input:focus,
.form-item-row select:focus,
.form-item-row textarea:focus {
    outline: none;
    border-color: var(--funi-primary-color);
}

.form-item-row textarea {
    min-height: 80px;
    resize: vertical;
}

.form-item-row .hint {
    font-size: 12px;
    color: var(--funi-text-color-secondary);
    margin-left: 10px;
}

.funi-form-actions {
    text-align: right;
    padding-top: 20px;
    border-top: 1px solid var(--funi-border-color-lighter);
    margin-top: 20px;
}

/* Buttons - Reusing .button styles from list.css or funi-components.css */
.funi-form-actions .button {
    margin-left: 10px;
}