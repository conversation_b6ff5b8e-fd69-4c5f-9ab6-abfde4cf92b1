/* funi-framework.css */
/* Basic Reset & Box Sizing */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  line-height: 1.6;
  color: #333;
  background-color: #f0f2f5;
  font-size: 14px;
}

/* Variables for theming */
:root {
  --funi-primary-color: #409EFF;
  /* Default primary color (Blue) */
  --funi-primary-light-color: #79bbff;
  --funi-primary-dark-color: #337ecc;
  --funi-success-color: #67C23A;
  --funi-warning-color: #E6A23C;
  --funi-danger-color: #F56C6C;
  --funi-info-color: #909399;

  --funi-text-color-primary: #303133;
  --funi-text-color-regular: #606266;
  --funi-text-color-secondary: #909399;
  --funi-text-color-placeholder: #C0C4CC;

  --funi-border-color-base: #DCDFE6;
  --funi-border-color-light: #E4E7ED;
  --funi-border-color-lighter: #EBEEF5;
  --funi-border-color-extra-light: #F2F6FC;

  --funi-background-color-base: #F5F7FA;
  --funi-background-color-light: #FAFAFA;
}

/* Dark Theme */
body.dark-theme {
  --funi-primary-color: #409EFF;
  /* Keep primary color consistent or adjust for dark mode */
  --funi-primary-light-color: #79bbff;
  --funi-primary-dark-color: #337ecc;
  --funi-success-color: #67C23A;
  --funi-warning-color: #E6A23C;
  --funi-danger-color: #F56C6C;
  --funi-info-color: #909399;

  --funi-text-color-primary: #E5EAF3;
  --funi-text-color-regular: #CFD3DC;
  --funi-text-color-secondary: #A3A6AD;
  --funi-text-color-placeholder: #8D9095;

  --funi-border-color-base: #4C4D4F;
  --funi-border-color-light: #545454;
  --funi-border-color-lighter: #5C5C5C;
  --funi-border-color-extra-light: #646464;

  --funi-background-color-base: #141414;
  --funi-background-color-light: #1A1A1A;
  background-color: #141414;
  color: var(--funi-text-color-regular);
}


/* Layout */
.funi-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.funi-header {
  background-color: #fff;
  border-bottom: 1px solid var(--funi-border-color-lighter);
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

body.dark-theme .funi-header {
  background-color: #1A1A1A;
  border-bottom-color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.funi-header-left,
.funi-header-right {
  display: flex;
  align-items: center;
}

.funi-logo {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.funi-logo img {
  height: 28px;
  margin-right: 8px;
}

.funi-logo-text {
  color: var(--funi-text-color-primary);
}

body.dark-theme .funi-logo-text {
  color: var(--funi-text-color-primary);
}

.funi-main {
  display: flex;
  flex: 1;
  background-color: var(--funi-background-color-base);
}

body.dark-theme .funi-main {
  background-color: #141414;
}

.funi-sidebar {
  width: 220px;
  background-color: #fff;
  border-right: 1px solid var(--funi-border-color-lighter);
  padding-top: 20px;
  transition: width 0.3s ease;
  flex-shrink: 0;
  position: relative;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.02);
}

body.dark-theme .funi-sidebar {
  background-color: #1A1A1A;
  border-right-color: #333;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.funi-sidebar.collapsed {
  width: 64px;
}

.funi-content {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.funi-content-wrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* For tabs */
}

body.dark-theme .funi-content-wrapper {
  background-color: #1A1A1A;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Sidebar Toggle Button */
.funi-sidebar-toggle {
  position: absolute;
  top: 10px;
  right: -16px;
  /* Half of button width to center on border */
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid var(--funi-border-color-base);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  z-index: 10;
}

body.dark-theme .funi-sidebar-toggle {
  background-color: #1A1A1A;
  border-color: #333;
  color: var(--funi-text-color-regular);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.funi-sidebar.collapsed .funi-sidebar-toggle {
  transform: rotate(180deg);
}

.funi-sidebar-toggle-icon {
  width: 16px;
  height: 16px;
  color: var(--funi-text-color-secondary);
}

body.dark-theme .funi-sidebar-toggle-icon {
  color: var(--funi-text-color-regular);
}



.funi-menu-group {
  margin-bottom: 10px;
}

.funi-menu-group-title {
  font-size: 14px;
  /* Changed from 12px to 14px */
  /* font-weight: bold; */
  /* Added bold for consistency */
  color: var(--funi-text-color-regular);
  /* Changed to regular for consistency with menu-text */
  padding: 8px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.funi-menu-group-title:hover {
  background-color: var(--funi-background-color-base);
}

body.dark-theme .funi-menu-group-title:hover {
  background-color: #2A2A2A;
}

.funi-sidebar.collapsed .funi-menu-group-title span {
  display: none;
}

.funi-menu-group-toggle {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.funi-menu-group.collapsed .funi-menu-group-toggle {
  transform: rotate(-90deg);
}

.funi-sidebar.collapsed .funi-menu-group-toggle {
  display: none;
  /* Hide toggle in collapsed state */
}

.funi-menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow: hidden;
  transition: height 0.3s ease;
}

.funi-menu-group.collapsed .funi-menu-list {
  height: 0 !important;
  /* Set by JS, but ensure it's hidden */
}

.funi-menu-item {
  margin-bottom: 4px;
}

.funi-menu-link {
  display: flex;
  align-items: center;
  padding: 10px;
  /* border-radius: 4px; */
  color: var(--funi-text-color-regular);
  text-decoration: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.funi-menu-link:hover {
  background-color: var(--funi-background-color-base);
  color: var(--funi-primary-color);
}

body.dark-theme .funi-menu-link:hover {
  background-color: #2A2A2A;
  color: var(--funi-primary-color);
}

.funi-menu-item-active .funi-menu-link {
  background-color: var(--funi-primary-color);
  color: #fff;
}

.funi-menu-item-active .funi-menu-link:hover {
  background-color: var(--funi-primary-dark-color);
  color: #fff;
}

.funi-menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.funi-sidebar.collapsed .funi-menu-icon {
  margin-right: 0;
}

.funi-sidebar.collapsed .funi-menu-text {
  display: none;
}

/* Header Right */
.funi-theme-switcher {
  margin-right: 20px;
}

.funi-theme-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--funi-text-color-regular);
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.funi-theme-btn:hover {
  background-color: var(--funi-background-color-base);
}

body.dark-theme .funi-theme-btn:hover {
  background-color: #2A2A2A;
}

.funi-user-menu {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.funi-user-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
}

.funi-user-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;

}

.funi-user-name {
  font-weight: bold;
  color: var(--funi-text-color-primary);
}

body.dark-theme .funi-user-name {
  color: var(--funi-text-color-primary);
}

/* Page Header */
.funi-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--funi-border-color-lighter);
  margin-bottom: 20px;
}

body.dark-theme .funi-page-header {
  border-bottom-color: #333;
}

.funi-page-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--funi-text-color-primary);
}

body.dark-theme .funi-page-title {
  color: var(--funi-text-color-primary);
}

.funi-page-actions {
  display: flex;
  gap: 10px;
}

/* Buttons */
.funi-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
  border: none;
  /* Default to no border */
}

.funi-btn-primary {
  background-color: var(--funi-primary-color);
  color: #fff;
}

.funi-btn-primary:hover {
  background-color: var(--funi-primary-dark-color);
}

.funi-btn-default {
  background-color: #fff;
  color: var(--funi-text-color-regular);
  border: 1px solid var(--funi-border-color-base);
}

.funi-btn-default:hover {
  border-color: var(--funi-primary-color);
  color: var(--funi-primary-color);
}

body.dark-theme .funi-btn-default {
  background-color: #2A2A2A;
  color: var(--funi-text-color-regular);
  border-color: #4C4D4F;
}

body.dark-theme .funi-btn-default:hover {
  border-color: var(--funi-primary-color);
  color: var(--funi-primary-color);
}

.funi-btn-danger {
  background-color: var(--funi-danger-color);
  color: #fff;
}

.funi-btn-danger:hover {
  background-color: #CC4C4C;
}

.funi-btn-text {
  background: none;
  border: none;
  color: var(--funi-primary-color);
  padding: 0;
}

.funi-btn-text:hover {
  text-decoration: underline;
}

.funi-btn-text.funi-btn-danger {
  color: var(--funi-danger-color);
}

.funi-btn-text.funi-btn-danger:hover {
  color: #CC4C4C;
}

.funi-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.funi-btn .funi-icon {
  margin-right: 5px;
}

.funi-btn:not(.funi-btn-text) .funi-icon {
  fill: currentColor;
  /* Ensure icons in colored buttons are white */
}

/* Search Box */
.funi-search-box {
  padding: 20px;
  background-color: #fff;
  border-bottom: 1px solid var(--funi-border-color-lighter);
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

body.dark-theme .funi-search-box {
  background-color: #1A1A1A;
  border-bottom-color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.funi-search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-end;
}

.funi-search-form-horizontal .funi-form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  flex: 1;
}

.funi-form-item {
  display: flex;
  flex-direction: column;
  min-width: 200px;
  /* Adjust as needed */
}

.funi-form-label {
  font-size: 14px;
  color: var(--funi-text-color-regular);
  margin-bottom: 8px;
}

.funi-form-input,
.funi-form-select,
.funi-form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--funi-border-color-base);
  border-radius: 4px;
  font-size: 14px;
  color: var(--funi-text-color-primary);
  background-color: #fff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.funi-form-input:focus,
.funi-form-select:focus,
.funi-form-textarea:focus {
  border-color: var(--funi-primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

body.dark-theme .funi-form-input,
body.dark-theme .funi-form-select,
body.dark-theme .funi-form-textarea {
  background-color: #2A2A2A;
  border-color: #4C4D4F;
  color: var(--funi-text-color-primary);
}

body.dark-theme .funi-form-input::placeholder,
body.dark-theme .funi-form-select::placeholder,
body.dark-theme .funi-form-textarea::placeholder {
  color: var(--funi-text-color-placeholder);
}

.funi-form-actions {
  display: flex;
  gap: 10px;
}

/* Table */
.funi-table-container {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  /* For scrollable tables */
  display: flex;
  flex-direction: column;
}

body.dark-theme .funi-table-container {
  background-color: #1A1A1A;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.funi-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  color: var(--funi-text-color-regular);
}

.funi-table th,
.funi-table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--funi-border-color-lighter);
  text-align: left;
}

body.dark-theme .funi-table th,
body.dark-theme .funi-table td {
  border-bottom-color: #333;
}

.funi-table th {
  background-color: var(--funi-background-color-base);
  font-weight: bold;
  color: var(--funi-text-color-primary);
}

body.dark-theme .funi-table th {
  background-color: #2A2A2A;
  color: var(--funi-text-color-primary);
}

.funi-table tbody tr:hover {
  background-color: var(--funi-background-color-base);
}

body.dark-theme .funi-table tbody tr:hover {
  background-color: #2A2A2A;
}

/* Pagination */
.funi-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-top: 1px solid var(--funi-border-color-lighter);
  background-color: #fff;
}

body.dark-theme .funi-pagination {
  background-color: #1A1A1A;
  border-top-color: #333;
}

.funi-pagination-info {
  color: var(--funi-text-color-secondary);
}

.funi-pagination-controls {
  display: flex;
  gap: 5px;
}

.funi-pagination-item {
  background-color: #fff;
  border: 1px solid var(--funi-border-color-base);
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--funi-text-color-regular);
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.funi-pagination-item:hover:not([disabled]):not(.funi-pagination-item-active) {
  border-color: var(--funi-primary-color);
  color: var(--funi-primary-color);
}

.funi-pagination-item-active {
  background-color: var(--funi-primary-color);
  border-color: var(--funi-primary-color);
  color: #fff;
}

.funi-pagination-item-active:hover {
  background-color: var(--funi-primary-dark-color);
  border-color: var(--funi-primary-dark-color);
}

.funi-pagination-item[disabled] {
  cursor: not-allowed;
  opacity: 0.6;
}

body.dark-theme .funi-pagination-item {
  background-color: #2A2A2A;
  border-color: #4C4D4F;
  color: var(--funi-text-color-regular);
}

body.dark-theme .funi-pagination-item:hover:not([disabled]):not(.funi-pagination-item-active) {
  border-color: var(--funi-primary-color);
  color: var(--funi-primary-color);
}

/* Status Tags */
.funi-status-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
}

.funi-status-success {
  background-color: var(--funi-success-color);
}

.funi-status-warning {
  background-color: var(--funi-warning-color);
}

.funi-status-danger {
  background-color: var(--funi-danger-color);
}

.funi-status-info {
  background-color: var(--funi-info-color);
}

/* Detail Page */
.funi-detail-page,
.funi-form-page {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

body.dark-theme .funi-detail-page,
body.dark-theme .funi-form-page {
  background-color: #1A1A1A;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.funi-detail-header,
.funi-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--funi-border-color-lighter);
}

body.dark-theme .funi-detail-header,
body.dark-theme .funi-form-header {
  border-bottom-color: #333;
}

.funi-detail-title-area,
.funi-form-title-area {
  display: flex;
  align-items: center;
}

.funi-back-btn {
  background: none;
  border: none;
  color: var(--funi-primary-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-right: 10px;
}

.funi-back-btn:hover {
  text-decoration: underline;
}

.funi-detail-title,
.funi-form-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--funi-text-color-primary);
}

body.dark-theme .funi-detail-title,
body.dark-theme .funi-form-title {
  color: var(--funi-text-color-primary);
}

.funi-detail-actions {
  display: flex;
  gap: 10px;
}

.funi-detail-content,
.funi-form-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.funi-info-group,
.funi-form-section {
  margin-bottom: 20px;
  border: 1px solid var(--funi-border-color-lighter);
  border-radius: 8px;
  padding: 20px;
}

body.dark-theme .funi-info-group,
body.dark-theme .funi-form-section {
  border-color: #333;
}

.funi-group-title,
.funi-section-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--funi-text-color-primary);
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--funi-border-color-lighter);
}

body.dark-theme .funi-group-title,
body.dark-theme .funi-section-title {
  border-bottom-color: #333;
  color: var(--funi-text-color-primary);
}

.funi-info-grid,
.funi-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px 20px;
}

.funi-info-item {
  display: flex;
  flex-direction: column;
}

.funi-info-label {
  font-size: 14px;
  color: var(--funi-text-color-secondary);
  margin-bottom: 5px;
}

.funi-info-value {
  font-size: 14px;
  color: var(--funi-text-color-primary);
  word-break: break-word;
}

body.dark-theme .funi-info-value {
  color: var(--funi-text-color-primary);
}

/* Form Page Specific */
.funi-form-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--funi-border-color-lighter);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

body.dark-theme .funi-form-footer {
  border-top-color: #333;
}

/* Tabs */
.funi-tabs {
  background-color: #fff;
  border-bottom: 1px solid var(--funi-border-color-lighter);
  padding: 0 16px;
  /* 16px padding as per spec */
  flex-shrink: 0;
}

body.dark-theme .funi-tabs {
  background-color: #1A1A1A;
  border-bottom-color: #333;
}

.funi-tab-nav {
  display: flex;
  align-items: flex-end;
  height: 40px;
  /* Standard tab height */
}

.funi-tab-item {
  padding: 8px 15px;
  cursor: pointer;
  color: var(--funi-text-color-regular);
  font-size: 14px;
  transition: color 0.2s ease, background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  border-radius: 4px 4px 0 0;
  margin-right: 5px;
  /* Space between tabs */
}

.funi-tab-item:hover {
  color: var(--funi-primary-color);
  background-color: var(--funi-background-color-base);
}

body.dark-theme .funi-tab-item:hover {
  background-color: #2A2A2A;
}

.funi-tab-item.active {
  color: var(--funi-primary-color);
  background-color: var(--funi-background-color-base);
  /* Active tab has a background */
  font-weight: bold;
}

body.dark-theme .funi-tab-item.active {
  background-color: #2A2A2A;
}

.funi-tab-close {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--funi-text-color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.funi-tab-close:hover {
  background-color: var(--funi-border-color-lighter);
}

body.dark-theme .funi-tab-close:hover {
  background-color: #3A3A3A;
}

.funi-tab-close svg {
  width: 12px;
  height: 12px;
}

.funi-page-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* List Toolbar */
.funi-list-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #fff;
  border-bottom: 1px solid var(--funi-border-color-lighter);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

body.dark-theme .funi-list-toolbar {
  background-color: #1A1A1A;
  border-bottom-color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.funi-toolbar-left {
  display: flex;
  gap: 10px;
}

.funi-toolbar-right {
  color: var(--funi-text-color-secondary);
  font-size: 14px;
}

.funi-selected-count {
  font-weight: bold;
  color: var(--funi-primary-color);
}

/* Common styles for form elements */
.funi-form-item .el-input,
.funi-form-item .el-select,
.funi-form-item .el-date-editor,
.funi-form-item .el-textarea {
  width: 100%;
}

/* Rich text editor placeholder */
.funi-rich-text-editor {
  border: 1px solid var(--funi-border-color-base);
  border-radius: 4px;
  min-height: 120px;
  padding: 10px;
  color: var(--funi-text-color-primary);
  background-color: #fff;
  font-size: 14px;
  line-height: 1.5;
}

body.dark-theme .funi-rich-text-editor {
  background-color: #2A2A2A;
  border-color: #4C4D4F;
  color: var(--funi-text-color-primary);
}

/* File upload placeholder */
.funi-file-upload {
  border: 1px dashed var(--funi-border-color-base);
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  color: var(--funi-text-color-secondary);
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.funi-file-upload:hover {
  border-color: var(--funi-primary-color);
}

body.dark-theme .funi-file-upload {
  border-color: #4C4D4F;
}

body.dark-theme .funi-file-upload:hover {
  border-color: var(--funi-primary-color);
}

.funi-file-upload-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 10px;
  color: var(--funi-text-color-placeholder);
}

.funi-file-upload-text {
  font-size: 14px;
}

/* Table for nested data (e.g., 投标人信息) */
.funi-nested-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.funi-nested-table th,
.funi-nested-table td {
  border: 1px solid var(--funi-border-color-lighter);
  padding: 8px 10px;
  text-align: left;
}

body.dark-theme .funi-nested-table th,
body.dark-theme .funi-nested-table td {
  border-color: #333;
}

.funi-nested-table th {
  background-color: var(--funi-background-color-base);
  font-weight: bold;
}

body.dark-theme .funi-nested-table th {
  background-color: #2A2A2A;
}

.funi-nested-table .funi-btn-text {
  padding: 0 5px;
}

/* Date range input */
.funi-date-range {
  display: flex;
  align-items: center;
  gap: 5px;
}

.funi-date-range .funi-form-input {
  flex: 1;
}

/* Calendar for dashboard */
.funi-calendar {
  border: 1px solid var(--funi-border-color-base);
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
}

body.dark-theme .funi-calendar {
  background-color: #2A2A2A;
  border-color: #4C4D4F;
}

.funi-calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: var(--funi-background-color-base);
  border-bottom: 1px solid var(--funi-border-color-lighter);
}

body.dark-theme .funi-calendar-header {
  background-color: #3A3A3A;
  border-bottom-color: #4C4D4F;
}

.funi-calendar-nav button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: var(--funi-text-color-primary);
  padding: 5px;
}

.funi-calendar-nav button:hover {
  color: var(--funi-primary-color);
}

.funi-calendar-month-year {
  font-weight: bold;
  color: var(--funi-text-color-primary);
}

.funi-calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
}

.funi-calendar-day-header {
  padding: 8px;
  font-weight: bold;
  color: var(--funi-text-color-secondary);
  border-bottom: 1px solid var(--funi-border-color-lighter);
}

.funi-calendar-day {
  padding: 10px 5px;
  border-bottom: 1px solid var(--funi-border-color-lighter);
  border-right: 1px solid var(--funi-border-color-lighter);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
}

.funi-calendar-day:nth-child(7n) {
  border-right: none;
}

.funi-calendar-day.empty {
  background-color: var(--funi-background-color-base);
}

.funi-calendar-day-number {
  font-weight: bold;
  font-size: 16px;
  color: var(--funi-text-color-primary);
  margin-bottom: 5px;
}

.funi-calendar-event-count {
  background-color: var(--funi-primary-color);
  color: #fff;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-top: 5px;
}

/* Data Dashboard */
.funi-dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.funi-dashboard-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

body.dark-theme .funi-dashboard-card {
  background-color: #1A1A1A;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.funi-dashboard-card-title {
  font-size: 16px;
  color: var(--funi-text-color-secondary);
  margin-bottom: 10px;
}

.funi-dashboard-card-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--funi-primary-color);
}

.funi-dashboard-chart-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

body.dark-theme .funi-dashboard-chart-container {
  background-color: #1A1A1A;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.funi-dashboard-chart-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--funi-text-color-primary);
  margin-bottom: 15px;
}

.funi-chart-placeholder {
  width: 100%;
  height: 300px;
  background-color: var(--funi-background-color-base);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--funi-text-color-secondary);
  border-radius: 4px;
}

body.dark-theme .funi-chart-placeholder {
  background-color: #2A2A2A;
}

/* Timeline for process records */
.funi-timeline {
  list-style: none;
  padding: 0;
  position: relative;
}

.funi-timeline::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--funi-border-color-lighter);
}

body.dark-theme .funi-timeline::before {
  background-color: #333;
}

.funi-timeline-item {
  position: relative;
  padding-left: 30px;
  margin-bottom: 20px;
}

.funi-timeline-item::before {
  content: '';
  position: absolute;
  left: 4px;
  top: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: var(--funi-primary-color);
  border: 2px solid #fff;
  z-index: 1;
}

body.dark-theme .funi-timeline-item::before {
  border-color: #1A1A1A;
}

.funi-timeline-content {
  background-color: var(--funi-background-color-base);
  border-radius: 4px;
  padding: 10px 15px;
  color: var(--funi-text-color-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

body.dark-theme .funi-timeline-content {
  background-color: #2A2A2A;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.funi-timeline-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.funi-timeline-meta {
  font-size: 12px;
  color: var(--funi-text-color-secondary);
}

/* General utility classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.p-20 {
  padding: 20px;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-10 {
  gap: 10px;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-auto {
  overflow: auto;
}

.hidden {
  display: none !important;
}

/* Menu Styling Enhancements */
/* Menu Styling Enhancements */
/* First-level menu items (direct children of .funi-menu) */
/* .funi-menu>.funi-menu-list>.funi-menu-item>.funi-menu-link .funi-menu-text {
  font-weight: bold;
} */

/* Indentation for second-level menu items */
.funi-menu-group .funi-menu-list .funi-menu-item .funi-menu-link {
  padding-left: calc(10px + 1em);
  /* 10px base + 1em additional indentation for 2nd level */
}