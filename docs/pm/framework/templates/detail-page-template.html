<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详情页面 - Funi管理系统</title>
    <link rel="stylesheet" href="../assets/css/funi-framework.css">
    <link rel="stylesheet" href="../assets/css/funi-components.css">
    <link rel="stylesheet" href="../assets/css/funi-themes.css">
</head>
<body>
    <!-- Funi布局容器 -->
    <div class="funi-layout" id="app">
        <!-- 头部区域 -->
        <header class="funi-header">
            <div class="funi-header-left">
                <div class="funi-logo">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iOCIgZmlsbD0iIzQwOUVGRiIvPgo8cGF0aCBkPSJNOCAxMkgxNlY4SDhWMTJaTTggMjBIMTZWMTZIOFYyMFpNMjAgMTJIMjhWOEgyMFYxMlpNMjAgMjBIMjhWMTZIMjBWMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Funi">
                    <span class="funi-logo-text">Funi管理系统</span>
                </div>
            </div>
            <div class="funi-header-right">
                <div class="funi-theme-switcher">
                    <button class="funi-theme-btn" onclick="toggleTheme()" title="切换主题">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 12a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z"/>
                        </svg>
                    </button>
                </div>
                <div class="funi-user-menu">
                    <div class="funi-user-avatar">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iNCIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMTYgMThDMTIuNjg2MyAxOCAxMCAyMC42ODYzIDEwIDI0VjI2SDE2SDIyVjI0QzIyIDIwLjY4NjMgMTkuMzEzNyAxOCAxNiAxOFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+" alt="用户头像">
                        <span class="funi-user-name">管理员</span>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 主体区域 -->
        <div class="funi-main">
            <!-- 侧边栏 -->
            <aside class="funi-sidebar">
                <nav class="funi-menu">
                    <div class="funi-menu-group" data-group-id="system-management">
                        <div class="funi-menu-group-title">
                            <span>系统管理</span>
                            <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>
                            </svg>
                        </div>
                        <ul class="funi-menu-list">
                            <li class="funi-menu-item funi-menu-item-active">
                                <a href="#" class="funi-menu-link">
                                    <svg class="funi-menu-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                        <path d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                                    </svg>
                                    <span class="funi-menu-text">用户管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#" class="funi-menu-link">
                                    <svg class="funi-menu-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                        <path d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z"/>
                                    </svg>
                                    <span class="funi-menu-text">角色管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#" class="funi-menu-link">
                                    <svg class="funi-menu-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                        <path d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872l-.1-.34zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z"/>
                                    </svg>
                                    <span class="funi-menu-text">系统设置</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </aside>
            
            <!-- 内容区域 -->
            <main class="funi-content">
                <div class="funi-content-wrapper">
                    <!-- 详情页面内容 -->
                    <div class="funi-page-content">
                        <!-- 详情元信息 -->
                        <div class="funi-detail-meta">
                            <span>用户ID: 1002</span>
                            <span>创建时间: 2024-01-16 09:15:00</span>
                            <span>最后更新: 2024-01-20 14:30:00</span>
                            <span>状态: <span class="funi-tag funi-tag-success">启用</span></span>
                        </div>
                        
                        <!-- 详情内容 -->
                        <div class="funi-detail-content">
                            <!-- 基本信息 -->
                            <div class="funi-detail-section">
                                <h2 class="funi-detail-section-title">基本信息</h2>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">用户名:</div>
                                    <div class="funi-detail-value">zhangsan</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">真实姓名:</div>
                                    <div class="funi-detail-value">张三</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">邮箱地址:</div>
                                    <div class="funi-detail-value"><EMAIL></div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">手机号码:</div>
                                    <div class="funi-detail-value">13800138001</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">性别:</div>
                                    <div class="funi-detail-value">男</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">出生日期:</div>
                                    <div class="funi-detail-value">1990-05-15</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">部门:</div>
                                    <div class="funi-detail-value">技术部</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">职位:</div>
                                    <div class="funi-detail-value">高级工程师</div>
                                </div>
                            </div>
                            
                            <!-- 账户信息 -->
                            <div class="funi-detail-section">
                                <h2 class="funi-detail-section-title">账户信息</h2>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">账户状态:</div>
                                    <div class="funi-detail-value">
                                        <span class="funi-tag funi-tag-success">启用</span>
                                    </div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">用户角色:</div>
                                    <div class="funi-detail-value">
                                        <span class="funi-tag funi-tag-primary">普通用户</span>
                                        <span class="funi-tag funi-tag-info">开发者</span>
                                    </div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">权限组:</div>
                                    <div class="funi-detail-value">技术部权限组</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">最后登录:</div>
                                    <div class="funi-detail-value">2024-01-20 08:30:00</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">登录IP:</div>
                                    <div class="funi-detail-value">*************</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">登录次数:</div>
                                    <div class="funi-detail-value">156 次</div>
                                </div>
                            </div>
                            
                            <!-- 联系信息 -->
                            <div class="funi-detail-section">
                                <h2 class="funi-detail-section-title">联系信息</h2>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">办公电话:</div>
                                    <div class="funi-detail-value">010-12345678</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">紧急联系人:</div>
                                    <div class="funi-detail-value">李四</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">紧急联系电话:</div>
                                    <div class="funi-detail-value">13900139000</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">家庭住址:</div>
                                    <div class="funi-detail-value">北京市朝阳区某某街道某某小区某某号楼某某单元某某室</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">邮政编码:</div>
                                    <div class="funi-detail-value">100000</div>
                                </div>
                            </div>
                            
                            <!-- 系统信息 -->
                            <div class="funi-detail-section">
                                <h2 class="funi-detail-section-title">系统信息</h2>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">创建时间:</div>
                                    <div class="funi-detail-value">2024-01-16 09:15:00</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">创建人:</div>
                                    <div class="funi-detail-value">系统管理员</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">最后更新:</div>
                                    <div class="funi-detail-value">2024-01-20 14:30:00</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">更新人:</div>
                                    <div class="funi-detail-value">张三</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">数据版本:</div>
                                    <div class="funi-detail-value">v1.2.3</div>
                                </div>
                                <div class="funi-detail-item">
                                    <div class="funi-detail-label">备注信息:</div>
                                    <div class="funi-detail-value">该用户为技术部核心开发人员，拥有系统开发和维护权限。工作认真负责，技术能力强，团队协作能力优秀。</div>
                                </div>
                            </div>
                            
                            <!-- 操作日志 -->
                            <div class="funi-detail-section">
                                <h2 class="funi-detail-section-title">最近操作日志</h2>
                                <div class="funi-card">
                                    <div class="funi-card-body">
                                        <table class="funi-table">
                                            <thead>
                                                <tr>
                                                    <th>操作时间</th>
                                                    <th>操作类型</th>
                                                    <th>操作内容</th>
                                                    <th>操作IP</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>2024-01-20 14:30:00</td>
                                                    <td>信息修改</td>
                                                    <td>修改个人联系方式</td>
                                                    <td>*************</td>
                                                    <td><span class="funi-tag funi-tag-success">成功</span></td>
                                                </tr>
                                                <tr>
                                                    <td>2024-01-20 08:30:00</td>
                                                    <td>系统登录</td>
                                                    <td>用户登录系统</td>
                                                    <td>*************</td>
                                                    <td><span class="funi-tag funi-tag-success">成功</span></td>
                                                </tr>
                                                <tr>
                                                    <td>2024-01-19 18:00:00</td>
                                                    <td>系统登出</td>
                                                    <td>用户退出系统</td>
                                                    <td>*************</td>
                                                    <td><span class="funi-tag funi-tag-success">成功</span></td>
                                                </tr>
                                                <tr>
                                                    <td>2024-01-19 09:00:00</td>
                                                    <td>系统登录</td>
                                                    <td>用户登录系统</td>
                                                    <td>*************</td>
                                                    <td><span class="funi-tag funi-tag-success">成功</span></td>
                                                </tr>
                                                <tr>
                                                    <td>2024-01-18 17:30:00</td>
                                                    <td>密码修改</td>
                                                    <td>用户修改登录密码</td>
                                                    <td>*************</td>
                                                    <td><span class="funi-tag funi-tag-success">成功</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="funi-detail-actions">
                            <button class="funi-btn funi-btn-default" onclick="history.back()">
                                <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                                    <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
                                </svg>
                                返回列表
                            </button>
                            <button class="funi-btn funi-btn-primary">
                                <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                                </svg>
                                编辑用户
                            </button>
                            <button class="funi-btn funi-btn-warning">
                                <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                    <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                </svg>
                                重置密码
                            </button>
                            <button class="funi-btn funi-btn-danger">
                                <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M2.5 1a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1H3v9a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V4h.5a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H10a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1H2.5zm3 4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 .5-.5zM8 5a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7A.5.5 0 0 1 8 5zm3 .5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 1 0z"/>
                                </svg>
                                删除用户
                            </button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="../assets/js/funi-theme-switcher.js"></script>
    <script src="../assets/js/funi-interactions.js"></script>
    
    <script>
        // 页面特定的交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 编辑按钮点击事件
            document.querySelector('.funi-btn-primary').addEventListener('click', function() {
                window.funiInteractions.showMessage('跳转到编辑页面', 'info');
                // 这里可以添加跳转到编辑页面的逻辑
            });
            
            // 重置密码按钮点击事件
            document.querySelector('.funi-btn-warning').addEventListener('click', function() {
                if (confirm('确定要重置该用户的密码吗？')) {
                    window.funiInteractions.showMessage('密码重置成功', 'success');
                    // 这里可以添加重置密码的逻辑
                }
            });
            
            // 删除按钮点击事件
            document.querySelector('.funi-btn-danger').addEventListener('click', function() {
                if (confirm('确定要删除该用户吗？此操作不可恢复！')) {
                    window.funiInteractions.showMessage('用户删除成功', 'success');
                    // 这里可以添加删除用户的逻辑
                    setTimeout(() => {
                        history.back();
                    }, 1500);
                }
            });
        });
    </script>
</body>
</html>