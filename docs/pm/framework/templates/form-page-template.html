<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单页面 - Funi管理系统</title>
    <link rel="stylesheet" href="../assets/css/funi-framework.css">
    <link rel="stylesheet" href="../assets/css/funi-components.css">
    <link rel="stylesheet" href="../assets/css/funi-themes.css">
</head>
<body>
    <!-- Funi布局容器 -->
    <div class="funi-layout" id="app">
        <!-- 头部区域 -->
        <header class="funi-header">
            <div class="funi-header-left">
                <div class="funi-logo">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iOCIgZmlsbD0iIzQwOUVGRiIvPgo8cGF0aCBkPSJNOCAxMkgxNlY4SDhWMTJaTTggMjBIMTZWMTZIOFYyMFpNMjAgMTJIMjhWOEgyMFYxMlpNMjAgMjBIMjhWMTZIMjBWMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K" alt="Funi">
                    <span class="funi-logo-text">Funi管理系统</span>
                </div>
            </div>
            <div class="funi-header-right">
                <div class="funi-theme-switcher">
                    <button class="funi-theme-btn" onclick="toggleTheme()" title="切换主题">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 12a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z"/>
                        </svg>
                    </button>
                </div>
                <div class="funi-user-menu">
                    <div class="funi-user-avatar">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iNCIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMTYgMThDMTIuNjg2MyAxOCAxMCAyMC42ODYzIDEwIDI0VjI2SDE2SDIyVjI0QzIyIDIwLjY4NjMgMTkuMzEzNyAxOCAxNiAxOFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+" alt="用户头像">
                        <span class="funi-user-name">管理员</span>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 主体区域 -->
        <div class="funi-main">
            <!-- 侧边栏 -->
            <aside class="funi-sidebar">
                <nav class="funi-menu">
                    <div class="funi-menu-group" data-group-id="system-management">
                        <div class="funi-menu-group-title">
                            <span>系统管理</span>
                            <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>
                            </svg>
                        </div>
                        <ul class="funi-menu-list">
                            <li class="funi-menu-item funi-menu-item-active">
                                <a href="#" class="funi-menu-link">
                                    <svg class="funi-menu-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                        <path d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                                    </svg>
                                    <span class="funi-menu-text">用户管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#" class="funi-menu-link">
                                    <svg class="funi-menu-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                        <path d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z"/>
                                    </svg>
                                    <span class="funi-menu-text">角色管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#" class="funi-menu-link">
                                    <svg class="funi-menu-icon" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                        <path d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872l-.1-.34zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z"/>
                                    </svg>
                                    <span class="funi-menu-text">系统设置</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </aside>
            
            <!-- 内容区域 -->
            <main class="funi-content">
                <div class="funi-content-wrapper">
                    <!-- 表单页面内容 -->
                    <div class="funi-page-content">
                        <!-- 表单说明 -->
                        <p class="funi-page-description">请填写用户的基本信息，带 * 号的为必填项</p>
                        
                        <!-- 表单内容 -->
                        <form class="funi-form" id="userForm">
                            <!-- 基本信息 -->
                            <div class="funi-card">
                                <div class="funi-card-header">
                                    <h3 class="funi-card-title">基本信息</h3>
                                </div>
                                <div class="funi-card-body">
                                    <div class="funi-form-item">
                                        <label class="funi-form-label required">用户名</label>
                                        <div class="funi-form-content">
                                            <input type="text" class="funi-form-input" name="username" required minlength="3" maxlength="20" placeholder="请输入用户名">
                                            <div class="funi-form-help">用户名长度为3-20个字符，只能包含字母、数字和下划线</div>
                                            <div class="funi-form-error"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label required">真实姓名</label>
                                        <div class="funi-form-content">
                                            <input type="text" class="funi-form-input" name="realname" required maxlength="50" placeholder="请输入真实姓名">
                                            <div class="funi-form-error"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label required">邮箱地址</label>
                                        <div class="funi-form-content">
                                            <input type="email" class="funi-form-input" name="email" required placeholder="请输入邮箱地址">
                                            <div class="funi-form-help">请输入有效的邮箱地址，用于接收系统通知</div>
                                            <div class="funi-form-error"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label required">手机号码</label>
                                        <div class="funi-form-content">
                                            <input type="tel" class="funi-form-input" name="phone" required data-type="phone" placeholder="请输入手机号码">
                                            <div class="funi-form-error"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">性别</label>
                                        <div class="funi-form-content">
                                            <select class="funi-form-select" name="gender">
                                                <option value="">请选择性别</option>
                                                <option value="male">男</option>
                                                <option value="female">女</option>
                                                <option value="other">其他</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">出生日期</label>
                                        <div class="funi-form-content">
                                            <input type="date" class="funi-form-input" name="birthday">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 工作信息 -->
                            <div class="funi-card">
                                <div class="funi-card-header">
                                    <h3 class="funi-card-title">工作信息</h3>
                                </div>
                                <div class="funi-card-body">
                                    <div class="funi-form-item">
                                        <label class="funi-form-label required">所属部门</label>
                                        <div class="funi-form-content">
                                            <select class="funi-form-select" name="department" required>
                                                <option value="">请选择部门</option>
                                                <option value="tech">技术部</option>
                                                <option value="product">产品部</option>
                                                <option value="design">设计部</option>
                                                <option value="marketing">市场部</option>
                                                <option value="sales">销售部</option>
                                                <option value="hr">人事部</option>
                                                <option value="finance">财务部</option>
                                            </select>
                                            <div class="funi-form-error"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label required">职位</label>
                                        <div class="funi-form-content">
                                            <input type="text" class="funi-form-input" name="position" required maxlength="50" placeholder="请输入职位">
                                            <div class="funi-form-error"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">入职日期</label>
                                        <div class="funi-form-content">
                                            <input type="date" class="funi-form-input" name="hire_date">
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">工号</label>
                                        <div class="funi-form-content">
                                            <input type="text" class="funi-form-input" name="employee_id" maxlength="20" placeholder="请输入工号">
                                            <div class="funi-form-help">如不填写，系统将自动生成工号</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 账户设置 -->
                            <div class="funi-card">
                                <div class="funi-card-header">
                                    <h3 class="funi-card-title">账户设置</h3>
                                </div>
                                <div class="funi-card-body">
                                    <div class="funi-form-item">
                                        <label class="funi-form-label required">初始密码</label>
                                        <div class="funi-form-content">
                                            <input type="password" class="funi-form-input" name="password" required minlength="6" maxlength="20" placeholder="请输入初始密码">
                                            <div class="funi-form-help">密码长度为6-20个字符，建议包含字母、数字和特殊字符</div>
                                            <div class="funi-form-error"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label required">确认密码</label>
                                        <div class="funi-form-content">
                                            <input type="password" class="funi-form-input" name="confirm_password" required placeholder="请再次输入密码">
                                            <div class="funi-form-error"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label required">用户角色</label>
                                        <div class="funi-form-content">
                                            <select class="funi-form-select" name="role" required>
                                                <option value="">请选择角色</option>
                                                <option value="admin">系统管理员</option>
                                                <option value="manager">部门经理</option>
                                                <option value="user">普通用户</option>
                                                <option value="guest">访客</option>
                                            </select>
                                            <div class="funi-form-error"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">账户状态</label>
                                        <div class="funi-form-content">
                                            <select class="funi-form-select" name="status">
                                                <option value="active">启用</option>
                                                <option value="inactive">禁用</option>
                                                <option value="pending">待审核</option>
                                            </select>
                                            <div class="funi-form-help">新用户默认为启用状态</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 联系信息 -->
                            <div class="funi-card">
                                <div class="funi-card-header">
                                    <h3 class="funi-card-title">联系信息</h3>
                                </div>
                                <div class="funi-card-body">
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">办公电话</label>
                                        <div class="funi-form-content">
                                            <input type="tel" class="funi-form-input" name="office_phone" placeholder="请输入办公电话">
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">紧急联系人</label>
                                        <div class="funi-form-content">
                                            <input type="text" class="funi-form-input" name="emergency_contact" maxlength="50" placeholder="请输入紧急联系人姓名">
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">紧急联系电话</label>
                                        <div class="funi-form-content">
                                            <input type="tel" class="funi-form-input" name="emergency_phone" placeholder="请输入紧急联系电话">
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">家庭住址</label>
                                        <div class="funi-form-content">
                                            <textarea class="funi-form-textarea" name="address" rows="3" maxlength="200" data-max-length="200" placeholder="请输入家庭住址"></textarea>
                                        </div>
                                    </div>
                                    
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">邮政编码</label>
                                        <div class="funi-form-content">
                                            <input type="text" class="funi-form-input" name="postal_code" maxlength="10" placeholder="请输入邮政编码">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 备注信息 -->
                            <div class="funi-card">
                                <div class="funi-card-header">
                                    <h3 class="funi-card-title">备注信息</h3>
                                </div>
                                <div class="funi-card-body">
                                    <div class="funi-form-item">
                                        <label class="funi-form-label">备注</label>
                                        <div class="funi-form-content">
                                            <textarea class="funi-form-textarea" name="remark" rows="4" maxlength="500" data-max-length="500" placeholder="请输入备注信息（可选）"></textarea>
                                            <div class="funi-form-help">可以填写用户的特殊说明、技能特长等信息</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 提交按钮 -->
                            <div class="funi-form-actions">
                                <button type="button" class="funi-btn funi-btn-default" onclick="history.back()">
                                    <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                                        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
                                    </svg>
                                    取消
                                </button>
                                <button type="button" class="funi-btn funi-btn-default" id="resetBtn">
                                    <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                                        <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                                    </svg>
                                    重置
                                </button>
                                <button type="submit" class="funi-btn funi-btn-primary">
                                    <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
                                        <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/>
                                    </svg>
                                    保存用户
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="../assets/js/funi-theme-switcher.js"></script>
    <script src="../assets/js/funi-interactions.js"></script>
    
    <script>
        // 页面特定的交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('userForm');
            const resetBtn = document.getElementById('resetBtn');
            
            // 密码确认验证
            const passwordInput = form.querySelector('input[name="password"]');
            const confirmPasswordInput = form.querySelector('input[name="confirm_password"]');
            
            function validatePasswordMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                const errorElement = confirmPasswordInput.parentNode.querySelector('.funi-form-error');
                
                if (confirmPassword && password !== confirmPassword) {
                    confirmPasswordInput.classList.add('error');
                    errorElement.textContent = '两次输入的密码不一致';
                    errorElement.style.display = 'block';
                    return false;
                } else {
                    confirmPasswordInput.classList.remove('error');
                    errorElement.style.display = 'none';
                    return true;
                }
            }
            
            passwordInput.addEventListener('blur', validatePasswordMatch);
            confirmPasswordInput.addEventListener('blur', validatePasswordMatch);
            
            // 重置按钮
            resetBtn.addEventListener('click', function() {
                if (confirm('确定要重置表单吗？所有已填写的内容将被清空。')) {
                    form.reset();
                    // 清除所有错误状态
                    form.querySelectorAll('.error').forEach(el => el.classList.remove('error'));
                    form.querySelectorAll('.funi-form-error').forEach(el => el.style.display = 'none');
                    window.funiInteractions.showMessage('表单已重置', 'info');
                }
            });
            
            // 表单提交
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 验证密码匹配
                if (!validatePasswordMatch()) {
                    return;
                }
                
                // 收集表单数据
                const formData = new FormData(form);
                const userData = Object.fromEntries(formData.entries());
                
                console.log('用户数据:', userData);
                
                // 模拟提交
                window.funiInteractions.showLoading(form);
                
                setTimeout(() => {
                    window.funiInteractions.hideLoading(form);
                    window.funiInteractions.showMessage('用户创建成功！', 'success');
                    
                    // 模拟跳转到列表页
                    setTimeout(() => {
                        if (confirm('用户创建成功！是否返回用户列表？')) {
                            history.back();
                        }
                    }, 1500);
                }, 2000);
            });
            
            // 监听表单验证事件
            document.addEventListener('formValidation', function(e) {
                if (!e.detail.isValid) {
                    window.funiInteractions.showMessage('请检查表单中的错误信息', 'error');
                }
            });
        });
    </script>
</body>
</html>