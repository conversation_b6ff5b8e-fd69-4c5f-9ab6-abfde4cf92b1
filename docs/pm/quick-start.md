# Funi框架HTML原型生成系统 - 快速启动指南

## 🚀 5分钟快速上手

### 第一步：准备PRD文档

确保你的PRD文档包含以下关键信息：
- 页面功能描述
- 数据字段定义
- 业务流程说明
- 用户角色和权限

### 第二步：使用AI生成原型

1. 打开你的AI助手（如Claude、ChatGPT等）
2. 复制 `master-prompt.md` 中的完整提示词
3. 在提示词后添加你的PRD内容
4. 发送给AI，等待生成结果

### 第三步：保存和预览

1. 将AI生成的HTML代码保存到 `html/` 目录
2. 用浏览器打开HTML文件
3. 测试各种交互功能
4. 检查主题切换效果

## 📝 使用示例

### 示例1：生成员工管理页面

**输入PRD片段：**
```
需求：员工管理系统
功能：查看员工列表、搜索员工、新增员工、编辑员工信息
字段：工号、姓名、部门、职位、联系电话、入职日期、状态
权限：HR可以增删改查，普通用户只能查看
```

**AI会自动生成：**
- 完整的员工管理列表页面
- 包含搜索表单的HTML结构
- 符合Funi框架规范的样式
- 贴合业务的示例数据
- 完整的交互功能

### 示例2：生成订单详情页面

**输入PRD片段：**
```
需求：订单详情页面
功能：查看订单详细信息、修改订单状态、打印订单
字段：订单号、客户信息、商品清单、金额、状态、创建时间
流程：待付款 → 已付款 → 已发货 → 已完成
```

**AI会自动生成：**
- 订单详情展示页面
- 状态流程展示组件
- 操作按钮和权限控制
- 符合业务的订单数据

## 🎯 最佳实践

### PRD编写建议

1. **明确页面类型**
   - 列表页：用于数据展示和搜索
   - 详情页：用于信息查看和编辑
   - 表单页：用于数据录入和修改

2. **详细描述字段**
   ```
   用户信息字段：
   - 用户名：字符串，必填，用于登录
   - 真实姓名：字符串，必填，用于显示
   - 邮箱：邮箱格式，必填，用于通知
   - 手机号：11位数字，必填，用于验证
   - 部门：下拉选择，必填，关联部门表
   - 角色：多选，必填，决定权限
   ```

3. **说明业务流程**
   ```
   审批流程：
   1. 员工提交申请
   2. 直属领导审批
   3. HR确认
   4. 财务审核
   5. 完成审批
   ```

### 生成质量优化

1. **提供具体的业务场景**
   - ❌ "需要一个用户管理页面"
   - ✅ "需要一个企业内部员工管理页面，支持HR进行员工信息维护"

2. **明确数据关系**
   - ❌ "用户有部门"
   - ✅ "用户属于某个部门，部门有层级关系，用户可以调岗"

3. **描述权限要求**
   - ❌ "不同用户有不同权限"
   - ✅ "管理员可以增删改查，普通用户只能查看自己的信息"

## 🔧 自定义和扩展

### 修改主题色彩

如果需要调整主题色彩，可以修改 `framework/assets/css/funi-themes.css`：

```css
:root {
  --funi-primary-color: #your-color;
  --funi-primary-light: #your-light-color;
  --funi-primary-dark: #your-dark-color;
}
```

### 添加新的组件样式

在 `framework/assets/css/funi-components.css` 中添加新的组件样式：

```css
.funi-your-component {
  /* 你的样式 */
}
```

### 扩展交互功能

在 `framework/assets/js/funi-interactions.js` 中添加新的交互逻辑：

```javascript
// 添加新的交互方法
addCustomInteraction(selector, handler) {
  // 你的交互逻辑
}
```

## 🐛 常见问题

### Q: AI生成的页面样式不正确？
**A:** 检查以下几点：
1. 确保引入了所有必要的CSS文件
2. 检查HTML结构是否符合Funi框架规范
3. 确认CSS文件路径是否正确

### Q: 交互功能不工作？
**A:** 检查以下几点：
1. 确保引入了所有必要的JS文件
2. 检查浏览器控制台是否有错误信息
3. 确认事件绑定是否正确

### Q: 主题切换不生效？
**A:** 检查以下几点：
1. 确保引入了 `funi-theme-switcher.js`
2. 检查主题CSS文件是否正确加载
3. 确认主题切换按钮的事件绑定

### Q: 如何添加新的页面类型？
**A:** 
1. 在 `framework/templates/` 目录下创建新的模板文件
2. 在 `master-prompt.md` 中添加新页面类型的识别规则
3. 更新组件样式以支持新的页面类型

## 📞 技术支持

如果遇到问题，可以：
1. 查看 `README.md` 中的详细文档
2. 参考 `html/` 目录下的示例页面
3. 联系前端团队获取技术支持

## 🔄 版本更新

系统会定期更新，包括：
- 新增组件样式
- 优化交互体验
- 修复已知问题
- 增强AI提示词

请定期检查更新，确保使用最新版本的框架。

---

**开始你的第一个原型吧！** 🎉