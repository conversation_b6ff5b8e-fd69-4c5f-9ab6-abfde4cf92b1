<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 详情/审核</title>
    <link rel="stylesheet" href="../../assets/css/funi-framework.css">
    <link rel="stylesheet" href="../../assets/css/funi-components.css">
    <link rel="stylesheet" href="../../assets/css/funi-themes.css">
    <link rel="stylesheet" href="../../assets/css/detail.css">
</head>

<body>
    <div id="app" class="container">
        <div class="container-header">
            <div class="tabs">
                <div class="tab-item active" data-tab="basic-info">基本信息</div>
                <div class="tab-item" data-tab="process-record">流程记录</div>
            </div>
        </div>
        <div class="container-content">
            <div class="tab-content active" id="basic-info">
                <div class="funi-detail-section">
                    <h2 class="funi-detail-section-title">招标信息</h2>
                    <div class="funi-detail-grid">
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">计划编号:</div>
                            <div class="funi-detail-value" id="detail-planNumber"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">计划项目名称:</div>
                            <div class="funi-detail-value" id="detail-planProjectName"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">审核状态:</div>
                            <div class="funi-detail-value" id="detail-auditStatus"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">采购类型:</div>
                            <div class="funi-detail-value" id="detail-procurementType"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">采购方式:</div>
                            <div class="funi-detail-value" id="detail-procurementMethod"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">招标类别:</div>
                            <div class="funi-detail-value" id="detail-biddingCategory"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">采购预算金额:</div>
                            <div class="funi-detail-value" id="detail-budgetAmount"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">资金来源:</div>
                            <div class="funi-detail-value" id="detail-fundSource"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">招标时间:</div>
                            <div class="funi-detail-value" id="detail-biddingTime"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">采购组织方式:</div>
                            <div class="funi-detail-value" id="detail-procurementOrganizationMethod"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">代理机构:</div>
                            <div class="funi-detail-value" id="detail-agency"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">年采购计划:</div>
                            <div class="funi-detail-value" id="detail-annualProcurementPlan"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">项目经办人:</div>
                            <div class="funi-detail-value" id="detail-projectHandler"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">立项决策日期:</div>
                            <div class="funi-detail-value" id="detail-decisionDate"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">驳回原因:</div>
                            <div class="funi-detail-value" id="detail-rejectReason"></div>
                        </div>
                    </div>
                </div>

                <div class="funi-detail-section">
                    <h2 class="funi-detail-section-title">项目信息</h2>
                    <div class="funi-detail-grid">
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">项目类型:</div>
                            <div class="funi-detail-value" id="detail-projectType"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">项目业主:</div>
                            <div class="funi-detail-value" id="detail-projectOwner"></div>
                        </div>
                        <div class="funi-detail-item funi-detail-item-full-width">
                            <div class="funi-detail-label">项目基本情况:</div>
                            <div class="funi-detail-value" id="detail-projectBasicInfo"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">所属二级公司单位:</div>
                            <div class="funi-detail-value" id="detail-secondaryCompany"></div>
                        </div>
                        <div class="funi-detail-item">
                            <div class="funi-detail-label">备注:</div>
                            <div class="funi-detail-value" id="detail-remarks"></div>
                        </div>
                        <div class="funi-detail-item funi-detail-item-full-width">
                            <div class="funi-detail-label">立项决策文件:</div>
                            <div class="funi-detail-value" id="detail-decisionFiles">
                                <!-- File links will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="process-record">
                <div class="funi-detail-section">
                    <h2 class="funi-detail-section-title">流程记录</h2>
                    <div class="funi-timeline" id="processTimeline">
                        <!-- Timeline items will be inserted here by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <div class="funi-detail-actions">
            <button class="button default" onclick="parent.window.location.hash = '#/procurement-plan-management'">
                <iconify-icon icon="mdi:arrow-left"></iconify-icon>
                返回列表
            </button>
            <!-- Dynamic action buttons based on audit status -->
            <button class="button primary" id="editButton" style="display: none;">
                <iconify-icon icon="mdi:pencil"></iconify-icon>
                编辑
            </button>
            <button class="button danger" id="deleteButton" style="display: none;">
                <iconify-icon icon="mdi:delete"></iconify-icon>
                删除
            </button>
            <button class="button primary" id="submitButton" style="display: none;">
                <iconify-icon icon="mdi:send"></iconify-icon>
                提交
            </button>
            <button class="button primary" id="auditButton" style="display: none;">
                <iconify-icon icon="mdi:check-circle"></iconify-icon>
                审核
            </button>
        </div>
    </div>

    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const tabs = document.querySelectorAll('.tabs .tab-item');
            const tabContents = document.querySelectorAll('.container-content .tab-content');
            const editButton = document.getElementById('editButton');
            const deleteButton = document.getElementById('deleteButton');
            const submitButton = document.getElementById('submitButton');
            const auditButton = document.getElementById('auditButton');
            const processTimeline = document.getElementById('processTimeline');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(item => item.classList.remove('active'));
                    tab.classList.add('active');

                    tabContents.forEach(content => content.classList.remove('active'));
                    document.getElementById(tab.dataset.tab).classList.add('active');
                });
            });

            const hash = window.location.hash;
            const params = new URLSearchParams(hash.split('?')[1]);
            const id = params.get('id');

            const dummyDetailData = {
                'CG—20240808—0001': {
                    planNumber: 'CG—20240808—0001',
                    planProjectName: '某项目采购计划一',
                    auditStatus: '待审核',
                    procurementType: '施工',
                    procurementMethod: '公告比选',
                    biddingCategory: '工程类(施工/勘察/EPC/监理)',
                    budgetAmount: '100.00',
                    fundSource: '自有资金',
                    biddingTime: '2024年3季度',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构A',
                    annualProcurementPlan: '90.00',
                    projectHandler: '张三',
                    decisionDate: '2024-07-20',
                    rejectReason: '--',
                    projectType: '依法必须招标项目',
                    projectOwner: '集团本部',
                    projectBasicInfo: '该项目旨在采购一批用于基础设施建设的施工服务。',
                    secondaryCompany: '某建设公司',
                    remarks: '无',
                    decisionFiles: [
                        { name: '立项决策文件A.pdf', url: '#' },
                        { name: '立项决策文件B.docx', url: '#' }
                    ],
                    processRecords: [
                        { operator: '张三', node: '创建计划', time: '2024-08-01 10:00:00' },
                        { operator: '张三', node: '提交审核', time: '2024-08-01 10:30:00' }
                    ]
                },
                'CG—20240808—0002': {
                    planNumber: 'CG—20240808—0002',
                    planProjectName: '某项目采购计划二',
                    auditStatus: '审核中',
                    procurementType: '货物',
                    procurementMethod: '竞争性磋商',
                    biddingCategory: '货物类(材料/设备/供应及安装)',
                    budgetAmount: '250.00',
                    fundSource: '政府投资',
                    biddingTime: '2024年4季度',
                    procurementOrganizationMethod: '自主招标',
                    agency: '--',
                    annualProcurementPlan: '240.00',
                    projectHandler: '李四',
                    decisionDate: '2024-07-25',
                    rejectReason: '--',
                    projectType: '非法定招标采购项目',
                    projectOwner: '分公司B',
                    projectBasicInfo: '采购一批办公设备和耗材。',
                    secondaryCompany: '某科技公司',
                    remarks: '紧急采购',
                    decisionFiles: [
                        { name: '采购需求文档.pdf', url: '#' }
                    ],
                    processRecords: [
                        { operator: '李四', node: '创建计划', time: '2024-08-02 09:00:00' },
                        { operator: '李四', node: '提交审核', time: '2024-08-02 09:30:00' },
                        { operator: '王经理', node: '开始审核', time: '2024-08-02 11:00:00' }
                    ]
                },
                'CG—20240808—0003': {
                    planNumber: 'CG—20240808—0003',
                    planProjectName: '某项目采购计划三',
                    auditStatus: '审核通过',
                    procurementType: '服务',
                    procurementMethod: '单一来源',
                    biddingCategory: '服务类(监理/咨询/物业)',
                    budgetAmount: '50.00',
                    fundSource: '自有资金',
                    biddingTime: '2025年1季度',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构B',
                    annualProcurementPlan: '50.00',
                    projectHandler: '王五',
                    decisionDate: '2024-07-30',
                    rejectReason: '--',
                    projectType: '依法必须招标项目',
                    projectOwner: '集团本部',
                    projectBasicInfo: '聘请专业咨询服务。',
                    secondaryCompany: '某咨询公司',
                    remarks: '无',
                    decisionFiles: [],
                    processRecords: [
                        { operator: '王五', node: '创建计划', time: '2024-08-03 14:00:00' },
                        { operator: '王五', node: '提交审核', time: '2024-08-03 14:30:00' },
                        { operator: '李经理', node: '审核通过', time: '2024-08-03 16:00:00' }
                    ]
                },
                'CG—20240808—0004': {
                    planNumber: 'CG—20240808—0004',
                    planProjectName: '某项目采购计划四',
                    auditStatus: '审核未过',
                    procurementType: '其他',
                    procurementMethod: '邀请比选',
                    biddingCategory: '其他(文本输入)',
                    budgetAmount: '80.00',
                    fundSource: '其他社会资本',
                    biddingTime: '2024年3季度',
                    procurementOrganizationMethod: '自主招标',
                    agency: '--',
                    annualProcurementPlan: '75.00',
                    projectHandler: '赵六',
                    decisionDate: '2024-08-01',
                    rejectReason: '预算超支，需重新评估。',
                    projectType: '非法定招标采购项目',
                    projectOwner: '分公司A',
                    projectBasicInfo: '特殊物资采购。',
                    secondaryCompany: '某贸易公司',
                    remarks: '无',
                    decisionFiles: [],
                    processRecords: [
                        { operator: '赵六', node: '创建计划', time: '2024-08-04 09:00:00' },
                        { operator: '赵六', node: '提交审核', time: '2024-08-04 09:30:00' },
                        { operator: '陈总', node: '审核驳回', time: '2024-08-04 10:00:00', reason: '预算超支，需重新评估。' }
                    ]
                }
            };

            const renderDetail = (data) => {
                document.getElementById('detail-planNumber').textContent = data.planNumber;
                document.getElementById('detail-planProjectName').textContent = data.planProjectName;
                document.getElementById('detail-auditStatus').textContent = data.auditStatus;
                document.getElementById('detail-procurementType').textContent = data.procurementType;
                document.getElementById('detail-procurementMethod').textContent = data.procurementMethod;
                document.getElementById('detail-biddingCategory').textContent = data.biddingCategory;
                document.getElementById('detail-budgetAmount').textContent = data.budgetAmount;
                document.getElementById('detail-fundSource').textContent = data.fundSource;
                document.getElementById('detail-biddingTime').textContent = data.biddingTime;
                document.getElementById('detail-procurementOrganizationMethod').textContent = data.procurementOrganizationMethod;
                document.getElementById('detail-agency').textContent = data.agency;
                document.getElementById('detail-annualProcurementPlan').textContent = data.annualProcurementPlan;
                document.getElementById('detail-projectHandler').textContent = data.projectHandler;
                document.getElementById('detail-decisionDate').textContent = data.decisionDate;
                document.getElementById('detail-rejectReason').textContent = data.rejectReason;

                document.getElementById('detail-projectType').textContent = data.projectType;
                document.getElementById('detail-projectOwner').textContent = data.projectOwner;
                document.getElementById('detail-projectBasicInfo').textContent = data.projectBasicInfo;
                document.getElementById('detail-secondaryCompany').textContent = data.secondaryCompany;
                document.getElementById('detail-remarks').textContent = data.remarks;

                const decisionFilesContainer = document.getElementById('detail-decisionFiles');
                decisionFilesContainer.innerHTML = '';
                if (data.decisionFiles && data.decisionFiles.length > 0) {
                    data.decisionFiles.forEach(file => {
                        const fileLink = document.createElement('a');
                        fileLink.href = file.url;
                        fileLink.textContent = file.name;
                        fileLink.target = '_blank';
                        fileLink.style.marginRight = '10px';
                        decisionFilesContainer.appendChild(fileLink);
                    });
                } else {
                    decisionFilesContainer.textContent = '无';
                }

                // Render process records
                processTimeline.innerHTML = '';
                if (data.processRecords && data.processRecords.length > 0) {
                    data.processRecords.forEach(record => {
                        const timelineItem = document.createElement('div');
                        timelineItem.classList.add('funi-timeline-item');
                        timelineItem.innerHTML = `
                            <div class="funi-timeline-dot"></div>
                            <div class="funi-timeline-content">
                                <div class="funi-timeline-title">${record.node}</div>
                                <div class="funi-timeline-meta">
                                    <span>操作人: ${record.operator}</span>
                                    <span>操作时间: ${record.time}</span>
                                    ${record.reason ? `<span>驳回原因: ${record.reason}</span>` : ''}
                                </div>
                            </div>
                        `;
                        processTimeline.appendChild(timelineItem);
                    });
                } else {
                    processTimeline.textContent = '无流程记录。';
                }

                // Show/hide action buttons based on audit status
                editButton.style.display = 'none';
                deleteButton.style.display = 'none';
                submitButton.style.display = 'none';
                auditButton.style.display = 'none';

                if (data.auditStatus === '待审核' || data.auditStatus === '审核未过') {
                    editButton.style.display = 'inline-block';
                    deleteButton.style.display = 'inline-block';
                    submitButton.style.display = 'inline-block';
                } else if (data.auditStatus === '审核中') {
                    auditButton.style.display = 'inline-block';
                }
            };

            if (id && dummyDetailData[id]) {
                renderDetail(dummyDetailData[id]);
            } else {
                // Handle case where ID is not found or not provided
                document.getElementById('app').innerHTML = '<div style="text-align: center; padding: 50px;">未找到相关采购计划详情。</div>';
            }

            // Event listeners for dynamic buttons
            editButton.addEventListener('click', () => {
                parent.window.location.hash = `#/procurement-plan-management/add-edit?id=${id}`;
            });

            deleteButton.addEventListener('click', () => {
                if (confirm(`确定要删除计划 ${dummyDetailData[id].planProjectName} (编号: ${id}) 吗？`)) {
                    alert(`删除: ${dummyDetailData[id].planProjectName} (编号: ${id})`);
                    parent.window.location.hash = '#/procurement-plan-management';
                }
            });

            submitButton.addEventListener('click', () => {
                if (confirm(`确定要提交计划 ${dummyDetailData[id].planProjectName} (编号: ${id}) 吗？`)) {
                    alert(`提交: ${dummyDetailData[id].planProjectName} (编号: ${id})`);
                    // In a real app, update status and re-render or navigate
                    parent.window.location.hash = '#/procurement-plan-management';
                }
            });

            auditButton.addEventListener('click', () => {
                const auditResult = prompt('请输入审核结果 (通过/驳回):');
                if (auditResult === '通过') {
                    alert('审核通过！');
                    parent.window.location.hash = '#/procurement-plan-management';
                } else if (auditResult === '驳回') {
                    const rejectReason = prompt('请输入驳回原因:');
                    alert(`审核驳回，原因: ${rejectReason}`);
                    parent.window.location.hash = '#/procurement-plan-management';
                } else {
                    alert('取消审核操作。');
                }
            });
        });
    </script>
</body>

</html>