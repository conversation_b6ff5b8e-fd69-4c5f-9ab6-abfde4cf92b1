/*
 * detail.css
 *
 * This file provides styles for detail and review pages in the Funi management system.
 * It is intended to be used in conjunction with funi-framework.css, funi-components.css,
 * funi-themes.css, and funi-form.css.
 */

/* General detail page layout */
.detail-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* Default to 3 columns */
    gap: 16px;
    /* Spacing between items */
    padding: 20px;
    background-color: var(--funi-color-bg-card);
    border-radius: 8px;
    box-shadow: var(--funi-shadow-sm);
}

.detail-section-title {
    grid-column: 1 / -1;
    /* Span all columns */
    font-size: 1.1em;
    font-weight: bold;
    color: var(--funi-color-text-primary);
    margin-top: 20px;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--funi-color-border);
}

.detail-item {
    display: flex;
    flex-direction: column;
    padding: 8px 0;
}

.detail-item-full-width {
    grid-column: 1 / -1;
    /* Span all columns */
}

.detail-label {
    font-weight: 600;
    color: var(--funi-color-text-secondary);
    margin-bottom: 4px;
    background-color: #f8f8f8;
    /* Light background for labels */
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
    /* Ensure background applies correctly */
}

/* Dark theme support for labels */
body.dark-theme .detail-label {
    background-color: #333;
    /* Darker background for labels in dark theme */
    color: #eee;
}

.detail-value {
    color: var(--funi-color-text-primary);
    word-break: break-word;
    padding: 4px 8px;
    /* Match label padding for alignment */
}

/* File upload display */
.file-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.file-list-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    color: var(--funi-color-link);
    cursor: pointer;
}

.file-list-item:hover {
    text-decoration: underline;
}

.file-list-item iconify-icon {
    margin-right: 5px;
    color: var(--funi-color-icon);
}

/* Timeline styles for process records */
.timeline-container {
    position: relative;
    padding: 20px 0 20px 30px;
    margin-top: 20px;
    background-color: var(--funi-color-bg-card);
    border-radius: 8px;
    box-shadow: var(--funi-shadow-sm);
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--funi-color-border);
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-left: 20px;
}

.timeline-dot {
    position: absolute;
    left: -7px;
    /* Adjust to align with the line */
    top: 0;
    width: 16px;
    height: 16px;
    background-color: var(--funi-color-primary);
    border-radius: 50%;
    border: 2px solid var(--funi-color-bg-card);
    /* To create a ring effect */
    z-index: 1;
}

.timeline-content {
    background-color: var(--funi-color-bg-default);
    padding: 15px;
    border-radius: 8px;
    box-shadow: var(--funi-shadow-xs);
    border: 1px solid var(--funi-color-border);
}

.timeline-title {
    font-weight: bold;
    color: var(--funi-color-text-primary);
    margin-bottom: 5px;
}

.timeline-meta {
    font-size: 0.85em;
    color: var(--funi-color-text-secondary);
    margin-top: 5px;
}

/* Tabs for detail page */
.detail-tabs {
    display: flex;
    border-bottom: 1px solid var(--funi-color-border);
    margin-bottom: 20px;
}

.detail-tab-item {
    padding: 10px 20px;
    cursor: pointer;
    color: var(--funi-color-text-secondary);
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.detail-tab-item.active {
    color: var(--funi-color-primary);
    border-bottom-color: var(--funi-color-primary);
}

.detail-tab-item:hover {
    color: var(--funi-color-primary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Action buttons at the bottom */
.detail-actions {
    position: sticky;
    bottom: 0;
    background-color: var(--funi-color-bg-page);
    padding: 15px 20px;
    border-top: 1px solid var(--funi-color-border);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    z-index: 100;
}