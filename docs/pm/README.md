# Funi框架HTML原型生成系统

## 系统概述

这是一个专为产品经理设计的HTML原型生成系统，基于Vue3+ElementPlus技术栈的Funi框架。产品经理只需提供PRD文档，通过AI提示词即可生成与前端系统完全一致的高保真HTML静态原型。

## 核心特性

- **零技术门槛**：产品岗无需任何技术背景，只需自然语言描述需求
- **智能页面规划**：AI自动分析PRD，决定需要生成的页面类型和数量
- **真实业务数据**：基于PRD字段定义，自动生成贴合业务场景的静态数据
- **视觉完全一致**：与Vue3+ElementPlus前端系统在布局、主题、组件样式上100%一致
- **主题切换支持**：支持多主题实时切换，与前端系统同步
- **即开即用**：生成的HTML页面可直接在浏览器中打开预览

## 文件结构

```
docs/pm2/
├── README.md                           # 系统使用指南（本文件）
├── master-prompt.md                    # 统一主提示词
├── framework/                          # 静态框架
│   ├── base-template.html             # 基础页面模板
│   ├── assets/
│   │   ├── css/
│   │   │   ├── funi-framework.css     # 框架核心样式
│   │   │   ├── funi-components.css    # 组件样式库
│   │   │   └── funi-themes.css        # 主题样式系统
│   │   └── js/
│   │       ├── funi-theme-switcher.js # 主题切换功能
│   │       └── funi-interactions.js   # 基础交互功能
│   └── components/
│       ├── layout-components.html     # 布局组件库
│       ├── list-components.html       # 列表组件库
│       ├── form-components.html       # 表单组件库
│       └── detail-components.html     # 详情组件库
└── html/                               # AI生成页面存放目录
    └── [项目名称]/                     # 按项目分组存放
        ├── user-list.html             # 用户列表页
        ├── user-detail.html           # 用户详情页
        └── user-form.html             # 用户表单页
```

## 快速开始

### 第一步：准备PRD文档

确保PRD文档包含以下内容：
- 功能描述和业务流程
- 完整的字段定义
- 页面结构说明
- 特殊需求说明

### 第二步：使用AI提示词

1. 打开 `master-prompt.md` 文件
2. 复制完整的提示词内容
3. 在AI对话中粘贴提示词
4. 按照提示词要求提供PRD内容

### 第三步：获取生成结果

1. AI会自动分析PRD并生成所需页面
2. 将生成的HTML文件保存到 `html/[项目名称]/` 目录
3. 在浏览器中打开HTML文件预览效果

## 使用示例

### 输入示例

```
我需要为以下PRD生成HTML原型页面：

PRD：用户管理系统

功能描述：
- 用户列表查看，支持按姓名、部门、状态筛选
- 用户详情查看，显示完整用户信息
- 新建用户，录入基本信息和权限
- 编辑用户信息
- 用户状态管理（启用/禁用）

字段定义：
- 用户名（username）：登录账号
- 真实姓名（realName）：中文姓名
- 邮箱（email）：联系邮箱
- 手机号（phone）：联系电话
- 部门（department）：所属部门
- 职位（position）：工作职位
- 状态（status）：启用/禁用/待审核
- 创建时间（createTime）：账号创建时间
- 最后登录（lastLoginTime）：最近登录时间

请基于Funi框架生成所需的所有页面，包含真实的业务数据。
```

### 输出示例

AI会自动生成：
1. **user-list.html** - 用户列表页面
2. **user-detail.html** - 用户详情页面
3. **user-form.html** - 用户表单页面（新建/编辑）

每个页面都包含：
- 完整的Funi框架布局
- 真实的用户业务数据
- 标准的组件样式
- 主题切换支持

## 技术规范

### 页面类型自动识别

AI会根据PRD内容自动决定生成的页面类型：

- **列表页**：当需要展示数据列表、支持搜索筛选时
- **详情页**：当需要查看完整信息、只读展示时
- **表单页**：当需要新建、编辑数据时
- **仪表板页**：当需要数据统计、图表展示时

### 数据生成规范

- **字段名有意义**：根据PRD中的字段定义生成
- **数据类型正确**：字符串、数字、日期、布尔值等
- **业务逻辑合理**：状态流转、数据关联符合实际
- **数据量适中**：列表页10-20条，详情页完整信息

### 样式一致性保证

- **布局结构**：与 `src/layout/index.vue` 完全一致
- **组件样式**：与 `src/components/` 中的组件样式一致
- **主题系统**：与 `src/layout/components/theme/` 主题配置一致
- **交互体验**：支持主题切换、响应式布局

## 常见问题

### Q: 生成的页面样式与前端系统不一致怎么办？
A: 检查是否正确引用了framework/assets/css/下的样式文件，确保CSS文件路径正确。

### Q: 如何自定义主题？
A: 修改framework/assets/css/funi-themes.css文件中的CSS变量值，或使用页面右上角的主题切换器。

### Q: 可以生成哪些类型的页面？
A: 目前支持列表页、详情页、表单页、仪表板页四种基础页面类型，AI会根据PRD自动选择。

### Q: 如何添加新的组件样式？
A: 在framework/components/目录下添加新的组件HTML模板，并在funi-components.css中添加对应样式。

## 更新日志

### v1.0.0 (2024-01-20)
- 初始版本发布
- 支持基础的列表页、详情页、表单页生成
- 完整的主题切换系统
- 统一的AI提示词

## 技术支持

如有问题或建议，请联系前端团队或在项目中提交Issue。

---

**注意**：本系统生成的是静态HTML原型，用于产品演示和需求确认。实际开发请使用Vue3+ElementPlus技术栈。