import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import Unocss from 'unocss/vite';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import { resolve } from 'path';
// 兼容CommonJS模块
import nodeResolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';

// vite自定义插件
import FuniVitePlugin from '@funi/unplugin-funi-vue-drop/vite';
import funiUnpluginVersions from '@funi/funi-unplugin-versions/vite';
import marionette from '@funi/vite-plugin-vue-marionette';
import puppeteer from '@funi/vite-plugin-vue-puppeteer';

export default defineConfig(({ mode }) => {
  const { VITE_FUNI_MODE, VITE_BASE } = loadEnv(mode, process.cwd());
  const funiBuildPlugin = VITE_FUNI_MODE === 'cli' ? puppeteer : VITE_FUNI_MODE === 'app' ? marionette : () => {};
  return {
    base: VITE_BASE,
    plugins: [
      nodeResolve(),
      commonjs(),
      vue(),
      vueJsx(),
      Unocss(),
      createSvgIconsPlugin({
        // Specify the icon folder to be cached
        iconDirs: [
          resolve(process.cwd(), 'src/components/FuniFormEngine/common/icons/svg'),
          resolve(process.cwd(), 'src/assets/icons')
        ],
        // Specify symbolId format
        symbolId: 'icon-[dir]-[name]'
      }),
      FuniVitePlugin([
        {
          id: 'excel',
          regular: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b|\bgit@github\.com\b/g,
          rewrite: ''
        }
      ]),
      funiUnpluginVersions(),
      funiBuildPlugin()
    ],
    esbuild: {
      // 移除console、debugger
      drop: ['console', 'debugger'],
      // 移除注释
      legalComments: 'none'
    },
    build: {
      // minify: false,
      outDir: 'output/dist',
      sourcemap: false,
      commonjsOptions: {
        exclude: ['lib/vuedraggable/lib/vuedraggable.umd.js,'],
        include: []
      },
      rollupOptions: {
        output: {
          manualChunks: {
            gantt: ['dhtmlx-gantt'],
            tinymce: ['tinymce', '@tinymce/tinymce-vue'],
            funi: ['@funi-lib/utils', '@funi/bpmn-js', '@funi/funi-paas-ops-ui'],
            codemirror: [
              'codemirror',
              '@codemirror/commands',
              '@codemirror/lang-javascript',
              '@codemirror/language',
              '@codemirror/state',
              '@codemirror/theme-one-dark',
              '@codemirror/view',
              'vue-codemirror'
            ]
          }
        }
      }
    },
    server: {
      host: true,
      port: 8000,
      proxy: {
        '/api': {
          target: 'http://bpaas.funi.com',
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, ''),
          headers: {
            Connection: 'keep-alive'
          }
        }
      }
    },
    optimizeDeps: {
      include: ['@/../lib/vuedraggable/lib/vuedraggable.umd.js']
    },
    resolve: {
      // Vite路径别名配置
      alias: { '@': path.resolve('./src') }
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler' // or 'modern'
        }
      }
    }
  };
});
