const fs = require('fs');
const path = require('path');
const pnpm_dir = path.resolve(__dirname, './node_modules/.pnpm/');
const vue_runtime_core = fs.readdirSync(pnpm_dir).find(file => file.startsWith('@vue+runtime-core@'));
const vue_runtime_core_bundler = 'node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js';
const vue_bundler_file = path.resolve(pnpm_dir, vue_runtime_core, vue_runtime_core_bundler);
fs.readFile(vue_bundler_file, 'utf8', function (err, data) {
  if (err) console.error(err);
  const regex = /instance\.__v_cache = cache;\n\s+}/;
  if (regex.test(data)) {
    console.log('Already patched');
    const newData = data.replace(regex, match => `${match}\n    instance.__v_cache = cache;`);
    fs.writeFile(vue_bundler_file, newData, 'utf8', err => !!err && console.error(err));
  }
});
