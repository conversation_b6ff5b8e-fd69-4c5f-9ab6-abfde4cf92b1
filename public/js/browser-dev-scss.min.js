"use strict";function findAndConvertTags(){for(var a=document.getElementsByTagName("style"),b=a.length-1;b>=0;b--)"text/scss"===a[b].type.toLowerCase()&&Sass.compile(a[b].innerHTML,function(a){var b=document.createElement("style");b.type="text/css",b.innerHTML=a.text,document.head.appendChild(b)})}if("undefined"!=typeof window&&"undefined"!=typeof document){if("undefined"==typeof Sass||"function"!=typeof Sass.compile){var sassJSScript=document.createElement("script");sassJSScript.type="text/javascript",sassJSScript.src="/js/sass.sync.min.js",sassJSScript.onload=findAndConvertTags,document.head.appendChild(sassJSScript)}else findAndConvertTags();console.warn("You are using the in-browser SASS CSS transformer. Be sure to precompile your stylesheets for production - https://www.npmjs.com/package/node-sass"),"undefined"!=typeof window&&null!==window&&"undefined"!=typeof Sass&&"function"==typeof Sass.compile&&setTimeout(findAndConvertTags,0)}"undefined"!=typeof exports&&(exports.findAndConvertTags=findAndConvertTags);